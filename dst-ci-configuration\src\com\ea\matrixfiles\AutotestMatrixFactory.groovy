package com.ea.matrixfiles

/**
 * This Factory contains utility functions to retrieve information about AutotestMatrix configurations.
 * See <a href='https://docs.google.com/document/d/1pTxwXhm0T5Mstbg4uaBkjWruC5ntz7pZxqGsmBEcTwk/preview'>Flow Overview</a>
 * See <a href='https://docs.google.com/document/d/1mTfOtPPX93529M7EgmmaGBpmcuMoDyhZ7ZlYCKoVUzw/preview'>Jenkins Config</a>
 **/
class AutotestMatrixFactory {
    private static final Map<String, AutotestMatrix> instances = [:]
    /**
     * List of autotest matrix names
     */
    static final List<String> autotestMatrices = [
        'DunAutotestMatrix',
        'CriterionAutotestMatrix',
        'KinAutotestMatrix',
        'TestAutotestMatrix',
        'FrostedAutotestMatrix',
        'BctAutotestMatrix',
        'BctCh1AutotestMatrix',
    ]

    /**
     * Constructs an instance of the subclassable {@link AutotestMatrix} class with the specified name.
     * @param autotestMatrixName the name of the AutotestMatrix to construct an instance for,
     * has to be a subclass of {@link AutotestMatrix} and be located in the {@code matrixfiles} package.
     * @return constructed singleton instance of {@link AutotestMatrix}
     */
    static AutotestMatrix getInstance(String autotestMatrixName) {
        if (instances[autotestMatrixName] == null) {
            switch (autotestMatrixName) {
                case autotestMatrices[0]: instances[autotestMatrixName] = new DunAutotestMatrix(); break
                case autotestMatrices[1]: instances[autotestMatrixName] = new CriterionAutotestMatrix(); break
                case autotestMatrices[2]: instances[autotestMatrixName] = new KinAutotestMatrix(); break
                case autotestMatrices[3]: instances[autotestMatrixName] = new TestAutotestMatrix(); break
                case autotestMatrices[4]: instances[autotestMatrixName] = new FrostedAutotestMatrix(); break
                case autotestMatrices[5]: instances[autotestMatrixName] = new BctAutotestMatrix(); break
                case autotestMatrices[6]: instances[autotestMatrixName] = new BctCh1AutotestMatrix(); break
            }
        }
        return instances[autotestMatrixName]
    }
}
