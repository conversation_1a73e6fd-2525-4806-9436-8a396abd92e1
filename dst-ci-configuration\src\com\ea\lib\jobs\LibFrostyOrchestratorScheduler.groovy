package com.ea.lib.jobs

import com.ea.lib.jobsettings.FrostyOrchestratorSettings

class LibFrostyOrchestratorScheduler {
    static void start(def job, def project, def branchFile, def masterFile, String branchName) {
        def settings = new FrostyOrchestratorSettings()
        settings.initializeStart(branchFile, masterFile, project, branchName)
        job.with {
            description(settings.description)
            environmentVariables {
                env('branch_name', settings.branchName)
            }
            disabled(false)
            logRotator(7, 100)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to sync.')
                    trim(true)
                }
                choiceParam('clean_data', ['False', 'True'], 'If True, Avalanche will be cleaned at the beginning of the run.')
            }
            properties {
                disableConcurrentBuilds()
                disableResume()
                pipelineTriggers {
                    triggers {
                        cron {
                            spec(settings.cronTrigger)
                        }
                    }
                }
            }
            quietPeriod(0)
        }
    }
}
