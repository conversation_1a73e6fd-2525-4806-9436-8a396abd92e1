import com.ea.lib.jobsettings.FrostyOrchestratorSettings
import spock.lang.Specification

class FrostyOrchestratorSettingsSpec extends Specification {
    class BranchFile {
        static Map standard_jobs_settings = [
            frosty_orchestrator_trigger: '@daily',
        ]
        static Map general_settings = [:]
    }

    class MasterFile {
        static Map branches = [branch: [data_branch: 'data-branch', code_branch: 'code-branch', code_folder: 'dev']]
    }

    @SuppressWarnings('EmptyClass')
    class ProjectFile {}

    void "test that we get expected job settings in initializeStart"() {
        when:
        FrostyOrchestratorSettings settings = new FrostyOrchestratorSettings()
        settings.initializeStart(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            description == 'Triggers deployment-data jobs which in turn trigger frosty jobs. Runs on a schedule.'
            cronTrigger == BranchFile.standard_jobs_settings.frosty_orchestrator_trigger
        }
    }
}
