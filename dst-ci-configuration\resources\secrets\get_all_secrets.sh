#!/bin/sh
echo "Validating requirements are installed..."
apk add --no-cache vault jq libcap
setcap cap_ipc_lock= /usr/sbin/vault

echo "Generating Vault token..."
export VAULT_TOKEN="$(vault write -field=token ${VAULT_AUTH_PATH} role=${VAULT_AUTH_ROLE} jwt=${VAULT_ID_TOKEN})"

JENKINS_API_SECRETS=$($CI_PROJECT_DIR/resources/secrets/get_secret.sh "cobra/automation/accounts/monkey.commons")

echo "Exporting secrets...."
export JENKINS_USER=$(echo $JENKINS_API_SECRETS | jq -r '.username')
export JENKINS_API_TOKEN_DICE_BUILD_JENKINS=$(echo $JENKINS_API_SECRETS | jq -r '.api_token_dice_build_jenkins')
echo "Done"
