package scripts.schedulers.data

import com.ea.lib.Lib<PERSON><PERSON>kins
import hudson.model.Result

/**
 * FrostyOrchestratorScheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Determine changelist') {
            steps {
                script {
                    echo 'Determining which changelist to build.'
                    String dataJob = "${env.branch_name}.data.start"
                    Map injectMap
                    if (params.code_changelist && params.data_changelist) {
                        injectMap = [
                            code_changelist: params.code_changelist,
                            data_changelist: params.data_changelist,
                        ]
                    } else {
                        injectMap = LibJenkins.retrieveIfTargetChangelistsAreHigher(env.JOB_NAME, dataJob)
                    }
                    if (injectMap) {
                        echo "Code changelist: ${injectMap.code_changelist}, data changelist: ${injectMap.data_changelist}"
                        currentBuild.displayName = "${env.JOB_NAME}.${injectMap.data_changelist}.${injectMap.code_changelist}"
                        EnvInject(currentBuild, injectMap)
                    } else {
                        echo "No changelist found in $dataJob. Nothing to build."
                        currentBuild.displayName = "${env.JOB_NAME}.no-changelists-to-run"
                        currentBuild.result = Result.UNSTABLE.toString()
                    }
                }
            }
        }
        stage('Trigger builds') {
            when {
                expression {
                    return !currentBuild.result
                }
            }
            steps {
                script {
                    echo 'Triggering builds'
                    def params = [
                        string(name: 'code_changelist', value: env.code_changelist),
                        string(name: 'data_changelist', value: env.data_changelist),
                        string(name: 'clean_data', value: params.clean_data),
                    ]
                    build(job: "${env.branch_name}.deployment-data.start" as String, wait: false, parameters: params, propagate: false)
                    echo 'Done.'
                }
            }
        }
    }
}
