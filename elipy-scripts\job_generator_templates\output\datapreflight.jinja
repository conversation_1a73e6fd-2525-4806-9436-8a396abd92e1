{#
    Command:
        datapreflight
            short_help: Unshelves and builds a given changelist for validation.

    Arguments:
        p4_port
        p4_client
        platform
        pending_changelist

    Required variables:
        pipeline_branch
            default: None
            help: Branch to fetch pipeline from.
            required: True
        pipeline_changelist
            default: None
            help: Which pipeline CL to use.
            required: True

    Optional variables:
        user
            default: None
            help: Perforce user name.
        assets
            default: None
            help: Which data assets to build.
            multiple: True
        asset
            default: ['preflightlevels']
            help: Deprecated parameter. Use --assets instead.
            multiple: True
        data_branch
            default: data
            help: Branch to fetch Avalanche state from.
        datadir
            default: data
            help: Specify which data directory to use (relative to GAME_ROOT).
        import_avalanche_state
            is_flag: True
            help: Imports Avalanche state from filer.
        data_changelist
            default: 123
            help: Data cl to run preflight on.
        cook_dbx_assets
            is_flag: True
            help: Cook extra assets based on unshelved dbx files
        fail_on_dbx_cook
            is_flag: True
            help: Fail if needed when cooking individual dbx assets
        clean_master_version_check
            is_flag: True
            help: Run clean on master version update.
        pipeline_args
            multiple: True
            help: Pipeline arguments for data build.
        do_warmup/__not_warmup
            default: False
            help: --do-warmup to warm up AWS agent; --not-warmup/not set, normal preflight.
        validate_direct_references
            default: False
            help: Validate dbx files that are not referenced in the main preflight asset.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    datapreflight
    p4_port {{ p4_port }}
    p4_client {{ p4_client }}
    platform {{ platform }}
    pending_changelist {{ pending_changelist }}
    --pipeline-branch {{ pipeline_branch }}
    --pipeline-changelist {{ pipeline_changelist }}
    {%- if user %}
    --user {{ user }}
    {%- endif %}
    {%- if assets %}
    --assets {{ assets }}
    {%- endif %}
    {%- if asset %}
    --asset {{ asset }}
    {%- endif %}
    {%- if data_branch %}
    --data-branch {{ data_branch }}
    {%- endif %}
    {%- if datadir %}
    --datadir {{ datadir }}
    {%- endif %}
    {%- if import_avalanche_state %}
    --import-avalanche-state {{ import_avalanche_state }}
    {%- endif %}
    {%- if data_changelist %}
    --data-changelist {{ data_changelist }}
    {%- endif %}
    {%- if cook_dbx_assets %}
    --cook-dbx-assets {{ cook_dbx_assets }}
    {%- endif %}
    {%- if fail_on_dbx_cook %}
    --fail-on-dbx-cook {{ fail_on_dbx_cook }}
    {%- endif %}
    {%- if clean_master_version_check %}
    --clean-master-version-check {{ clean_master_version_check }}
    {%- endif %}
    {%- if pipeline_args %}
    --pipeline-args {{ pipeline_args }}
    {%- endif %}
    {%- if do_warmup/__not_warmup %}
    --do-warmup/--not-warmup {{ do_warmup/__not_warmup }}
    {%- endif %}
    {%- if validate_direct_references %}
    --validate-direct-references {{ validate_direct_references }}
    {%- endif %}
  displayName: elipy datapreflight
