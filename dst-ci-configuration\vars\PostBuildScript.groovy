/**
 * PostBuildScript.groovy
 * Runs post build scripts on a build machine.
 */

import com.ea.lib.LibCommonNonCps

void call(Class project, Class branchfile, String post_build_type) {
    // Get settings from the branch settings file
    Map branch_info = branchfile.general_settings
    def p4_user_single_slash = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_user_single_slash', '', project)
    def p4_code_server = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_code_server', '', project)
    def p4_data_server = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_data_server', '', project)

    // Define the batch script and the label.
    String batch_script = ''
    String script_label = ''
    if (post_build_type == 'delete_git_lock_file') {
        batch_script = branch_info.elipy_call + ' delete_git_lock_file > ' + branch_info.workspace_root + '\\logs\\delete_git_lock_file.log 2>&1'
        script_label = 'Delete the Git .lock file.'
    } else if (post_build_type == 'kill_processes') {
        batch_script = branch_info.elipy_call + ' kill_processes > ' + branch_info.workspace_root + '\\logs\\kill_processes.log 2>&1'
        script_label = 'Kill processes after the build.'
    } else if (post_build_type == 'post_clean') {
        batch_script = branch_info.elipy_call + ' post_clean --user ' + p4_user_single_slash +
            ' --code-client ' + project.p4_code_client_env + ' --code-port ' + p4_code_server +
            ' --data-client ' + project.p4_data_client_env + ' --data-port ' + p4_data_server +
            ' >> ' + branch_info.workspace_root + '\\logs\\postclean.log 2>&1'
        script_label = 'Running post clean on the machine.'
    } else if (post_build_type == 'postpreflight') {
        batch_script = branch_info.elipy_call + ' post_preflight --preflight_type content --platform ' + env.platform +
            ' --config final ' + env.assetList + ' --datadir ' + branch_info.dataset + ' --p4_user %P4_USER%' +
            ' --p4_client ' + project.p4_data_client_env + ' --p4_port ' + p4_data_server +
            ' --p4_client_code ' + project.p4_code_client_env + ' --p4_port_code ' + p4_code_server
        script_label = 'Running post-preflight on the machine.'
    } else {
        throw new IllegalArgumentException('No settings defined for post build type: ' + post_build_type + ', aborting.')
    }

    // Run the batch script.
    bat(script: batch_script, label: script_label)
}
