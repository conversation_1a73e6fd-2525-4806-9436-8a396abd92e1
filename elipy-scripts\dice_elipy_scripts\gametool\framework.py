"""
gametool/framework.py
"""
import os
import click

from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2.cli import pass_context
from elipy2.frostbite import fbcli
from elipy2.telemetry import collect_metrics
from elipy2 import LOGGER, running_processes, p4, frostbite_core


@click.command(
    "gametool_framework",
    short_help="Build Framework.",
    context_settings=dict(ignore_unknown_options=True),
)
@click.option("--code-changelist", required=True, help="Which code changelist to use.")
@click.option(
    "--clean",
    type=click.BOOL,
    default=False,
    help="Run cleansln before building if --clean true is passed.",
)
@click.option("--p4-port", required=True)
@click.option("--p4-client", required=True)
@click.option("--p4-user", default=None, help="Perforce user name")
@click.option("--submit", type=click.BOOL, default=True, help="Set this to false for dry-run")
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    code_changelist,
    clean,
    p4_port,
    p4_client,
    p4_user,
    submit,
):
    """
    Build Framework
    """
    # adding sentry tags
    add_sentry_tags(__file__, "gametool_framework")

    running_processes.kill()

    perforce = p4.P4Utils(port=p4_port, user=p4_user, client=p4_client)
    perforce.set_environment()
    framework_bin_p4_path = os.path.join(
        frostbite_core.get_tnt_root(), "Build", "Framework", "bin" + os.sep + "..."
    )

    try:
        if clean:
            LOGGER.info("Run cleansln...")
            fbcli.run("cleansln", ["framework"])

        LOGGER.info("Run buildsln...")
        fbcli.run("buildsln", ["framework"])
    except Exception as ex:
        LOGGER.info(
            "An exception occurred while building Framework. Reverting and cleaning the P4 path."
        )
        perforce.revert(path=framework_bin_p4_path)
        perforce.clean(folder=framework_bin_p4_path)
        raise ex

    LOGGER.info("Framework has successfully built.")

    if submit:
        exc = None
        try:
            LOGGER.info("Submitting Framework binaries...")
            perforce.reconcile(path=framework_bin_p4_path, options=["e"])
            perforce.revert(path=framework_bin_p4_path, only_unchanged=True, wipe=False)
            submit_message = "[Automated] Submitting rebuilt Framework binaries from cl {}".format(
                code_changelist
            )
            perforce.submit(message=submit_message)
        except Exception as ex:
            LOGGER.warning("An exception occurred while submitting: {}".format(ex))
            exc = ex
        finally:
            perforce.revert(path=framework_bin_p4_path)
            perforce.clean(folder=framework_bin_p4_path)
            if exc:
                raise exc
