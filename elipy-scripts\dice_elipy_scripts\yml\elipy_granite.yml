# Mandatory
default:
  script_path:
    - "TnT\\Bin\\Python\\3\\Lib\\site-packages\\dice_elipy_scripts"
    - "TnT\\Bin\\Python\\virtual\\Lib\\site-packages\\dice_elipy_scripts"
    - "Python\\2.7\\Lib\\site-packages\\dice_elipy_scripts"
    - "Python\\virtual\\Lib\\site-packages\\dice_elipy_scripts"
  project_name: "granite"
  studio_location: "RippleEffect"
  vault_destination: "\\\\dice-la.la.ad.ea.com\\BuildsVault\\Granite"
  vault_symstore: "true"
  vault_verification_config_path: "vault_verification_config_granite.yml"
  avalanche_symbol_server: ""
  build_share: "\\\\dice-la.la.ad.ea.com\\Builds\\Granite"
  bilbo_url: "https://gnt-bilbo-eck.cobra.dre.ea.com"
  bilbo_api_version: 2
  handle_exe_path: "C:\\ProgramData\\chocolatey\\bin\\handle"
  licensee_code_folder_name: "Code\\DICE"
  md5_exf_path: "C:\\dre\\bin\\exf\\exf.exe"

  metadata_manager:
    primary:
      name: "bilbo_v2"
      url: "https://gnt-bilbo-eck.cobra.dre.ea.com"
      attributes_filename: "build.json"
    secondary:
      - name: "bilbo"
        index: "bilbo_v1"
        url: "https://gnt-bilbo-eck.cobra.dre.ea.com"
        attributes_filename: "bilbo_v1.json"

  game_binaries: ['BF2042.exe','BF2042Trial.exe']

  elsa_patch: "true"
  use_onefs_api: "false"
  skip_frosty_game_config_flags: "false"

  metrics_url: "https://dre-metrics-eck.cobra.dre.ea.com"
  metrics_port: 80

  jenkins_metrics_url: "https://dice-metrics-eck.cobra.dre.ea.com"
  jenkins_metrics_port: 443

  retention_list:
    - 'code\san-dev':    1000
    - 'symbols\san-dev': 1000
  shift_retention: 100
  release_candidate_retention: 56 # max_config(56) * number of release candidates
  shift_submission_path: "\\\\dice-la.la.ad.ea.com\\Builds\\Granite\\shift_upload"
  shift_config_file: "shift_config_granite.yml"
  shift_tool_url: "https://artifacts.ea.com/artifactory/list/dre-generic-federated/cobra/shiftsubmission/5.1.0"

  # The numbers here for Code are 1 per CL.
  # The numbers for frosty/symbols are 1 per CL.
  retention_categories:
      code:
        - 'default' :               40
      frosty\BattlefieldGame:
        - 'default' :               40
      webexport:
        - 'default' :               50
      expressiondebugdata\BattlefieldGame:
        - 'default' :               50

  spin_retention:
    - 'default': 5

  smoke_retention:
    - 'default': 5

  tasks_to_kill:
    - 'AvalancheCLI.exe'
    - 'msbuild.exe' # should be killed before cl.exe
    - 'cl.exe'
    - 'FrostyIsoTool.exe'
    - 'Icepick.Service.exe'
    - 'mspdbsrv.exe'
    - 'nant.exe'
    - 'orbis-clang.exe'
    - 'orbis-ctrl.exe'
    - 'orbis-pub-cmd.exe'
    - 'orbis-symupload.exe'
    - 'snowcacheserver.exe'
    - 'Tool.Pipeline_Win64_release_Dll.exe'
    - 'vctip.exe'
    - 'animationapp.exe'
    - 'Icepick.exe'
    - 'eapm.exe'

  # snowcache_host:
  #   win64game: 'sc2-8825e7.dice.ad.ea.com'
  #   win64trial: 'sc2-8825e7.dice.ad.ea.com'
  #   win64server: 'sc2-838ae7.dice.ad.ea.com'
  #   linux64server: 'sc2-838ae7.dice.ad.ea.com'
  #   linux64: 'sc2-6d91e5.dice.ad.ea.com'
  #   ps5: 'sc2-8d4329.dice.ad.ea.com'
  #   xbsx: 'sc2-d76424.dice.ad.ea.com'
  #   tool: 'sc2-6d91e5.dice.ad.ea.com'

  # recompression_cache:
  #   win64: "kraken-fake-value.dice.ad.ea.com"
  #   server: "kraken-fake-value.dice.ad.ea.com"
  #   xbsx: "kraken-fake-value.dice.ad.ea.com"
  #   ps5: "kraken-fake-value.dice.ad.ea.com"
  #   linuxserver: "kraken-fake-value.dice.ad.ea.com"
  #   linux64: "kraken-fake-value.dice.ad.ea.com"

  symbol_stores_suffix: "SymStore" # defaults to ""

  avalanche:
    avalanche_size: 150 #Size of the avalanche store in GB
    propagate_gets: true #true or false, GET's from upstream
    expiration_time_in_day: 3 #Expiration time in days 3 = never
    #never, monday, tuesday etc
    defrag_day: 'never' #What day a defrag should be done (off so we decide)
    full_defrag_day: 'never' #What day a full defrag should be done (off so we decide)
    maintenance_time_of_day : 9999 #Next maintenance time of day in minutes, set to 9999 so it never runs so only we trigger it.
    maintenance_window_in_minutes : 90 #Maintenance window in minutes.

  avalanche_state_lifetime: # Used by avalanche.remote_clone_db(), but falls back to default value if missing
    default: 3 # Optional because of the fallback
    gnt-dev: 1 # Must be integers and are the value of days

  secrets:
    # Get Kingston server connection keys for bundling into Frosty server builds
    - where:
        build_type: frosty
        platform: server
      url: 'https://ess.ea.com'
      namespace: 'dice-online-casablanca-prod'
      role_id: '0feab1e9-ba7e-acaa-610e-4365569bceb6'
      secret_id_envvar: 'GAME_TEAM_SECRETS_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/bf21/KINGSTON_BK_OL_SERVER.client.int.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.int.eadp.ea.com.key'
        - path: '/bf21/KINGSTON_BK_OL_SERVER.client.int.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.int.eadp.ea.com.crt'
        - path: '/bf21/KINGSTON_BK_OL_SERVER.client.prod.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.prod.eadp.ea.com.key'
        - path: '/bf21/KINGSTON_BK_OL_SERVER.client.prod.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.prod.eadp.ea.com.crt'
    # Get Kingston server connection keys for bundling into Frosty linuxserver builds
    - where:
        build_type: frosty
        platform: linuxserver
      url: 'https://ess.ea.com'
      namespace: 'dice-online-casablanca-prod'
      role_id: '0feab1e9-ba7e-acaa-610e-4365569bceb6'
      secret_id_envvar: 'GAME_TEAM_SECRETS_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/bf21/KINGSTON_BK_OL_SERVER.client.int.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.int.eadp.ea.com.key'
        - path: '/bf21/KINGSTON_BK_OL_SERVER.client.int.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.int.eadp.ea.com.crt'
        - path: '/bf21/KINGSTON_BK_OL_SERVER.client.prod.eadp.ea.com'
          key: 'key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.prod.eadp.ea.com.key'
        - path: '/bf21/KINGSTON_BK_OL_SERVER.client.prod.eadp.ea.com'
          key: 'crt'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\KINGSTON_BK_OL_SERVER.client.prod.eadp.ea.com.crt'

dice:
  build_share: "\\\\dice-la.la.ad.ea.com\\Builds\\Granite"
