package scripts.schedulers.all

import com.ea.lib.LibJenkins
import com.ea.project.GetBranchFile

def project = ProjectClass(env.projectName)

/**
 * outsource_package_scheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Trigger outsource package job.') {
            steps {
                script {
                    def branchFile = GetBranchFile.get_branchfile(env.projectName, env.branchName)
                    def outsourcePackageInfo = branchFile.standard_jobs_settings?.outsource_package_info
                    def referenceCodeJob = outsourcePackageInfo?.reference_job ?: "${env.branchName}.code.start"
                    def lastGoodCode = LibJenkins.getLastStableCodeChangelist(referenceCodeJob)
                    def lastGoodPackage = LibJenkins.getLastStableCodeChangelist("${env.branchName}.outsource-package.start")

                    def codeChangelist = params.code_changelist ?: lastGoodCode

                    if (codeChangelist != lastGoodPackage) {
                        def cleanLocal = params.clean_local

                        def args = [
                            string(name: 'code_changelist', value: codeChangelist),
                            string(name: 'clean_local', value: cleanLocal),
                        ]

                        def injectMap = [
                            'code_changelist': codeChangelist,
                        ]
                        EnvInject(currentBuild, injectMap)
                        currentBuild.displayName = "${env.JOB_NAME}.${codeChangelist}"

                        def jobName = "${env.branchName}.outsource-package.build"
                        def outsourcePackageJob = build(job: jobName, parameters: args, propagate: false)
                        currentBuild.result = outsourcePackageJob.result.toString()

                        def slackSettings = outsourcePackageInfo?.slack_channel
                        SlackMessageNew(currentBuild, slackSettings, project.short_name)

                        DownstreamErrorReporting(currentBuild)
                    } else {
                        echo('No new changelist to generate outsource package from, aborting.')
                    }
                }
            }
        }
    }
}
