import com.ea.lib.LibVaultSecretHandler
import com.ea.lib.LibCommonNonCps

/**
 * InjectVaultSecrets.groovy
 *
 * This script injects vault secrets into the environment for a Jenkins pipeline.
 *
 * @param branchFile The branch file containing vault secrets configuration
 * @param project The project containing vault settings
 * @param body The closure to execute with the resolved secrets
 */
@SuppressWarnings('UnnecessaryGetter')
void call(def branchFile, def project, Closure body) {
    Map branchInfo = (branchFile?.standard_jobs_settings ?: [:]) + (branchFile?.general_settings ?: [:])

    def vaultSecretsJob = branchInfo.vault_secrets_job ?: []
    def vaultSecretsProject = LibCommonNonCps.get_setting_value(branchInfo, [], 'vault_secrets_project', [], project)
    def vaultSecretsBranch = LibCommonNonCps.get_setting_value(branchInfo, [], 'vault_secrets_branch', [], project)
    def vaultSecrets = vaultSecretsProject + vaultSecretsBranch + vaultSecretsJob

    if (vaultSecrets.isEmpty()) {
        echo 'No vault secrets configured.'
        body()
        return
    }

    def vaultSecretsHandler = new LibVaultSecretHandler()
    vaultSecretsHandler.vault_secrets = LibVaultSecretHandler.mergeVaultSecrets(vaultSecrets)
    this.withVault(vaultSecretsHandler.getVaultConfiguration()) {
        body()
    }
}
