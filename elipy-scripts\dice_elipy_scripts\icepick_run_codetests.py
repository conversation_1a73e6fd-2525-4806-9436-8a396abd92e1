# pylint: disable=line-too-long
"""
icepick_run_codetests.py
"""
# pylint: enable=line-too-long
import os
import click
import time
from dice_elipy_scripts.utils.autotest_utils import (
    handle_failure,
    register_autotest_results,
    get_test_suites,
    get_test_suites_names,
)
from dice_elipy_scripts.utils.code_utils import run_gensln
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.frosty_build_utils import authenticate_eapm_credstore
from dice_elipy_scripts.utils.licensee_helper import set_licensee
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from dice_elipy_scripts.utils.frosty_build_utils import install_required_sdks
from elipy2 import data, frostbite_core, LOGGER, code
from elipy2 import running_processes
from elipy2.filer import FilerUtils
from elipy2.cli import pass_context
from elipy2.exceptions import ELIPYException
from elipy2.frostbite import fbenv_layer, icepick
from elipy2.telemetry import collect_metrics


# pylint: disable=too-many-locals
@click.command(
    "icepick_run_codetests",
    short_help="Run icepick test after running gensln.",
    context_settings=dict(ignore_unknown_options=True),
)
@click.argument("platform", required=True)
@click.argument("run-args", nargs=-1, type=click.UNPROCESSED)
@click.option(
    "--test-suites",
    "--ts",
    help="The test suite to run followed by pool-type. Multiple.",
    multiple=True,
    default=[],
)
@click.option(
    "--test-suites-json",
    "--tsj",
    help="Path to test suite json file",
    default=None,
)
@click.option(
    "--settings-files",
    "--sf",
    help="Settings files relative to the data folder.",
    multiple=True,
)
@click.option(
    "--code-changelist",
    "--cc",
    default=None,
    required=True,
    help="Which code changelist to use.",
)
@click.option(
    "--code-branch", "--cb", default=None, required=True, help="Branch to fetch code from."
)
@click.option(
    "--data-changelist",
    "--dc",
    default=None,
    help="Which data changelist to use.",
    required=True,
)
@click.option("--data-branch", "--db", help="Branch to fetch Avalanche state from.", default="")
@click.option(
    "--datadir",
    "--dd",
    default="data",
    help="Specify which data directory to use (relative to GAME_ROOT).",
)
@click.option(
    "--build-type",
    "--bt",
    default="static",
    type=click.Choice(["static", "dll", None]),
    help="Static",
)
@click.option("--autobuild", "--ab", default=False, help="Autobuild", type=bool)
@click.option("--config", "-c", default="release", help="Config")
@click.option("--test-definition", "--td", help="Which test to register.", default="test_def")
@click.option("--licensee", "-l", multiple=True, default=None, help="Licensee to use")
@click.option(
    "--password",
    "-p",
    default=None,
    help="User credentials to authenticate to package server",
)
@click.option("--email", "-e", default=None, help="User email to authenticate to package server")
@click.option(
    "--domain-user",
    "--du",
    default=None,
    help="The user to authenticate to package server as DOMAIN\\user",
)
@click.option("--show-test-results", "--str", default=False, help="Report test failures on jenkins")
@click.option(
    "--run-bilbo",
    "--rb",
    default=False,
    help="Whether or not to report test results to the configured metadata services",
)
@click.option(
    "--test-group", "--tg", default="nogroup", help="Set test group name as part of metadata."
)
@click.option("--inert-run", "--ir", default=False, help="Run but don't do anything")
@click.option(
    "--framework-args-icepick",
    "--fargi",
    default=None,
    help="Extra arguments for Icepick to pass to any Framework commands it starts",
)
@click.option(
    "--framework-args-gensln",
    "--fargg",
    default=None,
    multiple=True,
    help="Extra arguments for gensln to pass to any Framework commands it starts",
)
@click.option("--clean", default="false", help="Delete TnT/Local if --clean true is passed.")
@click.option(
    "--custom-test-suite-data",
    "--ctsd",
    default="",
    help="Custom test suite metadata to pass to Icepick.",
)
@click.option("--icepick-cook", "--ic", default=False, help="Cook data in icepick.")
@click.option(
    "--additional-tools-to-include",
    "--atti",
    help="Additional tool(s) to pull from network share. Not implemented.",
    default=(),
    multiple=True,
    type=str,
)
@click.option(
    "--use-existing-filer-build",
    "--uefb",
    default=False,
    type=click.BOOL,
    help="If True, copy the existing build from filer rather than rebuilding it.",
)
@click.option(
    "--custom-tag",
    "--cut",
    default=None,
    help="Extra folder before changelist to fetch code from.",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    platform,
    run_args,
    test_suites,
    test_suites_json,
    settings_files,
    code_changelist,
    code_branch,
    data_changelist,
    data_branch,
    datadir,
    build_type,
    autobuild,
    config,
    test_definition,
    licensee,
    password,
    email,
    domain_user,
    show_test_results,
    run_bilbo,
    test_group,
    inert_run,
    framework_args_icepick,
    framework_args_gensln,
    clean,
    custom_test_suite_data,
    icepick_cook,
    additional_tools_to_include,
    use_existing_filer_build,
    custom_tag,
):
    """
    Run icepick test.
    """
    if inert_run:
        LOGGER.info("Inert run, sleep for 3 seconds and exit...")
        time.sleep(3)
        return

    # adding sentry tags
    add_sentry_tags(__file__, "autotest")

    test_suites = get_test_suites(test_suites_json, test_suites)
    LOGGER.info("DECODED TEST SUITES: %s", test_suites)
    get_test_suites_names(test_suites)

    if additional_tools_to_include:
        LOGGER.info(
            "Support for --additional-tools-to-include (--atti) has not been implemented"
            " in this script. Ignoring %s",
            additional_tools_to_include,
        )

    # ensure the licensee is set
    frostbite_licensee = [frostbite_core.get_licensee_id()]
    if licensee:
        frostbite_licensee = list(licensee)

    running_processes.kill()

    icepicker = icepick.IcepickUtils()

    LOGGER.info("  Cleaning icepicktemp...")
    icepicker.clean_icepicktemp()

    if password is not None and email is not None:
        authenticate_eapm_credstore(user=email, password=password, domain_user=domain_user)

    icepick_run_args = list(run_args)
    icepick_settings_files = list(settings_files)

    data.DataUtils.set_datadir(datadir)

    install_required_sdks(
        password=password, user=email, domain_user=domain_user, platform=platform.lower()
    )

    if use_existing_filer_build:
        LOGGER.info(" Copying existing codebuild from filer.")
        try:
            _filer = FilerUtils()
            _filer.fetch_code(
                code_branch,
                code_changelist,
                platform,
                config,
                fetch_tests=True,
                custom_tag=custom_tag,
            )
        except Exception as exc:
            error_msg = (
                "Failed to copy existing build from filer:  %s",
                exc,
            )
            raise ELIPYException(error_msg)
    else:
        framework_args_gensln = list(framework_args_gensln)
        framework_args_gensln.extend(
            [
                "-G:frostbite.build.notests=false",
                "-G:package.FBBuild.enableAllCppTests=true",
                "-G:package.FBBuild.enableAllCSharpTests=true",
                "-G:Extension.DiceOnline.IntegrationTests.Enable=true",
            ]
        )
        framework_args_gensln = set_licensee(frostbite_licensee, framework_args_gensln)

        # Initialize
        builder = code.CodeUtils(platform, config)
        clean = clean.lower() == "true"
        if clean:
            builder.clean_local(close_handles=True)
        run_gensln(
            password=password,
            user=email,
            domain_user=domain_user,
            framework_args=framework_args_gensln,
            builder=builder,
        )
        # Build solution
        builder.buildsln()

    ## run the actual tests
    test_suite_has_failed, test_results = run_icepick_test_suites(
        test_suites=test_suites,
        icepick_run_args=icepick_run_args,
        run_bilbo=run_bilbo,
        code_branch=code_branch,
        code_changelist=code_changelist,
        data_branch=data_branch,
        data_changelist=data_changelist,
        test_definition=test_definition,
        platform=platform,
        config=config,
        test_group=test_group,
        build_type=build_type,
        autobuild=autobuild,
        framework_args_icepick=framework_args_icepick,
        icepick_settings_files=icepick_settings_files,
        icepicker=icepicker,
        lease=None,
        send_frosting_report=True,
        custom_test_suite_data=custom_test_suite_data,
        cook=icepick_cook,
    )

    LOGGER.info("Summary\n------\nRunning tests done. Results:\n{}\n---!---".format(test_results))
    if show_test_results and test_suite_has_failed:
        raise ELIPYException(
            "show_test_results_on_jenkins has been set to true. "
            "At least one test suite failed. Failing build."
        )


def run_icepick_test_suites(
    test_suites,
    icepick_run_args,
    run_bilbo,
    code_branch,
    code_changelist,
    data_branch,
    data_changelist,
    test_definition,
    platform,
    config,
    test_group,
    build_type,
    autobuild,
    framework_args_icepick,
    icepick_settings_files,
    icepicker,
    send_frosting_report,
    lease,
    custom_test_suite_data,
    cook,
):
    """
    run_icepick_test_suites
    """
    test_results = ""
    test_suite_has_failed = False
    for test_suite in test_suites:
        test_suite_run_args = icepick_run_args.copy()
        test_suite_name = test_suite["name"]

        LOGGER.info("Running test suite: {}".format(test_suite_name))
        if "extraArgs" in test_suite:
            test_suite_run_args.extend(test_suite["extraArgs"])
        LOGGER.info("-- test_suite_run_args --")
        for arg in test_suite_run_args:
            LOGGER.info(arg)
        LOGGER.info("---!---")

        # common arguments for register_autotest_results
        autotest_results_args = {
            "should_register_in_bilbo": run_bilbo,
            "code_branch": code_branch,
            "code_changelist": code_changelist,
            "data_branch": data_branch,
            "data_changelist": data_changelist,
            "test_definition": test_definition,
            "test_suite": test_suite_name,
            "platform": platform,
        }

        try:
            LOGGER.info("Test suite: " + test_suite_name)
            register_autotest_results(test_status="inprogress", **autotest_results_args)

            test_suite_data = (
                f"code_changelist:{code_changelist};" f"data_changelist:{data_changelist};"
            )
            if custom_test_suite_data:
                test_suite_data += f"{custom_test_suite_data};"

            icepicker.run_icepick(
                platform=platform,
                test_suite=test_suite_name,
                test_group=test_group,
                config=config,
                settings_file_list=icepick_settings_files,
                send_frosting_report=send_frosting_report,  # enable overlord
                lease=lease,
                build_type=build_type,
                autobuild=autobuild,
                run_args=test_suite_run_args,
                ignore_icepick_exit_code=False,
                cook=cook,
                extra_framework_args=framework_args_icepick,
                custom_test_suite_data=test_suite_data,
            )

            test_results += "- {} successful\n".format(test_suite_name)
            register_autotest_results(test_status="successful", **autotest_results_args)

        except Exception as exc:
            if fbenv_layer.is_api_function_failed_exception(exc):
                test_suite_has_failed = True
                test_results = handle_failure(test_results, test_suite_name, autotest_results_args)
                if exc.code < 0:
                    LOGGER.error(
                        "Something went wrong when running test suite {}. "
                        "Test results this far:\n{}".format(test_suite_name, test_results)
                    )
                    raise
                LOGGER.warning(exc)
            else:
                test_results = handle_failure(test_results, test_suite_name, autotest_results_args)
                LOGGER.error(
                    "Unexpected error when running test suite %s, Test results this far:\n%s",
                    test_suite_name,
                    test_results,
                )
                raise

    return test_suite_has_failed, test_results
