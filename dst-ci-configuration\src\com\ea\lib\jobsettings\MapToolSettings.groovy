package com.ea.lib.jobsettings

import com.ea.lib.LibCommonNonCps

class MapToolSettings extends JobSetting {
    void initializeMapToolStart(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        def modifiers = ['maptool']
        jobLabel = branchInfo.job_label ?: 'statebuild'
        isDisabled = LibCommonNonCps.get_setting_value(branchInfo, modifiers, 'disable_build', false)
        description = 'https://jaas.ea.com/browse/COBRA-143 && https://jaas.ea.com/browse/COBRA-138'
        cronTrigger = '@midnight'
        dataBranch = branchInfo.data_branch
        dataFolder = branchInfo.data_folder
        projectName = projectFile.name
    }
}
