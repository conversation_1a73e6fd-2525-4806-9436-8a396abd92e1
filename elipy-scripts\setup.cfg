[aliases]
test=pytest

[tool:pytest]
addopts = --verbose --cov-report term-missing --cov=dice_elipy_scripts -vv --durations=10 -m "not external_dep"
testpaths = dice_elipy_scripts/tests
python_files = *.py
env =
	ELIPY_TEST_RUN=TRUE
	TNT_ROOT=tnt_root
	GAME_DATA_DIR=game_data_dir
	GAME_ROOT=game_root
	EXE_MAIN_NAME=exe_main_name
	nantlocation=nantpath
	LICENSEE_ID=projectName
	fb_branch_id=test_branch
	USERNAME=test_user
  	PIP_INDEX_URL = https://artifacts.ea.com/artifactory/api/pypi/dre-pypi-federated/simple
  	PIP_EXTRA_INDEX_URL = https://pypi.python.org/simple

markers =
    external_dep: test that have external dependencies on services

[tox:tox]
min_version = 3.0
env_list = py{37,38,311}
requires =
    tox>3
    virtualenv>20.2

[testenv]
commands = pytest
allowlist_externals = pytest
set_env =
  PIP_INDEX_URL = https://artifacts.ea.com/artifactory/api/pypi/dre-pypi-federated/simple
  PIP_EXTRA_INDEX_URL = https://pypi.python.org/simple
