package com.ea.project

/* Settings that are common for all Cobra projects shall be here */

class Cobra {
    // Standard Git settings for all Cobra projects
    static String git_name_ci = 'origin'
    static String git_creds_ci = 'monkey-commons-ssh-v2'
    static String git_url_ci = '*****************:dre-cobra/dst-ci-configuration.git'
    static String git_branch_ci = 'master'
    static String git_targetdir_ci = 'ci'
    static String git_browser_url_ci = 'https://gitlab.ea.com/'
    static String git_browser_version_ci = '13.9'

    // Elipy setup
    static String git_url_elipy_setup = '*****************:dre-cobra/elipy/elipy-setup.git'
    // this variable only used for AWS agent due to connectivity issue, have to point to 'central' instance
    static String git_url_elipy_setup_aws = '*****************:dre-cobra/elipy/elipy-setup.git'
    static String elipy_install = 'C:\\dev\\ci\\install-elipy.bat'
    static String elipy_setup_env = 'C:\\dev\\ci\\setup-elipy-env.bat'

    // Project independent setup
    static String name = 'cobra'
    static String short_name = 'cob'
    static List<Map> af2_vault_credentials = [
        [
            vault_secret_path: 'artifacts/automation/dre-pypi-federated/ro',
            target_env_var   : 'AF2_USER',
            vault_secret_key : 'username',
        ],
        [
            vault_secret_path: 'artifacts/automation/dre-pypi-federated/ro',
            target_env_var   : 'AF2_TOKEN',
            vault_secret_key : 'reference_token',
        ],
        [
            vault_secret_path: 'artifacts/automation/dre-generic-federated/ro',
            target_env_var   : 'AF2_GENERIC_USER',
            vault_secret_key : 'username',
        ],
        [
            vault_secret_path: 'artifacts/automation/dre-generic-federated/ro',
            target_env_var   : 'AF2_GENERIC_TOKEN',
            vault_secret_key : 'reference_token',
        ],
    ]
}
