{#
    Command:
        bilbo_register_autotest
            short_help: Registers an autotest build in Bilbo.

    Arguments:

    Required variables:
        code_branch
            help: Perforce code branch/stream name.
            required: True
        code_changelist
            required: True
            help: Changelist number of code build used to verify data.
        data_branch
            help: Perforce data branch/stream name.
            required: True
        data_changelist
            help: Changelist number of data built.
            required: True
        test_definition
            help: Which test to register.
            required: True

    Optional variables:
        test_status
            help: The status of the test.
        run_bilbo
            default: False
            help: Whether or not to report test results to the configured metadata services
            required: False
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    bilbo_register_autotest
    --code-branch {{ code_branch }}
    --code-changelist {{ code_changelist }}
    --data-branch {{ data_branch }}
    --data-changelist {{ data_changelist }}
    --test-definition {{ test_definition }}
    {%- if test_status %}
    --test-status {{ test_status }}
    {%- endif %}
    {%- if run_bilbo %}
    --run-bilbo {{ run_bilbo }}
    {%- endif %}
  displayName: elipy bilbo_register_autotest
