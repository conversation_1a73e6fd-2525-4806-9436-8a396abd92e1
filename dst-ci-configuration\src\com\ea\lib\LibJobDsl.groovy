package com.ea.lib

import com.ea.CobraLogger
import javaposse.jobdsl.dsl.DslFactory
import javaposse.jobdsl.dsl.jobs.FreeStyleJob
import javaposse.jobdsl.dsl.jobs.WorkflowJob
import jenkins.model.Jenkins

/**
 This class should hold functions that act upon a DSL job/context.

 Logic that does not modify a job/context from JobDSL should not be placed in the class.
 */

class LibJobDsl {

    /**
     * Adds a publisher that will archive the Pipeline.log file when a job fails
     */
    static void archive_pipelinelog(FreeStyleJob job, String dataset = '*', String codeBranch = '*', String platform = '*', boolean archiveSuccess = false) {
        // This mapping is needed since Frostbite uses slightly different names for the platforms when storing PipelineAssetTypeSummary.
        Map<String, String> platformMapping = [
            'ps5'   : 'Ps5',
            'server': 'DedicatedServer',
            'win64' : 'Win32',
            'xbsx'  : 'Xbsx',
        ]
        job.with {
            publishers {
                flexiblePublish {
                    conditionalAction {
                        if (archiveSuccess) {
                            condition {
                                status('FAILURE', 'SUCCESS')
                            }
                        } else {
                            condition {
                                status('FAILURE', 'FAILURE')
                            }
                        }
                        publishers {
                            archiveArtifacts {
                                allowEmpty()
                                fingerprint(false)
                                pattern("${dataset}/.state/${codeBranch}/log/Pipeline.log")
                            }
                        }
                    }
                }
                flexiblePublish {
                    conditionalAction {
                        condition {
                            filesMatch("${dataset}/.state/${codeBranch}/log/buildStats/PipelineAssetTypeSummary_${platformMapping[platform]}.json")
                        }
                        publishers {
                            archiveArtifacts {
                                pattern("${dataset}/.state/${codeBranch}/log/buildStats/PipelineAssetTypeSummary_${platformMapping[platform]}.json")
                                allowEmpty()
                                fingerprint(false)
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Runs a post clean
     */
    static void postclean_silverback(def job, def project, def branch_info, def code_p4_server = null, def data_p4_server = null, boolean is_cloud = false, modifiers = []) {
        job.with {
            def elipy_call = LibCommonNonCps.get_setting_value(branch_info, [], 'elipy_call', '', project)
            def cloud_elipy_call = branch_info.azure_elipy_call ?: project.azure_elipy_call
            elipy_call = is_cloud ? cloud_elipy_call : elipy_call
            def workspace_root = LibCommonNonCps.get_setting_value(branch_info, [], 'workspace_root', '', project)
            def cloud_workspace_root = branch_info.azure_workspace_root ?: project.azure_workspace_root
            workspace_root = is_cloud ? cloud_workspace_root : workspace_root
            def azure_elipy_install_root = branch_info.azure_elipy_install_root ?: project.azure_elipy_install_root
            def elipy_install_root = is_cloud ? azure_elipy_install_root : null
            def delete_git_lock_file_repo_path_arg = elipy_install_root ? "--repo-path ${elipy_install_root}\\\\ci" : ''
            def p4_user_single_slash = is_cloud ? '%FROSTED_P4_DOMAINUSER%' : LibCommonNonCps.get_setting_value(branch_info, [], 'p4_user_single_slash', '', project)
            final CODE_SERVER = code_p4_server ?: LibCommonNonCps.get_setting_value(branch_info, modifiers, 'p4_code_server', '', project)
            final DATA_SERVER = data_p4_server ?: LibCommonNonCps.get_setting_value(branch_info, modifiers, 'p4_data_server', '', project)
            publishers {
                flexiblePublish {
                    conditionalAction {
                        condition {
                            status('ABORTED', 'FAILURE')
                        }
                        steps {
                            batchFile("${elipy_call} post_clean --user ${p4_user_single_slash}" +
                                " --code-client ${project.p4_code_client_env} --code-port ${CODE_SERVER}" +
                                " --data-client ${project.p4_data_client_env} --data-port ${DATA_SERVER} >> ${workspace_root}\\logs\\postclean_silverback.log 2>&1")
                        }
                    }
                }
                flexiblePublish {
                    conditionalAction {
                        condition {
                            status('ABORTED', 'FAILURE')
                        }
                        steps {
                            //Delete git .lock file sometimes left behind resulting in future jobs to fail. (To run on nodes)
                            batchFile("${elipy_call} delete_git_lock_file ${delete_git_lock_file_repo_path_arg} > %WORKSPACE%\\logs\\delete_git_lock_file.log 2>&1")
                        }
                    }
                }
            }
        }
    }

    /**
     * Runs the kill_processes script to clean up game processes left running
     */
    static void kill_processes(def job, def branch_info, boolean is_cloud = false) {
        job.with {
            publishers {
                flexiblePublish {
                    conditionalAction {
                        condition {
                            status('ABORTED', 'FAILURE')
                        }
                        steps {
                            batchFile((is_cloud ? branch_info.azure_elipy_call : branch_info.elipy_call) + ' kill_processes > %WORKSPACE%\\logs\\kill_processes.log 2>&1')
                        }
                    }
                }
            }
        }
    }

    /**
     * Runs the backup_local script to backup tnt/local dir for later troubleshooting
     */
    static void backup_local(def job, def branch_info) {
        job.with {
            publishers {
                flexiblePublish {
                    conditionalAction {
                        condition {
                            status('ABORTED', 'FAILURE')
                        }
                        steps {
                            batchFile(branch_info.elipy_call + ' backup_local > ' + branch_info.workspace_root + '\\logs\\backup_local.log 2>&1')
                        }
                    }
                }
            }
        }
    }

    /**
     * Adds a 'Prepare an environment for the run' step to a job which will call for p4 revert command for
     * both code and data streams. Can do an optional deletion for tnt\bin\python directory
     * Works only for non pipeline jobs
     */
    static void initialP4revert(
        def job, def project, def branchInfo,
        boolean code = true, boolean data = true, boolean wipePythonVenv = true,
        def code_preflight = null, def data_preflight = null, boolean is_cloud = false, def modifiers = []
    ) {
        job.with {
            environmentVariables {
                keepBuildVariables(true)
                keepSystemVariables(true)
                if (branchInfo?.environment_variables) {
                    branchInfo.environment_variables.each { key, value ->
                        env(key, value)
                    }
                }
                script(LibCommonCps.p4RevertScript(project, branchInfo, code, data, wipePythonVenv, code_preflight, data_preflight, is_cloud, modifiers))
            }
        }
    }

    static void initialAWSP4revert(def job, def project, def branchInfo) {
        job.with {
            environmentVariables {
                keepBuildVariables(true)
                keepSystemVariables(true)
                script(LibCommonNonCps.p4AWSRevertScript(project, branchInfo))
            }
        }
    }

    /**
     * Safely set primary view of Jenkins controller.
     */
    static void setPrimaryView(Jenkins controller, primaryViewName) {
        def primaryView = controller.views.find { view -> view.displayName == primaryViewName }

        if (primaryView) {
            controller.primaryView = primaryView
        } else {
            CobraLogger.warning("Tried to set primary view to $primaryViewName, but no view of that name was found.")
        }
    }

    static WorkflowJob standardPipelineJob(DslFactory dslFactory, String jobName, String schedulerFile) {
        return dslFactory.pipelineJob(jobName) {
            definition {
                cps {
                    script(dslFactory.readFileFromWorkspace(schedulerFile))
                    sandbox()
                }
            }
            logRotator(7, 100)
            properties {
                disableConcurrentBuilds()
                disableResume()
            }
            quietPeriod(0)
        }
    }

    /**
     * Method to copy builds to other studios.
     */
    static void store_offsite_zip(FreeStyleJob job, Map branchInfo, Boolean basicDroneBuild = false, Boolean basicDroneZip = false, List outsourcers = []) {
        // Set values for variables.
        String codeBranch = branchInfo.code_branch
        String elipyCall = branchInfo.elipy_call
        String extraArgs = branchInfo.extra_offsite_args ?: ''
        if (basicDroneBuild) {
            extraArgs += ' --basic-drone-build'
        }
        if (basicDroneZip) {
            extraArgs += ' --basic-drone-zip'
        }
        if (branchInfo.qa_verified_builds_only) {
            extraArgs += ' --qa-verified-builds-only'
        }
        if (branchInfo.offsite_basic_drone_zip_builds_force_zip_override) {
            extraArgs += ' --force-update-zip'
        }
        for (outsourcer in outsourcers) {
            extraArgs += " --outsourcer ${outsourcer}"
        }

        // Add sections to the Jenkins job.
        job.with {
            steps {
                batchFile(elipyCall + ' offsitebuild --code-changelist %code_changelist% --code-branch ' + codeBranch + ' ' + extraArgs)
            }
        }
    }

    static void curl_drone_builds(def job, def branch_info) {
        job.with {
            steps {
                batchFile(LibCommonCps.get_offsite_curl_cmd(branch_info))
            }
        }
    }

    /**
     * Tell Jenkins to add all .log files from 'logs' directory (non build specific logs) as build artifacts
     * Works only for NON-pipeline jobs
     */
    static void archive_non_build_logs(FreeStyleJob job, def branch_info = [:]) {
        String codeBranch = branch_info?.code_branch
        String dataSet = branch_info?.dataset

        job.with {
            publishers {
                flexiblePublish {
                    conditionalAction {
                        condition {
                            filesMatch('logs/*.log')
                        }
                        publishers {
                            archiveArtifacts {
                                pattern('logs/*.log')
                                allowEmpty()
                                fingerprint(false)
                            }
                        }
                    }
                }
                flexiblePublish {
                    conditionalAction {
                        condition {
                            filesMatch('TnT/*.log')
                        }
                        publishers {
                            archiveArtifacts {
                                pattern('TnT/*.log')
                                allowEmpty()
                                fingerprint(false)
                            }
                        }
                    }
                }
                flexiblePublish {
                    conditionalAction {
                        condition {
                            filesMatch('TnT/FrostyLogFile.txt')
                        }
                        publishers {
                            archiveArtifacts {
                                pattern('TnT/FrostyLogFile.txt')
                                allowEmpty()
                                fingerprint(false)
                            }
                        }
                    }
                }
                flexiblePublish {
                    conditionalAction {
                        condition {
                            filesMatch('logs/MSBuild_*.failure.txt')
                        }
                        publishers {
                            archiveArtifacts {
                                pattern('logs/MSBuild_*.failure.txt')
                                allowEmpty()
                                fingerprint(false)
                            }
                        }
                    }
                }
                flexiblePublish {
                    conditionalAction {
                        condition {
                            filesMatch('TnT/Local/Frosty/Output/*.pkg.verify.log')
                        }
                        publishers {
                            archiveArtifacts {
                                pattern('TnT/Local/Frosty/Output/*.pkg.verify.log')
                                allowEmpty()
                                fingerprint(false)
                            }
                        }
                    }
                }
                // COBRA-2762 - BCT needs additional Logs for mp_abbasid to assist in debugging crashes.
                if (codeBranch && dataSet) {
                    flexiblePublish {
                        conditionalAction {
                            condition {
                                filesMatch("${dataSet}/.state/${codeBranch}/debugOutput/Win32/game/glaciermp/levels/mp_abbasid/mp_abbasid_networkregistry_debug.log")
                            }
                            publishers {
                                archiveArtifacts {
                                    pattern("${dataSet}/.state/${codeBranch}/debugOutput/Win32/game/glaciermp/levels/mp_abbasid/mp_abbasid_networkregistry_debug.log")
                                    allowEmpty()
                                    fingerprint(false)
                                }
                            }
                        }
                    }
                    flexiblePublish {
                        conditionalAction {
                            condition {
                                filesMatch("${dataSet}/.state/${codeBranch}/debugOutput/Win32/game/glaciermp/levels/mp_abbasid/_layers_gameplay/gameplay_networkregistry_debug.log")
                            }
                            publishers {
                                archiveArtifacts {
                                    pattern("${dataSet}/.state/${codeBranch}/debugOutput/Win32/game/glaciermp/levels/mp_abbasid/_layers_gameplay/gameplay_networkregistry_debug.log")
                                    allowEmpty()
                                    fingerprint(false)
                                }
                            }
                        }
                    }
                    flexiblePublish {
                        conditionalAction {
                            condition {
                                filesMatch("${dataSet}/.state/${codeBranch}/debugOutput/DedicatedServer/game/glaciermp/levels/mp_abbasid/mp_abbasid_networkregistry_debug.log")
                            }
                            publishers {
                                archiveArtifacts {
                                    pattern("${dataSet}/.state/${codeBranch}/debugOutput/DedicatedServer/game/glaciermp/levels/mp_abbasid/mp_abbasid_networkregistry_debug.log")
                                    allowEmpty()
                                    fingerprint(false)
                                }
                            }
                        }
                    }
                    flexiblePublish {
                        conditionalAction {
                            condition {
                                filesMatch("${dataSet}/.state/${codeBranch}/debugOutput/DedicatedServer/game/glaciermp/levels/mp_abbasid/_layers_gameplay/gameplay_networkregistry_debug.log")
                            }
                            publishers {
                                archiveArtifacts {
                                    pattern("${dataSet}/.state/${codeBranch}/debugOutput/DedicatedServer/game/glaciermp/levels/mp_abbasid/_layers_gameplay/gameplay_networkregistry_debug.log")
                                    allowEmpty()
                                    fingerprint(false)
                                }
                            }
                        }
                    }
                }
                flexiblePublish {
                    conditionalAction {
                        condition {
                            filesMatch('${TEMP}\\ELIPY\\jenkins\\${JOB_NAME}\\${BUILD_ID}\\*.json')
                        }
                        publishers {
                            archiveArtifacts {
                                pattern('${TEMP}\\ELIPY\\jenkins\\${JOB_NAME}\\${BUILD_ID}\\*.json')
                                allowEmpty()
                                fingerprint(false)
                            }
                        }
                    }
                }
                flexiblePublish {
                    conditionalAction {
                        condition {
                            filesMatch("${dataSet}/.state/${codeBranch}/reports/indeterminism/**/*")
                        }
                        publishers {
                            archiveArtifacts {
                                pattern("${dataSet}/.state/${codeBranch}/reports/indeterminism/**/*")
                                allowEmpty()
                                fingerprint(false)
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Delete git .lock file sometimes left behind resulting in future jobs to fail. (To run on Jenkins master)
     */
    static void delete_unix_git_lock_file(def job) {
        job.with {
            publishers {
                flexiblePublish {
                    conditionalAction {
                        condition {
                            alwaysRun()
                        }
                        steps {
                            shell(
                                '''
                            echo Deleting .git lock file if present and logging to ES
                            file=/var/jenkins_home/workspace/${JOB_NAME}/ci/.git/shallow.lock

                            if test -f "$file"; then
                                echo "$file exists, removing..."
                                rm -f /var/jenkins_home/workspace/${JOB_NAME}/ci/.git/shallow.lock

                                echo 'Logging to ES'
                                curlCmd=$(curl -XPOST 'https://dre-metrics-eck.cobra.dre.ea.com/monitor-errors_'$(date +'%Y.%m')'/_doc' -H 'Content-Type: application/json' -d'
                                {
                                    'hostname': "'"$HOSTNAME"'",
                                    'timestamp': "'"$(date +%Y-%m-%dT%H:%M:%S)"'",
                                    'job_name': "'"$JOB_NAME"'",
                                    'build_url': "'"$BUILD_URL"'",
                                    'error_info': { 'LockFile': "'"$file"'" },
                                    'error': 'gitlockfile'
                                }
                                ')
                                echo $curlCmd
                            fi
                            ''')
                        }
                    }
                }
            }
        }
    }

    static void claim_builds(def job, def branch_info) {
        def claim_builds = branch_info.claim_builds ?: false
        if (claim_builds == true) {
            job.with {
                publishers {
                    allowBrokenBuildClaiming()
                }
            }
        }
    }

    // to prevent long startup time, call extra step in the end of codepreflight job as marker
    // ONLY when the codepreflight job or post-preflight is success
    static void aws_extra_steps(def job) {
        job.with {
            publishers {
                flexiblePublish {
                    conditionalAction {
                        condition {
                            status('SUCCESS', 'SUCCESS')
                        }
                        steps {
                            batchFile("echo 'Jenkins has done build on this agent' > C:\\JenkinsBuildtrack.txt")
                        }
                    }
                }
            }
        }
    }

    /**
     * Runs the import_builds_stats_quickscope script to gather pipeline  statistics in quickscope
     */
    static void import_builds_stats_quickscope(def job, def branch_info) {
        def quickscope_import = branch_info.quickscope_import ?: false
        if (quickscope_import == true) {
            def elipy_call = branch_info.elipy_call
            def quickscope_db = branch_info.quickscope_db
            job.with {
                publishers {
                    flexiblePublish {
                        conditionalAction {
                            steps {
                                batchFile("${elipy_call} quickscope_import --quickscope-db ${quickscope_db}")
                            }
                            condition {
                                alwaysRun()
                            }
                        }
                    }
                }
            }
        }
    }

    static void trigger_marvin_upload_on_external_jenkins(def job, def project, def branch_info, def platform, def variant) {
        def external_job = project.external_job
        def filer_path = project.name + '\\frosty\\' + project.frostbite_licensee + '\\' + branch_info.data_branch + '\\%data_changelist%\\' +
            branch_info.code_branch + '\\%code_changelist%\\' + platform + '\\' + variant.format + '\\' + variant.region + '\\' + variant.config
        def cl = branch_info.data_branch + '.%data_changelist%.' + branch_info.code_branch + '.%code_changelist%'
        def params_map = project.external_params + [cl: cl, filerPath: filer_path]

        def credentials = '-u %marvin_upload_user%:%marvin_upload_password%'
        def job_url = 'https://' + external_job.server + '/job/' + external_job.job + '/buildWithParameters?'
        def params_string = params_map.collect { /$it.key=$it.value/ }.join('&')
        def url = '"' + job_url + params_string + '"'
        trigger_build_on_external_jenkins(job, credentials, url)
    }

    static void trigger_build_on_external_jenkins(def job, def credentials, def url) {
        def curl_cmd = 'C:\\ProgramData\\Chocolatey\\bin\\curl -X POST'
        job.with {
            publishers {
                flexiblePublish {
                    conditionalAction {
                        steps {
                            batchFile(curl_cmd + ' --insecure ' + credentials + ' ' + url)
                        }
                        condition {
                            alwaysRun()
                        }
                    }
                }
            }
        }
    }

    static void trigger_gitlab_build_linux(def job, def branch_info, def platform) {
        def platform_name = ''

        if (platform == 'linuxserver') {
            platform_name = 'server'
        } else if (platform == 'linux64') {
            platform_name = 'client'
        }

        def downstream_job = branch_info.branch_name + '.linux.' + platform_name + '.docker-image'

        def predefinedMap = [
            'code_branch': branch_info.code_branch,
            'data_branch': branch_info.data_branch,
        ]
        job.with {
            publishers {
                downstreamParameterized {
                    trigger(downstream_job) {
                        condition('SUCCESS')
                        parameters {
                            currentBuild()
                            predefinedProps(predefinedMap)
                        }
                    }
                }
            }
        }
    }

    /**
     * Method to install Elipy on a build machine.
     */
    static void installElipy(def context, String elipyInstallCall, def project) {
        context.with {
            batchFile(elipyInstallCall)
        }

        if (['vault_credentials', 'vault_variable'].every { project.metaClass.hasProperty(project, it) }) {
            context.item.with {
                configure { contextJob ->
                    contextJob / 'buildWrappers' / 'org.jenkinsci.plugins.credentialsbinding.impl.SecretBuildWrapper' / 'bindings' << 'org.jenkinsci.plugins.credentialsbinding.impl.StringBinding' {
                        variable(project.vault_variable)
                        credentialsId(project.vault_credentials)
                    }
                }
            }
        }
    }

    /**
     * Adds a downstream job to a freestyle Jenkins job.
     * If downstreamJobName starts with 'https', triggers a remote Jenkins job using the provided details.
     *
     * @param job The Jenkins job context
     * @param downstreamJobName The name or URL of the downstream job
     * @param jobParameters A map containing parameters for the job
     * @param creds The Jenkins credentials ID required to trigger a remote Jenkins job
     * @throws IllegalArgumentException if downstreamJobName starts with 'https' but 'creds' is missing
     */
    static void addAnyDownstreamJobToFreestyleJob(def job, def downstreamJobName, Map<String, Object> jobParameters = [:], String creds = null) {
        if (downstreamJobName.startsWith('https')) {
            String jobUrl = downstreamJobName

            if (!creds) {
                def error_msg = "Error: Missing 'creds'. The 'creds' parameter refers to the Jenkins credentials ID, " +
                    'which is required to trigger the remote Jenkins job.'
                throw new IllegalArgumentException(error_msg)
            }

            triggerAnyRemoteJenkinsJobFromFreeStyleJob(job, jobUrl, jobParameters, creds)
        } else {
            job.with {
                publishers {
                    downstreamParameterized {
                        trigger(downstreamJobName) {
                            condition('SUCCESS')
                            parameters {
                                predefinedProps(jobParameters)
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Triggers a remote Jenkins job from freestyle job. .
     *
     * @param context Jenkins freestyle job context
     * @param jobUrl The full URL of the remote Jenkins job
     * @param jobParameters A map containing parameters for the job
     * @param credsId The Jenkins credentials ID for authentication
     */
    static void triggerAnyRemoteJenkinsJobFromFreeStyleJob(def freestyleJob, String jobUrl, Map<String, Object> jobParameters, String credsId) {
        freestyleJob.with {
            steps {
                configure { contextJob ->
                    contextJob / 'builders' / 'org.jenkinsci.plugins.ParameterizedRemoteTrigger.RemoteBuildConfiguration' {
                        job(jobUrl)
                        shouldNotFailBuild(true)
                        blockBuildUntilComplete(false)
                        preventRemoteBuildQueue(false)
                        maxConn(1)
                        overrideAuth(true)
                        auth {
                            'org.jenkinsci.plugins.ParameterizedRemoteTrigger.Auth' {
                                authType('credentialsPlugin')
                                creds(credsId)
                            }
                        }
                        if (!jobParameters.isEmpty()) {
                            def paramsString = jobParameters.collect { key, value -> "${key}=${value ?: ''}" }.join('\n')
                            parameters(paramsString)
                        }
                    }
                }
            }
        }
    }

    @SuppressWarnings('UnnecessaryGetter')
    static void addVaultSecrets(def job, def branch_info = [:], def project_info = null, List<Map> vault_secrets_job = []) {
        '''
        Resolves and applies vault secrets to a Jenkins job.
        @param job The Jenkins job to which the vault secrets will be applied.
        @param branch_info object from which to parse branch vault secrets.
        @param project_info object from which to parse project vault secrets.
        @param vault_secrets_job A List<Map> of vault secrets specific to the job.
        '''
        def project = project_info ?: branch_info.project
        def vault_secrets_project = LibCommonNonCps.get_setting_value(branch_info, [], 'vault_secrets_project', [], project)
        def vault_secrets_branch = LibCommonNonCps.get_setting_value(branch_info, [], 'vault_secrets_branch', [], project)
        def vault_secrets = vault_secrets_project + vault_secrets_branch + vault_secrets_job
        def vaultSecretsHandler = new LibVaultSecretHandler()
        vaultSecretsHandler.vault_secrets = LibVaultSecretHandler.mergeVaultSecrets(vault_secrets)

        job.with {
            wrappers {
                vaultBuildWrapper(vaultSecretsHandler.getVaultConfigurationDSL())
            }
        }
    }

    /**
     * Adds a post-build step to reboot the agent when the main job completes.
     * This helps resolve build failures caused by VM issues like avalanche errors and locked files.
     * The reboot runs regardless of job success or failure to ensure VM state is cleared.
     * Uses direct reboot command instead of downstream job to avoid executor spam.
     * @param job The Jenkins job to which the agent reboot step will be added.
     */
    static void agent_reboot_on_completion(def job) {
        job.with {
            publishers {
                flexiblePublish {
                    conditionalAction {
                        condition {
                            status('ABORTED', 'SUCCESS')
                        }
                        steps {
                            batchFile('echo Rebooting agent to clear VM state... && shutdown /r /t 30 /c "Jenkins job completed - rebooting agent for VM cleanup"')
                        }
                    }
                }
            }
        }
    }
}
