package vars

import com.ea.project.Cobra
import support.DeclarativePipelineSpockTest

class ProjectClassSpec extends DeclarativePipelineSpockTest {

    void 'test vars ProjectClass'() {
        when:
        Script script = loadScript('ProjectClass.groovy')
        def project = script.invokeMethod('call', 'cobra')
        printCallStack()

        then:
        project.getClass() == Cobra.getClass()
    }

}
