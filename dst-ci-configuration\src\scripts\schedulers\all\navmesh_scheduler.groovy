package scripts.schedulers.all

import com.ea.project.GetBranchFile
import com.ea.lib.<PERSON><PERSON><PERSON><PERSON><PERSON>

def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
def project = ProjectClass(env.project_name)

/**
 * navmesh_scheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Get changelist from Perforce') {
            steps {
                script {
                    def settings_map = branchfile.general_settings + branchfile.standard_jobs_settings
                    P4PreviewData(project, 'navmesh', env.data_folder, env.data_branch, env.non_virtual_data_folder, env.non_virtual_data_branch, settings_map)
                }
            }
        }
        stage('Trigger navmesh jobs') {
            steps {
                script {
                    def code_changelist = ''
                    def data_changelist = ''
                    def last_good_code = LibJenkins.getLastStableCodeChangelist(env.data_reference_job)
                    code_changelist = params.code_changelist ?: last_good_code
                    data_changelist = params.data_changelist ?: env.P4_CHANGELIST
                    if (env.frostbite_syncer_setup.toBoolean() == true) {
                        data_changelist = params.data_changelist ?: last_good_code
                    }
                    def clean_data = params.clean_data

                    if (code_changelist == null) {
                        echo 'Missing code changelist, aborting build!'
                        currentBuild.result = Result.FAILURE.toString()
                        return
                    }

                    def args = [
                        string(name: 'code_changelist', value: code_changelist),
                        string(name: 'data_changelist', value: data_changelist),
                        string(name: 'clean_data', value: clean_data),
                    ]
                    def inject_map = [
                        'code_changelist': code_changelist,
                        'data_changelist': data_changelist,
                    ]
                    EnvInject(currentBuild, inject_map)
                    currentBuild.displayName = env.JOB_NAME + '.' + data_changelist + '.' + code_changelist

                    def job_name = env.branch_name + '.navmesh'
                    def navmesh_job = build(job: job_name, parameters: args, propagate: false)

                    currentBuild.result = navmesh_job.result.toString()

                    def slack_settings = branchfile.standard_jobs_settings?.slack_channel_navmesh
                    SlackMessageNew(currentBuild, slack_settings, project.short_name)

                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
}
