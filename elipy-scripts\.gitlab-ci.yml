image: dre-docker-federated.artifacts.ea.com/cobra/python-dre-cobra:latest_37

default:
  id_tokens:
    VAULT_ID_TOKEN:
      aud: https://ess.ea.com
    VAULT_JWT_TOKEN:
      aud: https://gitlab.ea.com

.install_deps:
  secrets:
    PYTHON_USER:
      vault: artifacts/automation/dre-pypi-federated/rw/username@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
    PYTHON_PASSWORD:
      vault: artifacts/automation/dre-pypi-federated/rw/reference_token@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
    DOCKER_AUTH_CONFIG:
      vault: artifacts/automation/dre-docker-federated/ro/DOCKER_AUTH_CONFIG@secrets/kv
      token: $VAULT_JWT_TOKEN
      file: false
  before_script:
    - apk add curl
    - echo -e "machine artifacts.ea.com\nlogin ${PYTHON_USER}\npassword ${PYTHON_PASSWORD}" > $HOME/.netrc
    - pip3 install -r requirements.txt

stages:
 - test
 - deploy

variables:
  VAULT_AUTH_PATH: "jwt/gitlab"
  ARTIFACTORY_URL: https://artifacts.ea.com/artifactory/api/pypi/dre-pypi-federated
  PYTHON_INDEX_REPO: https://artifacts.ea.com/artifactory/api/pypi/dre-pypi-federated
  VAULT_SERVER_URL: "https://ess.ea.com"
  VAULT_AUTH_ROLE: "gl-dre-cobra-elipy"
  VAULT_NAMESPACE: "cds-dre-prod"
  VAULT_RATE_LIMIT: 50

black:
  stage: test
  extends: .install_deps
  script:
    - python -m black -l 100 --diff .
    - python -m black -l 100 --check -q .
  tags:
    - glaas-shared-k8s

linter:
  stage: test
  extends: .install_deps
  script:
    - python3 -m pylint dice_elipy_scripts -r y
  tags:
    - glaas-shared-k8s

editorconfig-validation:
  stage: test
  extends: .install_deps
  script:
    - ec
  tags:
    - glaas-shared-k8s

unit:
  stage: test
  extends: .install_deps
  script:
    - python3 setup.py test
  coverage: /(?i)total.*? (100(?:\.0+)?\%|[1-9]?\d(?:\.\d+)?\%)$/
  tags:
    - glaas-shared-k8s

elipy2-tox-pytest-py3_7:
  image: dre-docker-federated.artifacts.ea.com/cobra/python-dre-cobra:latest_37
  stage: test
  extends: .install_deps
  script:
    - tox -e py37
  tags:
    - glaas-shared-k8s

elipy2-tox-pytest-py3_8:
  image: dre-docker-federated.artifacts.ea.com/cobra/python-dre-cobra:latest_38
  stage: test
  extends: .install_deps
  script:
    - tox -e py38
  tags:
    - glaas-shared-k8s

elipy2-tox-pytest-py3_11:
  image: dre-docker-federated.artifacts.ea.com/cobra/python-dre-cobra:latest_311
  stage: test
  extends: .install_deps
  script:
    - tox -e py311
  tags:
    - glaas-shared-k8s

test docs:
  stage: test
  extends: .install_deps
  script:
  - sphinx-apidoc -f -o docs/source/ dice_elipy_scripts */tests
  - sphinx-build -b html ./docs/source/ public
  only:
  - branches
  except:
  - master

pages:
  stage: deploy
  extends: .install_deps
  script:
  - sphinx-apidoc -f -o docs/source/ dice_elipy_scripts */tests
  - sphinx-build -b html ./docs/source/ public
  artifacts:
    paths:
    - public
  only:
  - master

.deploy_af2_template:
  extends:
    - .install_deps
  stage: deploy
  tags:
    - glaas-shared-k8s
  script:
    - |+
      echo "[distutils]
      index-servers = ${PYTHON_INDEX_REPO}
      [${PYTHON_INDEX_REPO}]
      repository = ${PYTHON_INDEX_REPO}
      username = ${PYTHON_USER}
      password = ${PYTHON_PASSWORD}" >> ~/.pypirc
    - python3 setup.py bdist_wheel upload -r ${PYTHON_INDEX_REPO}

deploy-pre-release-to-af2-prod-repo:
  extends: .deploy_af2_template
  except:
    - master
  variables:
    IS_PRERELEASE: "true"

deploy-release-to-af2-prod-repo:
  extends: .deploy_af2_template
  only:
    - master
  variables:
    IS_PRERELEASE: "false"
