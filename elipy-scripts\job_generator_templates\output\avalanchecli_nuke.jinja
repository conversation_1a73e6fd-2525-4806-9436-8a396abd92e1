{#
    Command:
        avalanchecli_nuke
            short_help: Perform Avalanchecli nuke -y on the localhost.

    Arguments:

    Required variables:
        datadir
            help: Which datadir to nuke from.
            required: True

    Optional variables:
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    avalanchecli_nuke
    --datadir {{ datadir }}
  displayName: elipy avalanchecli_nuke
