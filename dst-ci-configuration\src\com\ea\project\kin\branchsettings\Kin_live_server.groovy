package com.ea.project.kin.branchsettings

class Kin_live_server {
    // Settings for jobs
    static Class project = com.ea.project.kin.Kingston
    static Map general_settings = [
        dataset           : project.dataset,
        frostbite_licensee: project.frostbite_licensee,
        elipy_install_call: project.elipy_install_call,
        elipy_call        : project.elipy_call,
        workspace_root    : project.workspace_root,
    ]
    static Map standard_jobs_settings = [
        asset                     : 'ShippingLevels',
        clean_local               : true,
        enable_eac                : true,
        frosty_reference_job      : 'kin-live-server.code.start',
        linux_docker_images       : false,
        server_asset              : 'ShippingLevels',
        shift_branch              : true,
        shift_every_build         : true,
        shift_reference_job       : 'kin-live-server.patchfrosty.start',
        trigger_type_frosty       : 'scm',
        skip_frosty_trigger       : true,
        slack_channel_code        : [
            channels                  : ['#kin-build-notify'],
            skip_for_multiple_failures: true,
        ],
        slack_channel_patchfrosty : [
            channels                  : ['#kin-build-notify'],
            skip_for_multiple_failures: true,
        ],
    ]
    static Map preflight_settings = [
        p4_code_server             : 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
        p4_code_creds              : 'dice-p4buildedge03-fb',
    ]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'linux64server', configs: ['final']],
        [name: 'tool', configs: ['release']],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = [
        [name: '.frosty.start', args: []]
    ]
    static List data_matrix = []
    static List data_downstream_matrix = []
    static List patchdata_matrix = []
    static List frosty_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww', args: '']]],
    ]
    static List frosty_downstream_matrix = [
        [name: '.shift.upload', args: ['code_changelist', 'data_changelist']],
    ]
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = [
        [name: '.spin.linuxserver.digital.final.ww', args: ['code_changelist', 'data_changelist']],
    ]
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww']]],
    ]
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
