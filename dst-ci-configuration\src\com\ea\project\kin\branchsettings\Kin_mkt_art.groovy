package com.ea.project.kin.branchsettings

class Kin_mkt_art {
    // Settings for jobs
    static Class project = com.ea.project.kin.Kingston
    static Map general_settings = [
        dataset           : project.dataset,
        frostbite_licensee: project.frostbite_licensee,
        elipy_install_call: project.elipy_install_call,
        elipy_call        : project.elipy_call,
        workspace_root    : project.workspace_root,
    ]
    static Map standard_jobs_settings = [
        asset                          : 'Marketing/GA_Preflight_kin-mkt-art',
        data_reference_job             : 'kin-dev.kindata.upgrade.data',
        extra_data_path                : 'future/future-dev-content',
        extra_locations_bilbo_drone_job: [],
        import_avalanche_state         : false,
        poolbuild_data                 : true,
        server_asset                   : 'Marketing/GA_Preflight_kin-mkt-art',
        timeout_hours_data             : 5,
        use_recompression_cache        : false,
        slack_channel_code             : [
            channels                  : ['#kin-mkt-art-stream'],
            skip_for_multiple_failures: true,
        ],
    ]
    static Map preflight_settings = [
        p4_code_server             : 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
        p4_code_creds              : 'dice-p4buildedge03-fb',
    ]
    //	concurrent_data: 2,
    //	pre_preflight: true,
    //	statebuild_datapreflight: false,
    //	slack_channel_preflight: [channels: ['#cobra-build-preflight']],
    //	datapreflight_reference_job: 'kin-mkt-art.data.lastknowngood',
    // ]

    // Matrix definitions for jobs
    static List code_matrix = []
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = []
    static List data_matrix = [
        'win64',
    ]
    static List data_downstream_matrix = []
    static List patchdata_matrix = []
    static List frosty_matrix = []
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = [
        //	[name: 'win64', platform: 'win64', assets: ['PreflightLevels'], extra_label: ''],
    ]
    static List spin_upload_matrix = []
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
