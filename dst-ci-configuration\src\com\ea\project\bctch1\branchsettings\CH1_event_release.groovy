package com.ea.project.bctch1.branchsettings

import com.ea.project.bctch1.BctCh1

class CH1_event_release {
    static Class project = BctCh1
    static Map general_settings = [
        dataset                 : project.dataset,
        elipy_call              : project.elipy_setup_call + ' && elipy --location event --use-fbenv-core',
        elipy_install_call      : project.elipy_install_call,
        frostbite_licensee      : project.frostbite_licensee,
        workspace_root          : project.workspace_root,
        azure_elipy_call        : project.azure_elipy_call,
        azure_elipy_install_call: project.azure_elipy_install_call,
        azure_workspace_root    : project.azure_workspace_root,
        job_label_statebuild    : 'statebuild_criterion',
        p4_code_creds           : 'perforce-battlefield-criterion',
        p4_data_creds           : 'perforce-battlefield-criterion',
        p4_code_server          : 'oh-p4edge-fb.eu.ad.ea.com:2001',
        p4_data_server          : 'oh-p4edge-fb.eu.ad.ea.com:2001',
    ]
    static Map code_settings = [
        extra_code_args              : [' --framework-args -G:BattlefieldGame-onlineSku=event'],
        skip_code_build_if_no_changes: false,
        slack_channel_code           : [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        statebuild_code              : false,
        sndbs_enabled                : true,
    ]
    static Map data_settings = [
        slack_channel_patchdata: [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        statebuild_data        : false,
        statebuild_webexport   : false,
        timeout_hours_data     : 6,
        webexport_branch       : true,
    ]
    static Map frosty_settings = [
        enable_eac_win64_digital : true,
        enable_eac_win64_steam   : true,
        slack_channel_patchfrosty: [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        statebuild_frosty        : false,
        frosty_reference_job     : 'CH1-event-release.deployment-data.start',
        timeout_hours_frosty     : 5,
        use_linuxclient          : true,
    ]
    static Map standard_jobs_settings = code_settings + data_settings + frosty_settings + [
        asset                     : 'Game/Setup/Build/EventClientLevels.dbx',
        baseline_set              : false,
        enable_lkg_p4_counters    : true,
        extra_data_args           : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true --pipeline-args -Pipeline.UpdateAssetIndeterminismIsError --pipeline-args false '],
        extra_frosty_args         : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        is_virtual_stream         : true,
        server_asset              : 'Game/Setup/Build/EventServerLevels.dbx',
        shift_branch              : true,
        shift_every_build         : true,
        skip_icepick_settings_file: true,
        strip_symbols             : false,
        use_super_bundles         : true,
        oreans_protection         : true,
    ]
    static Map icepick_settings = [:]
    static Map preflight_settings = [:]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'linux64', configs: ['final']],
        [name: 'linux64server', configs: ['final']],
        [name: 'ps5', configs: ['final', 'retail']],
        [name: 'tool', configs: ['release']],
        [name: 'win64game', configs: ['final', 'retail']],
        [name: 'win64server', configs: ['final']],
        [name: 'xbsx', configs: ['final', 'retail']],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = [
        [name: '.code.p4counterupdater', args: ['code_changelist', 'code_countername']],
        [name: '.patchdata.start', args: []],
    ]
    static List data_matrix = []
    static List data_downstream_matrix = []
    static List patchdata_matrix = [
        [name: 'ps5'],
        [name: 'win64'],
        [name: 'xbsx'],
    ]
    static List patchdata_downstream_matrix = [
        [name: '.data.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.data.p4cleancounterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.patchfrosty.start', args: []],
    ]
    static List frosty_matrix = []
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'event', args: '']]],
        [name: 'server', variants: [[format: 'files', config: 'final', region: 'event', args: '']]],
        [name: 'win64', variants: [[format: 'files', config: 'final', region: 'event', args: ''],
                                   [format: 'digital', config: 'final', region: 'event', args: ''],
                                   [format: 'digital', config: 'retail', region: 'event', args: ''],
                                   [format: 'steam', config: 'final', region: 'event', args: ''],
                                   [format: 'steam', config: 'retail', region: 'event', args: '']]],
        [name: 'ps5', variants: [[format: 'digital', config: 'final', region: 'event', args: ''],
                                 [format: 'digital', config: 'retail', region: 'event', args: ''],
                                 [format: 'files', config: 'final', region: 'event', args: '']]],
        [name: 'xbsx', variants: [[format: 'digital', config: 'final', region: 'event', args: ''],
                                  [format: 'digital', config: 'retail', region: 'event', args: ''],
                                  [format: 'files', config: 'final', region: 'event', args: '']]],
        [name: 'linux64', variants: [[format: 'files', config: 'final', region: 'event', args: '']]],
    ]
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = [
        [name: '.frosty.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.spin.linuxserver.digital.final.event', args: ['code_changelist', 'data_changelist']],
        [name: '.shift.upload', args: ['code_changelist', 'data_changelist']],
        [name: '.win64.upload_to_steam.event.final', args: ['code_changelist', 'data_changelist']],
        [name: '.win64.upload_to_steam.event.retail', args: ['code_changelist', 'data_changelist']],
    ]
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'event']]],
    ]
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
