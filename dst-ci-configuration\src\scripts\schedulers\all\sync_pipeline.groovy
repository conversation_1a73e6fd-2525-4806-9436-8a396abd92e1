package scripts.schedulers.all

import com.ea.lib.LibJenkins
import hudson.model.Node
import jenkins.model.Jenkins

def project = ProjectClass(env.project_name)

/**
 * sync_pipeline.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Trigger sync jobs for machines with the correct labels') {
            steps {
                script {
                    def jenkins_nodes = Jenkins.get().nodes
                    def job_name = 'sync.' + env.branch_name + '.job'
                    def nodes_to_run = []
                    def jobs = [:]

                    currentBuild.displayName = env.JOB_NAME + '.' + env.BUILD_NUMBER

                    // Set a limit on how far back in time we should check for recent jobs.
                    def max_hours_since_sync = 3
                    // Convert days to milliseconds.
                    def max_time_since_sync = max_hours_since_sync * 60 * 60 * 1000

                    for (Node node in jenkins_nodes) {
                        // Make sure the node is online.
                        if (!node.computer.offline) {
                            // Make sure that the node isn't running any other job.
                            if (node.computer.countBusy() == 0) {
                                // Only run this for nodes with the correct label.
                                if (node.labelString == env.job_label) {
                                    def node_name = node.nodeName
                                    // If the node hasn't run sync for a certain time, add it to the list of nodes to run on.
                                    if (!HasRecentSuccessfulJob(node_name, job_name, max_time_since_sync)) {
                                        nodes_to_run.add(node_name)
                                    }
                                }
                            }
                        }
                    }

                    def final_result = Result.SUCCESS

                    for (node_to_run in nodes_to_run) {
                        // Define parameters for the job
                        def args = [
                            string(name: 'node_name', value: node_to_run),
                        ]

                        // Create a job and add it to the job map
                        jobs["node_${node_to_run}"] = {
                            def downstream_job = build(job: job_name, parameters: args, propagate: false)
                            final_result = final_result.combine(Result.fromString(downstream_job.result))
                            LibJenkins.printRunningJobs(this)
                        }
                    }

                    // Trigger all jobs
                    parallel(jobs)
                    currentBuild.result = final_result.toString()

                    SlackMessageNew(currentBuild, '#cobra-silverback', project.short_name)

                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
}
