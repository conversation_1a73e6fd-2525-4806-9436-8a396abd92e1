package schedulers

import com.ea.lib.model.branchsettings.PipelineDeterminismTestConfiguration
import support.DeclarativePipelineSpockTest

class PipelineDeterminismTestSchedulerSpec extends DeclarativePipelineSpockTest {

    void setup() {
        binding.setVariable('env', [
            CODE_BRANCH            : 'code-branch',
            CODE_FOLDER            : 'code-folder',
            NON_VIRTUAL_CODE_BRANCH: 'kin-dev',
            NON_VIRTUAL_CODE_FOLDER: 'dev',
            P4_CHANGELIST          : '234',
            JOB_NAME               : 'my-job',
            BRANCH_NAME            : 'a-branch',
        ])
        helper.registerAllowedMethod('get_branchfile', [String, String]) { projectName, branchName ->
            [
                general_settings      : [
                    pipeline_determinism_test_configuration: new PipelineDeterminismTestConfiguration(
                        cronTrigger: '@daily',
                        referenceJob: '.data.start',
                    )
                ],
                standard_jobs_settings: [:],
                pipeline_determinism_test_matrix: [
                    [platform: 'win64'],
                    [platform: 'xbsx', job_name: 'test_job_name', additional_script_args: 'test_args'],
                ],
            ]
        }
        helper.with {
            registerAllowedMethod('setPollScmTriggers', []) {}
            registerAllowedMethod('EnvInject', [Map, Map]) { currentBuild, injectMap ->
                binding.setVariable('env', binding.getVariable('env') + injectMap)
            }
            registerAllowedMethod('getLastStableCodeChangelist', [String]) { codeChangelist -> ['123'] }
        }
    }

    void 'test PipelineDeterminismTest runs'() {
        when:
        runScript('PipelineDeterminismTestScheduler.groovy')
        printCallStack()
        then:
        testNonRegression()
    }
}
