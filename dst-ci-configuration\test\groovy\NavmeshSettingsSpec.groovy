import com.ea.lib.jobsettings.NavmeshSettings
import spock.lang.Specification

class NavmeshSettingsSpec extends Specification {

    class BranchFile {
        static Map standard_jobs_settings = [
            branch_name           : 'branch',
            elipy_call            : 'elipy_setup_call && elipy --location test',
            elipy_install_call    : 'elipy_install_call',
            workspace_root        : 'workspace-root',
            disable_build         : true,
            navmesh_label         : 'build-release && util',
            data_reference_job    : 'game.code.start',
            timeout_hours_navmesh : 8,
            trigger_string_navmesh: 'H 0,2,4,6,8,10,12,14,16 * * 1-5',
        ]
        static Map general_settings = [
            dataset: 'a-dataset',
        ]
    }

    class MasterFile {
        static Map branches = [branch: [code_folder            : 'dev',
                                        code_branch            : 'code_branch',
                                        data_branch            : 'data-branch',
                                        data_folder            : 'test-game',
                                        non_virtual_data_branch: 'non-virtual-branch',
                                        non_virtual_data_folder: 'non-virtual-folder',
        ]]
    }

    class ProjectFile {
        static Boolean frostbite_syncer_setup = true
        static String name = 'Kingston'
        static String p4_data_client_env = 'p4_data_client_env'
        static String p4_data_server = 'p4_data_server'
    }

    void "test that we get expected job settings in initializeNavmeshStart"() {
        when:
        NavmeshSettings settings = new NavmeshSettings()
        settings.initializeNavmeshStart(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            branchName == 'branch'
            dataBranch == 'data-branch'
            dataFolder == 'test-game'
            dataReferenceJob == 'game.code.start'
            description == 'Starts Navmesh build jobs.'
            frostbiteSyncerSetup == true
            isDisabled == true
            nonVirtualDataBranch == 'non-virtual-branch'
            nonVirtualDataFolder == 'non-virtual-folder'
            triggerString == 'H 0,2,4,6,8,10,12,14,16 * * 1-5'
            triggerType == 'scm'
        }
    }

    void "test that we get expected job settings in initializeNavmeshJob"() {
        when:
        NavmeshSettings settings = new NavmeshSettings()
        settings.initializeNavmeshJob(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            description == 'Builds navmesh for a-dataset.'
            elipyCmd == 'elipy_setup_call && elipy --location test navmesh ' +
                ' --code-branch code_branch --code-changelist %code_changelist%' +
                ' --data-changelist %data_changelist% --asset ' + 'no-asset' +
                ' --data-dir a-dataset --p4-port p4_data_server' +
                ' --p4-client p4_data_client_env' + ' --data-clean %clean_data% '
            elipyInstallCall == 'elipy_install_call'
            jobLabel == 'build-release && util'
            timeoutMinutes == 8 * 60
            workspaceRoot == 'workspace-root'
        }
    }
}
