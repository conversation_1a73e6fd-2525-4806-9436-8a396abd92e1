package all

import com.ea.lib.LibJobDsl
import com.ea.lib.LibScm
import com.ea.lib.jobs.LibDvcs
import com.ea.project.GetBranchFile
import com.ea.project.GetMasterFile

GetMasterFile.get_masterfile(BUILD_URL).each { masterSettings ->
    masterSettings.dvcs_configs.each { dvcs_key, dvcs_config ->
        def project = masterSettings.project
        def branchfile = GetBranchFile.get_branchfile(project.name, dvcs_config.branch_name)
        def general_settings = branchfile.general_settings
        def standard_jobs_settings = branchfile.standard_jobs_settings
        branch_info = general_settings + standard_jobs_settings + [branch_name: dvcs_config.branch_name, project: project]
        if (com.ea.project.all.All.isAssignableFrom(project)) {
            project = dvcs_config.project
        }

        def dvcs_start = pipelineJob("${dvcs_key}.dvcs.start") {
            definition {
                cps {
                    script(readFileFromWorkspace('src/scripts/schedulers/all/perforce_dvcs_scheduler.groovy'))
                    sandbox(true)
                }
            }
        }
        LibDvcs.dvcs_start(dvcs_start, project, dvcs_config, branch_info)

        ['push', 'fetch'].each {
            def dvcs_job = job("dvcs.${it}.${dvcs_config.remote_spec}") {}
            LibDvcs.dvcs_worker(dvcs_job, project, dvcs_config, it)
            LibScm.sync_code_and_data(dvcs_job, project, dvcs_config)
            LibJobDsl.initialP4revert(dvcs_job, project, dvcs_config, true, false)
            LibJobDsl.addVaultSecrets(dvcs_job, branch_info)
        }
    }
}
