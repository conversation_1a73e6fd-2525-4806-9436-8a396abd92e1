package scripts.schedulers

import com.ea.lib.LibJenkins
import hudson.model.Node
import jenkins.model.Jenkins

def project = ProjectClass(env.project_name)

/**
 * sdelete.statebuild.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Trigger sdelete jobs for statebuild machines') {
            steps {
                script {
                    def jenkins_nodes = Jenkins.get().nodes
                    Collections.shuffle(jenkins_nodes)
                    def job_name = 'sdelete.statebuild'
                    def max_concurrent_jobs = 5

                    def nodes_with_time = [:]
                    def nodes_to_run = []
                    def jobs = [:]

                    currentBuild.displayName = env.JOB_NAME + '.' + env.BUILD_NUMBER

                    // Set a limit on how far back in time we should check for recent jobs (nodes with no jobs within this time will be given priority).
                    def max_days_since_sdelete = 4
                    // Convert days to milliseconds.
                    def max_time_since_sdelete = max_days_since_sdelete * 24 * 60 * 60 * 1000

                    // Set a limit on how often sdelete is allowed to run for a certain node.
                    def min_days_since_sdelete = 2
                    // Convert days to milliseconds.
                    def min_time_since_sdelete = min_days_since_sdelete * 24 * 60 * 60 * 1000

                    for (Node node in jenkins_nodes) {
                        // Only continue to check if we haven't already filled the list of nodes to run on.
                        if (nodes_to_run.size() == max_concurrent_jobs) {
                            break
                        }
                        // Make sure the node is online.
                        if (!node.computer.offline) {
                            // Make sure that the node busy executor number is 0.
                            if (node.computer.countBusy() == 0) {
                                // Only run this for nodes with the statebuild label.
                                if (node.labelString == 'statebuild') {
                                    def node_name = node.nodeName

                                    if (HasRecentJob(node_name, job_name, max_time_since_sdelete) && !HasRecentJob(node_name, job_name, min_time_since_sdelete)) {
                                        nodes_with_time[node_name] = TimeSinceLastBuildOnMachine(node_name, job_name)
                                    } else {
                                        nodes_to_run.add(node_name)
                                    }
                                }
                            }
                        }
                    }

                    def sorted_nodes = nodes_with_time.sort { -it.value }
                    // Add nodes with the longest time since the last sdelete job, until the list contains the max number of jobs.
                    for (sorted_node in sorted_nodes.keySet()) {
                        if (nodes_to_run.size() < max_concurrent_jobs) {
                            nodes_to_run.add(sorted_node)
                        } else {
                            break
                        }
                    }

                    def final_result = Result.SUCCESS

                    for (node_to_run in nodes_to_run) {
                        // Define parameters for the job
                        def args = [
                            string(name: 'Node', value: node_to_run),
                        ]

                        // Create a job and add it to the job map
                        jobs["node_${node_to_run}"] = {
                            def downstream_job = build(job: job_name, parameters: args, propagate: false)
                            final_result = final_result.combine(Result.fromString(downstream_job.result))
                            LibJenkins.printRunningJobs(this)
                        }
                    }

                    // Trigger all jobs
                    parallel(jobs)
                    currentBuild.result = final_result.toString()

                    SlackMessageNew(currentBuild, '#cobra-support-alerts', project.short_name)

                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
}
