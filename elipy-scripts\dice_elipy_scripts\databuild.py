"""
databuild.py

The databuild copies in pipelines binaries and cooks data.
When data is cooked we have the options of exporting superbundles, which
can be used in the frosty builds and exporting the Avalanche state, which
can be used to bootstrap an Avalanche instance with data and for producing patches.

There's also the option of having the build be stateless by importing a previously
exported Avalanche state to bootstrap the machine with the build data. A detailed
diagram on how the stateless flow for data work can be found here:
https://drive.google.com/file/d/1hdY4wLEQSoUrUS1MwZGIn_ceAqh99gHT/view?usp=sharing

- General setup:
    - Clean up the machine by killing running processes.
    - Initialize objects using packages from Elipy core, to be used later.
        - One instance of build_metadata.BuildMetadataManager().
        - One instance of data.DataUtils().
        - One instance of filer.FilerUtils().
    - Set data directory.
    - Copy code binaries for pipeline (produced when building the code platform tool)
      from a network share.
    - Set licensee
        - Optional step that we run when a licensee is sent as an argument. We need to set this
          when we don't build the default licensee on a stream. For example when we build dev-na
          where the default licensee is ExampleGame.
    - Handle the Avalanche state before the build:
        - Nuke Avalanche state if we are going to run a job to export the state.
        - If a clean flag is set we clean Avalanche and do not import any state.
        - If building stateless we import the state by remote cloning it from where it's stored.
          We find this out by checking the primary metadata services. And then we...
    - If we are going to build Enlighten:
        - Set parameters relevant to Enlighten.
        - Add these parameters to the pipeline arguments that will be used when cooking the data.
    - Cook the data.
        - When cooking we call fbenv which in turn uses the pipeline,
          built by the codebuild jobs with platform tools. Copied in
          in earlier step. You specify which asset/level to cook and can also add import/export
          of state flags and other pipeline args.
        - Set Avalanche status, so we know which machine has built which
          platform using which changelist. This is used to check if we need
          to import state when building statelessly. Importing a state takes time and
          should only be done when the machine does not have any
          recent data for the specific platform.
    - Export bundles option
        - Export bundles
        - Deploy bundles to a network share.
        - Register the bundles in a metadata storage (bilbo).
    - Clone Avalanche db to an external database (currently only for Criterion).
    - Export Avalanche state, if we run an export-data job.
        - If we run this we add an export-state flag when cooking and then we
          remoteclone the state to an Avalanche instance used only for storing states.
    - If we have built Enlighten:
        - Submit the built files to Perforce.
        - Create a properties file that will be used by the Jenkins job.


Examples:
    * elipy --location criterion databuild Data server DevelopmentLevels
        --code-branch build-main-dre --code-changelist 436418 --data-branch build-main-dre
        --data-changelist 436418
    * elipy --location criterion databuild Data server DevelopmentLevels
        --code-branch build-main-dre --code-changelist 436418 --data-branch build-main-dre
        --data-changelist 436418 --data-clean False --no-trim  --enable-compression
        --clean-master-version-check true
    * elipy --location dice databuild kindata ps4 ShippingLevels --code-branch kin-dev
        --code-changelist 17953583 --data-branch kin-dev --data-changelist 5028738
        --data-clean False --no-trim --pipeline-args -ContentDatabase.EnableHailstormLocalCache
        --pipeline-args true  --licensee BattlefieldGame --use-recompression-cache
        --properties-file job.properties --clean-master-version-check --expression-debug-data true
"""
# pylint: disable=duplicate-code
import click
import copy
import os
from dice_elipy_scripts.utils.data_build_utils import (
    get_export_compression_args,
    run_expression_debug_data,
)
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.env_utils import extract_fb_env_values
from dice_elipy_scripts.utils.licensee_helper import set_licensee
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from dice_elipy_scripts.utils.state_utils import export_head_bundles, import_avalanche_data_state
from elipy2 import (
    avalanche,
    build_metadata_utils,
    core,
    data,
    filer,
    filer_paths,
    frostbite_core,
    local_paths,
    LOGGER,
    p4,
    running_processes,
    SETTINGS,
    windows_tools,
)
from elipy2.cli import pass_context
from elipy2.exceptions import ConfigValueNotFoundException, ELIPYException
from elipy2.frostbite import build_agent_utils, fbenv_layer, icepick
from elipy2.telemetry import collect_metrics


@click.command("databuild", short_help="Cook the game data.")
@click.argument("data_dir")
@click.argument("platform")
@click.argument("assets", nargs=-1, default=None, required=False)
@click.option("--code-branch", required=True, help="Perforce branch/stream name for binaries.")
@click.option("--code-changelist", required=True, help="Perforce changelist number for binaries.")
@click.option("--data-branch", required=True, help="Branch to fetch Avalanche state from.")
@click.option("--data-changelist", required=True, help="Changelist of data being cooked.")
@click.option("--pipeline-args", multiple=True, help="Pipeline arguments for data build.")
@click.option("--export-avalanche-state", default=False, help="Deploy Avalanche state to filer.")
@click.option("--import-avalanche-state", default=False, help="Imports Avalanche state from filer.")
@click.option(
    "--data-clean",
    default="false",
    help="Clean Avalanche if --data-clean true is passed.",
)
@click.option("--db-name-prefix", help="prefix for db name when exporting data to avalanche")
@click.option(
    "--use-recompression-cache",
    default=False,
    help="Alternative Avalanche server to use for the recompression cache",
)
@click.option("--dry-run", default=False, help="Build patch without deploying.")
@click.option(
    "--export-combine-bundles",
    default=False,
    help="Export bundles to filer, which will be combined with bundles from another stream.",
)
@click.option("--export-super-bundles", default=False, help="Export superbundles to filer.")
@click.option("--clone-host", default=None, help="Will clone db to host if host is added.")
@click.option("--trim", default=True)
@click.option(
    "--enable-compression",
    default=False,
    help="Add compression flag when exporting bundles.",
)
@click.option("--licensee", multiple=True, default=None, help="Licensee to use.")
@click.option(
    "--clean-master-version-check",
    default=False,
    help="Run clean on master version update.",
)
@click.option(
    "--expression-debug-data",
    default=False,
    help="Export expression debug data after data cook.",
)
@click.option(
    "--enlighten-mode",
    default=None,
    type=click.Choice([None, "shelve", "submit"]),
    help="Build Enlighten data if specified: valid values are 'shelve' or 'submit'",
)
@click.option(
    "--enlighten-asset-filter",
    default=None,
    help="GUID filter asset for Enlighten build, optional when using --enlighten-mode",
)
@click.option(
    "--enlighten-type",
    default=None,
    type=click.Choice([None, "asset", "ZoneStreamer"]),
    help="Type of the asset for Enlighten build: valid values are 'asset' or 'ZoneStreamer'",
)
@click.option("--disable-caches", default=False, help="Run clean build with no cache")
@click.option("--p4-port", help="Perforce server port, required when using --enlighten-mode.")
@click.option("--p4-client", help="Perforce client, required when using --enlighten-mode.")
@click.option(
    "--custom-tag",
    default=None,
    help="Extra folder before changelist to fetch code from.",
)
@click.option(
    "--properties-file",
    help="Properties file, used by the Jenkins plugin https://plugins.jenkins.io/envinject/",
)
@click.option("--icepick-suites", multiple=True, help="Switch using Ice Pick to cook.")
@click.option(
    "--fb-env-values",
    default=None,
    multiple=True,
    help="Frostbite environment values. Example: var1=value1",
)
@click.option(
    "--filer-user",
    default=None,
    help="username for creating a filer connection",
)
@click.option(
    "--filer-password",
    default=None,
    help="password for creating a filer connection",
)
@click.option(
    "--enable-hailstorm",
    type=bool,
    default=True,
    help="Should Icepick cook use the hailstorm server or not",
)
@click.option(
    "--fetch-pipeline",
    type=bool,
    default=True,
    help="whether or not to fetch pipeline from filer.",
)
@click.option(
    "--database-id",
    default=None,
    help="Name of the Avalanche database. If provided, will be added to the pipeline_args",
)
@click.option(
    "--deploy-super-bundles-local", default=False, help="Deploy superbundles to local only."
)
@click.option(
    "--super-bundles-deploy-path",
    default=None,
    help="Custom path to deploy the super bundles to within Tnt/Local",
)
@click.option(
    "--virtual-branch-override",
    type=bool,
    default=False,
    help="Override the Perforce depot branch with the virtual branch used in the job",
)
@click.option(
    "--content-layers",
    multiple=True,
    default=None,
    help="Specific content layer to run an extra cook",
)
@click.option(
    "--include-default-layer",
    default=True,
    help="Include the default layer (Source) when cooking the specified content layer(s)",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
# pylint: disable=too-many-locals
def cli(
    _,
    data_dir,
    platform,
    assets,
    code_branch,
    code_changelist,
    data_branch,
    data_changelist,
    pipeline_args,
    export_avalanche_state,
    import_avalanche_state,
    data_clean,
    db_name_prefix,
    dry_run,
    export_combine_bundles,
    export_super_bundles,
    clone_host,
    trim,
    enable_compression,
    licensee,
    clean_master_version_check,
    expression_debug_data,
    enlighten_mode,
    enlighten_asset_filter,
    enlighten_type,
    disable_caches,
    use_recompression_cache,
    p4_port,
    p4_client,
    properties_file,
    icepick_suites,
    custom_tag,
    fb_env_values,
    filer_user,
    filer_password,
    enable_hailstorm,
    fetch_pipeline,
    deploy_super_bundles_local,
    database_id,
    super_bundles_deploy_path,
    virtual_branch_override,
    content_layers,
    include_default_layer,
):
    """
    Cook the game data.

    Examples:
        * elipy --location criterion databuild Data server DevelopmentLevels
            --code-branch build-main-dre --code-changelist 436418 --data-branch build-main-dre
            --data-changelist 436418
        * elipy --location criterion databuild Data server DevelopmentLevels
            --code-branch build-main-dre --code-changelist 436418 --data-branch build-main-dre
            --data-changelist 436418 --data-clean False --no-trim  --enable-compression
            --clean-master-version-check true
        * elipy --location dice databuild kindata ps4 ShippingLevels --code-branch kin-dev
            --code-changelist 17953583 --data-branch kin-dev --data-changelist 5028738
            --data-clean False --no-trim --pipeline-args -ContentDatabase.EnableHailstormLocalCache
            --pipeline-args true  --licensee BattlefieldGame --use-recompression-cache
            --properties-file job.properties --clean-master-version-check true
            --expression-debug-data true
    """
    # Adding sentry tags
    add_sentry_tags(__file__)

    # Clean up before running the job.
    running_processes.kill()
    if SETTINGS.get("enable_threshold_clean", default=False):
        build_agent_utils.generic_threshold_clean(workspacepath=frostbite_core.get_game_root())
    # Restart Avalanche service if necessary
    avalanche.restart_avalanche()

    # Initialize
    pipeline_args = list(pipeline_args)
    indexing_args = []

    # Add database id to pipeline args if provided
    if database_id:
        pipeline_args.append(f"-databaseId {database_id}")

    # Validate frosted build (https://jaas.ea.com/browse/COBRA-334)
    # run "fb cook win64 <branch-asset> -- -forceDebugTarget <parameters>"
    if platform == "validate-frosted":
        platform = "win64"
        dry_run = True
        export_super_bundles = False
        export_avalanche_state = False
        pipeline_args.append("-forceDebugTarget")

    metadata_manager = build_metadata_utils.setup_metadata_manager()
    builder = data.DataUtils(platform, list(assets), monkey_build_label=data_changelist)
    _filer = filer.FilerUtils()

    # Authenticate filer if user/password specified
    if filer_user and filer_password:
        _filer.delete_network_connection()
        _filer.auth_network_connection(
            network_path=filer_paths.get_build_share_path(),
            username=filer_user,
            password=filer_password,
        )

    set_licensee(list(licensee), list())

    # Set data directory.
    builder.set_datadir(data_dir)

    if fb_env_values:
        fb_env_values_dict = extract_fb_env_values(fb_env_values)
        LOGGER.info("Setting Frostbite environment values: {}".format(fb_env_values_dict))
        fbenv_layer.set_environment_values(fb_env_values_dict)

    if enlighten_mode is not None:
        expression_debug_data = False
        clone_host = None
        export_super_bundles = False
        export_avalanche_state = False

    if fetch_pipeline:
        # # fetch pipeline
        _filer.fetch_code(
            code_branch,
            code_changelist,
            "pipeline",
            "release",
            custom_tag=custom_tag,
        )

    db_name = avalanche.get_temp_db_name(
        platform, data_changelist, code_changelist, data_branch, db_name_prefix
    )
    db_name_export = db_name + "_" + "export"
    try:
        SETTINGS.get("avalanche_state_host")[platform]
    except ConfigValueNotFoundException:
        db_name_export = db_name

    if export_avalanche_state:
        pipeline_args += ["-exportState", db_name_export]

    if virtual_branch_override:
        pipeline_args += ["-BuildSettings.ForceBranch", data_branch]
        pipeline_args += ["-stateId", data_branch]
        indexing_args += ["-stateId", data_branch]

    if data_clean.lower() == "true":
        builder.clean(extra_pipeline_args=indexing_args)
    elif import_avalanche_state:
        extra_args = import_avalanche_data_state(
            data_branch, code_branch, platform, _filer, data_changelist
        )
        pipeline_args = pipeline_args + extra_args

    # Build Enlighten
    # run "fb cook <platform> <assets> -- -f generateSceneDesc -functionParameters <parameters>"
    if enlighten_mode is not None:
        # Delete any previous pending CLs from this clientspec to avoid problems, since files
        # are automatically checked out by the Enlighten function into a "FrostEd" changelist
        if not all([p4_port, p4_client]):
            LOGGER.error("No Perforce information passed in for Enlighten bake.")
            raise ELIPYException
        perforce = p4.P4Utils(port=p4_port, client=p4_client)
        perforce.wipe_client()

        enlighten_data_args = []
        if enlighten_type == "asset":
            enlighten_parameters = [
                "false",  # approximateSystemInfluences
                "GiBakeMode_SNDBS",  # distributedBuild
                "false",  # cacheEnable
                "false",  # verboseLogging,
                "false",  # saveGeometricsDebugData
                "false",  # preVisualizationOnly
                "false",  # globalSolutionEnable
                "false",  # disableTerrain
                "false",  # debugDataOnly
            ]

            enlighten_data_args = [
                "-f",
                "generateSceneDesc",
                "-functionParameters",
                "(" + ",".join(enlighten_parameters) + ")",
            ]

            if enlighten_asset_filter:
                enlighten_data_args += ["-gi.EnlightenAssetFilter", enlighten_asset_filter]

        elif enlighten_type == "ZoneStreamer":
            enlighten_data_args = [
                "-f",
                "bakeGlobalRadiosity",
                "-zsbake.GlobalSolution",
                "true",
                "-zsbake.BakeMode",
                "GiBakeMode_SNDBS",
            ]

        pipeline_args = pipeline_args + enlighten_data_args

    # Set disable_cache only if the build is clean build
    # and branch has the settings: --disable-caches
    if data_clean.lower() != "true":
        disable_caches = False

    try:
        # Build data
        cook_args = {
            "pipeline_args": pipeline_args,
            "indexing_args": indexing_args,
            "collect_mdmps": True,
            "trim": trim,
            "clean_master_version_check": clean_master_version_check,
            "disable_caches": disable_caches,
        }

        if include_default_layer:
            if icepick_suites:
                icepick.IcepickUtils.run_icepick_cook(
                    platform=platform,
                    test_suites=icepick_suites,
                    pipeline_args=pipeline_args,
                    enable_hailstorm=enable_hailstorm,
                )
            else:
                builder.cook(**cook_args)

        if enlighten_mode is None:
            for layer in content_layers:
                LOGGER.info("Cooking content layer: '%s'", layer)
                layer_cook_args = copy.deepcopy(cook_args)
                layer_cook_args["pipeline_args"] += ["-activeContentLayer", layer]
                builder.cook(**layer_cook_args)

        avalanche.set_avalanche_build_status(
            code_changelist=code_changelist,
            data_changelist=data_changelist,
            data_branch=data_branch,
            platform=platform,
        )

        if export_super_bundles or deploy_super_bundles_local:
            if super_bundles_deploy_path:
                bundles_location = os.path.join(
                    frostbite_core.get_tnt_root(), "local", super_bundles_deploy_path
                )
            else:
                bundles_location = os.path.join(
                    frostbite_core.get_tnt_root(), "local", "bundles", "head"
                )

            deploy_extra_args = []
            if enable_compression is True:
                deploy_extra_args = get_export_compression_args(platform)
            if use_recompression_cache is True:
                recompression_cache = SETTINGS.get("recompression_cache")[platform]
                LOGGER.info(
                    "Using Alternative Avalanche server for the "
                    "recompression cache {}".format(recompression_cache)
                )
                deploy_extra_args += ["--recompressionCache", recompression_cache]

            export_head_bundles(
                platform,
                bundles_location,
                deploy_extra_args=deploy_extra_args,
                db_name=database_id,
            )

            if not dry_run and export_super_bundles:
                data_changelist_dir = (
                    data_changelist
                    if not content_layers
                    else f"{data_changelist}_{content_layers[0]}"
                )
                code_changelist_dir = (
                    code_changelist
                    if not content_layers
                    else f"{code_changelist}_{content_layers[0]}"
                )

                # Deploy HEAD bundles for safekeeping
                _filer.deploy_head_bundles(
                    bundles_location,
                    data_branch=data_branch,
                    data_changelist=data_changelist_dir,
                    code_branch=code_branch,
                    code_changelist=code_changelist_dir,
                    platform=platform,
                )

                metadata_manager.register_bundles(
                    data_branch=data_branch,
                    data_changelist=data_changelist,
                    code_branch=code_branch,
                    code_changelist=code_changelist,
                    platform=platform,
                )

        if export_combine_bundles:
            combine_bundles_location = os.path.join(
                frostbite_core.get_tnt_root(), "local", "combine_bundles", "head"
            )
            combine_deploy_args = ["-s", "pre-combine.yaml"]
            export_head_bundles(
                platform,
                combine_bundles_location,
                deploy_extra_args=combine_deploy_args,
                include_platform=False,
                ordering_algorithm=None,
            )
            if not dry_run:
                _filer.deploy_head_bundles(
                    combine_bundles_location,
                    data_branch=data_branch,
                    data_changelist=data_changelist,
                    code_branch=code_branch,
                    code_changelist=code_changelist,
                    platform=platform,
                    bundles_dir_name="combine_bundles",
                )

                metadata_manager.register_bundles(
                    data_branch=data_branch,
                    data_changelist=data_changelist,
                    code_branch=code_branch,
                    code_changelist=code_changelist,
                    platform=platform,
                    bundles_type="combine_bundles",
                )

        if clone_host is not None:
            source_db = avalanche.get_full_database_name(platform)
            dest_db = source_db + ".{}.{}".format(code_changelist, data_changelist)
            avalanche.remote_clone_db(source_db, dest_db, clone_host)
            metadata_manager.register_clone_db(
                data_branch=data_branch,
                data_changelist=data_changelist,
                code_branch=code_branch,
                code_changelist=code_changelist,
                platform=platform,
                clone_host=clone_host,
                destination_db=dest_db,
            )

        if export_avalanche_state:
            if frostbite_core.minimum_fb_version(year=2021, version_nr=1):
                remote_clone_state(
                    platform=platform,
                    db_name=db_name,
                    db_name_export=db_name_export,
                    data_changelist=data_changelist,
                    code_changelist=code_changelist,
                    data_branch=data_branch,
                    _filer=_filer,
                    _bilbo=metadata_manager,
                )
            else:
                deploy_state(
                    platform=platform,
                    db_name=db_name,
                    data_changelist=data_changelist,
                    code_changelist=code_changelist,
                    data_branch=data_branch,
                    _filer=_filer,
                )

        if expression_debug_data:
            run_expression_debug_data(
                code_changelist,
                data_changelist,
                code_branch,
                data_branch,
                platform,
                builder_instance=builder,
                pipeline_args=pipeline_args,
                clean_master_version_check=clean_master_version_check,
            )

        if enlighten_mode is not None:
            # Due to the massive size of FB files Enlighten creates, we need to
            # run the pipeline function for uploading the data to hailstorm first
            enlighten_changelist = perforce.latest_pending_changelist()
            # fmt: off
            pipeline_args = [
                "-hailstorm",
                "res-hailstorm.la.ad.ea.com",
                "-f",
                "uploadSandboxes",
                "-functionParameters",
                "{{['Changelist']='{0}', ['ChangeDescriptionInP4']=true}}" \
                       .format(enlighten_changelist),
            ]
            # fmt: on
            fbenv_layer.cook(pipeline_args=pipeline_args)

            # Submit or shelve the modified files that the Enlighten bake automatically
            # checked out in a newly-created "FrostEd" changelist
            changelist_description = "Enlighten bake of {} with:\ncode {}\ndata {}".format(
                list(assets), code_changelist, data_changelist
            )
            changelist_description += "\nJenkins URL: " + os.environ.get("BUILD_URL", "None")

            if enlighten_mode == "submit":
                perforce.reopen()
                result = perforce.submit(changelist_description)
                for action in result:
                    if b"submittedChange" in action:
                        enlighten_changelist = action[b"submittedChange"].decode()
            elif enlighten_mode == "shelve":
                perforce.set_description(enlighten_changelist, changelist_description)
                perforce.shelve(enlighten_changelist, discard=False)

            if properties_file:
                with open(os.path.join(os.environ.get("TEMP"), properties_file), "a+") as prop_file:
                    prop_file.write(
                        "{}_changelist = {}\n".format(enlighten_mode, enlighten_changelist)
                    )

    finally:
        if export_avalanche_state:
            # Drop temp DB
            avalanche.drop(db_name_export)


def deploy_state(platform, db_name, data_changelist, code_changelist, data_branch, _filer):
    """
    Extract the exported state from Avalanche and deploy it to filer.
    """
    # Export Avalanche state to local folder via avalanchecli
    local_path = local_paths.get_local_avalanche_export_path(
        data_branch, data_changelist, code_changelist, platform
    )
    avalanche.export(db_name, local_path)
    LOGGER.info("Avalanche export successful.")

    # Copy local Avalanche state to filer
    _filer.deploy_avalanche_state(platform, data_branch, data_changelist, code_changelist)
    # Delete locally exported state
    avalanche.clean_temp_state_folder(local_path)


def remote_clone_state(
    platform,
    db_name,
    db_name_export,
    data_changelist,
    code_changelist,
    data_branch,
    _filer,
    _bilbo,
):
    """
    Remote clone the exported state and register it in Bilbo.
    """
    try:
        dest_host = SETTINGS.get("avalanche_state_host")[platform]
        avalanche.remote_clone_db(
            source_db=db_name_export,
            dest_db=db_name,
            dest_host=dest_host,
            source_host=windows_tools.get_computer_name(),
            branch=data_branch,
            limited_lifetime=True,
            push_built_levels=False,
            complete_clone=True,
        )
        if core.use_bilbo():
            _bilbo.register_avalanche_state(
                path=db_name,
                data_changelist=data_changelist,
                code_changelist=code_changelist,
                branch=data_branch,
                platform=platform,
                remote_host=[dest_host],
            )
    except ConfigValueNotFoundException:
        LOGGER.info("Not setup to run remote cloning of state in elipy config.")
        deploy_state(
            platform=platform,
            db_name=db_name,
            data_changelist=data_changelist,
            code_changelist=code_changelist,
            data_branch=data_branch,
            _filer=_filer,
        )
