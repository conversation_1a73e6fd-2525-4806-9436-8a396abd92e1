{#
    Command:
        outsource_package
            short_help: Create packages for outsourcers.

    Arguments:

    Required variables:
        code_changelist
            required: True
            help: Perforce code changelist number.
        p4_client_code
            required: True
            help: Perforce workspace name to sync code.
        p4_client_packages
            required: True
            help: Perforce workspace to submit generated packages.
        p4_port
            required: True
            help: Perforce server address.
        p4_user
            required: True
            help: Perforce user name.
        script_path
            required: True
            help: Path to the script (relative to TnT).

    Optional variables:
        clean
            default: false
            help: Delete TnT/Local if --clean true is passed.
        config
            default: release
            help: Config to generate solution for.
        data_directory
            default: None
            help: Which data directory to use for fetching licensee settings.
        dry_run
            is_flag: True
            help: Run without submitting to Perforce.
        email
            default: None
            help: User email to authenticate to package server.
        domain_user
            default: None
            help: The user to authenticate to package server as DOMAIN\user
        framework_args
            multiple: True
            help: Framework arguments for gensln.
        licensee
            multiple: True
            default: None
            help: Licensee to use.
        password
            default: None
            help: User credentials to authenticate to package server.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    outsource_package
    --code-changelist {{ code_changelist }}
    --p4-client-code {{ p4_client_code }}
    --p4-client-packages {{ p4_client_packages }}
    --p4-port {{ p4_port }}
    --p4-user {{ p4_user }}
    --script-path {{ script_path }}
    {%- if clean %}
    --clean {{ clean }}
    {%- endif %}
    {%- if config %}
    --config {{ config }}
    {%- endif %}
    {%- if data_directory %}
    --data-directory {{ data_directory }}
    {%- endif %}
    {%- if dry_run %}
    --dry-run {{ dry_run }}
    {%- endif %}
    {%- if email %}
    --email {{ email }}
    {%- endif %}
    {%- if domain_user %}
    --domain-user {{ domain_user }}
    {%- endif %}
    {%- if framework_args %}
    --framework-args {{ framework_args }}
    {%- endif %}
    {%- if licensee %}
    --licensee {{ licensee }}
    {%- endif %}
    {%- if password %}
    --password {{ password }}
    {%- endif %}
  displayName: elipy outsource_package
