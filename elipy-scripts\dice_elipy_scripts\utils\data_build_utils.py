"""
utility module
"""

from elipy2 import (
    avalanche,
    core,
    filer_paths,
    LOGGER,
    local_paths,
    build_metadata_utils,
    frostbite_core,
)


def get_export_compression_args(platform):
    "Get avalanche export compression arguments"
    local_data_dir = frostbite_core.get_game_data_dir()
    file_name = "CompressionConfig.yaml"
    avalanche_platform_name = avalanche.get_avalanche_platform_name(platform)

    if avalanche_platform_name.lower() in ["dedicatedserver"]:
        file_name = "ServerCompressionConfig.yaml"

    compression_args = [
        "-c",
        "{}\\Config\\Frosty\\{}".format(local_data_dir, file_name),
    ]
    return compression_args


def run_expression_debug_data(
    code_changelist,
    data_changelist,
    code_branch,
    data_branch,
    platform,
    local_export_location=None,
    expression_export_dir=None,
    builder_instance=None,
    pipeline_args=[],
    clean_master_version_check=True,
):
    """
    Add expression debug data exports directory to filer and metadata service.
    """
    metadata_manager = build_metadata_utils.setup_metadata_manager()

    if expression_export_dir is None:
        expression_export_dir = filer_paths.get_expression_debug_data_path(
            data_branch, data_changelist, code_branch, code_changelist, platform
        )
    if local_export_location is None:
        local_export_location = local_paths.get_local_expressiondebug_path()

    LOGGER.info("Exporting expression debug data.")
    if builder_instance is not None:
        builder_instance.extract_expression_debugdata(
            pipeline_args=pipeline_args,
            clean_master_version_check=clean_master_version_check,
        )

    core.robocopy(source=local_export_location, dest=expression_export_dir)
    if builder_instance is not None:
        changelist = data_changelist + "_" + code_changelist
        metadata_manager.register_expression_debug_data(
            expression_export_dir, changelist, data_branch
        )
