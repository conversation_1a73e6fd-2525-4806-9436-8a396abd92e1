# elipy-scripts

Here you'll find the Criterion and DICE scripts for running [elipy2](https://gitlab.ea.com/dre-cobra/elipy/elipy2).

[Visit our Autogenerated API Documentations Page here.](https://dre-cobra.gitlab.ea.com/elipy/elipy-scripts/)

## Local development setup

### Prerequisites

- [git](https://git-scm.com/downloads) (Duh!)
- Supported versions of [Python](https://www.python.org/downloads/)
  - 3.7
  - 3.8
- macOS only:
  - [The Python Launcher for Unix](https://github.com/brettcannon/python-launcher)

### Setup

Clone elipy2 and elipy-scritps next to each other:

```sh
<NAME_EMAIL>:dre-cobra/elipy/elipy-scripts.git
<NAME_EMAIL>:dre-cobra/elipy/elipy2.git
```

### With Visual Studio Code

#### Setup

1. [Download Visual Studio Code](https://code.visualstudio.com/)
1. Open `elipy-scripts.code-workspace` in Visual Studio Code
1. Visual Studio Code will show a pop-up asking *Do you want to install the recommended extensions for this repository?* Click *Install*. Wait for the installations to finish.
1. Open *Terminal -> Run Task...* in the menu.
1. Choose *Create and update virtual environment* prefixed with the OS you are running. While the task is running, the Python extension will inform you that it detects new virtual environments created.
1. You can now choose an enviroment to run in by
    1. Show Command Palette
    1. Search for `Python: Select Interpreter`
    1. Click *elipy-scripts*
    1. *('.venv_py37')*.
    1. Redo the process for *elipy2*, but when you get to choose environment you'll notice that the virtual environments aren't listed. Instead choose *Enter interpreter path...*. You can give the full path or browse for it.

With that done you can run and debug elipy-scripts and elipy2 tests in Visual Studio Code with breakpoints and stepping through the code at execution.

### On command line

```sh
cd elipy2
```

Sync the environment setup files from Perforce:

- DICE: `//dicestudio/next/dev/dice-next/TnT/Bin/fbcli/bin/cli_bat.py`.
  Server: `dice-p4edge-fb.dice.ad.ea.com:2001`

Set the `PYTHONPATH` environment variable in Windows to include the path to your local clone of `elipy2`.

Add the `elipy-scripts` path to the development YAML configuration file `elipy_dev.yml`:

```yaml
default:
    script_path: # You can put your local dev path here when developing scripts
      ...
      - "C:\\Git\\elipy-scripts\\dice_elipy_scripts"
```

In `setup.cfg`, set `ELIPY_CONFIG` to the path to the YAML configuration file:

```ini
ELIPY_CONFIG=C:/Git/elipy-scripts/dice_elipy_scripts/yml/elipy_dev.yml
```

Now you should have everything you need in order to run the tests. Find the path to `cli.bat` that you synced from Perforce and run it:

```batch
C:/Perforce/dice-next/TnT/Bin/fbcli/cli.bat
```

A new Windows termnial opens and your environment should be ready.
Now you can run commands as described in the `README` for `elipy2`:

```sh
cd elipy2
install_elipy.bat elipy_dev.yml dice_elipy_scripts
# Install dev packages
python setup.py develop
# Run tests in elipy2
python setup.py test
# Run tests in elipy-scripts
cd ../elipy-scripts
python setup.py test
```

---

## Stashing

You shouldn't commit your local configuration to the repository. You can store the configuration for instance in  a stash:

```sh
git stash push -m "Local test configuration"
```

And retrieve it by first listing your stashes and copying the index of the stash:

```sh
$ git stash list
stash@{0}: On my-branch: Local test configuration
stash@{1}: WIP on my-branch: 61d01dc Update README with instructions on how to run tests
$ git stash apply stash@{0}
On branch my-branch
Your branch is up to date with 'origin/my-branch'.
```

Or just apply the latest stash:

```sh
git stash apply
```

## To run test from *nix machine

This only applies if you work in fbcli terminal (frostyshell does not help)
GUI: Drone->Run->fbcli
>cd $elipysetup_gitroot
>set ELIPY_CONFIG=D:/Git/elipy-scripts/dice_elipy_scripts/yml/elipy_dev.yml
>install-elipy.bat elipy_dev.yml dice_elipy_scripts
>cd $elipyscript_gitroot
>python setup.py test

Dice scripts for running elipy2

[elipy-scripts documentation (Google Site)](https://docs.google.com/document/d/1jmkrK7EYWxUG6nbHBtCNdHpS_3wE97DCHIDXu2D04fQ/edit?usp=sharing)

## Configuring your ELIPY config

The ELIPY config file contains a significant number of configurable settings. Only a few settings are required, but what you need to configure will change from script to script and depending on the arguments you pass into those scripts.

You can find more details about each setting [here](./ELIPY_CONFIG.md)

You can also find the ELIPY2 [ELIPY config docs here](https://gitlab.ea.com/dre-cobra/elipy/elipy2/-/blob/master/ELIPY_CONFIG.md).

## Documentation-in-Code and Docstrings

This repository relies on the sphinx documentation engine to generate the documentation. Sphinx uses the reStructuredText-style Python docstrings to format its response.

Here is an example of what can be included in a function/class:

```python
def foo(arg1, arg2):
  """
  Insert description here.

  :param arg1: description
  :param arg2: description
  :type arg1: type description
  :type arg1: type description
  :return: return description
  :rtype: the return type description
  """

  result = arg1 + arg2

  return result
```

## Click Guidelines

- [Click](https://click.palletsprojects.com/en/7.x/)
- Options
  - We have opted for using the `bool` type over `is_flag` for command options. The main driver behind this decision is to help provide a more consistant experience across elipy-script whilst also reducing complexity for CI tooling and command generation tools. For example:

   ```python
   click.option(
      "--ignore-icepick-exit-code",
      default=True,
      type=bool,
      help=(
          "Should icepick result be ignored. By ignoring it, "
          "the job will succeed when a unit test fails."
      )
   ```

---

## Removing Click parameters/options/arguments

### Requirements

- install <https://elasticvue.com/> in your browser (makes working with ES instance easier)

### What to check before you remove functionality

- If the functionality has been used in the past year, do not remove it
  - The goal here is to not remove functionality that is used at the start of a game cycle (before patching)
- Check if the parameter is used in Cobra's [DST repo](https://gitlab.ea.com/dre-cobra/dst-ci-configuration/) for the command you are modifying
  - if it is being used, you shouldn't remove it from elipy
- Using [elasticvue](https://elasticvue.com/), check if the parameter has been used recently
  - ES instance: <https://dre-metrics-eck.cobra.dre.ea.com/>
    - index: elipy_metrics_*
  - Legacy ES instance: <http://elipy-telemetry.dre.dice.se/>
    - index: metrics_*
  - If not data is available in ES, you need to add the decorator ```@collect_metrics()``` to the function first.
  - Here you are looking for non-default values. This will tell you the arguments are being overriden via the commandline. Its possible users are passing in the default value via the commandline though.
