import com.ea.lib.jobsettings.UnitTestsSettings
import com.ea.lib.model.branchsettings.UnitTestsConfiguration
import spock.lang.Specification

class UnitTestsSettingsSpec extends Specification {
    static UnitTestsConfiguration configuration = new UnitTestsConfiguration(
        label: 'build-main && unittests',
        enabled: true,
        ignorePaths: ['/path/'],
        nonVirtualCodeBranch: 'branch',
        nonVirtualCodeFolder: 'folder',
        trigger: 'cron',
        timeoutHours: 2,
    )
    class BranchFile {
        static Map standard_jobs_settings = [
            workspace_root    : 'workspace-root',
            elipy_call        : 'elipy-call',
            elipy_install_call: 'elipy-install-call',
            user_credentials  : 'user',
        ]
        static Map general_settings = [
            unittests: configuration
        ]
    }

    class MasterFile {
        static Map branches = [branch: [data_branch: 'data-branch', code_branch: 'code-branch', code_folder: 'dev']]
    }

    class ProjectFile {
        static String name = 'Santiago'
        static Map p4_fb_settings = [
            p4_port: 1667
        ]
    }

    void "test that we get expected job settings in initializeStart"() {
        when:
        UnitTestsSettings settings = new UnitTestsSettings()
        settings.initializeStart(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            description == 'Sync branch code and run unit tests'
            codeBranch == 'code-branch'
            codeFolder == 'dev'
            projectName == ProjectFile.name
            cronTrigger == configuration.trigger
            nonVirtualCodeBranch == configuration.nonVirtualCodeBranch
            nonVirtualCodeFolder == configuration.nonVirtualCodeFolder
            isDisabled == !configuration.enabled
        }
    }

    void "test that we get expected job settings in initializeJob"() {
        when:
        UnitTestsSettings settings = new UnitTestsSettings()
        settings.initializeJob(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            jobLabel == configuration.label
            description == 'Runs unit tests'
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${ENV, var="CODE_CHANGELIST"}'
            timeoutMinutes == 120
            fbLoginDetails.p4_port == 1667
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} unittests --email %monkey_email% --password \"%monkey_passwd%\""
        }
    }
}
