   GametoolScheduler.run()
      GetBranchFile.get_branchfile(null, null)
      GametoolScheduler.ProjectClass(null)
      GametoolScheduler.pipeline(groovy.lang.Closure)
         GametoolScheduler.allowBrokenBuildClaiming()
         GametoolScheduler.timestamps()
         GametoolScheduler.echo(Executing on agent [label:any])
         GametoolScheduler.stage(Get changelist from Perforce, groovy.lang.Closure)
            GametoolScheduler.script(groovy.lang.Closure)
               LibCommonNonCps.get_setting_value({gametool_settings={trigger=a cron trigger, non_virtual_code_branch=kin-dev, non_virtual_code_folder=dev, gametools={icepick={timeout_hours=6, config=final, framework_args=[first-arg, second-arg]}}}}, [], p4_code_root, , {short_name=kin})
               LibCommonNonCps.get_setting_value({gametool_settings={trigger=a cron trigger, non_virtual_code_branch=kin-dev, non_virtual_code_folder=dev, gametools={icepick={timeout_hours=6, config=final, framework_args=[first-arg, second-arg]}}}}, [], p4_code_creds, , {short_name=kin})
               LibPerforce.setPollScmTriggers()
               LibPerforce.getModifiedTools(234)
               GametoolScheduler.EnvInject({absoluteUrl=http://example.com/dummy, buildVariables={}, changeSets=[], currentResult=SUCCESS, description=dummy, displayName=#1, duration=1, durationString=1 ms, fullDisplayName=dummy #1, fullProjectName=dummy, id=1, keepLog=false, nextBuild=null, number=1, previousBuild=null, projectName=dummy, result=SUCCESS, startTimeInMillis=1, timeInMillis=1, upstreamBuilds=[]}, {CODE_CHANGELIST=234, GAMETOOLS_TO_BUILD=['icepick']})
         GametoolScheduler.stage(Trigger Gametool Build job, groovy.lang.Closure)
            GametoolScheduler.script(groovy.lang.Closure)
               GametoolScheduler.retryOnFailureCause(3, [], groovy.lang.Closure)
                  GametoolScheduler.string({name=CODE_CHANGELIST, value=234})
                  GametoolScheduler.booleanParam({name=CLEAN_LOCAL, value=false})
                  GametoolScheduler.EnvInject({absoluteUrl=http://example.com/dummy, buildVariables={}, changeSets=[], currentResult=SUCCESS, description=dummy, displayName=#1, duration=1, durationString=1 ms, fullDisplayName=dummy #1, fullProjectName=dummy, id=1, keepLog=false, nextBuild=null, number=1, previousBuild=null, projectName=dummy, result=SUCCESS, startTimeInMillis=1, timeInMillis=1, upstreamBuilds=[]}, {code_changelist=234})
                  GametoolScheduler.parallel({icepick=groovy.lang.Closure})
                     GametoolScheduler.build({job=a-branch.gametool.icepick, parameters=[{name=CODE_CHANGELIST, value=234}, {name=CLEAN_LOCAL, value=false}], propagate=false})
                        GametoolScheduler.clone()
                        GametoolScheduler.toString()
                     LibJenkins.printRunningJobs(scripts.schedulers.gametool.GametoolScheduler)
                  GametoolScheduler.SlackMessageNew({absoluteUrl=http://example.com/dummy, buildVariables={}, changeSets=[], currentResult=SUCCESS, description=dummy, displayName=my-job.234, duration=1, durationString=1 ms, fullDisplayName=dummy #1, fullProjectName=dummy, id=1, keepLog=false, nextBuild=null, number=1, previousBuild=null, projectName=dummy, result=SUCCESS, startTimeInMillis=1, timeInMillis=1, upstreamBuilds=[]}, null, kin)
