package com.ea.project.bct.branchsettings

import com.ea.project.bct.Bct

class Task1 {
    static Class project = Bct
    static Map general_settings = [
        dataset                 : project.dataset,
        elipy_call              : project.elipy_call + ' --use-fbenv-core',
        elipy_install_call      : project.elipy_install_call,
        frostbite_licensee      : project.frostbite_licensee,
        workspace_root          : project.workspace_root,
        azure_elipy_call        : project.azure_elipy_call,
        azure_elipy_install_call: project.azure_elipy_install_call,
        azure_workspace_root    : project.azure_workspace_root,
        job_label_statebuild    : 'statebuild',
        webexport_script_path   : 'Code\\DICE\\BattlefieldGame\\fbcli\\webexport.py',
        autotest_remote_settings: [
            eala: [
                credentials   : 'monkey.bct',
                p4_code_creds : 'bct-la-p4',
                p4_code_server: 'dicela-p4edge-fb.la.ad.ea.com:2001'
            ],
            dice: [
                p4_code_creds : 'perforce-battlefield01',
                p4_code_server: 'dice-p4buildedge02-fb.dice.ad.ea.com:2001',
            ]
        ],
    ]
    static Map code_settings = [
        skip_code_build_if_no_changes: false,
        slack_channel_code           : [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        sndbs_enabled                : true,
    ]
    static Map data_settings = [
        poolbuild_data         : true,
        webexport_branch       : true,
        webexport_allow_failure: true,
    ]
    static Map frosty_settings = [
        poolbuild_frosty    : true,
        slack_channel_frosty: [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        timeout_hours_frosty: 5,
        use_linuxclient     : true,
    ]
    static Map standard_jobs_settings = code_settings + data_settings + frosty_settings + [
        asset                     : 'Task1ClientLevels',
        enable_lkg_p4_counters    : true,
        extra_data_args           : ['--pipeline-args -Pipeline.MaxConcurrency --pipeline-args 10 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true --pipeline-args -Pipeline.UpdateAssetIndeterminismIsError --pipeline-args false '],
        extra_frosty_args         : ['--pipeline-args -Pipeline.MaxConcurrency --pipeline-args 10 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        skip_icepick_settings_file: true,
        server_asset              : 'Task1ServerLevels',
        shift_branch              : true,
        shift_every_build         : true,
        strip_symbols             : false,
        move_location_parallel    : true,
        new_locations             : [
            Guildford      : [
                elipy_call_new_location: project.elipy_call_criterion + ' --use-fbenv-core',
            ],
            Montreal       : [
                elipy_call_new_location: project.elipy_call_montreal + ' --use-fbenv-core',
            ],
            RippleEffect: [
                elipy_call_new_location: project.elipy_call_eala + ' --use-fbenv-core',
            ],
        ],
    ]
    static Map icepick_settings = [:]
    static Map preflight_settings = [:]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'linux64server', configs: ['final']],
        [name: 'tool', configs: ['release']],
        [name: 'win64game', configs: ['final', 'release', 'performance']],
        [name: 'win64server', configs: ['final']],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = [
        [name: '.code.p4counterupdater', args: ['code_changelist', 'code_countername']],
        [name: '.data.start', args: []],
    ]
    static List data_matrix = [
        [name: 'server'],
        [name: 'win64'],
    ]
    static List data_downstream_matrix = [
        [name: '.data.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.data.p4cleancounterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.frosty.start', args: []],
    ]
    static List patchdata_matrix = []
    static List frosty_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'playtest', args: '']]],
        [name: 'server', variants: [[format: 'files', config: 'final', region: 'playtest', args: '']]],
        [name: 'win64', variants: [[format: 'files', config: 'final', region: 'playtest', args: ' --additional-configs performance']]],
    ]
    static List frosty_downstream_matrix = [
        [name: '.frosty.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.shift.upload', args: ['code_changelist', 'data_changelist']],
        [name: '.spin.linuxserver.digital.final.playtest', args: ['code_changelist', 'data_changelist']],
    ]
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'playtest']]],
    ]
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
