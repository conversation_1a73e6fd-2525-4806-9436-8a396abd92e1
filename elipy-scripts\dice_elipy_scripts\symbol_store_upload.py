"""
symbol_store_upload.py

Copy symbols from a network share location to a symbol store.
"""
import click
import os
from dice_elipy_scripts.utils.frosty_build_utils import install_required_sdks
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from dice_elipy_scripts.utils.licensee_helper import set_licensee
from elipy2 import filer_paths, symbols, local_paths, LOGGER, filer
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command("symbol_store_upload", short_help="Copy symbols to symbol store.")
@click.option("--p4-port", help="Which p4 port to be used.", required=False, default=None)
@click.option("--p4-client", help="Which p4 client to use.", required=False, default=None)
@click.option("--p4-user", help="Which p4 user is being used.", required=False, default=None)
@click.option("--branch", required=True)
@click.option("--changelist", required=True, type=int)
@click.option("--config", required=True)
@click.option("--email", default=None, help="User email to authenticate to package server")
@click.option(
    "--domain-user",
    default=None,
    help="The user to authenticate to package server as DOMAIN\\user",
)
@click.option("--password", default=None, help="User password to authenticate to package server")
@click.option("--platform", required=True)
@click.option(
    "--compress-symbols",
    default=True,
    type=bool,
    help="Compress symbols before uploading them to the symstore.",
)
@click.option("--licensee", multiple=True, default=None, help="Licensee to use")
@click.option(
    "--custom-tag",
    "--cut",
    default=None,
    help="Extra folder before changelist to fetch code from.",
)
@click.option(
    "--needs-index",
    default=False,
    type=bool,
    help="Index symbols before uploading them to the symstore.",
)
@click.option(
    "--skip-bilbo",
    default=False,
    type=bool,
    help="Skip using Bilbo to register binaries upload",
)
@click.option(
    "--custom-p4-cmd",
    default=False,
    type=bool,
    help="Using a custom P4_CMD command for indexing",
)
@click.option(
    "--custom-sym-store",
    default=None,
    help="Custom SymStore location",
)
@click.option(
    "--custom-binaries-destination",
    default=None,
    help="Custom storage account for binaries",
)
@pass_context
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    p4_port,
    p4_client,
    p4_user,
    branch,
    changelist,
    config,
    email,
    domain_user,
    password,
    platform,
    compress_symbols,
    licensee,
    custom_tag,
    needs_index,
    skip_bilbo,
    custom_p4_cmd,
    custom_sym_store,
    custom_binaries_destination,
):
    """
    Copy symbols from a network share location to a symbol store.
    """
    # Adding sentry tags
    add_sentry_tags(__file__)

    if licensee:
        set_licensee(list(licensee), list())

    platform = platform.lower()

    install_required_sdks(password, email, domain_user, platform)

    utilities = symbols.SymbolsUtils()

    if needs_index and custom_sym_store is not None:
        _filer = filer.FilerUtils()
        path = local_paths.get_local_build_path(platform, config)

        if platform.lower() == "tool":
            path = os.path.dirname(path)
        LOGGER.info("Running source index in path %s", path)
        utilities.source_index(
            platform=platform,
            config=config,
            p4_port=p4_port,
            p4_client=p4_client,
            p4_user=p4_user,
            path=path,
            custom_p4_cmd=custom_p4_cmd,
        )
        if platform not in ["linux64server", "ps4", "ps5"]:
            LOGGER.info("Verifying symbols for %s", platform)
            utilities.verify_symbol_integrity(
                path_to_binaries=path,
            )
        LOGGER.info("Deploying binaries in %s", custom_binaries_destination)
        _filer.deploy_code(
            branch=branch,
            changelist=changelist,
            platform=platform,
            config=config,
            deploy_tests=False,
            deploy_frostedtests=False,
            custom_binaries_destination=custom_binaries_destination,
            skip_bilbo=skip_bilbo,
        )
        LOGGER.info("Uploading symbols in %s", custom_sym_store)
        utilities.upload_symbols_to_sym_store(
            platform,
            path=path,
            sym_store=custom_sym_store,
            changelist=changelist,
            product_name=f"{branch}.{platform}",
            compress=compress_symbols,
        )
    else:
        utilities.upload_symbols_to_sym_store(
            platform,
            path=filer_paths.get_code_build_path(
                branch, changelist, platform, config, custom_tag=custom_tag
            ),
            changelist=changelist,
            product_name=f"{branch}.{platform}",
            compress=compress_symbols,
        )
