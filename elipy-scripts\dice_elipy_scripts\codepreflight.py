"""
codepreflight.py
"""
import os
import re
import click
from retry.api import retry_call

from elipy2 import code, core, data, filer, LOGGER, p4, running_processes, frostbite_core
from elipy2.frostbite import icepick
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.file_system import print_drive_space
from dice_elipy_scripts.utils.licensee_helper import set_licensee
from dice_elipy_scripts.utils.preflight_utils import (
    raise_if_cl_not_exists,
    raise_if_wrong_stream,
)
from dice_elipy_scripts.utils.state_utils import import_local_code_state
from dice_elipy_scripts.utils.code_utils import run_gensln
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags


@click.command("codepreflight", short_help="Preflight the code build.")
@click.argument("p4_port")
@click.argument("p4_client")
@click.argument("platform")
@click.argument("config")
@click.argument("pending_changelist")
@click.option("--user", default=None, help="Perforce user name.")
@click.option(
    "--clean",
    default="false",
    help="Delete TnT/Local if --clean true is passed, otherwise no cleanup is performed.",
)
@click.option("--nomaster", is_flag=True, help="Run nomaster build.")
@click.option("--code-branch", default=None, help="Perforce branch/stream name.")
@click.option("--import-local", is_flag=True, help="Import contents of TnT/Local from filer.")
@click.option("--icepick-test", default=None, help="Icepick tests to run.")
@click.option(
    "--ignore-icepick-exit-code",
    default=False,
    type=bool,
    help=(
        "Should icepick result be ignored. By ignoring it, "
        "the job will succeed when a unit test fails."
    ),
)
@click.option(
    "--force-sequential-icepick", is_flag=True, help="When true sets max parallel tests to 1."
)
@click.option("--data-directory", help="Which data directory to use for the working data set.")
@click.option(
    "--settings-files",
    help="Settings files relative to the data folder.",
    multiple=True,
)
@click.option("--framework-args", multiple=True, help="Framework arguments for gen sln.")
@click.option("--p4-compile", is_flag=True, help="Run p4_compile before building.")
@click.option("--licensee", multiple=True, default=None, help="Frostbite Licensee to use")
@click.option(
    "--password",
    default=None,
    help="User credentials to authenticate to package server",
)
@click.option("--email", default=None, help="User email to authenticate to package server")
@click.option(
    "--domain-user",
    default=None,
    help="The user to authenticate to package server as DOMAIN\\user",
)
@click.option("--limit_cpu", is_flag=True, help="Only enable when codepreflight tool runs on AWS.")
@click.option(
    "--do-warmup/--not-warmup",
    default=False,
    help="--do-warmup to warm up AWS agent; --not-warmup/not set, normal preflight.",
)
@click.option("--reporting-branch", default=None, help="Icepick reporting branch")
@click.option("--heartbeat-timeout", default=None, help="Icepick heartbeat timeout")
@click.option(
    "--icepick-extra-framework-args",
    default=None,
    help="Extra arguments for Icepick to pass to any Framework commands it starts",
)
@click.option(
    "--skip-revert",
    default=False,
    help="Skip any p4 revert in case those are not necessary for the build",
)
@click.option(
    "--frosting-report",
    "--fr",
    default=True,
    help="Toggle to send frosting report.",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    p4_port,
    p4_client,
    platform,
    config,
    pending_changelist,
    user,
    clean,
    nomaster,
    code_branch,
    import_local,
    icepick_test,
    ignore_icepick_exit_code,
    force_sequential_icepick,
    data_directory,
    settings_files,
    framework_args,
    p4_compile,
    licensee,
    password,
    email,
    domain_user,
    limit_cpu,
    do_warmup,
    reporting_branch,
    heartbeat_timeout,
    icepick_extra_framework_args,
    skip_revert,
    frosting_report,
):
    """
    Preflight the code build.
    """
    LOGGER.start_group("Setup build environment")

    print_drive_space()

    # adding sentry tags
    add_sentry_tags(__file__, "preflight")

    running_processes.kill()

    p4_compile_changelist = pending_changelist

    perforce = p4.P4Utils(port=p4_port, user=user, client=p4_client)

    # Performs a p4_compile by first reshelving the target changelist
    # Unshelve pending changelist with extra flag -c
    if not do_warmup:
        raise_if_cl_not_exists(perforce, pending_changelist)
        raise_if_wrong_stream(perforce, pending_changelist)
        if p4_compile:
            p4_compile_changelist = retry_call(
                perforce.reshelve,
                fargs=[pending_changelist],
                tries=3,
                delay=6,
                backoff=2,
                logger=LOGGER,
            )
            retry_call(
                perforce.unshelve,
                fargs=[p4_compile_changelist],
                fkwargs={"overide_default_cl": True},
                tries=3,
                delay=6,
                backoff=2,
                logger=LOGGER,
            )
        else:
            retry_call(
                perforce.unshelve,
                fargs=[p4_compile_changelist],
                fkwargs={"overide_default_cl": False},
                tries=3,
                delay=6,
                backoff=2,
                logger=LOGGER,
            )

    try:
        builder = code.CodeUtils(platform, config)
        _filer = filer.FilerUtils()
        if clean.lower() == "true":
            builder.clean_local(close_handles=True)
        elif import_local:
            import_local_code_state(builder, _filer, code_branch, platform, config, nomaster)

        framework_args = list(framework_args)
        if licensee:
            framework_args = set_licensee(list(licensee), framework_args)

        LOGGER.end_group()

        # Generate solution
        run_gensln(
            password=password,
            user=email,
            domain_user=domain_user,
            framework_args=framework_args,
            builder=builder,
            nomaster=nomaster,
        )

        # Run p4_compile
        if p4_compile:
            # Sometimes after gensln, files are shelved again which causes compile to fail
            # unshelving again should fix this issue.
            perforce.unshelve(p4_compile_changelist, overide_default_cl=True)
            perforce.set_environment()
            p4_compile_run(p4_compile_changelist, platform)

        # special handling for tool on AWS code-preflight
        msbuild_args = []
        if limit_cpu and platform.lower() == "tool":
            msbuild_args = [
                "/m:20",
                "/p:MultiProcessorCompilation=true;GenerateFullPaths=true;Configuration=release;CL_MPCount=20;Platform=x64",  # pylint: disable=line-too-long
            ]
        # Build solution
        builder.buildsln(msbuild_args)

        if icepick_test and platform.lower() == "tool":
            # Run icepick unittests
            data.DataUtils.set_datadir(data_directory)
            icepicker = icepick.IcepickUtils()
            icepicker.clean_icepicktemp()
            icepick_run_args = [
                "--reporting-build-version-id",
                p4_compile_changelist,
                "--disable-ensemble-plugin",
                "true",
                "--reporting-public-suite",
                "true",
                "--reporting-is-monkey",
                "true",
            ]
            if force_sequential_icepick:
                icepick_run_args.extend(["--max-parallel-tests", "1"])
            if reporting_branch:
                icepick_run_args += ["--reporting-branch", reporting_branch]
            if heartbeat_timeout:
                icepick_run_args += ["--heartbeat-timeout", heartbeat_timeout]
            icepicker.run_icepick(
                platform="win64",
                test_suite=icepick_test,
                test_group="preflight",
                settings_file_list=list(settings_files),
                config=config,
                ignore_icepick_exit_code=ignore_icepick_exit_code,
                build_type="dll",
                run_args=icepick_run_args,
                lease=None,
                extra_framework_args=icepick_extra_framework_args,
                send_frosting_report=frosting_report,
            )
    finally:
        if not do_warmup and not skip_revert:
            perforce.revert()
        # Shelve and Change will clear up the changelist completely
        # otherwise left over cls will clutter the client workspace.
        if p4_compile:
            perforce.shelve(p4_compile_changelist, discard=True)
            perforce.change(p4_compile_changelist, discard=True)


def p4_compile_run(p4_compile_changelist, platform):
    """
    using reshelved CL to perform the compile.
    """
    LOGGER.info("Running p4_compile.")
    compile_path = os.path.join(
        frostbite_core.get_tnt_root(), "bin", "fbcli", "contrib", "p4_compile.py"
    )
    cmd = [
        "python",
        compile_path,
        p4_compile_changelist,
        platform,
        "-nogensln",
        "-nocltag",
    ]

    exit_code, stdout, stderr = core.run(cmd, allow_non_zero_exit_code=True)
    LOGGER.info("".join(stdout))

    if re.search(r"specifiedplatform", "".join(stderr)):
        LOGGER.warning("Skipping P4_Compile, invalid platform used")
    elif exit_code != 0:
        LOGGER.error("Exit Code %s", exit_code)
        LOGGER.error("Error Message %s", stderr)
        raise Exception("P4_Compile Failed")
