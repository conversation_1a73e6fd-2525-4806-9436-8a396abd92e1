{#
    Command:
        data_upgrade_integration
            short_help: Performs a data upgrade integration.

    Arguments:

    Required variables:
        port
            required: True
        client
            required: True
        code_changelist
            required: True
            help: Perforce target code changelist.
        licensee
            multiple: True
            required: True
            help: Licensee for fdu call
        script_path
            required: True
            help: Script path for FDU call.

    Optional variables:
        changelist
            default: None
            help: Deprecated parameter (since 9.1), use --data-changelist instead.
        data_changelist
            default: None
            help: Perforce source data changelist.
        submit/__no_submit
            default: True
        user
            default: None
            help: Perforce user name.
        data_directory
            default: None
            help: Which data directory to use.
        data_directory_source
            default: SourceData
            help: Which data directory to use.
        password
            default: None
            help: User credentials to authenticate to package server.
        email
            default: None
            help: User email to authenticate to package server.
        domain_user
            default: None
            help: The user to authenticate to package server as <PERSON>OMA<PERSON>\user.
        last_data_changelist
            default: None
            help: Data changelist for last build.
        p4_path_source
            default: None
            help: Perforce path for the source.
        revert_branchid_file
            is_flag: True
            help: Revert the BranchId.py file.
        clean
            default: false
            help: Delete TnT/Local if --clean true is passed.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    data_upgrade_integration
    --port {{ port }}
    --client {{ client }}
    --code-changelist {{ code_changelist }}
    --licensee {{ licensee }}
    --script-path {{ script_path }}
    {%- if changelist %}
    --changelist {{ changelist }}
    {%- endif %}
    {%- if data_changelist %}
    --data-changelist {{ data_changelist }}
    {%- endif %}
    {%- if submit/__no_submit %}
    --submit/--no-submit {{ submit/__no_submit }}
    {%- endif %}
    {%- if user %}
    --user {{ user }}
    {%- endif %}
    {%- if data_directory %}
    --data-directory {{ data_directory }}
    {%- endif %}
    {%- if data_directory_source %}
    --data-directory-source {{ data_directory_source }}
    {%- endif %}
    {%- if password %}
    --password {{ password }}
    {%- endif %}
    {%- if email %}
    --email {{ email }}
    {%- endif %}
    {%- if domain_user %}
    --domain-user {{ domain_user }}
    {%- endif %}
    {%- if last_data_changelist %}
    --last-data-changelist {{ last_data_changelist }}
    {%- endif %}
    {%- if p4_path_source %}
    --p4-path-source {{ p4_path_source }}
    {%- endif %}
    {%- if revert_branchid_file %}
    --revert-branchid-file {{ revert_branchid_file }}
    {%- endif %}
    {%- if clean %}
    --clean {{ clean }}
    {%- endif %}
  displayName: elipy data_upgrade_integration
