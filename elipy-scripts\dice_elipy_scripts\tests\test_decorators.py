"""
test_decorators.py

Unit testing for decorators
"""
from mock import patch, MagicMock
import api4jenkins
import os
import pytest
import unittest
from elipy2.exceptions import ELIPYException
from dice_elipy_scripts.utils.decorators import throw_if_files_found


class TestDecorators(unittest.TestCase):
    def test_throw_if_files_found(self):
        assert throw_if_files_found()(_temp_method)(1) == 1

    @patch("os.path.exists", MagicMock(return_value=True))
    @patch("api4jenkins.node.Node.disable")
    @patch("elipy2.LOGGER.error")
    def test_throw_if_files_found_failure(self, mock_error, mock_disable):
        with pytest.raises(ELIPYException):
            throw_if_files_found()(_temp_method)(1)
        assert mock_disable.call_count == 0
        mock_error.assert_called_once()

    @patch("os.path.exists", MagicMock(return_value=True))
    @patch("elipy2.secrets.get_secrets")
    @patch("api4jenkins.node.Nodes.get")
    @patch("api4jenkins.node.Node.disable")
    def test_throw_if_files_found_and_taint(self, mock_disable, mock_get, mock_get_secrets):
        mock_get_secrets.return_value = {"path": {"username": "u", "password": "p"}}
        url = "https:\\jenkins.se"
        os.environ["JENKINS_URL"] = url
        os.environ["NODE_NAME"] = "Jane"
        mock_get.return_value = api4jenkins.node.Node("jenkins", url)
        with pytest.raises(ELIPYException):
            throw_if_files_found()(_temp_method)(1)
        mock_disable.assert_called_once_with("[Taint] WaitingToBeIdle")


def _temp_method(value):
    return value
