"""
codecopy.py
"""
import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import build_metadata_utils, core, filer_paths, LOGGER
from elipy2.cli import pass_context
from elipy2.exceptions import ELIPYException
from elipy2.telemetry import collect_metrics


@click.command("codecopy", short_help="Copy old code build to current changelist.")
@click.option("--code-branch", required=True, help="Perforce branch/stream name.")
@click.option(
    "--source-changelist",
    required=True,
    help="Perforce changelist number of the source.",
)
@click.option(
    "--current-changelist",
    required=True,
    help="Perforce changelist number of the destination.",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
# pylint: disable=too-many-locals, invalid-name, too-many-statements
def cli(_, code_branch, source_changelist, current_changelist):
    """
    Copy old code build to current changelist.
    """
    # Adding sentry tags
    add_sentry_tags(__file__)

    source = filer_paths.get_code_build_root_path(code_branch, source_changelist)
    destination = filer_paths.get_code_build_root_path(code_branch, current_changelist)

    lock_location = os.path.dirname(destination)
    lock_file = os.path.join(lock_location, current_changelist + ".locked")

    if os.path.exists(destination):
        raise ELIPYException(
            "Attempting to deploy to a path that already exists.\
            This indicates that something went wrong when deploying for this changelist earlier.\
            You can either ask the build team to delete the incomplete build on the network share,\
            or wait for a build on a higher changelist."
        )

    LOGGER.info("Copying from {} to {} ..".format(source, destination))
    core.robocopy(source, destination, lock_file=lock_file)

    metadata_manager = build_metadata_utils.setup_metadata_manager()
    metadata_manager.register_code_build(
        destination, changelist=current_changelist, branch=code_branch
    )
