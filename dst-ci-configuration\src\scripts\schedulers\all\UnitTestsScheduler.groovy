package scripts.schedulers.all

import com.ea.lib.model.JobReference
import com.ea.project.GetBranchFile

def branchFile = GetBranchFile.get_branchfile(env.PROJECT_NAME, env.BRANCH_NAME)
def branchInfo = branchFile.general_settings + branchFile.standard_jobs_settings
def project = ProjectClass(env.PROJECT_NAME)

/**
 * UnitTestScheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Get changelist from Perforce') {
            steps {
                script {
                    List<String> ignorePaths = branchInfo?.unittests?.ignorePaths ?: []
                    P4PreviewCode(project, 'stream', env.CODE_FOLDER, env.CODE_BRANCH, env.NON_VIRTUAL_CODE_FOLDER, env.NON_VIRTUAL_CODE_BRANCH, ignorePaths, [], branchInfo)
                }
            }
        }
        stage('Run unit tests') {
            steps {
                script {
                    List<JobReference> jobReferences = []
                    retryOnFailureCause(3, jobReferences) {
                        String codeChangelist = params.CODE_CHANGELIST ?: env.P4_CHANGELIST
                        currentBuild.displayName = "${env.JOB_NAME}.${codeChangelist}"
                        List params = [string(name: 'CODE_CHANGELIST', value: codeChangelist)]
                        String jobName = "${env.BRANCH_NAME}.unittests.job"
                        def job = build(job: jobName, parameters: params, propagate: false)
                        jobReferences << new JobReference(downstreamJob: job, jobName: jobName, parameters: params, propagate: false)
                    }
                }
            }
        }
    }
}
