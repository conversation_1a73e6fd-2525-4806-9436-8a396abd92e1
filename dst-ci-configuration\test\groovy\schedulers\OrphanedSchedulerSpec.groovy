package schedulers

import hudson.model.TopLevelItem
import spock.lang.Shared
import spock.lang.Unroll
import support.DeclarativePipelineSpockTest

class OrphanedSchedulerSpec extends DeclarativePipelineSpockTest {

    void test() {
        when:
        int a = 1 + 1
        then:
        a == 2
    }

    // Commented out because we run out of heap space when we runn all tests. Add back when the issue has been fixed
    @Shared TopLevelItem job1 = mockJob('job1')
    @Shared TopLevelItem job2 = mockJob('job2')
    @Shared TopLevelItem job3 = mockJob('job3')
    @Shared TopLevelItem orphanedJob = mockJob('orphaned')
    @Shared Map<String, TopLevelItem> allGeneratedJobs = [job1: job1, job2: job2, job3: job3]
    @Shared List<TopLevelItem> allJobs = [job1, job2, job3]

    void setup() {
        binding.setVariable('env', [
            SLACK_CHANNEL     : '#test-channel',
            PROJECT_SHORT_NAME: 'kin',
            JOB_URL           : 'https://url.com,',
            BUILD_URL         : 'https://url.com/4/',
        ])
        String seedLog = '''
            Running as SYSTEM
            Processing DSL script src/seeds/all/basic_jobs.groovy
                Processing branch: lab.kin-dev
                    Processing code_matrix...
            Existing items:
                GeneratedJob{name='job1'}
                GeneratedJob{name='job2'}
                GeneratedJob{name='job3'}
            Finished: SUCCESS
        '''
        Map<String, TopLevelItem> items = allGeneratedJobs + ['orphaned': orphanedJob]
        helper.with {
            registerAllowedMethod('getLastBuildLog', [String]) { seedLog }
            registerAllowedMethod('getItem', [String]) { String name -> items.get(name) }
            registerAllowedMethod('getRootUrl') { 'https://www.cobra/' }
            registerAllowedMethod('sendMessage', [Object, String, String, String, String]) {}
            registerAllowedMethod('encodeTo', [String, String]) { String url, String text -> "${url}${text}" }
        }
    }

    @Unroll
    void 'OrphanedScheduler #message when do_delete is set to #doDelete'() {
        given:
        helper.with {
            registerAllowedMethod('isLastBuildSuccess', [String]) { seedSuccess }
            registerAllowedMethod('getJobs') { jobs }
            registerAllowedMethod('getAllGeneratedJobs') { generatedJobs }
        }
        binding.setVariable('params', [
            do_delete: doDelete,
        ])
        when:
        runScript('OrphanedScheduler.groovy')
        printCallStack()
        then:
        assertCalledTimes('sendMessage', sendMessageCallCount)
        assertAnyCall('echo', echoMessage)
        where:
        generatedJobs                              | jobs                  | doDelete | seedSuccess || sendMessageCallCount | echoMessage                                                                             | message
        allGeneratedJobs + [orphaned: orphanedJob] | allJobs               | false    | true        || 1                    | 'Message sent. Done.'                                                                   | 'notifies that there is a generated orphaned job'
        allGeneratedJobs                           | allJobs + orphanedJob | false    | true        || 1                    | 'Message sent. Done.'                                                                   | 'notifies that there is a manually created orphaned job'
        allGeneratedJobs                           | allJobs               | false    | true        || 0                    | 'All is good. No orphaned jobs to delete.'                                              | 'does nothing when there are no orphaned jobs'
        allGeneratedJobs                           | allJobs               | true     | true        || 0                    | 'All is good. No orphaned jobs to delete.'                                              | 'does nothing when there are no orphaned jobs'
        allGeneratedJobs + [orphaned: orphanedJob] | allJobs               | true     | true        || 0                    | 'Deleting orphaned...'                                                                  | 'deletes a generated orphaned job'
        allGeneratedJobs                           | allJobs + orphanedJob | true     | true        || 0                    | 'Deleting orphaned...'                                                                  | 'deletes a manually created orphaned job'
        allGeneratedJobs                           | allJobs               | false    | false       || 0                    | 'Can\'t delete orphaned jobs. Please rerun the seed job and make sure it is a success.' | 'does nothing when the seed job has failed'
    }

    private TopLevelItem mockJob(String name) {
        TopLevelItem item = GroovyMock()
        item.name >> name
        item.url >> "${name}.com"
        item.fullDisplayName >> "${name}-name"
        return item
    }

}
