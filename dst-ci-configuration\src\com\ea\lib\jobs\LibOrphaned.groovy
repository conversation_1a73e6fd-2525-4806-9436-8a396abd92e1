package com.ea.lib.jobs

import com.ea.lib.jobsettings.OrphanedSettings
import javaposse.jobdsl.dsl.DslFactory
import javaposse.jobdsl.dsl.jobs.WorkflowJob

class LibOrphaned {

    static WorkflowJob startJob(DslFactory dslFactory, def masterFile, def project) {
        OrphanedSettings settings = new OrphanedSettings()
        settings.initializeStartJob(masterFile, project)

        return dslFactory.pipelineJob('controller.delete_orphaned_jobs') {
            description(settings.description)
            logRotator(7, 50)
            parameters {
                booleanParam('do_delete', false, 'Whether or not to delete the orphaned jobs')
            }
            properties {
                disableConcurrentBuilds()
                disableResume()
                pipelineTriggers {
                    triggers {
                        cron {
                            spec(settings.cronTrigger)
                        }
                    }
                }
            }
            quietPeriod(0)
            environmentVariables {
                env('SLACK_CHANNEL', settings.slackChannel)
                env('PROJECT_SHORT_NAME', settings.projectShortName)
            }
            definition {
                cps {
                    script(dslFactory.readFileFromWorkspace('src/scripts/schedulers/all/OrphanedScheduler.groovy'))
                    sandbox(true)
                }
            }
        }
    }
}
