package com.ea.project.kin.mastersettings

class DiceKinAutotest {
    static Class project = com.ea.project.kin.Kingston
    static Map branches = [:]
    static Map preflight_branches = [:]
    static Map autotest_branches = [
        'kin-dev'            : [
            code_folder               : 'dev',
            code_branch               : 'kin-dev',
            data_folder               : 'dev',
            data_branch               : 'kin-dev',
            job_label_statebuild_bilbo: 'bilbo || statebuild',
            statebuild_autotest       : true,
            poolbuild_autotest        : true,
            enable_lkg_p4_counters    : true,
        ],
        'kin-dev-unverified' : [
            code_folder               : 'dev',
            code_branch               : 'kin-dev-unverified',
            data_folder               : 'dev',
            data_branch               : 'kin-dev-unverified',
            job_label_statebuild_bilbo: 'bilbo || statebuild',
            statebuild_autotest       : true,
            poolbuild_autotest        : true,
            enable_lkg_p4_counters    : true,
        ],
        'kin-stage'          : [
            code_folder               : 'stage',
            code_branch               : 'kin-stage',
            data_folder               : 'stage',
            data_branch               : 'kin-stage',
            job_label_statebuild_bilbo: 'bilbo || statebuild',
            statebuild_autotest       : true,
            poolbuild_autotest        : true,
            enable_lkg_p4_counters    : true,
        ],
        'kin-release'        : [
            code_folder               : 'release',
            code_branch               : 'kin-release',
            data_folder               : 'release',
            data_branch               : 'kin-release',
            job_label_statebuild_bilbo: 'bilbo || statebuild',
            statebuild_autotest       : true,
            poolbuild_autotest        : true,
            enable_lkg_p4_counters    : true,
        ],
        'future-dev-content' : [
            code_folder               : 'dev',
            code_branch               : 'kin-dev',
            data_folder               : 'future',
            data_branch               : 'future-dev-content',
            job_label_statebuild_bilbo: 'bilbo || statebuild',
            statebuild_autotest       : true,
            poolbuild_autotest        : true,
            enable_lkg_p4_counters    : true,
        ],
        'future-dev-runmode': [
            code_folder               : 'dev',
            code_branch               : 'kin-dev',
            data_folder               : 'dev',
            data_branch               : 'future-dev-runmode',
            job_label_statebuild_bilbo: 'bilbo || statebuild',
            statebuild_autotest       : true,
            poolbuild_autotest        : true,
            enable_lkg_p4_counters    : true,
        ],
    ]
    static Map integrate_branches = [:]
    static Map copy_branches = [:]
    static Map feature_integrations = [:]
    static Map maintenance_branch = [
        'kin-dev': [
            code_folder         : 'dev',
            code_branch         : 'kin-dev',
            data_folder         : 'dev',
            data_branch         : 'kin-dev',
            scheduled_nuke      : 'H 7 * * 6',  // Sat morning 7 A.M
            nuke_label          : 'statebuild',
            wait_doquite_hours  : 5,
            wait_forcekill_hours: 1,
            master_restart_timer: 'H 0 * * 7',
        ]
    ]
    static List dashboard_list = []
    static Map dvcs_configs = [:]
    static Map MAINTENANCE_SETTINGS = [:]
}
