import com.ea.lib.jobsettings.JobMonitoringSettings
import spock.lang.Specification

class JobMonitoringSettingsSpec extends Specification {

    class MasterFile {
        static Map MAINTENANCE_SETTINGS = [
            JOB_MONITORING_TRIGGER          : '0 7,18 * * *',
            JOB_MONITORING_SLACK_CHANNEL    : '#test-channel',
            JOB_MONITORING_CLOUD_NODE_PREFIX: 'bct7-',
        ]
    }

    class ProjectFile {
        static String short_name = 'kin'
    }

    void "test that we get expected job settings in initializeStartJob"() {
        when:
        JobMonitoringSettings settings = new JobMonitoringSettings()
        settings.initializeStartJob(MasterFile, ProjectFile)
        then:
        with(settings) {
            description == 'Maintenance job that alerts when jobs get stuck'
            cronTrigger == MasterFile.MAINTENANCE_SETTINGS.JOB_MONITORING_TRIGGER
            slackChannel == MasterFile.MAINTENANCE_SETTINGS.JOB_MONITORING_SLACK_CHANNEL
            cloudNodePrefix == MasterFile.MAINTENANCE_SETTINGS.JOB_MONITORING_CLOUD_NODE_PREFIX
            projectShortName == ProjectFile.short_name
        }
    }
}
