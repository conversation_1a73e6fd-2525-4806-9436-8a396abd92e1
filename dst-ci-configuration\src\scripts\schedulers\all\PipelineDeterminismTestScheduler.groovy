package scripts.schedulers.all

import com.ea.lib.LibJenkins
import com.ea.lib.model.JobReference
import com.ea.project.GetBranchFile

def branchFile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
def branchInfo = branchFile.general_settings + branchFile.standard_jobs_settings

/**
 * PipelineDeterminismTestScheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Get changelist from reference job') {
            steps {
                script {
                    def configuration = branchInfo.pipeline_determinism_test_configuration
                    String codeChangelist = LibJenkins.getLastStableCodeChangelist("${env.BRANCH_NAME}${configuration.referenceJob}")
                    Map<String, String> injectMap = [
                        code_changelist: codeChangelist,
                    ]
                    EnvInject(currentBuild, injectMap)
                }
            }
        }
        stage('Trigger pipeline determinism test jobs') {
            steps {
                script {
                    List<JobReference> jobReferences = []
                    retryOnFailureCause(3, jobReferences) {
                        def codeChangelist = env.code_changelist

                        def injectMap = [
                            'code_changelist': codeChangelist,
                        ]

                        EnvInject(currentBuild, injectMap)
                        currentBuild.displayName = "${env.JOB_NAME}.${codeChangelist}"
                        Map jobs = [:]
                        def pipeline_determinism_test_matrix = branchFile.pipeline_determinism_test_matrix
                        pipeline_determinism_test_matrix.each { job_configuration ->
                            def job_name = 'pipeline-determinism-test'
                            if (job_configuration.platform != 'win64') { // to keep things backward compatible, we don't append the win64 platform name
                                job_name = job_name + ".${job_configuration.platform}"
                            }
                            job_name = job_name + "${job_configuration.job_name ? ".${job_configuration.job_name}" : ''}"

                            jobs[job_name] = {
                                def script_args = ''
                                if (job_configuration.platform != 'win64') { // to keep things backward compatible, we don't add the platform arg unless it isn't win64
                                    script_args = "-platform=${job_configuration.platform} "
                                }
                                script_args = script_args + "${job_configuration.additional_script_args ? "${job_configuration.additional_script_args}" : ''}"

                                def args = [
                                    string(name: 'code_changelist', value: codeChangelist),
                                    string(name: 'script_args', value: script_args),
                                ]

                                String jobName = "${env.BRANCH_NAME}.${job_name}"
                                def downstreamJob = build(job: jobName, parameters: args, propagate: false)
                                jobReferences << new JobReference(downstreamJob: downstreamJob, jobName: jobName, parameters: args, propagate: false)
                                LibJenkins.printRunningJobs(this)
                            }
                        }
                        parallel(jobs)
                    }
                }
            }
        }
    }
}

String toString() {
    return 'scripts.schedulers.all.PipelineDeterminismTestScheduler'
}
