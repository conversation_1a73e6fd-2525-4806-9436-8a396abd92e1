r"""
virtualize.py

The virtualize script's main purpose is to run the Oreans Virtualizer protection after the build.

Example:
      * elipy virtualize win64game retail
      --artifactory-user svc_*@ea.com --artifactory-apikey ****

"""
import click
from elipy2 import oreans


@click.command("virtualize", short_help="Generate and build the solution.")
@click.argument("platform")
@click.argument("config")
@click.option(
    "--artifactory-apikey", default=None, help="Artifactory apikey for fetching denuvo files."
)
@click.option(
    "--artifactory-user", default=None, help="Artifactory user for fetching denuvo files."
)
@click.option("--oreans-config", default=None, help="Config file for Oreans Virtualizer.")
def cli(platform, config, artifactory_apikey, artifactory_user, oreans_config):
    r"""
    The virtualize script's main purpose is to run the Oreans Virtualizer
    protection after the build.

    Example:
        * elipy virtualize win64game retail
        --artifactory-user svc_*@ea.com --artifactory-apikey ***

    """

    oreans.protect(
        platform=platform,
        config=config,
        oreans_config=oreans_config,
        artifactory_user=artifactory_user,
        artifactory_api_key=artifactory_apikey,
    )
