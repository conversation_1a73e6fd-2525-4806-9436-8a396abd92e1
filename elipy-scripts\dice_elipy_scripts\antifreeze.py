"""
antifreeze.py
"""
import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.licensee_helper import set_licensee
from dice_elipy_scripts.utils.state_utils import import_local_code_state
from dice_elipy_scripts.utils.code_utils import run_gensln
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import code, core, data, filer, local_paths, LOGGER, p4, frostbite_core
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics
from elipy2.exceptions import ELIPYException


@click.command("antifreeze", short_help="Run the Antifreeze data export.")
@click.option("--clean", default="false", help="Delete TnT/Local if --clean true is passed.")
@click.option("--code-branch", default=None, help="Perforce branch name for code.")
@click.option("--code-changelist", required=True, help="Perforce changelist number for code.")
@click.option("--data-changelist", required=True, help="Perforce changelist number for data.")
@click.option(
    "--data-directory",
    required=True,
    help="Data directory to use for licensee settings and to find data builds.",
)
@click.option("--dry-run", is_flag=True, help="Don't submit the built Antifreeze files.")
@click.option("--framework-args", multiple=True, help="Framework arguments for gen sln.")
@click.option("--import-local", is_flag=True, help="Imports contents of TnT/Local from filer.")
@click.option("--p4-code-client", required=True, help="Workspace in Perforce for code.")
@click.option("--p4-code-port", required=True, help="Perforce server for code.")
@click.option("--p4-data-client", required=True, help="Workspace in Perforce for data.")
@click.option("--p4-data-port", required=True, help="Perforce server for data.")
@click.option("--p4-user", default=None, help="Perforce user name.")
@click.option(
    "--password",
    default=None,
    help="User credentials to authenticate to package server",
)
@click.option("--email", default=None, help="User email to authenticate to package server")
@click.option(
    "--domain-user",
    default=None,
    help="The user to authenticate to package server as DOMAIN\\user",
)
@click.option("--licensee", multiple=True, default=None, help="Licensee to use.")
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    clean,
    code_branch,
    code_changelist,
    data_changelist,
    data_directory,
    dry_run,
    framework_args,
    import_local,
    p4_code_client,
    p4_code_port,
    p4_data_client,
    p4_data_port,
    p4_user,
    password,
    email,
    domain_user,
    licensee,
):
    """
    Run the Antifreeze data export.
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    LOGGER.info("Starting Antifreeze.")

    # Create Perforce object and get changelist description
    perforce = p4.P4Utils(p4_data_port, user=p4_user, client=p4_data_client)
    description = perforce.get_description(data_changelist)
    LOGGER.info("Building Antifreeze for changelist {}:\n{}".format(data_changelist, description))

    # Generate and build solution for ANT
    platform = "ant"
    config = "release"
    LOGGER.info("Building code for platform {} and config {}.".format(platform, config))

    builder = code.CodeUtils(
        platform,
        config,
        monkey_build_label=code_changelist,
        p4_port=p4_code_port,
        p4_user=p4_user,
        p4_client=p4_code_client,
    )

    if clean.lower() == "true":
        builder.clean_local(close_handles=True)
    elif import_local:
        if code_branch is not None:
            _filer = filer.FilerUtils()
            import_local_code_state(builder, _filer, code_branch, platform, config, nomaster=False)
        else:
            raise ELIPYException("To import TnT/Local, a code branch must be specified.")

    framework_args = list(framework_args)
    framework_args = set_licensee(list(licensee), framework_args)

    LOGGER.info("Set data directory.")
    data.DataUtils.set_datadir(data_directory)

    LOGGER.info("Run gensln.")
    run_gensln(
        password=password,
        user=email,
        domain_user=domain_user,
        framework_args=framework_args,
        builder=builder,
    )
    LOGGER.info("Run buildsln.")
    builder.buildsln()

    # Create paths to use later
    antifreeze_dir = local_paths.get_antifreeze_dir()
    antifreeze_p4_path = antifreeze_dir + os.sep + "..."

    try:
        # Delete everything on the machine in the Antifreeze directory
        core.delete_folder(antifreeze_dir)

        # Run Antifreeze export
        ant_pipeline = os.path.join(
            local_paths.get_tnt_local_path(),
            "Bin",
            "Tools",
            "Ant",
            "Release",
            "Antpipeline.exe",
        )
        os.environ["ANT_SOURCE_BASE_PATH"] = os.path.join(
            frostbite_core.get_game_root(), data_directory, "ANT_Source"
        )
        if not os.path.exists(local_paths.get_ant_local_dir()):
            raise ELIPYException("No ANT path found.")
        animations_file = os.path.join(local_paths.get_ant_local_dir(), "Animations.apj")
        if not os.path.isfile(animations_file):
            raise ELIPYException("The file Animations.apj not found.")
        antifreeze_source_dir = os.path.join(antifreeze_dir, "Source")
        antifreeze_export_args = [
            ant_pipeline,
            "/Project=" + animations_file,
            "/ExportDBX=" + antifreeze_source_dir,
        ]
        LOGGER.info("Run Antifreeze export.")
        core.run(antifreeze_export_args)

        # Create Antifreeze manifest file
        antifreeze_manifest = os.path.join(antifreeze_dir, "Manifest.txt")
        if not os.path.exists(antifreeze_manifest):
            # Pylint doesn't detect that we only try to create the file.
            with open(antifreeze_manifest, "x"):
                LOGGER.info("File created: {}".format(antifreeze_manifest))
        create_manifest = os.path.join(
            frostbite_core.get_tnt_root(), "Tools", "CreateManifest", "CreateManifest.exe"
        )
        antifreeze_manifest_args = [
            create_manifest,
            antifreeze_source_dir,
            antifreeze_manifest,
        ]
        LOGGER.info("Create Antifreeze manifest.")
        core.run(antifreeze_manifest_args)

        # Reconcile differences
        LOGGER.info("Reconcile differences between built and checked in versions of Antifreeze.")
        perforce.reconcile(path=antifreeze_p4_path, options=["a", "d", "e", "f"], quiet=False)

        if not dry_run:
            # Commit the differences
            LOGGER.info("Submitting to Perforce.")
            message = "Antifreeze for changelist {}:\n{}".format(data_changelist, description)
            message += "\nJenkins URL: " + os.environ.get("BUILD_URL", "None")
            perforce.submit(message=message)
        else:
            LOGGER.info("Dry run, will not submit Antifreeze.")
    finally:
        perforce.revert(quiet=True)
        perforce.clean(antifreeze_p4_path)
