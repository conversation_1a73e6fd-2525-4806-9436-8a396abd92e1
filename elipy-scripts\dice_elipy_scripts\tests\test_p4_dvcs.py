"""
test_p4_dvcs.py

Unit testing for p4_dvcs
"""
import unittest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import patch
from dice_elipy_scripts.p4_dvcs import cli
from elipy2.exceptions import ELIPYException


class P4DvcsTests(unittest.TestCase):
    OPTION_CMD = "--cmd"
    OPTION_REMOTE_SPEC = "--remote-spec"
    OPTION_PORT = "--port"
    OPTION_USER = "--user"
    OPTION_CLIENT = "--client"
    OPTION_DRY_RUN = "--dry-run"

    def setUp(self):
        self.patcher_upload_metrics = patch("elipy2.telemetry.upload_metrics")
        self.mock_upload_metrics = self.patcher_upload_metrics.start()

    def tearDown(self):
        self.patcher_upload_metrics.stop()

    @patch("elipy2.p4.P4Utils.remotes", return_value=["some_remote_spec"])
    @patch("elipy2.p4.P4Utils.push")
    def test_p4_dvcs_push(self, patch_push, patch_remotes):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.OPTION_CMD,
                "push",
                self.OPTION_REMOTE_SPEC,
                "some_remote_spec",
                self.OPTION_PORT,
                "some_p4_port",
                self.OPTION_USER,
                "some_user",
                self.OPTION_CLIENT,
                "some_client",
            ],
        )

        assert patch_push.call_count == 1
        assert patch_remotes.call_count == 1

    @patch("elipy2.p4.P4Utils.remotes", return_value=[""])
    @patch("elipy2.p4.P4Utils.push")
    def test_p4_dvcs_push_missing_remote(self, patch_push, patch_remotes):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.OPTION_CMD,
                "push",
                self.OPTION_REMOTE_SPEC,
                "some_remote_spec",
                self.OPTION_PORT,
                "some_p4_port",
                self.OPTION_USER,
                "some_user",
                self.OPTION_CLIENT,
                "some_client",
            ],
        )
        assert isinstance(result.exception, ELIPYException)
        assert patch_push.call_count == 0
        assert patch_remotes.call_count == 1

    @patch("elipy2.p4.P4Utils.remotes", return_value=["some_remote_spec"])
    @patch("elipy2.p4.P4Utils.fetch")
    def test_p4_dvcs_fetch(self, patch_fetch, patch_remotes):
        runner = CliRunner()

        result = runner.invoke(
            cli,
            [
                self.OPTION_CMD,
                "fetch",
                self.OPTION_REMOTE_SPEC,
                "some_remote_spec",
                self.OPTION_PORT,
                "some_p4_port",
                self.OPTION_USER,
                "some_user",
                self.OPTION_CLIENT,
                "some_client",
            ],
        )

        assert patch_fetch.call_count == 1
        assert patch_remotes.call_count == 1
