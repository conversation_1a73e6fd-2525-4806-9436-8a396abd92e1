package com.ea.lib

class LibSlack {

    // Authentication keys for various areas.
    private static final Map AUTHENTICATION_CREDS = [
        bct      : 'bct-slack-token',
        cob      : 'dre_cobra-slack-token',
        dre_cobra: 'dre_cobra-slack-token',
        dst      : 'walrus-slack-token',
        fb1      : 'fb1-slack-token',
        fb1_aws  : 'fb1-slack-token',
        fb1_bfg  : 'fb1-slack-token',
        fbd      : 'fbdev-slack-token',
        gnt      : 'granite-slack-token',
        mer      : 'merlin-slack-token',
        nfs      : 'nfsupgrade-slack-token',
        kin      : 'kingston-slack-token',
        san      : 'santiago-slack-token',
        sta      : 'casablanca-slack-token',
        vik      : 'NOT-SET',
    ]

    // Default domain, this is unlikely to change.
    private static final String DEFAULT_DOMAIN = 'electronic-arts'

    /*
        Standard layout for the majority of all the projects we run.
        Set slack_always_notify to true, to enable failure notification repeatedly
        By default, it only sends out on failure on the first time till status flip
    */

    static void slack_default(def job, String room_name, String project_short, boolean slack_always_notify) {
        slack_custom(job, room_name, project_short, false, false, true, true, false, false, true, slack_always_notify)
    }

    // Custom method to be able to pass specific needs as required to suit edge cases.
    static void slack_custom(def job, String room_name, String project_short, boolean start_notify, boolean notify_not_build, boolean notify_abort, boolean notify_failure, boolean notify_success, boolean notify_unstable, boolean notify_back_to_normal, boolean notify_repeat_failures) {
        job.with {
            publishers {
                slackNotifier {
                    commitInfoChoice('NONE')
                    includeCustomMessage(false)
                    teamDomain(DEFAULT_DOMAIN)
                    tokenCredentialId(AUTHENTICATION_CREDS[project_short])
                    room(room_name)
                    startNotification(start_notify)
                    notifyNotBuilt(notify_not_build)
                    notifyAborted(notify_abort)
                    notifyFailure(notify_failure)
                    notifySuccess(notify_success)
                    notifyUnstable(notify_unstable)
                    notifyBackToNormal(notify_back_to_normal)
                    notifyRepeatedFailure(notify_repeat_failures)
                }
            }
        }
    }

    static void forPipelines(context, String settings, String shortName) {
        if (settings) {
            executeForPipelines(context, shortName, [settings])
        } else {
            noSettings()
        }
    }

    static void forPipelines(context, Map settings, String shortName) {
        if (settings?.channels) {
            settings.with {
                LibSlack.executeForPipelines(
                    context,
                    shortName,
                    channels,
                    always_notify ?: false,
                    skip_for_multiple_failures ?: false,
                    message_color ?: 'good',
                    report_message
                )
            }
        } else {
            noSettings()
        }
    }

    static void sendMessage(def context, def channel, String message, String shortName, String color = 'good') {
        context.steps.slackSend(
            channel: channel,
            color: color,
            message: message,
            teamDomain: DEFAULT_DOMAIN,
            tokenCredentialId: AUTHENTICATION_CREDS[shortName]
        )
    }

    private static void noSettings() {
        echo 'No Slack channel defined for this job.'
    }

    private static void executeForPipelines(context, String shortName, List channels, Boolean alwaysNotify = false, Boolean skipForMultipleFailures = false, String messageColour = 'good', String reportMessage = null) {
        // Get the result for this job and for the previous job of the same type.
        def run = context.currentBuild
        def buildResult = run.currentResult ?: 'SUCCESS'
        def lastResult = run?.previousCompletedBuild?.result ?: 'SUCCESS'
        def bothResultsAre = { result ->
            return [buildResult, lastResult].every { it == result }
        }

        if ((bothResultsAre('FAILURE') && skipForMultipleFailures) || (bothResultsAre('SUCCESS') && !alwaysNotify)) {
            return
        }

        def constructReportMessage = {
            def constructedReportMessage = "${run.displayName} - job "

            switch (buildResult) {
                case 'FAILURE':
                    constructedReportMessage += lastResult == 'FAILURE' ? 'still failing' : 'started to fail'
                    break
                case 'SUCCESS':
                    constructedReportMessage += lastResult == 'SUCCESS' ? 'working' : 'back to normal'
                    break
                default:
                    constructedReportMessage += buildResult.toLowerCase()
            }

            return "$constructedReportMessage (<${run.absoluteUrl}|Open>)"
        }

        for (channel in channels) {
            sendMessage(context,
                channel,
                reportMessage ?: constructReportMessage(),
                shortName,
                buildResult == 'SUCCESS' ? messageColour : 'danger'
            )
        }
    }
}
