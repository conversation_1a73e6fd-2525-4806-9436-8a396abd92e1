"""
p4_utils.py
"""


def contains_error_string(line):
    """
    Check for error sub strings
    Check if the passed string contains any "errors".

    :params line: <string> The string to check
    :return: <bool> True if an error string was found
    """

    result = False

    error_strings = ["access denied", "usage: fetch", "invalid option"]

    for error_string in error_strings:
        if error_string.lower() in line:
            result = True
            break

    return result
