package com.ea.lib.jobsettings

import com.ea.lib.LibCommonNonCps

class DataSnooperSettings extends JobSetting {

    String dataSnooperReferenceJob
    Boolean frostbiteSyncerSetup
    String nonVirtualDataBranch
    String nonVirtualDataFolder
    String dataset

    void initializeDataSnooperStart(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        def modifiers = ['datasnooper']
        dataSnooperReferenceJob = branchInfo.datasnooper_reference_job ?: branchInfo.branch_name + '.code.start'
        isDisabled = LibCommonNonCps.get_setting_value(branchInfo, modifiers, 'disable_build', false)
        frostbiteSyncerSetup = this.projectFile.frostbite_syncer_setup ?: false
        nonVirtualDataBranch = branchInfo.non_virtual_data_branch ?: ''
        nonVirtualDataFolder = branchInfo.non_virtual_data_folder ?: ''
        cronTrigger = branchInfo.trigger_string_datasnooper ?: 'H 20 * * 0'
        description = 'Runs datasnooper using ELIPY2.'
        dataBranch = branchInfo.data_branch
        dataFolder = branchInfo.data_folder
        dataset = branchInfo.dataset
        projectName = this.projectFile.name
    }

    void initializeDataSnooperJob(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        def modifiers = ['datasnooper']
        jobLabel = branchInfo.job_label_statebuild ?: 'statebuild'
        def timeoutHours = LibCommonNonCps.get_setting_value(branchInfo, modifiers, 'timeout_hours', 12, projectFile)
        timeoutMinutes = timeoutHours * 60
        description = 'Runs datasnooper using ELIPY2.'
        buildName = '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'
        elipyCmd = "${this.elipyCall} datasnooper --dbmanifest-path ${branchInfo.datasnooper_dbmanifest_path}"
    }

}
