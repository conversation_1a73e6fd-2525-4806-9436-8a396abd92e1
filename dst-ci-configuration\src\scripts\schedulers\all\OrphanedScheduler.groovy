//file:noinspection UnnecessaryGetter
//codenarc-disable UnnecessaryGetter
package scripts.schedulers.all

import com.ea.lib.LibJenkins
import com.ea.lib.LibSlack
import hudson.console.ModelHyperlinkNote
import hudson.model.Result
import hudson.model.TopLevelItem
import jenkins.util.io.CompositeIOException

/**
 * OrphanedScheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Determine which jobs are orphaned and delete if appropriate') {
            steps {
                script {
                    String slackChannel = env.SLACK_CHANNEL
                    String projectShortName = env.PROJECT_SHORT_NAME
                    Map<String, TopLevelItem> toDelete = [:]
                    if (LibJenkins.isLastBuildSuccess('seed')) {
                        Map<String, TopLevelItem> generatedJobs = LibJenkins.getAllGeneratedJobs()
                        List<TopLevelItem> jobs = LibJenkins.getJobs()
                        String seedLog = LibJenkins.getLastBuildLog('seed')
                        Map seedJobs = seedLog.split('\\r?\\n').findAll {
                            it.contains('GeneratedJob{name=\'')
                        }.collectEntries {
                            String toReturn = it.replace('GeneratedJob{name=\'', '')
                            toReturn = toReturn.replaceAll('\\s', '')
                            toReturn = toReturn.replace('\'}', '')
                            return [toReturn.toLowerCase(), toReturn]
                        }
                        StringJoiner log = new StringJoiner('\n')
                        StringJoiner slackMessage = new StringJoiner('\n')
                        log.add('The following jobs should be deleted:')
                        slackMessage.add("A <${env.BUILD_URL}console|maintenance job> has found orphaned jobs.")
                        slackMessage.add('The following jobs should be deleted:')
                        // Look for orphaned jobs created by the seed
                        generatedJobs.each {
                            String job = seedJobs.get(it.key.toLowerCase())
                            if (job == null) {
                                TopLevelItem jobReference = LibJenkins.getItem(it.key as String)
                                if (jobReference != null) {
                                    toDelete[it.key as String] = jobReference
                                    log.add("- ${ModelHyperlinkNote.encodeTo("/${jobReference.url}", jobReference.fullDisplayName)}")
                                    slackMessage.add("- <${LibJenkins.getRootUrl()}${jobReference.url}|${jobReference.fullDisplayName}>")
                                } else {
                                    echo "Jenkins claims that the following job exists on this master, but I coulnd't find it: ${it.key}"
                                }
                            }
                        }
                        // Look for manually created jobs
                        jobs.each { TopLevelItem item ->
                            if (item.name != 'seed' && generatedJobs.get(item.name) == null) {
                                toDelete[item.name] = item
                                log.add("- ${ModelHyperlinkNote.encodeTo("/${item.url}", item.fullDisplayName)}")
                                slackMessage.add("- <${LibJenkins.getRootUrl()}${item.url}|${item.fullDisplayName}>")
                            }
                        }
                        if (params.do_delete && !toDelete.isEmpty()) {
                            log.add('do_delete has been set to true. Starting deletion process')
                            echo log.toString()
                            toDelete.each { String name, TopLevelItem job ->
                                int retries = 20
                                while (retries > 0) {
                                    try {
                                        echo "Deleting ${name}..."
                                        job.delete()
                                        retries = 0
                                    } catch (CompositeIOException ignored) {
                                        echo "Failed to delete ${name} because it's running. Aborting the job and retrying."
                                        job.lastBuild.doStop()
                                        sleep 10
                                        retries--
                                    }
                                }
                            }
                            echo 'Done.'
                        } else if (!toDelete.isEmpty()) {
                            log.add('Sending slack message')
                            echo log.toString()
                            slackMessage.add("To delete these jobs, rerun <${env.JOB_URL}/build|this> job with `do_delete` set to `true`.")
                            String color = 'danger'
                            LibSlack.sendMessage(this, slackChannel, slackMessage.toString(), projectShortName, color)
                            echo 'Message sent. Done.'
                        } else {
                            echo 'All is good. No orphaned jobs to delete.'
                        }
                    } else {
                        echo 'Can\'t delete orphaned jobs. Please rerun the seed job and make sure it is a success.'
                        currentBuild.result = Result.UNSTABLE.toString()
                        DownstreamErrorReporting(currentBuild)
                    }
                }
            }
        }
    }
}
