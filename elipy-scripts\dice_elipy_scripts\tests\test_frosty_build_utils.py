"""
test_frosty_build_utils.py

Unit testing for frosty_build_utils
"""
import os
import unittest
from mock import patch, MagicMock
from dice_elipy_scripts.utils.frosty_build_utils import (
    authenticate_eapm_credstore,
    generate_buildlayout_xml,
    install_required_sdks,
    install_sdks,
)


@patch("elipy2.telemetry.upload_metrics", MagicMock())
@patch("elipy2.SETTINGS.get", MagicMock())
class TestFrostyBuildUtils(unittest.TestCase):
    def setUp(self):
        self.patcher_read_fb_version = patch("elipy2.frostbite_core.read_fb_version")
        self.mock_read_fb_version = self.patcher_read_fb_version.start()

    def tearDown(self):
        self.patcher_read_fb_version.stop()

    @patch("elipy2.frostbite.sdk_utils.install_required_sdks")
    def test_run_install_required_sdk(self, mock_install_required_sdks):
        install_required_sdks(
            password="pass",
            user="user",
            domain_user="d_user",
            platform="ps5",
            use_shift_build=False,
        )
        mock_install_required_sdks.assert_called_once_with(
            password="pass",
            user="user",
            domain_user="d_user",
            platform="ps5",
            use_shift_build=False,
        )

    @patch("elipy2.frostbite.sdk_utils.install_sdks_elipy")
    def test_install_sdks(self, mock_install_sdks_elipy):
        install_sdks(password="pass", user="user", domain_user="d_user", platform="ps5")
        mock_install_sdks_elipy.assert_called_once_with(
            password="pass", user="user", domain_user="d_user", platform="ps5"
        )

    @patch("elipy2.frostbite.sdk_utils.authenticate_eapm_credstore")
    def test_authenticate_eapm_credstore(self, mock_authenticate_eapm_credstore):
        authenticate_eapm_credstore("pass", "user", domain_user="d_user")
        mock_authenticate_eapm_credstore.assert_called_once_with(
            password="pass", user="user", domain_user="d_user"
        )

    def test_generate_buildlayout_xml(self):
        data_dir = os.path.join(os.path.dirname(__file__), "data")
        new_layout_file = os.path.join(data_dir, "BattlefieldGame.buildlayout.xml")

        try:
            generate_buildlayout_xml(data_dir)
            with open(new_layout_file) as data_file:
                new_file_content = data_file.read()
        finally:
            os.remove(new_layout_file)

        assert "<buildlayout>" in new_file_content
        assert "<exe>BattlefieldGame.Main_Win64_final.exe</exe>" in new_file_content
        assert "<platform>Windows</platform>" in new_file_content
        assert "<exe>BattlefieldGame.Main_Win64_final.exe</exe>" in new_file_content

    @patch("os.path.exists")
    def test_generate_buildlayout_xml_not_exist(self, mock_exist):
        mock_exist.return_value = False
        data_dir = os.path.join(os.path.dirname(__file__), "data")
        generate_buildlayout_xml(data_dir)
