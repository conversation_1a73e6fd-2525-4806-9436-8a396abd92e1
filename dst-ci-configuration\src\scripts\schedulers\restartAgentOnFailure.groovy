package scripts.schedulers

import hudson.model.Job
import hudson.model.Result
import jenkins.model.Jenkins

/**
 * restartAgentOnFailure.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Run job to detect broken nodes that need a restart') {
            steps {
                script {
                    currentBuild.displayName = env.JOB_NAME + '.' + env.BUILD_NUMBER
                    List<Job> failedJobs = []
                    List<String> failureStrings = [
                        'file oo2core_8_win64.dll operating system write error',
                        'HTTP Error 503: Service Unavailable; periodic maintenance',
                        'Failed to obtain a lease from \'localhost\': offline',
                    ]
                    echo('Ignoring running jobs...')
                    List<Job> jobs = Jenkins.get().getItems(Job).findAll { job ->
                        !job.building
                    }

                    echo('Processing jobs...')
                    for (def job : jobs) {
                        try {
                            echo("\tProcessing ${job.name}")
                            def build = job.lastCompletedBuild
                            def buildLog = build?.result == Result.FAILURE ? build.getLog(200) : ['']
                            def failureStringFound = buildLog.any { line ->
                                failureStrings.any { failureString ->
                                    line.contains(failureString)
                                }
                            }

                            if (failureStringFound) {
                                failedJobs.add(job)
                            }
                        } catch (FileNotFoundException exc) {
                            echo("No log found for ${job.name}, skipping")
                        }
                    }

                    echo("Found ${failedJobs.size()} failed builds, restarting nodes")
                    List<String> buildNodeNames = []
                    failedJobs.each { failedJob ->
                        // codenarc-disable UnnecessaryGetter
                        buildNodeNames.add(failedJob.getLastBuiltOn().name)
                    }
                    buildNodeNames.unique()
                    buildNodeNames.each {
                        echo "Build recently failed for ${it}"
                    }

                    List<String> recentlyRebooted = []
                    def restartProject = Jenkins.get().getItemByFullName('agent.reboot')
                    Long HOUR = 60 * 60 * 1000
                    Date endDate = new Date()
                    Date startDate = new Date(endDate.toInstant().toEpochMilli() - (HOUR * 2))
                    // codenarc-disable UnnecessaryGetter
                    List<Job> restartJobs = restartProject.getBuildsByTimestamp(startDate.getTime(), endDate.getTime())
                    restartJobs.each { job ->
                        String nodeName = job.getBuiltOn().name
                        echo "Skipping recently rebooted node: $nodeName"
                        recentlyRebooted.add(nodeName)
                    }

                    buildNodeNames = buildNodeNames.findAll { nodeName ->
                        !recentlyRebooted.contains(nodeName)
                    }

                    buildNodeNames.each { buildNodeName ->
                        echo "Triggering agent.reboot job on ${buildNodeName}"
                        build wait: false, propagate: false, job: 'agent.reboot', parameters: [
                            [
                                $class         : 'NodeParameterValue',
                                labels         : ["$buildNodeName"],
                                name           : 'machine',
                                nodeEligibility: [$class: 'AllNodeEligibility']
                            ]
                        ]
                    }

                    if (failedJobs) {
                        currentBuild.result = 'UNSTABLE'
                    }
                }
                SlackMessageNew(currentBuild, '#cobra-support-alerts', 'cob')
            }
        }
    }
}
