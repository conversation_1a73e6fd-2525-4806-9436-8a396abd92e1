   FrostyOrchestratorScheduler.run()
      FrostyOrchestratorScheduler.pipeline(groovy.lang.Closure)
         FrostyOrchestratorScheduler.allowBrokenBuildClaiming()
         FrostyOrchestratorScheduler.timestamps()
         FrostyOrchestratorScheduler.echo(Executing on agent [label:any])
         FrostyOrchestratorScheduler.stage(Determine changelist, groovy.lang.Closure)
            FrostyOrchestratorScheduler.script(groovy.lang.Closure)
               FrostyOrchestratorScheduler.echo(Determining which changelist to build.)
               LibJenkins.retrieveIfTargetChangelistsAreHigher(trunk-code-dev.deployment-data.start, trunk-code-dev.data.start)
               FrostyOrchestratorScheduler.echo(Code changelist: 101, data changelist: 201)
               FrostyOrchestratorScheduler.EnvInject({result=null, displayName=trunk-code-dev.deployment-data.start.201.101}, {code_changelist=101, data_changelist=201})
         FrostyOrchestratorScheduler.stage(<PERSON><PERSON> builds, groovy.lang.Closure)
            FrostyOrchestratorScheduler.script(groovy.lang.Closure)
               FrostyOrchestratorScheduler.echo(Triggering builds)
               FrostyOrchestratorScheduler.string({name=code_changelist, value=101})
               FrostyOrchestratorScheduler.string({name=data_changelist, value=201})
               FrostyOrchestratorScheduler.string({name=clean_data, value=false})
               FrostyOrchestratorScheduler.build({job=trunk-code-dev.deployment-data.start, wait=false, parameters=[{name=code_changelist, value=101}, {name=data_changelist, value=201}, {name=clean_data, value=false}], propagate=false})
               FrostyOrchestratorScheduler.echo(Done.)
