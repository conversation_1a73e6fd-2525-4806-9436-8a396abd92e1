"""
test_snowcache_utils.py

Unit testing for snowcache_utils.py
"""
import mock
import pytest

from mock.mock import MagicMock
from requests.exceptions import ReadTimeout, HTTPError, ConnectTimeout

from dice_elipy_scripts.utils import snowcache_utils


class TestSnowcacheUtils:
    @pytest.mark.parametrize(
        "clean, snowcache_mode_override, expected_result",
        [  # Clean flag should always force clean
            # Typical Dice
            (True, "", "forceupload"),
            (True, "", "forceupload"),
            (False, "", "uploadanddownload"),
            (False, "", "uploadanddownload"),
            (False, "forceupload", "forceupload"),
            (False, "upload", "upload"),
            # Typical Criterion
            (False, "upload", "upload"),
            (True, "upload", "upload"),
            (False, "upload", "upload"),
            (True, "upload", "upload"),
        ],
    )
    def test_get_snowcache_mode(self, clean, snowcache_mode_override, expected_result):
        result = snowcache_utils._get_snowcache_mode(clean, snowcache_mode_override)
        assert result == expected_result

    @pytest.mark.parametrize(
        "clean, snowcache_mode_override, use_snowcache, expected_result",
        [  # Clean flag should always force clean
            # Typical Dice
            (True, "", True, "forceupload"),
            (True, "", False, []),
            (False, "forceupload", False, "forceupload"),
            (False, "upload", True, "upload"),
            # Typical Criterion
            (False, "upload", True, "upload"),
            (True, "upload", True, "upload"),
            (False, "upload", False, "upload"),
            (True, "upload", False, "upload"),
        ],
    )
    def test_get_snowcache_mode_args(
        self, clean, snowcache_mode_override, use_snowcache, expected_result
    ):
        result = snowcache_utils.get_snowcache_mode_args(
            clean, snowcache_mode_override, use_snowcache
        )
        assert isinstance(result, list)
        if len(result) > 0:
            assert [expected_result in item for item in result]

    @pytest.mark.parametrize(
        "use_snowcache, snowcache_mode_override, clean, expected_result",
        [  # Clean flag should always force clean
            # Typical Dice
            (True, "", True, True),
            (True, "", False, True),
            (True, "forceupload", True, True),
            (True, "forceupload", False, True),
            # Typical Criterion
            (True, "upload", True, True),
            (True, "upload", False, False),
            (False, "upload", True, True),
            (False, "upload", False, False),
        ],
    )
    def test_clean_required_logic(
        self, use_snowcache, snowcache_mode_override, clean, expected_result
    ):
        result = snowcache_utils.clean_required(use_snowcache, snowcache_mode_override, clean)
        assert result == expected_result

    @mock.patch("dice_elipy_scripts.utils.snowcache_utils.LOGGER.warning", MagicMock())
    @mock.patch("dice_elipy_scripts.utils.snowcache_utils.get_avalanche_host")
    @mock.patch("dice_elipy_scripts.utils.snowcache_utils.AvalancheServer")
    @pytest.mark.parametrize("avalanche_status", [({"summary": "Running"}, 200)])
    def test_avalanche_api_endpoint_built_correctly_from_settings_avalanche_host(
        self,
        mock_avalanche_server,
        mock_get_avalanche_host,
        avalanche_status,
    ):
        # Arrange
        mock_avalanche_server.return_value = MagicMock()
        mock_avalanche_server.get_status.return_value = avalanche_status
        mock_settings = MagicMock()
        mock_platform = "mock_station"
        mock_get_avalanche_host.return_value = "uts-1234.fake.ad.ea.com"

        # Act
        snowcache_utils.avalanche_upstream_server_healthy(mock_settings, mock_platform)

        # Assert
        mock_avalanche_server.assert_called_once_with(url="http://uts-1234.fake.ad.ea.com:1338/")

    @mock.patch("dice_elipy_scripts.utils.snowcache_utils.LOGGER.warning", MagicMock())
    @mock.patch("dice_elipy_scripts.utils.snowcache_utils.get_avalanche_host")
    @mock.patch("dice_elipy_scripts.utils.snowcache_utils.AvalancheServer")
    @pytest.mark.parametrize(
        "get_status_side_effect,expected_result",
        [
            ({"summary": "running"}, True),  # ok!
            ({"summary": "Running"}, True),  # ok! (case insensitive)
            (HTTPError, False),  # bad request
            ({"summary": "not running"}, False),  # Service not running
            ({"summary": ""}, False),  # Service not running
            (ReadTimeout, False),  # Too slow response time
            (ConnectionError, False),  # Can't connect to server
        ],
    )
    def test_avalanche_upstream_server_healthy_results_appropriate(
        self,
        mock_avalanche_server,
        mock_get_avalanche_host,
        get_status_side_effect,
        expected_result,
    ):
        # Arrange
        mock_avalanche_server = MagicMock()
        mock_avalanche_server.return_value.get_status = MagicMock(
            side_effect=get_status_side_effect
        )
        mock_settings = MagicMock()
        mock_platform = "mock_station"
        mock_get_avalanche_host.return_value = "uts-1234.fake.ad.ea.com"

        # Act
        result = snowcache_utils.avalanche_upstream_server_healthy(mock_settings, mock_platform)

        # Assert
        result = expected_result

    @mock.patch("dice_elipy_scripts.utils.snowcache_utils.LOGGER.warning")
    @mock.patch("dice_elipy_scripts.utils.snowcache_utils.get_avalanche_host")
    @mock.patch("dice_elipy_scripts.utils.snowcache_utils.AvalancheServer")
    @pytest.mark.parametrize(
        "get_status_side_effect, expected_call_count",
        [
            (HTTPError, 1),  # ConnectionError
            (ReadTimeout, 1),  # Timeout on get
            (ConnectionError, 1),  # ConnectionError
            ({"summary": "not-running"}, 1),  # Non 'running' service
            ({"summary": ""}, 1),  # Non 'running' service
            ({"summary": "Running"}, 0),  # Good service
            (ConnectTimeout, 1),  # ConnectTimeout
        ],
    )
    def test_avalanche_upstream_requirements_failure_logs_warning(
        self,
        mock_avalanche_server,
        mock_get_avalanche_host,
        mock_log_warning,
        get_status_side_effect,
        expected_call_count,
    ):
        # Arrange
        mock_avalanche_server = MagicMock()
        mock_avalanche_server.return_value.get_status = MagicMock(
            side_effect=get_status_side_effect
        )
        mock_settings = MagicMock()
        mock_platform = "mock_station"
        mock_get_avalanche_host.return_value = "uts-1234.fake.ad.ea.com"

        # Act
        snowcache_utils.avalanche_upstream_server_healthy(mock_settings, mock_platform)

        # Assert
        mock_log_warning.call_count == expected_call_count
