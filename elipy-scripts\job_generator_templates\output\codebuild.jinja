{#
    Command:
        codebuild
            short_help: Generate and build the solution.

    Arguments:
        platform
        config

    Required variables:
        code_branch
            required: True
            help: Perforce branch/stream name.
        code_changelist
            required: True
            help: Perforce changelist number.
        p4_port
            required: True
            help: Perforce server address.
        p4_client
            required: True
            help: Perforce workspace name.

    Optional variables:
        p4_user
            default: None
            help: Perforce user name.
        framework_args
            multiple: True
            help: Framework arguments for gensln.
        msbuild_args
            multiple: True
            help: Msbuild arguments for buildsln
        clean
            default: false
            help: Delete TnT/Local if --clean true is passed.
        denuvo_wrapping
            default: False
            help: Do Denuvo wrapping after build.
        denuvo_exclusion
            default: None
            help: Exclusion list for Denuvo.
        mirror
            default: True
            help: Deploy to filer with mirror option.
        artifactory_user
            default: None
            help: Artifactory user for fetching denuvo files.
        artifactory_apikey
            default: None
            help: Artifactory apikey for fetching denuvo files.
        alltests
            default: False
            help: Run alltests build.
        nomaster
            default: False
            help: Run nomaster build.
        wsl
            default: False
            help: Run build in windows subsystem linux.
        dry_run
            default: False
            help: Build code without deploying.
        import_local
            default: False
            help: Imports contents of TnT/Local from filer.
        skip_deploy_tnt
            default: False
            help: Skip deploying TnT (DICE Drone builds req.).
        deploy_tests
            default: False
            help: Deploy test files using Tests_Win64-Dll_release_Files.txt
        deploy_frostedtests
            default: False
            help: Deploy frosted test files using FrostEd.Tests_Win64-Dll_release_Files.txt
        data_directory
            default: None
            help: Which data directory to use for fetching licensee settings.
        skip_symbols_backup
            default: False
            help: Skip backup for symbols.
        skip_symbols_to_symstore
            default: False
            help: Skip uploading symbols to Symstore.
        compress_symbols
            default: True
            help: Compress symbols before uploading them to the symstore.
        strip_symbols
            default: True
            help: Flag for stripping symbols (only applies to linuxserver).
        icepick_test
            default: None
            help: Icepick tests to run.
        override_do_not_run_code_unittests
            default: False
            help: Overrides whether to run the code unit tests.
        ignore_icepick_exit_code
            default: True
            type: bool
            help: Should icepick result be ignored. By ignoring it, the job will succeed when a unit test fails.
        settings_files
            help: Settings files relative to the data folder.
            multiple: True
        licensee
            multiple: True
            default: None
            help: Licensee to use
        password
            default: None
            help: User credentials to authenticate to package server
        email
            default: None
            help: User email to authenticate to package server
        domain_user
            default: None
            help: The user to authenticate to package server as DOMAIN\user
        fake_ooa_wrapped_symbol
            default: False
            help: Fake ooa wrapped symbol for project
        use_state_zip
            default: False
            help: Zip up local state
        use_snowcache
            default: False
            help: Whether to enable Snowcache or not
        snowcache_mode_override
            type: click.Choice(SNOWCACHE_MODES, case_sensitive=False)
            default: ''
            help: Override the logically evaluated snowcache mode with this
        gensln_config
            default: None
            help: Use different config for gensln
        buildlayout_config
            default: None
            help: Use different config in buildlayout file
        icepick_extra_framework_args
            default: None
            help: Extra arguments for Icepick to pass to any Framework commands it starts
        clean_packages
            default: False
            help: Clean the 'packages' and 'LocalPackages' directories.
        is_outsource_build
            default: False
            help: Are we building things for the oursourcers
        only_gensln
            default: False
            type: bool
            help: Should we exit after running gensln
        tool_targets
            multiple: True
            default: ['pipeline', 'frosted', 'win64-dll']
            help: Target tool platform(s) to build.
        custom_tag
            default: None
            help: Extra folder before changelist to fetch code from.
        filer_user
            default: None
            help: username for creating a filer connection
        filer_password
            default: None
            help: password for creating a filer connection
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    codebuild
    platform {{ platform }}
    config {{ config }}
    --code-branch {{ code_branch }}
    --code-changelist {{ code_changelist }}
    --p4-port {{ p4_port }}
    --p4-client {{ p4_client }}
    {%- if p4_user %}
    --p4-user {{ p4_user }}
    {%- endif %}
    {%- if framework_args %}
    --framework-args {{ framework_args }}
    {%- endif %}
    {%- if msbuild_args %}
    --msbuild-args {{ msbuild_args }}
    {%- endif %}
    {%- if clean %}
    --clean {{ clean }}
    {%- endif %}
    {%- if oreans_protection %}
    --oreans-protection {{ oreans_protection }}
    {%- endif %}
    {%- if oreans_config %}
    --oreans-config {{ oreans_config }}
    {%- endif %}
    {%- if denuvo_wrapping %}
    --denuvo-wrapping {{ denuvo_wrapping }}
    {%- endif %}
    {%- if denuvo_exclusion %}
    --denuvo-exclusion {{ denuvo_exclusion }}
    {%- endif %}
    {%- if mirror %}
    --mirror {{ mirror }}
    {%- endif %}
    {%- if artifactory_user %}
    --artifactory-user {{ artifactory_user }}
    {%- endif %}
    {%- if artifactory_apikey %}
    --artifactory-apikey {{ artifactory_apikey }}
    {%- endif %}
    {%- if alltests %}
    --alltests {{ alltests }}
    {%- endif %}
    {%- if nomaster %}
    --nomaster {{ nomaster }}
    {%- endif %}
    {%- if wsl %}
    --wsl {{ wsl }}
    {%- endif %}
    {%- if dry_run %}
    --dry-run {{ dry_run }}
    {%- endif %}
    {%- if import_local %}
    --import-local {{ import_local }}
    {%- endif %}
    {%- if skip_deploy_tnt %}
    --skip-deploy-tnt {{ skip_deploy_tnt }}
    {%- endif %}
    {%- if deploy_tests %}
    --deploy-tests {{ deploy_tests }}
    {%- endif %}
    {%- if deploy_frostedtests %}
    --deploy-frostedtests {{ deploy_frostedtests }}
    {%- endif %}
    {%- if data_directory %}
    --data-directory {{ data_directory }}
    {%- endif %}
    {%- if skip_symbols_backup %}
    --skip-symbols-backup {{ skip_symbols_backup }}
    {%- endif %}
    {%- if skip_symbols_to_symstore %}
    --skip-symbols-to-symstore {{ skip_symbols_to_symstore }}
    {%- endif %}
    {%- if compress_symbols %}
    --compress-symbols {{ compress_symbols }}
    {%- endif %}
    {%- if strip_symbols %}
    --strip-symbols {{ strip_symbols }}
    {%- endif %}
    {%- if icepick_test %}
    --icepick-test {{ icepick_test }}
    {%- endif %}
    {%- if override_do_not_run_code_unittests %}
    --override-do-not-run-code-unittests {{ override_do_not_run_code_unittests }}
    {%- endif %}
    {%- if ignore_icepick_exit_code %}
    --ignore-icepick-exit-code {{ ignore_icepick_exit_code }}
    {%- endif %}
    {%- if settings_files %}
    --settings-files {{ settings_files }}
    {%- endif %}
    {%- if licensee %}
    --licensee {{ licensee }}
    {%- endif %}
    {%- if password %}
    --password {{ password }}
    {%- endif %}
    {%- if email %}
    --email {{ email }}
    {%- endif %}
    {%- if domain_user %}
    --domain-user {{ domain_user }}
    {%- endif %}
    {%- if fake_ooa_wrapped_symbol %}
    --fake-ooa-wrapped-symbol {{ fake_ooa_wrapped_symbol }}
    {%- endif %}
    {%- if use_state_zip %}
    --use-state-zip {{ use_state_zip }}
    {%- endif %}
    {%- if use_snowcache %}
    --use-snowcache {{ use_snowcache }}
    {%- endif %}
    {%- if snowcache_mode_override %}
    --snowcache-mode-override {{ snowcache_mode_override }}
    {%- endif %}
    {%- if gensln_config %}
    --gensln-config {{ gensln_config }}
    {%- endif %}
    {%- if buildlayout_config %}
    --buildlayout-config {{ buildlayout_config }}
    {%- endif %}
    {%- if icepick_extra_framework_args %}
    --icepick-extra-framework-args {{ icepick_extra_framework_args }}
    {%- endif %}
    {%- if clean_packages %}
    --clean-packages {{ clean_packages }}
    {%- endif %}
    {%- if is_outsource_build %}
    --is-outsource-build {{ is_outsource_build }}
    {%- endif %}
    {%- if only_gensln %}
    --only-gensln {{ only_gensln }}
    {%- endif %}
    {%- if tool_targets %}
    --tool-targets {{ tool_targets }}
    {%- endif %}
    {%- if custom_tag %}
    --custom-tag {{ custom_tag }}
    {%- endif %}
    {%- if filer_user %}
    --filer-user {{ filer_user }}
    {%- endif %}
    {%- if filer_password %}
    --filer-password {{ filer_password }}
    {%- endif %}
  displayName: elipy codebuild
