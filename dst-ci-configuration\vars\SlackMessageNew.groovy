import jenkins.model.Jenkins

/**
 * SlackMessageNew.groovy
 * Sends a message to the specified channel(s) for the specified job.
 */
void call(def currentBuild, def slack_settings, def short_name) {
    if (slack_settings == null) { // Skip sending a message if there are no settings defined.
        echo 'No Slack channel defined for this job.'
        return
    }

    // Set general settings
    def domain_name = 'electronic-arts'
    def authentication_creds = [
        bct      : 'bct-slack-token',
        cob      : 'dre_cobra-slack-token',
        dre_cobra: 'dre_cobra-slack-token',
        dst      : 'walrus-slack-token',
        dun      : 'diceupgradenext-slack-token',
        fb1      : 'fb1-slack-token',
        fb1_aws  : 'fb1-slack-token',
        fb1_bfg  : 'fb1-slack-token',
        fbd      : 'fbdev-slack-token',
        gnt      : 'granite-slack-token',
        kin      : 'kingston-slack-token',
        mer      : 'merlin-slack-token',
        nfs      : 'nfsupgrade-slack-token',
        san      : 'santiago-slack-token',
        sta      : 'casablanca-slack-token',
        vik      : 'NOT-SET',
    ]
    def message_color = 'good'
    def report_message = 'temp'

    // Get Slack settings for this job.
    def slack_channels = []
    def always_notify = false
    def skip_for_multiple_failures = false
    if (slack_settings instanceof String) {
        slack_channels = [slack_settings]
    } else if (slack_settings instanceof Map) {
        slack_channels = slack_settings?.channels ?: []
        always_notify = slack_settings?.always_notify ?: false
        skip_for_multiple_failures = slack_settings?.skip_for_multiple_failures ?: false
        report_message = slack_settings?.report_message ?: report_message
        message_color = slack_settings?.message_color ?: message_color
    } else {
        echo 'Invalid format for Slack settings.'
        return
    }

    // Get the result for this job and for the previous job of the same type.
    def build_result = currentBuild.result
    def job_env = currentBuild.rawBuild.getEnvironment(TaskListener.NULL)
    def job_name = job_env?.JOB_NAME
    def last_result = Jenkins.get().getItem(job_name)?.lastCompletedBuild?.result
    def build_name = job_env?.BUILD_DISPLAY_NAME

    if (report_message == 'temp') {
        if (build_result.toString() == 'FAILURE') {
            message_color = 'danger'
            if (last_result.toString() == 'FAILURE') { // In the case of repeated failure
                if (skip_for_multiple_failures == false) {
                    report_message = "${build_name} - job still failing (<${job_env.BUILD_URL}|Open>)"
                } else {
                    return
                }
            } else { // In the case of a new failure
                report_message = "${build_name} - job started to fail (<${job_env.BUILD_URL}|Open>)"
            }
        } else if (build_result.toString() == 'ABORTED') {
            report_message = "${build_name} - job aborted (<${job_env.BUILD_URL}|Open>)"
        } else if (build_result.toString() == 'SUCCESS' && !(last_result.toString() in ['SUCCESS', 'UNSTABLE'])) {
            // In the case of success after repeated failure
            report_message = "${build_name} - job back to normal (<${job_env.BUILD_URL}|Open>)"
        } else {
            if (always_notify.toBoolean() == true) { // For jobs where we report status for all builds
                report_message = "${build_name} - job working (<${job_env.BUILD_URL}|Open>)"
            } else {
                return
            }
        }
    }

    for (channel_name in slack_channels) {
        slackSend(
            channel: channel_name,
            color: message_color,
            message: report_message,
            teamDomain: domain_name,
            tokenCredentialId: authentication_creds[short_name]
        )
    }
}
