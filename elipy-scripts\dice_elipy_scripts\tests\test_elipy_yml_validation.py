"""
test_elipy_yml_validation.py

Test if we can reach machines used for various types of caches.
"""
import pytest
from elipy2.config import ConfigManager
from dice_elipy_scripts.tests.utils.misc import elipy_config_managers, avalanche_connection_test


@pytest.mark.parametrize("config_manager", elipy_config_managers())
class TestElipyYml:
    @pytest.mark.external_dep
    def test_validate_snowcache_is_running(self, config_manager: ConfigManager):
        hosts = list(config_manager.get("snowcache_host", default={}).values())
        avalanche_connection_test(hosts)

    @pytest.mark.external_dep
    def test_validate_recompression_cache_is_running(self, config_manager: ConfigManager):
        hosts = list(config_manager.get("recompression_cache", default={}).values())
        avalanche_connection_test(hosts)

    @pytest.mark.external_dep
    def test_validate_avalanche_state_host_is_running(self, config_manager: ConfigManager):
        hosts = list(config_manager.get("avalanche_state_host", default={}).values())
        avalanche_connection_test(hosts)
