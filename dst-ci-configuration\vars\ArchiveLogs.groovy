/**
 * ArchiveLogs.groovy
 * Archives logs as build artifacts.
 */
void call(Map branchInfo, String logType) {
    // Artifacts to archive.
    List artifactList = []

    if (logType == 'non_build_specfic') {
        // Archive all .log files from 'logs' directories (non build specific logs) after all jobs.
        artifactList = [
            'logs/*.log',
            'TnT/*.log',
            'TnT/FrostyLogFile.txt',
            'logs/MSBuild_*.failure.txt',
        ]
    } else if (logType == 'pipeline') {
        artifactList = [
            branchInfo.dataset + '/.state/' + branchInfo.code_branch + '/log/Pipeline.log'
        ]
    } else {
        throw new IllegalArgumentException('No settings defined for log type: ' + logType + ', aborting.')
    }

    // Run the archiving step.
    for (artifact in artifactList) {
        echo "uploading ${artifact}..."
        archiveArtifacts(
            artifacts: artifact,
            allowEmptyArchive: true,
            fingerprint: false
        )
    }
}
