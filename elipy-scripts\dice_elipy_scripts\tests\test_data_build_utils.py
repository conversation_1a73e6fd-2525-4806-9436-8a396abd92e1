"""
test_data_build_utils.py

Unit testing for data_build_utils
"""
import pytest
from mock import patch
from dice_elipy_scripts.utils.data_build_utils import (
    get_export_compression_args,
    run_expression_debug_data,
)


class TestDataBuildUtils:
    @pytest.fixture(autouse=True)
    def setUp(self):
        from elipy2.data import DataUtils

        self.builder = DataUtils("win64", ["retaillevels"])

    @patch("elipy2.avalanche.get_avalanche_platform_name")
    def test_get_export_compression_args(self, mock_avalanche_name):
        mock_avalanche_name.return_value = "name"
        assert get_export_compression_args("win64") == [
            "-c",
            "{}\\Config\\Frosty\\{}".format("game_data_dir", "CompressionConfig.yaml"),
        ]

    @patch("elipy2.avalanche.get_avalanche_platform_name")
    def test_get_export_compression_args_server(self, mock_avalanche_name):
        mock_avalanche_name.return_value = "dedicatedserver"
        assert get_export_compression_args("server") == [
            "-c",
            "{}\\Config\\Frosty\\{}".format("game_data_dir", "ServerCompressionConfig.yaml"),
        ]

    @patch("elipy2.core.robocopy")
    @patch("elipy2.filer_paths.get_expression_debug_data_path")
    @patch("elipy2.local_paths.get_local_expressiondebug_path")
    @patch("elipy2.data.DataUtils.extract_expression_debugdata")
    def test_run_expression_debug_data(
        self,
        mock_expression,
        mock_get_local_path,
        mock_get_filer_path,
        mock_robocopy,
        fixture_metadata_manager,
    ):
        mock_get_local_path.return_value = "local"
        mock_get_filer_path.return_value = "filer"
        code_changelist = "123"
        data_changelist = "123"
        code_branch = "code"
        data_branch = "data"
        platform = "win"
        builder_instance = self.builder
        run_expression_debug_data(
            code_changelist=code_changelist,
            data_changelist=data_changelist,
            code_branch=code_branch,
            data_branch=data_branch,
            platform=platform,
            builder_instance=builder_instance,
        )

        assert mock_robocopy.call_count == 1
        assert mock_expression.call_count == 1
        assert fixture_metadata_manager.register_expression_debug_data.call_count == 1
        mock_robocopy.assert_called_once_with(source="local", dest="filer")

    @patch("elipy2.core.robocopy")
    @patch("elipy2.filer_paths.get_expression_debug_data_path")
    @patch("elipy2.local_paths.get_local_expressiondebug_path")
    @patch("elipy2.data.DataUtils.extract_expression_debugdata")
    def test_run_expression_debug_data_no_builder(
        self,
        mock_expression,
        mock_get_local_path,
        mock_get_filer_path,
        mock_robocopy,
        fixture_metadata_manager,
    ):
        mock_get_local_path.return_value = "local"
        mock_get_filer_path.return_value = "filer"
        code_changelist = "123"
        data_changelist = "123"
        code_branch = "code"
        data_branch = "data"
        platform = "win"
        run_expression_debug_data(
            code_changelist=code_changelist,
            data_changelist=data_changelist,
            code_branch=code_branch,
            data_branch=data_branch,
            platform=platform,
            builder_instance=None,
        )

        assert mock_robocopy.call_count == 1
        assert mock_expression.call_count == 0
        assert fixture_metadata_manager.register_expression_debug_data.call_count == 0
        mock_robocopy.assert_called_once_with(source="local", dest="filer")
