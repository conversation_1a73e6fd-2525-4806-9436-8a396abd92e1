   outsource_package_scheduler.run()
      outsource_package_scheduler.ProjectClass(Bct)
      outsource_package_scheduler.pipeline(groovy.lang.Closure)
         outsource_package_scheduler.allowBrokenBuildClaiming()
         outsource_package_scheduler.timestamps()
         outsource_package_scheduler.echo(Executing on agent [label:any])
         outsource_package_scheduler.stage(Trigger outsource package job., groovy.lang.Closure)
            outsource_package_scheduler.script(groovy.lang.Closure)
               GetBranchFile.get_branchfile(Bct, build-outsourcer-code)
               LibJenkins.getLastStableCodeChangelist(build-main.code.start)
               LibJenkins.getLastStableCodeChangelist(build-outsourcer-code.outsource-package.start)
               outsource_package_scheduler.string({name=code_changelist, value=1234})
               outsource_package_scheduler.string({name=clean_local, value=true})
               outsource_package_scheduler.EnvInject({absoluteUrl=http://example.com/dummy, buildVariables={}, changeSets=[], currentResult=SUCCESS, description=dummy, displayName=#1, duration=1, durationString=1 ms, fullDisplayName=dummy #1, fullProjectName=dummy, id=1, keepLog=false, nextBuild=null, number=1, previousBuild=null, projectName=dummy, result=SUCCESS, startTimeInMillis=1, timeInMillis=1, upstreamBuilds=[]}, {code_changelist=1234})
               outsource_package_scheduler.build({job=build-outsourcer-code.outsource-package.build, parameters=[{name=code_changelist, value=1234}, {name=clean_local, value=true}], propagate=false})
               outsource_package_scheduler.SlackMessageNew({absoluteUrl=http://example.com/dummy, buildVariables={}, changeSets=[], currentResult=SUCCESS, description=dummy, displayName=build-outsourcer-code.outsource-package.build.1234, duration=1, durationString=1 ms, fullDisplayName=dummy #1, fullProjectName=dummy, id=1, keepLog=false, nextBuild=null, number=1, previousBuild=null, projectName=dummy, result=SUCCESS, startTimeInMillis=1, timeInMillis=1, upstreamBuilds=[]}, #test-channel, exc)
               outsource_package_scheduler.DownstreamErrorReporting({absoluteUrl=http://example.com/dummy, buildVariables={}, changeSets=[], currentResult=SUCCESS, description=dummy, displayName=build-outsourcer-code.outsource-package.build.1234, duration=1, durationString=1 ms, fullDisplayName=dummy #1, fullProjectName=dummy, id=1, keepLog=false, nextBuild=null, number=1, previousBuild=null, projectName=dummy, result=SUCCESS, startTimeInMillis=1, timeInMillis=1, upstreamBuilds=[]})
