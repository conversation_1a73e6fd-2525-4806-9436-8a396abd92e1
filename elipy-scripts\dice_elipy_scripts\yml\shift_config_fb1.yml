# Duplicated settings lower in the settings list will get higher priority
buildtype: "QA"
milestone: "Production"
distribution_type: "InternalOnly"
retention_policy: "SpaceAvailable"
priority: ""
version: "2.0"

xbsx:
  content:
    files:
      file_names:
        - "builtLevels.json"
        - "*.buildlayout"
        - "*.BuildSettings"
        - "Logo.png"
        - "*.dll"
        - "SmallLogo.png"
        - "SplashScreen.png"
        - "StoreLogo.png"
        - "WideLogo.png"
        - "*.Main_Xbsx_*.exe"
        - "MicrosoftGame.config"
        - "gameos.xvd"
        - "nsal.json"
        - "package.mft"
        - "resources.pri"
        - "build.json"
        - "Elipy_MD5_hashes.csv"
      supplemental_files:
        - "*.xml"
      directory:
        - "Config"
        - "Data"
        - "Scripts"
    digital:
      file_names:
        - "*appxmanifest.xml"
        - "*Main_Xbsx_*.pdb"
        - "MicrosoftGame.config"
        - "*neutral__*"
        - "*.ekb"
      supplemental_files:
        - "build.json"
      directory:
        - ""
    patch:
      file_names:
        - "*appxmanifest.xml"
        - "*Main_Xbsx_*.pdb"
        - "MicrosoftGame.config"
        - "*neutral__*"
        - "*.ekb"
      supplemental_files:
        - "build.json"
      directory:
        - ""
  settings:
    dev-na-dice-next-build-data:
      final:
        files:
          sku_id: "eb955026-df32-404d-bbc4-b36d62146f50"
          sku_name: "FG - WW (dev-na xbsx files final)"

ps5:
  content:
    files:
      file_names:
        - "builtLevels.json"
        - "build.json"
        - "*.prx"
        - "*.elf"
        - "*.buildlayout"
        - "*.BuildSettings"
        - "eboot.bin"
        - "Elipy_MD5_hashes.csv"
      supplemental_files:
        - "*.xml"
      directory:
        - "Config"
        - "data"
        - "Scripts"
        - "sce_sys"
    digital: #TBC
      file_names:
        - "*.pkg"
      supplemental_files:
        - "chunks*.gp5"
        - "build.json"
      directory:
        - ""
    patch: #TBC
      file_names:
        - "*.pkg"
      supplemental_files:
        - "chunks*.gp5"
        - "build.json"
      directory:
        - ""
  settings:
    dev-na-dice-next-build-data:
      final:
        files:
          sku_id: "2a21d0b5-a39f-4fe6-9b4b-ce16a32cf49a"
          sku_name: "FG - EU (dev-na ps5 loose files)"

server:
  content:
    files:
      file_names:
        - "builtLevels.json"
        - "build.json"
        - "*.buildlayout"
        - "*.exe"
        - "*.dll"
        - "*.BuildSettings"
        - "Elipy_MD5_hashes.csv"
      supplemental_files:
        - "*.xml"
      directory:
        - "Config"
        - "Data"
        - "Scripts"
  settings:
    dev-na-dice-next-build-data:
      final:
        files:
          sku_id: "a86f9072-2426-43ef-9a29-2b7aebffe86c"
          sku_name: "Server - WW (dev-na server files final)"

win64:
  content:
    files:
      file_names:
        - "builtLevels.json"
        - "build.json"
        - "*.BuildSettings"
        - "*.buildlayout"
        - "*.Main_Win64_*.exe"
        - "*.dll"
        - "Elipy_MD5_hashes.csv"
      supplemental_files:
        - "*.xml"
      directory:
        - "Config"
        - "Data"
        - "Scripts"
    digital:
      file_names:
        - "*.zip"
      supplemental_files:
        - "build.json"
        - "*.Main_*_Retail.exe"
      directory:
        - ""
    patch:
      file_names:
        - "*.zip"
      supplemental_files:
        - "build.json"
        - "*.Main_*_Retail.exe"
      directory:
        - ""
  settings:
    dev-na-dice-next-build-data:
      final:
        files:
          sku_id: "71b208a6-5086-40e0-a2e2-f9f143527b83"
          sku_name: "FG - WW (dev-na win64 loose files)"

offsite_drone:
  content:
    file_names:
      - '*'
    supplemental_files:
      - ""
  settings:
    dev-na-dice-next-build:
      sku_id: "5f4bbf17-e203-4f12-920a-af3d2ba95ed9"
      sku_name: "Tool - WW (dev-na drone-build)"
