import com.ea.lib.jobsettings.GitLabRepoSettings
import spock.lang.Specification

class GitLabRepoSettingsSpec extends Specification {

    class MasterFile {
        static Map MAINTENANCE_SETTINGS = [:]
    }

    class MasterFileOverrides {
        static Map MAINTENANCE_SETTINGS = [
            JENKINS_REGISTRY_EMAIL         : 'custom-email',
            JENKINS_REGISTRY_JOB_LABEL     : 'custom-label',
            JENKINS_REGISTRY_BRANCH        : 'custom-branch',
            JENKINS_REGISTRY_REPO_URL      : 'custom-url',
            JENKINS_REGISTRY_TRIGGER_STRING: 'custom-trigger-string',
            IS_PRODUCTION                  : false,
        ]
    }

    void "test that we get expected job settings in initializeJenkinsRegistryJob"() {
        when:
        GitLabRepoSettings settings = new GitLabRepoSettings()
        settings.initializeJenkinsRegistryJob(MasterFile)
        then:
        with(settings) {
            description == 'Running the Jenkinsfile the Jenkins Registry repository'
            email == '<EMAIL>'
            jobLabel == 'master'
            repoBranch == 'main'
            repoUrl == '*****************:DRE/JenkinsCI/jenkins_registry.git'
            triggerString == 'H H * * *'
            isProduction == true
        }
    }

    void "test that we get expected job override settings in initializeJenkinsRegistryJob"() {
        when:
        GitLabRepoSettings settings = new GitLabRepoSettings()
        settings.initializeJenkinsRegistryJob(MasterFileOverrides)
        then:
        with(settings) {
            description == 'Running the Jenkinsfile the Jenkins Registry repository'
            email == MasterFileOverrides.MAINTENANCE_SETTINGS.JENKINS_REGISTRY_EMAIL
            jobLabel == MasterFileOverrides.MAINTENANCE_SETTINGS.JENKINS_REGISTRY_JOB_LABEL
            repoBranch == MasterFileOverrides.MAINTENANCE_SETTINGS.JENKINS_REGISTRY_BRANCH
            repoUrl == MasterFileOverrides.MAINTENANCE_SETTINGS.JENKINS_REGISTRY_REPO_URL
            triggerString == MasterFileOverrides.MAINTENANCE_SETTINGS.JENKINS_REGISTRY_TRIGGER_STRING
            isProduction == MasterFileOverrides.MAINTENANCE_SETTINGS.IS_PRODUCTION
        }
    }
}
