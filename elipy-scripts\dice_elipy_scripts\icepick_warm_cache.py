# pylint: disable=line-too-long
"""
icepick_warm_cache.py

DOCUMENATION:
 - Flow Overview: https://docs.google.com/document/d/1pTxwXhm0T5Mstbg4uaBkjWruC5ntz7pZxqGsmBEcTwk/preview
 - Jenkins Config: https://docs.google.com/document/d/1mTfOtPPX93529M7EgmmaGBpmcuMoDyhZ7ZlYCKoVUzw/preview
"""
# pylint: enable=line-too-long
import os
import click
import time
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from dice_elipy_scripts.utils.icepick_utils import icepick_clean
from elipy2 import (
    filer,
    data,
    SETTINGS,
    core,
    LOGGER,
    avalanche,
    windows_tools,
    build_metadata_utils,
    frostbite_core,
)
from elipy2.frostbite import icepick
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


# pylint: disable=too-many-locals
@click.command(
    "icepick_warm_cache",
    short_help="Run icepick cook and store the avalanche db for future use.",
    context_settings=dict(ignore_unknown_options=True),
)
@click.option("--platform", required=True)
@click.option("--category-name", required=True)
@click.option("--tests", required=True, multiple=True)
@click.option("--fully-qualified-paths", is_flag=True, help="test names should be fully qualified")
@click.option("--code-branch", required=True, help="Branch to fetch code from.")
@click.option("--code-changelist", required=True, help="Which code changelist to use.")
@click.option("--data-branch", help="Branch to fetch Avalanche state from.", required=True)
@click.option("--data-changelist", help="Which data changelist to use.", required=True)
@click.option(
    "--datadir",
    default="data",
    help="Specify which data directory to use (relative to GAME_ROOT).",
)
@click.option("--inert-run", is_flag=True, help="Run but don't do anything")
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    platform,
    category_name,
    tests,
    fully_qualified_paths,
    code_changelist,
    data_changelist,
    code_branch,
    data_branch,
    datadir,
    inert_run,
):
    """
    Run icepick cook and store the avalanche db for future use.
    """
    if inert_run:
        LOGGER.info("Inert run, sleep for 3 seconds and exit...")
        time.sleep(3)
        return

    # adding sentry tags
    add_sentry_tags(__file__, "autotest")

    metadata_manager = build_metadata_utils.setup_metadata_manager()

    _filer = filer.FilerUtils()
    icepicker = icepick.IcepickUtils()
    icepick_clean(icepicker)

    tests = list(tests)
    if fully_qualified_paths:
        tests = [os.path.join(frostbite_core.get_game_data_dir(), test) for test in tests]

    builder = data.DataUtils(platform, tests, monkey_build_label=data_changelist)
    builder.set_datadir(datadir)

    extra_args = []

    # Fetch pipeline binary
    _filer.fetch_code(code_branch, code_changelist, "pipeline", "release")

    icepick_cook_args = extra_args + [frostbite_core.get_emit_arg()]
    icepicker.run_icepick_cook(platform, tests, None, icepick_cook_args)

    source_db = avalanche.get_full_database_name(platform)
    dest_db = avalanche.get_autotest_temp_db_name(
        platform, category_name, data_changelist, code_changelist
    )

    dest_host = SETTINGS.get("avalanche_state_host")[platform]
    avalanche.remote_clone_db(
        source_db=source_db,
        dest_db=dest_db,
        source_host=windows_tools.get_computer_name(),
        dest_host=dest_host,
        branch=data_branch,
        limited_lifetime=True,
        push_built_levels=False,
        complete_clone=True,
    )
    if core.use_bilbo():
        metadata_manager.register_autotest_state(
            path=dest_db,
            data_changelist=data_changelist,
            code_changelist=code_changelist,
            branch=data_branch,
            platform=platform,
            remote_host=[dest_host],
        )
