{#
    Command:
        pre_preflight
            short_help: Performs a pre preflight maintenance run.

    Arguments:

    Required variables:
        platform
            help: Platform to run pre preflight on.
            required: True
        datadir
            help: Which datadir to build from.
            required: True
        p4_client
            default: None
            help: Which p4 client to use.
            required: True
        p4_user
            default: None
            help: Which p4 user is being used.
            required: True
        p4_port
            default: None
            help: Which p4 port to be used.
            required: True

    Optional variables:
        code_changelist
            default: None
            help: Code changelist to run pre preflight on.
        code_branch
            default: game-dev
            help: Perforce branch for code.
        asset
            help: Asset to be cooked.
            default: ['preflightlevels']
            multiple: True
        server_asset
            help: Server asset to be cooked.
            default: ['PreflightServerAssets.dbx']
            multiple: False
        data_changelist
            default: None
            help: Data changelist to run pre preflight on.
        data_branch
            default: None
            help: Perforce branch for data.
        clean_master_version_check
            is_flag: True
            help: Run clean on master version update.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    pre_preflight
    --platform {{ platform }}
    --datadir {{ datadir }}
    --p4-client {{ p4_client }}
    --p4-user {{ p4_user }}
    --p4-port {{ p4_port }}
    {%- if code_changelist %}
    --code-changelist {{ code_changelist }}
    {%- endif %}
    {%- if code_branch %}
    --code-branch {{ code_branch }}
    {%- endif %}
    {%- if asset %}
    --asset {{ asset }}
    {%- endif %}
    {%- if server_asset %}
    --server-asset {{ server_asset }}
    {%- endif %}
    {%- if data_changelist %}
    --data-changelist {{ data_changelist }}
    {%- endif %}
    {%- if data_branch %}
    --data-branch {{ data_branch }}
    {%- endif %}
    {%- if clean_master_version_check %}
    --clean-master-version-check {{ clean_master_version_check }}
    {%- endif %}
  displayName: elipy pre_preflight
