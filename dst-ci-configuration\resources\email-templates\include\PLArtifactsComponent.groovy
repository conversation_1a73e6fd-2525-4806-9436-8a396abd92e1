import groovy.xml.MarkupBuilder
import hudson.model.Run

public class PLArtifactsComponent implements IEmailComponent {

    private List artifactClasses
    private ClassLoader classLoader

    public PLArtifactsComponent(ClassLoader classLoader, List artifactClasses) {
        this.classLoader = classLoader
        this.artifactClasses = artifactClasses
    }


    private Map gather(Run run) {
        def artifacts = []
        for (String artifactClass : artifactClasses) {
            artifacts.addAll(classLoader.parseClass(new File(artifactClass)).newInstance().gatherArtifacts(run))
        }

        def data = [:]
        if (artifacts) {
            data.Artifacts = artifacts
        }

        return data
    }


    private String getSectionClass(Map data) {
        return "section"
    }


    public void render(Run run, MarkupBuilder builder) {
        def data = gather(run)
        if (data) {
            builder.tr {
                td(class: getSectionClass(data), align: "center") {
                    mkp.yieldUnescaped("<!-- ARTIFACTS -->")
                    table(border: "0", cellpadding: "0", cellspacing: "0", width: "100%", style: "max-width: 500px;", class: "responsive-table") {
                        tr {
                            td(class: "section-title") {
                                mkp.yield("Artifacts")
                            }
                        }
                    }
                    table(border: "0", cellpadding: "0", cellspacing: "0", width: "100%", style: "max-width: 500px;", class: "responsive-table") {
                        tr {
                            td(class: "artifacts-header-name") {
                                mkp.yield("NAME")
                            }
                        }
                        // list each artifact as a new table row
                        def artifacts = data.Artifacts
                        if (artifacts) {
                            artifacts.each { artifact ->
                                tr {
                                    td(class: "artifact") {
                                        a(href: artifact.getUrl(), class: "artifact") {
                                            mkp.yield(artifact.getName())
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }


    public boolean isApplicable(Run run) {
        // TODO check if there are any artifacts / build metadata?
        return true
    }


    public String getEmbeddedStyle(Run run) {
        return """
                    /* ARTIFACT SPECIFIC STYLES */

                    td.artifacts-header-name {
                        border: 1px solid #C8C8C8;
                        color: #9A9fA8;
                        font-family: "LatoLatinWeb", "Lato", "Helvetica Neue", Helvetica, Arial, sans-serif;
                        font-size: 12px;
                        font-weight: bold;
                        padding: 10px;
                        text-align: left;
                    }
                    td.artifact {
                        border: 1px solid #C8C8C8;
                        padding: 10px;
                        text-align: left;
                        vertical-align: top;
                    }
                    a.artifact {
                        color: #4A90E2;
                        font-family: "LatoLatinWeb", "Lato", "Helvetica Neue", Helvetica, Arial, sans-serif;
                        font-size: 12px;
                        font-weight: bold;
                        text-decoration: none;
                    }
        """
    }
}
