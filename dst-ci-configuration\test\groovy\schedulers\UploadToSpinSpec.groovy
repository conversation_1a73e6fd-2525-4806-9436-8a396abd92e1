package schedulers

import spock.lang.Unroll
import support.DeclarativePipelineSpockTest

@Unroll
class UploadToSpinSpec extends DeclarativePipelineSpockTest {
    final String scriptName = 'UploadToSpin.groovy'
    final String codeChangelist = '123'

    void setup() {
        binding.setVariable('env', [
            VAULT_CREDENTIALS : 'cred',
            VAULT_VARIABLE    : 'ENV_VAR',
            WORKSPACE_ROOT    : '/',
            code_branch       : 'code_branch',
            code_folder       : 'folder',
            data_branch       : 'data_branch',
            elipy_call        : 'elipy',
            elipy_install_call: 'install',
            node_label        : 'test-label',
            platform          : 'linuxserver',
            format            : 'digital',
            config            : 'final',
            region            : 'ww',
        ])
        binding.setVariable('params', [
            code_changelist: codeChangelist,
        ])
        helper.with {
            registerAllowedMethod('get_branchfile', [String, String]) {
                return [
                    standard_jobs_settings: [
                        slack_channel_spin: '#test-channel'
                    ],
                ]
            }
        }
        helper.registerAllowedMethod('withVault', [Map, Closure]) { Map config, Closure closure ->
            closure.call()
        }
    }

    void 'verify callstack'() {
        when:
        runScript(scriptName)
        printCallStack()

        then:
        assertJobStatusSuccess()
        testNonRegression()
    }

    void 'test setting data_changelist to "#dataChangelist"'() {
        when:
        if (dataChangelist) {
            binding.setVariable('params', binding.getVariable('params') + [
                data_changelist: dataChangelist
            ])
        }

        runScript(scriptName)

        then:
        helper.callStack.findAll { it.methodName == 'bat' && (it.args[0] =~ codeChangelist).size() == hits }.size() == 1

        where:
        dataChangelist || hits
        ''             || 2
        '456'          || 1
    }
}
