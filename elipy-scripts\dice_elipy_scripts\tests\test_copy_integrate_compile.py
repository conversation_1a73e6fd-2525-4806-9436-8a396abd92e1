"""
test_copy_integrate_compile.py

Unit testing for copy_integrate_compile
"""
import unittest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import patch, MagicMock
from dice_elipy_scripts.copy_integrate_compile import cli


@patch("elipy2.core.clean_temp", MagicMock())
@patch("elipy2.core.close_file_handles", MagicMock())
@patch("elipy2.running_processes.kill", Magic<PERSON>ock())
@patch("elipy2.telemetry.upload_metrics", MagicMock())
@patch("dice_elipy_scripts.copy_integrate_compile.add_sentry_tags", MagicMock())
class TestCopyIntegrateCompile(unittest.TestCase):
    OPTION_CHANGELIST = "--changelist"
    OPTION_CLEAN = "--clean"
    OPTION_COPY_MAPPING = "--copy-mapping"
    OPTION_DATA_DIRECTORY = "--data-directory"
    OPTION_EMAIL = "--email"
    OPTION_DOMAIN_USER = "--domain-user"
    OPTION_FRAMEWORK_ARGS = "--framework-args"
    OPTION_IGNORE_SOURCE_HISTORY = "--ignore-source-history"
    OPTION_INTEGRATE_MAPPING = "--integrate-mapping"
    OPTION_LICENSEE = "--licensee"
    OPTION_NO_SUBMIT = "--no-submit"
    OPTION_P4_CLIENT = "--p4-client"
    OPTION_P4_PORT = "--p4-port"
    OPTION_P4_USER = "--p4-user"
    OPTION_PASSWORD = "--password"
    OPTION_SHELVE_CL = "--shelve-cl"
    OPTION_SOURCE_BRANCH = "--source-branch"
    OPTION_SUBMIT_MESSAGE = "--submit-message"
    OPTION_USE_SNOWCACHE = "--use-snowcache"
    OPTION_SNOWCACHE_MODE_OVERRIDE = "--snowcache-mode-override"

    VALUE_CHANGELIST = "1234"
    VALUE_CLEAN = "true"
    VALUE_COPY_MAPPING = "copy_mapping"
    VALUE_DATA_DIRECTORY = "data_directory"
    VALUE_EMAIL = "email_pkg"
    VALUE_DOMAIN_USER = "domain_user"
    VALUE_FRAMEWORK_ARGS = "arg1"
    VALUE_INTEGRATE_MAPPING = "integrate_mapping"
    VALUE_LICENSEE = "licensee"
    VALUE_P4_CLIENT = "p4_client"
    VALUE_P4_PORT = "p4_port"
    VALUE_P4_USER = "p4_user"
    VALUE_PASSWORD = "password_pkg"
    VALUE_SOURCE_BRANCH = "source_branch"
    VALUE_SUBMIT_MESSAGE = "submit_message"
    VALUE_SNOWCACHE_MODE_OVERRIDE = "upload"

    DEFAULT_ARGS = [
        OPTION_CHANGELIST,
        VALUE_CHANGELIST,
        OPTION_COPY_MAPPING,
        VALUE_COPY_MAPPING,
        OPTION_INTEGRATE_MAPPING,
        VALUE_INTEGRATE_MAPPING,
        OPTION_P4_CLIENT,
        VALUE_P4_CLIENT,
        OPTION_P4_PORT,
        VALUE_P4_PORT,
        OPTION_SOURCE_BRANCH,
        VALUE_SOURCE_BRANCH,
    ]

    def setUp(self):
        self.patcher_set_datadir = patch("elipy2.data.DataUtils.set_datadir")
        self.mock_set_datadir = self.patcher_set_datadir.start()

        self.patcher_filerutils = patch("elipy2.filer.FilerUtils")
        self.mock_filerutils = self.patcher_filerutils.start()
        self.mock_filerutils.return_value = MagicMock()

        self.patcher_p4utils = patch("elipy2.p4.P4Utils")
        self.mock_p4utils = self.patcher_p4utils.start()
        self.mock_p4utils.return_value = MagicMock()
        self.mock_p4utils.return_value.unresolved.return_value = []
        self.mock_p4utils.return_value.latest_pending_changelist.return_value = None

        self.patcher_compile_code = patch("dice_elipy_scripts.copy_integrate_compile.compile_code")
        self.mock_compile_code = self.patcher_compile_code.start()

        self.patcher_submit_integration = patch(
            "dice_elipy_scripts.copy_integrate_compile.submit_integration"
        )
        self.mock_submit_integration = self.patcher_submit_integration.start()

    def tearDown(self):
        patch.stopall()

    def test_basic_run(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 0
        assert self.mock_p4utils.return_value.revert.call_count == 2

    def test_compile_code_default(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 0
        self.mock_compile_code.assert_called_once_with(
            licensee=[],
            password=None,
            email=None,
            domain_user=None,
            framework_args=[],
            overwrite_p4config=True,
            clean=False,
            use_snowcache=False,
            snowcache_mode_override="",
        )

    def test_compile_code_licensee(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS + [self.OPTION_LICENSEE, self.VALUE_LICENSEE])
        assert result.exit_code == 0
        self.mock_compile_code.assert_called_once_with(
            licensee=[self.VALUE_LICENSEE],
            password=None,
            email=None,
            domain_user=None,
            framework_args=[],
            overwrite_p4config=True,
            clean=False,
            use_snowcache=False,
            snowcache_mode_override="",
        )

    def test_compile_code_email_password(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_ARGS
            + [self.OPTION_EMAIL, self.VALUE_EMAIL, self.OPTION_PASSWORD, self.VALUE_PASSWORD],
        )
        assert result.exit_code == 0
        self.mock_compile_code.assert_called_once_with(
            licensee=[],
            password=self.VALUE_PASSWORD,
            email=self.VALUE_EMAIL,
            domain_user=None,
            framework_args=[],
            overwrite_p4config=True,
            clean=False,
            use_snowcache=False,
            snowcache_mode_override="",
        )

    def test_compile_code_domain_user(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.DEFAULT_ARGS + [self.OPTION_DOMAIN_USER, self.VALUE_DOMAIN_USER]
        )
        assert result.exit_code == 0
        self.mock_compile_code.assert_called_once_with(
            licensee=[],
            password=None,
            email=None,
            domain_user=self.VALUE_DOMAIN_USER,
            framework_args=[],
            overwrite_p4config=True,
            clean=False,
            use_snowcache=False,
            snowcache_mode_override="",
        )

    def test_compile_code_framework_args(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.DEFAULT_ARGS + [self.OPTION_FRAMEWORK_ARGS, self.VALUE_FRAMEWORK_ARGS]
        )
        assert result.exit_code == 0
        self.mock_compile_code.assert_called_once_with(
            licensee=[],
            password=None,
            email=None,
            domain_user=None,
            framework_args=[self.VALUE_FRAMEWORK_ARGS],
            overwrite_p4config=True,
            clean=False,
            use_snowcache=False,
            snowcache_mode_override="",
        )

    def test_compile_code_clean(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS + [self.OPTION_CLEAN, self.VALUE_CLEAN])
        assert result.exit_code == 0
        self.mock_compile_code.assert_called_once_with(
            licensee=[],
            password=None,
            email=None,
            domain_user=None,
            framework_args=[],
            overwrite_p4config=True,
            clean=True,
            use_snowcache=False,
            snowcache_mode_override="",
        )

    def test_compile_code_snowcache(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_ARGS
            + [
                self.OPTION_USE_SNOWCACHE,
                self.OPTION_SNOWCACHE_MODE_OVERRIDE,
                self.VALUE_SNOWCACHE_MODE_OVERRIDE,
            ],
        )
        assert result.exit_code == 0
        self.mock_compile_code.assert_called_once_with(
            licensee=[],
            password=None,
            email=None,
            domain_user=None,
            framework_args=[],
            overwrite_p4config=True,
            clean=False,
            use_snowcache=True,
            snowcache_mode_override=self.VALUE_SNOWCACHE_MODE_OVERRIDE,
        )

    def test_data_directory(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.DEFAULT_ARGS + [self.OPTION_DATA_DIRECTORY, self.VALUE_DATA_DIRECTORY]
        )
        assert result.exit_code == 0
        self.mock_set_datadir.assert_called_once_with("data_directory")

    def test_copy_mapping(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 0
        self.mock_p4utils.return_value.copy_mapping.assert_called_once_with(
            mapping="copy_mapping", to_revision="1234"
        )

    def test_integrate(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 0
        self.mock_p4utils.return_value.integrate.assert_called_once_with(
            mapping="integrate_mapping", to_revision="1234", ignore_source_history=False
        )

    def test_integrate_ignore_source_history(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS + [self.OPTION_IGNORE_SOURCE_HISTORY])
        assert result.exit_code == 0
        self.mock_p4utils.return_value.integrate.assert_called_once_with(
            mapping="integrate_mapping", to_revision="1234", ignore_source_history=True
        )

    def test_unresolved(self):
        self.mock_p4utils.return_value.unresolved.return_value = ["file1"]
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 1
        assert self.mock_p4utils.return_value.revert.call_count == 2

    def test_unresolved_shelve_no_pending(self):
        self.mock_p4utils.return_value.unresolved.return_value = ["file1"]
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS + [self.OPTION_SHELVE_CL])
        assert result.exit_code == 1
        self.mock_p4utils.return_value.latest_pending_changelist.assert_called_once_with()
        assert self.mock_p4utils.return_value.set_description.call_count == 0
        assert self.mock_p4utils.return_value.shelve.call_count == 0

    def test_unresolved_shelve_pending(self):
        self.mock_p4utils.return_value.unresolved.return_value = ["file1"]
        self.mock_p4utils.return_value.latest_pending_changelist.return_value = "5678"
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS + [self.OPTION_SHELVE_CL])
        assert result.exit_code == 1
        self.mock_p4utils.return_value.latest_pending_changelist.assert_called_once_with()
        self.mock_p4utils.return_value.set_description.assert_called_once_with(
            "5678", "Shelved changelist from failed integration."
        )
        self.mock_p4utils.return_value.shelve.assert_called_once_with("5678", discard=False)

    def test_submit_with_message(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.DEFAULT_ARGS + [self.OPTION_SUBMIT_MESSAGE, self.VALUE_SUBMIT_MESSAGE]
        )
        assert result.exit_code == 0
        self.mock_submit_integration.assert_called_once_with(
            p4_object=self.mock_p4utils.return_value,
            submit_message="Copied and integrated from source_branch@1234.\nsubmit_message",
            submit=True,
        )

    def test_no_submit(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS + [self.OPTION_NO_SUBMIT])
        assert result.exit_code == 0
        self.mock_submit_integration.assert_called_once_with(
            p4_object=self.mock_p4utils.return_value,
            submit_message="Copied and integrated from source_branch@1234.",
            submit=False,
        )
