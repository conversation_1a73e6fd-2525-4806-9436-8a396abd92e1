package com.ea.lib.jobs

import com.ea.lib.LibCommonCps
import com.ea.lib.LibJobDsl
import com.ea.lib.jobsettings.MaintenanceSettings
import javaposse.jobdsl.dsl.DslFactory
import javaposse.jobdsl.dsl.Job

class LibMaintenance {
    /**
     * Adds generic job parameters for Avalanche maintenance start jobs.
     */
    static void avalanche_maintenance_start(def job, def project, def branchFile, def masterFile, String branchName) {
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeAvalancheMaintenanceStart(branchFile, masterFile, project, branchName)
        job.with {
            description(settings.description)
            disabled(settings.isDisabled)
            properties {
                pipelineTriggers {
                    triggers {
                        cron {
                            spec('H 5 * * 1-5')  /* only run Mon-Fri*/
                        }
                    }
                }
            }
            environmentVariables {
                env('project_name', settings.projectName)
            }
        }
    }

    /**
     * Adds generic job parameters for Avalanche maintenance build jobs.
     */
    static void avalanche_maintenance_job(def job, def branchFile, def project, def masterFile, String branchName, Boolean isAzure = false) {
        MaintenanceSettings settings = new MaintenanceSettings()
        if (isAzure) {
            settings.initializeAvalancheMaintenanceAzureJob(branchFile, masterFile, project, branchName)
        } else {
            settings.initializeAvalancheMaintenanceJob(branchFile, masterFile, project, branchName)
        }
        job.with {
            description(settings.description)
            logRotator(7, 100)
            customWorkspace(settings.workspaceRoot)
            concurrentBuild()
            properties {
                groovyLabelAssignmentProperty {
                    secureGroovyScript {
                        script('return binding.getVariables().get("Node")')
                        sandbox(true)
                    }
                }
            }
            parameters {
                stringParam {
                    name('Node')
                    defaultValue('')
                    description('Name of the node to run the script on.')
                    trim(true)
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(settings.buildName)
                timeout {
                    absolute(300)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                batchFile(settings.elipyCmd)
            }
        }
    }

    /**
     * Adds generic job parameters for running p4 clean code stream.
     */
    static void p4_clean_codestream_job(def job, def project, def branchInfo, String p4_port) {
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeP4CleanCodestreamJob(project, branchInfo, p4_port)
        job.with {
            description(settings.description)
            logRotator(7, 100)
            customWorkspace(settings.workspaceRoot)
            concurrentBuild()
            wrappers {
                timestamps()
                buildName(settings.buildName)
            }
            throttleConcurrentBuilds {
                maxTotal(4)
            }
            properties {
                groovyLabelAssignmentProperty {
                    secureGroovyScript {
                        script('return binding.getVariables().get("Node")')
                        sandbox(true)
                    }
                }
            }
            parameters {
                nodeParam('Node') {
                    description('select which machine to run p4 clean')
                    trigger('allowMultiSelectionForConcurrentBuilds')
                    eligibility('IgnoreOfflineNodeEligibility')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                batchFile(settings.elipyCmd)
            }
        }
    }

    /**
     * Adds generic job parameters for running p4 clean data stream.
     */
    static void p4_clean_datastream_job(def job, def project, Map branchInfo, String p4Port, String dataset) {
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeP4CleanDatastreamJob(project, branchInfo, p4Port, dataset)
        job.with {
            description(settings.description)
            logRotator(7, 100)
            customWorkspace(settings.workspaceRoot)
            concurrentBuild()
            wrappers {
                timestamps()
                buildName(settings.buildName)
            }
            throttleConcurrentBuilds {
                maxTotal(12)
            }
            properties {
                groovyLabelAssignmentProperty {
                    secureGroovyScript {
                        script('return binding.getVariables().get("Node")')
                        sandbox(true)
                    }
                }
            }
            parameters {
                nodeParam('Node') {
                    description('select which machine to run p4 clean')
                    trigger('allowMultiSelectionForConcurrentBuilds')
                    eligibility('IgnoreOfflineNodeEligibility')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                batchFile(settings.elipyCmd)
            }
        }
    }

    /**
     * Adds generic job parameters for running p4 delete workspace.
     */
    static void p4_delete_workspace(def job, def project, def branchFile, def masterFile, String branchName) {
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeP4DeleteWorkspace(branchFile, masterFile, project, branchName)
        job.with {
            description(settings.description)
            label(settings.jobLabel)
            logRotator(7, 20)
            quietPeriod(0)
            customWorkspace(settings.workspaceRoot)
            properties {
                disableConcurrentBuilds()
                disableResume()
            }
            parameters {
                stringParam {
                    name('node')
                    defaultValue('')
                    description('Set correct node name: ks2-f8ccs')
                    trim(true)
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(settings.buildName)
                timeout {
                    absolute(30)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                batchFile(settings.elipyInstallCall)
                batchFile(settings.elipyCmd)
            }
        }
    }

    /**
     * Adds generic job parameters for Avalanche nuke start jobs (for multiple machines).
     */
    static void avalanchecli_nuke_start(def job, def branchFile, def project, def masterFile, String branchName) {
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeAvalancheCliNukeStart(branchFile, masterFile, project, branchName)
        job.with {
            description(settings.description)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(settings.workspaceRoot)
            concurrentBuild()
            parameters {
                nodeParam('machine') {
                    description('select which machine to run avalanche nuke')
                    trigger('allowMultiSelectionForConcurrentBuilds')
                    eligibility('IgnoreOfflineNodeEligibility')
                }
            }
            throttleConcurrentBuilds {
                maxPerNode(1)
            }
            wrappers {
                timestamps()
                buildName(settings.buildName)
                timeout {
                    absolute(240)
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                batchFile(settings.elipyCmd)
            }
        }
    }

    /**
     * Adds generic job parameters for Avalanche nuke all jobs (for all machines matching label).
     */
    static void avalanchecli_nuke_all(def job, def branchFile, def project, def masterFile, String branchName) {
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeAvalancheCliNukeAll(branchFile, masterFile, project, branchName)
        job.with {
            description(settings.description)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(settings.workspaceRoot)
            concurrentBuild()
            parameters {
                labelParam('allmachines') {
                    defaultValue(settings.allMachineLabel)
                    description('run job on all machines matching label: "allmachine_label", default to statebuild')
                    allNodes('allCases', 'IgnoreOfflineNodeEligibility')
                }
            }
            throttleConcurrentBuilds {
                maxPerNode(1)
            }
            wrappers {
                timestamps()
                buildName(settings.buildName)
                timeout {
                    absolute(60)
                }
            }
            triggers {
                cron {
                    spec(settings.cronTrigger)
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                batchFile(settings.elipyCmd)
            }
        }
    }

    /**
     * Adds generic job parameters for Avalanche nuke jobs (for a single machine).
     */
    static void avalanchecli_nuke_job(def job, def branchFile, def project, def masterFile, String branchName) {
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeAvalancheCliNukeJob(branchFile, masterFile, project, branchName)
        job.with {
            description(settings.description)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(settings.workspaceRoot)
            concurrentBuild()
            wrappers {
                timestamps()
                buildName(settings.buildName)
            }
            properties {
                groovyLabelAssignmentProperty {
                    secureGroovyScript {
                        script('return binding.getVariables().get("Node")')
                        sandbox(true)
                    }
                }
            }
            parameters {
                stringParam {
                    name('Node')
                    defaultValue('')
                    description('Name of the node to run the script on.')
                    trim(true)
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                batchFile(settings.elipyCmd)
            }
        }
    }

    /**
     * Adds generic job parameters for Avalanche drop all dbs start jobs (for multiple machines).
     */
    static void avalanchecli_drop_all_dbs_start(def job, def branchFile, def project, def masterFile, String branchName) {
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeAvalancheCliDropAllDbsStart(branchFile, masterFile, project, branchName)
        job.with {
            description(settings.description)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(settings.workspaceRoot)
            concurrentBuild()
            parameters {
                labelParam('allmachines') {
                    defaultValue(settings.allMachineLabel)
                    description('run job on all machines matching lable: statebuild')
                    allNodes('allCases', 'IgnoreOfflineNodeEligibility')
                }
            }
            triggers {
                cron {
                    spec(settings.cronTrigger)
                }
            }
            throttleConcurrentBuilds {
                maxPerNode(1)
            }
            wrappers {
                timestamps()
                buildName(settings.buildName)
                timeout {
                    absolute(240)
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                batchFile(settings.elipyCmd)
            }
        }
    }

    /**
     * Adds generic job parameters for Avalanche drop all dbs jobs (for a single machine).
     */
    static void avalanchecli_drop_all_dbs_job(def job, def branchFile, def project, def masterFile, String branchName) {
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeAvalancheCliDropAllDbsJob(branchFile, masterFile, project, branchName)
        job.with {
            description(settings.description)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(settings.workspaceRoot)
            concurrentBuild()
            wrappers {
                timestamps()
                buildName(settings.buildName)
            }
            properties {
                groovyLabelAssignmentProperty {
                    secureGroovyScript {
                        script('return binding.getVariables().get("Node")')
                        sandbox(true)
                    }
                }
            }
            parameters {
                stringParam {
                    name('Node')
                    defaultValue('')
                    description('Name of the node to run the script on.')
                    trim(true)
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                batchFile(settings.elipyCmd)
            }
        }
    }

    /**
     * Adds generic job parameters for jobs that clean folders on build machines.
     */
    static void clean_agent_folder(def job, def branchFile, def project, def masterFile, String branchName) {
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeCleanAgentFolder(branchFile, masterFile, project, branchName)
        job.with {
            description(settings.description)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(settings.workspaceRoot)
            concurrentBuild()
            parameters {
                nodeParam('machine') {
                    description('select which machine to clean folders on')
                    trigger('allowMultiSelectionForConcurrentBuilds')
                    eligibility('IgnoreOfflineNodeEligibility')
                }
                settings.parameters.each { key, value ->
                    booleanParam(key, value.flag, value.description)
                }
            }
            throttleConcurrentBuilds {
                maxPerNode(1)
            }
            wrappers {
                timestamps()
                buildName(settings.buildName)
                timeout {
                    absolute(240)
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                batchFile(settings.elipyCmd)
            }
        }
    }

    /**
     * Create jenkins.shutdown job.
     */
    static Job jenkinsShutdownJob(DslFactory dslFactory, def branchInfo = [:], Boolean hasCronTrigger = true) {
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeJenkinsShutdownJob(branchInfo, hasCronTrigger)
        return dslFactory.pipelineJob('controller.shutdown') {
            logRotator(30, 100)
            description(settings.description)

            if (settings.hasCronTrigger) {
                properties {
                    pipelineTriggers {
                        triggers {
                            cron {
                                spec(settings.cronTrigger)
                            }
                        }
                    }
                }
            }

            parameters {
                stringParam {
                    name('wait_doquite_hours')
                    defaultValue(settings.waitHours)
                    description('Specifies how long (hours) to wait for master to quit before restart.')
                    trim(true)
                }
                stringParam {
                    name('wait_forcekill_hours')
                    defaultValue(settings.forceWaitHours)
                    description('Specifies how long (hours) to wait extra before force restart')
                    trim(true)
                }
                booleanParam {
                    name('restart_nodes')
                    defaultValue(settings.restartNodes)
                    description('Set true to restart Jenkins nodes')
                }
                booleanParam {
                    name('restart_controller')
                    defaultValue(settings.restartController)
                    description('Set true to restart Jenkins controller')
                }
            }
            definition {
                cps {
                    script(dslFactory.readFileFromWorkspace('src/scripts/schedulers/restartCloudControllerAndAgents.groovy'))
                    sandbox(true)
                }
            }
        }
    }

    /**
     * Adds generic job parameters for sync start jobs.
     */
    static void sync_start(def job, def branchFile, def project, def masterFile, String branchName) {
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeSyncStart(branchFile, masterFile, project, branchName)
        job.with {
            description(settings.description)
            properties {
                pipelineTriggers {
                    triggers {
                        cron {
                            spec(settings.cronTrigger)
                        }
                    }
                }
            }
            environmentVariables {
                env('branch_name', settings.branchName)
                env('job_label', settings.jobLabel)
                env('project_name', settings.projectName)
            }
        }
    }

    /**
     * Adds generic job parameters for sync jobs.
     */
    static void sync_job(def job, def project, def branchFile, def masterFile, String branchName) {
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeSyncJob(branchFile, masterFile, project, branchName)
        job.with {
            description(settings.description)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(settings.workspaceRoot)
            concurrentBuild()
            throttleConcurrentBuilds {
                maxPerNode(1)
                maxTotal(8)
            }
            parameters {
                stringParam {
                    name('node_name')
                    defaultValue(settings.nodeNameDefaultValue)
                    description('What node to run on.')
                    trim(true)
                }
            }
            properties {
                groovyLabelAssignmentProperty {
                    secureGroovyScript {
                        script('return "$node_name"')
                        sandbox(true)
                    }
                }
            }
            wrappers {
                timestamps()
                buildName(settings.buildName)
            }
            steps {
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                batchFile(settings.batchScript)
            }
        }
    }

    /**
     * Adds generic job parameters for code warm up jobs.
     */
    static void code_warm_job(def job, def project, def branchFile, def masterFile, String branchName) {
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeCodeWarmJob(branchFile, masterFile, project, branchName)
        job.with {
            description(settings.description)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(settings.workspaceRoot)
            concurrentBuild()
            throttleConcurrentBuilds {
                maxPerNode(1)
                maxTotal(8)
            }
            parameters {
                nodeParam('machine') {
                    description('select which machine to warm')
                    trigger('allowMultiSelectionForConcurrentBuilds')
                    eligibility('IgnoreOfflineNodeEligibility')
                }
                stringParam {
                    name('platform')
                    defaultValue('')
                    description('Specifies which platform to compile.')
                    trim(true)
                }
                stringParam {
                    name('config')
                    defaultValue('')
                    description('Specifies what configuration to compile.')
                    trim(true)
                }
                stringParam {
                    name('code_folder')
                    defaultValue(settings.codeFolder)
                    description('Specifies which group of code branches to use.')
                    trim(true)
                }
                stringParam {
                    name('code_branch')
                    defaultValue(settings.codeBranch)
                    description('Specifies which code branch to use.')
                    trim(true)
                }
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                choiceParam('clean_local', ['false', 'true'], 'If true, TnT/Local will be deleted at the beginning of the run.')
            }
            multiscm {
                perforce {
                    credential(settings.p4Data.codeCreds)
                    workspace {
                        streamSpec {
                            charset('none')
                            pinHost(true)
                            streamName(settings.p4Data.codeRoot + '/${code_folder}/${code_branch}')
                            format(settings.p4Data.codeClient)
                        }
                    }
                    populate {
                        syncOnly {
                            have(true)
                            force(false)
                            modtime(false)
                            quiet(true)
                            revert(true)
                            pin('')
                            parallel {
                                enable(true)
                                path('')
                                threads('8')
                                minfiles('0')
                                minbytes('0')
                            }
                        }
                    }
                    browser {
                        swarm {
                            url(project.p4_browser_url)
                        }
                    }
                }
            }
            wrappers {
                timestamps()
                buildName(settings.buildName)
                timeout {
                    absolute(settings.timeoutMinutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                batchFile(settings.elipyCmd)
            }
        }
    }

    /**
     * Adds generic job parameters for data warm up jobs.
     */
    static void data_warm_job(def job, def project, def branchFile, def masterFile, String branchName) {
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeDataWarmJob(branchFile, masterFile, project, branchName)
        job.with {
            description(settings.description)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(settings.workspaceRoot)
            concurrentBuild()
            throttleConcurrentBuilds {
                maxPerNode(1)
                maxTotal(8)
            }
            parameters {
                nodeParam('machine') {
                    description('select which machine to warm')
                    trigger('allowMultiSelectionForConcurrentBuilds')
                    eligibility('IgnoreOfflineNodeEligibility')
                }
                stringParam {
                    name('platform')
                    defaultValue('')
                    description('Specifies which platform to compile.')
                    trim(true)
                }
                stringParam {
                    name('dataset')
                    defaultValue('')
                    description('Specifies which dataset to cook.')
                    trim(true)
                }
                stringParam {
                    name('asset')
                    defaultValue('')
                    description('Specifies what levels to cook.')
                    trim(true)
                }
                stringParam {
                    name('code_folder')
                    defaultValue(settings.codeFolder)
                    description('Specifies which group of code branches to use.')
                    trim(true)
                }
                stringParam {
                    name('code_branch')
                    defaultValue(settings.codeBranch)
                    description('Specifies which code branch to use.')
                    trim(true)
                }
                stringParam {
                    name('data_folder')
                    defaultValue(settings.dataFolder)
                    description('Specifies which group of data branches to use.')
                    trim(true)
                }
                stringParam {
                    name('data_branch')
                    defaultValue(settings.dataBranch)
                    description('Specifies which data branch to use.')
                    trim(true)
                }
                stringParam {
                    name('code_changelist')
                    defaultValue(settings.dataBranch)
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue(settings.dataBranch)
                    description('Specifies data changelist to sync.')
                    trim(true)
                }
            }
            multiscm {
                perforce {
                    credential(settings.p4Data.codeCreds)
                    workspace {
                        streamSpec {
                            charset('none')
                            pinHost(true)
                            streamName(settings.p4Data.codeRoot + '/${code_folder}/${code_branch}')
                            format(settings.p4Data.codeClient)
                        }
                    }
                    populate {
                        syncOnly {
                            have(true)
                            force(false)
                            modtime(false)
                            quiet(true)
                            revert(true)
                            pin('')
                            parallel {
                                enable(true)
                                path('')
                                threads('8')
                                minfiles('0')
                                minbytes('0')
                            }
                        }
                    }
                    browser {
                        swarm {
                            url(settings.p4Data.browserUrl)
                        }
                    }
                }
                perforce {
                    credential(settings.p4Data.dataCreds)
                    workspace {
                        streamSpec {
                            charset('none')
                            pinHost(true)
                            streamName(settings.p4Data.dataRoot + '/${data_folder}/${data_branch}')
                            format(settings.p4Data.dataClient)
                        }
                    }
                    populate {
                        syncOnly {
                            have(true)
                            force(false)
                            modtime(false)
                            quiet(true)
                            revert(false)
                            pin('')
                            parallel {
                                enable(true)
                                path('')
                                threads('8')
                                minfiles('0')
                                minbytes('0')
                            }
                        }
                    }
                    browser {
                        swarm {
                            url(settings.p4Data.browserUrl)
                        }
                    }
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(settings.buildName)
                timeout {
                    absolute(settings.timeoutMinutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                batchFile(settings.elipyCmd)
            }
        }
    }

    /**
     * Adds generic job parameters for vault start jobs.
     */
    static void vault_start(def job, def project, def branchFile, def masterFile, String branchName) {
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeVaultStart(branchFile, masterFile, project, branchName)
        // Add sections to the Jenkins job.
        job.with {
            description(settings.description)
            logRotator(180, 100)
            parameters {
                stringParam {
                    name('code_branch')
                    defaultValue('')
                    description('Code branch')
                    trim(true)
                }
                stringParam {
                    name('data_branch')
                    defaultValue('')
                    description('Data branch')
                    trim(true)
                }
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Code changelist')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Data changelist')
                    trim(true)
                }
                stringParam {
                    name('version')
                    defaultValue('')
                    description('Version of build (1.0)')
                    trim(true)
                }
                stringParam {
                    name('build_location')
                    defaultValue('')
                    description('Build location to vault. Default is in Stockholm datacenter. For UK-based builds, use Guildford')
                    trim(true)
                }
                stringParam {
                    name('vault_verification_location')
                    defaultValue('')
                    description('Elipy location for the vault verification settings. For default settings, leave empty. For other settings, specify location.')
                    trim(true)
                }
                stringParam {
                    name('additional_baseline_locations')
                    defaultValue('')
                    description('Store baseline in additional locations (default is the build location). Available options: DiceStockholm/Guildford/RippleEffect, comma separated list for multiple options.')
                    trim(true)
                }
                booleanParam {
                    name('vault_win64_trial')
                    defaultValue(settings.vaultWin64Trial)
                    description('Include win64-trial platform on symbol')
                }
                booleanParam {
                    name('verify_post_vault')
                    defaultValue(settings.verifyPostVault)
                    description('Verify files post build vault')
                }
                LibCommonCps.VAULT_PLATFORMS.each { platform ->
                    booleanParam(platform, project.vault_default_platforms.contains(platform), 'Include ' + platform + ' in the vault process.')
                }
            }
            environmentVariables {
                env('project_name', settings.projectName)
            }
        }
    }

    /**
     * Adds generic job parameters for vault jobs, for both builds and symbols.
     */
    static void vault_job(def job, def project, def branchFile, def masterFile, String branchName, def jobType) {
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeVaultJob(branchFile, masterFile, project, branchName, jobType)

        // Add sections to the Jenkins job.
        job.with {
            customWorkspace(settings.workspaceRoot)
            description(settings.description)
            label(settings.jobLabel)
            logRotator(180, 100)
            concurrentBuild()
            parameters {
                stringParam {
                    name('code_branch')
                    defaultValue('')
                    description('')
                    trim(true)
                }
                stringParam {
                    name('data_branch')
                    defaultValue('')
                    description('')
                    trim(true)
                }
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Code changelist')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Data changelist')
                    trim(true)
                }
                stringParam {
                    name('build_location')
                    defaultValue('')
                    description('Build location to vault. Default is in Stockholm datacenter. For UK-based builds, use Guildford')
                    trim(true)
                }
                stringParam {
                    name('vault_verification_location')
                    defaultValue('')
                    description('Elipy location for the vault verification settings. For default settings, leave empty. For other settings, specify location.')
                    trim(true)
                }
                stringParam {
                    name('additional_baseline_locations')
                    defaultValue('')
                    description('Store baseline in additional locations (default is the build location). Available options: DiceStockholm/Guildford/RippleEffect, comma separated list for multiple options.')
                    trim(true)
                }
                stringParam {
                    name('expression_debug_data')
                    defaultValue('')
                    description('Enable expression debug data by using --expression-debug-data flag')
                    trim(true)
                }
                stringParam {
                    name('vault_win64_trial')
                    defaultValue('')
                    description('Enable/disable win64-trial platform on symbol by using --win64-trial/--no-win64-trial flag')
                    trim(true)
                }
                stringParam {
                    name('verify_post_vault')
                    defaultValue('')
                    description('Enable post vaulting verification using --verify-post-vault flag')
                    trim(true)
                }
                stringParam {
                    name('version')
                    defaultValue('')
                    description('Version of build (1.0)')
                    trim(true)
                }
                choiceParam('platform', ['all', 'all-but-gen5', 'all-but-gen4', 'all-but-win64'] + LibCommonCps.VAULT_PLATFORMS, 'Platform to vault.')
            }
            quietPeriod(0)
            throttleConcurrentBuilds {
                maxPerNode(1)
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(settings.buildName)
                credentialsBinding {
                    if (settings.fbLoginDetails?.p4_creds) {
                        usernamePassword('fb_p4_user', 'fb_p4_passwd', settings.fbLoginDetails.p4_creds)
                    }
                }
            }
            steps {
                if (settings.fbLoginDetails) {
                    batchFile(settings.fbLoginDetailsCall)
                }

                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                batchFile(settings.elipyCmd)
                batchFile(settings.elipyCmd2)
            }

            publishers {
                downstreamParameterized {
                    trigger(settings.downstreamTrigger) {
                        condition('ALWAYS')
                        parameters {
                            currentBuild()
                        }
                    }
                }
            }
        }
    }

    /**
     * Adds generic job parameters for baseline backup jobs.
     */
    static void baseline_job(def job, def project, def branchFile, def masterFile, String branchName) {
        MaintenanceSettings settings = new MaintenanceSettings()
        settings.initializeBaselineJob(branchFile, masterFile, project, branchName)
        // Add sections to the Jenkins job.
        job.with {
            customWorkspace(settings.workspaceRoot)
            description(settings.description)
            label(settings.jobLabel)
            logRotator(180, 100)
            concurrentBuild()
            parameters {
                stringParam {
                    name('code_branch')
                    defaultValue('')
                    description('')
                    trim(true)
                }
                stringParam {
                    name('data_branch')
                    defaultValue('')
                    description('')
                    trim(true)
                }
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Code changelist')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Data changelist')
                    trim(true)
                }
                stringParam {
                    name('build_location')
                    defaultValue('')
                    description('Build location to vault. Default is in Stockholm datacenter. For UK-based builds, use Guildford')
                    trim(true)
                }
                stringParam {
                    name('additional_baseline_locations')
                    defaultValue('')
                    description('Store baseline in additional locations (default is the build location). Available options: DiceStockholm/Guildford/RippleEffect, comma separated list for multiple options.')
                    trim(true)
                }
                choiceParam('platform', ['all', 'all-but-gen5', 'all-but-gen4', 'all-but-win64'] + LibCommonCps.VAULT_PLATFORMS, 'Platform to store baseline for.')
            }
            quietPeriod(0)
            throttleConcurrentBuilds {
                maxPerNode(1)
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(settings.buildName)
            }
            steps {
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                conditionalBuilder {
                    runCondition {
                        and {
                            conditions {
                                conditionContainer {
                                    condition {
                                        not {
                                            condition {
                                                expressionCondition {
                                                    expression('${ENV,var="platform"}')
                                                    label('linuxserver')
                                                }
                                            }
                                        }
                                    }
                                }
                                conditionContainer {
                                    condition {
                                        not {
                                            condition {
                                                expressionCondition {
                                                    expression('${ENV,var="platform"}')
                                                    label('server')
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    runner {
                        fail()
                        // This feels a bit contradictory to me, but it's what to do with the build if the runCondition parsing fails.
                    }
                    conditionalbuilders {
                        batchFile {
                            command(settings.elipyCmd)
                        }
                    }
                }
            }
        }
    }
}
