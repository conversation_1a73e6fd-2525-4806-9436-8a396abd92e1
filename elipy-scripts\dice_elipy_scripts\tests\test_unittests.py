"""
test_unittests.py

Unit testing for unittests
"""
from unittest.mock import patch

from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock.mock import MagicMock

from dice_elipy_scripts.unittests import cli


@patch("dice_elipy_scripts.unittests.authenticate_eapm_credstore", MagicMock())
@patch("elipy2.running_processes.kill", MagicMock())
@patch("os.path.join", MagicMock(side_effect=lambda *args: "\\".join(args)))
@patch("elipy2.frostbite_core.get_tnt_root", MagicMock(return_value="h:\\dev\\tnt"))
@patch("dice_elipy_scripts.unittests.add_sentry_tags", MagicMock())
class TestUnittests:
    OPTION_PASSWORD = "--password"
    OPTION_EMAIL = "--email"
    OPTION_DOMAIN_USER = "--domain-user"

    VALUE_PASSWORD = "password"
    VALUE_EMAIL = "<EMAIL>"
    VALUE_DOMAIN_USER = "dice\\user"

    DEFAULT_ARGS = [
        OPTION_PASSWORD,
        VALUE_PASSWORD,
        OPTION_EMAIL,
        VALUE_PASSWORD,
        OPTION_DOMAIN_USER,
        VALUE_DOMAIN_USER,
    ]

    @patch("elipy2.core.run")
    def test_unittests_runs_script(self, mock_run):
        mock_run.return_value = (0, [], [])
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        mock_run.assert_called_once_with(
            ["python", "h:\\dev\\tnt\\Bin\\unicron\\unicron.py"], allow_non_zero_exit_code=True
        )
        assert result.exit_code == 0
        assert mock_run.call_count == 1

    @patch("elipy2.core.run", MagicMock(return_value=(1, [], "err")))
    @patch("elipy2.LOGGER.error")
    def test_unittests_raises_exception_on_error(self, mock_error):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        mock_error.called_with("Error Message err")
        assert mock_error.call_count == 2
        assert result.exit_code == 1
