import com.ea.exceptions.CobraException

/**
 * CheckValidCheckmateVersion.groovy
 * Checks if a preflighting user has a valid version of Checkmate.
 * this was only used in casablanca, but not in kingston
 */
boolean call(def versionString) {
    def g_allowOldPreflightTool = true
    def g_minCheckmateVersion = 60
    if (versionString?.trim()) {
        if (versionString.integer) {
            def version = versionString.toInteger()
            if (version < g_minCheckmateVersion) {
                throw new IllegalArgumentException('Checkmate version (' + versionString + ') does not meet minimum version requirement (' + g_minCheckmateVersion + '). Please update Checkmate!')
            }
        } else if (versionString != 'dev') {
            throw new IllegalArgumentException('Unknown Checkmate version received:' + versionString + '. Please update Checkmate by rerunning FrostbiteSetup. If the error persists please contact the Tools team.')
        }
    } else if (!g_allowOldPreflightTool) {
        throw new CobraException('Preflight must be run through Checkmate!')
    }
    return true
}
