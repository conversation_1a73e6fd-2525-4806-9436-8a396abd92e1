package com.ea.lib.jobsettings

import com.ea.lib.LibCommonNonCps

class NavmeshSettings extends JobSetting {
    Boolean frostbiteSyncerSetup
    String dataReferenceJob
    String nonVirtualDataBranch
    String nonVirtualDataFolder
    String triggerString
    String triggerType

    void initializeNavmeshJob(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)

        description = 'Builds navmesh for ' + branchInfo.dataset + '.'
        def dryRun = LibCommonNonCps.get_setting_value(branchInfo, [], 'dry_run_navmesh', false)
        jobLabel = LibCommonNonCps.get_setting_value(branchInfo, [], 'navmesh_label',
            LibCommonNonCps.get_setting_value(branchInfo, [], 'job_label_statebuild', 'statebuild')
        )
        def asset = LibCommonNonCps.get_setting_value(branchInfo, [], 'navmesh_asset', 'no-asset')
        timeoutMinutes = LibCommonNonCps.get_setting_value(branchInfo, [], 'timeout_hours_navmesh', 8) * 60
        String p4_data_server = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_data_server', '', projectFile)

        extraArgs = LibCommonNonCps.get_setting_value(branchInfo, [], 'extra_navmesh_args', '')
        if (dryRun == true) {
            extraArgs += ' --dry-run'
        }

        elipyCmd = "${this.elipyCall}" +
            ' navmesh ' +
            " --code-branch ${branchInfo.code_branch}" +
            ' --code-changelist %code_changelist%' +
            ' --data-changelist %data_changelist%' +
            " --asset ${asset}" +
            " --data-dir ${branchInfo.dataset}" +
            " --p4-port ${p4_data_server}" +
            " --p4-client ${projectFile.p4_data_client_env}" +
            ' --data-clean %clean_data%' +
            " ${extraArgs}"
    }

    void initializeNavmeshStart(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        dataBranch = branchInfo.data_branch
        dataFolder = branchInfo.data_folder
        description = 'Starts Navmesh build jobs.'
        dataReferenceJob = LibCommonNonCps.get_setting_value(branchInfo, [], 'data_reference_job', branchName + '.data.start')
        nonVirtualDataBranch = LibCommonNonCps.get_setting_value(branchInfo, [], 'non_virtual_data_branch', '')
        nonVirtualDataFolder = LibCommonNonCps.get_setting_value(branchInfo, [], 'non_virtual_data_folder', '')
        triggerString = LibCommonNonCps.get_setting_value(branchInfo, [], 'trigger_string_navmesh', 'H 0 * * 1-6\nH 6 * * 7')
        triggerType = LibCommonNonCps.get_setting_value(branchInfo, [], 'trigger_type_navmesh', 'scm')
        isDisabled = LibCommonNonCps.get_setting_value(branchInfo, ['navmesh'], 'disable_build', false)
        frostbiteSyncerSetup = projectFile.frostbite_syncer_setup ?: false
    }
}
