package vars

import com.ea.lib.model.JobReference
import hudson.model.Result
import spock.lang.Shared
import spock.lang.Unroll
import support.DeclarativePipelineSpockTest

class RetryOnFailureCauseSpec extends DeclarativePipelineSpockTest {

    static List<JobReference> jobReferences = []
    @Shared List parameters = [
        [name: 'code_changelist', value: '123'],
        [name: 'data_changelist', value: '234'],
    ]
    @Shared Closure failedClosure = {
        def downstreamJob = [
            result  : Result.FAILURE.toString(),
            rawBuild: [getAction      : { Class x -> [foundFailureCauses: [[categories: ['retry']]]] },
                       url            : 'https://url',
                       fullDisplayName: 'name',],
        ]
        jobReferences << new JobReference(downstreamJob: downstreamJob, jobName: 'job-name', parameters: parameters, propagate: false, wait: true)
    }
    @Shared Closure gameFailureClosure = {
        def downstreamJob = [
            result  : Result.FAILURE.toString(),
            rawBuild: [getAction      : { Class x -> [foundFailureCauses: [[categories: ['game']]]] },
                       url            : 'https://url',
                       fullDisplayName: 'name',],
        ]
        jobReferences << new JobReference(downstreamJob: downstreamJob, jobName: 'job-name', parameters: parameters, propagate: false, wait: true)
    }
    @Shared Closure successClosure = {
        def downstreamJob = [
            result  : Result.SUCCESS.toString(),
            rawBuild: [getAction: { Class x -> null }],
        ]
        jobReferences << new JobReference(downstreamJob: downstreamJob, jobName: 'job-name', parameters: parameters, propagate: false, wait: true)
    }

    void setup() {
        jobReferences.clear()
        helper.registerAllowedMethod('build', [Map]) {
            [
                result  : Result.SUCCESS.toString(),
                rawBuild: [getAction: { Class x -> null }],
            ]
        }
    }

    @Unroll
    void 'test retryOnFailureCause retries #expectedCallCount times when a downstream job #expectedResult with retryCount #retryCount'() {
        when:
        Script script = loadScript('retryOnFailureCause.groovy')
        Result result = script.invokeMethod('call', retryCount, jobReferences, false, closure)
        then:
        assertCalledTimes('build', expectedCallCount)
        assertJobStatusSuccess()
        result == Result.SUCCESS
        where:
        closure        | retryCount || expectedCallCount | expectedResult
        failedClosure  | 1          || 1                 | 'fails'
        successClosure | 1          || 0                 | 'succeeds'
        successClosure | 0          || 0                 | 'succeeds'
    }

    void 'test retryOnFailureCause builds with the given arguments when a downstream job fails'() {
        when:
        Script script = loadScript('retryOnFailureCause.groovy')
        Result result = script.invokeMethod('call', 3, jobReferences, false, failedClosure)
        then:
        assertCalledOnceWith('build', [
            job       : 'job-name',
            propagate : false,
            parameters: parameters,
            wait      : true,
        ])
        assertJobStatusSuccess()
        result == Result.SUCCESS
    }

    void 'test retryOnFailureCause job succeeds when a downstream job fails twice and then succeeds'() {
        given:
        List mockBuild = [
            [
                result  : Result.SUCCESS.toString(),
                rawBuild: [getAction: { Class x -> null }],
            ],
            [
                result  : Result.FAILURE.toString(),
                rawBuild: [getAction      : { Class x -> [foundFailureCauses: [[categories: ['retry']]]] },
                           url            : 'https://url',
                           fullDisplayName: 'name',],
            ],
        ]
        helper.registerAllowedMethod('build', [Map]) { mockBuild.pop() }
        when:
        Script script = loadScript('retryOnFailureCause.groovy')
        Result result = script.invokeMethod('call', 3, jobReferences, false, failedClosure)
        then:
        assertCalledTimes('build', 2)
        assertJobStatusSuccess()
        result == Result.SUCCESS
    }

    @Unroll
    void 'test retryOnFailureCause is a #expectedResult and does not retry on a game team error with retryCount #retryCount and allowFailure #allowFailure'() {
        given:
        helper.registerAllowedMethod('build', [Map]) {
            [
                result  : Result.FAILURE.toString(),
                rawBuild: [getAction      : { Class x -> [foundFailureCauses: [[categories: ['asdf', 'game']]]] },
                           url            : 'https://url',
                           fullDisplayName: 'name',],
            ]
        }
        when:
        Script script = loadScript('retryOnFailureCause.groovy')
        script.invokeMethod('call', retryCount, jobReferences, allowFailure, closure)
        then:
        assertCalledTimes('build', expectedCallCount)
        binding.getVariable('currentBuild').result == expectedResult
        where:
        retryCount | closure            | allowFailure || expectedCallCount | expectedResult
        3          | gameFailureClosure | true         || 0                 | Result.SUCCESS.toString()
        3          | gameFailureClosure | false        || 0                 | Result.FAILURE.toString()
        3          | failedClosure      | false        || 1                 | Result.FAILURE.toString()
        0          | failedClosure      | false        || 0                 | Result.FAILURE.toString()
    }

    @Unroll
    void 'test retryOnFailureCause is a #expectedResult if retries are unsuccessful and allowFailure is set to #allowFailure'() {
        given:
        helper.registerAllowedMethod('build', [Map]) {
            [
                result  : Result.FAILURE.toString(),
                rawBuild: [getAction      : { Class x -> [foundFailureCauses: [[categories: ['retry', 'game']]]] },
                           url            : 'https://url',
                           fullDisplayName: 'name',],
            ]
        }
        when:
        Script script = loadScript('retryOnFailureCause.groovy')
        script.invokeMethod('call', 3, jobReferences, allowFailure, failedClosure)
        then:
        def e = thrown Exception
        e.message == 'Maximum number of retries reached. Exiting job.'
        assertCalledTimes('build', 3)
        assertCalledTimes('printFailureMessage', 3)
        binding.getVariable('currentBuild').result == expectedResult
        where:
        allowFailure || expectedResult
        false        || Result.FAILURE.toString()
        true         || Result.SUCCESS.toString()
    }

}
