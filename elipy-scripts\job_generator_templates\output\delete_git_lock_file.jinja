{#
    Command:
        delete_git_lock_file
            short_help: delete git lock file.

    Arguments:

    Required variables:

    Optional variables:
        repo_path
            default: D:\dev\ci
            help: Path to folder where the git repo located.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    delete_git_lock_file
    {%- if repo_path %}
    --repo-path {{ repo_path }}
    {%- endif %}
  displayName: elipy delete_git_lock_file
