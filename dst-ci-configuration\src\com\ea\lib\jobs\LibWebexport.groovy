package com.ea.lib.jobs

import com.ea.lib.LibJobDsl
import com.ea.lib.jobsettings.WebexportSettings

class LibWebexport {
    /**
     * Adds generic job parameters for Webexport jobs.
     */
    static void webexport_job(def job, Class project_file, Class branch_file, Class master_file, String branch_name) {
        WebexportSettings jobSettings = new WebexportSettings()
        jobSettings.initialize(branch_file, master_file, project_file, branch_name)

        job.with {
            description(jobSettings.description)
            disabled(jobSettings.disableBuild)
            label(jobSettings.jobLabel)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(jobSettings.workspaceRoot)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to sync.')
                    trim(true)
                }
                choiceParam('clean_data', ['False', 'True'], 'If True, Avalanche will be cleaned at the beginning of the run.')
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(jobSettings.buildName)
                timeout {
                    absolute(jobSettings.timeoutMinutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
                credentialsBinding {
                    string('AWS_SECRET_KEY', 'webexport-aws-secret-key')
                    string('AWS_ACCESS_KEY_ID', 'webexport-aws-access-key-id')
                    if (jobSettings.p4Creds) {
                        usernamePassword('fb_p4_user', 'fb_p4_passwd', jobSettings.p4Creds)
                    }
                }
            }
            steps {
                if (jobSettings.fbLoginDetails) {
                    batchFile("echo %fb_p4_passwd%|p4 -p ${jobSettings.p4Port} -u %fb_p4_user% login & exit 0")
                }
                LibJobDsl.installElipy(delegate, jobSettings.elipyInstallCall, project_file)
                batchFile(jobSettings.elipyCmd)
            }
        }
    }
}
