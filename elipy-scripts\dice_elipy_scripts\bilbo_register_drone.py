"""
bilbo_register_drone.py
"""

import os
import click
import time

from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import filer_paths, LOGGER, SETTINGS, build_metadata_utils
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics
from elipy2.exceptions import ELIPYException


@click.command("bilbo_register_drone", short_help="Registers a Drone build in Bilbo.")
@click.option("--code-branch", help="Perforce code branch/stream name.", required=True)
@click.option(
    "--code-changelist",
    required=True,
    help="Changelist number of code build used to verify data.",
)
@click.option("--data-branch", help="Perforce data branch/stream name.", required=True)
@click.option("--data-changelist", help="Changelist number of data built.", required=True)
@click.option(
    "--dataset",
    required=True,
    help="Which dataset has been validated using this code build.",
)
@click.option(
    "--old-drone-setup",
    is_flag=True,
    help="Skip deploying TnT (DICE Drone builds req.).",
)
@click.option("--source-location", default="default", help="Location to move the build from.")
@click.option(
    "--extra-location",
    multiple=True,
    required=False,
    help="Another locations to register this build",
)
@click.option(
    "--skip-code-smoke-tag",
    is_flag=True,
    default=False,
    help="Skip copying code smoke tag to remote locations (for OneTrunk workflow)",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    code_branch,
    code_changelist,
    data_branch,
    data_changelist,
    dataset,
    old_drone_setup,
    source_location,
    extra_location,
    skip_code_smoke_tag,
):
    """
    Registers a Drone build in the configured metadata services.

    A drone build means that a certain data changelist has been verified as buildable
    given a certain code build, and is thus safe for syncing for content creators.

    Default location will be used as source if no location is specified for that.

    Future improvement: Allow us to register builds from cross-branches,
    i.e. game-dev code verifying game-dev-unverified data.

    """
    # adding sentry tags
    add_sentry_tags(__file__)

    if old_drone_setup and data_changelist is not None:
        target_changelist = max(int(code_changelist), int(data_changelist))
    else:
        target_changelist = code_changelist

    locations = list(extra_location)
    locations.insert(0, SETTINGS.location)

    # Setup metadata manager for the source location first
    source_build = None
    source_metadata_manager = build_metadata_utils.setup_metadata_manager(
        bilbo_url=SETTINGS.get("bilbo_url", location=source_location), location=source_location
    )
    source_path = filer_paths.get_code_build_root_path(
        code_branch, target_changelist, location=source_location
    )
    LOGGER.info("Checking for build at {}: {}".format(source_location, source_path))

    # Try to get the source build information
    try:
        query_string = "type.keyword:drone AND branch.keyword:{} AND changelist.keyword:{}".format(
            code_branch, target_changelist
        )
        builds = list(
            source_metadata_manager.get_all_builds_query_string(query_string=query_string)
        )
        if not builds:
            LOGGER.warning("No builds found at {}: {}".format(source_location, source_path))

        for build in builds:
            if build.source["location"] == SETTINGS.get(
                "studio_location", location=source_location
            ):
                LOGGER.info(
                    "Found source build at "
                    "studio location: {} "
                    "with path: {}".format(
                        SETTINGS.get("studio_location", location=source_location),
                        source_path,
                    )
                )
                source_build = build
                break

    except Exception as exc:
        raise ELIPYException("Error retrieving source build information: {}".format(str(exc)))

    # Register the build in each location
    for location in locations:  # pylint: disable=too-many-nested-blocks
        metadata_manager = build_metadata_utils.setup_metadata_manager(
            bilbo_url=SETTINGS.get("bilbo_url", location=location), location=location
        )
        LOGGER.info(
            "Registering Drone build code {0}.CL{1} with data {2}.CL{3} at {4}".format(
                code_branch, code_changelist, data_branch, data_changelist, location
            )
        )

        is_source_location = location == source_location
        is_original_location = location == SETTINGS.location
        path = filer_paths.get_code_build_root_path(
            code_branch, target_changelist, location=location
        )
        register_args = {
            "path": path,
            "data_changelist": data_changelist,
            "code_changelist": code_changelist,
            "code_branch": code_branch,
            "data_branch": data_branch,
            "dataset": dataset,
        }
        metadata_manager.register_drone_build(
            **register_args, write_attributes_file=is_original_location
        )

        LOGGER.info("Waiting 10 seconds to give time for build to register before possible updates")
        time.sleep(10)

        # Copy smoked build status from source build if it exists
        try:
            if source_build and not is_source_location:
                LOGGER.info("This is a remote location, updating attributes from the source build")
                has_data_smoke_tag = False

                # Check if there are any data builds connected to this code build
                if source_build.source.get("verified_data"):
                    # Check all data builds
                    for verified_data in source_build.source.get("verified_data"):
                        # and, if verified/smoked, tag it in the remote location
                        if verified_data.get("build_promotion_level"):
                            has_data_smoke_tag = True
                            metadata_manager.tag_data_only_as_smoked(
                                path=path, data_changelist=verified_data.get("changelist")
                            )
                            LOGGER.info(
                                "Successfully registered data smoke to CL {} at {}".format(
                                    verified_data.get("changelist"),
                                    location,
                                )
                            )

                # Check if source build has a code smoke tag and copy it to remote location
                if source_build.source.get("build_promotion_level") and not skip_code_smoke_tag:
                    metadata_manager.tag_code_build_as_smoked(path=path)
                    LOGGER.info(
                        "Successfully registered smoke tag to code build {} at {}".format(
                            source_build.source.get("changelist"), location
                        )
                    )
                elif not has_data_smoke_tag:
                    LOGGER.info(
                        "No smoke tags found in source build, not updating any attributes "
                        "in remote location"
                    )

        except Exception as exc:
            raise ELIPYException(
                "Error updating smoke tag attributes from source build to "
                "remote build {} at {}: {}".format(
                    source_build.source.get("changelist"), location, exc
                )
            )
