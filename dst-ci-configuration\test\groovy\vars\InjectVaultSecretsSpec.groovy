package vars

import support.DeclarativePipelineSpockTest

class InjectVaultSecretsSpec extends DeclarativePipelineSpockTest {

    class Project {
        static String name = 'bctch1'
    }

    class ProjectWithSecrets extends Project {
        static List<Map> vault_secrets_project =
        [
            [
                vault_secret_path: 'path/to/projectSecret',
                target_env_var   : 'envVar1',
                vault_secret_key : 'vaultKey1',
            ],
        ]
    }

    class BranchFile {
        static Map standard_jobs_settings = [:]
        static Map general_settings = [:]
    }

    class BranchFileWithBranchSecrets extends BranchFile {
        static Map standard_jobs_settings = [:]
        static Map general_settings = [
            vault_secrets_branch: [
                [
                    vault_secret_path: 'path/to/branchSecret',
                    target_env_var   : 'envVar1',
                    vault_secret_key : 'vaultKey1',
                ]
            ]
        ]
    }

    class BranchFileWithJobSecrets extends BranchFile {
        static Map standard_jobs_settings = [:]
        static Map general_settings = [
            vault_secrets_job: [
                [
                    vault_secret_path: 'path/to/jobSecret',
                    target_env_var   : 'envVar1',
                    vault_secret_key : 'vaultKey1',
                ]
            ]
        ]
    }

    void setup() {
        // Mock the withVault step
        helper.registerAllowedMethod('withVault', [Map, Closure]) { Map vaultConfig, Closure closure ->
            closure.call()
        }
        helper.registerAllowedMethod('bat', [String]) { String arg ->
            echo "bat: $arg"
        }
    }

    void 'test InjectVaultSecrets with no vault configurations'() {
        when:
        Script script = loadScript('InjectVaultSecrets.groovy')
        def closure = { bat('echo install elipy') }
        closure.delegate = script
        script.invokeMethod('call', BranchFile, Project, closure)
        printCallStack()

        then:
        assertCallStackContains('No vault secrets configured.')
        assertCallStackContains('bat: echo install elipy')
    }

    void 'test InjectVaultSecrets with branch secrets'() {
        when:
        Script script = loadScript('InjectVaultSecrets.groovy')
        def closure = { bat('echo install elipy') }
        closure.delegate = script
        script.invokeMethod('call', BranchFileWithBranchSecrets, Project, closure)
        printCallStack()

        then:
        assertCallStackContains('withVault')
        assertCallStackContains('path=path/to/branchSecret')
        assertCallStackContains('envVar1')
        assertCallStackContains('vaultKey1')
        assertCallStackContains('bat: echo install elipy')
    }

    void 'test InjectVaultSecrets with job secrets'() {
        when:
        Script script = loadScript('InjectVaultSecrets.groovy')
        def closure = { bat('echo install elipy') }
        closure.delegate = script
        script.invokeMethod('call', BranchFileWithJobSecrets, Project, closure)
        printCallStack()

        then:
        assertCallStackContains('withVault')
        assertCallStackContains('path=path/to/jobSecret')
        assertCallStackContains('envVar1')
        assertCallStackContains('vaultKey1')
        assertCallStackContains('bat: echo install elipy')
    }

    void 'test InjectVaultSecrets with project secrets'() {
        when:
        Script script = loadScript('InjectVaultSecrets.groovy')
        def closure = { bat('echo install elipy') }
        closure.delegate = script
        script.invokeMethod('call', BranchFile, ProjectWithSecrets, closure)
        printCallStack()

        then:
        assertCallStackContains('withVault')
        assertCallStackContains('path=path/to/projectSecret')
        assertCallStackContains('envVar1')
        assertCallStackContains('vaultKey1')
        assertCallStackContains('bat: echo install elipy')
    }
}
