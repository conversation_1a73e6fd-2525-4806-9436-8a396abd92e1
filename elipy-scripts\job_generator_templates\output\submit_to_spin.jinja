{#
    Command:
        submit_to_spin
            short_help: Submits a build to Spin.

    Arguments:

    Required variables:

    Optional variables:
        code_branch
            help: Branch/stream that the code/binary is coming from.
        code_changelist
            help: Changelist of binaries.
        data_branch
            help: Branch/stream that data is coming from.
        data_changelist
            help: Changelist of data being used.
        platform
            help: Which platform to deploy (default is linuxserver).
            default: linuxserver
        format
            help: Which format to deploy (default is digital).
            default: digital
        config
            help: Code debug level (default is final).
            default: final
        region
            help: Which region to deploy (default is ww).
            default: ww
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    submit_to_spin
    {%- if code_branch %}
    --code-branch {{ code_branch }}
    {%- endif %}
    {%- if code_changelist %}
    --code-changelist {{ code_changelist }}
    {%- endif %}
    {%- if data_branch %}
    --data-branch {{ data_branch }}
    {%- endif %}
    {%- if data_changelist %}
    --data-changelist {{ data_changelist }}
    {%- endif %}
    {%- if platform %}
    --platform {{ platform }}
    {%- endif %}
    {%- if format %}
    --format {{ format }}
    {%- endif %}
    {%- if config %}
    --config {{ config }}
    {%- endif %}
    {%- if region %}
    --region {{ region }}
    {%- endif %}
  displayName: elipy submit_to_spin
