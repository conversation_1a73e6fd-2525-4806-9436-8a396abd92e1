package com.ea.project.kin.branchsettings

class Kin_dev_unverified_outsource_code {
    // Settings for jobs
    static Class project = com.ea.project.kin.Kingston
    static Map general_settings = [
        dataset           : project.dataset,
        elipy_install_call: project.elipy_install_call,
        elipy_call        : project.elipy_call,
        workspace_root    : project.workspace_root,
    ]
    static Map standard_jobs_settings = [
        asset                 : 'PreflightLevels',
        prebuild_info         : [
            config               : 'release',
            extra_args           : [
                '--framework-args -G:frostbite.feature.OodleTexture=false --framework-args -G:frostbite.feature.Oodle-Tool=false',
            ],
            input_param_path     : 'Code\\DICE\\BattlefieldGame\\BattlefieldGame-outsource-input-param.xml',
            platforms_sln        : ['tool'],
            platform_prebuild    : ['win64-dll'],
            prebuild_path        : '//dicestudio/kin/dev/kin-dev-unverified-outsource-code/TnT/Prebuild',
            skip_platforms       : ['gdk', 'NX', 'ps4', 'ps5', 'xdk'],
            outsource_validation : true,
            platform_validation  : ['tool', 'frosted'],
            extra_args_validation: [
                ' --framework-args -G:package.eaconfig.useprebuiltpackages=true' +
                    ' --framework-args -G:package.FBBuild.enableAllCppTests=true' +
                    ' --framework-args -G:package.FBBuild.enableAllCSharpTests=true' +
                    ' --framework-args -G:disablepackageserver=true' +
                    ' --framework-args -G:useproxypackages=true' +
                    ' --framework-args -wn:2015',
            ],
            workspace_type       : 'streams',
        ],
        reference_job_prebuild: 'kin-dev-unverified.code.start',
        server_asset          : 'PreflightLevels',
        trigger_type_code     : 'none',
    ]
    static Map preflight_settings = [:]

    // Matrix definitions for jobs
    static List code_matrix = []
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = []
    static List data_matrix = []
    static List data_downstream_matrix = []
    static List patchdata_matrix = []
    static List frosty_matrix = []
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = []
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
