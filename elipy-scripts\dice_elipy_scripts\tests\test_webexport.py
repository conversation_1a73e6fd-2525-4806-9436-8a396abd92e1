"""
test_webexport.py

Unit testing for webexport
"""
import unittest
from unittest.mock import ANY, call
import pytest
import os
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import patch, MagicMock, mock_open
from dice_elipy_scripts.webexport import (
    cli,
    _run_webexport,
    _fetch_binaries,
    _get_webexport_module,
    _import_previous_avalanche_state,
    _filer_save,
)
from elipy2.config import ConfigManager
from elipy2.exceptions import WebexportException

config_path = os.path.join(os.path.dirname(__file__), "data", "elipy_test.yml")
config_manager = ConfigManager(path=config_path)

BASIC_ARGS = [
    "--asset",
    "ShippingLevels",
    "--script-path",
    "Scripts\\DICE\\webexport.py",
    "--data-dir",
    "testdata",
    "--code-branch",
    "testbranch",
    "--code-changelist",
    "123",
    "--branch-name",
    "testname",
    "--data-changelist",
    "456",
    "--data-clean",
    "False",
    "--aws-secret-key",
    "secret",
    "--aws-access-key-id",
    "id",
    "--clean-master-version-check",
]


@patch("elipy2.frostbite_core.get_tnt_root", MagicMock(return_value="h:\\dev\\tnt"))
@patch("elipy2.running_processes.kill", MagicMock())
@patch("elipy2.telemetry.upload_metrics", MagicMock())
@patch("inspect.getfullargspec", MagicMock())
@patch("os.path.join", MagicMock(side_effect=lambda *x: "\\".join(x)))
class TestWebexport(unittest.TestCase):
    def setUp(self):
        self.patcher_datautils = patch("elipy2.data.DataUtils")
        self.mock_datautils = self.patcher_datautils.start()
        self.mock_datautils.return_value = MagicMock()

        self.patcher_avalanche_nuke = patch("elipy2.avalanche.nuke")
        self.mock_avalanche_nuke = self.patcher_avalanche_nuke.start()

    def tearDown(self):
        patch.stopall()

    @patch("dice_elipy_scripts.webexport._get_webexport_module", MagicMock())
    @patch("dice_elipy_scripts.webexport._fetch_binaries", MagicMock())
    @patch("dice_elipy_scripts.webexport._import_previous_avalanche_state", MagicMock())
    @patch("dice_elipy_scripts.webexport._run_webexport", MagicMock())
    @patch("dice_elipy_scripts.webexport._filer_save", MagicMock())
    def test_cli_basic_args(self):
        runner = CliRunner()
        result = runner.invoke(cli, BASIC_ARGS)
        assert result.exit_code == 0

    @patch("dice_elipy_scripts.webexport._get_webexport_module", MagicMock())
    @patch("dice_elipy_scripts.webexport._fetch_binaries", MagicMock())
    @patch("dice_elipy_scripts.webexport._import_previous_avalanche_state", MagicMock())
    @patch("dice_elipy_scripts.webexport._run_webexport", MagicMock())
    @patch("dice_elipy_scripts.webexport._filer_save", MagicMock())
    def test_avalanche_nuke(self):
        runner = CliRunner()
        result = runner.invoke(cli, BASIC_ARGS + ["--nuke"])
        assert result.exit_code == 0
        self.mock_avalanche_nuke.assert_called_once_with()

    @patch("dice_elipy_scripts.webexport._get_webexport_module", MagicMock())
    @patch("dice_elipy_scripts.webexport._fetch_binaries", MagicMock())
    @patch("dice_elipy_scripts.webexport._import_previous_avalanche_state", MagicMock())
    @patch("dice_elipy_scripts.webexport._run_webexport", MagicMock())
    @patch("dice_elipy_scripts.webexport._filer_save", MagicMock())
    def test_data_clean(self):
        runner = CliRunner()
        result = runner.invoke(cli, BASIC_ARGS + ["--data-clean", "true"])
        assert result.exit_code == 0
        self.mock_datautils.return_value.clean.assert_called_once_with()

    @patch("dice_elipy_scripts.webexport._get_webexport_module", MagicMock())
    @patch("dice_elipy_scripts.webexport._fetch_binaries", MagicMock())
    @patch("dice_elipy_scripts.webexport._import_previous_avalanche_state", MagicMock())
    @patch("dice_elipy_scripts.webexport._run_webexport", MagicMock())
    @patch("dice_elipy_scripts.webexport._filer_save")
    def test_save_to_filer(self, mock_filer_save):
        runner = CliRunner()
        result = runner.invoke(cli, BASIC_ARGS)
        assert result.exit_code == 0
        mock_filer_save.assert_called_once_with(
            "h:\\dev\\tnt\\Local\\webexport",
            BASIC_ARGS[9],
            BASIC_ARGS[13],
            BASIC_ARGS[11],
        )

    @patch("dice_elipy_scripts.webexport._get_webexport_module", MagicMock())
    @patch("dice_elipy_scripts.webexport._fetch_binaries", MagicMock())
    @patch("dice_elipy_scripts.webexport._import_previous_avalanche_state", MagicMock())
    @patch("dice_elipy_scripts.webexport._run_webexport", MagicMock())
    @patch("dice_elipy_scripts.webexport.getfullargspec")
    @patch("dice_elipy_scripts.webexport._filer_save")
    def test_save_to_filer_with_content_layer(self, mock_filer_save, mock_getfullargspec):
        mock_getfullargspec.return_value = ["cookArgs"]
        runner = CliRunner()
        result = runner.invoke(cli, BASIC_ARGS + ["--content-layers", "TestLayer1"])
        assert result.exit_code == 0
        expected_calls = [
            call(
                "h:\\dev\\tnt\\Local\\webexport",
                BASIC_ARGS[9],
                BASIC_ARGS[13],
                BASIC_ARGS[11],
            ),
            call(
                "h:\\dev\\tnt\\Local\\webexport",
                BASIC_ARGS[9],
                BASIC_ARGS[13],
                BASIC_ARGS[11],
            ),
        ]
        mock_filer_save.assert_has_calls(expected_calls, any_order=False)

    @patch("dice_elipy_scripts.webexport._get_webexport_module", MagicMock())
    @patch("dice_elipy_scripts.webexport._fetch_binaries", MagicMock())
    @patch("dice_elipy_scripts.webexport._import_previous_avalanche_state", MagicMock())
    @patch("dice_elipy_scripts.webexport._run_webexport", MagicMock())
    @patch("dice_elipy_scripts.webexport._filer_save")
    def test_dont_save_to_filer(self, mock_filer_save):
        runner = CliRunner()
        result = runner.invoke(cli, BASIC_ARGS + ["--dont-save-to-filer"])
        assert result.exit_code == 0
        assert mock_filer_save.call_count == 0

    @patch("dice_elipy_scripts.webexport._get_webexport_module", MagicMock())
    @patch("dice_elipy_scripts.webexport._fetch_binaries", MagicMock())
    @patch("dice_elipy_scripts.webexport._import_previous_avalanche_state")
    @patch("dice_elipy_scripts.webexport._run_webexport", MagicMock())
    @patch("dice_elipy_scripts.webexport._filer_save", MagicMock())
    def test_dont_import_avalanche(self, mock_import_previous_avalanche_state):
        runner = CliRunner()
        result = runner.invoke(cli, BASIC_ARGS)
        assert result.exit_code == 0
        assert mock_import_previous_avalanche_state.call_count == 0

    @patch("dice_elipy_scripts.webexport._get_webexport_module", MagicMock())
    @patch("dice_elipy_scripts.webexport._fetch_binaries")
    @patch("dice_elipy_scripts.webexport._import_previous_avalanche_state")
    @patch("dice_elipy_scripts.webexport._run_webexport", MagicMock())
    @patch("dice_elipy_scripts.webexport._filer_save", MagicMock())
    def test_import_avalanche(self, mock_import_previous_avalanche_state, mock_fetch_binaries):
        mock_fetch_binaries.return_value = ["avalanche_arg"]
        runner = CliRunner()
        result = runner.invoke(cli, BASIC_ARGS + ["--import-avalanche-state"])
        assert result.exit_code == 0
        mock_import_previous_avalanche_state.assert_called_once_with(
            self.mock_datautils.return_value,
            ["avalanche_arg"],
            BASIC_ARGS[9],
            BASIC_ARGS[13],
            BASIC_ARGS[11],
            "win64",
        )

    @patch("dice_elipy_scripts.webexport._get_webexport_module", MagicMock())
    @patch("dice_elipy_scripts.webexport._fetch_binaries", MagicMock())
    @patch("dice_elipy_scripts.webexport._import_previous_avalanche_state", MagicMock())
    @patch("dice_elipy_scripts.webexport._filer_save", MagicMock())
    @patch("elipy2.frostbite_core.get_tnt_root", MagicMock(return_value="h:\\dev\\tnt"))
    @patch("dice_elipy_scripts.webexport.getfullargspec")
    @patch("dice_elipy_scripts.webexport._run_webexport")
    def test_cli_webexport_args(self, mock_run_webexport, mock_getfullargspec):
        mock_getfullargspec.return_value = ["cookArgs"]
        runner = CliRunner()
        result = runner.invoke(cli, BASIC_ARGS + ["--content-layers", "TestLayer1"])
        mock_run_webexport.assert_has_calls(
            [
                call(
                    ANY,
                    ANY,
                    ANY,
                    "ShippingLevels",
                    True,
                    "id",
                    "secret",
                    [],
                    False,
                    "h:\\dev\\tnt\\Local\\webexport",
                    ANY,
                    "win64",
                    False,
                    True,
                    "testname",
                ),
                call(
                    ANY,
                    ANY,
                    ANY,
                    "ShippingLevels",
                    True,
                    "id",
                    "secret",
                    ["-activeContentLayer", "TestLayer1"],
                    False,
                    "h:\\dev\\tnt\\Local\\webexport",
                    ANY,
                    "win64",
                    False,
                    True,
                    "testname",
                ),
            ],
            any_order=False,
        )
        assert result.exit_code == 0

    @patch(
        "elipy2.data.DataUtils.get_clean_master_version_args",
        MagicMock(return_value=["flag", "option"]),
    )
    def test_run_webexport(self):
        webexport = MagicMock()
        webexport.run_web_export.return_value = True
        builder = MagicMock()

        run_web_export_argnames = (
            "platform",
            "outputDir",
            "doNotMinify",
            "attach",
            "forceBuild",
            "awsAccessKeyId",
            "awsSecretAccessKey",
            "cookArgs",
            "uploadToAws",
            "branchOverride",
        )

        args = (
            webexport,
            builder,
            run_web_export_argnames,
            "asset",
            True,
            "id",
            "secret",
            ["-activeContentLayer", "TestLayer1"],
            True,
            "h:\\tnt\\Local\\webexport",
            (),
            "win64",
            False,
            True,
            "branch_override",
        )

        _run_webexport(*args)
        webexport.run_web_export.assert_called_once_with(
            attach=False,
            awsAccessKeyId=args[5],
            awsSecretAccessKey=args[6],
            doNotMinify=False,
            forceBuild=True,
            outputDir=args[9],
            platform=args[11],
            uploadToAws=not args[12],
            branchOverride="branch_override",
            cookArgs=args[7],
        )

        with self.assertRaises(Exception):
            webexport.run_web_export.return_value = False
            _run_webexport(*args)

    @patch("dice_elipy_scripts.webexport.filer.FilerUtils", MagicMock())
    @patch("dice_elipy_scripts.webexport.filer.FilerUtils.fetch_code", MagicMock())
    @patch(
        "dice_elipy_scripts.webexport.import_avalanche_data_state",
        MagicMock(return_value=["test_arg"]),
    )
    @patch(
        "dice_elipy_scripts.utils.state_utils.import_avalanche_data_state",
        MagicMock(return_value="test"),
    )
    def test_fetch_pipeline_bin(self):
        args = {
            "code_branch": BASIC_ARGS[7],
            "code_changelist": BASIC_ARGS[9],
            "branch_name": BASIC_ARGS[11],
            "platform": "win64",
            "data_changelist": BASIC_ARGS[13],
            "import_avalanche_state": True,
        }

        assert _fetch_binaries(**args) == ["test_arg"]

        args["import_avalanche_state"] = False
        assert _fetch_binaries(**args) == []

    @patch("os.path.exists", MagicMock(return_value=True))
    @patch(
        "elipy2.core.import_module_from_file",
        MagicMock(side_effect=lambda x, y: y + "\\" + x),
    )
    def test_get_webx_module(self):
        assert _get_webexport_module("test") == "h:\\dev\\tnt\\test\\webexport"

    @patch("os.path.exists", MagicMock(return_value=False))
    @patch(
        "elipy2.core.import_module_from_file",
        MagicMock(side_effect=lambda x, y: y + "\\" + x),
    )
    def test_get_webx_module_not_in_tnt(self):
        assert _get_webexport_module("test") == "game_root\\test\\webexport"

    @patch("os.path.exists", MagicMock(return_value=True))
    @patch("elipy2.core.import_module_from_file")
    def test_get_webx_module_exception(self, mock_import_module_from_file):
        mock_import_module_from_file.side_effect = Exception()
        with pytest.raises(Exception):
            _get_webexport_module("test")

    @patch("elipy2.avalanche.set_avalanche_build_status")
    def test_imp_prev_avalanche(self, mock_ava_status):
        builder = MagicMock()
        platform = "win64"

        _import_previous_avalanche_state(
            builder=builder,
            extra_args=[],
            code_changelist=BASIC_ARGS[9],
            data_changelist=BASIC_ARGS[13],
            branch_name=BASIC_ARGS[11],
            platform=platform,
        )

        builder.cook.assert_called_once_with(pipeline_args=[], collect_mdmps=True)
        mock_ava_status.assert_called_once_with(
            code_changelist=BASIC_ARGS[9],
            data_changelist=BASIC_ARGS[13],
            data_branch=BASIC_ARGS[11],
            platform=platform,
        )

    @patch("builtins.open", new_callable=mock_open, read_data="patchversion_contents")
    @patch("elipy2.core.robocopy")
    @patch("elipy2.build_metadata.BuildMetadataManager", MagicMock())
    @patch("dice_elipy_scripts.webexport.SETTINGS", config_manager)
    @patch.dict(os.environ, {"BRANCH_NAME": "envbranch", "fb_branch_id": "1"}, clear=True)
    @patch("os.path.exists", MagicMock(return_value=True))
    def test_filer_save(self, mock_robocopy, mock_open):
        _filer_save(
            write_directory="h:\\dev\\tnt\\Local\\webexport",
            code_changelist=BASIC_ARGS[9],
            data_changelist=BASIC_ARGS[13],
            branch_name=BASIC_ARGS[11],
        )

        mock_open.assert_called_with(
            os.path.join(
                "h:\\dev\\tnt\\Local\\webexport",
                "envbranch.1.Win32",
                "Win32",
                "patchversion.txt",
            ),
            "r",
        )

        changelist = "{0}_{1}".format(BASIC_ARGS[13], BASIC_ARGS[9])
        export_dir_final = os.path.join(
            config_manager.get("build_share"),
            "WebExport",
            BASIC_ARGS[11],
            "patchversion_contents",
            changelist,
        )

        mock_robocopy.assert_called_with(
            "h:\\dev\\tnt\\Local\\webexport",
            export_dir_final,
            extra_args=["/NFL"],
            purge=True,
        )

    @patch("builtins.open", new_callable=mock_open, read_data="patchversion_contents")
    @patch("elipy2.core.robocopy")
    @patch("elipy2.build_metadata.BuildMetadataManager", MagicMock())
    @patch("dice_elipy_scripts.webexport.SETTINGS", config_manager)
    @patch.dict(os.environ, {"BRANCH_NAME": "envbranch", "fb_branch_id": "1"}, clear=True)
    @patch("os.path.exists", MagicMock(side_effect=[False, True]))
    def test_filer_save_no_patchversion(self, mock_robocopy, mock_open):
        _filer_save(
            write_directory="h:\\dev\\tnt\\Local\\webexport",
            code_changelist=BASIC_ARGS[9],
            data_changelist=BASIC_ARGS[13],
            branch_name=BASIC_ARGS[11],
        )

        assert mock_open.call_count == 0

    @patch("builtins.open", new_callable=mock_open, read_data="patchversion_contents")
    @patch("elipy2.core.robocopy")
    @patch("elipy2.build_metadata.BuildMetadataManager", MagicMock())
    @patch("dice_elipy_scripts.webexport.SETTINGS", config_manager)
    @patch.dict(os.environ, {"BRANCH_NAME": "envbranch", "fb_branch_id": "1"}, clear=True)
    @patch("os.path.exists", MagicMock(side_effect=[True, False]))
    def test_filer_save_no_directory(self, mock_robocopy, mock_open):
        with pytest.raises(WebexportException):
            _filer_save(
                write_directory="h:\\dev\\tnt\\Local\\webexport",
                code_changelist=BASIC_ARGS[9],
                data_changelist=BASIC_ARGS[13],
                branch_name=BASIC_ARGS[11],
            )

    @patch("dice_elipy_scripts.webexport._get_webexport_module", MagicMock())
    @patch("dice_elipy_scripts.webexport._fetch_binaries", MagicMock())
    @patch("dice_elipy_scripts.webexport._import_previous_avalanche_state", MagicMock())
    @patch("dice_elipy_scripts.webexport._filer_save", MagicMock())
    @patch("dice_elipy_scripts.webexport._run_webexport")
    def test_cli_basic_args_branch_override_set(self, mock_run_webexport):
        ARGS = BASIC_ARGS + ["--branch-override", "virtual-stream-name"]
        runner = CliRunner()
        result = runner.invoke(cli, ARGS)
        assert result.exit_code == 0
        mock_run_webexport.assert_called_once_with(
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            "virtual-stream-name",
        )

    @patch("dice_elipy_scripts.webexport._get_webexport_module", MagicMock())
    @patch("dice_elipy_scripts.webexport._fetch_binaries", MagicMock())
    @patch("dice_elipy_scripts.webexport._import_previous_avalanche_state", MagicMock())
    @patch("dice_elipy_scripts.webexport._filer_save", MagicMock())
    @patch("dice_elipy_scripts.webexport._run_webexport")
    def test_cli_basic_args_branch_override_empty_string(self, mock_run_webexport):
        ARGS = BASIC_ARGS + ["--branch-override", ""]
        runner = CliRunner()
        result = runner.invoke(cli, ARGS)
        assert result.exit_code == 0
        mock_run_webexport.assert_called_once_with(
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            "testname",  # default BASIC_ARGS branch_name
        )

    @patch("dice_elipy_scripts.webexport._get_webexport_module", MagicMock())
    @patch("dice_elipy_scripts.webexport._fetch_binaries", MagicMock())
    @patch("dice_elipy_scripts.webexport._import_previous_avalanche_state", MagicMock())
    @patch("dice_elipy_scripts.webexport._filer_save", MagicMock())
    @patch("dice_elipy_scripts.webexport._run_webexport")
    def test_cli_basic_args_no_branch_override(self, mock_run_webexport):
        runner = CliRunner()
        result = runner.invoke(cli, BASIC_ARGS)
        assert result.exit_code == 0
        mock_run_webexport.assert_called_once_with(
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            ANY,
            "testname",  # default BASIC_ARGS branch_name
        )
