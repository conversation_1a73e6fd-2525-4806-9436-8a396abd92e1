"""
test_azure_filer_utils.py

Unit testing for azure_filer_utils
"""
from unittest.mock import MagicMock, patch

from dice_elipy_scripts.utils.azure_filer_utils import (
    _get_azure_fileshare_credentials,
    authenticate_filer,
)
from elipy2 import filer


class TestAzureFilerUtils:
    @patch("dice_elipy_scripts.utils.azure_filer_utils._get_azure_fileshare_credentials")
    @patch("elipy2.filer_paths.get_build_share_path")
    def test_authenticate_filer(
        self, mock_get_build_share_path, mock_get_azure_fileshare_credentials
    ):
        mock_get_build_share_path.return_value = "path"
        mock_get_azure_fileshare_credentials.return_value = "username", "password"
        mock_filer = filer.FilerUtils()
        mock_filer.delete_network_connection = MagicMock()
        mock_auth_network_connection = mock_filer.auth_network_connection = MagicMock()
        returned_filer = authenticate_filer(mock_filer, "glacier_azure_fileshare")
        mock_auth_network_connection.assert_called_once_with(
            network_path="path", username="username", password="password"
        )
        assert returned_filer == mock_filer

    @patch("elipy2.secrets.get_secrets")
    def test_get_azure_fileshare_credentials(self, mock_get_secrets):
        mock_get_secrets.return_value = {
            "path": {"AZURE_FILESHARE_USERNAME": "u", "AZURE_FILESHARE_PASSWORD": "p"}
        }
        user, password = _get_azure_fileshare_credentials("glacier_azure_fileshare")
        assert user == "u"
        assert password == "p"
