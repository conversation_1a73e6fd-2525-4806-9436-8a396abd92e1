{#
    Command:
        gametool
            short_help: Build the Icepick gametool.
            context_settings: dict(ignore_unknown_options=True)

    Arguments:
        package

    Required variables:
        code_changelist
            required: True
            help: Which code changelist to use.

    Optional variables:
        config
            default: release
            help: Config
        clean
            type: click.BOOL
            default: False
            help: Delete TnT/Local
        licensee
            multiple: True
            default: None
            help: Frostbite Licensee to use
        framework_args
            multiple: True
            help: Framework arguments for gen sln.
        submit
            type: click.BOOL
            default: True
            help: Set this to false for dry-run
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    gametool
    package {{ package }}
    --code-changelist {{ code_changelist }}
    {%- if config %}
    --config {{ config }}
    {%- endif %}
    {%- if clean %}
    --clean {{ clean }}
    {%- endif %}
    {%- if licensee %}
    --licensee {{ licensee }}
    {%- endif %}
    {%- if framework_args %}
    --framework-args {{ framework_args }}
    {%- endif %}
    {%- if submit %}
    --submit {{ submit }}
    {%- endif %}
  displayName: elipy gametool
