package com.ea.lib.jobs

import com.ea.lib.LibJobDsl
import com.ea.lib.jobsettings.SpartaSettings

class LibSparta {
    /**
     * Adds generic job parameters for sparta start jobs.
     */
    static void sparta_start(def job, def project, def branch_info, def masterFile, String branchName) {
        SpartaSettings spartaSettings = new SpartaSettings()
        spartaSettings.initializeSpartaStart(branch_info, masterFile, project, branchName)
        job.with {
            description(spartaSettings.description)
            disabled(spartaSettings.isDisabled)
            logRotator(7, 100)
            quietPeriod(0)
            properties {
                disableConcurrentBuilds()
                disableResume()
            }
            parameters {
                stringParam {
                    name('source_bundle_path')
                    defaultValue('\\\\filer.dice.ad.ea.com\\')
                    description('Specify where the bundles are coming from e.g. \\\\filer.dice.ad.ea.com\\Builds\\Sparta\\Offline\\master-21343\\casablanca.')
                    trim(true)
                }
                stringParam {
                    name('bundle_changelist')
                    defaultValue('1337')
                    description('Specify the Sparta bundle changelist e.g. 21343.')
                    trim(true)
                }
                stringParam {
                    name('description')
                    defaultValue('description')
                    description('Description for the P4 Submit e.g. https://jira.dice.se/browse/CAS-123456, no spaces allowed.')
                    trim(true)
                }
                booleanParam('no_submit', false, 'Specifies whether or not to submit to P4. Default is to submit, ticking the box will ensure it is not submitted.')
            }
            environmentVariables {
                env('branch_name', spartaSettings.branchName)
                env('project_name', spartaSettings.projectName)
            }
        }
    }

    /**
     * Adds generic job parameters for sparta bundle jobs.
     */
    static void sparta_bundle_job(def job, def project, def branchInfo, def masterFile, String branchName) {
        SpartaSettings spartaSettings = new SpartaSettings()
        spartaSettings.initializeSpartaBundleJob(branchInfo, masterFile, project, branchName)
        job.with {
            description(spartaSettings.description)
            label(spartaSettings.jobLabel)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(spartaSettings.workspaceRoot)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specify which revision to sync the P4 workspace to.')
                    trim(true)
                }
                stringParam {
                    name('source_bundle_path')
                    defaultValue('\\\\filer.dice.ad.ea.com\\')
                    description('Specify where the bundles are coming from e.g. \\\\filer.dice.ad.ea.com\\Builds\\Sparta\\Offline\\master-21343\\casablanca.')
                    trim(true)
                }
                stringParam {
                    name('bundle_changelist')
                    defaultValue('1337')
                    description('Specify the Sparta bundle changelist e.g. 21343.')
                    trim(true)
                }
                stringParam {
                    name('description')
                    defaultValue('description')
                    description('Description for the P4 Submit e.g. https://jira.dice.se/browse/CAS-123456, no spaces allowed.')
                    trim(true)
                }
                booleanParam('no_submit', false, 'Specify whether or not to submit to P4. Default is to submit, ticking the box will ensure it is not submitted.')
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(spartaSettings.buildName)
                timeout {
                    absolute(spartaSettings.timeoutMinutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, spartaSettings.elipyInstallCall, project)
                batchFile(spartaSettings.elipyCmd)
            }
        }
    }
}
