import com.ea.lib.jobsettings.DataSnooperSettings
import spock.lang.Specification

class DataSnooperSettingsSpec extends Specification {
    class BranchFile {
        static Map standard_jobs_settings = [
            datasnooper_reference_job  : 'a-reference-job',
            disable_build_datasnooper  : true,
            trigger_string_datasnooper : 'cron',
            workspace_root             : 'workspace-root',
            elipy_call                 : 'elipy-call',
            elipy_install_call         : 'elipy-install-call',
            job_label_statebuild       : 'job-label',
            timeout_hours_datasnooper  : 8,
            datasnooper_dbmanifest_path: 'path',
        ]
        static Map general_settings = [
            dataset: 'a-dataset',
        ]
    }

    class MasterFile {
        static Map branches = [
            'branch': [code_folder: 'dev', code_branch: 'code_branch', data_folder: 'dev', data_branch: 'data_branch', non_virtual_data_folder: 'non-virtual-folder', non_virtual_data_branch: 'non-virtual-branch'],
        ]
    }

    class ProjectFile {
        static String name = 'Kingston'
        static boolean frostbite_syncer_setup = true
    }

    void "test that we get expected job settings in initializeDataSnooperStart"() {
        when:
        DataSnooperSettings dataSnooperSettings = new DataSnooperSettings()
        dataSnooperSettings.initializeDataSnooperStart(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(dataSnooperSettings) {
            dataSnooperReferenceJob == BranchFile.standard_jobs_settings.datasnooper_reference_job
            isDisabled == BranchFile.standard_jobs_settings.disable_build_datasnooper
            frostbiteSyncerSetup == ProjectFile.frostbite_syncer_setup
            nonVirtualDataBranch == 'non-virtual-branch'
            nonVirtualDataFolder == 'non-virtual-folder'
            cronTrigger == BranchFile.standard_jobs_settings.trigger_string_datasnooper
            description == 'Runs datasnooper using ELIPY2.'
            branchName == 'branch'
            dataBranch == 'data_branch'
            dataFolder == 'dev'
            dataset == BranchFile.general_settings.dataset
            projectName == ProjectFile.name
        }
    }

    void "test that we get expected job settings in initializeDataSnooperJob"() {
        when:
        DataSnooperSettings dataSnooperSettings = new DataSnooperSettings()
        dataSnooperSettings.initializeDataSnooperJob(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(dataSnooperSettings) {
            jobLabel == BranchFile.standard_jobs_settings.job_label_statebuild
            timeoutMinutes == BranchFile.standard_jobs_settings.timeout_hours_datasnooper * 60
            description == 'Runs datasnooper using ELIPY2.'
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} datasnooper --dbmanifest-path ${BranchFile.standard_jobs_settings.datasnooper_dbmanifest_path}"
        }
    }

}
