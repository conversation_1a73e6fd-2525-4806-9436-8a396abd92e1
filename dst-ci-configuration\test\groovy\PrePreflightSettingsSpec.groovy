import com.ea.lib.jobsettings.PrePreflightSettings
import spock.lang.Specification

class BranchFile {
    static Map general_settings = [:]
    static Map standard_jobs_settings = [
        'workspace_root'    : 'workspace_root',
        'elipy_install_call': 'elipy_install',
        'elipy_setup_call'  : 'elipy_setup',
        'elipy_call'        : 'elipy_call',
        'code_branch'       : 'code_branch',
        'data_branch'       : 'data_branch',
        'dataset'           : 'data_set',
    ]
    static Map preflight_settings = [:]
    static List data_preflight_matrix = [
        [name: 'xbsx', platform: 'xbsx', assets: ['ShippingLevels'], extra_label: ''],
        [name: 'ps5', platform: 'ps5', assets: ['ShippingLevels'], extra_label: ''],
    ]
}

class MasterFile {
    static Map preflight_branches = ['dice-next': [code_folder: 'dev', code_branch: 'dice-next', data_folder: 'dev', data_branch: 'dice-next'],]
}

class ProjectFile {
    static String dataset = 'data_set'
    static String p4_code_server = 'dice-p4buildedge02-fb.dice.ad.ea.com:2001'
    static String p4_code_client = 'jenkins-${NODE_NAME}-codestream'
    static String p4_code_client_env = 'jenkins-%NODE_NAME%-codestream'

    static String p4_data_server = 'p4-tunguska-build01.dice.ad.ea.com:2001'
    static String p4_data_client = 'jenkins-${NODE_NAME}-' + dataset + 'stream'
    static String p4_data_client_env = 'jenkins-%NODE_NAME%-' + dataset + 'stream'
}

class PrePreflightSettingsSpec extends Specification {
    void "prePreflight: test that we get the expected job settings"() {
        when:
        def result = new PrePreflightSettings()
        result.initialize(BranchFile, MasterFile, ProjectFile, 'xbsx', 'dice-next', '123', '456')

        then:
        with(result) {
            description == 'Runs if preflight machine is idle for too long.'
            timeoutMinutes == 120
            workspaceRoot == 'workspace_root'
            concurrentBuilds == 10
            elipyCmd == 'elipy_call pre_preflight --code-branch dice-next --code-changelist %code_changelist% --data-branch dice-next --data-changelist %data_changelist% --platform %platform% --asset PreflightLevels.dbx --server-asset ShippingLevels --datadir data_set --p4-client jenkins-%NODE_NAME%-data_setstream --p4-user %P4_USER% --p4-port p4-tunguska-build01.dice.ad.ea.com:2001'
            elipyInstallCall == 'elipy_install'
        }
    }
}
