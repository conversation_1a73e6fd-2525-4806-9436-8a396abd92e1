from pathlib import Path

from elipy2 import reqs_utils

setup_py_path = Path(__file__).parent.parent.parent / "setup.py"
req_txt_path = Path(__file__).parent.parent.parent / "requirements.txt"

ALLOWED_EXCEPTIONS = [
    "editorconfig-checker",
    "sentry-sdk",
    "tox",
    "pytest-mock",
    "m2r2",
    "Sphinx",
    "mistune",
    "docutils",
    "sphinx-rtd-theme",
    "requests-mock",
    "black",
    "PyYAML",
    "mock",
    "wheel",
    "elasticsearch",
    "requests",
    "retry",
]


def test_elipy_scripts_requirements_txt_aligned_with_elipy_scripts_setup_py():
    """
    Audit parity between elipy-scripts requirements.txt and setup.py
    """
    req_parser = reqs_utils.RequirementsParser(
        setup_py_path=setup_py_path,
        req_txt_path=req_txt_path,
        allowed_exceptions=ALLOWED_EXCEPTIONS,
    )
    req_parser.test_requirements_txt_aligned_with_setup_py()
