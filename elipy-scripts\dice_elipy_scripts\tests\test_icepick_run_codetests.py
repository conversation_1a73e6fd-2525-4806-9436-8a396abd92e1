"""
test_icepick_run_codetests.py

Unit testing for icepick_run_codetests
"""
import unittest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import patch, MagicMock
from dice_elipy_scripts.icepick_run_codetests import cli
from elipy2.exceptions import ELIPYException

import os


def join(path, *args):
    return path + "\\" + ("\\".join(args))


@patch("dice_elipy_scripts.utils.code_utils", MagicMock())
@patch("os.path.join", MagicMock(side_effect=join))
@patch("elipy2.core.ensure_p4_config", MagicMock())
@patch("dice_elipy_scripts.icepick_run_codetests.run_gensln", MagicMock())
@patch("dice_elipy_scripts.icepick_run_codetests.add_sentry_tags", MagicMock())
@patch("dice_elipy_scripts.icepick_run_codetests.data.DataUtils", MagicMock())
@patch.multiple(
    "dice_elipy_scripts.icepick_run_codetests.frostbite_core",
    minimum_fb_version=MagicMock(return_value=True),
    get_game_data_dir=MagicMock(return_value="\\some\\path"),
    get_licensee_id=MagicMock(return_value="some-licensee"),
)
@patch("elipy2.build_metadata.BuildMetadataManager", MagicMock())
@patch("dice_elipy_scripts.icepick_run_codetests.install_required_sdks", MagicMock())
@patch(
    "dice_elipy_scripts.icepick_run_codetests.set_licensee",
    MagicMock(return_value=None),
)
class TestIcepickRunCodeTests(unittest.TestCase):
    OPTION_TEST_SUITES = "--ts"
    OPTION_TEST_SUITES_JSON = "--tsj"
    OPTION_CODE_BRANCH = "--cb"
    OPTION_CODE_CHANGELIST = "--cc"
    OPTION_DATA_CHANGELIST = "--dc"
    OPTION_DATA_BRANCH = "--db"
    OPTION_TEST_DEFINITION = "--td"
    OPTION_DATA_DIR = "--dd"
    OPTION_CONFIG = "-c"
    OPTION_BUILD_TYPE = "--bt"
    OPTION_TEST_GROUP = "--tg"
    OPTION_SETTINGS_FILE = "--sf"
    OPTION_SHOW_TEST_RESULTS = "--show-test-results"
    OPTION_ADDITIONAL_TOOLS_TO_INCLUDE = "--atti"

    VALUE_PLATFORM = "win64"
    VALUE_TEST_SUITES_01 = '{"name":"KingstonUnitTests"}'
    VALUE_TEST_SUITES_02 = '{"name":"KingstonIntegrationTests"}'
    VALUE_TEST_SUITES_JSON = os.path.join(
        os.path.dirname(__file__), "data", "testSuitesCodeTests.json"
    )
    VALUE_CODE_BRANCH = "code-branch"
    VALUE_CODE_CHANGELIST = "123"
    VALUE_DATA_CHANGELIST = "now"
    VALUE_DATA_BRANCH = "data-branch"
    VALUE_TEST_DEFINITION = "tooltests"
    VALUE_DATA_DIR = "kindata"
    VALUE_CONFIG = "release"
    VALUE_BUILD_TYPE = "dll"
    VALUE_TEST_GROUP = "tooltests"
    VALUE_SETTINGS_FILE = "Config/Icepick/IcepickSettings.ini"
    VALUE_SHOW_TEST_RESULTS = "True"
    VALUE_ADDITIONAL_TOOLS_TO_INCLUDE_01 = "frostedtests"
    VALUE_ADDITIONAL_TOOLS_TO_INCLUDE_02 = "pipeline"

    VALUE_RUN_ARGS = ["--no-key-press", "true"]

    DEFAULT_ARGS = [
        VALUE_PLATFORM,
        OPTION_CODE_BRANCH,
        VALUE_CODE_BRANCH,
        OPTION_CODE_CHANGELIST,
        VALUE_CODE_CHANGELIST,
        OPTION_DATA_CHANGELIST,
        VALUE_DATA_CHANGELIST,
        OPTION_DATA_BRANCH,
        VALUE_DATA_BRANCH,
        OPTION_DATA_DIR,
        VALUE_DATA_DIR,
        OPTION_BUILD_TYPE,
        VALUE_BUILD_TYPE,
        OPTION_CONFIG,
        VALUE_CONFIG,
        OPTION_SETTINGS_FILE,
        VALUE_SETTINGS_FILE,
        OPTION_TEST_DEFINITION,
        VALUE_TEST_DEFINITION,
        OPTION_TEST_GROUP,
        VALUE_TEST_GROUP,
        OPTION_ADDITIONAL_TOOLS_TO_INCLUDE,
        VALUE_ADDITIONAL_TOOLS_TO_INCLUDE_01,
        OPTION_ADDITIONAL_TOOLS_TO_INCLUDE,
        VALUE_ADDITIONAL_TOOLS_TO_INCLUDE_02,
    ]

    DEFAULT_ARGS_WITH_TEST_SUITE_JSON = DEFAULT_ARGS + [
        OPTION_TEST_SUITES_JSON,
        VALUE_TEST_SUITES_JSON,
    ]
    DEFAULT_ARGS_WITH_TEST_SUITE_ARGS = DEFAULT_ARGS + [
        OPTION_TEST_SUITES,
        VALUE_TEST_SUITES_01,
        OPTION_TEST_SUITES,
        VALUE_TEST_SUITES_02,
    ]

    def setUp(self):
        self.patcher_IcepickUtils = patch("dice_elipy_scripts.icepick_run.icepick.IcepickUtils")
        mock_IcepickUtils = self.patcher_IcepickUtils.start()
        self.mock_run_icepick = MagicMock()
        mock_IcepickUtils().run_icepick = self.mock_run_icepick

    def tearDown(self):
        self.patcher_IcepickUtils.stop()

    def test_icepick_run_codetests(self):
        runner = CliRunner()
        result = runner.invoke(cli, TestIcepickRunCodeTests.DEFAULT_ARGS_WITH_TEST_SUITE_JSON)
        assert result.exit_code == 0

    @patch("dice_elipy_scripts.icepick_run_codetests.run_icepick_test_suites")
    def test_icepick_run_codetests_show_test_results(self, mock_run_icepick_test_suites):
        mock_run_icepick_test_suites.return_value = (False, "some result")
        runner = CliRunner()
        result = runner.invoke(
            cli,
            TestIcepickRunCodeTests.DEFAULT_ARGS_WITH_TEST_SUITE_JSON
            + [self.OPTION_SHOW_TEST_RESULTS, self.VALUE_SHOW_TEST_RESULTS],
        )
        assert result.exit_code == 0

    @patch("dice_elipy_scripts.icepick_run_codetests.run_icepick_test_suites")
    def test_icepick_run_codetests_show_test_results_failure(self, mock_run_icepick_test_suites):
        mock_run_icepick_test_suites.return_value = (True, "some result")
        runner = CliRunner()
        result = runner.invoke(
            cli,
            TestIcepickRunCodeTests.DEFAULT_ARGS_WITH_TEST_SUITE_JSON
            + [self.OPTION_SHOW_TEST_RESULTS, self.VALUE_SHOW_TEST_RESULTS],
        )
        assert result.exit_code == 1

    def test_icepick_run_multiple_licensee(self):
        TestIcepickRunCodeTests.DEFAULT_ARGS_WITH_TEST_SUITE_JSON += ["-l", "some-licensee1"]
        TestIcepickRunCodeTests.DEFAULT_ARGS_WITH_TEST_SUITE_JSON += ["-l", "some-licensee2"]
        runner = CliRunner()
        result = runner.invoke(cli, TestIcepickRunCodeTests.DEFAULT_ARGS_WITH_TEST_SUITE_JSON)
        assert result.exit_code == 0

    def test_icepick_run_args(self):
        runner = CliRunner()
        result = runner.invoke(cli, TestIcepickRunCodeTests.DEFAULT_ARGS_WITH_TEST_SUITE_JSON)

        kwargs = {
            "platform": self.VALUE_PLATFORM,
            "test_suite": "KingstonUnitTests",
            "test_group": "tooltests",
            "config": "release",
            "settings_file_list": ["Config/Icepick/IcepickSettings.ini"],
            "send_frosting_report": True,
            "lease": None,
            "build_type": "dll",
            "autobuild": False,
            "run_args": [],
            "ignore_icepick_exit_code": False,
            "cook": False,
            "extra_framework_args": None,
            "custom_test_suite_data": "code_changelist:123;data_changelist:now;",
        }
        self.mock_run_icepick.assert_any_call(**kwargs)
        kwargs["test_suite"] = "KingstonIntegrationTests"
        self.mock_run_icepick.assert_called_with(**kwargs)

        assert result.exit_code == 0

    def test_icepick_run_args_with_test_suite_args(self):
        runner = CliRunner()
        result = runner.invoke(cli, TestIcepickRunCodeTests.DEFAULT_ARGS_WITH_TEST_SUITE_ARGS)

        kwargs = {
            "platform": self.VALUE_PLATFORM,
            "test_suite": "KingstonUnitTests",
            "test_group": "tooltests",
            "config": "release",
            "settings_file_list": ["Config/Icepick/IcepickSettings.ini"],
            "send_frosting_report": True,
            "lease": None,
            "build_type": "dll",
            "autobuild": False,
            "run_args": [],
            "ignore_icepick_exit_code": False,
            "cook": False,
            "extra_framework_args": None,
            "custom_test_suite_data": "code_changelist:123;data_changelist:now;",
        }
        self.mock_run_icepick.assert_any_call(**kwargs)
        kwargs["test_suite"] = "KingstonIntegrationTests"
        self.mock_run_icepick.assert_called_with(**kwargs)

        assert result.exit_code == 0

    def test_icepick_run_args_with_no_test_suite(self):
        runner = CliRunner()
        result = runner.invoke(cli, TestIcepickRunCodeTests.DEFAULT_ARGS)

        assert type(result.exception) == ELIPYException
        assert "Test suite information needs to be provided" in str(result.exception)
        assert result.exit_code != 0

    def test_icepick_run_args_custom_test_suite_data(self):
        CUSTOM_ARGS = TestIcepickRunCodeTests.DEFAULT_ARGS_WITH_TEST_SUITE_JSON + [
            "--ctsd",
            "mykey:myvalue",
            "--ic",
            True,
        ]
        runner = CliRunner()
        result = runner.invoke(cli, CUSTOM_ARGS)

        kwargs = {
            "platform": self.VALUE_PLATFORM,
            "test_suite": "KingstonUnitTests",
            "test_group": "tooltests",
            "config": "release",
            "settings_file_list": ["Config/Icepick/IcepickSettings.ini"],
            "send_frosting_report": True,
            "lease": None,
            "build_type": "dll",
            "autobuild": False,
            "run_args": [],
            "ignore_icepick_exit_code": False,
            "cook": True,
            "extra_framework_args": None,
            "custom_test_suite_data": "code_changelist:123;data_changelist:now;mykey:myvalue;",
        }
        self.mock_run_icepick.assert_any_call(**kwargs)
        kwargs["test_suite"] = "KingstonIntegrationTests"
        self.mock_run_icepick.assert_called_with(**kwargs)

        assert result.exit_code == 0

    @patch("dice_elipy_scripts.icepick_run_codetests.FilerUtils")
    def test_fetch_code_failure(self, mock_filer_utils):
        mock_filer_utils.return_value.fetch_code.side_effect = ELIPYException("error")
        args = TestIcepickRunCodeTests.DEFAULT_ARGS_WITH_TEST_SUITE_JSON + [
            "--use-existing-filer-build",
            "true",
        ]
        runner = CliRunner()
        result = runner.invoke(cli, args)
        assert result.exit_code != 0

    @patch("dice_elipy_scripts.icepick_run_codetests.FilerUtils", MagicMock())
    def test_fetch_code_ok(self):
        args = TestIcepickRunCodeTests.DEFAULT_ARGS_WITH_TEST_SUITE_JSON + [
            "--use-existing-filer-build",
            "true",
        ]
        runner = CliRunner()
        result = runner.invoke(cli, args)
        assert result.exit_code == 0

    @patch("dice_elipy_scripts.icepick_run_codetests.FilerUtils", MagicMock())
    @patch("dice_elipy_scripts.utils.code_utils")
    def test_fetch_code_called_on_uefb_flag(self, mock_code_utils):
        args = TestIcepickRunCodeTests.DEFAULT_ARGS_WITH_TEST_SUITE_JSON + [
            "--use-existing-filer-build",
            "true",
        ]
        runner = CliRunner()
        result = runner.invoke(cli, args)
        assert result.exit_code == 0
        assert mock_code_utils.call_count == 0
        args = TestIcepickRunCodeTests.DEFAULT_ARGS_WITH_TEST_SUITE_JSON + ["--uefb", "true"]
        runner = CliRunner()
        result = runner.invoke(cli, args)
        assert result.exit_code == 0
        assert mock_code_utils.call_count == 0
