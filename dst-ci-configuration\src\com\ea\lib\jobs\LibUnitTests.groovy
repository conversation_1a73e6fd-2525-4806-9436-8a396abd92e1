package com.ea.lib.jobs

import com.ea.lib.LibJobDsl
import com.ea.lib.jobsettings.UnitTestsSettings

class LibUnitTests {
    /**
     * Adds generic job parameters for start jobs.
     */
    static void start(def job, def project, def branchFile, def masterFile, String branchName) {
        def settings = new UnitTestsSettings()
        settings.initializeStart(branchFile, masterFile, project, branchName)
        job.with {
            description(settings.description)
            environmentVariables {
                env('BRANCH_NAME', settings.branchName)
                env('CODE_BRANCH', settings.codeBranch)
                env('CODE_FOLDER', settings.codeFolder)
                env('PROJECT_NAME', settings.projectName)
                env('NON_VIRTUAL_CODE_BRANCH', settings.nonVirtualCodeBranch)
                env('NON_VIRTUAL_CODE_FOLDER', settings.nonVirtualCodeFolder)
            }
            disabled(settings.isDisabled)
            logRotator(7, 100)
            parameters {
                stringParam {
                    name('CODE_CHANGELIST')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
            }
            properties {
                disableConcurrentBuilds()
                disableResume()
                pipelineTriggers {
                    triggers {
                        pollSCM {
                            scmpoll_spec(settings.cronTrigger)
                        }
                    }
                }
            }
            quietPeriod(0)
        }
    }

    /**
     * Adds generic job parameters for unittests.job jobs.
     */
    static void job(def job, def project, def branchFile, def masterFile, String branchName) {
        def settings = new UnitTestsSettings()
        settings.initializeJob(branchFile, masterFile, project, branchName)
        job.with {
            description(settings.description)
            label(settings.jobLabel)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(settings.workspaceRoot)
            parameters {
                stringParam {
                    name('CODE_CHANGELIST')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(settings.buildName)
                timeout {
                    absolute(settings.timeoutMinutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
                credentialsBinding {
                    if (settings.userCredentials) {
                        usernamePassword('monkey_email', 'monkey_passwd', settings.userCredentials)
                    }
                    if (settings.fbLoginDetails.p4_creds) {
                        usernamePassword('fb_p4_user', 'fb_p4_passwd', settings.fbLoginDetails.p4_creds as String)
                    }
                }
            }
            steps {
                if (settings.fbLoginDetails) {
                    batchFile("echo %fb_p4_passwd%|p4 -p ${settings.fbLoginDetails.p4_port} -u %fb_p4_user% login & exit 0")
                }
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                batchFile(settings.elipyCmd)
            }
        }
    }
}
