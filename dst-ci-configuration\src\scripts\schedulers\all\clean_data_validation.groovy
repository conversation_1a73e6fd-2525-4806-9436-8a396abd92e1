package scripts.schedulers.all

import com.ea.lib.LibJenkins
import com.ea.project.GetBranchFile

def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
def project = ProjectClass(env.project_name)

/**
 * clean_data_validation.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Get changelist from Perforce') {
            steps {
                script {
                    def settings_map = branchfile.general_settings + branchfile.standard_jobs_settings
                    P4PreviewData(project, 'stream', env.data_folder, env.data_branch, env.non_virtual_data_folder, env.non_virtual_data_branch, settings_map)
                }
            }
        }
        stage('Trigger clean data validation jobs') {
            steps {
                script {
                    def last_good_code = LibJenkins.getLastStableCodeChangelist(env.data_reference_job)
                    code_changelist = params.code_changelist ?: last_good_code
                    data_changelist = params.data_changelist ?: env.P4_CHANGELIST
                    def clean_data = 'True'

                    if (code_changelist == null) {
                        echo 'Missing code changelist, aborting build!'
                        currentBuild.result = Result.FAILURE.toString()
                        return
                    }

                    def args = [
                        string(name: 'code_changelist', value: code_changelist),
                        string(name: 'data_changelist', value: data_changelist),
                        string(name: 'clean_data', value: clean_data),
                    ]

                    def inject_map = [
                        'code_changelist': code_changelist,
                        'data_changelist': data_changelist,
                    ]
                    EnvInject(currentBuild, inject_map)
                    currentBuild.displayName = env.JOB_NAME + '.' + data_changelist + '.' + code_changelist

                    def data_matrix = branchfile.data_matrix

                    def jobs = [:]
                    final_result = Result.SUCCESS
                    for (platform in data_matrix) {
                        Boolean allow_failure = false
                        def platform_name = platform
                        def nightly_clean_build = false

                        if (platform instanceof Map) {
                            allow_failure = platform.allow_failure ?: false
                            nightly_clean_build = platform.nightly_clean_build ?: false
                            platform_name = platform.name
                        }

                        if (nightly_clean_build == false) {
                            continue
                        }

                        def job_name = env.branch_name + '.' + env.dataset + '.clean-data-validation.' + platform_name
                        jobs[job_name] = {
                            def downstream_job = build(job: job_name, parameters: args, propagate: false)
                            if (allow_failure == false) {
                                final_result = final_result.combine(Result.fromString(downstream_job.result))
                            }
                            LibJenkins.printFailureMessage(this, downstream_job, allow_failure)
                            LibJenkins.printRunningJobs(this)
                        }
                    }
                    parallel(jobs)
                    currentBuild.result = final_result.toString()

                    def slack_settings = branchfile.standard_jobs_settings?.slack_channel_clean_data_validation
                    SlackMessageNew(currentBuild, slack_settings, project.short_name)

                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
}
