"""
Wrapper around 'dbxmerge.exe'
"""
import os
import re
import subprocess
import tempfile

from elipy2 import LOGGER


class DBXMerge:
    """
    Wraps '%GAME_ROOT%/TnT/Bin/DbxCompare/dbxmerge.exe' into a .bat-file and
    runs it using 'p4 resolve' and P4MERGE environment variable.
    """

    _EXITCODE_PREFIX = "DBXMERGE: exitcode: "
    _re_exitcode = re.compile(f"{_EXITCODE_PREFIX}(?P<exitcode>-?\\d+)\n$")

    executable = os.path.abspath(
        os.path.join(os.environ.get("GAME_ROOT", ""), r"TnT\Bin\DbxCompare\dbxmerge.exe")
    )
    if not os.path.isfile(executable):
        executable = None

    def __init__(self, port=None, user=None, client=None, executable=None, args="-safemerge"):
        p4_resolve = ["p4.exe", "-q"]
        if port:
            p4_resolve += ["-p", port]
        if user:
            p4_resolve += ["-u", user]
        if client:
            p4_resolve += ["-c", client]
        p4_resolve.append("resolve")
        self.p4_resolve = tuple(p4_resolve)

        if executable:
            self.executable = os.path.abspath(executable)
        if isinstance(args, (list, tuple)):
            args = " ".join(args)
        if args:
            args = f" {args}"

        LOGGER.info("DBXMERGE: Command line '%s%s'", self.executable, args)

        with tempfile.NamedTemporaryFile(
            "wt", prefix="dbxmerge-", suffix=".bat", delete=False
        ) as dbxmerge_bat:
            dbxmerge_bat.write(
                f"@call {self.executable}{args} %2 %3 %1 %4\n"
                f"@(echo {self._EXITCODE_PREFIX}%ERRORLEVEL%) >&2\n"
            )
        self.dbxmerge_bat = dbxmerge_bat.name

        self.env = dict(os.environ)
        self.env["P4MERGE"] = self.dbxmerge_bat

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.cleanup()

    def cleanup(self):
        """
        Cleanup function for a class instance.
        """
        try:
            os.remove(self.dbxmerge_bat)
        except FileNotFoundError:
            pass

    def __get_exitcode(self, fileobj):
        for line in fileobj:
            match = self._re_exitcode.search(line)
            if match:
                return int(match.group("exitcode"))
            LOGGER.info("DBXMERGE: %s", line.strip())
        return None

    def resolve(self, filepath, changelist="default"):
        """
        Calls 'p4 resolve' with P4MERGE set to the path to dbxmerge.exe.
        Returns True if there were no errors, otherwise returns False
        (which probably means the files have conflicts).
        """
        if not os.path.isfile(filepath):
            LOGGER.warning("DBXMERGE: Skipping '%s' - No such file", filepath)
            return False

        cmdline = list(self.p4_resolve)
        cmdline += ["-c", changelist, filepath]

        LOGGER.info("DBXMERGE: Resolving '%s'", filepath)

        with subprocess.Popen(
            cmdline,
            env=self.env,
            text=True,
            encoding="utf-8",
            errors="ignore",
            stdin=subprocess.PIPE,
            stderr=subprocess.PIPE,
            stdout=subprocess.DEVNULL,
        ) as perforce:
            try:
                perforce.stdin.write("m\n")
                perforce.stdin.flush()
                success = self.__get_exitcode(perforce.stderr) == 0
                perforce.stdin.write("ae\n" if success else "s\n")
                perforce.stdin.flush()
            except OSError:
                success = False

        if perforce.returncode != 0:
            LOGGER.error("DBXMERGE: Failed to run '%s'", " ".join(cmdline))
            return False

        return success
