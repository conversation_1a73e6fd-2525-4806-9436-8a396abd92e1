"""
backup_baseline.py

During release and live service we run this after vaulting a build, we
then make sure everything we have vaulted has the baseline stored.
This includes all bundles and state from Avalanche and the game itself.

General setup:
- Set platform(s) to backup baseline for.
- Set source path.
- Set destination path.
- Run robocopy to copy from source to destination.
"""

import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import core, filer_paths, LOGGER, build_metadata_utils
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics
from elipy2.exceptions import ELIPYException


@click.command(
    "backup_baseline",
    short_help="Backup a frosty build that will be used as a baseline later.",
)
@click.option(
    "--platform",
    help="Which platform to back up.",
    required=True,
    type=click.Choice(
        [
            "ps4",
            "ps5",
            "win64",
            "xb1",
            "xbsx",
            "all-but-gen5",
            "all-but-gen4",
            "all-but-win64",
            "all",
        ]
    ),
)
@click.option(
    "--baseline-data-branch",
    required=True,
    help="Which branch the baseline data came from.",
)
@click.option(
    "--baseline-data-changelist",
    required=True,
    help="Which changelist the baseline data came from.",
)
@click.option(
    "--baseline-code-branch",
    required=True,
    help="Which branch the baseline code came from.",
)
@click.option(
    "--baseline-code-changelist",
    required=True,
    help="Which changelist the baseline code came from.",
)
@click.option(
    "--build-location",
    default=None,
    help="Location where the build is stored (studio name).",
)
@click.option(
    "--additional-baseline-locations",
    default=None,
    help="Additional locations where the baseline will be stored (studio names).",
)
@click.option(
    "--dry-run",
    is_flag=True,
    help="Perform a dry run without actually doing anything.",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    platform,
    baseline_data_branch,
    baseline_data_changelist,
    baseline_code_branch,
    baseline_code_changelist,
    build_location,
    additional_baseline_locations,
    dry_run,
):
    """
    Backup a frosty build that will be used as a baseline later,
    to be sure we don't delete it during normal clean-up.

    This is done by copying a build from /filer/frosty to /filer/baseline.
    The copied build will be used as a baseline for the next patch.
    Baseline is not copied for server, since we don't build patches for server.
    """
    # Adding sentry tags
    add_sentry_tags(__file__)

    # Set platforms to back up baseline for.
    if platform == "all-but-gen5":
        platforms = ["ps4", "win64", "xb1"]
    elif platform == "all-but-gen4":
        platforms = ["ps5", "win64", "xbsx"]
    elif platform == "all-but-win64":
        platforms = ["ps4", "ps5", "xb1", "xbsx"]
    elif platform == "all":
        platforms = ["ps4", "ps5", "win64", "xb1", "xbsx"]
    else:
        platforms = [platform]

    exceptionlist = []

    # Used to register baseline in Bilbo
    metadata_manager = build_metadata_utils.setup_metadata_manager()

    for temp_platform in platforms:
        # If build_location is provided, use it directly as the location configuration
        location_config = None
        if build_location:
            location_config = build_location
            LOGGER.info("Using location configuration '{}'".format(location_config))

        source = filer_paths.get_frosty_base_build_path(
            data_changelist=baseline_data_changelist,
            code_changelist=baseline_code_changelist,
            data_branch=baseline_data_branch,
            code_branch=baseline_code_branch,
            platform=temp_platform,
            location=location_config,
        )
        dest_locations = [location_config]
        if additional_baseline_locations:
            dest_locations += additional_baseline_locations.split(",")

        for dest_location in dest_locations:
            dest_root = filer_paths.get_baseline_build_path(
                data_changelist=baseline_data_changelist,
                code_changelist=baseline_code_changelist,
                data_branch=baseline_data_branch,
                code_branch=baseline_code_branch,
                location=dest_location,
            )
            dest = os.path.join(dest_root, temp_platform)

            try:
                if not dry_run:
                    LOGGER.info(
                        "Copying {0} from {1} to {2} ..".format(temp_platform, source, dest)
                    )
                    copy_baseline_build(source, dest)
                else:
                    LOGGER.info(
                        "Dry run: Would copy {0} from {1} to {2} ..".format(
                            temp_platform, source, dest
                        )
                    )
            except Exception as exc:
                exceptionlist.append(exc)
            else:
                if not dry_run:
                    metadata_manager.register_baseline_build(
                        dest,
                        platform=temp_platform,
                        data_changelist=baseline_data_changelist,
                        data_branch=baseline_data_branch,
                        code_changelist=baseline_code_changelist,
                        code_branch=baseline_code_branch,
                    )

    if exceptionlist:
        for i in exceptionlist:
            LOGGER.error(i)
        raise ELIPYException("Failed to store all baselines.")


def copy_baseline_build(source, dest):
    """
    Copy baseline at source to archive at dest
    """
    try:
        file_check(source)
        core.robocopy(source, dest)
    except Exception as exc:
        raise exc


def file_check(path):
    """
    Check that path exists and contains files.
    """
    if not os.path.exists(path):
        raise ELIPYException("Path {} for storing baseline does not exist".format(path))
    total_files = sum([len(files) for _, _, files in os.walk(path)])
    if total_files < 100:
        raise ELIPYException(
            "Path: {}, contains only {} total file(s). Verify directory contents.".format(
                path, total_files
            )
        )
