{#
    Command:
        unittests
            short_help: Runs unit tests.
            context_settings: dict(ignore_unknown_options=True)

    Arguments:

    Required variables:
        password
            required: True
        email
            required: True

    Optional variables:
        domain_user
            default: None
            help: The user to authenticate to package server as DOMAIN\user
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    unittests
    --password {{ password }}
    --email {{ email }}
    {%- if domain_user %}
    --domain-user {{ domain_user }}
    {%- endif %}
  displayName: elipy unittests
