"""
test_test_runner.py

Unit testing for test_runner
"""
import unittest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import Magic<PERSON>ock, patch
from dice_elipy_scripts.test_runner import cli


@patch("dice_elipy_scripts.test_runner.code", MagicMock())
@patch("dice_elipy_scripts.test_runner.collect_metrics", MagicMock())
@patch("dice_elipy_scripts.test_runner.run_gensln", MagicMock())
class TestTestRunner(unittest.TestCase):
    OPTION_CUSTOM_CONFIG_1 = "--custom-configs"
    OPTION_CUSTOM_CONFIG_2 = "--custom-configs"
    OPTION_PASS_THROUGH = "--pass-through"

    VALUE_CUSTOM_CONFIG_1 = "task1.json"
    VALUE_CUSTOM_CONFIG_2 = "task2.json"
    VALUE_PASS_THROUGH_1 = "--gtest_repeat=10"
    VALUE_PASS_THROUGH_2 = "--gtest_break_on_failure"

    def setUp(self):
        self.patcher_get_runner = patch("dice_elipy_scripts.test_runner._get_test_runner_module")
        self.mock_get_runner = self.patcher_get_runner.start()
        self.mock_get_runner.return_value = MagicMock()

        self.patcher_set_licensee = patch("dice_elipy_scripts.test_runner.set_licensee")
        self.mock_set_licensee = self.patcher_set_licensee.start()
        self.mock_set_licensee.return_value = ["licensee_arg"]

        self.runner = CliRunner()

        self.default_arguments = {
            "projects": None,
            "variant": None,
            "attempts": None,
            "workers": None,
            "test_filters": None,
            "timeout": None,
            "pass_through": [],
            "nofrosting": False,
            "monkey": True,
        }

    def tearDown(self):
        patch.stopall()

    def test_one_custom_config(self):
        result = self.runner.invoke(
            cli,
            [
                self.OPTION_CUSTOM_CONFIG_1,
                self.VALUE_CUSTOM_CONFIG_1,
            ],
        )
        assert result.exit_code == 0
        self.mock_get_runner.return_value.main.assert_called_once_with(
            custom_config=self.VALUE_CUSTOM_CONFIG_1,
            **self.default_arguments,
        )

    def test_multiple_custom_config(self):
        result = self.runner.invoke(
            cli,
            [
                self.OPTION_CUSTOM_CONFIG_1,
                self.VALUE_CUSTOM_CONFIG_1,
                self.OPTION_CUSTOM_CONFIG_2,
                self.VALUE_CUSTOM_CONFIG_2,
            ],
        )
        assert result.exit_code == 0
        assert self.mock_get_runner.return_value.main.call_count == 2
        self.mock_get_runner.return_value.main.assert_any_call(
            **{
                **self.default_arguments,
                "custom_config": self.VALUE_CUSTOM_CONFIG_1,
            },
        )
        self.mock_get_runner.return_value.main.assert_called_with(
            **{
                **self.default_arguments,
                "custom_config": self.VALUE_CUSTOM_CONFIG_2,
            },
        )

    def test_gtest_arguments_2(self):
        result = self.runner.invoke(
            cli,
            [
                self.OPTION_CUSTOM_CONFIG_1,
                self.VALUE_CUSTOM_CONFIG_1,
                self.OPTION_PASS_THROUGH,
                self.VALUE_PASS_THROUGH_1,
                self.OPTION_PASS_THROUGH,
                self.VALUE_PASS_THROUGH_2,
            ],
        )
        assert result.exit_code == 0
        self.mock_get_runner.return_value.main.assert_called_once_with(
            custom_config=self.VALUE_CUSTOM_CONFIG_1,
            **{
                **self.default_arguments,
                "pass_through": [
                    self.VALUE_PASS_THROUGH_1,
                    self.VALUE_PASS_THROUGH_2,
                ],
            },
        )
