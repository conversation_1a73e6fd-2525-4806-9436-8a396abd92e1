# Mandatory
default:
  script_path: # You can put your local dev path here when developing scripts
    - "D:\\gitLab\\elipy-scripts\\dice_elipy_scripts"
  project_name: "dev"
  studio_location: "DiceStockholm"
  bilbo_api_version: 2
  bilbo_url: "https://fake-bilbo-test.loki.dice.se"
  build_share: "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca"
#  fetch_xb_basepackage: "true"
  vault_verification_config_path: "vault_verification_config_test.yml"
  skip_frosty_game_config_flags: "true"
  shift_md5_skipped_files:
    - "*.pdb"
    - "some_file.txt"
  elsa_patch: "true"
  cleanup_retention_paths: "true"
  retention_categories:
      code:
        - 'default' :              5
        - 'branch1' :              6
        - 'branch2' :              7
        - 'branch3' :              8
        - 'branch4' :              9
      frosty\casablanca:
        - 'default' :               20
        - 'branch1' :               21
        - 'branch2' :               22
        - 'branch3' :               23
        - 'branch4' :               24
  azure_path_retention:
  - secret_context: "test_azure_fileshare"
    fileshares:
      - fileshare_name: "testfileshare1"
        paths:
          - path/to/dir: 10
      - fileshare_name: "testfileshare2"
        paths:
          - path/to/dir: 20

  snowcache_host:
    win64: fake-vm.fake.dre.dice.se

  recompression_cache:
    "win64": "server.address"

  avalanche_state_host:
    win64: 'avalanche.state.host'
    ps4: 'avalanche.state.host'
    ps5: 'avalanche.state.host'
    xb1: 'avalanche.state.host'
    xbsx: 'avalanche.state.host'
    linux64: 'avalanche.state.host'
    nx: 'avalanche.state.host'
    android: 'avalanche.state.host'
    ios: 'avalanche.state.host'
    win64server: 'avalanche.state.host'
    server: 'avalanche.state.host'

different_location:
  bilbo_api_version: 2
  bilbo_url: "https://fake-bilbo-test1.loki.dice.se"
  build_share: "\\\\fake-filer1.dice.ad.ea.com\\builds\\Casablanca"
  skip_frosty_game_config_flags: "false"

location_threshold_clean:
  enable_threshold_clean: true
