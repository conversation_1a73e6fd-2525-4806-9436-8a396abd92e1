package com.ea.lib.jobs

import com.ea.lib.LibJobDsl
import com.ea.lib.jobsettings.ShiftSettings
import javaposse.jobdsl.dsl.jobs.FreeStyleJob

class LibShift {
    /**
     * Adds generic job parameters for shift start jobs.
     */
    static void shift_start(def job, def project, def branchFile, def masterFile, String branchName) {
        // Get settings from ShiftSettings.
        ShiftSettings settings = new ShiftSettings()
        settings.initializeShiftStart(branchFile, masterFile, project, branchName)

        // Add sections to the Jenkins job.
        job.with {
            description(settings.description)
            disabled(settings.isDisabled)
            logRotator(7, 100)
            quietPeriod(0)
            throttleConcurrentBuilds {
                maxTotal(settings.concurrentBuilds)
            }
            properties {
                disableResume()
                pipelineTriggers {
                    triggers {
                        if (settings.triggerType == 'scm') {
                            pollSCM {
                                scmpoll_spec(settings.cronTrigger)
                            }
                        } else if (settings.triggerType == 'cron') {
                            cron {
                                spec(settings.cronTrigger)
                            }
                        }
                    }
                }
            }
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to sync.')
                    trim(true)
                }
            }
            environmentVariables {
                env('branch_name', settings.branchName)
                env('project_name', settings.projectName)
                env('shift_reference_job', settings.referenceJob)
            }
        }
    }

    /**
     * Add a start job for a specific shifter type.
     */
    static void shift_type_start(def job, String defaultShifterType, String defaultCurrentBranch) {
        job.with {
            parameters {
                stringParam {
                    name('shifter_type')
                    defaultValue(defaultShifterType)
                    trim(true)
                }
                stringParam {
                    name('branch_name')
                    defaultValue(defaultCurrentBranch)
                    trim(true)
                }
            }
        }
    }

    /**
     * Adds generic job parameters for shift upload jobs.
     */
    static void shift_upload(def job, def project, def branchFile, def masterFile, String branchName, String ShifterType = null, Boolean codeOnly = false) {
        // Get settings from ShiftSettings.
        ShiftSettings settings = new ShiftSettings()
        settings.initializeShiftUpload(branchFile, masterFile, project, branchName, ShifterType, codeOnly)

        // Add sections to the Jenkins job.
        job.with {
            description(settings.description)
            label(settings.jobLabel)
            logRotator(7, 100)
            quietPeriod(0)
            concurrentBuild()
            customWorkspace(settings.workspaceRoot)
            throttleConcurrentBuilds {
                maxTotal(settings.concurrentBuilds)
            }
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                if (!codeOnly) {
                    stringParam {
                        name('data_changelist')
                        defaultValue('')
                        description('Specifies data changelist to sync.')
                        trim(true)
                    }
                }
                booleanParam {
                    name('force_reshift')
                    defaultValue(false)
                    description('Shift builds even though they are marked as shifted')
                }
                if (ShifterType == null || settings.SHIFTER_TYPE_FROSTY_SHIFTER == ShifterType) {
                    stringParam {
                        name('build_id')
                        defaultValue('None')
                        description('The BUILD_ID of the existing Shift build for incremental submission.')
                        trim(true)
                    }
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(settings.buildName)
                injectPasswords {
                    injectGlobalPasswords()
                    maskPasswords()
                }
                timeout {
                    absolute(settings.timeoutMinutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
                credentialsBinding {
                    usernamePassword('monkey_shift_email', 'monkey_shift_passwd', 'monkey.shift')
                    usernamePassword('artifactory_user', 'artifactory_apikey', 'artifactory-credentials')

                    if (settings.fbLoginDetails.p4_creds) {
                        usernamePassword('fb_p4_user', 'fb_p4_passwd', settings.fbLoginDetails.p4_creds)
                    }
                }
            }
            steps {
                if (settings.fbLoginDetails) {
                    batchFile("echo %fb_p4_passwd%|p4 -p ${settings.fbLoginDetails.p4_port} -u %fb_p4_user% login & exit 0")
                }
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                batchFile(settings.elipyCmd)
            }
        }
    }

    /**
     * Adds generic job parameters for shift upload jobs.
     */
    static void processShiftSubscriptionDownloads(FreeStyleJob job, def project, def branchFile, def masterFile, String branchName, Map shiftSubscriptionInfo) {
        // Get settings from ShiftSettings.
        ShiftSettings settings = new ShiftSettings()
        settings.initializeProcessShiftSubscriptionDownloads(branchFile, masterFile, project, branchName, shiftSubscriptionInfo)

        // Add sections to the Jenkins job.
        job.with {
            description(settings.description)
            label(settings.jobLabel)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(settings.workspaceRoot)
            if (settings.triggerType == 'cron') {
                triggers {
                    cron {
                        spec(settings.cronTrigger)
                    }
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(settings.buildName)
                injectPasswords {
                    injectGlobalPasswords()
                    maskPasswords()
                }
                timeout {
                    absolute(settings.timeoutMinutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
                credentialsBinding {
                    if (settings.fbLoginDetails.p4_creds) {
                        usernamePassword('fb_p4_user', 'fb_p4_passwd', settings.fbLoginDetails.p4_creds as String)
                    }
                }
            }
            steps {
                if (settings.fbLoginDetails) {
                    batchFile("echo %fb_p4_passwd%|p4 -p ${settings.fbLoginDetails.p4_port} -u %fb_p4_user% login & exit 0")
                }
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                batchFile(settings.elipyCmd)
            }
        }
    }
}
