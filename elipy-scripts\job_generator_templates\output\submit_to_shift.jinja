{#
    Command:
        submit_to_shift
            short_help: Submits a build to Shift.

    Arguments:

    Required variables:
        user
            help: User for Shift authentication
            required: True
        password
            help: Password for Shift authentication.
            required: True

    Optional variables:
        code_branch
            help: Branch/stream that the code/binary is coming from.
        code_changelist
            help: Changelist of binaries.
        data_branch
            help: Branch/stream that data is coming from.
        data_changelist
            help: Changelist of data being used.
        submission_path
            default: None
            help: Folder path to use to stage build. Shift service account needs access to this.
        use_bilbo/__no_bilbo
            default: True
            help: If passed, staged build will be registered in the configured metadata services.
        compression
            default: False
            help: Compress big supplemental files
        shift_url
            help: Override the default Shift URL.
            default: https://shift.ea.com
        submission_tool
            help: Override which submission tool gets used.
            default: None
        shifter_type
            help: Override which shifter type to use. Defaults to frosty_shifter.
            default: frosty_shifter
        force_reshift
            is_flag: True
            help: Shift builds even though they are marked as shifted in the configured metadata services.
        use_elipy_config
            is_flag: True
            help: Use shift elipy config instead of shift template.
        artifactory_user
            default: None
            help: Specify a user to download the shift submission tool from artifactory.
        artifactory_apikey
            default: None
            help: Specify an api key to download the shift submission tool from artifactory.
        use_zipped_drone_builds
            is_flag: True
            help: Use zipped drone builds, instead of loose files in a folder.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    submit_to_shift
    --user {{ user }}
    --password {{ password }}
    {%- if code_branch %}
    --code-branch {{ code_branch }}
    {%- endif %}
    {%- if code_changelist %}
    --code-changelist {{ code_changelist }}
    {%- endif %}
    {%- if data_branch %}
    --data-branch {{ data_branch }}
    {%- endif %}
    {%- if data_changelist %}
    --data-changelist {{ data_changelist }}
    {%- endif %}
    {%- if submission_path %}
    --submission-path {{ submission_path }}
    {%- endif %}
    {%- if use_bilbo/__no_bilbo %}
    --use-bilbo/--no-bilbo {{ use_bilbo/__no_bilbo }}
    {%- endif %}
    {%- if compression %}
    --compression {{ compression }}
    {%- endif %}
    {%- if shift_url %}
    --shift-url {{ shift_url }}
    {%- endif %}
    {%- if submission_tool %}
    --submission-tool {{ submission_tool }}
    {%- endif %}
    {%- if shifter_type %}
    --shifter-type {{ shifter_type }}
    {%- endif %}
    {%- if force_reshift %}
    --force-reshift {{ force_reshift }}
    {%- endif %}
    {%- if use_elipy_config %}
    --use-elipy-config {{ use_elipy_config }}
    {%- endif %}
    {%- if artifactory_user %}
    --artifactory-user {{ artifactory_user }}
    {%- endif %}
    {%- if artifactory_apikey %}
    --artifactory-apikey {{ artifactory_apikey }}
    {%- endif %}
    {%- if use_zipped_drone_builds %}
    --use-zipped-drone-builds {{ use_zipped_drone_builds }}
    {%- endif %}
  displayName: elipy submit_to_shift
