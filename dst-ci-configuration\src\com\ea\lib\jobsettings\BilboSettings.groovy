package com.ea.lib.jobsettings

import com.ea.lib.LibCommonNonCps

class BilboSettings extends JobSetting {

    String smokeDownstreamJob
    String elipyCmdNewLocation
    String dataset
    int retryLimit

    void initializeBilboDroneJob(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        def modifiers = ['bilbo', 'drone', 'job']
        jobLabel = LibCommonNonCps.get_setting_value(branchInfo, modifiers, 'job_label_statebuild', 'statebuild')
        def extraLocations = LibCommonNonCps.get_setting_value(branchInfo, modifiers, 'extra_locations', [])
        def stateBuildPatchFrosty = LibCommonNonCps.get_setting_value(branchInfo, modifiers, 'statebuild_patchfrosty', false)
        def stateBuildFrosty = LibCommonNonCps.get_setting_value(branchInfo, modifiers, 'statebuild_frosty', false)
        def notStateBuildFrosty = stateBuildPatchFrosty == false || stateBuildFrosty == false
        // makes it possible to set job_label_statebuild to 'data_branch util' and have it run on a util machine instead
        if (branchInfo.statebuild_data == false && branchInfo.job_label_statebuild == null && notStateBuildFrosty) {
            jobLabel = branchInfo.data_branch
        }

        def extraArgs = ''
        for (location in extraLocations) {
            extraArgs += " --extra-location ${location}"
        }

        description = "Registers a ${branchInfo.dataset} build in Drone."
        isDisabled = LibCommonNonCps.get_setting_value(branchInfo, modifiers, 'disable_build', false)
        buildName = '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'
        elipyCmd = "${this.elipyCall} bilbo_register_drone --dataset ${branchInfo.dataset}" +
            " --code-branch ${branchInfo.code_branch} --code-changelist %code_changelist%" +
            " --data-branch ${branchInfo.data_branch} --data-changelist %data_changelist% ${extraArgs}"
    }

    void initializeBilboDroneJobMoveLocationParallelDroneBuildStart(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        description = "Registers a ${branchInfo.dataset} build in Drone."
        retryLimit = branchInfo.retry_limit_bilbo_copy ?: 1
    }

    void initializeBilboDroneJobMoveLocationParallelLocationStart(def branchFile, def masterFile, def projectFile, String branchName, String location) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        description = "Registers a ${branchInfo.dataset} build in ${location} Drone."
        retryLimit = branchInfo.retry_limit_bilbo_copy ?: 1
    }

    void initializeBilboDroneJobMoveLocationParallelCopy(def branchFile, def masterFile, def projectFile, String branchName,
                                                         String platform, String config, String location) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        if (!branchInfo.new_locations[location]) {
            throw new IllegalArgumentException("The following setting cannot be null: new_locations[${location}]")
        }
        description = "Copies code for ${platform}.${config} to ${location}"
        elipyCmd = "${this.elipyCall} move_location_drone --parallel-copy" +
            " --code-branch ${branchInfo.code_branch} --code-changelist %code_changelist%" +
            " --dest-location ${location} --data-changelist %data_changelist%" +
            " --platform ${platform} --config ${config}"
    }

    void initializeBilboBundlesJobMoveLocation(def branchFile, def masterFile, def projectFile, String branchName, String platform, String bundleType, String location) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        def modifiers = ['bilbo']

        jobLabel = branchInfo.job_label_statebuild_bilbo ?: branchInfo.job_label_statebuild ?: LibCommonNonCps.get_setting_value(branchInfo, modifiers, 'job_label_statebuild', 'statebuild')
        description = "Move ${bundleType} for ${platform} from default location to ${location}."
        buildName = '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'
        isDisabled = LibCommonNonCps.get_setting_value(branchInfo, modifiers, 'disable_build', false)
        dataset = branchInfo.dataset

        retryLimit = branchInfo.retry_limit_bilbo_copy ?: 1

        // Command to copy the bundles from source to destination location
        elipyCmd = [
                this.elipyCall,
                'move_location_bundles',
                '--code-branch', "${branchInfo.code_branch}",
                '--code-changelist', '%code_changelist%',
                '--data-branch', "${branchInfo.data_branch}",
                '--data-changelist', '%data_changelist%',
                '--platform', "${platform}",
                '--dest-location', "${location}",
                '--bundle-type', "${bundleType}",
        ].join(' ')

        // Command to register the bundles in the new location
        elipyCmdNewLocation = [
                "${branchInfo?.new_locations[location]?.elipy_call_new_location ?: this.elipyCall}",
                'bilbo_register_bundles',
                '--code-branch', "${branchInfo.code_branch}",
                '--code-changelist', '%code_changelist%',
                '--data-branch', "${branchInfo.data_branch}",
                '--data-changelist', '%data_changelist%',
                '--platform', "${platform}",
                '--dataset', "${branchInfo.dataset}",
                '--bundle-type', "${bundleType}",
        ].join(' ')
    }

    void initializeBilboDroneJobMoveLocationParallelRemote(def branchFile, def masterFile, def projectFile, String branchName, String location) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        if (!branchInfo.new_locations[location]) {
            throw new IllegalArgumentException("The following setting cannot be null: new_locations[${location}]")
        }
        description = "Registers a ${branchInfo.dataset} build in Drone."
        def extraArgs = ''
        if (branchInfo.new_locations[location].additional_bilbo_instances) {
            for (bilboserver in branchInfo.new_locations[location].additional_bilbo_instances) {
                extraArgs += " --extra-location ${bilboserver}"
            }
        }
        elipyCmd = "${branchInfo.new_locations[location].elipy_call_new_location} bilbo_register_drone --dataset ${branchInfo.dataset}" +
            " --code-branch ${branchInfo.code_branch} --code-changelist %code_changelist%" +
            " --data-branch ${branchInfo.data_branch} --data-changelist %data_changelist% ${extraArgs}"
    }

    void initializeBilboDroneJobMoveLocationParallel() {
        String extraArgs = projectFile.frostbite_syncer_setup ? '--old-drone-setup' : ''
        def notStatebuildFrosty = branchInfo.statebuild_patchfrosty == false || branchInfo.statebuild_frosty == false
        jobLabel = branchInfo.job_label_statebuild ?: 'statebuild'
        if (branchInfo.statebuild_data == false && branchInfo.job_label_statebuild == null && notStatebuildFrosty) {
            jobLabel = branchInfo.data_branch
        }
        buildName = '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'
        elipyCmd = "${this.elipyCmd} ${extraArgs}"
    }

    void initializeBilboFrostyJobMoveLocation(def branchFile, def masterFile, def projectFile, String branchName, def variant, String platformName, String location) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        branchInfo += [platform: platformName]
        jobLabel = branchInfo.job_label_statebuild ?: 'statebuild'
        if (branchInfo.statebuild_frosty == false) {
            jobLabel = branchInfo.data_branch
        }
        description = "Registers a ${branchInfo.dataset} build in Drone."
        buildName = '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'

        def combineParams = ''
        if (variant.format.contains('combine')) {
            combineParams = ' --combine-code-branch %combine_code_branch% --combine-code-changelist %combine_code_changelist%' +
                ' --combine-data-branch %combine_data_branch% --combine-data-changelist %combine_data_changelist%'
        }

        elipyCmd = "${this.elipyCall} move_location_frosty" +
            " --code-branch ${branchInfo.code_branch} --code-changelist %code_changelist%" +
            " --data-branch ${branchInfo.data_branch} --data-changelist %data_changelist%" +
            " --dest-location ${location} --region ${variant.region} --platform ${branchInfo.platform}" +
            " --package-type ${variant.format} --config ${variant.config}" +
            combineParams

        elipyCmdNewLocation = "${branchInfo?.new_locations[location].elipy_call_new_location} bilbo_register_frosty" +
            " --code-branch ${branchInfo.code_branch} --code-changelist %code_changelist%" +
            " --data-branch ${branchInfo.data_branch} --data-changelist %data_changelist%" +
            " --region ${variant.region} --platform ${branchInfo.platform} --dataset ${branchInfo.dataset}" +
            " --package-type ${variant.format} --config ${variant.config}" +
            combineParams
    }

    void initializeBilboRegisterSmoke(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        jobLabel = branchInfo.job_label_statebuild ?: 'statebuild'
        smokeDownstreamJob = branchInfo.smoke_downstream_job
        buildName = '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'
        def extraArgs = ''
        if (branchInfo.register_smoke_multi_location) {
            branchInfo.new_locations?.keySet()?.each { location ->
                extraArgs += " --extra-location ${location}"
            }
        }

        elipyCmd = "${this.elipyCall} bilbo_register_smoke" +
            " --code-branch ${branchInfo.code_branch} --code-changelist %code_changelist% --data-changelist %data_changelist%${extraArgs}"
    }

    void initializeBilboRegisterVerifiedForPreflight(def branchFile, def masterFile, def projectFile, String branchName, String changelistParamName) {
        if (masterFile.autotest_branches) {
            this.init(branchFile, masterFile, projectFile, branchName, masterFile.autotest_branches as Map)
        } else {
            this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        }
        jobLabel = LibCommonNonCps.get_setting_value(branchInfo, ['bilbo'], 'job_label_statebuild', 'statebuild')
        if (branchInfo.statebuild_autotest == false) {
            jobLabel = 'bilbo'
        }
        description = 'Registers a build as verified_for_preflight bilbo.'
        buildName = '${JOB_NAME}.${ENV, var="' + changelistParamName + '"}'
        elipyCmd = "${this.elipyCall} bilbo_register_verified_for_preflight" +
            " --branch ${branchInfo.code_branch} --changelist %${changelistParamName}%"
    }

    void initializeBilboAutotest(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.autotest_branches as Map)
        jobLabel = LibCommonNonCps.get_setting_value(branchInfo, ['bilbo'], 'job_label_statebuild', 'statebuild')
        if (branchInfo.statebuild_autotest == false) {
            jobLabel = 'bilbo'
        }
        description = "Registers a ${branchInfo.dataset} build in Drone."
        buildName = '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'

        def extraArgs = ''
        branchInfo.new_locations?.keySet()?.each { location ->
            extraArgs += " --extra-location ${location}"
        }

        elipyCmd = "${this.elipyCall} bilbo_register_autotest" +
            " --code-branch ${branchInfo.code_branch} --code-changelist %code_changelist%" +
            " --data-branch ${branchInfo.data_branch} --data-changelist %data_changelist%" +
            ' --test-status %status% --run-bilbo %run_bilbo% --test-definition %test_definition% --register-smoke %register_smoke%' +
            extraArgs
    }

    void initializeBilboRegisterReleaseCandidate(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName)
        jobLabel = branchInfo.job_label_statebuild ?: 'statebuild'
        description = 'Registers a build as a release candidate in Bilbo.'
        buildName = '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'
        elipyCmd = "${this.elipyCall} bilbo_register_release_candidate" +
            ' --code-branch %code_branch% --code-changelist %code_changelist%' +
            ' --data-branch %data_branch% --data-changelist %data_changelist%'
    }
}
