"""
testutils.py

Utility functions for use when writing unit tests.
"""
import json
import os
import mock
from deprecated import deprecated


class MockResponse(object):
    def __init__(self, data):
        self.data = data

    def __enter__(self):
        return self

    def __exit__(self, _u, _n, _used):
        return

    status_code = 200

    def json(self):
        return self.data

    @staticmethod
    def raise_for_status():
        return

    @staticmethod
    def iter_content(_):
        yield [i for i in range(3)]

    def read(self):
        return json.dumps(self.data).encode("utf-8")


def got_but_expected(got, expected):
    """
    Helper method to print Got X but expected Y when unit tests assert.
    """
    return "Got {0} but expected {1}".format(got, expected)


@deprecated(
    version="2.1",
    reason="Patch out update_shell in your test, since this function is tested separately.",
)
def mocked_update_shell():
    """
    Mocks recalculate_shell_vars in fbcli so we can run update_shell() during tests.
    """
    patcher_load_source = mock.patch("elipy2.core.import_module_from_file")
    mock_load_source = patcher_load_source.start()
    mock_load_source = mock.MagicMock()
    mock_load_source.recalculate_shell_vars.return_value = ""
    return mock_load_source


def mock_retry(
    f,
    exceptions=Exception,
    tries=-1,
    delay=0,
    max_delay=None,
    backoff=1,
    jitter=0,
    logger=None,
):
    _tries, _delay, _backoff = 3, 0, 0
    while _tries:
        try:
            return f()
        except exceptions as e:
            _tries -= 1
            if not _tries:
                raise


def get_file_content(*args):
    """
    Helper function to get test file content
    """
    full_path = os.path.join(os.path.dirname(__file__), *args)
    with open(full_path, "r") as file:
        content = file.read()
    return content
