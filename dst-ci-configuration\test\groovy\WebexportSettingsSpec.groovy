import com.ea.lib.jobsettings.WebexportSettings
import spock.lang.Specification

class WebexportSettingsSpec extends Specification {

    class BranchFile {
        static Map standard_jobs_settings = [
            asset             : 'testLevels',
            elipy_call        : 'elipy-call',
            elipy_install_call: 'elipy-install-call',
            workspace_root    : 'workspace-root',
        ]
        static Map general_settings = [
            dataset: 'testdataset'
        ]
    }

    class BranchFilePoolbuild {
        static Map standard_jobs_settings = [
            asset             : 'testLevels',
            elipy_call        : 'elipy-call',
            elipy_install_call: 'elipy-install-call',
            poolbuild_data    : true,
            workspace_root    : 'workspace-root',
        ]
        static Map general_settings = [
            dataset: 'testdataset'
        ]
    }

    class BranchFilePoolbuildCustom {
        static Map standard_jobs_settings = [
            asset             : 'testLevels',
            elipy_call        : 'elipy-call',
            elipy_install_call: 'elipy-install-call',
            poolbuild_data    : true,
            poolbuild_label   : 'poolbuild_custom',
            workspace_root    : 'workspace-root',
        ]
        static Map general_settings = [
            dataset: 'testdataset'
        ]
    }

    class MasterFile {
        static Map branches = [
            'branch': [code_folder: 'dev', code_branch: 'code_branch', data_folder: 'dev', data_branch: 'data_branch'],
        ]
    }

    class ProjectFile {
        static String webexport_script_path = 'path\\webexport.py'
        static Map p4_fb_settings = [
            p4_port : 'port',
            p4_creds: 'creds',
        ]
    }

    void "test that we get expected job settings"() {
        when:
        WebexportSettings settings = new WebexportSettings()
        settings.initialize(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            description == 'Builds testdataset for win64 with code from code_branch.'
            disableBuild == false
            buildName == '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'
            timeoutMinutes == 240
            fbLoginDetails == [
                p4_port : 'port',
                p4_creds: 'creds',
            ]
            p4Creds == 'creds'
            p4Port == 'port'
            jobLabel == 'statebuild'
            workspaceRoot == 'workspace-root'
            elipyInstallCall == 'elipy-install-call'
            elipyCmd == 'elipy-call webexport --asset testLevels --script-path path\\webexport.py --data-dir testdataset --code-branch code_branch --code-changelist %code_changelist% --branch-name data_branch --data-changelist %data_changelist% --data-clean %clean_data% --aws-secret-key %AWS_SECRET_KEY% --aws-access-key-id %AWS_ACCESS_KEY_ID% --clean-master-version-check'
        }
    }

    void "test that poolbuild is handled correctly"() {
        when:
        WebexportSettings settings = new WebexportSettings()
        settings.initialize(BranchFilePoolbuild, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            jobLabel == 'poolbuild && win64'
        }
    }

    void "test that custom poolbuild label is handled correctly"() {
        when:
        WebexportSettings settings = new WebexportSettings()
        settings.initialize(BranchFilePoolbuildCustom, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            jobLabel == 'poolbuild_custom && win64'
        }
    }

    void "test webexport_import_avalanche logic"() {
        when:
        WebexportSettings settings = new WebexportSettings()
        BranchFile.standard_jobs_settings += [webexport_import_avalanche: false]
        settings.initialize(BranchFile, MasterFile, ProjectFile, 'branch')
        BranchFile.standard_jobs_settings -= [webexport_import_avalanche: false]
        then:
        with(settings) {
            elipyCmd == 'elipy-call webexport --asset testLevels --script-path path\\webexport.py --data-dir testdataset --code-branch code_branch --code-changelist %code_changelist% --branch-name data_branch --data-changelist %data_changelist% --data-clean %clean_data% --aws-secret-key %AWS_SECRET_KEY% --aws-access-key-id %AWS_ACCESS_KEY_ID% --clean-master-version-check'
        }
    }

    void "test webexport_import_avalanche logic exportDataBranch"() {
        when:
        WebexportSettings settings = new WebexportSettings()
        BranchFile.standard_jobs_settings += [export_data_branch: false]
        settings.initialize(BranchFile, MasterFile, ProjectFile, 'branch')
        BranchFile.standard_jobs_settings -= [export_data_branch: false]
        then:
        with(settings) {
            elipyCmd == 'elipy-call webexport --asset testLevels --script-path path\\webexport.py --data-dir testdataset --code-branch code_branch --code-changelist %code_changelist% --branch-name data_branch --data-changelist %data_changelist% --data-clean %clean_data% --aws-secret-key %AWS_SECRET_KEY% --aws-access-key-id %AWS_ACCESS_KEY_ID% --clean-master-version-check'
        }
    }

    void "test statebuild_webexport logic"() {
        when:
        WebexportSettings settings = new WebexportSettings()
        BranchFile.standard_jobs_settings += [statebuild_webexport: false]
        settings.initialize(BranchFile, MasterFile, ProjectFile, 'branch')
        BranchFile.standard_jobs_settings -= [statebuild_webexport: false]
        then:
        with(settings) {
            elipyCmd == 'elipy-call webexport --asset testLevels --script-path path\\webexport.py --data-dir testdataset --code-branch code_branch --code-changelist %code_changelist% --branch-name data_branch --data-changelist %data_changelist% --data-clean %clean_data% --aws-secret-key %AWS_SECRET_KEY% --aws-access-key-id %AWS_ACCESS_KEY_ID% --clean-master-version-check'
            jobLabel == 'data_branch && webexport'
        }
    }

    void "test webexport_label logic"() {
        when:
        WebexportSettings settings = new WebexportSettings()
        BranchFile.standard_jobs_settings += [webexport_label: 'data && webexport && test-branch']
        settings.initialize(BranchFile, MasterFile, ProjectFile, 'branch')
        BranchFile.standard_jobs_settings -= [webexport_label: 'data && webexport && test-branch']
        then:
        with(settings) {
            elipyCmd == 'elipy-call webexport --asset testLevels --script-path path\\webexport.py --data-dir testdataset --code-branch code_branch --code-changelist %code_changelist% --branch-name data_branch --data-changelist %data_changelist% --data-clean %clean_data% --aws-secret-key %AWS_SECRET_KEY% --aws-access-key-id %AWS_ACCESS_KEY_ID% --clean-master-version-check'
            jobLabel == 'data && webexport && test-branch'
        }
    }
}
