{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "tasks": [
        {
            "label": "Windows: Create and update virtual environment",
            "type": "shell",
            "command": ".\\.vscode\\create_virtual_environments.bat",
            "problemMatcher": []
        },
        {
            "label": "Windows: Update environment",
            "type": "shell",
            "command": ".\\.vscode\\setup_virtual_environment.bat",
            "args": ["3"],
            "problemMatcher": []
        },
        {
            "label": "macOS: Create and update virtual environment",
            "type": "shell",
            "command": "./.vscode/create_virtual_environments.sh",
            "problemMatcher": []
        },
    ]
}
