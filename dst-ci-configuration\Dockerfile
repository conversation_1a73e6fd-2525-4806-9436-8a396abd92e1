FROM docker.artifacts.ea.com/openjdk:21-slim

# Set temporal work directory
ENV TARGET_PATH=/tmp/src
RUN mkdir -p "${TARGET_PATH}"
WORKDIR "${TARGET_PATH}"
COPY ./gradlew .

# Set AF2 auth variables
RUN --mount=type=secret,id=ARTIFACTORY_USER,env=ARTIFACTORY_USER \
	--mount=type=secret,id=ARTIFACTORY_APIKEY,env=ARTIFACTORY_APIKEY
#    --credentials artifacts.ea.com:$ARTIFACTORY_USER:$ARTIFACTORY_APIKEY

# Install dependencies
RUN apt update
RUN apt install dos2unix
RUN dos2unix gradlew \
    && apt --purge remove -y dos2unix

# Install gradle dependencies
COPY . .
RUN --mount=type=secret,id=ARTIFACTORY_USER,env=ARTIFACTORY_USER \
    --mount=type=secret,id=ARTIFACTORY_APIKEY,env=ARTIFACTORY_APIKEY \
    ./gradlew --stacktrace --info assemble
RUN --mount=type=secret,id=ARTIFACTORY_USER,env=ARTIFACTORY_USER \
    --mount=type=secret,id=ARTIFACTORY_APIKEY,env=ARTIFACTORY_APIKEY \
    ./gradlew --stacktrace --info dependencies
RUN --mount=type=secret,id=ARTIFACTORY_USER,env=ARTIFACTORY_USER \
    --mount=type=secret,id=ARTIFACTORY_APIKEY,env=ARTIFACTORY_APIKEY \
    ./gradlew --stacktrace --info codenarcMain
RUN --mount=type=secret,id=ARTIFACTORY_USER,env=ARTIFACTORY_USER \
    --mount=type=secret,id=ARTIFACTORY_APIKEY,env=ARTIFACTORY_APIKEY \
    ./gradlew --stacktrace --info codenarcTest
RUN --mount=type=secret,id=ARTIFACTORY_USER,env=ARTIFACTORY_USER \
    --mount=type=secret,id=ARTIFACTORY_APIKEY,env=ARTIFACTORY_APIKEY \
    ./gradlew --stacktrace --info seed --tests '*basic_jobs*'

# Clean up
RUN rm -rf "${TARGET_PATH}"
RUN rm -rf '/var/lib/apt/lists/'*
