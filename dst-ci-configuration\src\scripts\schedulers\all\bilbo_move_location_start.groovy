package scripts.schedulers.all

import com.ea.lib.LibJenkins
import com.ea.project.GetBranchFile
import hudson.model.Result

def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
def project = ProjectClass(env.project_name)

/**
 * bilbo_move_location_start.groovy
 */
pipeline {
    agent { label '(scheduler && master) || scheduler || executor_agent' }
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Start register Drone in the build location.') {
            steps {
                script {
                    currentBuild.displayName = env.JOB_NAME + '.' + params.data_changelist + '.' + params.code_changelist

                    def args = [
                        string(name: 'code_changelist', value: params.code_changelist),
                        string(name: 'data_changelist', value: params.data_changelist),
                    ]
                    def move_locations = branchfile.standard_jobs_settings.new_locations.keySet()

                    def last_code_built = LibJenkins.getLastStableCodeChangelist(env.JOB_NAME)
                    def jobs = [:]
                    for (def run = 0; run <= env.retry_limit.toInteger(); run++) { // Retry failed jobs if retry_limit > 0.
                        jobs = [:]
                        final_result = Result.SUCCESS
                        // Register in Bilbo for the build location.
                        def job_name_local = env.branch_name + '.bilbo.register.local'
                        if (NeedsRebuildData(job_name, code_changelist, data_changelist)) {
                            jobs[job_name_local] = {
                                def downstream_job_local = build(job: job_name_local, parameters: args, propagate: false)
                                final_result = final_result.combine(Result.fromString(downstream_job_local.result))
                                LibJenkins.printRunningJobs(this)
                            }
                        }
                        // Copy code to the new location.
                        if (env.code_changelist == last_code_built) {
                            echo('Last code copy used code CL ' + last_code_built + ' and current code CL is ' + code_changelist + ', skipping copy.')
                        } else { // Only run the copy if we have a new code changelist.
                            for (location in move_locations) {
                                def job_name = env.branch_name + '.bilbo.move.' + location + '.start'
                                if (NeedsRebuildData(job_name, code_changelist, data_changelist)) {
                                    jobs[job_name] = {
                                        def downstream_job = build(job: job_name, parameters: args, propagate: false)
                                        final_result = final_result.combine(Result.fromString(downstream_job.result))
                                        LibJenkins.printRunningJobs(this)
                                    }
                                }
                            }
                        }
                        parallel(jobs)
                        if (final_result == Result.SUCCESS) {
                            break
                        }
                    }

                    currentBuild.result = final_result.toString()

                    def slack_settings = branchfile.standard_jobs_settings?.slack_channel_bilbo ?: branchfile.standard_jobs_settings?.slack_channel_data
                    SlackMessageNew(currentBuild, slack_settings, project.short_name)
                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
}
