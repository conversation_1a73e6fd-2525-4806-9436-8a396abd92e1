import com.ea.lib.jobsettings.BilboSettings
import spock.lang.Specification

class BilboSettingsSpec extends Specification {

    class BranchFile {
        static Map standard_jobs_settings = [
            workspace_root                 : 'workspace-root',
            elipy_call                     : 'elipy-call',
            elipy_install_call             : 'elipy-install-call',
            statebuild_autotest            : '',
            extra_locations_bilbo_drone_job: [
                'criterion',
            ],
            statebuild_data                : false,
            disable_build_bilbo_drone_job  : true,
            smoke_downstream_job           : 'downstream.job',
            statebuild_frosty              : false,
            new_location                   : 'criterion',
            retry_limit_bilbo_copy         : 4,
            branchName                     : 'a-branch-name',
            data_branch                    : 'data-branch',
            move_location_parallel         : true,
            new_locations                  : [
                earo: [
                    elipy_call_new_location: 'elipy-new-call --use-fbenv-core',
                ],
            ],
        ]
        static Map general_settings = [
            dataset: 'a-dataset',
        ]
    }

    class MasterFile {
        static Map autotest_branches = [branch: [data_branch: 'data-branch', code_branch: 'code-branch', statebuild_autotest: false]]
        static Map branches = [branch: [data_branch: 'data-branch', code_branch: 'code-branch']]
    }

    class ProjectFile {
        static boolean frostbite_syncer_setup = true
        static String name = 'Kingston'
    }

    Map variant = [format: 'digital', config: 'final', region: 'eu', args: '']
    Map combineVariant = [format: 'combine', config: 'combine', region: 'combine', args: '']

    void "test that we get expected job settings in initializeBilboDroneJob"() {
        when:
        BilboSettings bilboSettings = new BilboSettings()
        bilboSettings.initializeBilboDroneJob(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(bilboSettings) {
            jobLabel == 'data-branch'
            description == "Registers a ${BranchFile.general_settings.dataset} build in Drone."
            isDisabled == BranchFile.standard_jobs_settings.disable_build_bilbo_drone_job
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} bilbo_register_drone --dataset ${BranchFile.general_settings.dataset}" +
                ' --code-branch code-branch --code-changelist %code_changelist%' +
                ' --data-branch data-branch --data-changelist %data_changelist%  --extra-location criterion'
        }
    }

    void "test that we get expected job settings in initializeBilboDroneJobMoveLocationParallelDroneBuildStart"() {
        when:
        BilboSettings bilboSettings = new BilboSettings()
        bilboSettings.initializeBilboDroneJobMoveLocationParallelDroneBuildStart(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(bilboSettings) {
            description == "Registers a ${BranchFile.general_settings.dataset} build in Drone."
            retryLimit == BranchFile.standard_jobs_settings.retry_limit_bilbo_copy
            branchName == 'branch'
            projectName == ProjectFile.name
        }
    }

    void "test that we get expected job settings in initializeBilboDroneJobMoveLocationParallelLocationStart"() {
        when:
        BilboSettings bilboSettings = new BilboSettings()
        bilboSettings.initializeBilboDroneJobMoveLocationParallelLocationStart(BranchFile, MasterFile, ProjectFile, 'branch', 'NA')
        then:
        with(bilboSettings) {
            description == "Registers a ${BranchFile.general_settings.dataset} build in NA Drone."
            retryLimit == BranchFile.standard_jobs_settings.retry_limit_bilbo_copy
            branchName == 'branch'
            projectName == ProjectFile.name
        }
    }

    void "test that we get expected job settings in initializeBilboDroneJobMoveLocationParallelCopy"() {
        when:
        BilboSettings bilboSettings = new BilboSettings()
        bilboSettings.initializeBilboDroneJobMoveLocationParallelCopy(BranchFile, MasterFile, ProjectFile, 'branch', 'ps5', 'final', 'earo')
        bilboSettings.initializeBilboDroneJobMoveLocationParallel()
        then:
        with(bilboSettings) {
            description == 'Copies code for ps5.final to earo'
            jobLabel == 'data-branch'
            buildName == '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'
            workspaceRoot == 'workspace-root'
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} move_location_drone --parallel-copy" +
                ' --code-branch code-branch --code-changelist %code_changelist%' +
                " --dest-location ${BranchFile.standard_jobs_settings.new_locations.keySet()[0]} --data-changelist %data_changelist%" +
                ' --platform ps5 --config final --old-drone-setup'
        }
    }

    void "test that we get expected job settings in initializeBilboDroneJobMoveLocationParallelRemote"() {
        when:
        BilboSettings bilboSettings = new BilboSettings()
        bilboSettings.initializeBilboDroneJobMoveLocationParallelRemote(BranchFile, MasterFile, ProjectFile, 'branch', 'earo')
        bilboSettings.initializeBilboDroneJobMoveLocationParallel()
        then:
        with(bilboSettings) {
            description == "Registers a ${BranchFile.general_settings.dataset} build in Drone."
            jobLabel == 'data-branch'
            buildName == '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'
            workspaceRoot == 'workspace-root'
            elipyCmd == "${BranchFile.standard_jobs_settings.new_locations['earo'].elipy_call_new_location} bilbo_register_drone --dataset ${BranchFile.general_settings.dataset}" +
                ' --code-branch code-branch --code-changelist %code_changelist%' +
                ' --data-branch data-branch --data-changelist %data_changelist%  --old-drone-setup'
        }
    }

    void "test that we get expected job settings in initializeBilboFrostyJobMoveLocation"() {
        when:
        BilboSettings bilboSettings = new BilboSettings()
        bilboSettings.initializeBilboFrostyJobMoveLocation(BranchFile, MasterFile, ProjectFile, 'branch', variant, 'ps5', 'earo')
        then:
        with(bilboSettings) {
            jobLabel == 'data-branch'
            description == "Registers a ${BranchFile.general_settings.dataset} build in Drone."
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} move_location_frosty" +
                ' --code-branch code-branch --code-changelist %code_changelist%' +
                ' --data-branch data-branch --data-changelist %data_changelist%' +
                " --dest-location ${BranchFile.standard_jobs_settings.new_locations.keySet()[0]} --region ${variant.region} --platform ps5" +
                " --package-type ${variant.format} --config ${variant.config}"
            elipyCmdNewLocation == "${BranchFile.standard_jobs_settings.new_locations['earo'].elipy_call_new_location} bilbo_register_frosty" +
                ' --code-branch code-branch --code-changelist %code_changelist%' +
                ' --data-branch data-branch --data-changelist %data_changelist%' +
                " --region ${variant.region} --platform ps5 --dataset ${BranchFile.general_settings.dataset}" +
                " --package-type ${variant.format} --config ${variant.config}"
        }
    }

    void "test that combine parameters are added for combine format in initializeBilboFrostyJobMoveLocation"() {
        when:
        BilboSettings bilboSettings = new BilboSettings()
        bilboSettings.initializeBilboFrostyJobMoveLocation(BranchFile, MasterFile, ProjectFile, 'branch', combineVariant, 'ps5', 'earo')
        then:
        with(bilboSettings) {
            jobLabel == 'data-branch'
            description == "Registers a ${BranchFile.general_settings.dataset} build in Drone."
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} move_location_frosty" +
                ' --code-branch code-branch --code-changelist %code_changelist%' +
                ' --data-branch data-branch --data-changelist %data_changelist%' +
                " --dest-location ${BranchFile.standard_jobs_settings.new_locations.keySet()[0]} --region ${combineVariant.region} --platform ps5" +
                " --package-type ${combineVariant.format} --config ${combineVariant.config}" +
                ' --combine-code-branch %combine_code_branch% --combine-code-changelist %combine_code_changelist%' +
                ' --combine-data-branch %combine_data_branch% --combine-data-changelist %combine_data_changelist%'
            elipyCmdNewLocation == "${BranchFile.standard_jobs_settings.new_locations['earo'].elipy_call_new_location} bilbo_register_frosty" +
                ' --code-branch code-branch --code-changelist %code_changelist%' +
                ' --data-branch data-branch --data-changelist %data_changelist%' +
                " --region ${combineVariant.region} --platform ps5 --dataset ${BranchFile.general_settings.dataset}" +
                " --package-type ${combineVariant.format} --config ${combineVariant.config}" +
                ' --combine-code-branch %combine_code_branch% --combine-code-changelist %combine_code_changelist%' +
                ' --combine-data-branch %combine_data_branch% --combine-data-changelist %combine_data_changelist%'
        }
    }

    void "test that we get expected job settings in initializeBilboRegisterSmoke"() {
        when:
        BilboSettings bilboSettings = new BilboSettings()
        bilboSettings.initializeBilboRegisterSmoke(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(bilboSettings) {
            jobLabel == 'statebuild'
            smokeDownstreamJob == BranchFile.standard_jobs_settings.smoke_downstream_job
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} bilbo_register_smoke" +
                ' --code-branch code-branch --code-changelist %code_changelist% --data-changelist %data_changelist%'
        }
    }

    void "test that we get expected job settings in initializeBilboRegisterSmoke with multi location enabled"() {
        when:
        BilboSettings bilboSettings = new BilboSettings()
        BranchFile.standard_jobs_settings['register_smoke_multi_location'] = true
        bilboSettings.initializeBilboRegisterSmoke(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(bilboSettings) {
            jobLabel == 'statebuild'
            smokeDownstreamJob == BranchFile.standard_jobs_settings.smoke_downstream_job
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} bilbo_register_smoke" +
                ' --code-branch code-branch --code-changelist %code_changelist% --data-changelist %data_changelist% --extra-location earo'
        }
        BranchFile.standard_jobs_settings.remove('register_smoke_multi_location')
    }

    void "test that we get expected job settings in initializeBilboRegisterVerifiedForPreflight"() {
        when:
        BilboSettings bilboSettings = new BilboSettings()
        bilboSettings.initializeBilboRegisterVerifiedForPreflight(BranchFile, MasterFile, ProjectFile, 'branch', 'changelist')
        then:
        with(bilboSettings) {
            jobLabel == 'bilbo'
            description == 'Registers a build as verified_for_preflight bilbo.'
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${ENV, var="changelist"}'
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} bilbo_register_verified_for_preflight" +
                ' --branch code-branch --changelist %changelist%'
        }
    }

    void "test that we get different changelist param in initializeBilboRegisterVerifiedForPreflight"() {
        when:
        BilboSettings bilboSettings = new BilboSettings()
        bilboSettings.initializeBilboRegisterVerifiedForPreflight(BranchFile, MasterFile, ProjectFile, 'branch', 'data_changelist')
        then:
        with(bilboSettings) {
            jobLabel == 'bilbo'
            description == 'Registers a build as verified_for_preflight bilbo.'
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${ENV, var="data_changelist"}'
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} bilbo_register_verified_for_preflight" +
                ' --branch code-branch --changelist %data_changelist%'
        }
    }

    void "test that we get expected job settings in initializeBilboAutotest"() {
        when:
        BilboSettings bilboSettings = new BilboSettings()
        bilboSettings.initializeBilboAutotest(BranchFile, MasterFile, ProjectFile, 'branch')

        then:
        with(bilboSettings) {
            jobLabel == 'bilbo'
            description == "Registers a ${BranchFile.general_settings.dataset} build in Drone."
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'

            def expectedCmd = "${BranchFile.standard_jobs_settings.elipy_call} bilbo_register_autotest" +
                ' --code-branch code-branch --code-changelist %code_changelist%' +
                ' --data-branch data-branch --data-changelist %data_changelist%' +
                ' --test-status %status% --run-bilbo %run_bilbo% --test-definition %test_definition% --register-smoke %register_smoke%'

            BranchFile.standard_jobs_settings.new_locations.keySet().each { location ->
                expectedCmd += " --extra-location ${location}"
            }

            elipyCmd == expectedCmd
        }
    }

    void "test we get expected job settings in initializeBilboRegisterReleaseCandidate"() {
        when:
        BilboSettings bilboSettings = new BilboSettings()
        bilboSettings.initializeBilboRegisterReleaseCandidate(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(bilboSettings) {
            jobLabel == 'statebuild'
            description == 'Registers a build as a release candidate in Bilbo.'
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} bilbo_register_release_candidate" +
                ' --code-branch %code_branch% --code-changelist %code_changelist%' +
                ' --data-branch %data_branch% --data-changelist %data_changelist%'
        }
    }
}
