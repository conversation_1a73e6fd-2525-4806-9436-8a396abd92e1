"""
test_frosty.py

Unit testing for frosty
"""

import os
import unittest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import call, patch, MagicMock
from dice_elipy_scripts.frosty import cli
from elipy2.config import ConfigManager
from elipy2.oreans import __OUTPUT_SUFFIX as OREANS_OUTPUT_SUFFIX

config_path = os.path.join(os.path.dirname(__file__), "data", "elipy_test.yml")
config_manager = ConfigManager(path=config_path)
config_manager_2 = ConfigManager(path=config_path, default_location="different_location")


def mock_split(input_path):
    path_parts = input_path.split("\\")
    return ["\\".join(path_parts[:-1]), path_parts[-1]]


@patch("os.path.join", MagicMock(side_effect=lambda *args: "\\".join(args)))
@patch("elipy2.avalanche.set_avalanche_build_status", MagicMock())
@patch("elipy2.frostbite.icepick.IcepickUtils.clean_local_frosty", MagicMock())
@patch("elipy2.running_processes.kill", MagicMock())
@patch("elipy2.secrets.get_secrets", MagicMock())
@patch("elipy2.secrets.remove_old_frosty_cert", MagicMock())
@patch("elipy2.telemetry.upload_metrics", MagicMock())
@patch("dice_elipy_scripts.frosty.add_files_to_frosty_output", MagicMock())
@patch("dice_elipy_scripts.frosty.add_frosty_log_to_output", MagicMock())
@patch("dice_elipy_scripts.frosty.add_sentry_tags", MagicMock())
@patch("dice_elipy_scripts.frosty.generate_buildlayout_xml", MagicMock())
@patch("dice_elipy_scripts.frosty.install_required_sdks", MagicMock())
@patch("dice_elipy_scripts.frosty.patch_eainstaller_signtool", MagicMock())
@patch("dice_elipy_scripts.frosty.set_licensee", MagicMock())
class TestFrosty(unittest.TestCase):
    ARGUMENT_PLATFORM_1 = "xb1"
    ARGUMENT_PLATFORM_2 = "xbsx"
    ARGUMENT_PLATFORM_3 = "ps4"
    ARGUMENT_PLATFORM_4 = "ps5"
    ARGUMENT_PLATFORM_5 = "win64"
    ARGUMENT_PLATFORM_6 = "linux64"
    ARGUMENT_PLATFORM_7 = "server"
    ARGUMENT_PLATFORM_8 = "linuxserver"
    ARGUMENT_PLATFORM_9 = "fake_platform"
    ARGUMENT_PLATFORM_10 = "win32"
    ARGUMENT_PACKAGE_TYPE = "files"
    ARGUMENT_PACKAGE_TYPE_2 = "digital"
    ARGUMENT_CONFIG = "retail"
    ARGUMENT_CONFIG_2 = "release"
    ARGUMENT_ASSETS = "test_asset"

    OPTION_DATA_DIRECTORY = "--data-directory"
    OPTION_CODE_BRANCH = "--code-branch"
    OPTION_CODE_CHANGELIST = "--code-changelist"
    OPTION_DATA_BRANCH = "--data-branch"
    OPTION_DATA_CHANGELIST = "--data-changelist"
    OPTION_REGION = "--region"
    OPTION_USE_DEPLOYED_BUNDLES = "--use-deployed-bundles"
    OPTION_LOCAL_BUNDLES_PATH = "--local-bundles-path"
    OPTION_PIPELINE_ARGS = "--pipeline-args"
    OPTION_FROSTY_ARGS = "--frosty-args"
    OPTION_USE_WIN64TRIAL = "--use-win64trial"
    OPTION_DRY_RUN = "--dry-run"
    OPTION_USE_OREANS = "--use-oreans"
    OPTION_USE_DENUVO = "--use-denuvo"
    OPTION_ADDITIONAL_CONFIGS = "--additional-configs"
    OPTION_IMPORT_AVALANCHE_STATE = "--import-avalanche-state"
    OPTION_DATA_CLEAN = "--data-clean"
    OPTION_USE_RECOMPRESSION_CACHE = "--use-recompression-cache"
    OPTION_USE_LINUXCLIENT = "--use-linuxclient"
    OPTION_SKIP_STREAMING_INSTALL_PACKAGE = "--skip-streaming-install-package"
    OPTION_CLEAN_MASTER_VERSION_CHECK = "--clean-master-version-check"
    OPTION_ENABLE_EAC = "--enable-eac"
    OPTION_EXPRESSION_DEBUG_DATA = "--expression-debug-data"
    OPTION_KEEP_INTERMEDIATE_DATA = "--keep-intermediate-data"
    OPTION_BUILD_GAMESCRIPTS = "--build-gamescripts"
    OPTION_RUN_BESPOKE = "--run-bespoke"
    OPTION_FILE_HASHES = "--file-hashes"
    OPTION_FETCH_PIPELINE = "--fetch-pipeline"
    OPTION_USE_COMBINE_BUNDLES = "--use-combine-bundles"
    OPTION_COMBINE_CODE_BRANCH = "--combine-code-branch"
    OPTION_COMBINE_CODE_CHANGELIST = "--combine-code-changelist"
    OPTION_COMBINE_DATA_BRANCH = "--combine-data-branch"
    OPTION_COMBINE_DATA_CHANGELIST = "--combine-data-changelist"
    OPTION_COMBINE_SETTINGS_FILE = "--combine-settings-file"
    OPTION_STEAM_BUILD = "--steam-build"
    OPTION_STEAM_DRMWRAP = "--steam-drmwrap"
    OPTION_CONTENT_LAYER = "--content-layer"
    OPTION_VIRTUAL_BRANCH_OVERRIDE = "--virtual-branch-override"

    VALUE_DATA_DIRECTORY = "SomeDatadir"
    VALUE_CODE_BRANCH = "some-branch"
    VALUE_CODE_CHANGELIST = "1234"
    VALUE_DATA_BRANCH = "other-branch"
    VALUE_DATA_CHANGELIST = "5678"
    VALUE_REGION = "test_region"
    VALUE_PIPELINE_ARGS = "arg1"
    VALUE_FROSTY_ARGS = "arg2"
    VALUE_DATA_CLEAN = "true"
    VALUE_FILE_HASHES = "true"
    VALUE_USE_COMBINE_BUNDLES = "true"
    VALUE_COMBINE_CODE_BRANCH = "combine_code_branch"
    VALUE_COMBINE_CODE_CHANGELIST = "1122"
    VALUE_COMBINE_DATA_BRANCH = "combine_data_branch"
    VALUE_COMBINE_DATA_CHANGELIST = "3344"
    VALUE_COMBINE_SETTINGS_FILE = "combine_settings_file"
    VALUE_LOCAL_BUNDLES_PATH = "superbundles\\data"
    VALUE_CONTENT_LAYER = "Layer1"

    BASIC_ARGS = [
        ARGUMENT_ASSETS,
        OPTION_DATA_DIRECTORY,
        VALUE_DATA_DIRECTORY,
        OPTION_CODE_BRANCH,
        VALUE_CODE_BRANCH,
        OPTION_CODE_CHANGELIST,
        VALUE_CODE_CHANGELIST,
        OPTION_DATA_BRANCH,
        VALUE_DATA_BRANCH,
        OPTION_DATA_CHANGELIST,
        VALUE_DATA_CHANGELIST,
    ]

    DEFAULT_ARGS = [ARGUMENT_PACKAGE_TYPE, ARGUMENT_CONFIG] + BASIC_ARGS

    def setUp(self):
        self.patcher_settings_get = patch("dice_elipy_scripts.frosty.SETTINGS", config_manager)
        self.mock_settings_get = self.patcher_settings_get.start()

        self.patcher_copy_submissionvalidator = patch(
            "dice_elipy_scripts.frosty.package.PackageUtils.copy_submissionvalidator"
        )
        self.mock_copy_submissionvalidator = self.patcher_copy_submissionvalidator.start()

        self.patcher_datautils = patch("elipy2.data.DataUtils")
        self.mock_datautils = self.patcher_datautils.start()
        self.mock_datautils.return_value = MagicMock()

        self.patcher_filerutils = patch("elipy2.filer.FilerUtils")
        self.mock_filerutils = self.patcher_filerutils.start()
        self.mock_filerutils.return_value = MagicMock()

        self.patcher_packageutils = patch("elipy2.package.PackageUtils")
        self.mock_packageutils = self.patcher_packageutils.start()
        self.mock_packageutils.return_value = MagicMock()

        self.patcher_symbolsutils = patch("elipy2.symbols.SymbolsUtils")
        self.mock_symbolsutils = self.patcher_symbolsutils.start()
        self.mock_symbolsutils.return_value = MagicMock()

        self.patcher_read_fb_version = patch("elipy2.frostbite_core.read_fb_version")
        self.mock_read_fb_version = self.patcher_read_fb_version.start()
        self.mock_read_fb_version.return_value = "2019-PR7"

        self.patcher_os_exists = patch("dice_elipy_scripts.frosty.os.path.exists")
        self.mock_os_exists = self.patcher_os_exists.start()
        self.mock_os_exists.return_value = False

        self.patcher_import_avalanche_data_state = patch(
            "dice_elipy_scripts.frosty.import_avalanche_data_state"
        )
        self.mock_import_avalanche_data_state = self.patcher_import_avalanche_data_state.start()

        self.patcher_local_bundles_path = patch("elipy2.local_paths.get_local_bundles_path")
        self.mock_local_bundles_path = self.patcher_local_bundles_path.start()
        self.mock_local_bundles_path.return_value = "local\\bundle\\path\\data"

        self.patcher_local_frosty_path = patch("elipy2.local_paths.get_local_frosty_path")
        self.mock_local_frosty_path = self.patcher_local_frosty_path.start()
        self.mock_local_frosty_path.return_value = "local\\frosty\\path"

        self.patcher_robocopy = patch("elipy2.core.robocopy")
        self.mock_robocopy = self.patcher_robocopy.start()

        self.patcher_update_shell = patch("elipy2.core.update_shell")
        self.mock_update_shell = self.patcher_update_shell.start()

        self.patcher_run_expression_debug_data = patch(
            "dice_elipy_scripts.frosty.run_expression_debug_data"
        )
        self.mock_run_expression_debug_data = self.patcher_run_expression_debug_data.start()

        self.patcher_local_build_path = patch("elipy2.local_paths.get_local_build_path")
        self.mock_local_build_path = self.patcher_local_build_path.start()
        self.mock_local_build_path.return_value = "local\\build\\path"

        self.patcher_os_listdir = patch("os.listdir")
        self.mock_os_listdir = self.patcher_os_listdir.start()
        self.mock_os_listdir.return_value = ["file1", "file2"]

        self.patcher_re_match = patch("re.match")
        self.mock_re_match = self.patcher_re_match.start()

        self.patcher_os_path_isfile = patch("os.path.isfile")
        self.mock_os_path_isfile = self.patcher_os_path_isfile.start()

        self.patcher_combine = patch("elipy2.avalanche.combine")
        self.mock_combine = self.patcher_combine.start()

        self.patcher_get_built_levels = patch("elipy2.avalanche.get_built_levels")
        self.mock_get_built_levels = self.patcher_get_built_levels.start()

        self.patcher_get_full_database_name = patch("elipy2.avalanche.get_full_database_name")
        self.mock_get_full_database_name = self.patcher_get_full_database_name.start()

        self.patcher_md5_hash_folder = patch("elipy2.core.md5_hash_folder")
        self.mock_md5_hash_folder = self.patcher_md5_hash_folder.start()

        self.patcher_get_frosty_build_path = patch("elipy2.filer_paths.get_frosty_build_path")
        self.mock_get_frosty_build_path = self.patcher_get_frosty_build_path.start()

    def tearDown(self):
        patch.stopall()

    def test_xb1(self):
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_1] + self.DEFAULT_ARGS)
        assert result.exit_code == 0

    def test_xbsx(self):
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_2] + self.DEFAULT_ARGS)
        assert result.exit_code == 0

    def test_ps4(self):
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_3] + self.DEFAULT_ARGS)
        assert result.exit_code == 0

    def test_ps5(self):
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_4] + self.DEFAULT_ARGS)
        assert result.exit_code == 0

    def test_win64(self):
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_5] + self.DEFAULT_ARGS)
        assert result.exit_code == 0

    def test_linux64(self):
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_6] + self.DEFAULT_ARGS)
        assert result.exit_code == 0

    def test_server(self):
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_7] + self.DEFAULT_ARGS)
        assert result.exit_code == 0

    def test_linuxserver(self):
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_8] + self.DEFAULT_ARGS)
        assert result.exit_code == 0

    def test_copy_submissionvalidator_xb1(self):
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_1] + self.DEFAULT_ARGS)
        self.mock_packageutils.return_value.copy_submissionvalidator.assert_called_once()

    def test_copy_submissionvalidator_xbsx(self):
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_2] + self.DEFAULT_ARGS)
        self.mock_packageutils.return_value.copy_submissionvalidator.assert_called_once()

    def test_not_copy_submissionvalidator_new_fb(self):
        self.mock_read_fb_version.return_value = "2022-1-PR2"
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_1] + self.DEFAULT_ARGS)
        assert not self.mock_packageutils.return_value.copy_submissionvalidator.called

    def test_not_copy_submissionvalidator_ps4(self):
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_3] + self.DEFAULT_ARGS)
        assert not self.mock_packageutils.return_value.copy_submissionvalidator.called

    def test_frosty_platform_win64(self):
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_5] + self.DEFAULT_ARGS)
        self.mock_packageutils.assert_called_once_with(
            self.ARGUMENT_PLATFORM_5,
            self.ARGUMENT_PACKAGE_TYPE,
            self.ARGUMENT_CONFIG,
            monkey_build_label=self.VALUE_DATA_CHANGELIST,
            overwrite_p4config=False,
        )

    def test_frosty_platform_linux64(self):
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_6] + self.DEFAULT_ARGS)
        self.mock_packageutils.assert_called_once_with(
            self.ARGUMENT_PLATFORM_6,
            self.ARGUMENT_PACKAGE_TYPE,
            self.ARGUMENT_CONFIG,
            monkey_build_label=self.VALUE_DATA_CHANGELIST,
            overwrite_p4config=False,
        )

    def test_frosty_platform_xb1_old(self):
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_1] + self.DEFAULT_ARGS)
        self.mock_packageutils.assert_called_once_with(
            self.ARGUMENT_PLATFORM_1,
            self.ARGUMENT_PACKAGE_TYPE,
            self.ARGUMENT_CONFIG,
            monkey_build_label=self.VALUE_DATA_CHANGELIST,
            overwrite_p4config=False,
        )

    def test_frosty_platform_xb1_temporary(self):
        self.mock_read_fb_version.return_value = "2022-1-PR2"
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_1] + self.DEFAULT_ARGS)
        self.mock_packageutils.assert_called_once_with(
            "xb1gdk",
            self.ARGUMENT_PACKAGE_TYPE,
            self.ARGUMENT_CONFIG,
            monkey_build_label=self.VALUE_DATA_CHANGELIST,
            overwrite_p4config=False,
        )

    def test_frosty_platform_xb1_new(self):
        self.mock_read_fb_version.return_value = "2022-1.Alpha"
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_1] + self.DEFAULT_ARGS)
        self.mock_packageutils.assert_called_once_with(
            self.ARGUMENT_PLATFORM_1,
            self.ARGUMENT_PACKAGE_TYPE,
            self.ARGUMENT_CONFIG,
            monkey_build_label=self.VALUE_DATA_CHANGELIST,
            overwrite_p4config=False,
        )

    @patch("dice_elipy_scripts.utils.gamescripts_utils.generate_gamescripts")
    def test_frosty_build_gamescripts(self, mock_gamescripts):
        runner = CliRunner()
        result = runner.invoke(
            cli, [self.ARGUMENT_PLATFORM_1] + self.DEFAULT_ARGS + [self.OPTION_BUILD_GAMESCRIPTS]
        )
        self.mock_packageutils.assert_called_once_with(
            self.ARGUMENT_PLATFORM_1,
            self.ARGUMENT_PACKAGE_TYPE,
            self.ARGUMENT_CONFIG,
            monkey_build_label=self.VALUE_DATA_CHANGELIST,
            overwrite_p4config=False,
        )
        mock_gamescripts.assert_called_once()

    def test_frosty_bespoke(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, [self.ARGUMENT_PLATFORM_1] + self.DEFAULT_ARGS + [self.OPTION_RUN_BESPOKE]
        )
        self.mock_packageutils.assert_called_once_with(
            self.ARGUMENT_PLATFORM_1,
            self.ARGUMENT_PACKAGE_TYPE,
            self.ARGUMENT_CONFIG,
            monkey_build_label=self.VALUE_DATA_CHANGELIST,
            overwrite_p4config=False,
        )
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            frosty_args=["XB1_PUT_BUILD_LABEL_INTO_VERSION=True"], region="ww", run_bespoke=True
        )

    def test_pipeline_args_hyphen(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_1]
            + self.DEFAULT_ARGS
            + [self.OPTION_PIPELINE_ARGS, self.VALUE_PIPELINE_ARGS],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_datautils.return_value.cook.assert_called_once_with(
            pipeline_args=["arg1"],
            indexing_args=[],
            collect_mdmps=True,
            trim=True,
            clean_master_version_check=False,
        )

    def test_frosty_args_hyphen(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_9]
            + self.DEFAULT_ARGS
            + [self.OPTION_FROSTY_ARGS, self.VALUE_FROSTY_ARGS],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww", frosty_args=["arg2"], run_bespoke=False
        )

    @patch("os.path.exists")
    def test_path_exists(self, mock_exists):
        mock_exists.side_effect = [False, False, False, True]
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_9] + self.DEFAULT_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 1

    def test_data_clean(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_1]
            + self.DEFAULT_ARGS
            + [self.OPTION_DATA_CLEAN, self.VALUE_DATA_CLEAN],
        )
        assert result.exit_code == 0
        self.mock_datautils.return_value.clean.assert_called_once_with()

    def test_import_avalanche_state(self):
        self.mock_import_avalanche_data_state.return_value = ["avalanche_arg"]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_1]
            + self.DEFAULT_ARGS
            + [
                self.OPTION_PIPELINE_ARGS,
                self.VALUE_PIPELINE_ARGS,
                self.OPTION_IMPORT_AVALANCHE_STATE,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_import_avalanche_data_state.assert_called_once_with(
            self.VALUE_DATA_BRANCH,
            self.VALUE_CODE_BRANCH,
            self.ARGUMENT_PLATFORM_1,
            self.mock_filerutils.return_value,
            self.VALUE_DATA_CHANGELIST,
        )
        self.mock_datautils.return_value.cook.assert_called_once_with(
            pipeline_args=["arg1", "avalanche_arg"],
            indexing_args=[],
            collect_mdmps=True,
            trim=True,
            clean_master_version_check=False,
        )

    def test_keep_intermediate_data(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_9]
            + self.DEFAULT_ARGS
            + [
                self.OPTION_FROSTY_ARGS,
                self.VALUE_FROSTY_ARGS,
                self.OPTION_KEEP_INTERMEDIATE_DATA,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww",
            frosty_args=["arg2", "CLEAN_INTERMEDIATE_AFTERBUILD=false"],
            run_bespoke=False,
        )

    @patch("os.path.split")
    def test_use_deployed_bundles_not_server(self, mock_os_path_split):
        mock_os_path_split.return_value = ["local\\bundle\\path", "data"]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_9]
            + self.DEFAULT_ARGS
            + [
                self.OPTION_FROSTY_ARGS,
                self.VALUE_FROSTY_ARGS,
                self.OPTION_USE_DEPLOYED_BUNDLES,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww",
            frosty_args=[
                "arg2",
                "USE_ALREADYCREATED_SUPERBUNDLES=true",
                "SUPERBUNDLE_SOURCE_DIR=local\\bundle\\path\\data",
            ],
            run_bespoke=False,
        )
        self.mock_filerutils.return_value.fetch_head_bundles.assert_called_once_with(
            code_branch=self.VALUE_CODE_BRANCH,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            data_branch=self.VALUE_DATA_BRANCH,
            data_changelist=self.VALUE_DATA_CHANGELIST,
            platform=self.ARGUMENT_PLATFORM_9,
            dest="local\\bundle\\path",
        )
        self.mock_robocopy.assert_called_once_with(
            source="local\\bundle\\path\\data",
            dest="local\\frosty\\path",
            extra_args=["builtLevels.json", "/s"],
        )

    @patch("os.path.split")
    def test_use_super_bundles_path(self, mock_os_path_split):
        mock_os_path_split.return_value = ["local\\superbundles\\", "data"]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_9]
            + self.DEFAULT_ARGS
            + [self.OPTION_LOCAL_BUNDLES_PATH, self.VALUE_LOCAL_BUNDLES_PATH],
        )
        print(result.output)
        print(str(result.exit_code))
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww",
            frosty_args=[
                "USE_ALREADYCREATED_SUPERBUNDLES=true",
                "SUPERBUNDLE_SOURCE_DIR=tnt_root\\local\\superbundles\\data",
            ],
            run_bespoke=False,
        )
        self.mock_filerutils.return_value.fetch_head_bundles.assert_not_called()
        self.mock_robocopy.assert_called_once_with(
            source="tnt_root\\local\\superbundles\\data",
            dest="local\\frosty\\path",
            extra_args=["builtLevels.json", "/s"],
        )

    def test_no_fetch_pipeline(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_9] + self.DEFAULT_ARGS + [self.OPTION_FETCH_PIPELINE, False],
        )
        assert result.exit_code == 0
        # assert code binaries is the only fetch_code call
        self.mock_filerutils.return_value.fetch_code.assert_has_calls(
            [
                call(
                    self.VALUE_CODE_BRANCH,
                    self.VALUE_CODE_CHANGELIST,
                    self.ARGUMENT_PLATFORM_9,
                    self.ARGUMENT_CONFIG,
                )
            ]
        )

    @patch("os.path.split")
    def test_use_deployed_bundles_not_server_not_files(self, mock_os_path_split):
        mock_os_path_split.return_value = ["local\\bundle\\path", "data"]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_9, self.ARGUMENT_PACKAGE_TYPE_2, self.ARGUMENT_CONFIG]
            + self.BASIC_ARGS
            + [
                self.OPTION_FROSTY_ARGS,
                self.VALUE_FROSTY_ARGS,
                self.OPTION_USE_DEPLOYED_BUNDLES,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww",
            frosty_args=[
                "arg2",
                "USE_ALREADYCREATED_SUPERBUNDLES=true",
                "SUPERBUNDLE_SOURCE_DIR=local\\bundle\\path\\data",
            ],
            run_bespoke=False,
        )
        self.mock_filerutils.return_value.fetch_head_bundles.assert_called_once_with(
            code_branch=self.VALUE_CODE_BRANCH,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            data_branch=self.VALUE_DATA_BRANCH,
            data_changelist=self.VALUE_DATA_CHANGELIST,
            platform=self.ARGUMENT_PLATFORM_9,
            dest="local\\bundle\\path",
        )
        assert self.mock_robocopy.call_count == 0

    @patch("os.path.split")
    def test_use_deployed_bundles_win32(self, mock_os_path_split):
        mock_os_path_split.return_value = ["local\\bundle\\path", "data"]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_10]
            + self.DEFAULT_ARGS
            + [
                self.OPTION_FROSTY_ARGS,
                self.VALUE_FROSTY_ARGS,
                self.OPTION_USE_DEPLOYED_BUNDLES,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_head_bundles.assert_called_once_with(
            code_branch=self.VALUE_CODE_BRANCH,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            data_branch=self.VALUE_DATA_BRANCH,
            data_changelist=self.VALUE_DATA_CHANGELIST,
            platform=self.ARGUMENT_PLATFORM_5,
            dest="local\\bundle\\path",
        )

    def test_expression_debug_data(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_9] + self.DEFAULT_ARGS + [self.OPTION_EXPRESSION_DEBUG_DATA],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_run_expression_debug_data.assert_called_once_with(
            self.VALUE_CODE_CHANGELIST,
            self.VALUE_DATA_CHANGELIST,
            self.VALUE_CODE_BRANCH,
            self.VALUE_DATA_BRANCH,
            self.ARGUMENT_PLATFORM_9,
            builder_instance=self.mock_datautils.return_value,
            clean_master_version_check=False,
        )

    def test_expression_debug_data_version_check(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_9]
            + self.DEFAULT_ARGS
            + [self.OPTION_EXPRESSION_DEBUG_DATA, self.OPTION_CLEAN_MASTER_VERSION_CHECK],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_run_expression_debug_data.assert_called_once_with(
            self.VALUE_CODE_CHANGELIST,
            self.VALUE_DATA_CHANGELIST,
            self.VALUE_CODE_BRANCH,
            self.VALUE_DATA_BRANCH,
            self.ARGUMENT_PLATFORM_9,
            builder_instance=self.mock_datautils.return_value,
            clean_master_version_check=True,
        )

    def test_packager_platform(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_9] + self.DEFAULT_ARGS,
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_packageutils.assert_called_once_with(
            self.ARGUMENT_PLATFORM_9,
            self.ARGUMENT_PACKAGE_TYPE,
            self.ARGUMENT_CONFIG,
            monkey_build_label=self.VALUE_DATA_CHANGELIST,
            overwrite_p4config=False,
        )

    def test_packager_linux64_old(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_6] + self.DEFAULT_ARGS,
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_packageutils.assert_called_once_with(
            self.ARGUMENT_PLATFORM_6,
            self.ARGUMENT_PACKAGE_TYPE,
            self.ARGUMENT_CONFIG,
            monkey_build_label=self.VALUE_DATA_CHANGELIST,
            overwrite_p4config=False,
        )

    def test_packager_linux64_linuxclient(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_6] + self.DEFAULT_ARGS + [self.OPTION_USE_LINUXCLIENT],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_packageutils.assert_called_once_with(
            "linuxclient",
            self.ARGUMENT_PACKAGE_TYPE,
            self.ARGUMENT_CONFIG,
            monkey_build_label=self.VALUE_DATA_CHANGELIST,
            overwrite_p4config=False,
        )

    def test_additional_configs_no_match(self):
        self.mock_re_match.return_value = False
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_9]
            + self.DEFAULT_ARGS
            + [self.OPTION_ADDITIONAL_CONFIGS, self.ARGUMENT_CONFIG_2],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_code.assert_has_calls(
            [
                call(self.VALUE_CODE_BRANCH, self.VALUE_CODE_CHANGELIST, "pipeline", "release"),
                call(
                    self.VALUE_CODE_BRANCH,
                    self.VALUE_CODE_CHANGELIST,
                    self.ARGUMENT_PLATFORM_9,
                    self.ARGUMENT_CONFIG,
                ),
                call(
                    self.VALUE_CODE_BRANCH,
                    self.VALUE_CODE_CHANGELIST,
                    self.ARGUMENT_PLATFORM_9,
                    self.ARGUMENT_CONFIG_2,
                ),
            ]
        )
        assert self.mock_robocopy.call_count == 0

    def test_additional_configs_already_exists(self):
        self.mock_re_match.return_value = True
        self.mock_os_path_isfile.return_value = True
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_9]
            + self.DEFAULT_ARGS
            + [self.OPTION_ADDITIONAL_CONFIGS, self.ARGUMENT_CONFIG_2],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        assert self.mock_robocopy.call_count == 0

    def test_additional_configs_copy_files(self):
        self.mock_re_match.return_value = True
        self.mock_os_path_isfile.return_value = False
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_9]
            + self.DEFAULT_ARGS
            + [self.OPTION_ADDITIONAL_CONFIGS, self.ARGUMENT_CONFIG_2],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_robocopy.assert_called_once_with(
            "local\\build\\path", "local\\frosty\\path", extra_args=["file1", "file2"]
        )

    def test_additional_configs_copy_files_sp(self):
        self.mock_re_match.return_value = True
        self.mock_os_path_isfile.return_value = False
        self.mock_os_exists.side_effect = lambda x: (
            True if x == "local\\frosty\\path\\SP" else False
        )
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_9]
            + self.DEFAULT_ARGS
            + [self.OPTION_ADDITIONAL_CONFIGS, self.ARGUMENT_CONFIG_2],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_robocopy.assert_has_calls(
            [
                call("local\\build\\path", "local\\frosty\\path", extra_args=["file1", "file2"]),
                call(
                    "local\\build\\path", "local\\frosty\\path\\SP", extra_args=["file1", "file2"]
                ),
            ]
        )

    def test_code_platform_win64(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_5] + self.DEFAULT_ARGS,
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_code.assert_has_calls(
            [
                call(self.VALUE_CODE_BRANCH, self.VALUE_CODE_CHANGELIST, "pipeline", "release"),
                call(
                    self.VALUE_CODE_BRANCH,
                    self.VALUE_CODE_CHANGELIST,
                    "win64game",
                    self.ARGUMENT_CONFIG,
                ),
            ]
        )

    def test_code_platform_win32(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_10] + self.DEFAULT_ARGS,
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_code.assert_has_calls(
            [
                call(self.VALUE_CODE_BRANCH, self.VALUE_CODE_CHANGELIST, "pipeline", "release"),
                call(
                    self.VALUE_CODE_BRANCH,
                    self.VALUE_CODE_CHANGELIST,
                    "win64game",
                    self.ARGUMENT_CONFIG,
                ),
            ]
        )

    @patch("elipy2.frostbite_core.get_licensee_id")
    def test_roboto_arg(self, mock_get_licensee_id):
        mock_get_licensee_id.return_value = "Roboto"
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_9]
            + self.DEFAULT_ARGS
            + [self.OPTION_FROSTY_ARGS, self.VALUE_FROSTY_ARGS],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww",
            frosty_args=["arg2", "COBRA_ON_ROBOTO=True"],
            run_bespoke=False,
        )

    def test_additional_configs_digital_failure(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_9, self.ARGUMENT_PACKAGE_TYPE_2, self.ARGUMENT_CONFIG]
            + self.BASIC_ARGS
            + [self.OPTION_ADDITIONAL_CONFIGS, self.ARGUMENT_CONFIG_2],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 1

    def test_enable_eac_files(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_5]
            + self.DEFAULT_ARGS
            + [self.OPTION_FROSTY_ARGS, self.VALUE_FROSTY_ARGS, self.OPTION_ENABLE_EAC],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww",
            frosty_args=[
                "arg2",
                "ANTICHEAT_ENABLED=True",
                "IS_TRIAL_BUILD=false",
                "USE_DENUVO=false",
            ],
            run_bespoke=False,
        )

    def test_enable_eac_digital(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_5, self.ARGUMENT_PACKAGE_TYPE_2, self.ARGUMENT_CONFIG]
            + self.BASIC_ARGS
            + [self.OPTION_FROSTY_ARGS, self.VALUE_FROSTY_ARGS, self.OPTION_ENABLE_EAC],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww",
            frosty_args=[
                "arg2",
                "ANTICHEAT_ENABLED=True",
                "IS_TRIAL_BUILD=false",
                "USE_DENUVO=false",
            ],
            run_bespoke=False,
        )

    def test_enable_eac_non_win64(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_9]
            + self.DEFAULT_ARGS
            + [self.OPTION_FROSTY_ARGS, self.VALUE_FROSTY_ARGS, self.OPTION_ENABLE_EAC],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww",
            frosty_args=["arg2"],
            run_bespoke=False,
        )

    def test_recompression_cache(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_5]
            + self.DEFAULT_ARGS
            + [
                self.OPTION_FROSTY_ARGS,
                self.VALUE_FROSTY_ARGS,
                self.OPTION_USE_RECOMPRESSION_CACHE,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww",
            frosty_args=[
                "arg2",
                "RECOMPRESSION_CACHE_SERVER=server.address",
                "IS_TRIAL_BUILD=false",
                "USE_DENUVO=false",
            ],
            run_bespoke=False,
        )

    @patch("dice_elipy_scripts.frosty.SETTINGS", config_manager_2)
    def test_frosty_game_config_flags_xbsx(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_2]
            + self.DEFAULT_ARGS
            + [self.OPTION_FROSTY_ARGS, self.VALUE_FROSTY_ARGS],
        )
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww",
            frosty_args=["arg2", "UPDCOMPAT=3", "XBSX_PUT_BUILD_LABEL_INTO_VERSION=True"],
            run_bespoke=False,
        )

    @patch("elipy2.frostbite_core.get_licensee_id")
    @patch.dict(os.environ, {"DurangoXDK": "durango\\path"}, clear=True)
    @patch("dice_elipy_scripts.frosty.SETTINGS", config_manager_2)
    def test_frosty_game_config_flags_xb1_files_retail(self, mock_get_licensee_id):
        mock_get_licensee_id.return_value = "fake_licensee"
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_1]
            + self.DEFAULT_ARGS
            + [self.OPTION_FROSTY_ARGS, self.VALUE_FROSTY_ARGS],
        )
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww",
            frosty_args=[
                "arg2",
                "UPDCOMPAT=3",
                "DurangoXDK=durango\\path",
                "XBONE_CLEAN_NETWORK_MANIFEST=True",
                "XB1_APPXMANIFEST_GENERATE_ALL_CONFIGURATIONS=True",
                "XB1_PUT_BUILD_LABEL_INTO_VERSION=True",
            ],
            run_bespoke=False,
        )

    @patch("elipy2.frostbite_core.get_licensee_id")
    @patch.dict(os.environ, {"DurangoXDK": "durango\\path"}, clear=True)
    @patch("dice_elipy_scripts.frosty.SETTINGS", config_manager_2)
    def test_frosty_game_config_flags_xb1_files_release(self, mock_get_licensee_id):
        mock_get_licensee_id.return_value = "fake_licensee"
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_1, self.ARGUMENT_PACKAGE_TYPE, self.ARGUMENT_CONFIG_2]
            + self.BASIC_ARGS
            + [self.OPTION_FROSTY_ARGS, self.VALUE_FROSTY_ARGS],
        )
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww",
            frosty_args=[
                "arg2",
                "UPDCOMPAT=3",
                "DurangoXDK=durango\\path",
                "XB1_APPXMANIFEST_GENERATE_ALL_CONFIGURATIONS=True",
                "XB1_PUT_BUILD_LABEL_INTO_VERSION=True",
            ],
            run_bespoke=False,
        )

    @patch("elipy2.frostbite_core.get_licensee_id")
    @patch.dict(os.environ, {"DurangoXDK": "durango\\path"}, clear=True)
    @patch("dice_elipy_scripts.frosty.SETTINGS", config_manager_2)
    def test_frosty_game_config_flags_xb1_digital_retail(self, mock_get_licensee_id):
        mock_get_licensee_id.return_value = "fake_licensee"
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_1, self.ARGUMENT_PACKAGE_TYPE_2, self.ARGUMENT_CONFIG]
            + self.BASIC_ARGS
            + [self.OPTION_FROSTY_ARGS, self.VALUE_FROSTY_ARGS],
        )
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww",
            frosty_args=[
                "arg2",
                "UPDCOMPAT=3",
                "DurangoXDK=durango\\path",
                "XBONE_CLEAN_NETWORK_MANIFEST=True",
                "XB1_PUT_BUILD_LABEL_INTO_VERSION=True",
            ],
            run_bespoke=False,
        )

    @patch("elipy2.frostbite_core.get_licensee_id")
    @patch.dict(os.environ, {"GameDK": "gamedk\\path"}, clear=True)
    @patch("dice_elipy_scripts.frosty.SETTINGS", config_manager_2)
    def test_frosty_game_config_flags_xb1_digital_release_new_frostbite(self, mock_get_licensee_id):
        mock_get_licensee_id.return_value = "fake_licensee"
        self.mock_read_fb_version.return_value = "2022-1-PR2"
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_1, self.ARGUMENT_PACKAGE_TYPE_2, self.ARGUMENT_CONFIG_2]
            + self.BASIC_ARGS
            + [self.OPTION_FROSTY_ARGS, self.VALUE_FROSTY_ARGS],
        )
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww",
            frosty_args=[
                "arg2",
                "UPDCOMPAT=3",
                "GameDK=gamedk\\path",
                "XB1_PUT_BUILD_LABEL_INTO_VERSION=True",
            ],
            run_bespoke=False,
        )

    def test_win64_default_args(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_5]
            + self.DEFAULT_ARGS
            + [self.OPTION_FROSTY_ARGS, self.VALUE_FROSTY_ARGS],
        )
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww",
            frosty_args=["arg2", "IS_TRIAL_BUILD=false", "USE_DENUVO=false"],
            run_bespoke=False,
        )

    def test_win64_trial(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_5]
            + self.DEFAULT_ARGS
            + [self.OPTION_FROSTY_ARGS, self.VALUE_FROSTY_ARGS, self.OPTION_USE_WIN64TRIAL],
        )
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww",
            frosty_args=["arg2", "IS_TRIAL_BUILD=true", "USE_DENUVO=false"],
            run_bespoke=False,
        )

    def test_win64_denuvo(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_5]
            + self.DEFAULT_ARGS
            + [self.OPTION_FROSTY_ARGS, self.VALUE_FROSTY_ARGS, self.OPTION_USE_DENUVO],
        )
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww",
            frosty_args=["arg2", "IS_TRIAL_BUILD=false", "USE_DENUVO=true"],
            run_bespoke=False,
        )

    def test_win64_oreans(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_5]
            + self.DEFAULT_ARGS
            + [self.OPTION_FROSTY_ARGS, self.VALUE_FROSTY_ARGS, self.OPTION_USE_OREANS],
        )
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww",
            frosty_args=[
                "arg2",
                "IS_TRIAL_BUILD=false",
                "USE_DENUVO=false",
                "EXECUTABLE_POSTFIX=" + OREANS_OUTPUT_SUFFIX,
            ],
            run_bespoke=False,
        )

    def test_win64_no_denuvo_not_retail(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_5, self.ARGUMENT_PACKAGE_TYPE, self.ARGUMENT_CONFIG_2]
            + self.BASIC_ARGS
            + [self.OPTION_FROSTY_ARGS, self.VALUE_FROSTY_ARGS, self.OPTION_USE_DENUVO],
        )
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww",
            frosty_args=["arg2", "IS_TRIAL_BUILD=false", "USE_DENUVO=false"],
            run_bespoke=False,
        )

    def test_ps4_default_args(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_3]
            + self.DEFAULT_ARGS
            + [self.OPTION_FROSTY_ARGS, self.VALUE_FROSTY_ARGS],
        )
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww",
            frosty_args=["arg2"],
            run_bespoke=False,
        )

    @patch("dice_elipy_scripts.frosty.SETTINGS", config_manager_2)
    def test_ps4_streaming_install(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_3]
            + self.DEFAULT_ARGS
            + [self.OPTION_FROSTY_ARGS, self.VALUE_FROSTY_ARGS],
        )
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww",
            frosty_args=["arg2", "STREAMING_INSTALL_CREATE_SUBMISSION_PACKAGES=True"],
            run_bespoke=False,
        )

    @patch("dice_elipy_scripts.frosty.SETTINGS", config_manager_2)
    def test_ps4_skip_streaming_install_flag(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_3]
            + self.DEFAULT_ARGS
            + [
                self.OPTION_FROSTY_ARGS,
                self.VALUE_FROSTY_ARGS,
                self.OPTION_SKIP_STREAMING_INSTALL_PACKAGE,
            ],
        )
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww",
            frosty_args=["arg2"],
            run_bespoke=False,
        )

    @patch("dice_elipy_scripts.frosty.SETTINGS", config_manager_2)
    def test_ps4_skip_streaming_install_not_retail(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_3, self.ARGUMENT_PACKAGE_TYPE, self.ARGUMENT_CONFIG_2]
            + self.BASIC_ARGS
            + [self.OPTION_FROSTY_ARGS, self.VALUE_FROSTY_ARGS],
        )
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww",
            frosty_args=["arg2"],
            run_bespoke=False,
        )

    def test_win64_symbols(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_5, self.ARGUMENT_PACKAGE_TYPE_2, self.ARGUMENT_CONFIG]
            + self.BASIC_ARGS
            + [self.OPTION_FROSTY_ARGS, self.VALUE_FROSTY_ARGS],
        )
        self.mock_symbolsutils.return_value.upload_game_binary.assert_called_once_with(
            platform=self.ARGUMENT_PLATFORM_5,
            config=self.ARGUMENT_CONFIG,
            package_type=self.ARGUMENT_PACKAGE_TYPE_2,
            data_changelist=self.VALUE_DATA_CHANGELIST,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            data_branch=self.VALUE_DATA_BRANCH,
            code_branch=self.VALUE_CODE_BRANCH,
        )

    def test_win64_trial_symbols(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_5, self.ARGUMENT_PACKAGE_TYPE_2, self.ARGUMENT_CONFIG]
            + self.BASIC_ARGS
            + [self.OPTION_FROSTY_ARGS, self.VALUE_FROSTY_ARGS, self.OPTION_USE_WIN64TRIAL],
        )
        self.mock_symbolsutils.return_value.upload_game_binary.assert_has_calls(
            [
                call(
                    platform=self.ARGUMENT_PLATFORM_5,
                    config=self.ARGUMENT_CONFIG,
                    package_type=self.ARGUMENT_PACKAGE_TYPE_2,
                    data_changelist=self.VALUE_DATA_CHANGELIST,
                    code_changelist=self.VALUE_CODE_CHANGELIST,
                    data_branch=self.VALUE_DATA_BRANCH,
                    code_branch=self.VALUE_CODE_BRANCH,
                ),
                call(
                    platform="win64trial",
                    config=self.ARGUMENT_CONFIG,
                    package_type="digital",
                    data_changelist=self.VALUE_DATA_CHANGELIST,
                    code_changelist=self.VALUE_CODE_CHANGELIST,
                    data_branch=self.VALUE_DATA_BRANCH,
                    code_branch=self.VALUE_CODE_BRANCH,
                ),
            ]
        )
        assert self.mock_symbolsutils.return_value.upload_game_binary.call_count == 2

    def test_not_dry_run(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_9]
            + self.DEFAULT_ARGS
            + [self.OPTION_DATA_DIRECTORY, self.VALUE_DATA_DIRECTORY],
        )
        self.mock_filerutils.return_value.deploy_frosty_build.assert_called_once_with(
            data_changelist=self.VALUE_DATA_CHANGELIST,
            data_branch=self.VALUE_DATA_BRANCH,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            code_branch=self.VALUE_CODE_BRANCH,
            package_type=self.ARGUMENT_PACKAGE_TYPE,
            region="ww",
            config=self.ARGUMENT_CONFIG,
            additional_configs=[],
            dataset=self.VALUE_DATA_DIRECTORY,
            platform=self.ARGUMENT_PLATFORM_9,
            content_layer=None,
        )

    def test_dry_run(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_9]
            + self.DEFAULT_ARGS
            + [self.OPTION_DATA_DIRECTORY, self.VALUE_DATA_DIRECTORY, self.OPTION_DRY_RUN],
        )
        assert self.mock_filerutils.return_value.deploy_frosty_build.call_count == 0

    def test_built_levels_server(self):
        self.mock_get_full_database_name.return_value = "avalanche_db_name"
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_7] + self.DEFAULT_ARGS,
        )
        self.mock_get_built_levels.assert_called_once_with(
            "avalanche_db_name", to_file="local\\frosty\\path\\builtLevels.json"
        )

    def test_built_levels_linuxserver(self):
        self.mock_get_full_database_name.return_value = "avalanche_db_name"
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_8] + self.DEFAULT_ARGS,
        )
        self.mock_get_built_levels.assert_called_once_with(
            "avalanche_db_name", to_file="local\\frosty\\path\\builtLevels.json"
        )

    def test_built_levels_linux64(self):
        self.mock_get_full_database_name.return_value = "avalanche_db_name"
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_6] + self.DEFAULT_ARGS,
        )
        self.mock_get_built_levels.assert_called_once_with(
            "avalanche_db_name", to_file="local\\frosty\\path\\builtLevels.json"
        )

    def test_file_hashes_created(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_1]
            + self.DEFAULT_ARGS
            + [self.OPTION_FILE_HASHES, self.VALUE_FILE_HASHES],
        )
        assert result.exit_code == 0
        self.mock_md5_hash_folder.assert_called_once_with(
            "local\\frosty\\path",
            skipped_files=["*.pdb", "some_file.txt"],
            output_file_type="csv",
            chunk_size=65536,
        )

    def test_file_hashes_not_created_default(self):
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_1] + self.DEFAULT_ARGS)
        assert result.exit_code == 0
        assert self.mock_md5_hash_folder.call_count == 0

    def test_file_hashes_not_created_digital(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_1, self.ARGUMENT_PACKAGE_TYPE_2, self.ARGUMENT_CONFIG]
            + self.BASIC_ARGS
            + [self.OPTION_FILE_HASHES, self.VALUE_FILE_HASHES],
        )
        assert result.exit_code == 0
        assert self.mock_md5_hash_folder.call_count == 0

    @patch("os.path.split", MagicMock(side_effect=mock_split))
    def test_combine_bundles_fetch(self):
        self.mock_local_bundles_path.side_effect = [
            os.path.join("local", "bundles", "data"),
            os.path.join("local", "bundles_main", "data"),
            os.path.join("local", "bundles_combine", "data"),
        ]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_1]
            + self.DEFAULT_ARGS
            + [
                self.OPTION_USE_COMBINE_BUNDLES,
                self.VALUE_USE_COMBINE_BUNDLES,
                self.OPTION_COMBINE_CODE_BRANCH,
                self.VALUE_COMBINE_CODE_BRANCH,
                self.OPTION_COMBINE_CODE_CHANGELIST,
                self.VALUE_COMBINE_CODE_CHANGELIST,
                self.OPTION_COMBINE_DATA_BRANCH,
                self.VALUE_COMBINE_DATA_BRANCH,
                self.OPTION_COMBINE_DATA_CHANGELIST,
                self.VALUE_COMBINE_DATA_CHANGELIST,
            ],
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_head_bundles.assert_has_calls(
            [
                call(
                    code_branch=self.VALUE_CODE_BRANCH,
                    code_changelist=self.VALUE_CODE_CHANGELIST,
                    data_branch=self.VALUE_DATA_BRANCH,
                    data_changelist=self.VALUE_DATA_CHANGELIST,
                    platform=self.ARGUMENT_PLATFORM_1,
                    dest=os.path.join("local", "bundles_main"),
                    bundles_dir_name="combine_bundles",
                ),
                call(
                    code_branch=self.VALUE_COMBINE_CODE_BRANCH,
                    code_changelist=self.VALUE_COMBINE_CODE_CHANGELIST,
                    data_branch=self.VALUE_COMBINE_DATA_BRANCH,
                    data_changelist=self.VALUE_COMBINE_DATA_CHANGELIST,
                    platform=self.ARGUMENT_PLATFORM_1,
                    dest=os.path.join("local", "bundles_combine"),
                    bundles_dir_name="combine_bundles",
                ),
            ]
        )

    @patch("os.path.split", MagicMock(side_effect=mock_split))
    def test_combine_bundles_fetch_linuxserver(self):
        self.mock_local_bundles_path.side_effect = [
            os.path.join("local", "bundles", "data"),
            os.path.join("local", "bundles_main", "data"),
            os.path.join("local", "bundles_combine", "data"),
        ]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_8]
            + self.DEFAULT_ARGS
            + [
                self.OPTION_USE_COMBINE_BUNDLES,
                self.VALUE_USE_COMBINE_BUNDLES,
                self.OPTION_COMBINE_CODE_BRANCH,
                self.VALUE_COMBINE_CODE_BRANCH,
                self.OPTION_COMBINE_CODE_CHANGELIST,
                self.VALUE_COMBINE_CODE_CHANGELIST,
                self.OPTION_COMBINE_DATA_BRANCH,
                self.VALUE_COMBINE_DATA_BRANCH,
                self.OPTION_COMBINE_DATA_CHANGELIST,
                self.VALUE_COMBINE_DATA_CHANGELIST,
            ],
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_head_bundles.assert_has_calls(
            [
                call(
                    code_branch=self.VALUE_CODE_BRANCH,
                    code_changelist=self.VALUE_CODE_CHANGELIST,
                    data_branch=self.VALUE_DATA_BRANCH,
                    data_changelist=self.VALUE_DATA_CHANGELIST,
                    platform=self.ARGUMENT_PLATFORM_7,
                    dest=os.path.join("local", "bundles_main"),
                    bundles_dir_name="combine_bundles",
                ),
                call(
                    code_branch=self.VALUE_COMBINE_CODE_BRANCH,
                    code_changelist=self.VALUE_COMBINE_CODE_CHANGELIST,
                    data_branch=self.VALUE_COMBINE_DATA_BRANCH,
                    data_changelist=self.VALUE_COMBINE_DATA_CHANGELIST,
                    platform=self.ARGUMENT_PLATFORM_7,
                    dest=os.path.join("local", "bundles_combine"),
                    bundles_dir_name="combine_bundles",
                ),
            ]
        )

    @patch("os.path.split", MagicMock(side_effect=mock_split))
    def test_combine_bundles_run_non_xbsx(self):
        self.mock_local_bundles_path.side_effect = [
            os.path.join("local", "bundles", "data"),
            os.path.join("local", "bundles_main", "data"),
            os.path.join("local", "bundles_combine", "data"),
        ]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_4]
            + self.DEFAULT_ARGS
            + [
                self.OPTION_USE_COMBINE_BUNDLES,
                self.VALUE_USE_COMBINE_BUNDLES,
                self.OPTION_COMBINE_CODE_BRANCH,
                self.VALUE_COMBINE_CODE_BRANCH,
                self.OPTION_COMBINE_CODE_CHANGELIST,
                self.VALUE_COMBINE_CODE_CHANGELIST,
                self.OPTION_COMBINE_DATA_BRANCH,
                self.VALUE_COMBINE_DATA_BRANCH,
                self.OPTION_COMBINE_DATA_CHANGELIST,
                self.VALUE_COMBINE_DATA_CHANGELIST,
                self.OPTION_REGION,
                self.VALUE_REGION,
            ],
        )
        assert result.exit_code == 0
        self.mock_combine.assert_called_once_with(
            input_dir_1=os.path.join("local", "bundles_main"),
            input_dir_2=os.path.join("local", "bundles_combine"),
            output_dir=os.path.join("local", "bundles"),
            extra_combine_args=["-s", "project-combine-hres.yaml"],
        )

    @patch("os.path.split", MagicMock(side_effect=mock_split))
    def test_combine_bundles_run_xbsx(self):
        self.mock_local_bundles_path.side_effect = [
            os.path.join("local", "bundles", "data"),
            os.path.join("local", "bundles_main", "data"),
            os.path.join("local", "bundles_combine", "data"),
        ]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_2]
            + self.DEFAULT_ARGS
            + [
                self.OPTION_USE_COMBINE_BUNDLES,
                self.VALUE_USE_COMBINE_BUNDLES,
                self.OPTION_COMBINE_CODE_BRANCH,
                self.VALUE_COMBINE_CODE_BRANCH,
                self.OPTION_COMBINE_CODE_CHANGELIST,
                self.VALUE_COMBINE_CODE_CHANGELIST,
                self.OPTION_COMBINE_DATA_BRANCH,
                self.VALUE_COMBINE_DATA_BRANCH,
                self.OPTION_COMBINE_DATA_CHANGELIST,
                self.VALUE_COMBINE_DATA_CHANGELIST,
                self.OPTION_REGION,
                self.VALUE_REGION,
            ],
        )
        assert result.exit_code == 0
        self.mock_combine.assert_called_once_with(
            input_dir_1=os.path.join("local", "bundles_main"),
            input_dir_2=os.path.join("local", "bundles_combine"),
            output_dir=os.path.join("local", "bundles"),
            extra_combine_args=["-s", "project-combine-hres-smart-delivery.yaml"],
        )

    @patch("os.path.split", MagicMock(side_effect=mock_split))
    def test_combine_bundles_run_with_combined_settings_file(self):
        self.mock_local_bundles_path.side_effect = [
            os.path.join("local", "bundles", "data"),
            os.path.join("local", "bundles_main", "data"),
            os.path.join("local", "bundles_combine", "data"),
        ]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_2]
            + self.DEFAULT_ARGS
            + [
                self.OPTION_USE_COMBINE_BUNDLES,
                self.VALUE_USE_COMBINE_BUNDLES,
                self.OPTION_COMBINE_CODE_BRANCH,
                self.VALUE_COMBINE_CODE_BRANCH,
                self.OPTION_COMBINE_CODE_CHANGELIST,
                self.VALUE_COMBINE_CODE_CHANGELIST,
                self.OPTION_COMBINE_DATA_BRANCH,
                self.VALUE_COMBINE_DATA_BRANCH,
                self.OPTION_COMBINE_DATA_CHANGELIST,
                self.VALUE_COMBINE_DATA_CHANGELIST,
                self.OPTION_REGION,
                self.VALUE_REGION,
                self.OPTION_COMBINE_SETTINGS_FILE,
                self.VALUE_COMBINE_SETTINGS_FILE,
            ],
        )
        assert result.exit_code == 0
        self.mock_combine.assert_called_once_with(
            input_dir_1=os.path.join("local", "bundles_main"),
            input_dir_2=os.path.join("local", "bundles_combine"),
            output_dir=os.path.join("local", "bundles"),
            extra_combine_args=["-s", self.VALUE_COMBINE_SETTINGS_FILE],
        )

    @patch("os.path.split", MagicMock(side_effect=mock_split))
    def test_combine_bundles_deploy(self):
        self.mock_local_bundles_path.side_effect = [
            os.path.join("local", "bundles", "data"),
            os.path.join("local", "bundles_main", "data"),
            os.path.join("local", "bundles_combine", "data"),
        ]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_1]
            + self.DEFAULT_ARGS
            + [
                self.OPTION_USE_COMBINE_BUNDLES,
                self.VALUE_USE_COMBINE_BUNDLES,
                self.OPTION_COMBINE_CODE_BRANCH,
                self.VALUE_COMBINE_CODE_BRANCH,
                self.OPTION_COMBINE_CODE_CHANGELIST,
                self.VALUE_COMBINE_CODE_CHANGELIST,
                self.OPTION_COMBINE_DATA_BRANCH,
                self.VALUE_COMBINE_DATA_BRANCH,
                self.OPTION_COMBINE_DATA_CHANGELIST,
                self.VALUE_COMBINE_DATA_CHANGELIST,
                self.OPTION_REGION,
                self.VALUE_REGION,
            ],
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.deploy_frosty_build.assert_called_once_with(
            data_changelist=self.VALUE_DATA_CHANGELIST,
            data_branch=self.VALUE_DATA_BRANCH,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            code_branch=self.VALUE_CODE_BRANCH,
            package_type="files_combine",
            region=self.VALUE_REGION,
            config=self.ARGUMENT_CONFIG,
            additional_configs=[],
            dataset=self.VALUE_DATA_DIRECTORY,
            platform=self.ARGUMENT_PLATFORM_1,
            combine_data_branch=self.VALUE_COMBINE_DATA_BRANCH,
            combine_data_changelist=self.VALUE_COMBINE_DATA_CHANGELIST,
            combine_code_branch=self.VALUE_COMBINE_CODE_BRANCH,
            combine_code_changelist=self.VALUE_COMBINE_CODE_CHANGELIST,
        )

    def test_steam_build(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_5] + self.DEFAULT_ARGS + [self.OPTION_STEAM_BUILD, "True"],
        )

        assert result.exit_code == 0
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww",
            frosty_args=[
                "IS_TRIAL_BUILD=false",
                "USE_DENUVO=false",
                "WIN32_DIGITAL_PLATFORM=Steam",
                'STEAM_BUILD_DESCRIPTION="retail build from other-branch on CL 5678"',
            ],
            run_bespoke=False,
        )

    @patch(
        "elipy2.steam_utils.SteamUtils.download_steam_sdk",
        MagicMock(return_value="D:/packages/SteamSDK/version"),
    )
    @patch(
        "elipy2.steam_utils.SteamUtils.get_ess_steam_config_name",
        MagicMock(return_value="DRE_SVC_STEAM01"),
    )
    @patch(
        "elipy2.steam_utils.SteamUtils.prepare_steam_user_session",
        MagicMock(return_value="DRE_STEAM_ACCOUNT"),
    )
    @patch("elipy2.steam_utils.SETTINGS.get", MagicMock(return_value="test.yml"))
    @patch(
        "elipy2.steam_utils.SETTINGS.load_auxiliary_file_contents",
        MagicMock(
            return_value={
                "unique_sessions": {
                    "other-branch": {
                        "ww": {
                            "retail": {
                                "frosty": ["username"],
                            }
                        }
                    }
                }
            }
        ),
    )
    def test_steam_build_combined_bundles(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_5]
            + self.DEFAULT_ARGS
            + [
                self.OPTION_STEAM_BUILD,
                "True",
                self.OPTION_USE_COMBINE_BUNDLES,
                True,
                self.OPTION_STEAM_DRMWRAP,
            ],
        )

        assert result.exit_code == 0
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww",
            frosty_args=[
                "USE_ALREADYCREATED_SUPERBUNDLES=true",
                "SUPERBUNDLE_SOURCE_DIR=local\\bundle\\path\\data",
                "ADDITIONAL_LOCAL_ROOT=tnt_root\\SP_Local",
                "IS_TRIAL_BUILD=false",
                "USE_DENUVO=false",
                "WIN32_DIGITAL_PLATFORM=Steam",
                'STEAM_BUILD_DESCRIPTION="retail build from other-branch on CL 5678"',
                "STEAM_DRMWRAP=True",
                f"STEAM_USERID=DRE_STEAM_ACCOUNT",
                "STEAM_DRM_LOCAL=True",
            ],
            run_bespoke=False,
        )

    def test_frosty_build_with_content_layer(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_4]
            + self.DEFAULT_ARGS
            + [self.OPTION_CONTENT_LAYER, self.VALUE_CONTENT_LAYER],
        )

        assert result.exit_code == 0
        self.mock_datautils.return_value.cook.assert_called_once_with(
            pipeline_args=["-activeContentLayer", self.VALUE_CONTENT_LAYER],
            indexing_args=[],
            collect_mdmps=True,
            trim=True,
            clean_master_version_check=False,
        )
        self.mock_filerutils.return_value.deploy_frosty_build.assert_called_once_with(
            data_changelist=self.VALUE_DATA_CHANGELIST,
            data_branch=self.VALUE_DATA_BRANCH,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            code_branch=self.VALUE_CODE_BRANCH,
            package_type=self.ARGUMENT_PACKAGE_TYPE,
            region="ww",
            config=self.ARGUMENT_CONFIG,
            additional_configs=[],
            dataset=self.VALUE_DATA_DIRECTORY,
            platform=self.ARGUMENT_PLATFORM_4,
            content_layer=self.VALUE_CONTENT_LAYER,
        )

    def test_frosty_build_with_source_layer(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_4] + self.DEFAULT_ARGS + [self.OPTION_CONTENT_LAYER, "Source"],
        )

        assert result.exit_code == 0
        self.mock_get_frosty_build_path.assert_called_once_with(
            self.VALUE_DATA_BRANCH,
            self.VALUE_DATA_CHANGELIST,
            self.VALUE_CODE_BRANCH,
            self.VALUE_CODE_CHANGELIST,
            self.ARGUMENT_PLATFORM_4,
            self.ARGUMENT_PACKAGE_TYPE,
            "ww",
            self.ARGUMENT_CONFIG,
            content_layer=None,
        )
        self.mock_datautils.return_value.cook.assert_called_once_with(
            pipeline_args=[],
            indexing_args=[],
            collect_mdmps=True,
            trim=True,
            clean_master_version_check=False,
        )
        self.mock_filerutils.return_value.deploy_frosty_build.assert_called_once_with(
            data_changelist=self.VALUE_DATA_CHANGELIST,
            data_branch=self.VALUE_DATA_BRANCH,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            code_branch=self.VALUE_CODE_BRANCH,
            package_type=self.ARGUMENT_PACKAGE_TYPE,
            region="ww",
            config=self.ARGUMENT_CONFIG,
            additional_configs=[],
            dataset=self.VALUE_DATA_DIRECTORY,
            platform=self.ARGUMENT_PLATFORM_4,
            content_layer=None,
        )

    def test_frosty_build_with_content_layer_and_combined_bundles_raises_exception(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_4]
            + self.DEFAULT_ARGS
            + [
                self.OPTION_CONTENT_LAYER,
                self.VALUE_CONTENT_LAYER,
                self.OPTION_USE_COMBINE_BUNDLES,
                self.VALUE_USE_COMBINE_BUNDLES,
                self.OPTION_COMBINE_CODE_BRANCH,
                self.VALUE_COMBINE_CODE_BRANCH,
                self.OPTION_COMBINE_CODE_CHANGELIST,
                self.VALUE_COMBINE_CODE_CHANGELIST,
                self.OPTION_COMBINE_DATA_BRANCH,
                self.VALUE_COMBINE_DATA_BRANCH,
                self.OPTION_COMBINE_DATA_CHANGELIST,
                self.VALUE_COMBINE_DATA_CHANGELIST,
                self.OPTION_REGION,
                self.VALUE_REGION,
            ],
        )

        assert "Usage:" not in result.output
        assert result.exit_code == 1
        self.mock_datautils.cook.assert_not_called()
        self.mock_packageutils.assert_not_called()

    @patch.dict(os.environ, {"FB_DEFAULT_PLATFORM_DB": "GameData.other-branch.{}"})
    def test_virtual_branch_override(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_4]
            + self.DEFAULT_ARGS
            + [self.OPTION_VIRTUAL_BRANCH_OVERRIDE, "true"],
        )
        assert result.exit_code == 0
        self.mock_datautils.return_value.cook.assert_called_once_with(
            pipeline_args=[
                "-BuildSettings.ForceBranch",
                self.VALUE_DATA_BRANCH,
                "-stateId",
                self.VALUE_DATA_BRANCH,
            ],
            indexing_args=["-stateId", self.VALUE_DATA_BRANCH],
            collect_mdmps=True,
            trim=True,
            clean_master_version_check=False,
        )
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww",
            frosty_args=["DATABASE_ID=GameData.other-branch.{}", "FB_BRANCH_ID=other-branch"],
            run_bespoke=False,
        )
