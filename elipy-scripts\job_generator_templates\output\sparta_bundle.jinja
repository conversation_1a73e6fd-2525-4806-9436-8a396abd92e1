{#
    Command:
        sparta_bundle
            short_help: Performs a Sparta submission build.

    Arguments:
        data_dir

    Required variables:

    Optional variables:
        branch
            help: Perforce branch/stream name
        code_branch
            default: None
            help: Perforce branch/stream name
        code_changelist
            help: Perforce changelist number for last smoked/built pipeline
            default: ''
        source_bundle_path
            help: Path to the Sparta bundle
            default: ''
        bundle_changelist
            help: Changelist for the bundle
            default: 1337
        description
            help: Description for the P4 submit
            default: ''
        no_submit
            help: Specifies whether or not to submit to P4
            default: False
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    sparta_bundle
    data-dir {{ data_dir }}
    {%- if branch %}
    --branch {{ branch }}
    {%- endif %}
    {%- if code_branch %}
    --code-branch {{ code_branch }}
    {%- endif %}
    {%- if code_changelist %}
    --code-changelist {{ code_changelist }}
    {%- endif %}
    {%- if source_bundle_path %}
    --source-bundle-path {{ source_bundle_path }}
    {%- endif %}
    {%- if bundle_changelist %}
    --bundle-changelist {{ bundle_changelist }}
    {%- endif %}
    {%- if description %}
    --description {{ description }}
    {%- endif %}
    {%- if no_submit %}
    --no-submit {{ no_submit }}
    {%- endif %}
  displayName: elipy sparta_bundle
