"""
bilbo_register_smoke.py
"""
import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import filer_paths, LOGGER, SETTINGS, build_metadata_utils
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command(
    "bilbo_register_smoke",
    short_help="Registers a Smoked build in the configured metadata services.",
)
@click.option("--code-branch", help="Perforce code branch/stream name.", required=True)
@click.option(
    "--code-changelist",
    required=True,
    help="Changelist number of code build used to verify data.",
)
@click.option(
    "--data-changelist",
    default=None,
    help="Changelist number of data used to verify data.",
)
@click.option(
    "--extra-location",
    multiple=True,
    required=False,
    help="Another location to register this build",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(_, code_branch, code_changelist, data_changelist, extra_location):
    """
    Registers a code build as smoked in the configured metadata services.

    A drone build means is a QA verified build.
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    if not data_changelist or not data_changelist.isnumeric():
        data_changelist = None

    # Register original build as smoked
    metadata_manager = build_metadata_utils.setup_metadata_manager()

    LOGGER.info("Registering a build as smoked for {} with {}".format(code_branch, code_changelist))

    source = filer_paths.get_code_build_root_path(code_branch, code_changelist)
    metadata_manager.tag_code_build_as_smoked(path=source, data_changelist=data_changelist)

    # Run on all extra locations if provided
    locations = list(extra_location)
    for location in locations:
        metadata_manager = build_metadata_utils.setup_metadata_manager(
            bilbo_url=SETTINGS.get("bilbo_url", location=location), location=location
        )

        query_string = "type.keyword:drone AND branch.keyword:{} AND changelist.keyword:{}".format(
            code_branch, code_changelist
        )
        builds = list(metadata_manager.get_all_builds_query_string(query_string=query_string))

        if not builds:
            LOGGER.warning("No builds found at {}".format(location))
        else:
            extra_path = builds[0].id
            LOGGER.info("Marking build as smoked at {}: {}".format(location, extra_path))
            metadata_manager.tag_code_build_as_smoked(
                path=extra_path, data_changelist=data_changelist
            )
