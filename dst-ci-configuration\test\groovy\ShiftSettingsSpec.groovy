import com.ea.lib.jobsettings.ShiftSettings
import spock.lang.Specification

class ShiftSettingsSpec extends Specification {

    class BranchFile {
        static Map standard_jobs_settings = [
            branch_name                : 'branch',
            workspace_root             : 'workspace-root',
            disable_build              : true,
            elipy_call                 : 'elipy-call',
            elipy_install_call         : 'elipy-install-call',
            job_label                  : 'label',
            p4_fb_settings             : [p4_creds: 'p4-creds', p4_port: '1111'],
            statebuild_data            : true,
            shift_reference_job        : 'shift-reference-job',
            trigger_type_shift         : 'scm',
            elipy_shift_submission_tool: true,
            shift_compression          : true,
            offsitedrone_reference_job : 'offsitedrone-reference-job',
            reshift                    : true,
            job_label_statebuild       : 'name && util',
            use_zipped_drone_builds    : true,
            shift_upload_timeout_hours : 10,
        ]
        static Map general_settings = [:]
    }

    class MasterFile {
        static Map maintenance_branch = [branch: [data_branch: 'data-branch', data_folder: 'data', code_branch: 'code-branch', code_folder: 'code']]
        static Map branches = [branch: [data_branch: 'data-branch', code_branch: 'code-branch']]
    }

    class ProjectFile {
        static String name = 'Kingston'
        static String p4_code_client_env = 'p4-code-client-env'
        static String p4_data_client_env = 'p4-data-client-env'
        static String p4_user_single_slash = 'p4-user-single-slash'
        static String p4_code_server = 'p4-code-server'
        static String p4_data_server = 'p4-data-server'
        static String dataset = 'project-dataset'
        static Boolean vault_win64_trial = true
        static Boolean verify_post_vault = true
        static String p4_browser_url = 'localhost'
        static String short_name = 'kin'
    }

    static Map shiftSubscriptionInfo = [
        build_type   : 'code',
        dest_location: 'dest-location',
        src_location : 'src-location',
    ]

    void "test that we get expected job settings in initializeShiftStart"() {
        when:
        ShiftSettings settings = new ShiftSettings()
        settings.initializeShiftStart(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            isDisabled == BranchFile.standard_jobs_settings.disable_build
            description == 'Scheduler to shift start job for branch'
            referenceJob == 'shift-reference-job'
            triggerType == 'scm'
            cronTrigger == 'TZ=Europe/Stockholm \n H 0,13 * * 1-6\nH 6,13 * * 7'
        }
    }

    void "test that we get expected job settings in initializeShiftUpload"() {
        when:
        ShiftSettings settings = new ShiftSettings()
        settings.initializeShiftUpload(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            description == 'Job for uploading builds to Shift.'
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'
            elipyCmd == 'elipy-call submit_to_shift --user %monkey_shift_email% --password %monkey_shift_passwd% --code-branch code-branch --code-changelist %code_changelist% --data-branch data-branch --data-changelist %data_changelist% --artifactory-user %AF2_GENERIC_USER% --artifactory-apikey %AF2_GENERIC_TOKEN% --use-elipy-config --submission-tool true --compression true --shifter-type frosty_shifter --force-reshift %force_reshift% --build-id %build_id%'
            elipyInstallCall == 'elipy-install-call'
            timeoutMinutes == 600
        }
    }

    void "test that we get expected job settings in initializeShiftUploadForOffsiteBasicDroneZipBuilds"() {
        when:
        ShiftSettings settings = new ShiftSettings()
        settings.initializeShiftUpload(BranchFile, MasterFile, ProjectFile, 'branch', 'offsite_basic_drone_shifter')
        then:
        with(settings) {
            description == 'Job for uploading builds to Shift.'
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'
            elipyCmd == 'elipy-call submit_to_shift --user %monkey_shift_email% --password %monkey_shift_passwd% --code-branch code-branch --code-changelist %code_changelist% --data-branch data-branch --data-changelist %data_changelist% --artifactory-user %AF2_GENERIC_USER% --artifactory-apikey %AF2_GENERIC_TOKEN% --use-elipy-config --submission-tool true --compression true --shifter-type offsite_basic_drone_shifter --force-reshift %force_reshift%'
            elipyInstallCall == 'elipy-install-call'
        }
    }

    void "test that we get expected job settings in initializeShiftUpload for drone builds"() {
        when:
        ShiftSettings settings = new ShiftSettings()
        settings.initializeShiftUpload(BranchFile, MasterFile, ProjectFile, 'branch', 'offsite_drone_shifter')
        then:
        with(settings) {
            description == 'Job for uploading builds to Shift.'
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'
            elipyCmd == 'elipy-call submit_to_shift --user %monkey_shift_email% --password %monkey_shift_passwd% --code-branch code-branch --code-changelist %code_changelist% --data-branch data-branch --data-changelist %data_changelist% --artifactory-user %AF2_GENERIC_USER% --artifactory-apikey %AF2_GENERIC_TOKEN% --use-elipy-config --submission-tool true --compression true --shifter-type offsite_drone_shifter --force-reshift %force_reshift% --use-zipped-drone-builds'
            elipyInstallCall == 'elipy-install-call'
        }
    }

    void "test that we get expected job settings in initializeProcessShiftSubscriptionDownloads"() {
        when:
        BranchFile.standard_jobs_settings.elipy_call = 'elipy-call --location src-location'
        ShiftSettings settings = new ShiftSettings()
        settings.initializeProcessShiftSubscriptionDownloads(BranchFile, MasterFile, ProjectFile, 'branch', shiftSubscriptionInfo)
        then:
        with(settings) {
            buildName == '${JOB_NAME}.${BUILD_NUMBER}'
            cronTrigger == 'H/10 * * * 1-6\nH 6-23/2 * * 7'
            description == 'Job for processing builds downloaded with a Shift subscription, and registering these in Bilbo.'
            elipyCmd == 'elipy-call --location dest-location process_shift_subscription_downloads --build-type code --code-branch code-branch'
            elipyInstallCall == 'elipy-install-call'
            timeoutMinutes == 240
            triggerType == 'cron'
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
        }
    }

    void "test that we get expected job settings in initializeProcessShiftSubscriptionDownloads when running without trigger"() {
        when:
        BranchFile.standard_jobs_settings.elipy_call = 'elipy-call --location src-location'
        shiftSubscriptionInfo += [trigger_type: 'none']
        ShiftSettings settings = new ShiftSettings()
        settings.initializeProcessShiftSubscriptionDownloads(BranchFile, MasterFile, ProjectFile, 'branch', shiftSubscriptionInfo)
        then:
        with(settings) {
            buildName == '${JOB_NAME}.${BUILD_NUMBER}'
            cronTrigger == 'H/10 * * * 1-6\nH 6-23/2 * * 7'
            description == 'Job for processing builds downloaded with a Shift subscription, and registering these in Bilbo.'
            elipyCmd == 'elipy-call --location dest-location process_shift_subscription_downloads --build-type code --code-branch code-branch'
            elipyInstallCall == 'elipy-install-call'
            timeoutMinutes == 240
            triggerType == 'none'
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
        }
    }

    void "test that we get expected job settings in initializeShiftUpload when shift-retention-policy argument when set"() {
        when:
        BranchFile.standard_jobs_settings.shift_retention_policy = 'Archive'
        ShiftSettings settings = new ShiftSettings()
        settings.initializeShiftUpload(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            buildName == '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'
            elipyCmd == 'elipy-call --location src-location submit_to_shift --user %monkey_shift_email% --password %monkey_shift_passwd% --code-branch code-branch --code-changelist %code_changelist% --data-branch data-branch --data-changelist %data_changelist% --artifactory-user %AF2_GENERIC_USER% --artifactory-apikey %AF2_GENERIC_TOKEN% --use-elipy-config --submission-tool true --compression true --shift-retention-policy Archive --shifter-type frosty_shifter --force-reshift %force_reshift% --build-id %build_id%'
            elipyInstallCall == 'elipy-install-call'
        }
    }

    void "test that we append --build-id to extraArgs only for frosty_shifter"() {
        when:
        ShiftSettings settings = new ShiftSettings()
        settings.initializeShiftUpload(BranchFile, MasterFile, ProjectFile, 'branch', 'frosty_shifter')
        then:
        with(settings) {
            buildName == '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'
            elipyCmd == 'elipy-call --location src-location submit_to_shift --user %monkey_shift_email% --password %monkey_shift_passwd% --code-branch code-branch --code-changelist %code_changelist% --data-branch data-branch --data-changelist %data_changelist% --artifactory-user %AF2_GENERIC_USER% --artifactory-apikey %AF2_GENERIC_TOKEN% --use-elipy-config --submission-tool true --compression true --shift-retention-policy Archive --shifter-type frosty_shifter --force-reshift %force_reshift% --build-id %build_id%'
            elipyInstallCall == 'elipy-install-call'
        }

        when:
        settings = new ShiftSettings()
        settings.initializeShiftUpload(BranchFile, MasterFile, ProjectFile, 'branch', 'offsite_drone_shifter')
        then:
        with(settings) {
            buildName == '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'
            elipyCmd == 'elipy-call --location src-location submit_to_shift --user %monkey_shift_email% --password %monkey_shift_passwd% --code-branch code-branch --code-changelist %code_changelist% --data-branch data-branch --data-changelist %data_changelist% --artifactory-user %AF2_GENERIC_USER% --artifactory-apikey %AF2_GENERIC_TOKEN% --use-elipy-config --submission-tool true --compression true --shift-retention-policy Archive --shifter-type offsite_drone_shifter --force-reshift %force_reshift% --use-zipped-drone-builds'
            elipyInstallCall == 'elipy-install-call'
        }
    }
}
