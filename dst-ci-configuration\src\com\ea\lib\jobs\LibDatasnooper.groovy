package com.ea.lib.jobs

import com.ea.lib.LibJobDsl
import com.ea.lib.jobsettings.DataSnooperSettings

class LibDatasnooper {
    /**
     * Start datasnooper jobs
     */
    static void datasnooper_start(def job, def project, def branchFile, def masterFile, String branchName) {
        DataSnooperSettings dataSnooperSettings = new DataSnooperSettings()
        dataSnooperSettings.initializeDataSnooperStart(branchFile, masterFile, project, branchName)
        job.with {
            description(dataSnooperSettings.description)
            disabled(dataSnooperSettings.isDisabled)
            logRotator(7, 100)
            quietPeriod(0)
            properties {
                disableConcurrentBuilds()
                disableResume()
                pipelineTriggers {
                    triggers {
                        cron {
                            spec(dataSnooperSettings.cronTrigger)
                        }
                    }
                }
            }
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to sync.')
                    trim(true)
                }
            }
            environmentVariables {
                env('branch_name', dataSnooperSettings.branchName)
                env('data_branch', dataSnooperSettings.dataBranch)
                env('data_folder', dataSnooperSettings.dataFolder)
                env('datasnooper_reference_job', dataSnooperSettings.dataSnooperReferenceJob)
                env('dataset', dataSnooperSettings.dataset)
                env('frostbite_syncer_setup', dataSnooperSettings.frostbiteSyncerSetup)
                env('non_virtual_data_branch', dataSnooperSettings.nonVirtualDataBranch)
                env('non_virtual_data_folder', dataSnooperSettings.nonVirtualDataFolder)
                env('project_name', dataSnooperSettings.projectName)
            }
        }
    }

    /**
     * Adds generic job parameters for code.drone build jobs.
     */
    static void datasnooper_job(def job, def project, def branchFile, def masterFile, String branchName) {
        DataSnooperSettings dataSnooperSettings = new DataSnooperSettings()
        dataSnooperSettings.initializeDataSnooperJob(branchFile, masterFile, project, branchName)
        job.with {
            description(dataSnooperSettings.description)
            label(dataSnooperSettings.jobLabel)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(dataSnooperSettings.workspaceRoot)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to sync.')
                    trim(true)
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(dataSnooperSettings.buildName)
                timeout {
                    absolute(dataSnooperSettings.timeoutMinutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, dataSnooperSettings.elipyInstallCall, project)
                batchFile(dataSnooperSettings.elipyCmd)
            }
        }
    }
}
