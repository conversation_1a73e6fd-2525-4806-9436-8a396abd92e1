package com.ea.lib.jobsettings

import com.ea.lib.LibCommonNonCps

class PipelineWarningSettings extends JobSetting {

    String pipelineWarningReferenceJob
    String cronTrigger
    String extraArgs
    String userCredentials
    String frostbiteLoginP4Creds
    String frostbiteLoginP4Port

    void initializePipelineWarningStart(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        def modifiers = ['pipeline_warning']
        def warnInfo = projectFile.pipeline_warning_settings
        isDisabled = LibCommonNonCps.get_setting_value(branchInfo, modifiers, 'disable_build', false)
        pipelineWarningReferenceJob = warnInfo.reference_job ?: "${branchName}.data.start"
        description = "Job that runs the pipeline warning extraction scripts for ${branchName}."
        projectName = projectFile.name
        cronTrigger = '@midnight'
    }

    void initializePipelineWarningJob(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        def frostbiteLoginDetails = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_fb_settings', [:], projectFile)
        def warnInfo = projectFile.pipeline_warning_settings
        def importAvalanche = warnInfo.import_avalanche_state != null ? warnInfo.import_avalanche_state : true
        def warnAsset = warnInfo.asset ?: branchInfo.asset
        String frostbiteLicensee = branchInfo.frostbite_licensee
        List extraArgsList = branchInfo.extra_data_args as List ?: []
        def timeoutHours = warnInfo.timeout_hours ?: 4
        jobLabel = branchInfo.job_label_statebuild ?: 'statebuild'
        description = "Job that runs the pipeline warning extraction scripts for ${branchName}."
        extraArgs = extraArgsList.join(' ')
        extraArgs += """${sw ->
            if (frostbiteLicensee) { sw << " --licensee ${frostbiteLicensee}" }
            if (warnInfo.user_credentials) { sw << ' --username %pwarn_user% --password "%pwarn_passwd%"' }
            if (importAvalanche) { sw << ' --import-avalanche-state' }
            if (warnInfo.target_address) { sw << " --target-address ${warnInfo.target_address}" }
        }"""
        timeoutMinutes = timeoutHours * 60
        buildName = '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'
        userCredentials = warnInfo.user_credentials
        frostbiteLoginP4Creds = frostbiteLoginDetails.p4_creds
        frostbiteLoginP4Port = frostbiteLoginDetails.p4_port
        elipyCmd = "${this.elipyCall} pipeline_warning --data-directory ${branchInfo.dataset}" +
            " --platform ${warnInfo.platform} --assets ${warnAsset}" +
            " --code-branch ${branchInfo.code_branch} --code-changelist %code_changelist%" +
            " --data-branch ${branchInfo.data_branch} --data-changelist %data_changelist%" +
            " --data-clean %clean_data% ${extraArgs}"
    }
}
