package com.ea.lib.jobsettings

import com.ea.lib.LibCommonNonCps

class GametoolSettings extends JobSetting {
    String nonVirtualCodeBranch
    String nonVirtualCodeFolder
    Map fbLoginDetails

    void initializeStart(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        description = "Sync ${branchInfo.branch_name} code and deploy a binary build."
        cronTrigger = branchInfo.gametool_settings.trigger ?: 'H/2 * * * 1-6\nH/2 6-23 * * 7'
        nonVirtualCodeBranch = branchInfo.gametool_settings.non_virtual_code_branch ?: ''
        nonVirtualCodeFolder = branchInfo.gametool_settings.non_virtual_code_folder ?: ''
        codeBranch = branchInfo.code_branch
        codeFolder = branchInfo.code_folder
        projectName = projectFile.name
    }

    void initializeIcepick(def branchFile, def masterFile, def projectFile, String branchName, String gametool) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)

        jobLabel = branchInfo.gametool_settings.gametools."$gametool".job_label ?: branchInfo.job_label_statebuild ?: 'statebuild'

        int timeoutHours = branchInfo.gametool_settings.gametools."$gametool".timeout_hours ?: 12
        timeoutMinutes = timeoutHours * 60
        fbLoginDetails = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_fb_settings', [:], projectFile) as Map

        String frostbiteLicensee = branchInfo.frostbite_licensee
        extraArgs = branchInfo.gametool_settings.gametools."$gametool".extra_args ?: ''
        if (frostbiteLicensee) {
            extraArgs += " --licensee ${frostbiteLicensee}"
        }
        branchInfo.gametool_settings.gametools."$gametool".framework_args.each {
            extraArgs += " --framework-args ${it}"
        }

        String config = branchInfo.gametool_settings.gametools."$gametool".config ?: 'release'
        buildName = '${JOB_NAME}.${ENV, var="CODE_CHANGELIST"}'
        description = 'Compiles and submits Icepick'

        elipyCmd = "${this.elipyCall} gametool/gametool icepick --code-changelist %CODE_CHANGELIST% --clean %CLEAN_LOCAL%" +
            "${extraArgs} --config ${config} --submit %SUBMIT%"
    }

    void initializeFrostbiteDatabaseUpgrader(def branchFile, def masterFile, def projectFile, String branchName, String gametool) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)

        jobLabel = branchInfo.gametool_settings.gametools."$gametool".job_label ?: branchInfo.job_label_statebuild ?: 'statebuild'

        int timeoutHours = branchInfo.gametool_settings.gametools."$gametool".timeout_hours ?: 12
        timeoutMinutes = timeoutHours * 60
        extraArgs = branchInfo.gametool_settings.gametools."$gametool".extra_args ?: ''
        branchInfo.gametool_settings.gametools."$gametool".framework_args.each {
            extraArgs += " --framework-args ${it}"
        }
        buildName = '${JOB_NAME}.${ENV, var="CODE_CHANGELIST"}'
        description = 'Compiles and submits Frostbite Database Upgrader'
        String config = branchInfo.gametool_settings.gametools."$gametool".config ?: 'release'
        String p4Port = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_code_server', '', projectFile)
        String p4Client = projectFile.p4_code_client_env
        String p4User = projectFile.p4_user_single_slash

        elipyCmd = "${this.elipyCall} gametool/frostbite_database_upgrader --code-changelist %CODE_CHANGELIST%" +
            " --clean %CLEAN_LOCAL%${extraArgs} --config ${config} --p4-port ${p4Port} --p4-client ${p4Client} --p4-user ${p4User}" +
            ' --submit %SUBMIT%'
    }

    void initializeFrostyisotool(def branchFile, def masterFile, def projectFile, String branchName, String gametool) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)

        jobLabel = branchInfo.gametool_settings.gametools."$gametool".job_label ?: branchInfo.job_label_statebuild ?: 'statebuild'

        int timeoutHours = branchInfo.gametool_settings.gametools."$gametool".timeout_hours ?: 12
        timeoutMinutes = timeoutHours * 60
        description = 'Compiles and submits FrostyIsoTool.'
        String frostbiteLicensee = branchInfo.frostbite_licensee
        extraArgs = branchInfo.gametool_settings.gametools."$gametool".extra_args ?: ''

        String p4Port = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_code_server', false, projectFile)
        fbLoginDetails = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_fb_settings', [:], projectFile) as Map
        buildName = '${JOB_NAME}.${ENV, var="CODE_CHANGELIST"}'
        if (frostbiteLicensee) {
            extraArgs += " --licensee ${frostbiteLicensee}"
        }
        userCredentials = LibCommonNonCps.get_setting_value(branchInfo, [], 'user_credentials', null, projectFile)
        if (userCredentials) {
            extraArgs += ' --email %monkey_email% --password "%monkey_passwd%"'
        }
        if (branchInfo.use_deprecated_blox_packages) {
            extraArgs += ' --framework-args -D:frostbite.useDeprecatedBloxPackages=true'
        }
        elipyCmd = "${this.elipyCall} gametool/frostyisotool --code-changelist %CODE_CHANGELIST% --clean %CLEAN_LOCAL%" +
            " --submit %SUBMIT% --p4-client ${projectFile.p4_code_client_env} --p4-port ${p4Port} ${extraArgs}"
    }

    void initializeDrone(def branchFile, def masterFile, def projectFile, String branchName, String gametool) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        List<String> modifiers = ['drone']
        userCredentials = LibCommonNonCps.get_setting_value(branchInfo, modifiers, 'user_credentials', null, projectFile)
        String frostbiteLicensee = branchInfo.frostbite_licensee
        Boolean dry_run = branchInfo.gametool_settings.gametools."$gametool".dry_run ?: false
        String p4Port = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_code_server', '', projectFile)
        jobLabel = branchInfo.gametool_settings.gametools."$gametool".job_label ?: branchInfo.job_label_statebuild ?: 'statebuild'
        int timeoutHours = branchInfo.gametool_settings.gametools."$gametool".timeout_hours ?: 12
        timeoutMinutes = timeoutHours * 60
        fbLoginDetails = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_fb_settings', [:], projectFile) as Map
        extraArgs = branchInfo.gametool_settings.gametools."$gametool".extra_args ?: ''

        if (branchInfo.use_deprecated_blox_packages) {
            extraArgs += " --framework-args -D:frostbite.useDeprecatedBloxPackages=${branchInfo.use_deprecated_blox_packages}"
        }
        if (frostbiteLicensee) {
            extraArgs += " --licensee ${frostbiteLicensee}"
        }
        if (userCredentials) {
            extraArgs += ' --email %monkey_email% --password "%monkey_passwd%"'
        }
        if (dry_run) {
            extraArgs += ' --dry-run'
        }
        buildName = '${JOB_NAME}.${ENV, var="CODE_CHANGELIST"}'
        description = 'Compiles and submits Drone.'
        String p4Client = projectFile.p4_code_client_env

        elipyCmd = "${this.elipyCall} gametool/drone --code-changelist %CODE_CHANGELIST% --clean %CLEAN_LOCAL% --p4-port ${p4Port}" +
            " --p4-client ${p4Client} --submit %SUBMIT% ${extraArgs}"
    }

    void initializeFramework(def branchFile, def masterFile, def projectFile, String branchName, String gametool) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)

        jobLabel = branchInfo.gametool_settings.gametools."$gametool".job_label ?: branchInfo.job_label_statebuild ?: 'statebuild'

        int timeoutHours = branchInfo.gametool_settings.gametools."$gametool".timeout_hours ?: 12
        timeoutMinutes = timeoutHours * 60
        fbLoginDetails = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_fb_settings', [:], projectFile) as Map
        buildName = '${JOB_NAME}.${ENV, var="CODE_CHANGELIST"}'
        description = 'Compiles and submits Framework'
        String p4Port = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_code_server', '', projectFile)
        String p4Client = projectFile.p4_code_client_env
        String p4User = projectFile.p4_user_single_slash

        elipyCmd = "${this.elipyCall} gametool/framework --code-changelist %CODE_CHANGELIST% --clean %CLEAN% --p4-port ${p4Port}" +
            " --p4-client ${p4Client} --p4-user ${p4User} --submit %SUBMIT%"
    }

    void initializeFbenv(def branchFile, def masterFile, def projectFile, String branchName, String gametool) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)

        jobLabel = branchInfo.gametool_settings.gametools."$gametool".job_label ?: branchInfo.job_label_statebuild ?: 'statebuild'

        int timeoutHours = branchInfo.gametool_settings.gametools."$gametool".timeout_hours ?: 12
        timeoutMinutes = timeoutHours * 60
        fbLoginDetails = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_fb_settings', [:], projectFile) as Map
        buildName = '${JOB_NAME}.${ENV, var="CODE_CHANGELIST"}'
        description = 'Compiles and submits fbenv'
        String p4Port = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_code_server', '', projectFile)
        String p4Client = projectFile.p4_code_client_env
        String p4User = projectFile.p4_user_single_slash

        elipyCmd = "${this.elipyCall} gametool/fbenv --code-changelist %CODE_CHANGELIST% --p4-port ${p4Port}" +
            " --p4-client ${p4Client} --p4-user ${p4User} --submit %SUBMIT%"
    }
}
