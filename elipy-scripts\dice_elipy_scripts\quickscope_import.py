"""
quickscope_import.py
"""
import click
import os

from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import LOGGER, core, frostbite_core
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command("quickscope_import", short_help="import latest buid stats to quickscope")
@click.option("--quickscope-db", default=None, help="db name used to store stats.")
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(_, quickscope_db):
    """
    import latest buid stats to quickscope
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    LOGGER.info("Starting post data clean step: import latest buid stats to quickscope ")
    quickscope_import(quickscope_db)
    LOGGER.info("import latest stats to quickscope done")


def quickscope_import(quickscope_db="kinpipeline"):
    """
    import latest buid stats to quickscope
    """
    quickscope_path = os.path.join(frostbite_core.get_tnt_root(), "bin", "Quickscope", "qcli.exe")
    cmd = [
        quickscope_path,
        "-import",
        "latest",
        "-appid",
        "all",
        "-upload",
        "-database",
        quickscope_db,
    ]
    core.run(cmd, print_std_out=True)
