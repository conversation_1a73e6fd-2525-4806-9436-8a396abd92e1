{#
    Command:
        imerge
            help: Runs `p4 merge` on the specified set of CLs grouping them by integration mode.

    Arguments:

    Required variables:

    Optional variables:
        port
            metavar: P4PORT
            help: protocol:host:port of the Perforce service with which to communicate.
        client
            metavar: P4CLIENT
            help: Perforce client name.
        user
            metavar: P4USER
            help: Perforce user name.
        no_submit
            is_flag: True
            default: False
            show_default: True
            help: Skip submission of the merged files to the depot.
        exclude_path
            metavar: DEPO_PATH
            multiple: True
            help: Revert the path before running `p4 resolve`. Can be provided multiple times.
        safe_resolve
            is_flag: True
            default: False
            show_default: True
            help: User `p4 resolve -as` for changelists with 'Integration: merge'.
        description
            metavar: DESCRIPTION
            default: ''
            help: Submit the changelist with the DESCRIPTION.
        from
            metavar: STREAM
            help: Specifies a stream other than the parent stream to merge from.
        start_changelist
            metavar: NUMBER
            help: Merge changes starting from the specified change number.
        stop_changelist
            metavar: NUMBER
            help: Merge changes up to and including the specified change number.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    imerge
    {%- if port %}
    --port {{ port }}
    {%- endif %}
    {%- if client %}
    --client {{ client }}
    {%- endif %}
    {%- if user %}
    --user {{ user }}
    {%- endif %}
    {%- if no_submit %}
    --no-submit {{ no_submit }}
    {%- endif %}
    {%- if exclude_path %}
    --exclude-path {{ exclude_path }}
    {%- endif %}
    {%- if safe_resolve %}
    --safe-resolve {{ safe_resolve }}
    {%- endif %}
    {%- if description %}
    --description {{ description }}
    {%- endif %}
    {%- if from %}
    --from {{ from }}
    {%- endif %}
    {%- if start_changelist %}
    --start-changelist {{ start_changelist }}
    {%- endif %}
    {%- if stop_changelist %}
    --stop-changelist {{ stop_changelist }}
    {%- endif %}
  displayName: elipy imerge
