"""
post_preflight.py
"""
import os
import click
from dice_elipy_scripts.utils.code_utils import run_gensln
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import LOGGER, core, data, avalanche, p4, code, running_processes, frostbite_core
from elipy2 import local_paths
from elipy2.cli import pass_context
from elipy2.exceptions import ELIPYException
from elipy2.telemetry import collect_metrics


@click.command("post_preflight", short_help="Performs a post preflight maintenance run.")
@click.option("--platform", help="Platform to run post preflight on.", required=True)
@click.option("--asset", help="Asset to be cooked.", default=["preflightlevels"], multiple=True)
@click.option(
    "--preflight_type",
    default="content",
    help="Code or content post preflight to perform.",
)
@click.option("--config", help="Configuration to build with (code only).", default="final")
@click.option("--datadir", help="Which datadir to build from.", required=True)
@click.option("--p4_client", help="Which p4 client to use.", required=True)
@click.option("--p4_user", help="Which p4 user is being used.", required=True)
@click.option("--p4_port", help="Which p4 port to be used.", required=True)
@click.option("--p4_client_code", help="Which p4 client to use.", required=True)
@click.option("--p4_port_code", help="Which p4 port to be used.", required=True)
@click.option(
    "--password",
    default=None,
    help="User credentials to authenticate to package server",
)
@click.option("--email", default=None, help="User email to authenticate to package server")
@click.option(
    "--domain-user",
    default=None,
    help="The user to authenticate to package server as DOMAIN\\user",
)
@click.option("--limit_cpu", is_flag=True, help="Only enable when code preflight tool on AWS.")
@click.option("--framework-args", multiple=True, help="Framework arguments for gen sln.")
@click.option(
    "--clean-master-version-check",
    is_flag=True,
    help="Run clean on master version update.",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    platform,
    asset,
    preflight_type,
    config,
    datadir,
    p4_client,
    p4_user,
    p4_port,
    p4_client_code,
    p4_port_code,
    password,
    email,
    domain_user,
    limit_cpu,
    framework_args,
    clean_master_version_check,
):
    """
    Performs a post preflight maintenance operation on code or content preflights.
    """
    # adding sentry tags
    add_sentry_tags(__file__, "preflight")

    # kill any unexpected running processes
    running_processes.kill()

    if preflight_type.lower() == "content":
        # Post preflight for content preflight
        real_platform = avalanche.get_avalanche_platform_name(platform)
        content_post_preflight(
            real_platform,
            asset,
            datadir,
            p4_client,
            p4_user,
            p4_port,
            clean_master_version_check,
        )
        LOGGER.info("Cook completed, putting node back into preflight pool.")

    elif preflight_type.lower() == "code":
        # Post preflight for code preflight.
        code_post_preflight(
            platform,
            config,
            p4_client_code,
            p4_user,
            p4_port_code,
            password,
            email,
            domain_user,
            limit_cpu,
            framework_args,
        )
        LOGGER.info("All tests passed, node has recovered.")
    else:
        raise ELIPYException(
            "Invalid options selected, Platform: {0}, Asset: {1}, Preflight Type: {2}".format(
                platform, asset, preflight_type
            )
        )


def content_post_preflight(
    platform, asset, datadir, p4client, p4user, p4port, clean_master_version_check
):
    """
    Run content post_preflight
    """
    perforce = p4.P4Utils(p4port, p4user, p4client)

    # # Perform a quick garbage collection for Avalanche
    # LOGGER.info("---------------Running Avalanche maintenance--------------------")
    # LOGGER.info("Checking if Avalanche is running.")
    # avalanche_status = windows_tools.query_service('Avalanche')
    # if avalanche_status != 'Running':
    #     LOGGER.error("Avalanche is: {0}".format(avalanche_status))
    # else:
    #     LOGGER.info("Avalanche is: {0}".format(avalanche_status))
    # LOGGER.info("---------------Before Avalanche maintenance--------------------")
    # avalanche.avalanche_status()
    # avalanche.avalanche_general()
    # LOGGER.info("---------------After Avalanche maintenance--------------------")
    # avalanche.avalanche_status()
    # LOGGER.info("---------------Finished Avalanche maintenance--------------------")

    # Reverting any files
    perforce.revert()
    # Running quick cook to test
    builder_first = data.DataUtils(platform, list(asset))
    builder_first.set_datadir(datadir)

    try:
        builder_first.cook(
            collect_mdmps=True,
            trim=False,
            clean_master_version_check=clean_master_version_check,
        )

    except Exception:
        # Need to reset builder otherwise asset is wrong going from ["","asset"] to ["","","asset"].
        builder_recover = data.DataUtils(platform, list(asset))
        builder_recover.set_datadir(datadir)
        LOGGER.warning("cook invocation returned error when cooking {0}".format(platform))
        LOGGER.info("------------- Cleaning Avalanche Store ---------------")
        builder_recover.clean_local_datastate()
        avalanche.clean_avalanche(avalanche.get_avalanche_db_valid(platform))
        perforce.clean(folder=os.path.join(frostbite_core.get_game_root(), datadir, "..."))
        # Running quick cook to test
        builder_recover.cook(
            collect_mdmps=True,
            trim=False,
            clean_master_version_check=clean_master_version_check,
        )


def code_post_preflight(
    platform,
    config,
    p4_client,
    p4_user,
    p4_port,
    password,
    email,
    domain_user,
    limit_cpu,
    framework_args,
):
    """
    Run Code post_preflight
    """
    perforce = p4.P4Utils(p4_port, p4_user, p4_client)
    # Reverting any files
    perforce.revert()

    # Cleaning TNT Local
    builder = code.CodeUtils(platform, config)

    try:
        framework_args = list(framework_args)
        run_gensln(
            password=password,
            user=email,
            domain_user=domain_user,
            framework_args=framework_args,
            builder=builder,
        )
        # special handling for tool on code-preflight
        msbuild_args = []
        if limit_cpu and platform.lower() == "tool":
            msbuild_args = [
                "/m:20",
                "/p:MultiProcessorCompilation=true;GenerateFullPaths=true;Configuration=release;CL_MPCount=20;Platform=x64",  # pylint: disable=line-too-long
            ]
        builder.buildsln(msbuild_args)  # Buildsln solution

    except Exception:
        # Cleaning TNT Local
        builder.clean_local(close_handles=True)
        core.delete_folder(local_paths.get_tnt_localpackages_path())
        perforce.clean(
            folder=os.path.join(frostbite_core.get_tnt_root(), "...")
        )  # P4 clean tnt folder
        run_gensln(
            password=password,
            user=email,
            domain_user=domain_user,
            framework_args=framework_args,
            builder=builder,
        )
        builder.buildsln()  # Buildsln
