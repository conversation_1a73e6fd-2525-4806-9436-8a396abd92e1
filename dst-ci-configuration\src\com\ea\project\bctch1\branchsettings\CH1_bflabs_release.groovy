package com.ea.project.bctch1.branchsettings

import com.ea.project.bctch1.BctCh1

class CH1_bflabs_release {
    // Settings for jobs
    static Class project = BctCh1
    static Map general_settings = [
        dataset                     : project.dataset,
        elipy_call                  : project.elipy_setup_call + ' && elipy --location bflabs-criterion --use-fbenv-core',
        elipy_install_call          : project.elipy_install_call,
        frostbite_licensee          : project.frostbite_licensee,
        workspace_root              : project.workspace_root,
        job_label_statebuild        : 'statebuild_criterion', // Default value, but needed to run Shift jobs on statebuild machines.
        separate_symbol_store_upload: true,
        p4_code_creds               : 'perforce-battlefield-criterion',
        p4_data_creds               : 'perforce-battlefield-criterion',
        p4_code_server              : 'oh-p4edge-fb.eu.ad.ea.com:2001',
        p4_data_server              : 'oh-p4edge-fb.eu.ad.ea.com:2001',
    ]
    static Map code_settings = [
        extra_code_args              : [' --framework-args -G:BattlefieldGame-onlineSku=labs'],
        fake_ooa_wrapped_symbol      : false,
        skip_code_build_if_no_changes: false,
        slack_channel_code           : [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        statebuild_code              : false,
        sndbs_enabled                : true,
    ]
    static Map data_settings = [
        enable_daily_data_clean: true,
        enable_lkg_cleaning    : true,
        slack_channel_patchdata: [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        statebuild_data        : false,
        statebuild_patchdata   : false,
        statebuild_webexport   : false,
        timeout_hours_data     : 10,
        timeout_hours_patchdata: 10,
        webexport_allow_failure: false,
        webexport_branch       : true,
    ]
    static Map frosty_settings = [
        enable_eac_win64_digital        : true,
        enable_eac_win64_steam          : true,
        enable_eac_win64_combine        : true,
        enable_eac_win64_steam_combine  : true,
        slack_channel_patchfrosty       : [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        statebuild_frosty               : false,
        statebuild_patchfrosty          : false,
        timeout_hours_frosty            : 10,
        timeout_hours_frosty_patchfrosty: 10,
        use_linuxclient                 : true,
        combine_settings_file           : 'project-combine-labs.yaml',
        combine_settings_file_xbsx      : 'project-combine-labs-smart-delivery.yaml',
        enable_steam_drm_wrapping       : true,
    ]
    static Map standard_jobs_settings = code_settings + data_settings + frosty_settings + [
        asset                       : 'Game/Setup/Build/LabsClientLevels',
        baseline_set                : true,
        combine_bundles             : [
            combine_asset        : 'Game/Setup/Build/LabsClientLevels',
            combine_reference_job: 'CH1-SP-release.patchdata.start',
            is_target_branch     : true,
            source_branch_code   : 'CH1-SP-release',
            source_branch_data   : 'CH1-SP-release',
        ],
        enable_lkg_p4_counters      : true,
        extra_data_args             : ['--pipeline-args -Pipeline.MaxConcurrency --pipeline-args 12 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        extra_frosty_args           : ['--pipeline-args -Pipeline.MaxConcurrency --pipeline-args 12 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        move_location_parallel      : true,
        new_locations               : [
            DiceStockholm: [
                elipy_call_new_location: project.elipy_call + ' --use-fbenv-core',
            ],
        ],
        first_patch                 : false,
        is_virtual_stream           : true,
        quickscope_db               : 'kinpipeline',
        quickscope_import           : true,
        server_asset                : 'Game/Setup/Build/LabsServerLevels',
        shift_branch                : true,
        shift_every_build           : true,
        shift_reference_job         : 'CH1-bflabs-release.patchfrosty.start',
        skip_icepick_settings_file  : true,
        standalone_disc_baseline    : true,
        standalone_patch_baseline   : false,
        strip_symbols               : false,
        // trigger_string_shift        : 'TZ=Europe/Stockholm \n H 1,14 * * 1-6\nH 6,13 * * 7',
        // trigger_type_shift          : 'cron',
        use_deprecated_blox_packages: true,
        use_super_bundles           : true,
        oreans_protection           : true,
    ]
    static Map icepick_settings = [
        ignore_icepick_exit_code: false
    ]
    static Map preflight_settings = [:]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'win64game', configs: ['final', 'release', 'retail', 'performance']],
        [name: 'tool', configs: ['release']],
        [name: 'win64server', configs: ['final']],
        [name: 'xbsx', configs: ['final', 'release', 'retail', 'performance']],
        [name: 'ps5', configs: ['final', 'release', 'retail', 'performance']],
        [name: 'linux64server', configs: ['final']],
        [name: 'linux64', configs: ['final']],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = [
        [name: '.code.p4counterupdater', args: ['code_changelist', 'code_countername']],
        [name: '.bfdata.webexport.win64', args: ['code_changelist', 'mirror_changelist']],
        [name: '.patchdata.start', args: []],
    ]
    static List data_matrix = []
    static List data_downstream_matrix = []
    static List patchdata_matrix = [
        [name: 'win64'],
        [name: 'xbsx'],
        [name: 'ps5'],
    ]
    static List patchdata_downstream_matrix = [
        [name: '.patchfrosty.start', args: []],
    ]
    static List frosty_matrix = []
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = [
        [name: 'win64', variants: [[format: 'files', config: 'final', region: 'labs', args: ' --additional-configs performance --additional-configs release'],
                                   [format: 'steam_combine', config: 'retail', region: 'labs', args: ''],
                                   [format: 'steam_combine', config: 'final', region: 'labs', args: '']]],
        [name: 'ps5', variants: [[format: 'files', config: 'final', region: 'labs', args: ' --additional-configs performance --additional-configs release']]],
        [name: 'xbsx', variants: [[format: 'files', config: 'final', region: 'labs', args: ' --additional-configs performance --additional-configs release']]],
        [name: 'server', variants: [[format: 'files', config: 'final', region: 'labs', args: '']]],
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'labs', args: '']]],
        [name: 'linux64', variants: [[format: 'files', config: 'final', region: 'labs', args: '']]],
    ]
    static List patchfrosty_matrix = [
        [name: 'win64', variants: [[format: 'combine', config: 'final', region: 'labs', args: ''],
                                   [format: 'combine', config: 'retail', region: 'labs', args: ''],
                                   [format: 'steam_combine', config: 'retail', region: 'labs', args: ''],
                                   [format: 'steam_combine', config: 'final', region: 'labs', args: '']]],
        [name: 'ps5', variants: [[format: 'combine', config: 'final', region: 'labs', args: ''],
                                 [format: 'combine', config: 'retail', region: 'labs', args: '', allow_failure: true]]],
        [name: 'xbsx', variants: [[format: 'combine', config: 'final', region: 'labs', args: ''],
                                  [format: 'combine', config: 'retail', region: 'labs', args: '']]],
    ]
    static List patchfrosty_downstream_matrix = [
        [name: '.spin.linuxserver.digital.final.labs', args: ['code_changelist', 'data_changelist']],
        [name: '.win64.upload_to_steam.patch.combine.labs.final', args: ['code_changelist', 'data_changelist', 'combine_code_changelist', 'combine_data_changelist']],
        [name: '.win64.upload_to_steam.patch.combine.labs.retail', args: ['code_changelist', 'data_changelist', 'combine_code_changelist', 'combine_data_changelist']],
    ]
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'labs']]],
        [name: 'linux64', variants: [[format: 'files', config: 'final', region: 'labs']]],
    ]
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = [
        [name: '.spin.linux64.files.final.labs', args: ['code_changelist', 'data_changelist']],
    ]
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
