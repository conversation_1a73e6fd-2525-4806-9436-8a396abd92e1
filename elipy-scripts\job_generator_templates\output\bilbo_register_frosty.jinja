{#
    Command:
        bilbo_register_frosty
            short_help: Registers a Frosty-generated build in Bilbo.

    Arguments:

    Required variables:
        code_branch
            help: Perforce code branch/stream name.
            required: True
        code_changelist
            required: True
            help: Changelist number of code build used to verify data.
        data_branch
            help: Perforce data branch/stream name.
            required: True
        data_changelist
            help: Changelist number of data built.
            required: True
        dataset
            required: True
            help: Which dataset has been validated using this code build.
        platform
            help: Which platform the build is for.
            required: True
        package_type
            required: True
            help: Which package type the build is of (files, digital, patch).
        region
            help: Which region/SKU the build is (ww, na, eu).
            required: True
        config
            required: True
            help: Which configuration the build is in (final, release, retail).

    Optional variables:
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    bilbo_register_frosty
    --code-branch {{ code_branch }}
    --code-changelist {{ code_changelist }}
    --data-branch {{ data_branch }}
    --data-changelist {{ data_changelist }}
    --dataset {{ dataset }}
    --platform {{ platform }}
    --package-type {{ package_type }}
    --region {{ region }}
    --config {{ config }}
  displayName: elipy bilbo_register_frosty
