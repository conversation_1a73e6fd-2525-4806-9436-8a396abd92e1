plugins {
    // Shared library documentation: https://www.jenkins.io/doc/book/pipeline/shared-libraries/
    id 'com.mkobit.jenkins.pipelines.shared-library' version '0.10.1' // Jenkins shared library plugin
    id 'groovy' // Groovy plugin for Groovy language support
    id 'codenarc' // CodeNarc plugin for static analysis of Groovy code
    id 'com.jfrog.artifactory' version '5.2.5' // Artifactory plugin for managing dependencies
    id 'java' // Java plugin for Java language support
}

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21

    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

allprojects {
    repositories {
        maven {
            url "https://artifacts.ea.com/artifactory/jcenter-maven-remote/"
            credentials {
                username = (System.getenv("ARTIFACTORY_USER") != null ? System.getenv("ARTIFACTORY_USER") : "${ARTIFACTORY_USER}")
                password = (System.getenv("ARTIFACTORY_APIKEY") != null ? System.getenv("ARTIFACTORY_APIKEY") : "${ARTIFACTORY_APIKEY}")
            }
        }
        maven {
            url "https://artifacts.ea.com/artifactory/jenkins-maven-remote/"
            credentials {
                username = (System.getenv("ARTIFACTORY_USER") != null ? System.getenv("ARTIFACTORY_USER") : "${ARTIFACTORY_USER}")
                password = (System.getenv("ARTIFACTORY_APIKEY") != null ? System.getenv("ARTIFACTORY_APIKEY") : "${ARTIFACTORY_APIKEY}")
            }
        }
        maven {
            url "https://artifacts.ea.com/artifactory/jenkins-updates-generic-remote/"
            credentials {
                username = (System.getenv("ARTIFACTORY_USER") != null ? System.getenv("ARTIFACTORY_USER") : "${ARTIFACTORY_USER}")
                password = (System.getenv("ARTIFACTORY_APIKEY") != null ? System.getenv("ARTIFACTORY_APIKEY") : "${ARTIFACTORY_APIKEY}")
            }
        }
        maven {
            url "https://artifacts.ea.com/artifactory/apache-maven-remote/"
            credentials {
                username = (System.getenv("ARTIFACTORY_USER") != null ? System.getenv("ARTIFACTORY_USER") : "${ARTIFACTORY_USER}")
                password = (System.getenv("ARTIFACTORY_APIKEY") != null ? System.getenv("ARTIFACTORY_APIKEY") : "${ARTIFACTORY_APIKEY}")
            }
        }
        flatDir { dirs 'gradle/repository' }
    }
}

ext {
    buildFailureAnalyzerVersion = '2.5.4'
    buildMonitorPluginVersion = '1.13+build.202205140447'
    ec2FleetVersion = '3.2.0'
    gitVersion = '5.2.1'
    harnessVersion = '2307.v10e5d0701b_e5'
    jenkinsVersion = '2.479.3'
    jobDslVersion = '1.87'
    log4jVersion = '1.2.17'
    multipleScmsVersion = '0.8'
    pipelineBuildStepVersion = '567.vea_ce550ece97'
    pipelineGroovyLibVersion = '689.veec561a_dee13'
    structsVersion = '343.vdcf37b_a_c81d5'
    workflowApiVersion = '1363.v03f731255494'
    workflowCpsVersion = '4018.vf02e01888da_f'

    jenkinsTestTimeout = 600
    jobsFolders = ['src/seeds']
}

sourceSets {
    jobs {
        groovy {
            srcDirs = jobsFolders
            compileClasspath += main.compileClasspath
        }
        compileClasspath += sourceSets.main.output
        runtimeClasspath += sourceSets.main.output
    }

    main {
        groovy {
            srcDirs = ['src', 'vars'] + jobsFolders
        }
        resources {
            srcDirs = ['resources']
        }
    }

    test {
        groovy {
            srcDirs = ['test/groovy']
        }
    }
}

configurations {
    testPlugins {}

    // see JENKINS-45512: https://issues.jenkins.io/browse/JENKINS-45512
    testCompile {
        exclude group: 'xalan'
        exclude group: 'xerces'
    }
}

// Exclude buggy Xalan dependency this way the JRE default TransformerFactory is used
// The xalan pulled in by htmlunit does not properly deal with spaces folder / job names
configurations.all*.exclude group: 'xalan'

dependencies {
    testCompile 'org.spockframework:spock-core:1.3-groovy-2.4'
    testImplementation 'org.spockframework:spock-core:1.3-groovy-2.4'

    // Jenkins test harness dependencies
    implementation 'jakarta.servlet:jakarta.servlet-api:5.0.0'
    testImplementation "jakarta.persistence:jakarta.persistence-api:3.1.0"
    testImplementation "org.jenkins-ci.main:jenkins-war:${jenkinsVersion}"
    testImplementation "org.jenkins-ci.main:cli:${jenkinsVersion}"
    testImplementation("org.jenkins-ci.main:jenkins-test-harness:${harnessVersion}")

    // Use the latest Groovy version for building this library
    testImplementation 'org.codehaus.groovy:groovy-all:2.4.21'
    testImplementation "com.cloudbees:groovy-cps:${workflowCpsVersion}"

    // Job DSL plugin including plugin dependencies
    compileOnly "org.jenkins-ci.plugins:job-dsl-core:${jobDslVersion}"
    implementation "org.jenkins-ci.plugins:structs:${structsVersion}@jar"
    testImplementation "org.jenkins-ci.plugins:job-dsl:${jobDslVersion}"
    testImplementation "org.jenkins-ci.plugins:job-dsl:${jobDslVersion}@jar"
    compileOnly "org.jenkins-ci.plugins:git:${gitVersion}@jar"

    // Test suite
    testImplementation 'org.mockito:mockito-core:5.5.0'

    // Jenkins plugins
    testPlugins "com.amazon.jenkins.fleet:ec2-fleet:${ec2FleetVersion}"
    testPlugins 'com.axis.system.jenkins.plugins.downstream:downstream-build-cache:1.7'
    testPlugins 'com.axis.system.jenkins.plugins.downstream:yet-another-build-visualizer:1.17'
    testPlugins 'com.coravy.hudson.plugins.github:github:********'
    testPlugins 'com.datapipe.jenkins.plugins:hashicorp-vault-plugin:371.v884a_4dd60fb_6'
    testPlugins 'com.sonyericsson.hudson.plugins.rebuild:rebuild:338.va_0a_b_50e29397'
    testPlugins "com.sonyericsson.jenkins.plugins.bfa:build-failure-analyzer:${buildFailureAnalyzerVersion}"
    testPlugins 'io.jenkins:configuration-as-code:1953.v148f87d74b_1e'
    testPlugins 'io.jenkins.plugins:azure-sdk:174.va_89c1df897d2'
    testPlugins 'io.jenkins.plugins:bootstrap4-api:4.6.0-6'
    testPlugins 'io.jenkins.plugins:bootstrap5-api:5.3.3-2'
    testPlugins 'io.jenkins.plugins:caffeine-api:3.2.0-166.v72a_6d74b_870f'
    testPlugins 'io.jenkins.plugins:checks-api:2.2.3'
    testPlugins 'io.jenkins.plugins:commons-lang3-api:3.17.0-84.vb_b_938040b_078'
    testPlugins 'io.jenkins.plugins:commons-text-api:1.13.0-153.v91dcd89e2a_22'
    testPlugins 'io.jenkins.plugins:data-tables-api:1.13.6-4'
    testPlugins 'io.jenkins.plugins:echarts-api:5.4.0-7'
    testPlugins 'io.jenkins.plugins:font-awesome-api:6.7.2-1'
    testPlugins 'io.jenkins.plugins:gson-api:2.12.1-113.v347686d6729f'
    testPlugins 'io.jenkins.plugins:ionicons-api:82.v0597178874e1'
    testPlugins 'io.jenkins.plugins:jakarta-activation-api:2.1.3-2'
    testPlugins 'io.jenkins.plugins:jakarta-mail-api:2.1.3-2'
    testPlugins 'io.jenkins.plugins:javax-activation-api:1.2.0-8'
    testPlugins 'io.jenkins.plugins:javax-mail-api:1.6.2-11'
    testPlugins 'io.jenkins.plugins:jaxb:2.3.9-133.vb_ec76a_73f706'
    testPlugins 'io.jenkins.plugins:jersey2-api:2.45-154.v4ded3dc34f81'
    testPlugins 'io.jenkins.plugins:jjwt-api:0.11.5-120.v0268cf544b_89'
    testPlugins 'io.jenkins.plugins:gson-api:2.12.1-113.v347686d6729f'
    testPlugins 'io.jenkins.plugins:jquery3-api:3.7.1-2'
    testPlugins 'io.jenkins.plugins:okhttp-api:4.11.0-189.v976fa_d3379d6'
    testPlugins 'io.jenkins.plugins:plugin-util-api:6.0.0'
    testPlugins 'io.jenkins.plugins:popper-api:1.16.1-3'
    testPlugins 'io.jenkins.plugins:popper2-api:2.11.6-5'
    testPlugins 'io.jenkins.plugins:snakeyaml-api:2.3-123.v13484c65210a_'
    testPlugins 'io.jenkins.plugins.mina-sshd-api:mina-sshd-api-common:2.12.0-90.v9f7fb_9fa_3d3b_'
    testPlugins 'io.jenkins.plugins.mina-sshd-api:mina-sshd-api-core:2.12.0-90.v9f7fb_9fa_3d3b_'
    testPlugins "io.jenkins.plugins:pipeline-groovy-lib:${pipelineGroovyLibVersion}"
    testPlugins 'jp.ikedam.jenkins.plugins:groovy-label-assignment:1.2.0'
    testPlugins 'org.6wind.jenkins:lockable-resources:1185.v0c528656ce04'
    testPlugins 'org.jenkins-ci.main:maven-plugin:3.25'
    testPlugins 'org.jenkins-ci.modules:instance-identity:201.vd2a_b_5a_468a_a_6'
    testPlugins 'org.jenkins-ci.modules:sshd:3.322.v159e91f6a_550'
    testPlugins 'org.jenkins-ci.plugins:active-directory:2.34'
    testPlugins 'org.jenkins-ci.plugins:ansicolor:1.0.6'
    testPlugins 'org.jenkins-ci.plugins:ant:513.vde9e7b_a_0da_0f'
    testPlugins 'org.jenkins-ci.plugins:antisamy-markup-formatter:173.v680e3a_b_69ff3'
    testPlugins 'org.jenkins-ci.plugins:any-buildstep:14.ve115ec1484f0'
    testPlugins 'org.jenkins-ci.plugins:apache-httpcomponents-client-4-api:4.5.14-269.vfa_2321039a_83'
    testPlugins 'org.jenkins-ci.plugins:authentication-tokens:1.53.v1c90fd9191a_b_'
    testPlugins 'org.jenkins-ci.plugins:aws-credentials:191.vcb_f183ce58b_9'
    testPlugins 'org.jenkins-ci.plugins:aws-java-sdk:1.12.406-370.v8f993c987059'
    testPlugins 'org.jenkins-ci.plugins:azure-credentials:341.v4881e9f4ffea_'
    testPlugins 'org.jenkins-ci.plugins:azure-vm-agents:975.va_a_d9b_6c912b_a_'
    testPlugins 'org.jenkins-ci.plugins:bouncycastle-api:2.30.1.80-256.vf98926042a_9b_'
    testPlugins 'org.jenkins-ci.plugins:branch-api:2.1217.v43d8b_b_d8b_2c7'
    testPlugins "org.jenkins-ci.plugins:build-monitor-plugin:${buildMonitorPluginVersion}@hpi"
    testPlugins "org.jenkins-ci.plugins:build-monitor-plugin:${buildMonitorPluginVersion}@jar"
    testPlugins 'org.jenkins-ci.plugins:build-name-setter:2.4.0'
    testPlugins 'org.jenkins-ci.plugins:build-timeout:1.32'
    testPlugins 'org.jenkins-ci.plugins:claim:516.v36293563731d'
    testPlugins 'org.jenkins-ci.plugins:cloud-stats:336.v788e4055508b_'
    testPlugins 'org.jenkins-ci.plugins:cloudbees-disk-usage-simple:203.v3f46a_7462b_1a_'
    testPlugins 'org.jenkins-ci.plugins:cloudbees-folder:6.1012.v79a_86a_1ea_c1f'
    testPlugins 'org.jenkins-ci.plugins:command-launcher:123.v37cfdc92ef67'
    testPlugins 'org.jenkins-ci.plugins:conditional-buildstep:1.5.0'
    testPlugins 'org.jenkins-ci.plugins:cors-filter:1.1'
    testPlugins 'org.jenkins-ci.plugins:credentials-binding:642.v737c34dea_6c2'
    testPlugins 'org.jenkins-ci.plugins:credentials:1405.vb_cda_74a_f8974'
    testPlugins 'org.jenkins-ci.plugins:dashboard-view:2.528.v3470c02b_d7c9'
    testPlugins 'org.jenkins-ci.plugins:display-url-api:2.209.v582ed814ff2f'
    testPlugins 'org.jenkins-ci.plugins:durable-task:587.v84b_877235b_45'
    testPlugins 'org.jenkins-ci.plugins:email-ext:1876.v28d8d38315b_d'
    testPlugins 'org.jenkins-ci.plugins:envinject-api:1.235.va_14c74f8f487'
    testPlugins 'org.jenkins-ci.plugins:envinject:2.926.v69c9b_3896a_96'
    testPlugins 'org.jenkins-ci.plugins:external-monitor-job:223.vb_fddcf42c9b_3'
    testPlugins 'org.jenkins-ci.plugins:flexible-publish:0.16.1'
    testPlugins 'org.jenkins-ci.plugins:git-client:4.6.0'
    testPlugins 'org.jenkins-ci.plugins:git-server:114.v068a_c7cc2574'
    testPlugins "org.jenkins-ci.plugins:git:${gitVersion}"
    testPlugins 'org.jenkins-ci.plugins:github-api:1.321-486.vd7fa_ef919b_48'
    testPlugins 'org.jenkins-ci.plugins:github-branch-source:1730.vff97c8a_1f804'
    testPlugins 'org.jenkins-ci.plugins:gitlab-plugin:1.7.16'
    testPlugins 'org.jenkins-ci.plugins:groovy:457.v99900cb_85593'
    testPlugins 'org.jenkins-ci.plugins:jackson2-api:2.18.3-402.v74c4eb_f122b_2'
    testPlugins 'org.jenkins-ci.plugins:javadoc:310.v032f3f16b_0f8'
    testPlugins 'org.jenkins-ci.plugins:jdk-tool:83.v417146707a_3d'
    testPlugins 'org.jenkins-ci.plugins:job-dsl:1.92'
    testPlugins 'org.jenkins-ci.plugins:jobConfigHistory:1305.vf20a_356586b_8'
    testPlugins 'org.jenkins-ci.plugins:jquery:1.12.4-3'
    testPlugins 'org.jenkins-ci.plugins:jsch:0.2.16-86.v42e010d9484b_'
    testPlugins 'org.jenkins-ci.plugins:junit:1312.v1a_235a_b_94a_31'
    testPlugins 'org.jenkins-ci.plugins:ldap:780.vcb_33c9a_e4332'
    testPlugins 'org.jenkins-ci.plugins:mailer:489.vd4b_25144138f'
    testPlugins 'org.jenkins-ci.plugins:mask-passwords:173.v6a_077a_291eb_5'
    testPlugins 'org.jenkins-ci.plugins:matrix-auth:3.2.3'
    testPlugins 'org.jenkins-ci.plugins:matrix-project:845.vffd7fa_f27555'
    testPlugins 'org.jenkins-ci.plugins:metrics:4.2.21-464.vc9fa_a_0d6265d'
    testPlugins 'org.jenkins-ci.plugins:multiple-scms:0.8'
    testPlugins 'org.jenkins-ci.plugins:node-iterator-api:72.vc90e81737df1'
    testPlugins 'org.jenkins-ci.plugins:nodelabelparameter:1.11.0'
    testPlugins 'org.jenkins-ci.plugins:p4:1.17.1'
    testPlugins 'org.jenkins-ci.plugins:pam-auth:1.12'
    testPlugins 'org.jenkins-ci.plugins:Parameterized-Remote-Trigger:3.2.1'
    testPlugins 'org.jenkins-ci.plugins:parameterized-trigger:787.v665fcf2a_830b_'
    testPlugins 'org.jenkins-ci.plugins:permissive-script-security:0.7'
    testPlugins "org.jenkins-ci.plugins:pipeline-build-step:${pipelineBuildStepVersion}"
    testPlugins 'org.jenkins-ci.plugins:pipeline-graph-analysis:235.vb_a_a_36b_f248c2'
    testPlugins 'org.jenkins-ci.plugins:pipeline-input-step:508.v584c0e9a_2177'
    testPlugins 'org.jenkins-ci.plugins:pipeline-milestone-step:111.v449306f708b_7'
    testPlugins 'org.jenkins-ci.plugins:pipeline-stage-step:312.v8cd10304c27a_'
    testPlugins 'org.jenkins-ci.plugins:plain-credentials:183.va_de8f1dd5a_2b_'
    testPlugins 'org.jenkins-ci.plugins:plugin-usage-plugin:4.4'
    testPlugins 'org.jenkins-ci.plugins:postbuildscript:3.4.1-695.vf6b_0b_8053979'
    testPlugins 'org.jenkins-ci.plugins:powershell:2.3'
    testPlugins 'org.jenkins-ci.plugins:promoted-builds:892.vd6219fc0a_efb'
    testPlugins 'org.jenkins-ci.plugins:role-strategy:680.v3a_6a_1698b_864'
    testPlugins 'org.jenkins-ci.plugins:run-condition:1.7'
    testPlugins 'org.jenkins-ci.plugins:scm-api:704.v3ce5c542825a_'
    testPlugins 'org.jenkins-ci.plugins:script-security:1373.vb_b_4a_a_c26fa_00'
    testPlugins 'org.jenkins-ci.plugins:sectioned-view:1.30'
    testPlugins 'org.jenkins-ci.plugins:slack:761.v2a_8770f0d169'
    testPlugins 'org.jenkins-ci.plugins:ssh-agent:346.vda_a_c4f2c8e50'
    testPlugins 'org.jenkins-ci.plugins:ssh-credentials:308.ve4497b_ccd8f4'
    testPlugins 'org.jenkins-ci.plugins:ssh-slaves:2.948.vb_8050d697fec'
    testPlugins "org.jenkins-ci.plugins:structs:${structsVersion}"
    testPlugins 'org.jenkins-ci.plugins:throttle-concurrents:2.14'
    testPlugins 'org.jenkins-ci.plugins:timestamper:1.28'
    testPlugins 'org.jenkins-ci.plugins:token-macro:400.v35420b_922dcb_'
    testPlugins 'org.jenkins-ci.plugins:trilead-api:2.190.v1ed19f8137f8'
    testPlugins 'org.jenkins-ci.plugins:unique-id:2.113.va_25f74db_66a_b_'
    testPlugins 'org.jenkins-ci.plugins:variant:60.v7290fc0eb_b_cd'
    testPlugins 'org.jenkins-ci.plugins:windows-slaves:1.8.1'
    testPlugins 'org.jenkins-ci.plugins.aws-java-sdk:aws-java-sdk-cloudformation:1.12.586-413.v6a_6c3a_420126'
    testPlugins 'org.jenkins-ci.plugins.aws-java-sdk:aws-java-sdk-codebuild:1.12.586-413.v6a_6c3a_420126'
    testPlugins 'org.jenkins-ci.plugins.aws-java-sdk:aws-java-sdk-ec2:1.12.586-413.v6a_6c3a_420126'
    testPlugins 'org.jenkins-ci.plugins.aws-java-sdk:aws-java-sdk-ecr:1.12.586-413.v6a_6c3a_420126'
    testPlugins 'org.jenkins-ci.plugins.aws-java-sdk:aws-java-sdk-ecs:1.12.586-413.v6a_6c3a_420126'
    testPlugins 'org.jenkins-ci.plugins.aws-java-sdk:aws-java-sdk-efs:1.12.406-370.v8f993c987059'
    testPlugins 'org.jenkins-ci.plugins.aws-java-sdk:aws-java-sdk-elasticbeanstalk:1.12.586-413.v6a_6c3a_420126'
    testPlugins 'org.jenkins-ci.plugins.aws-java-sdk:aws-java-sdk-iam:1.12.586-413.v6a_6c3a_420126'
    testPlugins 'org.jenkins-ci.plugins.aws-java-sdk:aws-java-sdk-logs:1.12.586-413.v6a_6c3a_420126'
    testPlugins 'org.jenkins-ci.plugins.aws-java-sdk:aws-java-sdk-minimal:1.12.586-413.v6a_6c3a_420126'
    testPlugins 'org.jenkins-ci.plugins.aws-java-sdk:aws-java-sdk-sns:1.12.406-370.v8f993c987059'
    testPlugins 'org.jenkins-ci.plugins.aws-java-sdk:aws-java-sdk-sqs:1.12.406-370.v8f993c987059'
    testPlugins 'org.jenkins-ci.plugins.aws-java-sdk:aws-java-sdk-ssm:1.12.586-413.v6a_6c3a_420126'
    testPlugins 'org.jenkins-ci.plugins.pipeline-stage-view:pipeline-rest-api:2.34'
    testPlugins 'org.jenkins-ci.plugins.pipeline-stage-view:pipeline-stage-view:2.31'
    testPlugins 'org.jenkins-ci.plugins.workflow:workflow-aggregator:596.v8c21c963d92d'
    testPlugins "org.jenkins-ci.plugins.workflow:workflow-api:${workflowApiVersion}"
    testPlugins 'org.jenkins-ci.plugins.workflow:workflow-basic-steps:1042.ve7b_140c4a_e0c'
    testPlugins "org.jenkins-ci.plugins.workflow:workflow-cps:${workflowCpsVersion}"
    testPlugins 'org.jenkins-ci.plugins.workflow:workflow-durable-task-step:1289.v4d3e7b_01546b_'
    testPlugins 'org.jenkins-ci.plugins.workflow:workflow-job:1505.vea_4b_20a_4a_495'
    testPlugins 'org.jenkins-ci.plugins.workflow:workflow-multibranch:795.ve0cb_1f45ca_9a_'
    testPlugins 'org.jenkins-ci.plugins.workflow:workflow-scm-step:427.v4ca_6512e7df1'
    testPlugins 'org.jenkins-ci.plugins.workflow:workflow-step-api:686.v603d058a_e148'
    testPlugins 'org.jenkins-ci.plugins.workflow:workflow-support:946.v2a_79d8a_4b_e14'
    testPlugins 'org.jenkinsci.plugins:emailext-template:1.5'
    testPlugins 'org.jenkinsci.plugins:pipeline-model-api:2.2150.v4cfd8916915c'
    testPlugins 'org.jenkinsci.plugins:pipeline-model-definition:2.2150.v4cfd8916915c'
    testPlugins 'org.jenkinsci.plugins:pipeline-model-extensions:2.2150.v4cfd8916915c'
    testPlugins 'org.jenkinsci.plugins:pipeline-stage-tags-metadata:2.2150.v4cfd8916915c'
    testPlugins 'org.jvnet.hudson.plugins:extended-read-permission:3.2'
    testPlugins 'org.jvnet.hudson.plugins:monitoring:2.5.0'
    testPlugins 'org.jvnet.hudson.plugins:postbuild-task:1.9'

    // Run the following script in the Script Console of your Jenkins instance to get which versions it uses.
    // (adapted from https://git.io/fjpUs)
    /*
        println "jenkinsVersion = '${Jenkins.get().version}'"
        println "implementation 'org.codehaus.groovy:groovy-all:${GroovySystem.version}'"

        Jenkins.get().pluginManager.plugins
            .collect { "testPlugins '${it.manifest.mainAttributes.getValue("Group-Id")}:${it.shortName}:${it.version}'" }
            .sort()
            .each { println it }
     */
}

// Configures the shared library plugin with specific settings
sharedLibrary {
    coreVersion = jenkinsVersion
    testHarnessVersion = harnessVersion
    pipelineTestUnitVersion = '1.12'
    pluginDependencies {
        dependency('io.jenkins.plugins', 'pipeline-groovy-lib', pipelineGroovyLibVersion)
        dependency('org.jenkins-ci.plugins', 'multiple-scms', multipleScmsVersion)
        dependency('com.amazon.jenkins.fleet', 'ec2-fleet', ec2FleetVersion)
        dependency('com.sonyericsson.jenkins.plugins.bfa', 'build-failure-analyzer', buildFailureAnalyzerVersion)
        dependency('org.jenkins-ci.plugins', 'pipeline-build-step', pipelineBuildStepVersion)
        dependency('log4j', 'log4j', log4jVersion)
    }
}

// Configures the CodeNarc plugin with specific settings
codenarc {
    toolVersion = '2.2.0'

    reportFormat = project.hasProperty('report') ? report : 'console'
    configFile = rootProject.file('gradle/codenarc.txt')
}

// Resolves and copies test plugin dependencies to the test resources directory
task resolveTestPlugins(type: Copy) {
    from configurations.testPlugins
    into new File(sourceSets.test.output.resourcesDir, 'test-dependencies')
    include '*.hpi'
    include '*.jpi'

    Map<String, String> mapping = [:]

    doFirst {
        configurations.testPlugins.resolvedConfiguration.resolvedArtifacts.each {
            mapping.put(it.file.name, "${it.name}.${it.extension}")
        }
    }
    rename { mapping.get(it) }

    doLast {
        List<String> baseNames = source*.name.collect { mapping.get(it) }.collect { it[0..it.lastIndexOf('.') - 1] }
        new File(destinationDir, 'index').setText(baseNames.join('\n'), 'UTF-8')
    }
}

// Generates Groovy documentation for the specified packages
groovydoc {
    includes = ['**/com/ea/lib/model/**']
    docTitle = 'DRE Cobra Autotest Configuration Documentation'
    overviewText = resources.text.fromFile('resources/documentation/index.html')
    windowTitle = 'DRE Cobra Autotest Documentation'
    footer = 'DRE Cobra'
}

// Configures the test task with specific system properties and dependencies
test {
    systemProperty 'pipeline.stack.write', false
    dependsOn tasks.resolveTestPlugins
    systemProperty 'jenkins.test.timeout', jenkinsTestTimeout
}

task unit(type: Test, group: 'verification', description: 'Only runs the unit tests') {
    // Runs only the unit tests, excluding specific test patterns
    filter {
        excludeTestsMatching 'schedulers.*'
        excludeTestsMatching 'vars.*'
        exclude('JobScriptsSpec.class')
    }
    outputs.upToDateWhen { false }
}

task seed(type: Test, group: 'verification', description: 'Only runs the seed tests') {
    // Runs only the seed tests, including specific JVM arguments
    testClassesDirs = sourceSets.test.output.classesDirs
    classpath = sourceSets.test.runtimeClasspath
    filter {
        include 'JobScriptsSpec.class'
    }
    systemProperty 'jenkins.test.timeout', jenkinsTestTimeout
    dependsOn tasks.resolveTestPlugins
    jvmArgs += [
        '--add-opens', 'java.base/java.io=ALL-UNNAMED',
        '--add-opens', 'java.base/java.base=ALL-UNNAMED',
        '--add-opens', 'java.base/java.lang=ALL-UNNAMED',
        '--add-opens', 'java.base/java.lang.reflect=ALL-UNNAMED',
        '--add-opens', 'java.base/java.util=ALL-UNNAMED',
        '--add-opens', 'java.base/java.util.concurrent=ALL-UNNAMED',
        '--add-opens', 'java.base/java.util.stream=ALL-UNNAMED',
        '--add-opens', 'java.base/java.net=ALL-UNNAMED',
        '--add-opens', 'java.base/java.nio=ALL-UNNAMED',
        '--add-opens', 'java.base/java.text=ALL-UNNAMED',
        '--add-opens', 'java.base/java.time=ALL-UNNAMED',
        '--add-opens', 'java.base/java.util.logging=ALL-UNNAMED',
        '--add-opens', 'java.base/javax.crypto=ALL-UNNAMED',
        '--add-opens', 'java.base/javax.net.ssl=ALL-UNNAMED',
        '--add-opens', 'java.base/javax.security.auth=ALL-UNNAMED'
    ]
}

// Runs only the pipeline scheduler tests with specified heap size
task schedulers(type: Test, group: 'verification', description: 'Only runs the pipeline scheduler tests') {
    testClassesDirs = sourceSets.test.output.classesDirs
    classpath = sourceSets.test.runtimeClasspath
    minHeapSize = "512m"
    maxHeapSize = "6144m"
    filter {
        includeTestsMatching 'schedulers.*'
    }
}

// Runs only the vars tests with specified heap size
task vars(type: Test, group: 'verification', description: 'Only runs the vars tests') {
    testClassesDirs = sourceSets.test.output.classesDirs
    classpath = sourceSets.test.runtimeClasspath
    minHeapSize = "512m"
    maxHeapSize = "6144m"
    filter {
        includeTestsMatching 'vars.*'
    }
}

// Updates non-regression test callstacks for specified test patterns
task updateCallstacks(type: Test, group: 'verification', description: 'Updates non-regression test callstacks') {
    testClassesDirs = sourceSets.test.output.classesDirs
    classpath = sourceSets.test.runtimeClasspath
    filter {
        includeTestsMatching 'schedulers.*'
        includeTestsMatching 'vars.*'
    }
    systemProperty 'pipeline.stack.write', true
    maxHeapSize = '2g'
}

// Runs CodeNarc static analysis on all parts of the project
task codenarc(type: GradleBuild, group: 'verification', description: 'Run CodeNarc all parts of project') {
    tasks = [
        'codenarcMain',
        'codenarcTest'
    ]
}
