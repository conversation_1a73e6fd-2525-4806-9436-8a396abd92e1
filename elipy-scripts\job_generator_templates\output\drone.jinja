{#
    Command:
        drone
            short_help: Build and submit Drone

    Arguments:

    Required variables:
        p4_port
            required: True
        p4_client
            required: True

    Optional variables:
        changelist
            help: Deprecated parameter. Use --code-changelist instead
        code_changelist
            default: None
            help: Perforce changelist number
        clean
            type: click.BOOL
            default: False
            help: Delete TnT/Local if --clean true is passed, otherwise no cleanup is performed.
        config
            default: release
        password
            required: False
        email
            required: False
        domain_user
            default: None
            help: The user to authenticate to package server as DOMAIN\user
        user
            default: None
            help: Perforce user name
        licensee
            multiple: True
            default: None
            help: Licensee to use
        framework_args
            multiple: True
            default: []
            help: Framework arguments for nantonpackage.
        dry_run
            is_flag: True
            help: Don't submit the built result.
        submit
            type: click.BOOL
            default: True
            help: Set this to false for dry-run
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    drone
    --p4-port {{ p4_port }}
    --p4-client {{ p4_client }}
    {%- if changelist %}
    --changelist {{ changelist }}
    {%- endif %}
    {%- if code_changelist %}
    --code-changelist {{ code_changelist }}
    {%- endif %}
    {%- if clean %}
    --clean {{ clean }}
    {%- endif %}
    {%- if config %}
    --config {{ config }}
    {%- endif %}
    {%- if password %}
    --password {{ password }}
    {%- endif %}
    {%- if email %}
    --email {{ email }}
    {%- endif %}
    {%- if domain_user %}
    --domain-user {{ domain_user }}
    {%- endif %}
    {%- if user %}
    --user {{ user }}
    {%- endif %}
    {%- if licensee %}
    --licensee {{ licensee }}
    {%- endif %}
    {%- if framework_args %}
    --framework-args {{ framework_args }}
    {%- endif %}
    {%- if dry_run %}
    --dry-run {{ dry_run }}
    {%- endif %}
    {%- if submit %}
    --submit {{ submit }}
    {%- endif %}
  displayName: elipy drone
