package com.ea.lib.jobsettings

import com.ea.lib.LibCommonNonCps

class CustomTestsSettings extends JobSetting {
    Map fbLoginDetails
    String userCredentials
    String customTestsReferenceJob
    String triggerType

    void initializeCustomTestsStart(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        def customTestsSettings = branchInfo.custom_tests
        def modifiers = ['custom_tests']
        description = "Scheduler to run tests for ${branchName}"
        codeBranch = branchInfo.code_branch
        projectName = projectFile.name
        isDisabled = LibCommonNonCps.get_setting_value(branchInfo, modifiers, 'disable_build', false)
        customTestsReferenceJob = customTestsSettings.reference_job ?: branchInfo.branch_name + '.code.start'
        cronTrigger = customTestsSettings.trigger_string ?: 'H H/2 * * 1-6\nH 6-23/2 * * 7'
        triggerType = customTestsSettings.trigger_type ?: 'cron'
    }

    void initializeCustomTestsJob(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        def customTestsSettings = branchInfo.custom_tests
        jobLabel = branchInfo.job_label_statebuild ?: 'statebuild'
        description = 'Job for running certain types of tests.'
        buildName = '${JOB_NAME}.${ENV, var="code_changelist"}'
        elipyInstallCall = branchInfo.elipy_install_call

        List customConfigs = customTestsSettings.custom_configs ?: []
        List passThroughArgs = customTestsSettings.pass_through_args ?: []

        fbLoginDetails = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_fb_settings', [:], projectFile)
        userCredentials = LibCommonNonCps.get_setting_value(branchInfo, [], 'user_credentials', '', projectFile)

        extraArgs = customTestsSettings.extra_args ?: ''

        for (customConfig in customConfigs) {
            extraArgs += " --custom-configs ${customConfig}"
        }
        if (customTestsSettings.attempts) {
            extraArgs += " --attempts ${customTestsSettings.attempts}"
        }
        if (customTestsSettings.test_filter) {
            extraArgs += " --test-filter ${customTestsSettings.test_filter}"
        }
        if (customTestsSettings.timeout) {
            extraArgs += " --timeout ${customTestsSettings.timeout}"
        }
        if (customTestsSettings.workers) {
            extraArgs += " --workers ${customTestsSettings.workers}"
        }
        if (branchInfo.frostbite_licensee) {
            extraArgs += " --licensee ${branchInfo.frostbite_licensee}"
        }
        if (userCredentials) {
            extraArgs += ' --email %monkey_email% --password "%monkey_passwd%"'
        }
        for (passThrough in passThroughArgs) {
            extraArgs += " --pass-trough ${passThrough}"
        }

        elipyCmd = this.elipyCall + ' test_runner --data-directory ' + projectFile.dataset + extraArgs
    }
}
