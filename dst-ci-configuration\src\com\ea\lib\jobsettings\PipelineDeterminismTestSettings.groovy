package com.ea.lib.jobsettings

import com.ea.lib.LibCommonNonCps
import com.ea.lib.model.branchsettings.PipelineDeterminismTestConfiguration

class PipelineDeterminismTestSettings extends JobSetting {

    void initializeStart(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        PipelineDeterminismTestConfiguration pipelineDeterminismTestConfiguration = (PipelineDeterminismTestConfiguration) this.branchInfo.pipeline_determinism_test_configuration
        cronTrigger = pipelineDeterminismTestConfiguration.cronTrigger
    }

    void initializeJob(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        PipelineDeterminismTestConfiguration pipelineDeterminismTestConfiguration = (PipelineDeterminismTestConfiguration) this.branchInfo.pipeline_determinism_test_configuration
        timeoutMinutes = pipelineDeterminismTestConfiguration.timeoutMinutes
        jobLabel = pipelineDeterminismTestConfiguration.label
        isDisabled = !pipelineDeterminismTestConfiguration.enabled
        buildName = '${JOB_NAME}.${ENV, var="code_changelist"}'
        String p4Port = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_code_server', '', projectFile)
        if (pipelineDeterminismTestConfiguration.perforceCodeServer) {
            p4Port = pipelineDeterminismTestConfiguration.perforceCodeServer
        }
        elipyCmd = [
            this.elipyCall,
            'pipeline_determinism_test',
            '--code-branch',
            this.branchInfo.code_branch?.replaceAll("'", "\\'"),
            '--code-changelist',
            '%code_changelist%',
            '--script-args',
            '"%script_args%"',
            '--p4-port',
            p4Port?.replaceAll("'", "\\'"),
        ].join(' ')
    }
}
