package com.ea.project.kin.branchsettings

class Upgrade_kin {
    // Settings for jobs
    static Class project = com.ea.project.kin.Kingston
    static Map general_settings = [
        dataset             : project.dataset,
        elipy_install_call  : project.elipy_install_call,
        elipy_call          : project.elipy_call,
        job_label_statebuild: 'statebuild-kin',
        workspace_root      : project.workspace_root,
    ]
    static Map standard_jobs_settings = [
        asset                    : 'Preflightlevels',
        code_reference_job       : 'trigger-from.dice-kin-dev',
        dry_run_code             : true,
        dry_run_data             : true,
        dry_run_frosty           : true,
        dry_run_patchdata        : true,
        dry_run_patchfrosty      : true,
        elipy_shift_config       : false,
        patch_branch             : 'kin-dev',
        retry_limit              : 2,
        server_asset             : 'Preflightlevels',
        slack_channel_code       : [channels: ['dice-build-upgrade']],
        slack_channel_data       : [channels: ['dice-build-upgrade']],
        slack_channel_frosty     : [channels: ['dice-build-upgrade']],
        slack_channel_patchdata  : [channels: ['dice-build-upgrade']],
        slack_channel_patchfrosty: [channels: ['dice-build-upgrade']],
        trigger_type_code        : 'none',
        trigger_type_data        : 'none',
        trigger_type_patchdata   : 'none',
    ]
    static Map preflight_settings = [
        p4_code_server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
        p4_code_creds : 'dice-p4buildedge03-fb',
    ]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'win64game', configs: ['final', 'release', 'retail']],
        [name: 'ps4', configs: ['final', 'release', 'retail']],
        [name: 'xb1', configs: ['final', 'release', 'retail']],
        //[name: 'linux64', configs: ['final']],
        [name: 'win64server', configs: ['final', 'release']],
        [name: 'linux64server', configs: ['final', 'release']],
        [name: 'win64trial', configs: ['final', 'retail']],
        [name: 'tool', configs: ['release']],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = []
    static List data_matrix = [
        'win64',
        'ps4',
        'xb1',
        //'linux64',
        'server',
    ]
    static List data_downstream_matrix = [
        // [name: '.frosty.start', args: []],
    ]
    static List patchdata_matrix = [
        'win64',
        'ps4',
        'xb1',
    ]
    static List patchdata_downstream_matrix = [
        [name: '.patchfrosty.start', args: []],
    ]
    static List frosty_matrix = [
        [name: 'win64', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release']]],
        [name: 'ps4', variants: [[format: 'files', config: 'final', region: 'eu', args: ' --additional-configs release']]],
        [name: 'xb1', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release']]],
        //[name: 'linux64', variants: [[format: 'digital', config: 'final', region: 'ww', args: '']]],
        [name: 'server', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release']]],
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww', args: '']]],
    ]
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = [
        //[name: 'linux64', variants: [[format: 'digital', config: 'final', region: 'ww', args: '']]],
        [name: 'server', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww', args: '']]],
    ]
    static List patchfrosty_matrix = [
        [name: 'win64', variants: [[format: 'digital', config: 'final', region: 'ww', args: ''],
                                   [format: 'digital', config: 'retail', region: 'ww', args: '']]],
        [name: 'ps4', variants: [[format: 'digital', config: 'final', region: 'eu', args: ''],
                                 [format: 'digital', config: 'retail', region: 'eu', args: '']]],
        [name: 'xb1', variants: [[format: 'digital', config: 'final', region: 'ww', args: ''],
                                 [format: 'digital', config: 'retail', region: 'ww', args: '']]],
    ]
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = []
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
