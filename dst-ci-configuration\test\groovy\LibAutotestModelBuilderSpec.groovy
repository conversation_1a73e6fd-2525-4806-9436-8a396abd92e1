import com.ea.lib.jobs.LibAutotestModelBuilder
import com.ea.lib.model.autotest.AutotestCategory
import com.ea.lib.model.autotest.Name
import com.ea.lib.model.autotest.Platform
import com.ea.lib.model.autotest.Region
import com.ea.lib.model.autotest.TestInfo
import com.ea.lib.model.autotest.TestSuite
import com.ea.lib.model.autotest.jobs.AutotestJobsModel
import spock.lang.Specification

class LibAutotestModelBuilderSpec extends Specification {

    private static final List<Platform> DEFAULT_PLATFORMS = [
        new Platform(name: Name.PS5),
        new Platform(name: Name.XBSX),
        new Platform(name: Name.WIN64),
    ]

    void "test compose platforms nested"() {
        when:
        TestInfo testInfo = [
            platforms: [
                new Platform(name: Name.PS4),
                new Platform(name: Name.XB1),
            ],
            tests    : [
                new TestSuite(name: 'test1', platforms: [
                    new Platform(name: Name.PS4),
                    new Platform(name: Name.PS3)],
                ),
            ],
        ]
        List<Platform> platforms = LibAutotestModelBuilder.composePlatforms(testInfo, DEFAULT_PLATFORMS)
        List<Platform> expected = [
            new Platform(name: Name.PS4),
            new Platform(name: Name.XB1),
            new Platform(name: Name.PS3),
        ]
        // 'testInfo overrides defaultPlatforms and adds unique platforms from tests'
        then:
        expected == platforms
    }

    void "test compose platforms prioritize default platforms"() {
        when:
        TestInfo testInfo = [
            tests: [
                new TestSuite(name: 'test1', platforms: [
                    new Platform(name: Name.PS5),
                    new Platform(name: Name.PS3),
                ]),
            ]
        ]
        List<Platform> platforms = LibAutotestModelBuilder.composePlatforms(testInfo, DEFAULT_PLATFORMS)
        List<Platform> expected = [
            new Platform(name: Name.PS5),
            new Platform(name: Name.XBSX),
            new Platform(name: Name.WIN64),
            new Platform(name: Name.PS3),
        ]
        // 'testInfo overrides defaultPlatforms and adds unique platforms from tests'
        then:
        expected == platforms
    }

    void "test compose build selector platforms nested"() {
        when:
        TestInfo testInfo = [
            platforms: [
                new Platform(name: Name.PS4, region: Region.EU),
                new Platform(name: Name.XB1, region: Region.WW),
            ],
            tests    : [
                new TestSuite(name: 'test1', platforms: [
                    new Platform(name: Name.PS5, region: Region.DEV),
                    new Platform(name: Name.PS4, region: Region.EU),
                    new Platform(name: Name.PS4, region: Region.DEV)],
                ),
            ],
        ]
        List<Platform> platforms = LibAutotestModelBuilder.composeBuildSelectorPlatforms(testInfo, DEFAULT_PLATFORMS)
        List<Platform> expected = [
            new Platform(name: Name.PS4, region: Region.EU),
            new Platform(name: Name.XB1, region: Region.WW),
            new Platform(name: Name.PS5, region: Region.DEV),
            new Platform(name: Name.PS4, region: Region.DEV),
        ]
        // 'testInfo overrides defaultPlatforms and adds unique platforms from tests'
        then:
        expected == platforms
    }

    void "test compose jobs sequential"() {
        when:
        AutotestCategory testCategory = new AutotestCategory(
            name: 'testCategoryName',
            testDefinition: 'testDefinition',
            testInfo: new TestInfo(
                platforms: [
                    new Platform(name: Name.PS4),
                    new Platform(name: Name.PS5)],
                tests: [
                    new TestSuite(name: 'test1'),
                    new TestSuite(name: 'test2', platforms: [new Platform(name: Name.PS5)])],
            )
        )
        AutotestJobsModel autotestJobsModel = LibAutotestModelBuilder.composeJobs(testCategory, DEFAULT_PLATFORMS, false, 'kin-dev', null)
        final String EXPECTED_RESULT = '----- BUCKETS -----\n' +
            'BUCKET: test1 2\n' +
            '- kin-dev.autotest.testCategoryName.ps4.test1.job - tests size: 1\n' +
            'Optional region: null\n' +
            'Optional extra_args: null\n' +
            '-- Test suites:\n' +
            '--- test1\n' +
            '- kin-dev.autotest.testCategoryName.ps5.test1.job - tests size: 1\n' +
            'Optional region: null\n' +
            'Optional extra_args: null\n' +
            '-- Test suites:\n' +
            '--- test1\n' +
            'BUCKET: test2 1\n' +
            '- kin-dev.autotest.testCategoryName.ps5.test2.job - tests size: 1\n' +
            'Optional region: null\n' +
            'Optional extra_args: null\n' +
            '-- Test suites:\n' +
            '--- test2\n'

        then:
        // One job for every platform
        autotestJobsModel.jobs.size() == 3
        // The tests are triggered in buckets, one for each test that contains all platforms
        autotestJobsModel.jobBuckets.size() == 2
        autotestJobsModel.modelLog == EXPECTED_RESULT
    }

    void "test compose jobs strict sequential"() {
        when:
        AutotestCategory testCategory = new AutotestCategory(
            name: 'testCategoryName',
            testDefinition: 'testDefinition',
            testInfo: new TestInfo(
                strictSequential: true,
                platforms: [
                    new Platform(name: Name.PS4),
                    new Platform(name: Name.PS5)],
                tests: [
                    new TestSuite(name: 'test1'),
                    new TestSuite(name: 'test2', platforms: [new Platform(name: Name.PS5)])],
            )
        )
        AutotestJobsModel autotestJobsModel = LibAutotestModelBuilder.composeJobs(testCategory, DEFAULT_PLATFORMS, false, 'kin-dev', null)
        final String EXPECTED_RESULT = '----- BUCKETS -----\n' +
            'BUCKET: test1ps4 1\n' +
            '- kin-dev.autotest.testCategoryName.ps4.test1.job - tests size: 1\n' +
            'Optional region: null\n' +
            'Optional extra_args: null\n' +
            '-- Test suites:\n' +
            '--- test1\n' +
            'BUCKET: test1ps5 1\n' +
            '- kin-dev.autotest.testCategoryName.ps5.test1.job - tests size: 1\n' +
            'Optional region: null\n' +
            'Optional extra_args: null\n' +
            '-- Test suites:\n' +
            '--- test1\n' +
            'BUCKET: test2ps5 1\n' +
            '- kin-dev.autotest.testCategoryName.ps5.test2.job - tests size: 1\n' +
            'Optional region: null\n' +
            'Optional extra_args: null\n' +
            '-- Test suites:\n' +
            '--- test2\n'
        then:
        // One job for every platform
        autotestJobsModel.jobs.size() == 3
        // The tests are triggered in buckets, one for each test per platform
        autotestJobsModel.jobBuckets.size() == 3
        autotestJobsModel.modelLog == EXPECTED_RESULT
    }

    void "test compose jobs parallel"() {
        when:
        AutotestCategory testCategory = new AutotestCategory(
            name: 'testCategoryName',
            testDefinition: 'testDefinition',
            testInfo: new TestInfo(
                runLevelsInParallel: true,
                parallelLimit: 6,
                platforms: [
                    new Platform(name: Name.PS4, region: Region.EU),
                    new Platform(name: Name.PS5, region: Region.WW),
                ],
                tests: [
                    new TestSuite(name: 'test1'),
                    new TestSuite(name: 'test2'),
                    new TestSuite(name: 'test3'),
                    new TestSuite(name: 'test4'),
                    new TestSuite(name: 'test5'),
                    new TestSuite(name: 'test6', platforms: [new Platform(name: Name.PS5), new Platform(name: Name.WIN64)]),
                ]
            )
        )
        AutotestJobsModel autotestJobsModel = LibAutotestModelBuilder.composeJobs(testCategory, DEFAULT_PLATFORMS, false, 'kin-dev', null)

        final String EXPECTED_RESULT = '----- BUCKETS -----\n' +
            'BUCKET: parallel-jobs 5\n' +
            '- kin-dev.autotest.testCategoryName.ps4.parallel.job-1 - tests size: 3\n' +
            'Optional region: eu\n' +
            'Optional extra_args: null\n' +
            '-- Test suites:\n' +
            '--- test1\n' +
            '--- test3\n' +
            '--- test5\n' +
            '- kin-dev.autotest.testCategoryName.ps5.parallel.job-1 - tests size: 3\n' +
            'Optional region: ww\n' +
            'Optional extra_args: null\n' +
            '-- Test suites:\n' +
            '--- test1\n' +
            '--- test3\n' +
            '--- test5\n' +
            '- kin-dev.autotest.testCategoryName.win64.parallel.job-1 - tests size: 1\n' +
            'Optional region: null\n' +
            'Optional extra_args: null\n' +
            '-- Test suites:\n' +
            '--- test6\n' +
            '- kin-dev.autotest.testCategoryName.ps4.parallel.job-2 - tests size: 2\n' +
            'Optional region: eu\n' +
            'Optional extra_args: null\n' +
            '-- Test suites:\n' +
            '--- test2\n' +
            '--- test4\n' +
            '- kin-dev.autotest.testCategoryName.ps5.parallel.job-2 - tests size: 3\n' +
            'Optional region: ww\n' +
            'Optional extra_args: null\n' +
            '-- Test suites:\n' +
            '--- test2\n' +
            '--- test4\n' +
            '--- test6\n'
        then:
        // It creates parallel_limit amount of jobs and divides the tests into them, thereafter it removes the empty jobs, in this case the empty win64 job
        autotestJobsModel.jobs.size() == 5
        // The jobs are all started in parallel, therefore there is only one bucket
        autotestJobsModel.jobBuckets.size() == 1
        autotestJobsModel.modelLog == EXPECTED_RESULT
    }

}
