"""
test_icepick_run_preflight.py

Unit testing for icepick_run_preflight
"""
import unittest
from unittest import TestCase
import os
import pytest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import MagicMock, patch, call
from dice_elipy_scripts import icepick_run_preflight
from dice_elipy_scripts.icepick_run_preflight import cli
from elipy2 import LOGGER

config_path = os.path.join(os.path.dirname(__file__), "data", "elipy_test.yml")


class TestIcepickBase(unittest.TestCase):
    OPTION_TEST_SUITES = "--ts"
    OPTION_CODE_BRANCH = "--cb"
    OPTION_CODE_CHANGELIST = "--cc"
    OPTION_USER = "--user"
    OPTION_PENDING_CHANGELIST = "--pc"
    OPTION_DATA_CHANGELIST = "--dc"
    OPTION_TEST_EXTRA_ARG = "--extra_arg"
    OPTION_CONFIG = "-c"
    OPTION_SERVER_CONFIG = "--sc"
    OPTION_LICENSEEE = "-l"
    OPTION_custom_tag = "--cut"
    OPTION_USE_LOCAL_CODEBUILDS = "--ulcb"
    OPTION_KEEP_LOCAL_BIN = "--klb"
    OPTION_SKIP_UNSHELVE = "--sun"
    OPTION_SKIP_REVERT = "--sre"
    OPTION_COOK_ASSETS = "--ca"
    OPTION_DO_WARMUP = "--do-warmup"
    OPTION_NOT_WARMUP = "--not-warmup"
    ARGUMENT_P4PORT = "myjenkinsp4port"
    ARGUMENT_P4CLIENT = "myjenkinsclient"

    VALUE_PLATFORM = "ps4"
    VALUE_TEST_SUITE_NAME1 = "test-suite1"
    VALUE_TEST_SUITE_NAME2 = "test-suite2"
    VALUE_LICENSEE1 = "ExampleGame"
    VALUE_LICENSEE2 = "FBNULLLICENSEE"
    VALUE_ARTIFACT_TEST = "Tests"
    VALUE_custom_tag = "test_folder"
    VALUE_TEST_SUITES = VALUE_TEST_SUITE_NAME1
    VALUE_TEST_SUITES2 = VALUE_TEST_SUITE_NAME2
    VALUE_ASSETS = "asset1"
    VALUE_CODE_BRANCH = "code-branch"
    VALUE_CODE_CHANGELIST = "123"
    VALUE_PENDING_CHANGELIST = "123"
    VALUE_DATA_CHANGELIST = "123"
    VALUE_TEST_EXTRA_ARG = "test-value"
    VALUE_SERVER_PLATFORM = "server"
    VALUE_CONFIG = "final"
    VALUE_SERVER_CONFIG = "final"
    VALUE_USER = "user"

    DEFAULT_ARGS = [
        VALUE_PLATFORM,
        OPTION_TEST_SUITES,
        VALUE_TEST_SUITES,
        OPTION_TEST_SUITES,
        VALUE_TEST_SUITES2,
        OPTION_CODE_BRANCH,
        VALUE_CODE_BRANCH,
        OPTION_CODE_CHANGELIST,
        VALUE_CODE_CHANGELIST,
        OPTION_DATA_CHANGELIST,
        VALUE_DATA_CHANGELIST,
        VALUE_ASSETS,
    ]

    def setUp(self):
        self.patcher_IcepickUtils = patch(
            "dice_elipy_scripts.icepick_run_preflight.icepick.IcepickUtils", autospec=True
        )
        mock_IcepickUtils = self.patcher_IcepickUtils.start()

        self.patcher_fbcli_pullbuild = patch("elipy2.frostbite.fbcli.pullbuild", autospec=True)
        self.mock_fbcli_pullbuild = self.patcher_fbcli_pullbuild.start()

        self.patcher_fbenv_layer_cook = patch("elipy2.frostbite.fbenv_layer.cook", autospec=True)
        self.mock_fbenv_layer_cook = self.patcher_fbenv_layer_cook.start()

        self.patcher_p4utils = patch("elipy2.p4.P4Utils", autospec=True)
        self.mock_p4utils = self.patcher_p4utils.start()

        self.patcher_raise_if_cl_not_exists = patch(
            "dice_elipy_scripts.datapreflight.raise_if_cl_not_exists"
        )
        self.mock_raise_if_cl_not_exists = self.patcher_raise_if_cl_not_exists.start()

        self.patcher_raise_if_wrong_stream = patch(
            "dice_elipy_scripts.datapreflight.raise_if_wrong_stream"
        )
        self.mock_raise_if_wrong_stream = self.patcher_raise_if_wrong_stream.start()

        self.patcher_filerutils = patch("elipy2.filer.FilerUtils")
        self.mock_filerutils = self.patcher_filerutils.start()
        self.mock_filerutils.return_value = MagicMock()

        self.mock_run_icepick = MagicMock()
        mock_IcepickUtils().run_icepick = self.mock_run_icepick

        self.mock_run_icepick_cook = MagicMock()
        mock_IcepickUtils().run_icepick_cook = self.mock_run_icepick_cook

    def tearDown(self):
        self.patcher_IcepickUtils.stop()
        self.patcher_fbcli_pullbuild.stop()
        self.patcher_fbenv_layer_cook.stop()
        self.patcher_p4utils.stop()
        self.patcher_raise_if_wrong_stream.stop()
        self.patcher_raise_if_cl_not_exists.stop()
        self.patcher_filerutils.stop()
        os.environ["use_fbcli"] = "False"

    def _helper_invoke_icepick_run_preflight_cli(
        self, extra_args=[], use_fbcli=True, catch_exceptions=True
    ):
        if use_fbcli:
            os.environ["use_fbcli"] = "True"
        runner = CliRunner()
        return runner.invoke(cli, self.DEFAULT_ARGS + extra_args, catch_exceptions=catch_exceptions)


@patch("dice_elipy_scripts.icepick_run_preflight.save_icepick_logs", MagicMock())
@patch("dice_elipy_scripts.icepick_run_preflight.add_sentry_tags", MagicMock())
@patch.multiple(
    "dice_elipy_scripts.icepick_run_preflight.frostbite_core",
    minimum_fb_version=MagicMock(return_value=True),
    get_game_data_dir=MagicMock(return_value="\\some\\path"),
    get_licensee_id=MagicMock(return_value="some-licensee"),
)
@patch("dice_elipy_scripts.icepick_run_preflight.set_licensee", MagicMock())
@patch("dice_elipy_scripts.icepick_run_preflight.raise_if_cl_not_exists", MagicMock())
@patch("dice_elipy_scripts.icepick_run_preflight.raise_if_wrong_stream", MagicMock())
@patch("dice_elipy_scripts.icepick_run_preflight.icepick_clean", MagicMock())
@patch("dice_elipy_scripts.icepick_run_preflight.frosty_build_utils", MagicMock())
@patch(
    "dice_elipy_scripts.icepick_run_preflight.icepick.IcepickUtils.settings_files_relative_to_absolute_paths",
    MagicMock(return_value=[]),
)
class TestIcepickRunPreflight(TestIcepickBase):
    @patch("dice_elipy_scripts.icepick_run_preflight.filer", MagicMock())
    def test_run_icepick_preflight(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.ARGUMENT_P4PORT,
                self.ARGUMENT_P4CLIENT,
                self.VALUE_PLATFORM,
                self.OPTION_TEST_SUITES,
                self.VALUE_TEST_SUITES,
                self.OPTION_CODE_BRANCH,
                self.VALUE_CODE_BRANCH,
                self.OPTION_CODE_CHANGELIST,
                self.VALUE_CODE_CHANGELIST,
                self.OPTION_DATA_CHANGELIST,
                self.VALUE_DATA_CHANGELIST,
                self.OPTION_PENDING_CHANGELIST,
                self.VALUE_PENDING_CHANGELIST,
                self.OPTION_USER,
                self.VALUE_USER,
            ],
        )
        if result.exception:
            LOGGER.info(result.exception)
        self.assertEqual(self.mock_run_icepick.call_count, 1)
        kwargs = {
            "platform": self.VALUE_PLATFORM,
            "test_suite": self.VALUE_TEST_SUITE_NAME1,
            "test_group": "preflight_skybuild",
            "config": "final",
            "settings_file_list": [],
            "send_frosting_report": True,
            "lease": None,
            "build_type": "static",
            "autobuild": False,
            "run_args": [],
            "ignore_icepick_exit_code": False,
            "cook": False,
            "extra_framework_args": None,
            "custom_test_suite_data": "code_changelist:123;data_changelist:123;",
        }
        self.mock_run_icepick.assert_called_once_with(**kwargs)
        assert result.exit_code == 0

    @patch("dice_elipy_scripts.icepick_run_preflight.filer", MagicMock())
    def test_run_icepick_preflight_extra(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.ARGUMENT_P4PORT,
                self.ARGUMENT_P4CLIENT,
                "win64",
                self.OPTION_TEST_SUITES,
                self.VALUE_TEST_SUITES,
                self.OPTION_CODE_BRANCH,
                self.VALUE_CODE_BRANCH,
                self.OPTION_CODE_CHANGELIST,
                self.VALUE_CODE_CHANGELIST,
                self.OPTION_DATA_CHANGELIST,
                self.VALUE_DATA_CHANGELIST,
                self.OPTION_LICENSEEE,
                self.VALUE_LICENSEE1,
                "run_arg",
                self.OPTION_PENDING_CHANGELIST,
                self.VALUE_PENDING_CHANGELIST,
                self.OPTION_USER,
                self.VALUE_USER,
            ],
        )
        if result.exception:
            LOGGER.info(result.exception)
        self.assertEqual(self.mock_run_icepick.call_count, 1)
        kwargs = {
            "platform": "win64",
            "test_suite": self.VALUE_TEST_SUITE_NAME1,
            "test_group": "preflight_skybuild",
            "config": "final",
            "settings_file_list": [],
            "send_frosting_report": True,
            "lease": None,
            "build_type": "static",
            "autobuild": False,
            "run_args": ["run_arg"],
            "ignore_icepick_exit_code": False,
            "cook": False,
            "extra_framework_args": None,
            "custom_test_suite_data": "code_changelist:123;data_changelist:123;",
        }
        self.mock_run_icepick.assert_called_once_with(**kwargs)
        assert result.exit_code == 0

    def test_download_server_binaries(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.ARGUMENT_P4PORT,
                self.ARGUMENT_P4CLIENT,
                self.VALUE_PLATFORM,
                self.OPTION_TEST_SUITES,
                self.VALUE_TEST_SUITES,
                self.OPTION_CODE_BRANCH,
                self.VALUE_CODE_BRANCH,
                self.OPTION_CODE_CHANGELIST,
                self.VALUE_CODE_CHANGELIST,
                self.OPTION_DATA_CHANGELIST,
                self.VALUE_DATA_CHANGELIST,
                self.OPTION_PENDING_CHANGELIST,
                self.VALUE_PENDING_CHANGELIST,
                self.OPTION_USER,
                self.VALUE_USER,
            ],
        )

        self.mock_filerutils.return_value.fetch_code.assert_has_calls(
            [
                call(
                    self.VALUE_CODE_BRANCH,
                    self.VALUE_CODE_CHANGELIST,
                    self.VALUE_PLATFORM,
                    self.VALUE_CONFIG,
                    mirror=False,
                    custom_tag=None,
                    target_build_share=None,
                    use_bilbo=False,
                ),
                call(
                    self.VALUE_CODE_BRANCH,
                    self.VALUE_CODE_CHANGELIST,
                    "server",
                    "final",
                    mirror=False,
                    custom_tag=None,
                    target_build_share=None,
                    use_bilbo=False,
                ),
            ]
        )
        assert result.exit_code == 0

    def test_ignore_download_server_binaries(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.ARGUMENT_P4PORT,
                self.ARGUMENT_P4CLIENT,
                self.VALUE_PLATFORM,
                self.OPTION_TEST_SUITES,
                self.VALUE_TEST_SUITES,
                self.OPTION_CODE_BRANCH,
                self.VALUE_CODE_BRANCH,
                self.OPTION_CODE_CHANGELIST,
                self.VALUE_CODE_CHANGELIST,
                self.OPTION_DATA_CHANGELIST,
                self.VALUE_DATA_CHANGELIST,
                self.OPTION_PENDING_CHANGELIST,
                self.VALUE_PENDING_CHANGELIST,
                self.OPTION_USER,
                self.VALUE_USER,
                self.OPTION_USE_LOCAL_CODEBUILDS,
                "true",
            ],
        )
        self.mock_filerutils.return_value.fetch_code.assert_not_called()
        assert result.exit_code == 0

    def test_ignore_download_skip_revert(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.ARGUMENT_P4PORT,
                self.ARGUMENT_P4CLIENT,
                self.VALUE_PLATFORM,
                self.OPTION_TEST_SUITES,
                self.VALUE_TEST_SUITES,
                self.OPTION_CODE_BRANCH,
                self.VALUE_CODE_BRANCH,
                self.OPTION_CODE_CHANGELIST,
                self.VALUE_CODE_CHANGELIST,
                self.OPTION_DATA_CHANGELIST,
                self.VALUE_DATA_CHANGELIST,
                self.OPTION_PENDING_CHANGELIST,
                self.VALUE_PENDING_CHANGELIST,
                self.OPTION_USER,
                self.VALUE_USER,
                self.OPTION_SKIP_REVERT,
                "true",
            ],
        )
        self.mock_p4utils.return_value.revert.assert_not_called()
        self.mock_p4utils.return_value.unshelve.assert_called_once()
        assert result.exit_code == 0

    def test_ignore_download_skip_unshelve(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.ARGUMENT_P4PORT,
                self.ARGUMENT_P4CLIENT,
                self.VALUE_PLATFORM,
                self.OPTION_TEST_SUITES,
                self.VALUE_TEST_SUITES,
                self.OPTION_CODE_BRANCH,
                self.VALUE_CODE_BRANCH,
                self.OPTION_CODE_CHANGELIST,
                self.VALUE_CODE_CHANGELIST,
                self.OPTION_DATA_CHANGELIST,
                self.VALUE_DATA_CHANGELIST,
                self.OPTION_PENDING_CHANGELIST,
                self.VALUE_PENDING_CHANGELIST,
                self.OPTION_USER,
                self.VALUE_USER,
                self.OPTION_SKIP_UNSHELVE,
                "true",
            ],
        )
        self.mock_p4utils.return_value.revert.assert_called()
        self.mock_p4utils.return_value.unshelve.assert_not_called()
        assert result.exit_code == 0

    def test_do_warmup(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.ARGUMENT_P4PORT,
                self.ARGUMENT_P4CLIENT,
                self.VALUE_PLATFORM,
                self.OPTION_TEST_SUITES,
                self.VALUE_TEST_SUITES,
                self.OPTION_CODE_BRANCH,
                self.VALUE_CODE_BRANCH,
                self.OPTION_CODE_CHANGELIST,
                self.VALUE_CODE_CHANGELIST,
                self.OPTION_DATA_CHANGELIST,
                self.VALUE_DATA_CHANGELIST,
                self.OPTION_PENDING_CHANGELIST,
                self.VALUE_PENDING_CHANGELIST,
                self.OPTION_USER,
                self.VALUE_USER,
                self.OPTION_DO_WARMUP,
            ],
        )
        self.mock_p4utils.return_value.revert.assert_called()
        self.mock_p4utils.return_value.unshelve.assert_not_called()
        self.mock_raise_if_cl_not_exists.assert_not_called()
        self.mock_raise_if_wrong_stream.assert_not_called()

        assert result.exit_code == 0

    def test_icepick_cook_assets(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.ARGUMENT_P4PORT,
                self.ARGUMENT_P4CLIENT,
                self.VALUE_PLATFORM,
                self.OPTION_TEST_SUITES,
                self.VALUE_TEST_SUITES,
                self.OPTION_CODE_BRANCH,
                self.VALUE_CODE_BRANCH,
                self.OPTION_CODE_CHANGELIST,
                self.VALUE_CODE_CHANGELIST,
                self.OPTION_DATA_CHANGELIST,
                self.VALUE_DATA_CHANGELIST,
                self.OPTION_PENDING_CHANGELIST,
                self.VALUE_PENDING_CHANGELIST,
                self.OPTION_USER,
                self.VALUE_USER,
                self.OPTION_COOK_ASSETS,
                "true",
                self.OPTION_NOT_WARMUP,
            ],
        )

        self.mock_p4utils.return_value.unshelve.assert_called_once()
        self.mock_run_icepick_cook.assert_called_once()
        assert result.exit_code == 0

    def test_icepick_not_cook_assets(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.ARGUMENT_P4PORT,
                self.ARGUMENT_P4CLIENT,
                self.VALUE_PLATFORM,
                self.OPTION_TEST_SUITES,
                self.VALUE_TEST_SUITES,
                self.OPTION_CODE_BRANCH,
                self.VALUE_CODE_BRANCH,
                self.OPTION_CODE_CHANGELIST,
                self.VALUE_CODE_CHANGELIST,
                self.OPTION_DATA_CHANGELIST,
                self.VALUE_DATA_CHANGELIST,
                self.OPTION_PENDING_CHANGELIST,
                self.VALUE_PENDING_CHANGELIST,
                self.OPTION_USER,
                self.VALUE_USER,
            ],
        )

        self.mock_run_icepick_cook.assert_not_called()
        assert result.exit_code == 0

    def test_run_icepick_custom_test_suite_data(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.ARGUMENT_P4PORT,
                self.ARGUMENT_P4CLIENT,
                self.VALUE_PLATFORM,
                self.OPTION_TEST_SUITES,
                self.VALUE_TEST_SUITES,
                self.OPTION_CODE_BRANCH,
                self.VALUE_CODE_BRANCH,
                self.OPTION_CODE_CHANGELIST,
                self.VALUE_CODE_CHANGELIST,
                self.OPTION_DATA_CHANGELIST,
                self.VALUE_DATA_CHANGELIST,
                self.OPTION_PENDING_CHANGELIST,
                self.VALUE_PENDING_CHANGELIST,
                self.OPTION_USER,
                self.VALUE_USER,
                "--ctsd",
                "mykey:myvalue",
            ],
        )
        if result.exception:
            LOGGER.info(result.exception)

        kwargs = {
            "platform": self.VALUE_PLATFORM,
            "test_suite": self.VALUE_TEST_SUITE_NAME1,
            "test_group": "preflight_skybuild",
            "config": "final",
            "settings_file_list": [],
            "send_frosting_report": True,
            "lease": None,
            "build_type": "static",
            "autobuild": False,
            "run_args": [],
            "ignore_icepick_exit_code": False,
            "cook": False,
            "extra_framework_args": None,
            "custom_test_suite_data": "code_changelist:123;data_changelist:123;mykey:myvalue;",
        }
        self.mock_run_icepick.assert_called_once_with(**kwargs)
        assert result.exit_code == 0

    def test_run_icepick_with_false_send_frosting_report_param(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.ARGUMENT_P4PORT,
                self.ARGUMENT_P4CLIENT,
                self.VALUE_PLATFORM,
                self.OPTION_TEST_SUITES,
                self.VALUE_TEST_SUITES,
                self.OPTION_CODE_BRANCH,
                self.VALUE_CODE_BRANCH,
                self.OPTION_CODE_CHANGELIST,
                self.VALUE_CODE_CHANGELIST,
                self.OPTION_DATA_CHANGELIST,
                self.VALUE_DATA_CHANGELIST,
                self.OPTION_PENDING_CHANGELIST,
                self.VALUE_PENDING_CHANGELIST,
                self.OPTION_USER,
                self.VALUE_USER,
                "--frosting-report",
                "false",
            ],
        )
        if result.exception:
            LOGGER.info(result.exception)

        kwargs = {
            "platform": self.VALUE_PLATFORM,
            "test_suite": self.VALUE_TEST_SUITE_NAME1,
            "test_group": "preflight_skybuild",
            "config": "final",
            "settings_file_list": [],
            "send_frosting_report": False,
            "lease": None,
            "build_type": "static",
            "autobuild": False,
            "run_args": [],
            "ignore_icepick_exit_code": False,
            "cook": False,
            "extra_framework_args": None,
            "custom_test_suite_data": "code_changelist:123;data_changelist:123;",
        }
        self.mock_run_icepick.assert_called_once_with(**kwargs)
        assert result.exit_code == 0
