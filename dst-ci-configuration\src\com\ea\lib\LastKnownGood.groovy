package com.ea.lib

class LastKnownGood {
    static List<String> dataRemoteTriggers(branchInfo) {
        return populateCurlCalls(
            branchInfo.branch_name + '.data.lastknowngood',
            'token=remotebuild&code_changelist=${code_changelist}&data_changelist=${data_changelist}&cause=${BUILD_URL}"',
            branchInfo.remote_masters_to_receive_data)
    }

    static List<String> codeRemoteTriggers(branchInfo) {
        return populateCurlCalls(
            branchInfo.branch_name + '.code.lastknowngood',
            'token=remotebuild&code_changelist=${code_changelist}&cause=${BUILD_URL}"',
            branchInfo.remote_masters_to_receive_code)
    }

    static List<String> autotestToCodeRemoteTriggers(String remoteJob, String remoteMaster) {
        return populateCurlCalls(
            remoteJob,
            'token=remotebuild&code_changelist=${code_changelist}&cause=${BUILD_URL}"',
            [[name: remoteMaster]]
        )
    }

    static List<String> lastknowngoodRemoteTriggers(String remoteJob, List<Map> remoteMasters) {
        return populateCurlCalls(
            remoteJob,
            'token=remotebuild&code_changelist=${code_changelist}&data_changelist=${data_changelist}&cause=${BUILD_URL}"',
            remoteMasters
        )
    }

    private static List<String> populateCurlCalls(remoteJobName, arguments, remoteMasters) {
        List<String> curlCalls = []
        for (remoteMaster in remoteMasters) {
            def remoteMasterName = remoteMaster.name.split('\\.')[0].replace('-', '_')
            def authString = '-u ${jenkinsUser_' + remoteMasterName + '}:${jenkinsAPIToken_' + remoteMasterName + '}'
            // Add "stage-" to jobName in case of staging environment - project/kin/mastersettings/AWSStaging.groovy
            def header = "/usr/bin/curl -s ${authString} -XPOST \"https://${remoteMaster.name}/job/${remoteMaster.name.contains('staging') ? "stage-${remoteJobName}" : remoteJobName}/buildWithParameters?"
            def ignoreResponse = ' || true'
            def curlTrigger = "${header}${arguments}"
            if (remoteMaster.allow_failure) {
                curlTrigger = curlTrigger + ignoreResponse
            }
            curlCalls.add(curlTrigger)
        }
        return curlCalls
    }
}
