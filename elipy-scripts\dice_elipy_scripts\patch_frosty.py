"""
patch_frosty.py

Script used by DICE to create our patch packages.

The patch_frosty script copies code from the filer, and combines
this with the bundles created by the patch_databuild script to
create a patch which only contains the difference between the
latest released version and the current version (instead of the full game).

General setup:
    - Clean up the machine by killing running processes.
    - Initialize objects using packages from Elipy core, to be used later.
        - One instance of filer.FilerUtils().
        - One instance of package.PackageUtils().
    - Set data directory, since the Frostbite build process sometimes gets
      an incorrect default value for this.
    - Set licensee
        - Optional step that we run when a licensee is sent as an argument.
          We need to set this when we don't build the default licensee on a stream.
          For example when we build dev-na where the default licensee is ExampleGame.
    - Run a correction for the EA Installer signtool, since it's incorrectly configured.
    - Set baseline branches and changelists.
    - Add some extra arguments depending on the platform.
    - If we build ps4 or xb1, install the relevant SDKs.
    - If we build xb1, install the submission validator (skip this in some cases).
    - Copy code binaries for the platform we're building.
    - Specify some platform-independent arguments.
        - This includes the location of the delta bundles
          (produced by the patch_databuild script).
    - Special for xb1:
        - Copy baseline layout.
        - Specify platform specific arguments.
    - Special for ps:
        - Get appversion: Here we check the version of the baseline
          patch and increase the version with 1, unless we from jenkins
          say this should be increase with more.
        - Specify platform specific arguments.
        - Get package type for the disc build.
        - Get disc package from network share.
          Ps platforms always needs the disc build when patching
        - If we're not building the first patch,
          also get the currently live package from the network share.
        - Default is to use the retail config for the baseline, regardless of what
          the patch is built with. This can be changed to use the same config for
          baseline and patch.
    - Special for win64:
        - Specify platform specific arguments.
        - If specified, copy code binaries for win64trial from network share.
    - Get the secrets we need to run the packaging process.
    - Get the delta bundles (produced by the patch_databuild script).
    - Run FrostyIsoTool to package the code and the delta bundles to a game build.
    - Deploy the built game to network share.
    - If we build win64, upload symbols.
        - If specified, upload symbols also for win64trial.

Examples:
    * elipy --location criterion patch_frosty win64 final --code-branch build-release
      --code-changelist 436395 --data-branch build-release --data-changelist 436395
      --disc-code-branch build-release --disc-code-changelist 371518
      --disc-data-branch build-release --disc-data-changelist 371518
      --data-directory Data --region ww  --first-patch
    * elipy --location criterion patch_frosty win64 final --code-branch build-release
      --code-changelist 436395 --data-branch build-release --data-changelist 436395
      --disc-code-branch build-release --disc-code-changelist 371518
      --disc-data-branch build-release --disc-data-changelist 371518  --data-directory Data
      --region ww  --patch-code-branch build-release --patch-code-changelist 417610
      --patch-data-branch build-release --patch-data-changelist 417610
    * elipy --location criterion patch_frosty win64 final --code-branch build-release
      --code-changelist 436395 --data-branch build-release --data-changelist 436395
      --disc-code-branch build-release --disc-code-changelist 371518
      --disc-data-branch build-release --disc-data-changelist 371518  --data-directory Data
      --region ww  --patch-code-branch build-release --patch-code-changelist 417610
      --patch-data-branch build-release --patch-data-changelist 417610 --use-win64trial
      --skip-streaming-install-package --email <EMAIL> --password "****"
    * elipy --location dice patch_frosty xbsx final --code-branch kin-release
      --code-changelist 17903931 --data-branch kin-release --data-changelist 5026337
      --disc-code-branch kin-release --disc-code-changelist 12409338
      --disc-data-branch kin-release --disc-data-changelist 4592495
      --data-directory kindata --region ww  --patch-code-branch kin-release
      --patch-code-changelist 17743749 --patch-data-branch kin-release
      --patch-data-changelist 5019321 --use-win64trial --licensee BattlefieldGame
      --email <EMAIL> --password "****" --use-recompression-cache --enable-eac
"""

import click
import os
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.frosty_build_utils import (
    add_frosty_log_to_output,
    install_required_sdks,
    patch_eainstaller_signtool,
)
from dice_elipy_scripts.utils.licensee_helper import set_licensee
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import (
    avalanche,
    core,
    data,
    filer,
    filer_paths,
    frostbite_core,
    local_paths,
    LOGGER,
    package,
    running_processes,
    secrets,
    SETTINGS,
    symbols,
)
from elipy2.cli import pass_context
from elipy2.exceptions import ELIPYException
from elipy2.frostbite import fbenv_layer, icepick
from elipy2.steam_utils import SteamUtils
from elipy2.telemetry import collect_metrics
from elipy2.oreans import __OUTPUT_SUFFIX as OREANS_OUTPUT_SUFFIX


@click.command(
    "patch_frosty",
    short_help="Deploys a playable game build with both binaries and data.",
)
@click.argument("platform")
@click.argument("config")
@click.option("--data-directory", help="Which data directory to use for the working data set.")
@click.option("--code-branch", help="Branch/stream to fetch the code/binary build from.")
@click.option("--code-changelist", help="Changelist of binaries to fetch.")
@click.option("--data-branch", help="Branch/stream that data is coming from.")
@click.option("--data-changelist", help="Changelist of data being used.")
@click.option("--region", help="Which region to deploy for (default is ww).", default="ww")
@click.option(
    "--first-patch/--not-first-patch",
    default=False,
    help="Flag for building the first patch.",
)
@click.option(
    "--use-win64trial/--no-win64trial",
    default=False,
    help="Flag for using win64 trial for win64 patches.",
)
@click.option("--dry-run", is_flag=True, help="Build code without deploying.")
@click.option("--use-oreans", is_flag=True, help="Use oreans binaries.")
@click.option(
    "--use-recompression-cache",
    is_flag=True,
    help="Alternative Avalanche server to use for the recompression cache.",
)
@click.option(
    "--increase-version-by",
    default=1,
    help="Number which we should increase the ps4/ps5 appversion with.",
)
@click.option("--disc-data-branch", help="Which branch the disc data was built on.", required=True)
@click.option(
    "--disc-data-changelist",
    required=True,
    help="Which changelist the disc data was deployed from.",
)
@click.option("--disc-code-branch", help="Which branch the disc code was built on.", required=True)
@click.option(
    "--disc-code-changelist",
    required=True,
    help="Which changelist the disc code was deployed from.",
)
@click.option(
    "--patch-data-branch",
    required=False,
    help="Which branch the latest live patch data was built on.",
)
@click.option(
    "--patch-data-changelist",
    required=False,
    help="Which changelist the latest live patch data was deployed from.",
)
@click.option(
    "--patch-code-branch",
    required=False,
    help="Which branch the latest live patch code was built on.",
)
@click.option(
    "--patch-code-changelist",
    required=False,
    help="Which changelist the latest live patch code was deployed from.",
)
@click.option(
    "--disable-frosty-symbol-upload",
    is_flag=True,
    default=False,
    help="Disable Frosty Symbol Upload.",
)
@click.option(
    "--skip-streaming-install-package",
    is_flag=True,
    help="set STREAMING_INSTALL_CREATE_SUBMISSION_PACKAGES to false.",
)
@click.option(
    "--password",
    default=None,
    help="User credentials to authenticate to package server.",
)
@click.option("--email", default=None, help="User email to authenticate to package server.")
@click.option(
    "--domain-user",
    default=None,
    help="The user to authenticate to package server as DOMAIN\\user",
)
@click.option("--enable-eac", is_flag=True, help="Enable EasyAntiCheat.")
@click.option(
    "--same-baseline-config",
    is_flag=True,
    help="Use same config for the baseline and the patch (default is retail baseline).",
)
@click.option("--steam-build", default=False)
@click.option("--licensee", multiple=True, default=None, help="Licensee to use.")
@click.option(
    "--use-combine-bundles",
    default=False,
    help="Use head bundles from two streams, combine these before running ddelta.",
)
@click.option(
    "--combine-code-branch",
    default=None,
    help="Branch for the second set of binaries to use in the combine workflow.",
)
@click.option(
    "--combine-code-changelist",
    default=None,
    help="Changelist for the second set of binaries to use in the combine workflow.",
)
@click.option(
    "--combine-data-branch",
    default=None,
    help="Branch for the second set of data to use in the combine workflow.",
)
@click.option(
    "--combine-data-changelist",
    default=None,
    help="Changelist for the second set of data to use in the combine workflow.",
)
@click.option(
    "--combine-disc-code-branch",
    default=None,
    help="Branch for the disc version of the second set of binaries to use in combine.",
)
@click.option(
    "--combine-disc-code-changelist",
    default=None,
    help="Changelist for the disc version of the second set of binaries to use in combine.",
)
@click.option(
    "--combine-disc-data-branch",
    default=None,
    help="Branch for the disc version of the second set of data to use in combine.",
)
@click.option(
    "--combine-disc-data-changelist",
    default=None,
    help="Changelist for the disc version of the second set of data to use in combine.",
)
@click.option(
    "--combine-patch-code-branch",
    default=None,
    help="Branch for the patch version of the second set of binaries to use in combine.",
)
@click.option(
    "--combine-patch-code-changelist",
    default=None,
    help="Changelist for the patch version of the second set of binaries to use in combine.",
)
@click.option(
    "--combine-patch-data-branch",
    default=None,
    help="Branch for the patch version of the second set of data to use in combine.",
)
@click.option(
    "--combine-patch-data-changelist",
    default=None,
    help="Changelist for the patch version of the second set of data to use in combine.",
)
@click.option(
    "--combine-settings-file",
    type=str,
    default=None,
    help="Settings file to use for the combine build",
)
@click.option(
    "--use-precreated-delta-bundles",
    is_flag=True,
    default=False,
    help="Use pre-created delta bundles from network share instead of creating them locally.",
)
@click.option(
    "--use-precreated-combined-bundles",
    is_flag=True,
    default=False,
    help="Use pre-created combined bundles from network share instead of creating them locally.",
)
@click.option(
    "--patch-type",
    type=click.Choice(["from_disk", "incremental", "disc_build"]),
    default="incremental",
    help="What kind of patch are we producing",
    required=True,
)
@click.option(
    "--use-head-bundles-as-base-bundles",
    is_flag=True,
    help="Use head bundles as base bundles. Experimental, only use if requested by the game team.",
)
@click.option(
    "--standalone-disc-baseline",
    is_flag=True,
    help="Use a standalone build as a disc baseline for a combined build.",
)
@click.option(
    "--standalone-patch-baseline",
    is_flag=True,
    help="Use a standalone build as a patch baseline for a combined build.",
)
@click.option(
    "--virtual-branch-override",
    type=bool,
    default=False,
    help="Override the Perforce depot branch with the virtual branch used in the job",
)
@click.option(
    "--clean-steam-sdk",
    default=False,
    type=click.BOOL,
    help="Force re-download and replace Steam SDK on the file system",
)
@click.option(
    "--steam-drmwrap",
    is_flag=True,
    help="Enable Steam DRM wrapper for the build",
)
# pylint: disable=too-many-statements
# pylint: disable=too-many-locals
# pylint: disable=too-many-lines
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    platform,
    config,
    data_directory,
    code_branch,
    code_changelist,
    data_branch,
    data_changelist,
    region,
    first_patch,
    use_win64trial,
    dry_run,
    use_oreans,
    increase_version_by,
    use_recompression_cache,
    disc_code_branch,
    disc_code_changelist,
    disc_data_branch,
    disc_data_changelist,
    patch_code_branch,
    patch_code_changelist,
    patch_data_branch,
    patch_data_changelist,
    disable_frosty_symbol_upload,
    skip_streaming_install_package,
    password,
    email,
    domain_user,
    enable_eac,
    same_baseline_config,
    steam_build,
    licensee,
    use_combine_bundles,
    combine_code_branch,
    combine_code_changelist,
    combine_data_branch,
    combine_data_changelist,
    combine_disc_code_branch,
    combine_disc_code_changelist,
    combine_disc_data_branch,
    combine_disc_data_changelist,
    combine_patch_code_branch,
    combine_patch_code_changelist,
    combine_patch_data_branch,
    combine_patch_data_changelist,
    combine_settings_file,
    use_precreated_delta_bundles,
    use_precreated_combined_bundles,
    patch_type,
    use_head_bundles_as_base_bundles,
    standalone_disc_baseline,
    standalone_patch_baseline,
    virtual_branch_override,
    clean_steam_sdk,
    steam_drmwrap,
):
    """
    Deploy a playable build using Frosty.

    Examples:
        * elipy --location criterion patch_frosty win64 final --code-branch build-release
          --code-changelist 436395 --data-branch build-release --data-changelist 436395
          --disc-code-branch build-release --disc-code-changelist 371518
          --disc-data-branch build-release --disc-data-changelist 371518
          --data-directory Data --region ww  --first-patch
        * elipy --location criterion patch_frosty win64 final --code-branch build-release
          --code-changelist 436395 --data-branch build-release --data-changelist 436395
          --disc-code-branch build-release --disc-code-changelist 371518
          --disc-data-branch build-release --disc-data-changelist 371518  --data-directory Data
          --region ww  --patch-code-branch build-release --patch-code-changelist 417610
          --patch-data-branch build-release --patch-data-changelist 417610
        * elipy --location criterion patch_frosty win64 final --code-branch build-release
          --code-changelist 436395 --data-branch build-release --data-changelist 436395
          --disc-code-branch build-release --disc-code-changelist 371518
          --disc-data-branch build-release --disc-data-changelist 371518  --data-directory Data
          --region ww  --patch-code-branch build-release --patch-code-changelist 417610
          --patch-data-branch build-release --patch-data-changelist 417610 --use-win64trial
          --skip-streaming-install-package --email <EMAIL> --password "****"
        * elipy --location dice patch_frosty xbsx final --code-branch kin-release
          --code-changelist 17903931 --data-branch kin-release --data-changelist 5026337
          --disc-code-branch kin-release --disc-code-changelist 12409338
          --disc-data-branch kin-release --disc-data-changelist 4592495
          --data-directory kindata --region ww  --patch-code-branch kin-release
          --patch-code-changelist 17743749 --patch-data-branch kin-release
          --patch-data-changelist 5019321 --use-win64trial --licensee BattlefieldGame
          --email <EMAIL> --password "****" --use-recompression-cache --enable-eac
    """
    # Adding sentry tags
    add_sentry_tags(__file__)

    platform = platform.lower()
    # Clean up before running the job.
    running_processes.kill()
    icepick.IcepickUtils.clean_local_frosty()

    # Initialize
    _filer = filer.FilerUtils()
    packager = package.PackageUtils(platform, "digital", config, monkey_build_label=data_changelist)

    # Set data directory.
    data.DataUtils.set_datadir(data_directory)

    # Set licensee
    set_licensee(list(licensee), list())

    # Patch signtool
    patch_eainstaller_signtool(password=password, user=email, domain_user=domain_user)

    LOGGER.info("[DEBUG] first_patch: {}".format(first_patch))

    if first_patch:
        baseline_code_branch = disc_code_branch
        baseline_code_changelist = disc_code_changelist
        baseline_data_branch = disc_data_branch
        baseline_data_changelist = disc_data_changelist
        combine_baseline_code_branch = combine_disc_code_branch
        combine_baseline_code_changelist = combine_disc_code_changelist
        combine_baseline_data_branch = combine_disc_data_branch
        combine_baseline_data_changelist = combine_disc_data_changelist
    else:
        baseline_code_branch = patch_code_branch
        baseline_code_changelist = patch_code_changelist
        baseline_data_branch = patch_data_branch
        baseline_data_changelist = patch_data_changelist
        combine_baseline_code_branch = combine_patch_code_branch
        combine_baseline_code_changelist = combine_patch_code_changelist
        combine_baseline_data_branch = combine_patch_data_branch
        combine_baseline_data_changelist = combine_patch_data_changelist

    frosty_args = []

    deploy_package_type = "steam_" if steam_build else ""
    if use_combine_bundles:
        deploy_package_type += "patch_combine"
    else:
        deploy_package_type += "patch"

    # Pre-Check if the dest folder already exist in filer, abort job immediately
    if use_combine_bundles:
        dest_filer = filer_paths.get_frosty_build_path(
            data_branch=data_branch,
            data_changelist=data_changelist,
            code_branch=code_branch,
            code_changelist=code_branch,
            platform=platform,
            package_type=deploy_package_type,
            region=region,
            config=config,
            combine_data_branch=combine_data_branch,
            combine_data_changelist=combine_data_changelist,
            combine_code_branch=combine_code_branch,
            combine_code_changelist=combine_code_changelist,
        )
    else:
        dest_filer = filer_paths.get_frosty_build_path(
            data_branch=data_branch,
            data_changelist=data_changelist,
            code_branch=code_branch,
            code_changelist=code_branch,
            platform=platform,
            package_type=deploy_package_type,
            region=region,
            config=config,
        )
    if os.path.exists(dest_filer) and not dry_run:
        raise ELIPYException(
            "Attempting to deploy to a path that already exists.\
            Possibly because a previous build succeeded in deploying before failing.\
            This can cause us to lose builds and is not allowed."
        )

    install_required_sdks(password=password, user=email, domain_user=domain_user, platform=platform)

    code_platform = platform
    if platform == "win64":
        code_platform = "win64game"
    _filer.fetch_code(code_branch, code_changelist, code_platform, config)
    if use_combine_bundles:
        try:
            # Set a different local root for SP
            sp_local_root = os.path.join(local_paths.frostbite_core.get_tnt_root(), "SP_Local")
            fbenv_layer.set_local_root(sp_local_root)
            # Fetch code for SP
            _filer.fetch_code(combine_code_branch, combine_code_changelist, code_platform, config)
            # Set the SP local root as a frosty arg
            frosty_args.append("ADDITIONAL_LOCAL_ROOT={}".format(sp_local_root))
            # Reset the local root
            fbenv_layer.set_local_root(
                os.path.join(local_paths.frostbite_core.get_tnt_root(), "Local")
            )
        except Exception as exc:
            fbenv_layer.set_local_root(
                os.path.join(local_paths.frostbite_core.get_tnt_root(), "Local")
            )
            raise ELIPYException(
                "Encountered the following exception when attempting to fetch SP code:\
                {}\
                Local root has been reset.".format(
                    exc
                )
            )

    delta_bundles_location = os.path.join(frostbite_core.get_tnt_root(), "local", "current_delta")

    # Set up shared frosty arguments that apply to all platforms.
    frosty_args += [
        "USE_ALREADY_CREATED_SUPERBUNDLES=true",
        "SUPERBUNDLE_DELTA_DIR={0}".format(delta_bundles_location),
        "USE_ALREADYCREATED_SUPERBUNDLES=true",  # supporting older version of frosty
    ]
    # reminder: remove this crap https://gitlab.ea.com/dre-cobra/team/-/issues/564
    if frostbite_core.get_licensee_id().lower() in ["roboto"]:
        frosty_args.append("COBRA_ON_ROBOTO=True")

    if use_recompression_cache is True:
        recompression_cache = SETTINGS.get("recompression_cache")[platform]
        LOGGER.info(
            "Using Alternative Avalanche server for the \
                    recompression cache {}".format(
                recompression_cache
            )
        )
        frosty_args.append("RECOMPRESSION_CACHE_SERVER={0}".format(recompression_cache))

    if enable_eac is True and platform in ["win64"]:
        frosty_args.append("ANTICHEAT_ENABLED=True")

    if SETTINGS.get("elsa_patch") == "true":
        if frostbite_core.minimum_fb_version(year=2021, version_nr=1):
            frosty_args.append("DEPLOY_PATCH_TYPE=elsa")
        else:
            frosty_args.append("ELSA_PATCH=true")

    last_package_type = "patch"
    if use_combine_bundles and not standalone_patch_baseline:
        last_package_type = "patch_combine"
    if first_patch:
        last_package_type = "digital"
        if use_combine_bundles and not standalone_disc_baseline:
            last_package_type = "digital_combine"

    if platform in ["xb1", "xbsx"]:
        if not frostbite_core.minimum_fb_version(year=2022, season=1, version_nr=2):
            packager.copy_submissionvalidator()

    if platform == "xb1":
        # XB1 patches need the layout.xml file from the LIVE patch.
        xb1_layout_file_location = os.path.join(
            frostbite_core.get_tnt_root(), "local", "baselayout"
        )
        _filer.fetch_baseline_xb1_layout(
            data_branch=baseline_data_branch,
            data_changelist=baseline_data_changelist,
            code_branch=baseline_code_branch,
            code_changelist=baseline_code_changelist,
            package_type=last_package_type,
            config=config,
            region=region,
            destination=xb1_layout_file_location,
        )

        # Add XB1 frosty arguments.
        frosty_args += [
            "XB1_PUT_BUILD_LABEL_INTO_VERSION=True",
            "XB1_PREVIOUS_CHUNK_LAYOUT={0}".format(xb1_layout_file_location),
        ]

        # This is required for CUv3 (Content Update v3),
        # Note the XVC file copied to build machine here can be of significant size (20GB+)
        xb1_priorpackage_file_location = os.path.join(
            frostbite_core.get_tnt_root(), "local", "basepriorpackage"
        )

        xb1_priorpackage_file = _filer.fetch_baseline_xb_priorpackage(
            data_branch=baseline_data_branch,
            data_changelist=baseline_data_changelist,
            code_branch=baseline_code_branch,
            code_changelist=baseline_code_changelist,
            package_type=last_package_type,
            config=config,
            region=region,
            destination=xb1_priorpackage_file_location,
            platform=platform,
            combine_data_branch=combine_baseline_data_branch,
            combine_data_changelist=combine_baseline_data_changelist,
            combine_code_branch=combine_baseline_code_branch,
            combine_code_changelist=combine_baseline_code_changelist,
        )

        frosty_args.append("PRIORPACKAGE={0}".format(xb1_priorpackage_file))

        if config.lower() == "retail" and SETTINGS.get("skip_frosty_game_config_flags") != "true":
            frosty_args.append("XBONE_CLEAN_NETWORK_MANIFEST=True")
        if frostbite_core.minimum_fb_version(year=2021, version_nr=1):
            frosty_args.append("XB1_RUN_CUDIFF_FOR_PATCHES=True")

    if platform in ["xbsx"]:
        # Add XBSX frosty arguments.
        frosty_args += [
            "XBSX_PUT_BUILD_LABEL_INTO_VERSION=True",
        ]
        if SETTINGS.get("skip_frosty_game_config_flags") != "true":
            frosty_args.append("UPDCOMPAT=3")

        # This is required for CUv3 (Content Update v3),
        # Note the XVC file copied to build machine here can be of significant size (20GB+)
        xbsx_priorpackage_file_location = os.path.join(
            frostbite_core.get_tnt_root(), "local", "basepriorpackage"
        )

        xbsx_priorpackage_file = _filer.fetch_baseline_xb_priorpackage(
            data_branch=baseline_data_branch,
            data_changelist=baseline_data_changelist,
            code_branch=baseline_code_branch,
            code_changelist=baseline_code_changelist,
            package_type=last_package_type,
            config=config,
            region=region,
            destination=xbsx_priorpackage_file_location,
            platform=platform,
            combine_data_branch=combine_baseline_data_branch,
            combine_data_changelist=combine_baseline_data_changelist,
            combine_code_branch=combine_baseline_code_branch,
            combine_code_changelist=combine_baseline_code_changelist,
        )

        frosty_args.append("PRIORPACKAGE={0}".format(xbsx_priorpackage_file))

    # Copy BASEPACKAGE (build on game disc) from network share to build machine
    # https://jaas.ea.com/browse/COBRA-674
    if platform in ["xb1", "xbsx"]:
        xb_basepackage_required = SETTINGS.get(key="fetch_xb_basepackage", default=False)
        try:
            if xb_basepackage_required:
                xb_basepackage_file_location = os.path.join(
                    frostbite_core.get_tnt_root(), "local", "basepackage"
                )
                # Copy original disc build from network share to build machine
                xb_basepackage_file = _filer.fetch_baseline_xb_priorpackage(
                    data_branch=disc_data_branch,
                    data_changelist=disc_data_changelist,
                    code_branch=disc_code_branch,
                    code_changelist=disc_code_changelist,
                    package_type=last_package_type,
                    config=config,
                    region=region,
                    destination=xb_basepackage_file_location,
                    platform=platform,
                    combine_data_branch=combine_disc_data_branch,
                    combine_data_changelist=combine_disc_data_changelist,
                    combine_code_branch=combine_disc_code_branch,
                    combine_code_changelist=combine_disc_code_changelist,
                )
                frosty_args.append("BASEPACKAGE={0}".format(xb_basepackage_file))
        except Exception as exc:
            if xb_basepackage_required:
                LOGGER.warning("Unable to fetch {} basepackage: {}".format(platform, exc))

    if platform in ["ps4", "ps5"]:
        if use_combine_bundles and not standalone_disc_baseline:
            disc_package_type = "digital_combine"
        else:
            disk_baseline_build_path = filer_paths.get_baseline_build_path(
                disc_data_branch,
                disc_data_changelist,
                disc_code_branch,
                disc_code_changelist,
            )
            disc_package_type = packager.get_disc_package_type(disk_baseline_build_path)

    ps_baseline_config = "retail"
    if same_baseline_config:
        ps_baseline_config = config

    if platform == "ps4":
        # PS4 patch packages need the .pkg that got shipped on disk, and the one released most
        # recently.
        ps4_disk_package_location = os.path.join(
            frostbite_core.get_tnt_root(), "local", "ps4_disk_package"
        )
        ps4_live_package_location = os.path.join(
            frostbite_core.get_tnt_root(), "local", "ps4_live_package"
        )
        baseline_path = filer_paths.get_baseline_build_path(
            baseline_data_branch,
            baseline_data_changelist,
            baseline_code_branch,
            baseline_code_changelist,
        )
        appversion = packager.fetch_appversion(
            baseline_path,
            region=region,
            first_patch=first_patch,
            inc_version=increase_version_by,
            config=ps_baseline_config,
        )
        frosty_args += [
            "PS4_PKG_BASE_DIR={0}".format(ps4_disk_package_location),
            "PS4_APP_VERSION_MINOR={0}".format(appversion),
        ]
        if not first_patch:
            if frostbite_core.get_licensee_id().lower() == "casablanca":
                frosty_args.append("PS4_PREV_PATCH_DIR={0}".format(ps4_live_package_location))
            else:
                frosty_args.append("CURRENT_LIVE_PATCH_DIR={0}".format(ps4_live_package_location))
        if config.lower() == "retail" and skip_streaming_install_package is False:
            if SETTINGS.get("skip_frosty_game_config_flags") != "true":
                frosty_args.append("STREAMING_INSTALL_CREATE_SUBMISSION_PACKAGES=True")

        # Fetch DISK ps4 package.
        _filer.fetch_baseline_ps_package(
            data_branch=disc_data_branch,
            data_changelist=disc_data_changelist,
            code_branch=disc_code_branch,
            code_changelist=disc_code_changelist,
            package_type=disc_package_type,
            config=ps_baseline_config,
            region=region,
            destination=ps4_disk_package_location,
            platform=platform,
            combine_data_branch=combine_disc_data_branch,
            combine_data_changelist=combine_disc_data_changelist,
            combine_code_branch=combine_disc_code_branch,
            combine_code_changelist=combine_disc_code_changelist,
        )

        if not first_patch:
            # Fetch CURRENT LIVE ps4 package.
            _filer.fetch_baseline_ps_package(
                data_branch=patch_data_branch,
                data_changelist=patch_data_changelist,
                code_branch=patch_code_branch,
                code_changelist=patch_code_changelist,
                package_type=last_package_type,
                config=ps_baseline_config,
                region=region,
                destination=ps4_live_package_location,
                platform=platform,
                combine_data_branch=combine_patch_data_branch,
                combine_data_changelist=combine_patch_data_changelist,
                combine_code_branch=combine_patch_code_branch,
                combine_code_changelist=combine_patch_code_changelist,
            )

    if platform == "ps5":
        # For PS5 first patch requires the .pkg that got shipped on disk, any following
        # patch on patches only require the one released most recently.
        ps5_disk_package_location = os.path.join(
            frostbite_core.get_tnt_root(), "local", "ps5_disk_package"
        )
        ps5_live_package_location = os.path.join(
            frostbite_core.get_tnt_root(), "local", "ps5_live_package"
        )

        baseline_path = filer_paths.get_baseline_build_path(
            baseline_data_branch,
            baseline_data_changelist,
            baseline_code_branch,
            baseline_code_changelist,
        )

        contentversion = packager.fetch_contversion(
            baseline_path,
            region=region,
            first_patch=first_patch,
            inc_version=increase_version_by,
            config=ps_baseline_config,
            use_combine_bundles=use_combine_bundles,
            standalone_disc_baseline=standalone_disc_baseline,
            standalone_patch_baseline=standalone_patch_baseline,
            combine_data_branch=combine_baseline_data_branch,
            combine_data_changelist=combine_baseline_data_changelist,
            combine_code_branch=combine_baseline_code_branch,
            combine_code_changelist=combine_baseline_code_changelist,
        )

        if first_patch:
            # Fetch DISK ps5 package.
            _filer.fetch_baseline_ps_package(
                data_branch=disc_data_branch,
                data_changelist=disc_data_changelist,
                code_branch=disc_code_branch,
                code_changelist=disc_code_changelist,
                package_type=disc_package_type,
                config=ps_baseline_config,
                region=region,
                destination=ps5_disk_package_location,
                platform=platform,
                combine_data_branch=combine_disc_data_branch,
                combine_data_changelist=combine_disc_data_changelist,
                combine_code_branch=combine_disc_code_branch,
                combine_code_changelist=combine_disc_code_changelist,
            )
            ps5_base_package_location = ps5_disk_package_location
        else:
            # Fetch CURRENT LIVE ps5 package.
            _filer.fetch_baseline_ps_package(
                data_branch=patch_data_branch,
                data_changelist=patch_data_changelist,
                code_branch=patch_code_branch,
                code_changelist=patch_code_changelist,
                package_type=last_package_type,
                config=ps_baseline_config,
                region=region,
                destination=ps5_live_package_location,
                platform=platform,
                combine_data_branch=combine_patch_data_branch,
                combine_data_changelist=combine_patch_data_changelist,
                combine_code_branch=combine_patch_code_branch,
                combine_code_changelist=combine_patch_code_changelist,
            )
            ps5_base_package_location = ps5_live_package_location

        frosty_args += [
            "PS5_PKG_BASE_DIR={0}".format(ps5_base_package_location),
            "PS5_CONTENT_VERSION={0}".format(contentversion),
        ]

    if platform in ["win64", "win64game"]:
        win64_base_dir = os.path.join(frostbite_core.get_tnt_root(), "local", "win64_base")
        win64_base_live = os.path.join(frostbite_core.get_tnt_root(), "local", "win64_live")

        _filer.fetch_baseline_win64_chunkmanifest(
            data_branch=baseline_data_branch,
            data_changelist=baseline_data_changelist,
            code_branch=baseline_code_branch,
            code_changelist=baseline_code_changelist,
            package_type=last_package_type,
            config=config,
            region=region,
            destination=win64_base_dir,
            combine_data_branch=combine_baseline_data_branch,
            combine_data_changelist=combine_baseline_data_changelist,
            combine_code_branch=combine_baseline_code_branch,
            combine_code_changelist=combine_baseline_code_changelist,
        )

        frosty_args += [
            "IS_TRIAL_BUILD={}".format(use_win64trial),
            "USE_DENUVO={}".format(False),
            "PC_PUT_BUILD_LABEL_INTO_VERSION=True",
            f"WIN64_DIGITAL_BASE_DIR={win64_base_dir}",
        ]

        # set the oreans executable postfix
        if config.lower() == "retail" and use_oreans:
            frosty_args.append("EXECUTABLE_POSTFIX=" + OREANS_OUTPUT_SUFFIX)

        if not first_patch:
            _filer.fetch_baseline_win64_chunkmanifest(
                data_branch=baseline_data_branch,
                data_changelist=baseline_data_changelist,
                code_branch=baseline_code_branch,
                code_changelist=baseline_code_changelist,
                package_type=last_package_type,
                config=config,
                region=region,
                destination=win64_base_live,
                combine_data_branch=combine_baseline_data_branch,
                combine_data_changelist=combine_baseline_data_changelist,
                combine_code_branch=combine_baseline_code_branch,
                combine_code_changelist=combine_baseline_code_changelist,
            )
            frosty_args += [f"CURRENT_LIVE_PATCH_DIR={win64_base_live}"]

        if use_win64trial:
            _filer.fetch_code(code_branch, code_changelist, "win64trial", config, purge=False)
        if steam_build:
            frosty_args.extend(
                [
                    "WIN32_DIGITAL_PLATFORM=Steam",
                    f'STEAM_BUILD_DESCRIPTION="{config} build from {data_branch} '
                    f'on CL {data_changelist}"',
                ]
            )
            if use_combine_bundles:
                steam_sdk_root = SteamUtils.download_steam_sdk(clean_steam_sdk)
                # code and data branch should be identical as of July 2025
                steam_config_name = SteamUtils.get_ess_steam_config_name(
                    data_branch, config, region, "frosty"
                )
                steam_account = SteamUtils.prepare_steam_user_session(
                    steam_config_name, steam_sdk_root
                )
                frosty_args.extend(
                    [
                        f"STEAM_DRMWRAP={'True' if steam_drmwrap else 'False'}",
                        f"STEAM_USERID={steam_account}",
                        "STEAM_DRM_LOCAL=True",
                    ]
                )

    # Get secrets needed for packaging
    secrets.remove_old_frosty_cert()
    secrets.get_secrets({"build_type": "frosty", "platform": platform})

    # All platforms require the delta bundles from the CURRENT patch data builds.
    if not use_combine_bundles:
        _filer.fetch_delta_bundles(
            code_branch=code_branch,
            code_changelist=code_changelist,
            data_branch=data_branch,
            data_changelist=data_changelist,
            platform=platform,
            dest=os.path.join(delta_bundles_location),
        )
    else:
        # Handle combined bundles workflow
        if use_precreated_combined_bundles:
            # Use pre-created combined bundles from separate job
            LOGGER.info("Using pre-created combined bundles from network share")
            # Combined bundles are fetched using the combine_output directory name
            headbundles_combine = os.path.split(
                local_paths.get_local_bundles_path(deployed_bundles_dir_name="head_bundles_combine")
            )[0]
            _filer.fetch_combined_bundles(
                code_branch=code_branch,
                code_changelist=code_changelist,
                data_branch=data_branch,
                data_changelist=data_changelist,
                combine_code_branch=combine_code_branch,
                combine_code_changelist=combine_code_changelist,
                combine_data_branch=combine_data_branch,
                combine_data_changelist=combine_data_changelist,
                platform=platform,
                dest=headbundles_combine,
                bundles_dir_name="combine_output",
            )
        else:
            # Create combined bundles locally (existing behavior)
            # Fetch bundles set 1.
            bundles_location_main = os.path.split(
                local_paths.get_local_bundles_path(
                    deployed_bundles_dir_name="deployed_bundles_main"
                )
            )[0]
            _filer.fetch_head_bundles(
                code_branch=code_branch,
                code_changelist=code_changelist,
                data_branch=data_branch,
                data_changelist=data_changelist,
                platform=platform,
                dest=bundles_location_main,
                bundles_dir_name="combine_bundles",
            )
            # Fetch bundles set 2.
            bundles_location_combine = os.path.split(
                local_paths.get_local_bundles_path(
                    deployed_bundles_dir_name="deployed_bundles_combine"
                )
            )[0]
            _filer.fetch_head_bundles(
                code_branch=combine_code_branch,
                code_changelist=combine_code_changelist,
                data_branch=combine_data_branch,
                data_changelist=combine_data_changelist,
                platform=platform,
                dest=bundles_location_combine,
                bundles_dir_name="combine_bundles",
            )
            # Combine the two sets of bundles.
            headbundles_combine = os.path.split(
                local_paths.get_local_bundles_path(deployed_bundles_dir_name="head_bundles_combine")
            )[0]
            extra_combine_args = ["-s"]
            if combine_settings_file:
                extra_combine_args.append(combine_settings_file)
            else:
                if platform == "xbsx":
                    extra_combine_args.append("project-combine-hres-smart-delivery.yaml")
                else:
                    extra_combine_args.append("project-combine-hres.yaml")
            avalanche.combine(
                input_dir_1=bundles_location_main,
                input_dir_2=bundles_location_combine,
                output_dir=headbundles_combine,
                extra_combine_args=extra_combine_args,
            )
            if not dry_run:
                # Deploy avalanche combine output for safekeeping
                _filer.deploy_avalanche_combine_output(
                    source=headbundles_combine,
                    data_branch=data_branch,
                    data_changelist=data_changelist,
                    code_branch=code_branch,
                    code_changelist=code_changelist,
                    platform=platform,
                )

        # Handle delta bundles creation for combined builds
        if use_precreated_delta_bundles:
            # Use pre-created delta bundles from separate job
            LOGGER.info("Using pre-created delta bundles from network share")
            _filer.fetch_precreated_delta_bundles(
                code_branch=code_branch,
                code_changelist=code_changelist,
                data_branch=data_branch,
                data_changelist=data_changelist,
                platform=platform,
                dest=delta_bundles_location,
                combine_code_branch=combine_code_branch,
                combine_code_changelist=combine_code_changelist,
                combine_data_branch=combine_data_branch,
                combine_data_changelist=combine_data_changelist,
            )
        else:
            # Create delta bundles locally (existing behavior)
            # Get bundles from a baseline build.
            baseline_bundles = os.path.split(
                local_paths.get_local_bundles_path(deployed_bundles_dir_name="baseline_bundles")
            )[0]
            baseline_bundles_dir_name = "combine_bundles"
            if first_patch or patch_type == "from_disk":
                # For first patch, use head
                if standalone_disc_baseline:
                    baseline_bundles_dir_name = "bundles"
                _filer.fetch_baseline_bundles_head(
                    data_branch=disc_data_branch,
                    data_changelist=disc_data_changelist,
                    code_branch=disc_code_branch,
                    code_changelist=disc_code_changelist,
                    platform=platform,
                    dest=baseline_bundles,
                    bundles_dir_name=baseline_bundles_dir_name,
                )
            else:
                # Fetch base bundles from the network share
                if standalone_patch_baseline:
                    baseline_bundles_dir_name = "bundles"
                _filer.fetch_baseline_bundles(
                    data_branch=patch_data_branch,
                    data_changelist=patch_data_changelist,
                    code_branch=patch_code_branch,
                    code_changelist=patch_code_changelist,
                    platform=platform,
                    dest=baseline_bundles,
                    bundles_dir_name=baseline_bundles_dir_name,
                )

            LOGGER.info("Patching using AvalancheCLI")
            if use_head_bundles_as_base_bundles:
                # If the baseline for some reason is broken, we can try
                # and use the current headbundles also as basebundles.
                # This should only be tried when requested by the game team.
                baseline_bundles = headbundles_combine
            avalanche.ddelta(headbundles_combine, baseline_bundles, delta_bundles_location)

            # Deploy delta bundles for combined builds
            if not dry_run:
                _filer.deploy_delta_bundles(
                    source=delta_bundles_location,
                    data_branch=data_branch,
                    data_changelist=data_changelist,
                    code_branch=code_branch,
                    code_changelist=code_changelist,
                    platform=platform,
                    bundles_dir_name="combine_bundles",
                    combine_build_delta=use_combine_bundles,
                )

    if virtual_branch_override:
        frosty_args.append("DATABASE_ID={}".format(os.environ.get("FB_DEFAULT_PLATFORM_DB")))
        frosty_args.append("FB_BRANCH_ID={}".format(data_branch))

    try:
        packager.frosty(region=region, frosty_args=frosty_args)
    except Exception as exception:
        if not dry_run:
            output_path = local_paths.get_local_frosty_path()
            for file in os.listdir(output_path):
                if file.endswith(".pkg.verify.log"):
                    output_file = os.path.join(output_path, file)
                    core.robocopy(
                        output_file,
                        dest_filer,
                        extra_args=[os.path.basename(output_file)],
                    )
                    LOGGER.info("{} is copied to {}".format(output_file, dest_filer))
                    break
        raise exception
    add_frosty_log_to_output()

    if not dry_run:
        # Deploy packaged build
        if use_combine_bundles:
            _filer.deploy_frosty_build(
                data_changelist=data_changelist,
                data_branch=data_branch,
                code_changelist=code_changelist,
                code_branch=code_branch,
                package_type=deploy_package_type,
                config=config,
                region=region,
                dataset=data_directory,
                platform=platform,
                combine_code_changelist=combine_code_changelist,
                combine_code_branch=combine_code_branch,
                combine_data_changelist=combine_data_changelist,
                combine_data_branch=combine_data_branch,
            )
        else:
            _filer.deploy_frosty_build(
                data_changelist=data_changelist,
                data_branch=data_branch,
                code_changelist=code_changelist,
                code_branch=code_branch,
                package_type=deploy_package_type,
                config=config,
                region=region,
                dataset=data_directory,
                platform=platform,
            )

        # Upload renamed binary to symstore
        if "win64" in platform and not disable_frosty_symbol_upload:
            _symbols = symbols.SymbolsUtils()
            _symbols.upload_game_binary(
                platform=platform,
                config=config,
                package_type="digital",
                data_changelist=data_changelist,
                code_changelist=code_changelist,
                data_branch=data_branch,
                code_branch=code_branch,
            )
            if use_win64trial:
                _symbols.upload_game_binary(
                    platform="win64trial",
                    config=config,
                    package_type="digital",
                    data_changelist=data_changelist,
                    code_changelist=code_changelist,
                    data_branch=data_branch,
                    code_branch=code_branch,
                )
