{#
    Command:
        quickscope_import
            short_help: import latest buid stats to quickscope

    Arguments:

    Required variables:

    Optional variables:
        quickscope_db
            default: None
            help: db name used to store stats.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    quickscope_import
    {%- if quickscope_db %}
    --quickscope-db {{ quickscope_db }}
    {%- endif %}
  displayName: elipy quickscope_import
