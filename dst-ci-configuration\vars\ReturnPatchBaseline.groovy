/**
 * ReturnPatchBaseline.groovy
 * Returns a baseline with branch and changelist for code and data, when given a project name, branch name and platform.
 */
List call(def baseline_file, def branch_name, def platform, Boolean use_combined_baseline = false) {
    def baseline = baseline_file.get_patch_baseline_for(branch_name, platform)

    def platform_args = [
        string(name: 'patch_code_branch', value: baseline.code_branch),
        string(name: 'patch_data_branch', value: baseline.data_branch),
        string(name: 'patch_code_changelist', value: baseline.code_changelist),
        string(name: 'patch_data_changelist', value: baseline.data_changelist),
    ]

    if (use_combined_baseline && (baseline.combine_code_branch?.trim() || baseline.combine_data_branch?.trim() || baseline.combine_code_changelist?.trim() || baseline.combine_data_changelist?.trim())) {
        platform_args += [
            string(name: 'combine_patch_code_branch', value: baseline.combine_code_branch),
            string(name: 'combine_patch_data_branch', value: baseline.combine_data_branch),
            string(name: 'combine_patch_code_changelist', value: baseline.combine_code_changelist),
            string(name: 'combine_patch_data_changelist', value: baseline.combine_data_changelist),
        ]
    }

    return platform_args
}
