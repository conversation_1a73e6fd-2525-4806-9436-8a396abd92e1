package scripts.schedulers.all

import com.ea.lib.BuildSelector
import com.ea.lib.LibCommonCps
import com.ea.lib.model.JobReference
import com.ea.lib.model.autotest.Name
import com.ea.matrixfiles.AutotestMatrixFactory
import com.ea.project.GetBranchFile
import hudson.AbortException
import hudson.model.Result

def project = ProjectClass(env.projectName)
def autotestMatrix = AutotestMatrixFactory.getInstance(env.autotestMatrix)
Map slackSettings = autotestMatrix.getSlackSettings(env.branchName)

def finalResult = Result.SUCCESS

final String BILBO_RESULT_FAILED = 'failed'
final String BILBO_RESULT_DONE = 'done'

/**
 * autotest_scheduler.groovy
 */
pipeline {
    agent { label '( scheduler && master ) || executor_agent' }
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Validate test category') {
            steps {
                script {
                    try {
                        echo 'Checking if the job has been orphaned...'
                        autotestMatrix.getTestCategory(env.testCategory, env.branchName)
                        echo 'Configuration for test category has been found. The job is not orphaned.'
                    } catch (IllegalArgumentException e) {
                        LibCommonCps.handleExceptionWithResult(this, e, Result.FAILURE, 'The job has been orphaned. Exiting...')
                        finalResult = Result.FAILURE
                    } catch (Exception e) {
                        LibCommonCps.handleExceptionWithResult(this, e)
                        finalResult = Result.FAILURE
                    }
                }
            }
        }
        stage('Determine changelists') {
            steps {
                script {
                    try {
                        echo 'Determine changelists'
                        BuildSelector buildSelector = new BuildSelector(this, currentBuild, env, params)
                        finalResult = buildSelector.trigger(autotestMatrix, env.branchName, env.BUILD_URL)
                    } catch (AbortException e) {
                        LibCommonCps.handleExceptionWithResult(this, e, Result.ABORTED)
                        finalResult = Result.ABORTED
                    } catch (Exception e) {
                        LibCommonCps.handleExceptionWithResult(this, e)
                        finalResult = Result.FAILURE
                    }
                }
            }
        }
        stage('Trigger autotest jobs') {
            when {
                expression {
                    return finalResult != Result.FAILURE
                }
            }
            steps {
                script {
                    try {
                        List<JobReference> jobReferences = []
                        def testCategory = autotestMatrix.getTestCategory(env.testCategory, env.branchName)
                        finalResult = finalResult.combine(retryOnFailureCause(testCategory.testInfo.disableAutomaticRetry ? 0 : 3, jobReferences) {
                            echo 'Trigger Autotest jobs'
                            Map targetBuildInfo = (Map) Eval.me(env.target_build_info)
                            AutotestRunCategory(testCategory, env.branchName, env.autotestMatrix, targetBuildInfo, jobReferences)
                        })
                    } catch (AbortException e) {
                        LibCommonCps.handleExceptionWithResult(this, e, Result.ABORTED, null, false)
                        finalResult = Result.ABORTED
                    } catch (Exception e) {
                        LibCommonCps.handleExceptionWithResult(this, e, Result.FAILURE, null, false)
                        finalResult = Result.FAILURE
                    }
                }
            }
        }

        stage('Trigger LKG counters') {
            when {
                expression {
                    Map changelists = (Map) Eval.me(env.target_build_info)
                    return finalResult == Result.SUCCESS && env.lkg_p4_counters_enabled.toBoolean() && changelists[Name.ANY.toString()]
                }
            }
            steps {
                script {
                    try {
                        List<JobReference> jobReferences = []
                        finalResult = finalResult.combine(retryOnFailureCause(3, jobReferences) {
                            echo 'Trigger LKG counters'
                            Map changelists = (Map) Eval.me(env.target_build_info)
                            def branchFile = GetBranchFile.get_branchfile(env.projectName, env.branchName)
                            def counter_args = [
                                string(name: 'code_countername', value: LibCommonCps.getp4Countername(env.branchName, "lkg.${env.testCategory}", branchFile, 'code')),
                                string(name: 'code_changelist', value: changelists[Name.ANY.toString()].codeChangelist),
                                string(name: 'data_countername', value: LibCommonCps.getp4Countername(env.branchName, "lkg.${env.testCategory}", branchFile, 'data')),
                                string(name: 'data_changelist', value: changelists[Name.ANY.toString()].dataChangelist),
                            ]
                            String jobName = "${env.branchName}.${env.testCategory}.p4counterupdater"
                            def downstreamJob = build(job: jobName, parameters: counter_args, propagate: false)
                            jobReferences << new JobReference(downstreamJob: downstreamJob, jobName: jobName, parameters: counter_args, propagate: false)
                        })
                    } catch (AbortException e) {
                        LibCommonCps.handleExceptionWithResult(this, e, Result.ABORTED, null, false)
                        finalResult = Result.ABORTED
                    } catch (Exception e) {
                        LibCommonCps.handleExceptionWithResult(this, e, Result.FAILURE, null, false)
                        finalResult = Result.FAILURE
                    }
                }
            }
        }

        stage('Trigger register verified_for_preflight') {
            when {
                expression {
                    Map changelists = (Map) Eval.me(env.target_build_info)
                    def testCategory = autotestMatrix.getTestCategory(env.testCategory, env.branchName)
                    return finalResult == Result.SUCCESS && testCategory.registerVerifiedForPreflight && changelists[Name.ANY.toString()]
                }
            }
            steps {
                script {
                    try {
                        List<JobReference> jobReferences = []
                        finalResult = finalResult.combine(retryOnFailureCause(3, jobReferences) {
                            echo 'Trigger register verified_for_preflight'
                            Map changelists = (Map) Eval.me(env.target_build_info)
                            def counter_args = [
                                string(name: 'changelist', value: changelists[Name.ANY.toString()].codeChangelist),
                            ]
                            String jobName = "${env.branchName}.${env.testCategory}.register.verifiedForPreflight"
                            def downstreamJob = build(job: jobName, parameters: counter_args, propagate: false)
                            jobReferences << new JobReference(downstreamJob: downstreamJob, jobName: jobName, parameters: counter_args, propagate: false)
                        })
                    } catch (AbortException e) {
                        LibCommonCps.handleExceptionWithResult(this, e, Result.ABORTED, null, false)
                        finalResult = Result.ABORTED
                    } catch (Exception e) {
                        LibCommonCps.handleExceptionWithResult(this, e, Result.FAILURE, null, false)
                        finalResult = Result.FAILURE
                    }
                }
            }
        }

        stage('Trigger LKG job for integrations') {
            when {
                expression {
                    Map changelists = (Map) Eval.me(env.target_build_info)
                    return finalResult == Result.SUCCESS && env.set_integration_info.toBoolean() && changelists[Name.ANY.toString()]
                }
            }
            steps {
                script {
                    try {
                        List<JobReference> jobReferences = []
                        finalResult = finalResult.combine(retryOnFailureCause(3, jobReferences) {
                            echo 'Trigger LKG job for integrations'
                            Map changelists = (Map) Eval.me(env.target_build_info)
                            def parameters = [string(name: 'code_changelist', value: changelists[Name.ANY.toString()].codeChangelist)]
                            String jobName = "${env.branchName}.autotest-to-integration.code"
                            def downstreamJob = build(job: jobName, parameters: parameters, propagate: false)
                            jobReferences << new JobReference(downstreamJob: downstreamJob, jobName: jobName, parameters: parameters, propagate: false)
                        })
                    } catch (AbortException e) {
                        LibCommonCps.handleExceptionWithResult(this, e, Result.ABORTED, null, false)
                        finalResult = Result.ABORTED
                    } catch (Exception e) {
                        LibCommonCps.handleExceptionWithResult(this, e, Result.FAILURE, null, false)
                        finalResult = Result.FAILURE
                    }
                }
            }
        }

        stage('Trigger downstream autotests') {
            when {
                expression {
                    Map changelists = (Map) Eval.me(env.target_build_info)
                    def testCategory = autotestMatrix.getTestCategory(env.testCategory, env.branchName)
                    return finalResult == Result.SUCCESS && testCategory.downstreamAutotestCategories && changelists[Name.ANY.toString()]
                }
            }
            steps {
                script {
                    try {
                        echo 'Trigger downstream autotest categories'
                        def testCategory = autotestMatrix.getTestCategory(env.testCategory, env.branchName)
                        Map changelists = (Map) Eval.me(env.target_build_info)
                        def parameters = [
                            string(name: 'code_changelist', value: changelists[Name.ANY.toString()].codeChangelist),
                            string(name: 'data_changelist', value: changelists[Name.ANY.toString()].dataChangelist),
                            string(name: 'client_build_id', value: changelists[Name.ANY.toString()].clientBuildId),
                            string(name: 'server_build_id', value: changelists[Name.ANY.toString()].serverBuildId),
                        ]
                        for (autotestCategory in testCategory.downstreamAutotestCategories) {
                            String jobName = "${env.branchName}.autotest.${autotestCategory}.all.start"
                            build(job: jobName, parameters: parameters, propagate: false, wait: false)
                        }
                    } catch (AbortException e) {
                        LibCommonCps.handleExceptionWithResult(this, e, Result.ABORTED, null, false)
                        finalResult = Result.ABORTED
                    } catch (Exception e) {
                        LibCommonCps.handleExceptionWithResult(this, e, Result.FAILURE, null, false)
                        finalResult = Result.FAILURE
                    }
                }
            }
        }
    }
    post {
        cleanup {
            script {
                try {
                    List<JobReference> jobReferences = []
                    finalResult = finalResult.combine(retryOnFailureCause(3, jobReferences) {
                        Map changelists = env.target_build_info ? (Map) Eval.me(env.target_build_info) : [:]
                        if (changelists) {
                            def testCategory = autotestMatrix.getTestCategory(env.testCategory, env.branchName)
                            String buildSelectorResult = finalResult == Result.SUCCESS ? BILBO_RESULT_DONE : BILBO_RESULT_FAILED
                            boolean registerSmoke = testCategory.registerSmoke && buildSelectorResult == BILBO_RESULT_DONE
                            AutotestRegisterBilboResult(
                                testCategory.runBilbo,
                                registerSmoke,
                                buildSelectorResult,
                                env.branchName,
                                env.dataset,
                                changelists,
                                testCategory.testDefinition,
                                finalResult,
                                jobReferences)
                        }
                    })
                    AutotestSetPipelineResult(currentBuild, finalResult)
                    def testCategory = autotestMatrix.getTestCategory(env.testCategory, env.branchName)
                    // If no slack setting on testInfo level, fall back to use branch level slack
                    def test_slackSettings = testCategory.testInfo.slackChannel ?: testCategory.slackChannel ?: slackSettings
                    SlackMessageNew(currentBuild, test_slackSettings, project.short_name)
                } catch (Exception e) {
                    LibCommonCps.handleExceptionWithResult(this, e)
                }
            }
        }
    }
}

