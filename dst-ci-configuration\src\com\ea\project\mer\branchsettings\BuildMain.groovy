package com.ea.project.mer.branchsettings

import com.ea.lib.jobs.LibCustomScript
import com.ea.lib.jobsettings.ShiftSettings
import com.ea.lib.model.branchsettings.CustomScriptConfiguration
import com.ea.lib.model.branchsettings.UnitTestsConfiguration
import com.ea.project.mer.Merlin

class BuildMain {
    // Settings for jobs
    static Class project = Merlin
    static Map general_settings = [
        dataset           : project.dataset,
        elipy_install_call: project.elipy_install_call,
        elipy_call        : project.elipy_call,
        workspace_root    : project.workspace_root,
        unittests         : new UnitTestsConfiguration(
            label: 'build-main && unittests',
            nonVirtualCodeBranch: 'main',
        ),
        custom_script     : [
            (LibCustomScript.GENERATE_SYNC_BUILD_CACHE.jobName): new CustomScriptConfiguration(
                scriptPath: LibCustomScript.GENERATE_SYNC_BUILD_CACHE.scriptPath,
                executable: LibCustomScript.GENERATE_SYNC_BUILD_CACHE.executable,
                executableArgs: LibCustomScript.GENERATE_SYNC_BUILD_CACHE.executableArgs,
                label: 'build-main && generate-sync-build-cache',
                argumentDescription: 'Optional AutoBuildPath',
                jobName: LibCustomScript.GENERATE_SYNC_BUILD_CACHE.jobName,
            ),
        ],
    ]
    static Map standard_jobs_settings = [
        asset                             : 'DevelopmentLevels',
        avalanche_compress_bundles        : true,
        clean_master_version_check        : true,
        dataset_in_path                   : true,
        extra_code_args                   : ['--framework-args -G:package.SnowCache.log=true'],
        frosty_asset                      : 'DevelopmentLevels',
        frosty_reference_job              : 'build-main.code.start',
        frosty_server_asset               : 'DevelopmentLevels',
        job_label_statebuild              : 'build-main && util',
        server_asset                      : 'DevelopmentLevels',
        navmesh                           : true,
        navmesh_asset                     : 'levels\\L_MER_MainLevel\\L_MER_MainLevel_racePathfinding',
        navmesh_label                     : 'build-main && util',
        offsite_basic_drone_zip_builds    : true,
        skip_code_build_if_no_changes     : false,
        shift_branch                      : true,
        override_snowcache                : 'upload',
        use_snowcache                     : true,
        trigger_string_shift_offsite_drone: 'TZ=Europe/London \n H * * * 1-6\nH 6-23 * * 7',
        trigger_string_shift              : 'TZ=Europe/London \n H 01,12,18 * * 1-5',
        trigger_string_navmesh            : 'TZ=Europe/London \n H 07,10,13,16,19,22 * * 1-6\nH 6-23 * * 7',
        statebuild_code                   : false,
        statebuild_code_nomaster          : false,
        statebuild_data                   : false,
        statebuild_frosty                 : false,
    ]
    static Map preflight_settings = [:]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'win64game', configs: ['final', 'release', 'performance', 'retail']],
        [name: 'ps5', configs: ['final', 'release', 'performance', 'retail']],
        [name: 'xbsx', configs: ['final', 'release', 'performance', 'retail']],
        [name: 'win64server', configs: ['final', 'release']],
        [name: 'linux64server', configs: ['final', 'release']],
        [name: 'tool', configs: ['release']],
    ]
    static List code_nomaster_matrix = [
        [name: 'win64game', configs: ['release']],
        [name: 'ps5', configs: ['final']],
        [name: 'xbsx', configs: ['retail']],
        [name: 'linux64server', configs: ['final']],
    ]
    static List code_downstream_matrix = [
        [name: '.data.start', args: []],
        [name: '.frosty.start', args: []],
        [name: '.custom-script.generate-sync-build-cache', args: ['code_changelist']],
    ]
    static List data_matrix = [
        [name: 'win64'],
        [name: 'ps5'],
        [name: 'xbsx'],
        [name: 'server'],
    ]
    static List data_downstream_matrix = []
    static List patchdata_matrix = []
    static List frosty_matrix = [
        [name: 'win64', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release --additional-configs retail --additional-configs performance'], [format: 'digital', config: 'final', region: 'ww', args: '']]],
        [name: 'ps5', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release --additional-configs retail --additional-configs performance'], [format: 'digital', config: 'final', region: 'ww', args: '']]],
        [name: 'xbsx', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release --additional-configs retail --additional-configs performance'], [format: 'digital', config: 'final', region: 'ww', args: '']]],
        [name: 'server', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release ']]],
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww', args: '']]],
    ]
    static List frosty_downstream_matrix = [
        [name: '.spin.linuxserver.digital.final.ww', args: ['code_changelist', 'data_changelist']],
    ]
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww']]],
    ]
    static List shift_upload_matrix = [
        [shifter_type: ShiftSettings.SHIFTER_TYPE_OFFSITE_BASIC_DRONE, args: ['code_changelist']],
    ]
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = [
        [upstream_job: '.bilbo.register-Data-dronebuild', downstream_job: ".shift.${ShiftSettings.SHIFTER_TYPE_OFFSITE_BASIC_DRONE}.start", args: ['code_changelist']],
    ]
    static List azure_uploads_matrix = []
}
