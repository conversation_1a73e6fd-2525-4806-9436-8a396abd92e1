{#
    Command:
        clean_agent
            short_help: Clean resources on the current machine

    Arguments:

    Required variables:

    Optional variables:
        delete_tnt_local
            default: False
            help: Delete the `d:\dev\tnt\local` directory
            type: click.BOOL
        delete_packages
            default: False
            help: Delete the `d:\packages` directory
            type: click.BOOL
        delete_packagesdev
            default: False
            help: Delete the `d:\packagesdev` directory
            type: click.BOOL
        delete_pip_cache
            default: False
            help: Delete the `d:\.pip-cache` directory
            type: click.BOOL
        delete_logs
            default: False
            help: Delete the `d:\dev\logs` directory
            type: click.BOOL
        delete_localpackages
            default: False
            help: Delete the `d:\dev\tnt\LocalPackages` directory
            type: click.BOOL
        delete_data_state
            default: False
            help: Delete %GAME_DATA_DIR%\.state directory
            type: click.BOOL
        delete_temp
            default: False
            help: Delete the `%temp%` directory
            type: click.BOOL
        data_dir
            default: ''
            help: Delete %GAME_DATA_DIR%\.state directory
            type: str
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    clean_agent
    {%- if delete_tnt_local %}
    --delete-tnt-local {{ delete_tnt_local }}
    {%- endif %}
    {%- if delete_packages %}
    --delete-packages {{ delete_packages }}
    {%- endif %}
    {%- if delete_packagesdev %}
    --delete-packagesdev {{ delete_packagesdev }}
    {%- endif %}
    {%- if delete_pip_cache %}
    --delete-pip-cache {{ delete_pip_cache }}
    {%- endif %}
    {%- if delete_logs %}
    --delete-logs {{ delete_logs }}
    {%- endif %}
    {%- if delete_localpackages %}
    --delete-localpackages {{ delete_localpackages }}
    {%- endif %}
    {%- if delete_data_state %}
    --delete-data-state {{ delete_data_state }}
    {%- endif %}
    {%- if delete_temp %}
    --delete-temp {{ delete_temp }}
    {%- endif %}
    {%- if data_dir %}
    --data-dir {{ data_dir }}
    {%- endif %}
  displayName: elipy clean_agent
