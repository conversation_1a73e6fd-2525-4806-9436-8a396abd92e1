{#
    Command:
        avalanchecli_drop_all_dbs
            short_help: Drop all avalanche databases

    Arguments:

    Required variables:

    Optional variables:
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    avalanchecli_drop_all_dbs
  displayName: elipy avalanchecli_drop_all_dbs
