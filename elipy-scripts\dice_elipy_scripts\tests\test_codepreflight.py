"""
test_codepreflight.py

Unit testing for codepreflight
"""
import unittest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import patch, MagicMock
from elipy2.exceptions import ELIPYException
from dice_elipy_scripts.codepreflight import cli
from dice_elipy_scripts.tests.testutils import mock_retry


class TestCodePreflight(unittest.TestCase):
    ARGUMENT_P4PORT = "myjenkinsp4port"
    ARGUMENT_P4CLIENT = "myjenkinsclient"
    ARGUMENT_PLATFORM = "ps4"
    ARGUMENT_CONFIG = "final"
    ARGUMENT_CHANGELIST = "1234567"

    OPTION_CLEAN = "--clean"
    OPTION_IMPORT_LOCAL = "--import-local"
    OPTION_P4_COMPILE = "--p4-compile"
    OPTION_NOMASTER = "--nomaster"

    ARGUMENT_ICEPICK_TEST = "--icepick-test"
    OPTION_TEST_SUITE = "mytestsuite"
    ARGUMENT_IGNORE_ICEPICK_EXIT_CODE = "--ignore-icepick-exit-code"
    VALUE_IGNORE_ICEPICK_EXIT_CODE = "false"
    OPTION_SETTINGS_FILES = "--settings-files file1 --settings-files file2"

    OPTION_DO_WARM_MACHINE = "--do-warmup"
    OPTION_SKIP_REVERT = "--skip-revert"

    DEFAULT_ARGS = [
        ARGUMENT_P4PORT,
        ARGUMENT_P4CLIENT,
        ARGUMENT_PLATFORM,
        ARGUMENT_CONFIG,
        ARGUMENT_CHANGELIST,
    ]

    def setUp(self):
        self.patcher_core_run = patch("dice_elipy_scripts.codepreflight.core.run")
        self.mock_core_run = self.patcher_core_run.start()
        self.mock_core_run.return_value = (0, [], [])

        self.patcher_retry = patch("retry.api.__retry_internal", mock_retry)
        self.mock_retry = self.patcher_retry.start()

        self.patcher_p4 = patch("dice_elipy_scripts.codepreflight.p4.P4Utils")
        self.mock_p4 = self.patcher_p4.start()

    def tearDown(self):
        self.patcher_core_run.stop()
        self.patcher_p4.stop()
        self.patcher_retry.stop()

    @patch("dice_elipy_scripts.codepreflight.p4.P4Utils.unshelve")
    @patch("dice_elipy_scripts.codepreflight.code.CodeUtils")
    def test_warmup_machine(self, mock_patcher_CodeUtils, mock_unshelve):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS + [self.OPTION_DO_WARM_MACHINE])
        mock_unshelve.assert_not_called()
        assert result.exit_code == 0

    @patch("dice_elipy_scripts.codepreflight.code.CodeUtils")
    def test_do_clean_no_import_local(self, mock_patcher_CodeUtils):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS + [self.OPTION_CLEAN, "true"])
        mock_patcher_CodeUtils.return_value.clean_local.assert_called_with(close_handles=True)
        assert result.exit_code == 0

    @patch("dice_elipy_scripts.codepreflight.code.CodeUtils")
    def test_no_clean_no_import_local(self, mock_patcher_CodeUtils):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 0

    @patch(
        "dice_elipy_scripts.codepreflight.import_local_code_state",
        return_value=MagicMock(),
    )
    @patch("dice_elipy_scripts.codepreflight.filer.FilerUtils", return_value=MagicMock())
    @patch("dice_elipy_scripts.codepreflight.code.CodeUtils", return_value=MagicMock())
    def test_no_clean_do_import_local(self, mock_Builder, mock_Import, mock_Filer):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS + [self.OPTION_IMPORT_LOCAL])
        mock_Import(mock_Builder.return_value, mock_Filer.return_value, None, "ps4", "final")
        assert result.exit_code == 0

    @patch("dice_elipy_scripts.codepreflight.p4_compile_run", return_value=MagicMock())
    @patch("dice_elipy_scripts.codepreflight.run_gensln", return_value=MagicMock())
    @patch("dice_elipy_scripts.codepreflight.code.CodeUtils", return_value=MagicMock())
    def test_p4_do_not_compile_run(self, mock_Builder, mock_gensln, mock_p4run):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        mock_gensln.assert_called_once_with(
            framework_args=[],
            nomaster=False,
            password=None,
            user=None,
            domain_user=None,
            builder=mock_Builder.return_value,
        )
        mock_p4run.assert_not_called()
        assert result.exit_code == 0

    @patch("dice_elipy_scripts.codepreflight.p4_compile_run", return_value=MagicMock())
    @patch("dice_elipy_scripts.codepreflight.run_gensln", return_value=MagicMock())
    @patch("dice_elipy_scripts.codepreflight.code.CodeUtils", return_value=MagicMock())
    def test_p4_do_compile_run(self, mock_Builder, mock_gensln, mock_p4run):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS + ["--p4-compile"])
        mock_gensln.assert_called_once_with(
            framework_args=[],
            nomaster=False,
            password=None,
            user=None,
            domain_user=None,
            builder=mock_Builder.return_value,
        )
        assert mock_p4run.call_count == 1

    @patch("dice_elipy_scripts.codepreflight.p4_compile_run", return_value=MagicMock())
    def test_p4_wrong_compile_run(self, mock_p4run):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.ARGUMENT_P4PORT,
                self.ARGUMENT_P4CLIENT,
                "ps10",
                self.ARGUMENT_CONFIG,
                self.ARGUMENT_CHANGELIST,
                self.OPTION_P4_COMPILE,
            ],
        )
        mock_p4run(self.ARGUMENT_CHANGELIST, "ps10")
        assert not result.exit_code == 0

    @patch("dice_elipy_scripts.codepreflight.code.CodeUtils", return_value=MagicMock())
    def test_icepick_test_on_tool(self, mock_Builder):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.ARGUMENT_P4PORT,
                self.ARGUMENT_P4CLIENT,
                self.ARGUMENT_PLATFORM,
                "tool",
                self.ARGUMENT_CHANGELIST,
                self.ARGUMENT_ICEPICK_TEST,
                self.OPTION_TEST_SUITE,
                self.ARGUMENT_IGNORE_ICEPICK_EXIT_CODE,
                self.VALUE_IGNORE_ICEPICK_EXIT_CODE,
            ],
        )
        mock_Builder.return_value.buildsln.assert_called_once()

    @patch("dice_elipy_scripts.codepreflight.raise_if_wrong_stream", MagicMock())
    @patch("dice_elipy_scripts.codepreflight.raise_if_cl_not_exists", MagicMock())
    @patch("dice_elipy_scripts.codepreflight.code.CodeUtils", MagicMock())
    def test_p4_found_cl(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 0

    @patch("dice_elipy_scripts.codepreflight.raise_if_wrong_stream", MagicMock())
    @patch(
        "dice_elipy_scripts.codepreflight.raise_if_cl_not_exists",
        MagicMock(side_effect=ELIPYException),
    )
    @patch("dice_elipy_scripts.codepreflight.code.CodeUtils", MagicMock())
    def test_p4_missing_cl(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 1

    @patch("dice_elipy_scripts.codepreflight.raise_if_wrong_stream", MagicMock())
    @patch("dice_elipy_scripts.codepreflight.raise_if_cl_not_exists", MagicMock())
    @patch("dice_elipy_scripts.codepreflight.code.CodeUtils", MagicMock())
    def test_p4_correct_stream(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 0

    @patch(
        "dice_elipy_scripts.codepreflight.raise_if_wrong_stream",
        MagicMock(side_effect=ELIPYException),
    )
    @patch("dice_elipy_scripts.codepreflight.raise_if_cl_not_exists", MagicMock())
    @patch("dice_elipy_scripts.codepreflight.code.CodeUtils", MagicMock())
    def test_p4_wrong_stream(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 1

    @patch("dice_elipy_scripts.codepreflight.p4.P4Utils.revert")
    @patch("dice_elipy_scripts.codepreflight.code.CodeUtils", MagicMock())
    def test_skip_revert(self, mock_revert):
        runner = CliRunner()

        result = runner.invoke(cli, self.DEFAULT_ARGS + [self.OPTION_SKIP_REVERT, "true"])

        mock_revert.assert_not_called()

        assert result.exit_code == 0

    @patch("dice_elipy_scripts.codepreflight.data.DataUtils", MagicMock())
    @patch("dice_elipy_scripts.codepreflight.code.CodeUtils", MagicMock())
    @patch("dice_elipy_scripts.codepreflight.icepick.IcepickUtils")
    def test_tool_frosting_report_param(self, mock_icepick_utils):
        args = self.DEFAULT_ARGS
        args[2] = "tool"
        mock_icepicker = mock_icepick_utils.return_value = MagicMock()
        runner = CliRunner()
        result = runner.invoke(
            cli, self.DEFAULT_ARGS + ["--icepick-test", "abc", "--frosting-report", "true"]
        )
        assert mock_icepicker.run_icepick.call_count == 1
        assert mock_icepicker.run_icepick.call_args[1]["send_frosting_report"] is True
        assert result.exit_code == 0

    @patch("dice_elipy_scripts.codepreflight.data.DataUtils", MagicMock())
    @patch("dice_elipy_scripts.codepreflight.code.CodeUtils", MagicMock())
    @patch("dice_elipy_scripts.codepreflight.filer.FilerUtils", MagicMock())
    @patch("dice_elipy_scripts.codepreflight.LOGGER", MagicMock())
    @patch("dice_elipy_scripts.codepreflight.add_sentry_tags", MagicMock())
    @patch("dice_elipy_scripts.codepreflight.running_processes", MagicMock())
    @patch("dice_elipy_scripts.codepreflight.raise_if_cl_not_exists", MagicMock())
    @patch("dice_elipy_scripts.codepreflight.raise_if_wrong_stream", MagicMock())
    @patch("dice_elipy_scripts.codepreflight.retry_call", MagicMock())
    @patch("dice_elipy_scripts.codepreflight.run_gensln", MagicMock())
    @patch("dice_elipy_scripts.codepreflight.icepick.IcepickUtils")
    def test_forces_sequential_icepick(self, mock_icepick_utils):
        args = self.DEFAULT_ARGS
        args[2] = "tool"
        mock_icepicker = mock_icepick_utils.return_value = MagicMock()
        runner = CliRunner()
        result = runner.invoke(
            cli, self.DEFAULT_ARGS + ["--icepick-test", "faketest", "--force-sequential-icepick"]
        )
        assert mock_icepicker.run_icepick.call_count == 1

        run_args = mock_icepicker.run_icepick.call_args[1]["run_args"]

        max_parallel_set = "--max-parallel-tests" in run_args
        if max_parallel_set:
            index = run_args.index("--max-parallel-tests")
            assert run_args[index + 1] == "1"

        assert result.exit_code == 0

    @patch("dice_elipy_scripts.codepreflight.data.DataUtils", MagicMock())
    @patch("dice_elipy_scripts.codepreflight.code.CodeUtils", MagicMock())
    @patch("dice_elipy_scripts.codepreflight.filer.FilerUtils", MagicMock())
    @patch("dice_elipy_scripts.codepreflight.LOGGER", MagicMock())
    @patch("dice_elipy_scripts.codepreflight.add_sentry_tags", MagicMock())
    @patch("dice_elipy_scripts.codepreflight.running_processes", MagicMock())
    @patch("dice_elipy_scripts.codepreflight.raise_if_cl_not_exists", MagicMock())
    @patch("dice_elipy_scripts.codepreflight.raise_if_wrong_stream", MagicMock())
    @patch("dice_elipy_scripts.codepreflight.retry_call", MagicMock())
    @patch("dice_elipy_scripts.codepreflight.run_gensln", MagicMock())
    @patch("dice_elipy_scripts.codepreflight.icepick.IcepickUtils")
    def test_not_forces_sequential_icepick(self, mock_icepick_utils):
        args = self.DEFAULT_ARGS
        args[2] = "tool"
        mock_icepicker = mock_icepick_utils.return_value = MagicMock()
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS + ["--icepick-test", "faketest"])
        assert mock_icepicker.run_icepick.call_count == 1

        run_args = mock_icepicker.run_icepick.call_args[1]["run_args"]

        max_parallel_set = "--max-parallel-tests" in run_args
        assert not max_parallel_set
        assert result.exit_code == 0
