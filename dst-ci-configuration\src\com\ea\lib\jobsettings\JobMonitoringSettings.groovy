package com.ea.lib.jobsettings

class JobMonitoringSettings extends JobSetting {
    String slackChannel
    String projectShortName
    String cloudNodePrefix

    void initializeStartJob(def masterFile, def projectFile) {
        description = 'Maintenance job that alerts when jobs get stuck'
        cronTrigger = masterFile.MAINTENANCE_SETTINGS.JOB_MONITORING_TRIGGER ?: '@hourly'
        slackChannel = masterFile.MAINTENANCE_SETTINGS.JOB_MONITORING_SLACK_CHANNEL ?: '#cobra-jobdsl'
        projectShortName = masterFile.MAINTENANCE_SETTINGS.JOB_MONITORING_PROJECT_SHORT_NAME ?: projectFile.short_name
        cloudNodePrefix = masterFile.MAINTENANCE_SETTINGS.JOB_MONITORING_CLOUD_NODE_PREFIX ?: 'bct6-'
    }
}
