import com.ea.lib.jobs.LibAutotestModelBuilder
import com.ea.lib.model.autotest.AutotestCategory
import com.ea.lib.model.autotest.BranchConfiguration
import com.ea.lib.model.autotest.Name
import com.ea.lib.model.autotest.Platform
import com.ea.lib.model.autotest.TestInfo
import com.ea.lib.model.autotest.TestSuite
import com.ea.matrixfiles.AutotestMatrix
import com.ea.matrixfiles.AutotestMatrixFactory
import spock.lang.Specification
import spock.lang.Unroll

class AutotestMatrixSpec extends Specification {

    void 'test bytecode size of children'() {
        when:
        String line = "javap -c ${classPath}"
            .execute()
            .text
            .split('\n')
            .find { it.contains('return') }
        Integer size = line[2..line.indexOf(':') - 1].toInteger()
        then:
        size < 60 * 1024
        where:
        classPath << {
            List<String> classPaths = []
            String packagePath = 'build/classes/groovy/main/com/ea/matrixfiles'
            new File(packagePath).eachFileMatch(~/\w*\.class/) { classPaths << packagePath + '/' + it.name }
            classPaths
        }()
    }

    void "test getTimeoutHours prioritizes testInfo.timeoutHours"() {
        given:
        AutotestCategory testCategory = new AutotestCategory(
            testInfo: new TestInfo(
                timeoutHours: 8,
                tests: [
                    new TestSuite(timeoutHours: 10),
                    new TestSuite(timeoutHours: 4),
                ])
        )
        when:
        int timeoutHours = AutotestMatrix.getTimeoutHours(testCategory)
        then:
        timeoutHours == 8
    }

    void "test getTimeoutHours picks highest test suite timeoutHours"() {
        given:
        AutotestCategory testCategory = new AutotestCategory(
            testInfo: new TestInfo(
                tests: [
                    new TestSuite(timeoutHours: 10),
                    new TestSuite(timeoutHours: 4),
                    new TestSuite(timeoutHours: 11),
                    new TestSuite(timeoutHours: 5),
                ])
        )
        when:
        int timeoutHours = AutotestMatrix.getTimeoutHours(testCategory)
        then:
        timeoutHours == 11
    }

    void "test filterCategories retrieves all the configured categories for the given branch"() {
        given:
        String branchName = 'kin-dev'
        String cronTrigger = 'cron1'
        List downstreamAutotestCategories = ['test-category-1']
        TestInfo testInfo = new TestInfo(testGroup: 'test-group-1')
        List<AutotestCategory> categories = [
            new AutotestCategory(
                name: 'cat1',
                branches: [
                    new BranchConfiguration(branchName: branchName, testInfo: testInfo, cronTrigger: cronTrigger, downstreamAutotestCategories: downstreamAutotestCategories),
                    new BranchConfiguration(branchName: 'bf-trunk', testInfo: new TestInfo(testGroup: 'test-group-2'), cronTrigger: 'cron2'),
                    new BranchConfiguration(branchName: 'cas-dev', testInfo: new TestInfo(testGroup: 'test-group-3'), cronTrigger: 'cron3'),
                ]
            ),
            new AutotestCategory(
                name: 'cat2',
                branches: [
                    new BranchConfiguration(branchName: 'bf-trunk', testInfo: new TestInfo(testGroup: 'test-group-2'), cronTrigger: 'cron2'),
                    new BranchConfiguration(branchName: 'cas-dev', testInfo: new TestInfo(testGroup: 'test-group-3'), cronTrigger: 'cron3'),
                ]
            ),
            new AutotestCategory(
                name: 'cat3',
                branches: [
                    new BranchConfiguration(branchName: 'cas-dev', testInfo: new TestInfo(testGroup: 'test-group-3'), cronTrigger: 'cron3'),
                    new BranchConfiguration(branchName: 'bf-trunk', testInfo: new TestInfo(testGroup: 'test-group-2'), cronTrigger: 'cron2'),
                    new BranchConfiguration(branchName: branchName, testInfo: new TestInfo(testGroup: 'test-group-1'), cronTrigger: 'cron1'),
                ]
            ),
            new AutotestCategory(
                name: 'cat4',
                branches: [
                    new BranchConfiguration(branchName: 'bf-trunk', testInfo: new TestInfo(testGroup: 'test-group-2'), cronTrigger: 'cron2'),
                    new BranchConfiguration(branchName: branchName, testInfo: new TestInfo(testGroup: 'test-group-1'), cronTrigger: 'cron1'),
                    new BranchConfiguration(branchName: 'cas-dev', testInfo: new TestInfo(testGroup: 'test-group-3'), cronTrigger: 'cron3'),
                ]
            ),]
        when:
        List<AutotestCategory> result = AutotestMatrix.filterCategories(categories, branchName)
        then:
        result.size() == 3
        result.get(0).testInfo == testInfo
        result.get(0).trigger == cronTrigger
        result.get(0).downstreamAutotestCategories == downstreamAutotestCategories
    }
    static String branchName = 'kin-dev'
    static Map<String, List<Platform>> foundPlatforms = [
        'kin-dev-unverified': [new Platform(name: Name.PS5), new Platform(name: Name.PS4)],
        (branchName)        : [new Platform(name: Name.WIN64), new Platform(name: Name.PS5)],
        'kin-dev-irt'       : [new Platform(name: Name.XB1), new Platform(name: Name.XBSX)],
    ]
    static Map<String, List<Platform>> notFoundPlatforms = [
        'kin-dev-unverified': [new Platform(name: Name.PS5), new Platform(name: Name.PS4)],
        'kin-next'          : [new Platform(name: Name.WIN64), new Platform(name: Name.PS5)],
        'kin-dev-irt'       : [new Platform(name: Name.XB1), new Platform(name: Name.XBSX)],
    ]

    @Unroll
    void 'test getPlatforms #result platforms for the given branch'() {
        when:
        List<Platform> platforms = AutotestMatrix.findPlatforms(givenPlatforms, branchName)
        then:
        platforms.size() == expectedSize
        where:
        givenPlatforms    || expectedSize | result
        foundPlatforms    || 2            | 'finds'
        notFoundPlatforms || 0            | 'does not find'
    }

    void 'test getPlatforms returns platforms for the given branch'() {
        when:
        List<Platform> platforms = AutotestMatrix.findPlatforms(foundPlatforms, branchName)
        then:
        platforms[0].name == Name.WIN64
        platforms[1].name == Name.PS5
        platforms.size() == 2
    }

    void "test parallel limit correctly configured"() {
        when:
        String failedTests = ''
        List<String> autotestMatrices = AutotestMatrixFactory.autotestMatrices
        autotestMatrices.each { autotestMatrixName ->
            AutotestMatrix autotestMatrix = AutotestMatrixFactory.getInstance(autotestMatrixName)
            List<String> branches = autotestMatrix.branches
            branches.each { branchName ->
                List<Platform> defaultPlatforms = autotestMatrix.getPlatforms(branchName)
                List<AutotestCategory> testCategories = autotestMatrix.getTestCategories(branchName)
                testCategories.each { testCategory ->
                    if (testCategory.runLevelsInParallel) {
                        List<Platform> platforms = LibAutotestModelBuilder.composePlatforms(testCategory.testInfo, defaultPlatforms)
                        int platformsCount = platforms.size()
                        int parallelLimit = testCategory.parallelLimit
                        int platformsCountRest = parallelLimit % platformsCount
                        if (platformsCountRest != 0) {
                            failedTests += "\nAutotest Matrix: ${autotestMatrixName}, Branch: ${branchName}, Test Category: ${testCategory.name} " +
                                "- Incorrect parallelLimit ${parallelLimit}: Should be a multiple of the number of platforms, ${platformsCount}. The rest is ${platformsCountRest}"
                        }
                    }
                }
            }
        }
        then:
        failedTests.isEmpty()
    }

    void "test FrostEd Autotests correctly configured"() {
        when:
        String failedTests = ''
        List<String> autotestMatrices = AutotestMatrixFactory.autotestMatrices
        autotestMatrices.each { autotestMatrixName ->
            AutotestMatrix autotestMatrix = AutotestMatrixFactory.getInstance(autotestMatrixName)
            List<String> branches = autotestMatrix.branches
            branches.each { branchName ->
                List<AutotestCategory> testCategories = autotestMatrix.getTestCategories(branchName)
                testCategories.each { testCategory ->
                    if (testCategory.isFrostedAutotest) {
                        if (testCategory.testInfo.parallelLimit != 0 && !testCategory.testInfo.runLevelsInParallel) {
                            failedTests += "\nAutotest Matrix: ${autotestMatrixName}, Branch: ${branchName}, Test Category: ${testCategory.name}" +
                                ' - FrostedAutotestCategory misconfigured: parallelLimit and runLevelsInParallel cannot be modified.'
                        }
                    }
                }
            }
        }
        then:
        failedTests.isEmpty()
    }

    void "test platforms correctly configured"() {
        when:
        String failedTests = ''
        List<String> autotestMatrices = AutotestMatrixFactory.autotestMatrices
        autotestMatrices.each { autotestMatrixName ->
            AutotestMatrix autotestMatrix = AutotestMatrixFactory.getInstance(autotestMatrixName)
            List<String> branches = autotestMatrix.branches
            branches.each { branchName ->
                boolean defaultShouldLevelsRunInParallel = autotestMatrix.shouldLevelsRunInParallel(branchName)
                List<Platform> defaultPlatforms = autotestMatrix.getPlatforms(branchName)
                failedTests += validatePlatforms(defaultPlatforms, autotestMatrixName, branchName, 'None', '<Default platforms>')
                List<AutotestCategory> testCategories = autotestMatrix.getTestCategories(branchName)
                testCategories.each { testCategory ->
                    List<Platform> testCategoryPlatforms = testCategory.testInfo.platforms ?: []
                    failedTests += validatePlatforms(testCategoryPlatforms, autotestMatrixName, branchName, testCategory.name)
                    List<TestSuite> tests = testCategory.testInfo.tests ?: []
                    tests.each { test ->
                        def testPlatforms = test.platforms ?: []
                        boolean runLevelsInParallel = testCategory.runLevelsInParallel != null ? testCategory.runLevelsInParallel : defaultShouldLevelsRunInParallel
                        boolean isTestWithLooseFiles = testCategory.isTestWithLooseFiles
                        failedTests += validatePlatforms(testPlatforms, autotestMatrixName, branchName, testCategory.name, "Test suite: ${test.name}")
                        failedTests += validateRegion(testPlatforms, testCategoryPlatforms, runLevelsInParallel, isTestWithLooseFiles, autotestMatrixName, branchName, testCategory.name, test)
                        List frostedPlatforms = testPlatforms ?: testCategory.testInfo.platforms ?: defaultPlatforms
                        failedTests += validateFrostedPlatforms(testCategory, frostedPlatforms, autotestMatrixName, branchName, testCategory.name, "Test suite: ${test.name}")
                    }
                }
            }
        }
        then:
        failedTests.isEmpty()
    }

    private static String validateFrostedPlatforms(AutotestCategory autotestCategory, List<Platform> platforms, String autotestMatrixName,
                                                   String branchName, String testCategoryName, String extraContext = '') {
        String failedTests = ''
        if (autotestCategory.isFrostedAutotest && (platforms.size() != 1 || platforms.get(0).name != Name.WIN64)) {
            failedTests += "\nAutotest Matrix: ${autotestMatrixName}, Branch: ${branchName}, Test Category: ${testCategoryName}" +
                " - FrostedAutotestMatrix misconfigured: platforms may only consist of ${Name.WIN64}. ${extraContext}"
        }
        return failedTests
    }

    private static String validatePlatforms(List<Platform> platforms, String autotestMatrixName,
                                            String branchName, String testCategoryName, String extraContext = '') {
        String failedTests = ''
        platforms.each { platform ->
            if (!platform.name) {
                failedTests += "\nAutotest Matrix: ${autotestMatrixName}, Branch: ${branchName}, Test Category: ${testCategoryName}" +
                    " - Platform misconfigured: missing property name. ${extraContext}"
            }
        }
        return failedTests
    }

    private static String validateRegion(List<Platform> testSuitePlatforms, List<Platform> testCategoryPlatforms,
                                         boolean runLevelsInParallel, boolean isTestWithLooseFiles, String autotestMatrixName,
                                         String branchName, String testCategoryName, TestSuite testSuite) {
        String failedTests = ''
        testSuitePlatforms.each { platform ->
            Platform incorrectlyConfiguredPlatform = testCategoryPlatforms.find { it.name == platform.name && it.region != platform.region }
            if (runLevelsInParallel && isTestWithLooseFiles && incorrectlyConfiguredPlatform) {
                failedTests += "\nAutotest Matrix: ${autotestMatrixName}, Branch: ${branchName}, Test Category: ${testCategoryName}" +
                    ", Test suite: ${testSuite.name} - We only support one Region configuration per Platform when running tests in " +
                    "parallel. You have configured this TestCategory to create jobs for {Platform: ${platform.name}, Region: " +
                    "${platform.region}} and {Platform: ${incorrectlyConfiguredPlatform.name}, Region: " +
                    "${incorrectlyConfiguredPlatform.region}}. Please delete one of them or modify them both to have the same Region."
            }
        }
        return failedTests
    }

}
