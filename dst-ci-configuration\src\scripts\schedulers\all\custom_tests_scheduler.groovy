package scripts.schedulers.all

import com.ea.project.GetBranchFile
import jenkins.model.<PERSON>

def project = ProjectClass(env.projectName)
/**
 * custom_tests_scheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Run custom tests') {
            steps {
                script {
                    def branchfile = GetBranchFile.get_branchfile(env.projectName, env.branchName)
                    def lastCodeBuild = Jenkins.get().getItem(env.customTestsReferenceJob as String).lastStableBuild?.getEnvironment(TaskListener.NULL)?.code_changelist

                    def codeChangelist = params.code_changelist ?: lastCodeBuild

                    if (codeChangelist == null || codeChangelist == '') {
                        echo 'Missing changelist, aborting build!'
                        currentBuild.result = Result.FAILURE.toString()
                        return
                    }

                    def args = [
                        string(name: 'code_changelist', value: codeChangelist),
                    ]

                    def injectMap = [
                        'code_changelist': codeChangelist,
                    ]
                    EnvInject(currentBuild, injectMap)
                    currentBuild.displayName = env.JOB_NAME + '.' + codeChangelist

                    def jobName = env.branchName + '.custom-tests.build'
                    def customTestsJob = build(job: jobName, parameters: args, propagate: false)
                    currentBuild.result = customTestsJob.result.toString()

                    def slackSettings = branchfile.standard_jobs_settings?.slack_channel_custom_tests
                    SlackMessageNew(currentBuild, slackSettings, project.short_name)

                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
}
