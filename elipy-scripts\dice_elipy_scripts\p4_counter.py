"""
p4_counter.py
"""
import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import exceptions, LOGGER, p4
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command("p4_counter", short_help="Performs a Perforce set single counter value operation.")
@click.option("--port", required=True, help="Perforce server specification")
@click.option("--client", required=True)
@click.option("--user", required=True)
@click.option("--countername", required=True)
@click.option("--value", required=True)
@click.option(
    "--extra-port",
    required=False,
    help="When enable 2nd counter on a different Perforce server",
)
@click.option(
    "--extra-client",
    required=False,
    help="When enable 2nd counter from a different client",
)
@click.option(
    "--extra-countername",
    required=False,
    help="Set for 2nd counter's name, usually for data-counter",
)
@click.option(
    "--extra-value",
    required=False,
    help="Set for 2nd counter's value, usually for data-counter",
)
@click.option("--force", is_flag=True)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    port,
    client,
    user,
    countername,
    value,
    extra_port,
    extra_client,
    extra_countername,
    extra_value,
    force,
):  # pylint: disable=unused-argument #pylint: disable=line-too-long
    """
    Performs the Perforce set operation on one single code counter or code+data counters
    """
    # Adding sentry tags
    add_sentry_tags(__file__)

    LOGGER.info(
        "Performing P4 counter using client %s on port %s by user %s",
        client,
        port,
        user,
    )
    try:
        perforce = p4.P4Utils(port=port, client=client, user=user)

        # Update first counter (usually code)
        current_value = get_counter_as_int(perforce, countername)
        if current_value < int(value) or force:
            perforce.setcounter(countername, value)
            LOGGER.info("Set P4 counter %s to value %s", countername, value)
        else:
            LOGGER.warning(
                "Counter not updated. New LKG P4 Counters value {} should be greater than existing value: {}.".format(
                    value, current_value
                )  # pylint: disable=line-too-long
            )

    except exceptions.ELIPYException:
        raise exceptions.ELIPYException(
            "p4 counter operation failed on {0} with {1}".format(countername, value)
        )  # pylint: disable=unused-argument #pylint: disable=line-too-long
    LOGGER.info("Done!")

    # Update second counter (usually data)
    if extra_countername and extra_value:
        if extra_port and extra_client:
            perforce2 = p4.P4Utils(port=extra_port, client=extra_client, user=user)
        else:
            perforce2 = perforce
        try:
            current_value = get_counter_as_int(perforce2, extra_countername)
            if current_value < int(extra_value) or force:
                perforce2.setcounter(extra_countername, extra_value)
                LOGGER.info("Set P4 counter %s to value %s", extra_countername, extra_value)
            else:
                LOGGER.warning(
                    "Counter not updated. New LKG P4 Counters value {} should be greater than existing value: {}.".format(
                        extra_value, current_value
                    )  # pylint: disable=line-too-long
                )
        except exceptions.ELIPYException:
            raise exceptions.ELIPYException(
                "p4 counter operation failed on {0} with {1}".format(extra_countername, extra_value)
            )  # pylint: disable=unused-argument #pylint: disable=line-too-long
        LOGGER.info("Done!")


def get_counter_as_int(perforce: p4.P4Utils, countername: str, default_value: int = 0) -> int:
    """
    Get counter value as int.

    :param perforce: Perforce object to use for Perforce interactions.
    :param countername: The name of the counter.
    :param default_value: Default value to use if we don't get a useful response from Perforce.
    :return: The counter value as an integer.
    """
    try:
        raw_value = perforce.getcounter(countername)
        return_value = int(raw_value)
    except TypeError as exception:
        LOGGER.warning(exception)
        LOGGER.warning("Counter {} cannot be converted to int: {}".format(countername, raw_value))
        LOGGER.warning(
            "Using default counter value {} for Counter {}".format(default_value, countername)
        )
        return_value = default_value

    return return_value
