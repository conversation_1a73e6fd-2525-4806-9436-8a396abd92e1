<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="AbcMetric" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="AbstractClassName" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="AbstractClassWithPublicConstructor" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="AbstractClassWithoutAbstractMethod" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="AddEmptyString" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="AssertWithinFinallyBlock" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="AssignCollectionSort" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="AssignCollectionUnique" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="AssignmentInConditional" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="BigDecimalInstantiation" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="BitwiseOperatorInConditional" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="BlankLineBeforePackage" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="BlockEndsWithBlankLine" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="BlockStartsWithBlankLine" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="BooleanGetBoolean" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="BooleanMethodReturnsNull" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="BracesForClass" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="BracesForForLoop" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="BracesForIfElse" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="BracesForMethod" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="BracesForTryCatchFinally" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="BrokenNullCheck" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="BrokenOddnessCheck" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="BuilderMethodWithSideEffects" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="CatchArrayIndexOutOfBoundsException" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="CatchError" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="CatchException" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="CatchIllegalMonitorStateException" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="CatchIndexOutOfBoundsException" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="CatchNullPointerException" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="CatchRuntimeException" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="CatchThrowable" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ChainedTest" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="ClassEndsWithBlankLine" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="ClassForName" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ClassJavadoc" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="ClassNameSameAsFilename" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ClassNameSameAsSuperclass" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ClassSize" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="ClassStartsWithBlankLine" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="CloneableWithoutClone" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="CloseWithoutCloseable" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ClosureAsLastMethodParameter" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ClosureStatementOnOpeningLineOfMultipleLineClosure" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="CodeNarc.ClassJavadoc" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="CodeNarc.CompileStatic" enabled="false" level="WARNING" enabled_by_default="false">
      <option name="doNotApplyToClassNames" value="*Spec,*Specification" />
      <option name="doNotApplyToFileNames" value="*.gradle" />
    </inspection_tool>
    <inspection_tool class="CollectAllIsDeprecated" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="CompareToWithoutComparable" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ComparisonOfTwoConstants" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ComparisonWithSelf" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="CompileStatic" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="ConfusingClassNamedException" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ConfusingMethodName" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ConfusingMultipleReturns" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ConfusingTernary" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ConsecutiveBlankLines" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ConsecutiveLiteralAppends" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ConsecutiveStringConcatenation" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ConstantAssertExpression" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ConstantIfExpression" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ConstantTernaryExpression" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ConstantsOnlyInterface" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="CouldBeElvis" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="CouldBeSwitchStatement" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="CoupledTestCase" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="CrapMetric" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="DeadCode" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="DirectConnectionManagement" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="DoubleNegative" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="DuplicateCaseStatement" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="DuplicateImport" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="DuplicateListLiteral" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="DuplicateMapKey" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="DuplicateMapLiteral" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="DuplicateNumberLiteral" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="DuplicateSetValue" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="DuplicateStringLiteral" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="ElseBlockBraces" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="EmptyCatchBlock" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="EmptyElseBlock" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="EmptyForStatement" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="EmptyIfStatement" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="EmptyInstanceInitializer" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="EmptyMethodInAbstractClass" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="EmptyStaticInitializer" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="EmptySwitchStatement" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="EmptyWhileStatement" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="EnumCustomSerializationIgnored" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="EqualsAndHashCode" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="EqualsOverloaded" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ExceptionExtendsError" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ExceptionExtendsThrowable" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ExceptionNotThrown" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ExplicitArrayListInstantiation" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ExplicitCallToAndMethod" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ExplicitCallToCompareToMethod" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ExplicitCallToDivMethod" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ExplicitCallToEqualsMethod" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ExplicitCallToGetAtMethod" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ExplicitCallToLeftShiftMethod" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ExplicitCallToMinusMethod" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ExplicitCallToModMethod" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ExplicitCallToMultiplyMethod" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ExplicitCallToOrMethod" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ExplicitCallToPlusMethod" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ExplicitCallToPowerMethod" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ExplicitCallToPutAtMethod" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ExplicitCallToRightShiftMethod" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ExplicitCallToXorMethod" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ExplicitGarbageCollection" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ExplicitHashMapInstantiation" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ExplicitHashSetInstantiation" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ExplicitLinkedHashMapInstantiation" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ExplicitLinkedListInstantiation" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ExplicitStackInstantiation" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ExplicitTreeSetInstantiation" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="FactoryMethodName" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="FieldName" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="FieldTypeRequired" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="FileCreateTempFile" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="FileEndsWithoutNewline" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="FinalClassWithProtectedMember" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ForLoopShouldBeWhileLoop" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ForStatementBraces" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="GStringAsMapKey" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="GStringExpressionWithinString" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="GetterMethodCouldBeProperty" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="GrailsDomainHasEquals" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="GrailsDomainHasToString" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="GrailsDomainReservedSqlKeywordName" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="GrailsDomainStringPropertyMaxSize" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="GrailsDomainWithServiceReference" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="GrailsDuplicateConstraint" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="GrailsDuplicateMapping" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="GrailsMassAssignment" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="GrailsPublicControllerMethod" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="GrailsServletContextReference" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="GrailsStatelessService" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="GroovyLangImmutable" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="HardCodedWindowsFileSeparator" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="HardCodedWindowsRootDirectory" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="HashtableIsObsolete" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="IfStatementBraces" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="IfStatementCouldBeTernary" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="IllegalClassMember" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="IllegalClassReference" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="IllegalPackageReference" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="IllegalRegex" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="IllegalString" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="IllegalSubclass" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ImplementationAsType" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ImportFromSamePackage" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ImportFromSunPackages" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="InconsistentPropertyLocking" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="InconsistentPropertySynchronization" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="Indentation" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="InsecureRandom" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="Instanceof" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="IntegerGetInteger" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="InterfaceName" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="InterfaceNameSameAsSuperInterface" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="InvertedCondition" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="InvertedIfElse" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="JUnitAssertAlwaysFails" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="JUnitAssertAlwaysSucceeds" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="JUnitFailWithoutMessage" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="JUnitLostTest" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="JUnitPublicField" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="JUnitPublicNonTestMethod" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="JUnitPublicProperty" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="JUnitSetUpCallsSuper" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="JUnitStyleAssertions" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="JUnitTearDownCallsSuper" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="JUnitTestMethodWithoutAssert" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="JUnitUnnecessarySetUp" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="JUnitUnnecessaryTearDown" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="JUnitUnnecessaryThrowsException" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="JavaIoPackageAccess" enabled="true" level="ERROR" enabled_by_default="true">
      <option name="doNotApplyToFilesMatching" value=".*(Spec|Test(s|Case|Util)?)\.groovy" />
    </inspection_tool>
    <inspection_tool class="JavadocConsecutiveEmptyLines" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="JavadocEmptyAuthorTag" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="JavadocEmptyExceptionTag" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="JavadocEmptyFirstLine" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="JavadocEmptyLastLine" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="JavadocEmptyParamTag" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="JavadocEmptyReturnTag" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="JavadocEmptySeeTag" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="JavadocEmptySinceTag" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="JavadocEmptyThrowsTag" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="JavadocEmptyVersionTag" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="JavadocMissingExceptionDescription" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="JavadocMissingParamDescription" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="JavadocMissingThrowsDescription" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="JdbcConnectionReference" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="JdbcResultSetReference" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="JdbcStatementReference" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="LineLength" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="LocaleSetDefault" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="LoggerForDifferentClass" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="LoggerWithWrongModifiers" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="LoggingSwallowsStacktrace" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="LongLiteralWithLowerCaseL" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="MethodName" enabled="true" level="ERROR" enabled_by_default="true">
      <option name="doNotApplyToClassNames" value="*Spec,*Specification" />
    </inspection_tool>
    <inspection_tool class="MethodParameterTypeRequired" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="MethodReturnTypeRequired" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="MethodSize" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="MisorderedStaticImports" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="MissingBlankLineAfterImports" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="MissingBlankLineAfterPackage" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="MissingNewInThrowStatement" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="MultipleLoggers" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="MultipleUnaryOperators" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="NestedBlockDepth" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="NestedForLoop" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="NestedSynchronization" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="NoDef" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="NoJavaUtilDate" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="NoTabCharacter" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="NoWildcardImports" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="NonFinalPublicField" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="NonFinalSubclassOfSensitiveInterface" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ObjectFinalize" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ObjectOverrideMisspelledMethodName" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="PackageNameMatchesFilePath" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ParameterCount" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="ParameterName" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="ParameterReassignment" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="PrintStackTrace" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="Println" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="PrivateFieldCouldBeFinal" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="PublicFinalizeMethod" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="PublicInstanceField" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="PublicMethodsBeforeNonPublicMethods" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="RandomDoubleCoercedToZero" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="RemoveAllOnSelf" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="RequiredRegex" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="RequiredString" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ReturnNullFromCatchBlock" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ReturnsNullInsteadOfEmptyArray" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ReturnsNullInsteadOfEmptyCollection" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="SerialPersistentFields" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="SerialVersionUID" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="SerializableClassMustDefineSerialVersionUID" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="SimpleDateFormatMissingLocale" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="SpaceAfterCatch" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="SpaceAfterClosingBrace" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="SpaceAfterComma" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="SpaceAfterFor" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="SpaceAfterIf" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="SpaceAfterOpeningBrace" enabled="false" level="ERROR" enabled_by_default="false">
      <option name="ignoreEmptyBlock" value="true" />
    </inspection_tool>
    <inspection_tool class="SpaceAfterSemicolon" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="SpaceAfterSwitch" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="SpaceAfterWhile" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="SpaceAroundClosureArrow" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="SpaceAroundMapEntryColon" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="SpaceAroundOperator" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="SpaceBeforeClosingBrace" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="SpaceBeforeOpeningBrace" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="SpockIgnoreRestUsed" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="StatelessClass" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="StatelessSingleton" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="StaticCalendarField" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="StaticConnection" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="StaticDateFormatField" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="StaticFieldsBeforeInstanceFields" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="StaticMatcherField" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="StaticMethodsBeforeInstanceMethods" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="StaticSimpleDateFormatField" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="SwallowThreadDeath" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="SynchronizedOnBoxedPrimitive" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="SynchronizedOnGetClass" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="SynchronizedOnReentrantLock" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="SynchronizedOnString" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="SynchronizedOnThis" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="SynchronizedReadObjectMethod" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="SystemErrPrint" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="SystemOutPrint" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="TernaryCouldBeElvis" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ThisReferenceEscapesConstructor" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ThreadGroup" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ThrowError" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ThrowException" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ThrowExceptionFromFinallyBlock" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ThrowNullPointerException" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ThrowRuntimeException" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ThrowThrowable" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="ToStringReturnsNull" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="TrailingWhitespace" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryBigDecimalInstantiation" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryBigIntegerInstantiation" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryBooleanExpression" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="UnnecessaryBooleanInstantiation" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryCallForLastElement" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryCallToSubstring" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryCast" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryCatchBlock" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryCollectCall" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryCollectionCall" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryDefInFieldDeclaration" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryDefInMethodDeclaration" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryDefInVariableDeclaration" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryDotClass" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryDoubleInstantiation" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryElseStatement" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryFail" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="UnnecessaryFinalOnPrivateMethod" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryFloatInstantiation" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryGString" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryGetter" enabled="true" level="ERROR" enabled_by_default="true">
      <option name="ignoreMethodNames" value="isEmpty" />
    </inspection_tool>
    <inspection_tool class="UnnecessaryGroovyImport" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryIfStatement" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryInstanceOfCheck" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryInstantiationToGetClass" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryIntegerInstantiation" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryLongInstantiation" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryModOne" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryNullCheck" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryNullCheckBeforeInstanceOf" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryObjectReferences" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryOverridingMethod" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryPackageReference" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryParenthesesForMethodCallWithClosure" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryPublicModifier" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryReturnKeyword" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="UnnecessarySafeNavigationOperator" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessarySelfAssignment" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessarySetter" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryStringInstantiation" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessarySubstring" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryTernaryExpression" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryToString" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnnecessaryTransientModifier" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnsafeArrayDeclaration" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnsupportedRule_org_codenarc_rule_design_CloneWithoutCloneableRule" enabled="false" level="ERROR" enabled_by_default="false">
      <option name="name" value="Extended rule is not supported" />
    </inspection_tool>
    <inspection_tool class="UnsupportedRule_org_codenarc_rule_enhanced_MissingOverrideAnnotationRule" enabled="false" level="ERROR" enabled_by_default="false">
      <option name="name" value="Extended rule is not supported" />
    </inspection_tool>
    <inspection_tool class="UnsupportedRule_org_codenarc_rule_junit_JUnitAssertEqualsConstantActualValueRule" enabled="false" level="ERROR" enabled_by_default="false">
      <option name="name" value="Extended rule is not supported" />
    </inspection_tool>
    <inspection_tool class="UnsupportedRule_org_codenarc_rule_security_UnsafeImplementationAsMapRule" enabled="false" level="ERROR" enabled_by_default="false">
      <option name="name" value="Extended rule is not supported" />
    </inspection_tool>
    <inspection_tool class="UnusedArray" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnusedImport" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnusedMethodParameter" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnusedObject" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnusedPrivateField" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnusedPrivateMethod" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnusedPrivateMethodParameter" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UnusedVariable" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UseAssertEqualsInsteadOfAssertTrue" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="UseAssertFalseInsteadOfNegation" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="UseAssertNullInsteadOfAssertEquals" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="UseAssertSameInsteadOfAssertTrue" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="UseAssertTrueInsteadOfAssertEquals" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="UseAssertTrueInsteadOfNegation" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="UseCollectMany" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UseCollectNested" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="UseOfNotifyMethod" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="VariableName" enabled="true" level="WEAK WARNING" enabled_by_default="true" />
    <inspection_tool class="VariableTypeRequired" enabled="false" level="ERROR" enabled_by_default="false" />
    <inspection_tool class="VectorIsObsolete" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="VolatileLongOrDoubleField" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="WaitOutsideOfWhileLoop" enabled="true" level="ERROR" enabled_by_default="true" />
    <inspection_tool class="WhileStatementBraces" enabled="true" level="ERROR" enabled_by_default="true" />
  </profile>
</component>
