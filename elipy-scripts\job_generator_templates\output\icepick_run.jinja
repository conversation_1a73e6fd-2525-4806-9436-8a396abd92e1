{#
    Command:
        icepick_run
            short_help: Run icepick test.
            context_settings: dict(ignore_unknown_options=True)

    Arguments:
        platform
            required: True
        run_args
            nargs: -1
            type: click.UNPROCESSED

    Required variables:
        test_suites
            help: The test suite to run followed by pool-type. Multiple.
            multiple: True
            required: True
        code_branch
            default: None
            required: True
            help: Branch to fetch code from.
        code_changelist
            default: None
            required: True
            help: Which code changelist to use.
        data_changelist
            default: None
            help: Which data changelist to use.
            required: True

    Optional variables:
        settings_files
            help: Settings files relative to the data folder.
            multiple: True
        assets
            default: None
            help: Assets to build before running icepick.
            multiple: True
        asset
            default: ['preflightlevels']
            help: Deprecated parameter. Use --assets instead.
            multiple: True
        client_build_id
            default: None
            help: Which client build to use.
        server_build_id
            default: None
            help: Which server build to use.
        data_branch
            help: Branch to fetch Avalanche state from.
            default: ''
        datadir
            default: data
            help: Specify which data directory to use (relative to GAME_ROOT).
        frosting_report
            default: True
            help: Toggle to send frosting report.
        lease
            default: False
            help: Lease, run remotely.
        build_type
            default: static
            type: click.Choice(['static', 'dll', None])
            help: Static
        autobuild
            default: True
            help: Autobuild
        config
            default: final
            help: Config
        import_avalanche_state
            default: False
            help: Imports Avalanche state from filer.
        icepick_cook
            default: False
            help: Cook data in icepick.
        test_definition
            help: Which test to register.
            default: test_def
        need_game_server
            default: False
            help: Set this if the test requires a game server build.
        region
            help: Which region to deploy for (default is ww).
            default: ww
        server_region
            help: Which server region to deploy for (default is ww).
            default: ww
        server_config
            help: Server config (default is config value).
            default: None
        package_type
            help: Which package type to test.
            default: None
        server_assets
            help: what server asset to build if needed.
            default: None
            multiple: True
        fetch_frosted
            default: False
            help: Fetch FrostEd binaries if enabled.
        is_frosted
            default: False
            help: Flag to determine if it is a FrostEd prototype job run.
        licensee
            multiple: True
            default: None
            help: Licensee to use
        password
            default: None
            help: User credentials to authenticate to package server
        email
            default: None
            help: User email to authenticate to package server
        domain_user
            default: None
            help: The user to authenticate to package server as DOMAIN\user
        show_test_results
            default: False
            help: Report test failures on jenkins
        run_bilbo
            default: False
            help: Whether or not to report test results to the configured metadata services
        test_group
            default: nogroup
            help: Set test group name as part of metadata.
        inert_run
            default: False
            help: Run but don't do anything
        extra_framework_args
            default: None
            help: Extra arguments for Icepick to pass to any Framework commands it starts
        server_platform
            default: server
            help: Which server platform to use, can be either server (windows) or linuxserver.
        use_shift_build
            default: False
            type: bool
            help: Should these tests use a Shift build or not
        use_spin_build
            default: False
            type: bool
            help: Should these tests use a Spin build or not
        fetch_tests
            default: False
            help: Fetch test files using Tests_Win64-Dll_release_Files.txt
        code_files
            default: None
            help: What code files to sync to head
            multiple: True
        code_p4_port
            default: dice-p4buildedge02-fb.dice.ad.ea.com:2001
            help: Code P4 to sync files from
        data_files
            default: None
            help: What data files to sync to head
            multiple: True
        data_p4_port
            default: p4-tunguska-build01.dice.ad.ea.com:2001
            help: Data P4 to sync files from
        p4_user
            default: f'dice\\{getpass.getuser()}'
            help: What users should be used to connect to P4
        sync_files
            default: False
            help: Should sync the specified files to head
        clean
            type: click.BOOL
            default: False
            help: Perform a clean for this test
        custom_test_suite_data
            default: ''
            help: Custom test suite metadata to pass to Icepick.
        clean_master_version_check
            default: False
            help: Run clean on master version update.
        cook_type
            type: click.Choice([t.name.lower() for t in CookTypes])
            default: CookTypes.COOK.name.lower()
            help: decide which method is called to cook data. [cook, icepick_cook, no_cook].
        db_name_prefix
            help: prefix for db name when specifying cook type as AVALANCHE_STORAGE
        custom_tag
            default: None
            help: Extra folder before changelist to fetch code from.
        filer_user
            default: None
            help: username for creating a filer connection
        filer_password
            default: None
            help: password for creating a filer connection
        enable_hailstorm
            type: bool
            default: True
            help: Should Icepick cook use the hailstorm server or not
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    icepick_run
    platform {{ platform }}
    run-args {{ run_args }}
    --test-suites {{ test_suites }}
    --code-branch {{ code_branch }}
    --code-changelist {{ code_changelist }}
    --data-changelist {{ data_changelist }}
    {%- if settings_files %}
    --settings-files {{ settings_files }}
    {%- endif %}
    {%- if assets %}
    --assets {{ assets }}
    {%- endif %}
    {%- if asset %}
    --asset {{ asset }}
    {%- endif %}
    {%- if client_build_id %}
    --client-build-id {{ client_build_id }}
    {%- endif %}
    {%- if server_build_id %}
    --server-build-id {{ server_build_id }}
    {%- endif %}
    {%- if data_branch %}
    --data-branch {{ data_branch }}
    {%- endif %}
    {%- if datadir %}
    --datadir {{ datadir }}
    {%- endif %}
    {%- if frosting_report %}
    --frosting-report {{ frosting_report }}
    {%- endif %}
    {%- if lease %}
    --lease {{ lease }}
    {%- endif %}
    {%- if build_type %}
    --build-type {{ build_type }}
    {%- endif %}
    {%- if autobuild %}
    --autobuild {{ autobuild }}
    {%- endif %}
    {%- if config %}
    --config {{ config }}
    {%- endif %}
    {%- if import_avalanche_state %}
    --import-avalanche-state {{ import_avalanche_state }}
    {%- endif %}
    {%- if icepick_cook %}
    --icepick-cook {{ icepick_cook }}
    {%- endif %}
    {%- if test_definition %}
    --test-definition {{ test_definition }}
    {%- endif %}
    {%- if need_game_server %}
    --need-game-server {{ need_game_server }}
    {%- endif %}
    {%- if region %}
    --region {{ region }}
    {%- endif %}
    {%- if server_region %}
    --server-region {{ server_region }}
    {%- endif %}
    {%- if server_config %}
    --server-config {{ server_config }}
    {%- endif %}
    {%- if package_type %}
    --package-type {{ package_type }}
    {%- endif %}
    {%- if server_assets %}
    --server-assets {{ server_assets }}
    {%- endif %}
    {%- if fetch_frosted %}
    --fetch-frosted {{ fetch_frosted }}
    {%- endif %}
    {%- if is_frosted %}
    --is-frosted {{ is_frosted }}
    {%- endif %}
    {%- if licensee %}
    --licensee {{ licensee }}
    {%- endif %}
    {%- if password %}
    --password {{ password }}
    {%- endif %}
    {%- if email %}
    --email {{ email }}
    {%- endif %}
    {%- if domain_user %}
    --domain-user {{ domain_user }}
    {%- endif %}
    {%- if show_test_results %}
    --show-test-results {{ show_test_results }}
    {%- endif %}
    {%- if run_bilbo %}
    --run-bilbo {{ run_bilbo }}
    {%- endif %}
    {%- if test_group %}
    --test-group {{ test_group }}
    {%- endif %}
    {%- if inert_run %}
    --inert-run {{ inert_run }}
    {%- endif %}
    {%- if extra_framework_args %}
    --extra-framework-args {{ extra_framework_args }}
    {%- endif %}
    {%- if server_platform %}
    --server-platform {{ server_platform }}
    {%- endif %}
    {%- if use_shift_build %}
    --use-shift-build {{ use_shift_build }}
    {%- endif %}
    {%- if use_spin_build %}
    --use-spin-build {{ use_spin_build }}
    {%- endif %}
    {%- if fetch_tests %}
    --fetch-tests {{ fetch_tests }}
    {%- endif %}
    {%- if code_files %}
    --code-files {{ code_files }}
    {%- endif %}
    {%- if code_p4_port %}
    --code-p4-port {{ code_p4_port }}
    {%- endif %}
    {%- if data_files %}
    --data-files {{ data_files }}
    {%- endif %}
    {%- if data_p4_port %}
    --data-p4-port {{ data_p4_port }}
    {%- endif %}
    {%- if p4_user %}
    --p4-user {{ p4_user }}
    {%- endif %}
    {%- if sync_files %}
    --sync-files {{ sync_files }}
    {%- endif %}
    {%- if clean %}
    --clean {{ clean }}
    {%- endif %}
    {%- if custom_test_suite_data %}
    --custom-test-suite-data {{ custom_test_suite_data }}
    {%- endif %}
    {%- if clean_master_version_check %}
    --clean-master-version-check {{ clean_master_version_check }}
    {%- endif %}
    {%- if cook_type %}
    --cook-type {{ cook_type }}
    {%- endif %}
    {%- if db_name_prefix %}
    --db-name-prefix {{ db_name_prefix }}
    {%- endif %}
    {%- if custom_tag %}
    --custom-tag {{ custom_tag }}
    {%- endif %}
    {%- if filer_user %}
    --filer-user {{ filer_user }}
    {%- endif %}
    {%- if filer_password %}
    --filer-password {{ filer_password }}
    {%- endif %}
    {%- if enable_hailstorm %}
    --enable-hailstorm {{ enable_hailstorm }}
    {%- endif %}
  displayName: elipy icepick_run
