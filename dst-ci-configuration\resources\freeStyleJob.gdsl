/**
 * Manually added documentation for freeStyleJob: https://jenkinsci.github.io/job-dsl-plugin/#path/freeStyleJob
 */
def closures = context(scope: closureScope())
contributor(closures) {
    if (enclosingCall('with') || enclosingCall('job')) {
        method(name: 'stringParam', params: [parameterName: 'java.lang.String', defaultValue: 'java.lang.String', description: 'java.lang.String'], doc: 'Defines a simple text parameter, where users can enter a string value.')
        method(name: 'stringParam', params: [body: 'Closure'], doc: 'Defines a simple text parameter, where users can enter a string value')
        if (enclosingCall('stringParam')) {
            method(name: 'name', params: [name: 'java.lang.String'], doc: 'The name of the parameter')
            method(name: 'defaultValue', params: [defaultValue: 'java.lang.String'], doc: 'Sets a default value for the item.')
            method(name: 'trim', params: [name: 'java.lang.Boolean'], doc: 'Whether or not to trim the input')
            method(name: 'description', params: [description: 'java.lang.String'], doc: 'Sets a description for the parameter.')
        }
        method(name: 'properties', type: 'Object', params: [properties: 'Map'], doc: 'Set job properties')

        method(name: 'wrappers', params: [body: 'Closure'], doc: 'Adds pre/post actions to the job.')
        if (enclosingCall('wrappers')) {
            method(name: 'allocatePorts', params: [portsArg: 'String[]', body: 'Closure'], doc: 'Allocate ports for build executions to prevent conflicts between build jobs competing for a single port number.')
            method(name: 'allocatePorts', params: [body: 'Closure'], doc: 'Allocate ports for build executions to prevent conflicts between build jobs competing for a single port number.')
            if (enclosingCall('allocatePorts')) {
                method(name: 'glassfish', params: [port: 'java.lang.String', user: 'java.lang.String', password: 'java.lang.String'], doc: 'Allocates a GlassFish JMX port that lets Jenkins shut down a run-away GlassFish through JMX.')
                method(name: 'port', params: [port: 'java.lang.String', ports: 'java.lang.String...'], doc: 'Allocates plain TCP ports.')
                method(name: 'tomcat', params: [port: 'java.lang.String', password: 'java.lang.String'], doc: 'Allocates a Tomcat shutdown port that lets Jenkins shut down a run-away Tomcat through the shut down port.')
            }
            method(name: 'buildName', params: [nameTemplate: 'java.lang.String'], doc: 'Sets the display name of a build.')
            method(name: 'buildUserVars', doc: 'Adds a number of environment variables with information about the current user.')
            method(name: 'colorizeOutput', params: [colorMap: 'java.lang.String'], doc: 'Renders ANSI escape sequences, including color, to console output.')
            method(name: 'colorizeOutput', doc: 'Renders ANSI escape sequences, including color, to console output.')
            method(name: 'credentialsBinding', params: [body: 'Closure'], doc: 'Binds environment variables to credentials.')
            if (enclosingCall('credentialsBinding')) {
                method(name: 'file', params: [variable: 'java.lang.String', credentials: 'java.lang.String'], doc: 'Copies the file given in the credentials to a temporary location, then sets the variable to that location.')
                method(name: 'string', params: [variable: 'java.lang.String', credentials: 'java.lang.String'], doc: 'Sets a variable to the text given in the credentials.')
                method(name: 'usernamePassword', params: [variable: 'java.lang.String', credentials: 'java.lang.String'], doc: 'Sets a variable to the username and password given in the credentials, separated by a colon (:).')
                method(name: 'usernamePassword', params: [userVariableName: 'java.lang.String', passwordVariableName: 'java.lang.String', credentials: 'java.lang.String'], doc: 'Sets one variable to the username and one variable to the password given in the credentials.')
                method(name: 'zipFile', params: [variable: 'java.lang.String', credentials: 'java.lang.String'], doc: 'Unpacks the ZIP file given in the credentials to a temporary directory, then sets the variable to that location.')
            }
            method(name: 'injectPasswords', params: [body: 'Closure'], doc: 'Injects passwords as environment variables into the job.')
            if (enclosingCall('injectPasswords')) {
                method(name: 'injectGlobalPasswords', params: [injectGlobalPasswords: 'java.lang.Boolean'], doc: 'Injects global passwords provided by Jenkins configuration.')
                method(name: 'injectGlobalPasswords', doc: 'Injects global passwords provided by Jenkins configuration.')
                method(name: 'maskPasswordParameters', params: [maskPasswordParameters: 'java.lang.Boolean'], doc: 'Masks passwords provided by build parameters.')
                method(name: 'maskPasswordParameters', doc: 'Masks passwords provided by build parameters.')
            }
            method(name: 'maskPasswords', doc: 'Masks the passwords that occur in the console output.')
            method(name: 'timeout', params: [body: 'Closure'], doc: 'Add a timeout to the build job.')
            if (enclosingCall('timeout')) {
                method(name: 'abortBuild', doc: 'Aborts the build.')
                method(name: 'absolute', params: [minutes: 'java.lang.Integer'], doc: 'Aborts the build based on a fixed time-out.')
                method(name: 'absolute', params: [minutes: 'java.lang.String'], doc: 'Aborts the build based on a fixed time-out.')
                method(name: 'elastic', params: [percentage: 'java.lang.Integer', numberOfBuilds: 'java.lang.Integer', minutesDefault: 'java.lang.Integer'], doc: 'Defines time to wait before killing the build as a percentage of the mean of the duration of the last successful builds.')
                method(name: 'elastic', doc: 'Defines time to wait before killing the build as a percentage of the mean of the duration of the last successful builds.')
                method(name: 'failBuild', doc: 'Marked the build as failed.')
                method(name: 'likelyStuck', doc: 'Uses a heuristics based approach to detect builds that are suspiciously running for a long time.')
                method(name: 'noActivity', params: [seconds: 'java.lang.Integer'], doc: 'Aborts the build when the specified seconds have passed since the last log output.')
                method(name: 'noActivity', doc: 'Aborts the build when the specified seconds have passed since the last log output.')
                method(name: 'writeDescription', params: [description: 'java.lang.String'], doc: 'Sets the build description.')
            }
            method(name: 'timestamps', doc: 'Adds timestamps to the console log.')
            method(name: 'toolenv', params: [tools: 'java.lang.String...'], doc: 'Downloads the specified tools, if needed, and puts the path to each of them in the build\'s environment.')
        }
        method(name: 'label', params: [labelExpression: 'java.lang.String'], doc: 'Label which specifies which nodes this job can run on.')
        method(name: 'label', doc: 'Label which specifies which nodes this job can run on.')
        method(name: 'customWorkspace', params: [workspacePath: 'java.lang.String'], doc: 'Defines that a project should use the given directory as a workspace instead of the default workspace location.')
        method(name: 'steps', params: [body: 'Closure'], doc: 'Adds build steps to the jobs.')
        if (enclosingCall('steps')) {
            method(name: 'batchFile', params: [command: 'java.lang.String'], doc: 'Runs a Windows batch script.')
        }
        method(name: 'publishers', params: [body: 'Closure'], doc: 'Adds post-build actions to the job.')
        if (enclosingCall('publishers')) {
            method(name: 'flexiblePublish', params: [body: 'Closure'], doc: 'Add conditional post-build actions.')
            method(name: 'conditionalAction', params: [body: 'Closure'], doc: 'Adds a conditional action.')
            method(name: 'condition', params: [body: 'Closure'], doc: 'Specifies the condition to evaluate before executing the build steps.')
            method(name: 'alwaysRun', doc: 'Runs the build steps no matter what.')
            method(name: 'status', params: [worstResult: 'java.lang.String', bestResult: 'java.lang.String'], doc: 'Runs the build steps if the current build status is within the configured range.')
            method(name: 'archiveArtifacts', params: [body: 'Closure'], doc: 'Archives artifacts with each build.')
            method(name: 'archiveArtifacts', params: [glob: 'java.lang.String', excludeGlob: 'java.lang.Boolean'], doc: 'Archives artifacts with each build.')
            method(name: 'archiveArtifacts', params: [glob: 'java.lang.String'], doc: 'Archives artifacts with each build.')
            method(name: 'filesMatch', params: [includes: 'java.lang.String'], doc: 'Runs the build steps if one or more files match the selectors.')
            method(name: 'shell', params: [command: 'java.lang.String'], doc: 'Runs a shell script.')
            method(name: 'allowEmpty', params: [allowEmpty: 'java.lang.Boolean'], doc: 'If set, does not fail the build if archiving returns nothing.')
            method(name: 'allowEmpty', doc: 'If set, does not fail the build if archiving returns nothing.')
            method(name: 'fingerprint', params: [fingerprint: 'java.lang.Boolean'], doc: 'Fingerprints all archived artifacts.')
            method(name: 'fingerprint', doc: 'Fingerprints all archived artifacts.')
            method(name: 'allowBrokenBuildClaiming', doc: 'Allows to claim unsuccessful builds.')
            method(name: 'pattern', params: [glob: 'java.lang.String'], doc: 'Specifies the files to archive.')
            method(name: 'postBuildScript', params: [body: 'Closure'], doc: 'Execute a set of scripts at the end of the build.')
            if (enclosingCall('postBuildScript')) {
                method(name: 'markBuildUnstable', params: [markBuildUnstable: 'java.lang.Boolean'], doc: 'Mandatory field to mark build unstable or not.')
                method(name: 'buildSteps', params: [body: 'Closure'], doc: 'Adds build steps post-build scripts.')
                if (enclosingCall('buildSteps')) {
                    method(name: 'postBuildStep', params: [body: 'Closure'], doc: 'Adds post-build step.')
                    if (enclosingCall('postBuildStep')) {
                        method(name: 'stopOnFailure', params: [stopOnFailure: 'java.lang.Boolean'], doc: 'Mandatory field to stop on failure or not.')
                        method(name: 'results', params: [results: 'List<String>'], doc: 'List of result string that evaluate before executing post step.')
                    }
                }
            }
            method(name: 'onlyIfBuildSucceeds', params: [onlyIfBuildSucceeds: 'java.lang.Boolean'], doc: 'If set, runs the build steps only if the build was successful.')
            method(name: 'onlyIfBuildSucceeds', doc: 'If set, runs the build steps only if the build was successful.')
            method(name: 'onlyIfBuildFails', params: [onlyIfBuildFails: 'java.lang.Boolean'], doc: 'If set, runs the build steps only if the build failed.')
            method(name: 'onlyIfBuildFails', doc: 'If set, runs the build steps only if the build failed.')
            method(name: 'downstreamParameterized', params: [body: 'Closure'], doc: 'Triggers new parametrized builds.')
            method(name: 'trigger', params: [projects: 'java.lang.String', body: 'Closure'], doc: 'Adds a trigger for parametrized builds.')
            method(name: 'trigger', params: [projects: 'List<String>', body: 'Closure'], doc: 'Adds a trigger for parametrized builds.')
            method(name: 'parameters', params: [body: 'Closure'], doc: 'Adds parameter values for the projects to trigger.')
            method(name: 'currentBuild', doc: 'Copies parameters from the current build, except for file parameters.')
            method(name: 'predefinedProps', params: [predefinedPropsMap: 'Map<String, String>'], doc: 'Adds parameters.')
        }
        method(name: 'booleanParam', params: [parameterName: 'java.lang.String', defaultValue: 'java.lang.Boolean', description: 'java.lang.String'], doc: 'Defines a simple boolean parameter.')
        method(name: 'booleanParam', params: [parameterName: 'java.lang.String'], doc: 'Defines a simple boolean parameter.')
        method(name: 'booleanParam', params: [body: 'Closure'], doc: 'Defines a simple boolean parameter.')
        if (enclosingCall('booleanParam')) {
            method(name: 'name', params: [name: 'java.lang.String'])
            method(name: 'defaultValue', params: [defaultValue: 'java.lang.Boolean'])
            method(name: 'description', params: [name: 'java.lang.String'])
        }
        method(name: 'multiscm', params: [body: 'Closure'], doc: 'Allows a job to check out sources from multiple SCM providers.')
        if (enclosingCall('multiscm')) {
            method(name: 'perforce', params: [credentials: 'java.lang.String', body: 'Closure'], doc: 'Add Perforce SCM source using Perforce p4 plugin.')
            method(name: 'configure', params: [closure: 'Closure'], doc: 'Allows direct manipulation of the generated XML.')
            method(name: 'workspace ', params: [body: 'Closure'], doc: 'Sets the appropriate Perforce workspace behaviour.')
            if (enclosingCall('workspace')) {
                method(name: 'manual ', params: [workspaceName: 'java.lang.String', viewSpec: 'java.lang.String'], doc: 'Set up a manual workspace with workspace name and view spec.')
            }
        }
    }
}
