package com.ea.lib.model.branchsettings

/**
 * Model used to configure <branch-name>.unittests.start
 */
class UnitTestsConfiguration {
    /**
     * Whether or not the job is enabled
     */
    boolean enabled = true
    /**
     * Agent labels
     */
    String label = 'statebuild'
    /**
     * Overwrite the default filepaths to ignore in Icepick code in Perforce (doesn't trigger SCM change)
     */
    List<String> ignorePaths = []
    /**
     * Filter branch
     */
    String nonVirtualCodeBranch
    /**
     * Filter folder
     */
    String nonVirtualCodeFolder
    /**
     * Poll SCM CRON trigger
     */
    String trigger = 'H/5 * * * 1-6\nH/5 6-23 * * 7'
    /**
     * Job timeout in hours
     */
    int timeoutHours = 3

    /**
     * Job timeout in hours
     * @return the timeout
     */
    int getTimeoutMinutes() {
        return timeoutHours * 60
    }

    @Override
    String toString() {
        return "{com.ea.lib.model.branchsettings.UnitTestsConfiguration: enabled: ${enabled}}"
    }
}
