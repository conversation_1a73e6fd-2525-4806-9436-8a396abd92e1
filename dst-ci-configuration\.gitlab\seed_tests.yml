.seeds_tests_template: &seeds_tests_template
  stage: test
  extends:
    - .gradle
    - .test_artifacts
  before_script:
    - date
  after_script:
    - date

seeds_autotests_seed:
  <<: *seeds_tests_template
  script:
    - $GRADLEW seed --tests *autotests_seed*

seeds_basic_jobs:
  <<: *seeds_tests_template
  script:
    - $GRADLEW seed --tests *basic_jobs*

seeds_cobra_jobs:
  <<: *seeds_tests_template
  script:
    - $GRADLEW seed --tests *cobra_jobs*

seeds_dvcs_seed:
  <<: *seeds_tests_template
  script:
    - $GRADLEW seed --tests *dvcs_seed*

seeds_integrations_seed:
  <<: *seeds_tests_template
  script:
    - $GRADLEW seed --tests *integrations_seed*

seeds_maintenance_seed:
  <<: *seeds_tests_template
  script:
    - $GRADLEW seed --tests *maintenance_seed*

seeds_preflight_seed:
  <<: *seeds_tests_template
  script:
    - $GRADLEW seed --tests *preflight_seed*

seeds_coverity_seed:
  <<: *seeds_tests_template
  script:
    - $GRA<PERSON><PERSON><PERSON> seed --tests *coverity_seed*

seeds_test_jobs_seed:
  <<: *seeds_tests_template
  script:
    - $<PERSON><PERSON><PERSON><PERSON> seed --tests *test_jobs_seed*

seeds_util_jobs:
  <<: *seeds_tests_template
  script:
    - $GRADLEW seed --tests *util_jobs*

seeds_views:
  <<: *seeds_tests_template
  script:
    - $GRADLEW seed --tests *views*

seeds_game_tools_jobs:
  <<: *seeds_tests_template
  script:
    - $GRADLEW seed --tests *game_tools_jobs*
