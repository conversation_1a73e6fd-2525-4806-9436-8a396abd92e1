$startDate = Get-Date
# Percentage free space to be left on disk.
$PercentFree =.08
# Path to file that will contain the zero content.
$FilePath = "C:\ThinSAN.tmp"

Write-Host "start cleanup on $env:COMPUTERNAME"

# Check and make sure the file doesn't already exist so we don't clobber someone's data.
if((Test-Path $FilePath)) {
    Write-Host -Message "The file $FilePath already exists. Deleting..."
    try {
        Remove-Item -Path $FilePath -Force
    }
    catch {
        Write-Error "Could not delete $FilePath"
        exit 1
    }
    Write-Host "File deleted."
}

# Get a reference to the volume so we can calculate the desired file size later.
$Volume = gwmi win32_volume -filter "name='C:\\'"
if($Volume) {
    # I have not tested for the optimum IO size ($ArraySize), 64kb is what sdelete.exe uses.
    # 2025-02-26(jthoren): I tested locally some various sizes. 1MB seems to be a sweet spot locally on my machine.
    $ArraySize = 1MB
    # Calculate the amount of space to leave on the disk.
    [int64]$SpaceToLeave = $Volume.Capacity * $PercentFree
    # Calculate the file size needed to leave the desired amount of space.
    [int64]$FileSize = $Volume.FreeSpace - $SpacetoLeave
    # Create an array of zeroes to write to disk.
    $ZeroArray = new-object byte[]($ArraySize)

    # Open a file stream to our file
    $Stream = [io.File]::OpenWrite($FilePath)
    # Start a try/finally block so we don't leak file handles if any exceptions occur.
    try {
        # Keep track of how much data we've written to the file.
        [int64]$CurFileSize = 0
        Write-Host "space to clean: $($FileSize / 1gb) GB"
        while($CurFileSize -lt $FileSize) {
            # Write the entire zero array buffer out to the file stream.
            $Stream.Write($ZeroArray,0, $ZeroArray.Length)
            # Increment our file size by the amount of data written to disk.
            $CurFileSize += $ZeroArray.Length
            if($CurFileSize % 1gb -eq 0) {
                Write-Host "space cleaned: $($CurFileSize / 1gb) GB"
            }
        }
    }
    finally {
        # Always close our file stream, even if an exception occurred.
        if($Stream) {
            $Stream.Close()
        }
        # Always delete the file if we created it, even if an exception occurred.
        if((Test-Path $FilePath)) {
            Remove-Item -Path $FilePath -Force
        }
    }
}
else {
    Write-Error "Unable to locate a volume mounted at $Root"
    exit 1
}

Write-Host "cleanup completed on $env:COMPUTERNAME"
Write-Host "took" ((Get-Date) - $startDate)
