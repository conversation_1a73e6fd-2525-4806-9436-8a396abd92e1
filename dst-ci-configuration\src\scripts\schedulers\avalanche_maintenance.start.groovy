package scripts.schedulers

import com.ea.lib.LibJenkins
import hudson.model.Node
import hudson.model.Result
import jenkins.model.Jenkins

import java.util.concurrent.TimeUnit

def project = ProjectClass(env.project_name)

/**
 * avalanche_maintenance.start.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
        timeout(time: 30, unit: TimeUnit.HOURS)
    }
    stages {
        stage('Trigger Avalanche maintenance jobs') {
            steps {
                script {
                    def jobs = [:]
                    currentBuild.displayName = env.JOB_NAME + '.' + env.BUILD_NUMBER
                    def final_result = Result.SUCCESS

                    for (Node node in Jenkins.get().nodes) {
                        def node_name = node.nodeName
                        Boolean isCloud = node.labelString.contains('cloud')
                        def skipMaintenance = ['av2', 'kraken', 'sc2'].any { node_name.startsWith("$it-") }
                        if (node.toComputer().online && !skipMaintenance) {
                            jobs["node_${node_name}"] = {
                                def downstream_job = build(
                                    job: isCloud ? 'avalanche_maintenance_azure' : 'avalanche_maintenance',
                                    parameters: [string(name: 'Node', value: node_name)],
                                    propagate: false,
                                )
                                final_result = final_result.combine(downstream_job.rawBuild.result)
                                LibJenkins.printRunningJobs(this)
                            }
                        }
                    }

                    // Trigger all jobs
                    parallel(jobs)
                    currentBuild.result = final_result.toString()

                    SlackMessageNew(currentBuild, '#cobra-outage-avalanche', project.short_name)

                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
}
