import hudson.model.ParametersAction
import hudson.model.StringParameterValue

/**
 * EnvInject.groovy
 * Updates the parameters in a job that will later be available for other jobs.
 */
void call(def currentBuild, def injectMap) {
    echo '[EnvInject] Start'
    def old_params = currentBuild.rawBuild.getAction(ParametersAction)
    def new_params = null

    List<StringParameterValue> params_list = []
    injectMap.each { param_name, param_value ->
        if (param_value == null) {
            throw new IllegalArgumentException('Not allowed to add parameter ' + param_name + ' with the value null, aborting.')
        }
        params_list.add(new StringParameterValue(param_name, param_value))
    }

    if (old_params != null) {
        new_params = old_params.createUpdated(params_list)
        // To remove old ParametersAction before we add it again with new key-value pairs
        currentBuild.rawBuild.removeActions(ParametersAction)
    } else {
        new_params = new ParametersAction(params_list)
    }

    currentBuild.rawBuild.actions.add(new_params)
    echo '[EnvInject] End'
}
