"""
gamescripts_utils.py
"""
import os
from elipy2 import LOGGER, core, SETTINGS, frostbite_core
from elipy2.exceptions import ELIPYException


def generate_gamescripts():
    """
    Util for building gamescripts.
    They are used for Kennel tests and are mandatory for some game titles when running frosty
    """
    # Import the main build gamescripts logic
    try:
        script_path = SETTINGS.get("gamescripts_script_path")

        # This is horrible. We need to find a better way around this.
        build_gamescripts = core.import_module_from_file(
            "buildgamescripts",
            os.path.join(frostbite_core.get_tnt_root(), script_path),
        )
    except Exception as exc:
        LOGGER.error("Unable to import Code.FIFAGame.fbcli.buildgamescripts")
        raise exc

    # Run generate_gamescripts build job
    exit_code = build_gamescripts.call_target("gensln")
    if exit_code not in (0, None):
        raise ELIPYException("Unable to run gensln. Please reach out to build or gamescripts team.")
    LOGGER.info("Gensln ran succesfuly")

    exit_code = build_gamescripts.call_target("buildsln")
    if exit_code not in (0, None):
        raise ELIPYException(
            "Unable to run buildsln. Please reach out to build or gamescripts team."
        )
    LOGGER.info("buildsln ran succesfuly")
