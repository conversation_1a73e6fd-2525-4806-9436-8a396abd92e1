{#
    Command:
        symbol_store_upload
            short_help: Copy symbols to symbol store.

    Arguments:

    Required variables:
        branch
            required: True
        changelist
            required: True
            type: int
        config
            required: True
        platform
            required: True

    Optional variables:
        email
            default: None
            help: User email to authenticate to package server
        domain_user
            default: None
            help: The user to authenticate to package server as <PERSON><PERSON><PERSON><PERSON>\user
        password
            default: None
            help: User password to authenticate to package server
        compress_symbols
            default: True
            type: bool
            help: Compress symbols before uploading them to the symstore.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    symbol_store_upload
    --branch {{ branch }}
    --changelist {{ changelist }}
    --config {{ config }}
    --platform {{ platform }}
    {%- if email %}
    --email {{ email }}
    {%- endif %}
    {%- if domain_user %}
    --domain-user {{ domain_user }}
    {%- endif %}
    {%- if password %}
    --password {{ password }}
    {%- endif %}
    {%- if compress_symbols %}
    --compress-symbols {{ compress_symbols }}
    {%- endif %}
  displayName: elipy symbol_store_upload
