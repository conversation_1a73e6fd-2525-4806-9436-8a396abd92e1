package com.ea.lib.jobs

import com.ea.lib.jobsettings.GitLabRepoSettings
import javaposse.jobdsl.dsl.DslFactory
import javaposse.jobdsl.dsl.jobs.WorkflowJob

class LibGitLabRepo {
    /**
     * Adds generic job parameters for Jenkins Registry jobs.
     */
    static WorkflowJob jenkinsRegistryJob(DslFactory dslFactory, def masterFile) {
        return dslFactory.pipelineJob('controller.register') {
            GitLabRepoSettings settings = new GitLabRepoSettings()
            settings.initializeJenkinsRegistryJob(masterFile)
            description(settings.description)
            logRotator(7, 50)
            parameters {
                stringParam('label', 'master', 'What job label to run this job against')
            }
            properties {
                description(settings.description)
                disableConcurrentBuilds()
                disableResume()
                pipelineTriggers {
                    triggers {
                        cron {
                            spec(settings.triggerString)
                        }
                    }
                }
            }
            quietPeriod(0)
            environmentVariables {
                env('EMAIL', settings.email)
                env('IS_PRODUCTION', settings.isProduction)
            }
            definition {
                cpsScm {
                    scm {
                        git {
                            remote {
                                name('origin')
                                credentials('monkey-commons-ssh-v2')
                                url(settings.repoUrl)
                            }
                            branch(settings.repoBranch)
                        }
                    }
                }
            }
        }
    }
}
