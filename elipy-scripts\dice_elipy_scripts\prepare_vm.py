"""
prepare_vm.py
"""
import os
import requests
import subprocess
import time
import click

from dice_elipy_scripts.utils.decorators import throw_if_files_found
from elipy2 import avalanche, core, frostbite_core, local_paths, LOGGER, p4, running_processes
from elipy2.cli import pass_context
from elipy2.exceptions import ELIPYException
from elipy2.telemetry import collect_metrics
from urllib.parse import urlparse


@click.command("prepare_vm", short_help="")
@click.option(
    "--vm-type",
    required=True,
    default="self-hosted",
    type=click.Choice([None, "self-hosted", "azure-hosted"]),
)
@click.option("--processes", default=None)
@click.option("--p4-user", default=None)
@click.option("--p4-port", default=None)
@click.option("--p4-client", default=None)
@click.option(
    "--antifreeze",
    is_flag=True,
    help="For Antifreeze jobs, we need to clean the Antifreeze folder.",
)
@click.option("--avalanche-server", default=None)
@click.option("--hailstorm", default=None)
@click.option("--package-server", default=None)
@click.option("--file-share", default=None)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    vm_type,
    processes,
    p4_user,
    p4_port,
    p4_client,
    antifreeze,
    avalanche_server,
    hailstorm,
    package_server,
    file_share,
):
    """
    Performs prepare (self/azure-hosted) VM environment before handling automation.
    """
    if vm_type == "self-hosted":
        if not processes:
            processes = [
                "Tool.Pipeline_Win64_release_Dll*",
                "MSBuild.exe",  # should be killed before cl.exe
                "cl.exe",
                "Casablanca.Main_Win64*",
                "frostyisotool.exe",
                "orbis-pub-cmd.exe",
                "orbis-ctrl.exe",
                "orbis-symupload.exe",
                "prospero-symupload.exe",
                "orbis-clang.exe",
                "nant.exe",
                "AvalancheCLI.exe",
                "mspdbsrv.exe",
                "vctip.exe",
                "snowcacheserver.exe",
                "Icepick.exe",
                "fbenvcore.exe",
            ]

        # Sleep for 1 min before killing processes that might do a crash dump.
        LOGGER.info("Waiting 60 seconds...")
        time.sleep(60)

        LOGGER.info("Killing processes...")
        running_processes.kill(list(processes))

        LOGGER.info("Deleting ThinSAN.tmp...")
        core.delete_folder(os.path.join("C:/", "ThinSAN.tmp"))

        LOGGER.info("Deleting ps log files")
        core.delete_folder(
            os.path.join(os.getenv("UserProfile"), "Documents", "PS_Logs"),
            close_handles=True,
        )

        if p4_port and p4_client:
            LOGGER.info("Reverting p4 workspace...")
            perforce = p4.P4Utils(port=p4_port, client=p4_client, user=p4_user)
            perforce.revert()
            if antifreeze:
                antifreeze_p4_path = local_paths.get_antifreeze_dir() + os.sep + "..."
                perforce.clean(folder=antifreeze_p4_path)

        LOGGER.info("Deleteing tnt logs...")
        delete_tnt_logs()

    elif vm_type == "azure-hosted":
        setup_opentelemetry_environment()
        confirm_connectivity([avalanche_server, hailstorm, package_server])
        confirm_fileshare(file_share)
        confirm_perforce(p4_port)
        configure_avalanche(avalanche_server)


def delete_tnt_logs():
    """
    Delete Log files in TnT directory
    """
    tnt_path = frostbite_core.get_tnt_root()

    files = os.listdir(tnt_path)
    for item in files:
        if item.endswith(".log") or (item == "FrostyLogFile.txt"):
            os.remove(os.path.join(tnt_path, item))


def _convert_to_string_data(hash_table):
    result = []
    for item in hash_table:
        for key, value in item.items():
            result.append(f"{key}={value}")
    return result


def setup_opentelemetry_environment():
    """
    Setup Open Telemetry environment
    """
    if "OTEL_RESOURCE_ATTRIBUTES" in os.environ:
        otel_resource_attributes = dict(
            item.split("=", 1) for item in os.environ["OTEL_RESOURCE_ATTRIBUTES"].split(",")
        )

        if "environment" in otel_resource_attributes:
            LOGGER.info(
                "OTEL_RESOURCE_ATTRIBUTES environment value already set. Skipping service restart"
            )
        else:
            otel_resource_attributes["environment"] = "staging"
            otel_resource_attributes_string = ",".join(
                _convert_to_string_data([otel_resource_attributes])
            )
            os.environ["OTEL_RESOURCE_ATTRIBUTES"] = otel_resource_attributes_string
            try:
                subprocess.run(["systemctl", "restart", "opentelemetry-collector"], check=True)
            except subprocess.CalledProcessError:
                raise ELIPYException(
                    "Unable restart OTel Collector service but OTEL_RESOURCE_ATTRIBUTES is set"
                )
    else:
        LOGGER.info("OTEL_RESOURCE_ATTRIBUTES environment variable not found.")
        LOGGER.info("----- Not updating environment variable -----")


def confirm_connectivity(target_urls):
    """
    Verify website connectivity via HTTP GET request
    """
    for target_url in target_urls:
        if target_url:
            try:
                response = requests.get(target_url, timeout=5)
                response.raise_for_status()

            except requests.exceptions.ConnectionError as error:
                raise ELIPYException(str(error))

            if response.status_code != 200:
                raise ELIPYException(f"StatusCode not 200: {response.status_code}")

            LOGGER.info("Connected to {}".format(target_url))


def confirm_fileshare(target_path):
    """
    Verify file share connectivity
    """
    if target_path:
        try:
            if os.path.exists(target_path):
                LOGGER.info("Connected to {}".format(target_path))
            else:
                raise ELIPYException("Failed to connect to {}".format(target_path))
        except Exception as error:
            raise error


def confirm_perforce(p4_port):
    """
    Verify perforce server connectivity
    """
    try:
        subprocess.run(
            ["p4", "-p", p4_port, "info"],
            check=True,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL,
        )
        LOGGER.info("Connected to {}".format(p4_port))
    except Exception as error:
        raise ELIPYException("Failed to connect to {}".format(p4_port)) from error


def configure_avalanche(upsteam_node, storage_path="C:\\ProgramData\\Avalanche", storage_size=50):
    """
    Configure Avalanche server hosted in agent
    """
    if upsteam_node:
        avalanche.stop_avalanche()
        avalanche.disable_maintenance()
        avalanche.disable_upstream_sets()
        avalanche.set_upstream_node(urlparse(upsteam_node).hostname)
        avalanche.update_storage_settings(storage_path, storage_size)
        avalanche.restart_avalanche()
