package schedulers

import support.DeclarativePipelineSpockTest

class GametoolSchedulerSpec extends DeclarativePipelineSpockTest {

    void setup() {
        binding.setVariable('env', [
            CODE_BRANCH            : 'code-branch',
            CODE_FOLDER            : 'code-folder',
            NON_VIRTUAL_CODE_BRANCH: 'kin-dev',
            NON_VIRTUAL_CODE_FOLDER: 'dev',
            P4_CHANGELIST          : '234',
            JOB_NAME               : 'my-job',
            BRANCH_NAME            : 'a-branch',
        ])
        binding.setVariable('params', [
            CLEAN_LOCAL: false,
        ])
        helper.registerAllowedMethod('ProjectClass', [String]) { projectName -> [short_name: 'kin'] }
        helper.registerAllowedMethod('get_branchfile', [String, String]) { projectName, branchName ->
            [
                general_settings      : [
                    gametool_settings: [
                        trigger                : 'a cron trigger',
                        non_virtual_code_branch: 'kin-dev',
                        non_virtual_code_folder: 'dev',
                        gametools              : [
                            icepick: [
                                timeout_hours : 6,
                                config        : 'final',
                                framework_args: ['first-arg', 'second-arg'],
                            ],
                        ],
                    ]
                ],
                standard_jobs_settings: [:],
            ]
        }
        helper.with {
            registerAllowedMethod('setPollScmTriggers', []) {}
            registerAllowedMethod('EnvInject', [Map, Map]) { currentBuild, injectMap ->
                binding.setVariable('env', binding.getVariable('env') + injectMap)
            }
            registerAllowedMethod('getModifiedTools', [String]) { codeChangelist -> ['icepick'] }
        }
    }

    void 'test GametoolScheduler runs'() {
        when:
        runScript('GametoolScheduler.groovy')
        printCallStack()
        then:
        testNonRegression()
    }
}
