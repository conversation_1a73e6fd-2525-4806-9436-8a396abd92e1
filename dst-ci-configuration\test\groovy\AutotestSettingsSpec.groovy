import com.ea.lib.jobsettings.AutotestSettings
import com.ea.lib.jobsettings.JobSetting
import com.ea.lib.model.autotest.AutotestCategory
import com.ea.lib.model.autotest.BranchConfiguration
import com.ea.lib.model.autotest.FrostedAutotestCategory
import com.ea.lib.model.autotest.Name
import com.ea.lib.model.autotest.Platform
import com.ea.lib.model.autotest.Region
import com.ea.lib.model.autotest.TestInfo
import com.ea.lib.model.autotest.TestSuite
import com.ea.lib.model.autotest.jobs.AutotestModel
import spock.lang.Specification

class AutotestSettingsSpec extends Specification {

    class BranchFile {
        static Map standard_jobs_settings = [
            workspace_root            : 'workspace-root',
            elipy_call                : 'elipy-call',
            elipy_install_call        : 'elipy-install-call',
            azure_workspace_root      : 'azure-workspace-root',
            azure_elipy_call          : 'azure-elipy-call',
            azure_elipy_install_call  : 'azure-elipy-install-call',
            azure_fileshare           : [
                additional_tools_to_include: ['tool1', 'tool2'],
                secret_context             : 'secret-context',
                target_build_share         : 'target-build-share',
            ],
            disable_build_autotest    : true,
            enable_lkg_p4_counters    : true,
            user_credentials          : 'user',
            import_avalanche_autotest : true,
            clean_master_version_check: true,
            job_label_statebuild      : 'statebuild',
            poolbuild_label           : 'poolbuild',
        ]
        static Map general_settings = [
            dataset           : 'dataset',
            frostbite_licensee: 'frostbite-licensee',
        ]
    }

    class MasterFile {
        static Map autotest_branches = [(BRANCH_NAME): [data_branch         : 'data-branch', code_branch: 'code-branch', build_frosted: true,
                                                        set_integration_info: [test_category: TEST_CATEGORY_NAME]
        ]]
    }

    class ProjectFile {
        static String autotest_matrix = 'TestAutotestMatrix'
        static String name = 'santiago'
        static Map p4_fb_settings = [
            p4_port: 'url:1667'
        ]
        static boolean single_perforce_server = false
        static Map icepick_settings = [
            settings_files: 'Config/Icepick/IcepickSettings.ini',
        ]
    }

    private static final String BRANCH_NAME = 'branch'
    private static final String TEST_CATEGORY_NAME = 'test-name'
    private static final String FROSTED_TEST_CATEGORY_NAME = 'frosted-test-name'
    private final String CRON_TRIGGER = 'trigger'

    private final TestInfo TEST_INFO = new TestInfo(
        platforms: [
            new Platform(name: Name.XBSX, region: Region.WW),
            new Platform(name: Name.PS5, region: Region.DEV),
        ],
        parallelLimit: 2,
        runLevelsInParallel: true,
        testGroup: 'test-group',
        serverPlatform: 'linuxserver',
        serverAsset: 'PreflightLevels',
        serverConfig: 'final',
        timeoutHours: 5,
        installPypiwin32: true,
        tests: [
            new TestSuite(name: 'BootAndReloadLevelTest', extraArgs: ['--runtime-connect-timeout', 360], needGameServer: true),
            new TestSuite(name: 'BootAndDeploy', extraArgs: ['--runtime-connect-timeout', 360], needGameServer: true),
        ],
        customTestSuiteData: 'custom-test-suite-data',
        serverRegion: 'eu',
        enableSyncFiles: true,
        hasPinnedAgents: true,
        numTestRuns: 3,
    )
    private final AutotestCategory TEST_CATEGORY = new AutotestCategory(
        name: TEST_CATEGORY_NAME,
        testDefinition: 'test-definition',
        runBilbo: true,
        captureVideo: true,
        needGameServer: true,
        config: 'final',
        uploadJournals: true,
        branches: [
            new BranchConfiguration(branchName: BRANCH_NAME, testInfo: TEST_INFO, cronTrigger: CRON_TRIGGER)
        ],
        testInfo: TEST_INFO,
        trigger: CRON_TRIGGER,
        enableP4Counters: true,
        buildType: 'dll',
        extraFrameworkArgs: 'some-extra-framework-args',
        frostingEndpointOverride: 'override',
    )
    private final TestInfo FROSTED_TEST_INFO = new TestInfo(
        testGroup: 'test-frostedtests',
        timeoutHours: 3,
        platforms: [new Platform(name: Name.WIN64)],
        tests: [
            new TestSuite(
                name: 'frostedtest_DUNLevelDesign',
                extraArgs: ['--frosted-launch-timeout', 600, '--frosted-test-timeout', 480, '--alltests', '--databaseId', 'BattlefieldGameData.kin-dev-unverified.Win32.Debug'],
                poolType: '',
            ),
        ],
    )
    private final TestInfo FROSTED_TESTNAMES_TEST_INFO = new TestInfo(
        testGroup: 'test-frostedtests',
        timeoutHours: 3,
        platforms: [new Platform(name: Name.WIN64)],
        testNames: [
            'FrostEdTest_BFWorkflowsUI',
        ],
        extraArgs: ['--frosted-launch-timeout', 600, '--frosted-test-timeout', 480, '--alltests'],
        poolType: '',
    )
    private final AutotestCategory FROSTED_TEST_CATEGORY = new FrostedAutotestCategory(
            name: FROSTED_TEST_CATEGORY_NAME,
            testDefinition: 'frosted-test-definition',
            runBilbo: false,
            needGameServer: false,
            uploadJournals: false,
            branches: [
                new BranchConfiguration(branchName: BRANCH_NAME, testInfo: FROSTED_TEST_INFO, cronTrigger: CRON_TRIGGER)
            ],
            testInfo: FROSTED_TEST_INFO,
            trigger: CRON_TRIGGER,
            buildType: 'dll',
            extraFrameworkArgs: 'some-extra-framework-args',
            frostingEndpointOverride: 'override',
        )
    private final AutotestCategory FROSTED_TESTNAMES_TEST_CATEGORY = new FrostedAutotestCategory(
            name: FROSTED_TEST_CATEGORY_NAME,
            testDefinition: 'frosted-test-definition',
            runBilbo: false,
            needGameServer: false,
            uploadJournals: false,
            branches: [
                new BranchConfiguration(branchName: BRANCH_NAME, testInfo: FROSTED_TESTNAMES_TEST_INFO, cronTrigger: CRON_TRIGGER)
            ],
            testInfo: FROSTED_TEST_INFO,
            trigger: CRON_TRIGGER,
            buildType: 'dll',
            extraFrameworkArgs: 'some-extra-framework-args',
            frostingEndpointOverride: 'override',
        )
    private AutotestModel autotestModel
    private AutotestModel frostedAutotestModel
    private AutotestModel frostedTestnamesAutotestModel

    void setup() {
        autotestModel = new AutotestModel(
            platform: Name.XBSX.toString()
        )
        frostedAutotestModel = new AutotestModel(
            platform: Name.WIN64.toString()
        )
        frostedTestnamesAutotestModel = new AutotestModel(
            platform: Name.WIN64.toString()
        )
        TEST_INFO.tests.each {
            autotestModel.addTest(it)
        }
        FROSTED_TEST_INFO.tests.each {
            frostedAutotestModel.addTest(it)
        }
        FROSTED_TESTNAMES_TEST_INFO.tests.each {
            frostedTestnamesAutotestModel.addTest(it)
        }
    }

    void 'test that we get expected job settings in initializeAutotestStart'() {
        when:
        JobSetting settings = new AutotestSettings()
        settings.initializeAutotestStart(BranchFile, MasterFile, ProjectFile, BRANCH_NAME, TEST_CATEGORY)
        then:
        with(settings) {
            isDisabled == BranchFile.standard_jobs_settings.disable_build_autotest
            enableP4Counters
            setIntegrationInfo
            !isTestWithLooseFiles
            !isFrostedAutotest
            needGameServer
            projectName == ProjectFile.name
            dataset == BranchFile.general_settings.dataset
            autotestMatrixName == ProjectFile.autotest_matrix
            testCategoryName == TEST_CATEGORY_NAME
        }
    }

    void 'test that we get expected job settings in initializeAutotestManualStart'() {
        when:
        JobSetting settings = new AutotestSettings()
        settings.initializeAutotestManualStart(BranchFile, MasterFile, ProjectFile, BRANCH_NAME)
        then:
        with(settings) {
            description == 'Manual trigger start job for Autotest jobs.'
            projectName == ProjectFile.name
            dataset == BranchFile.general_settings.dataset
            autotestMatrixName == ProjectFile.autotest_matrix
        }
    }

    void 'initializeAutotestJob generates a correct CLI call for a Drone test'() {
        when:
        JobSetting settings = new AutotestSettings()
        settings.initializeAutotestRun(BranchFile, MasterFile, ProjectFile, BRANCH_NAME, autotestModel, TEST_CATEGORY)
        then:
        with(settings) {
            jobLabel == "$TEST_CATEGORY_NAME && ${Name.XBSX}"
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            needGameServer
            testDefinition == 'test-definition'
            !isTestWithLooseFiles
            timeoutMinutes == 300
            userCredentials == BranchFile.standard_jobs_settings.user_credentials
            fbLoginDetails == ProjectFile.p4_fb_settings
            elipyInstallCall == BranchFile.standard_jobs_settings.elipy_install_call
            elipyCall == BranchFile.standard_jobs_settings.elipy_call
            installPypiwin32
            testSuitesString == '[{"name":"BootAndReloadLevelTest","extraArgs":["--runtime-connect-timeout","360","--pool-type","atf",' +
                '"--default-server-target-group-overrides","dedicated_server_pool"]},{"name":"BootAndDeploy",' +
                '"extraArgs":["--runtime-connect-timeout","360","--pool-type","atf","--default-server-target-group-overrides",' +
                '"dedicated_server_pool"]}]'
            extraArgs == ' --retry-infrastructure-failure 0 --ps5-retry-launch-on-fail false --wait-for-output-ms 120000 --reporting-build-version-id %data_changelist% --atti tool1 --atti tool2' +
                ' --ctsd custom-test-suite-data --ic true --ias true --ngs true --sbi %server_build_id%' +
                ' --frosting-upload-trace-journal true --runtime-debug-args="-Vision.Enabled true -Vision.StreamOnBoot true"' +
                ' -l frostbite-licensee --sp linuxserver --sas PreflightLevels -c final --sc final' +
                ' -e %monkey_email% -p "%monkey_passwd%" --bt dll --rb true ' +
                '--tg test-group --efa "some-extra-framework-args" --clean %clean% --sr eu --frosting-api-address override' +
                ' --str true --sfs true --sf Config/Icepick/IcepickSettings.ini --cmvc true --num-test-runs 3'
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} icepick_run ${Name.XBSX} --tsj workspace-root\\testSuites.json" +
                ' --cb code-branch --cc %code_changelist%' +
                ' --db data-branch --dc %data_changelist%' +
                ' --cbi %client_build_id% --reporting-branch data-branch' +
                ' --reporting-public-suite true --reporting-is-monkey true --no-key-press true' +
                " --td %test_definition% --dd ${BranchFile.general_settings.dataset} $extraArgs"
        }
    }

    void 'initializeAutotestJob generates a correct CLI call for a performance test'() {
        given:
        AutotestCategory testCategory = new AutotestCategory(
            isTestWithLooseFiles: true,
            format: 'files',
            region: Region.EU,
            config: 'final',
            useTempDeploymentIfLooseFiles: false,
            copyLooseFileBuildToWindowsShareTimeoutSeconds: 2500,
            testInfo: new TestInfo(),
        )
        when:
        JobSetting settings = new AutotestSettings()
        settings.initializeAutotestRun(BranchFile, MasterFile, ProjectFile, BRANCH_NAME, new AutotestModel(), testCategory)
        then:
        settings.jobLabel == 'statebuild'
        settings.extraArgs == ' --retry-infrastructure-failure 0 --ps5-retry-launch-on-fail false --wait-for-output-ms 120000 --reporting-build-version-id %data_changelist% --atti tool1 --atti tool2' +
            ' --ias true -l frostbite-licensee -c final -e %monkey_email% -p "%monkey_passwd%"' +
            ' --pt files -r eu --use-temp-deployment false --copy-build-to-share-timeout-seconds 2500 --superbundles true' +
            ' --str true --sf Config/Icepick/IcepickSettings.ini --cmvc true --num-test-runs 1'
    }

    void 'initializeAutotestJob generates a correct CLI call for a FrostEd test'() {
        when:
        JobSetting settings = new AutotestSettings()
        settings.initializeAutotestRun(BranchFile, MasterFile, ProjectFile, BRANCH_NAME, frostedAutotestModel, FROSTED_TEST_CATEGORY)
        then:
        with(settings) {
            jobLabel == "$BRANCH_NAME && frostedtests && cloud"
            extraArgs == ' --retry-infrastructure-failure 0 --ps5-retry-launch-on-fail false --wait-for-output-ms 120000 --reporting-build-version-id %data_changelist% --if true' +
                ' --tbs target-build-share --sctx secret-context --fpu %fb_p4_user% --fpp "%fb_p4_passwd%" --fbp url:1667 --atti tool1' +
                ' --atti tool2 --ic true --ias true -l frostbite-licensee -c release -e %monkey_email% -p "%monkey_passwd%" --bt dll' +
                ' --tg test-frostedtests --efa "some-extra-framework-args" --clean %clean% --frosting-api-address override' +
                ' --str true --sf Config/Icepick/IcepickSettings.ini --cmvc true --num-test-runs 1 --ifbp true'
            testSuitesString == '[{"name":"frostedtest_DUNLevelDesign","extraArgs":["--frosted-launch-timeout","600",' +
                '"--frosted-test-timeout","480","--alltests","--databaseId","BattlefieldGameData.kin-dev-unverified.Win32.Debug"]}]'
            workspaceRoot == BranchFile.standard_jobs_settings.azure_workspace_root
            elipyInstallCall == BranchFile.standard_jobs_settings.azure_elipy_install_call
            elipyCall == BranchFile.standard_jobs_settings.azure_elipy_call
            FROSTED_TEST_CATEGORY.p4CodeServer == 'ssl:uswest2-p4buildedge-fb.p4one.ea.com:2001'
            FROSTED_TEST_CATEGORY.p4DataServer == 'ssl:uswest2-p4buildedge-fb.p4one.ea.com:2001'
        }
    }
    void 'initializeAutotestJob generates a correct CLI call for a FrostEd test defined with test_names'() {
        when:
        JobSetting settings = new AutotestSettings()
        settings.initializeAutotestRun(BranchFile, MasterFile, ProjectFile, BRANCH_NAME, frostedTestnamesAutotestModel, FROSTED_TESTNAMES_TEST_CATEGORY)
        then:
        with(settings) {
            jobLabel == "$BRANCH_NAME && frostedtests && cloud"
            extraArgs == ' --retry-infrastructure-failure 0 --ps5-retry-launch-on-fail false --wait-for-output-ms 120000 --reporting-build-version-id %data_changelist% --if true' +
                ' --tbs target-build-share --sctx secret-context --fpu %fb_p4_user% --fpp "%fb_p4_passwd%" --fbp url:1667 --atti tool1' +
                ' --atti tool2 --ic true --ias true -l frostbite-licensee -c release -e %monkey_email% -p "%monkey_passwd%" --bt dll' +
                ' --tg test-frostedtests --efa "some-extra-framework-args" --clean %clean% --frosting-api-address override' +
                ' --str true --sf Config/Icepick/IcepickSettings.ini --cmvc true --num-test-runs 1 --ifbp true'
            testSuitesString == '[{"name":"FrostEdTest_BFWorkflowsUI","extraArgs":["--frosted-launch-timeout","600",' +
                '"--frosted-test-timeout","480","--alltests"]}]'
            workspaceRoot == BranchFile.standard_jobs_settings.azure_workspace_root
            elipyInstallCall == BranchFile.standard_jobs_settings.azure_elipy_install_call
            elipyCall == BranchFile.standard_jobs_settings.azure_elipy_call
        }
    }

    void 'initializeAutotestJob uses the correct label for a stream with build jobs in DICE and autotests in a remote datacenter'() {
        given:
        AutotestCategory testCategory = new AutotestCategory(
            isTestWithLooseFiles: true,
            format: 'files',
            region: Region.EU,
            config: 'final',
            useTempDeploymentIfLooseFiles: false,
            copyLooseFileBuildToWindowsShareTimeoutSeconds: 2500,
            testInfo: new TestInfo(),
            remoteLabel: 'eala'
        )
        when:
        JobSetting settings = new AutotestSettings()
        settings.initializeAutotestRun(BranchFile, MasterFile, ProjectFile, BRANCH_NAME, new AutotestModel(), testCategory)
        then:
        settings.jobLabel == 'statebuild_eala'
    }

    void 'initializeAutotestJob uses the correct label for a stream with build jobs in a remote datacenter with autotests in DICE'() {
        given:
        AutotestCategory testCategory = new AutotestCategory(
            isTestWithLooseFiles: true,
            format: 'files',
            region: Region.EU,
            config: 'final',
            useTempDeploymentIfLooseFiles: false,
            copyLooseFileBuildToWindowsShareTimeoutSeconds: 2500,
            testInfo: new TestInfo(),
        )
        BranchFile.standard_jobs_settings += [
            poolbuild_label     : 'poolbuild_eala',
            job_label_statebuild: 'statebuild_eala',
        ]
        when:
        JobSetting settings = new AutotestSettings()
        settings.initializeAutotestRun(BranchFile, MasterFile, ProjectFile, BRANCH_NAME, new AutotestModel(), testCategory)
        then:
        settings.jobLabel == 'statebuild'
    }

    void 'initializeAutotestJob uses the correct label for a stream with build jobs in a remote datacenter with autotests in the same datacenter'() {
        given:
        AutotestCategory testCategory = new AutotestCategory(
            isTestWithLooseFiles: true,
            format: 'files',
            region: Region.EU,
            config: 'final',
            useTempDeploymentIfLooseFiles: false,
            copyLooseFileBuildToWindowsShareTimeoutSeconds: 2500,
            testInfo: new TestInfo(),
            remoteLabel: 'eala'
        )
        BranchFile.standard_jobs_settings += [
            poolbuild_label     : 'poolbuild_eala',
            job_label_statebuild: 'statebuild_eala',
        ]
        when:
        JobSetting settings = new AutotestSettings()
        settings.initializeAutotestRun(BranchFile, MasterFile, ProjectFile, BRANCH_NAME, new AutotestModel(), testCategory)
        then:
        settings.jobLabel == 'statebuild_eala'
    }

    void 'initializeAutotestJob uses the correct label for a stream with build jobs in a remote datacenter with autotests in a different remote datacenter'() {
        given:
        AutotestCategory testCategory = new AutotestCategory(
            isTestWithLooseFiles: true,
            format: 'files',
            region: Region.EU,
            config: 'final',
            useTempDeploymentIfLooseFiles: false,
            copyLooseFileBuildToWindowsShareTimeoutSeconds: 2500,
            testInfo: new TestInfo(),
            remoteLabel: 'eala'
        )
        BranchFile.standard_jobs_settings += [
            poolbuild_label     : 'poolbuild_criterion',
            job_label_statebuild: 'statebuild_criterion',
        ]
        when:
        JobSetting settings = new AutotestSettings()
        settings.initializeAutotestRun(BranchFile, MasterFile, ProjectFile, BRANCH_NAME, new AutotestModel(), testCategory)
        then:
        settings.jobLabel == 'statebuild_eala'
    }
}
