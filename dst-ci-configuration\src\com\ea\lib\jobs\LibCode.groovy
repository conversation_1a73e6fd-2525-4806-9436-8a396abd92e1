package com.ea.lib.jobs

import com.ea.lib.LibCommonNonCps
import com.ea.lib.LibJobDsl

class LibCode {
    /**
     * Adds generic job parameters for code start jobs.
     */
    static void code_start(def job, def project, def branch_info) {
        // Set values for variables.
        def modifiers = ['code']
        def clean_local = branch_info.clean_local ?: false
        def skip_clean_label = branch_info.skip_clean_label ?: false
        def clean_local_nomaster = branch_info.clean_local_nomaster ?: false
        def clean_local_stressbulkbuild = branch_info.clean_local_stressbulkbuild ?: false
        def data_triggers_patchdata = branch_info.data_triggers_patchdata ?: false
        def drone = branch_info.drone ?: false
        def enable_lkg_p4_counters = branch_info.enable_lkg_p4_counters ?: false
        def frostyisotool = branch_info.frostyisotool ?: false
        def main_unverified_branch = branch_info.main_unverified_branch ?: false
        def nomaster = branch_info.nomaster ?: false
        def stressbulkbuild = branch_info.stressbulkbuild ?: false
        def non_virtual_code_branch = branch_info.non_virtual_code_branch ?: ''
        def non_virtual_code_folder = branch_info.non_virtual_code_folder ?: ''
        def retry_limit = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'retry_limit', 1, project)
        def skip_code_build_if_no_changes = branch_info.skip_code_build_if_no_changes != null ? branch_info.skip_code_build_if_no_changes : true
        def skip_frosty_scheduler = branch_info.skip_frosty_scheduler ?: false
        def slack_notify_bot_code = branch_info.slack_notify_bot_code ?: false
        def slack_notify_bot_code_nomaster = branch_info.slack_notify_bot_code_nomaster ?: false
        def slack_notify_bot_code_stressbulkbuild = branch_info.slack_notify_bot_code_stressbulkbuild ?: false
        def code_reference_job = branch_info.code_reference_job ?: ''

        def frostbite_syncer_setup = branch_info.frostbite_syncer_setup ?: project.frostbite_syncer_setup ?: false
        def smoke_cl_after_success = branch_info.smoke_cl_after_success ?: false

        def description_string = 'Sync ' + branch_info.branch_name + ' code and deploy a binary build.'
        def trigger_string = branch_info.trigger_string_code ?: 'H/5 * * * 1-6\nH/5 6-23 * * 7'
        def trigger_type = branch_info.trigger_type_code ?: 'scm'
        if (drone == true) {
            description_string = 'Trigger job for code.drone.build on ' + branch_info.branch_name + '.'
            trigger_string = branch_info.trigger_string_code_drone ?: 'H/5 * * * 1-6\nH/5 6-23 * * 7'
            trigger_type = branch_info.trigger_type_code_drone ?: 'scm'
        }
        if (frostyisotool == true) {
            description_string = 'Trigger job for code.frostyisotool.build on ' + branch_info.branch_name + '.'
            trigger_string = branch_info.trigger_string_code_frostyisotool ?: 'H/5 * * * 1-6\nH/5 6-23 * * 7'
            trigger_type = branch_info.trigger_type_code_frostyisotool ?: 'scm'
        }
        if (nomaster == true) {
            description_string = 'Sync ' + branch_info.branch_name + ' code and build a nomaster build.'
            trigger_string = branch_info.trigger_string_code_nomaster ?: 'H * * * 1-6\nH 6-23 * * 7'
            trigger_type = branch_info.trigger_type_code_nomaster ?: 'scm'
            if (clean_local == false) {
                clean_local = clean_local_nomaster
            }
        }
        if (stressbulkbuild == true) {
            description_string = 'Sync ' + branch_info.branch_name + ' code and run a stressbulkbuild.'
            trigger_string = branch_info.trigger_string_code_stressbulkbuild ?: 'H * * * 1-6\nH 6-23 * * 7'
            trigger_type = branch_info.trigger_type_code_stressbulkbuild ?: 'scm'
            if (clean_local == false) {
                clean_local = clean_local_stressbulkbuild
            }
        }
        def clean_local_default = ['false', 'true']
        if (clean_local) {
            clean_local_default = clean_local_default.reverse()
        }

        def disable_build = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'disable_build', false)

        // Add sections to the Jenkins job.
        job.with {
            description(description_string)
            environmentVariables {
                env('branch_name', branch_info.branch_name)
                env('code_branch', branch_info.code_branch)
                env('code_folder', branch_info.code_folder)
                env('codepreflight_info_sending', branch_info.codepreflight_info_sending)
                env('data_triggers_patchdata', data_triggers_patchdata)
                env('enable_lkg_p4_counters', enable_lkg_p4_counters)
                env('frostbite_syncer_setup', frostbite_syncer_setup)
                env('main_unverified_branch', main_unverified_branch)
                env('non_virtual_code_branch', non_virtual_code_branch)
                env('non_virtual_code_folder', non_virtual_code_folder)
                env('project_name', project.name)
                env('retry_limit', retry_limit)
                env('skip_code_build_if_no_changes', skip_code_build_if_no_changes)
                env('smoke_cl_after_success', smoke_cl_after_success)
                env('skip_frosty_scheduler', skip_frosty_scheduler)
                env('slack_notify_bot_code', slack_notify_bot_code)
                env('slack_notify_bot_code_nomaster', slack_notify_bot_code_nomaster)
                env('slack_notify_bot_code_stressbulkbuild', slack_notify_bot_code_stressbulkbuild)
                env('code_reference_job', code_reference_job)
                env('skip_clean_label', skip_clean_label)
            }
            disabled(disable_build)
            logRotator(7, 100)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                choiceParam('clean_local', clean_local_default, 'If true, TnT/Local will be deleted at the beginning of the run.')
            }
            properties {
                disableConcurrentBuilds()
                disableResume()
                pipelineTriggers {
                    triggers {
                        if (trigger_type == 'scm') {
                            pollSCM {
                                scmpoll_spec(trigger_string)
                            }
                        } else if (trigger_type == 'cron') {
                            cron {
                                spec(trigger_string)
                            }
                        }
                    }
                }
            }
            quietPeriod(0)
        }
    }

    /**
     * Adds generic job parameters for code check job.
     */
    static void code_check(def job, def project, def branch_info) {
        // Set values for variables.
        def non_virtual_code_branch = branch_info.non_virtual_code_branch ?: ''
        def non_virtual_code_folder = branch_info.non_virtual_code_folder ?: ''

        // Add sections to the Jenkins job.
        job.with {
            description('Starts code check jobs.')
            environmentVariables {
                env('code_branch', branch_info.code_branch)
                env('code_folder', branch_info.code_folder)
                env('non_virtual_code_branch', non_virtual_code_branch)
                env('non_virtual_code_folder', non_virtual_code_folder)
                env('project_name', project.name)
            }
            logRotator(7, 100)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
            }
            properties {
                disableConcurrentBuilds()
                disableResume()
            }
            quietPeriod(0)
        }
    }

    /**
     * Adds generic job parameters for job that copies code to filer with a newer changelist.
     */
    static void copy_code_to_filer(def job, def project, def branch_info) {
        job.with {
            def job_label = branch_info.job_label_statebuild ?: 'statebuild'
            if (branch_info.statebuild_code == false) {
                job_label = branch_info.code_branch + ' && code'
            }

            description('Job that copies already built code to filer with a new changelist.')
            label(job_label)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(branch_info.workspace_root)
            properties {
                disableConcurrentBuilds()
                disableResume()
            }
            parameters {
                stringParam {
                    name('source_changelist')
                    defaultValue('')
                    description('Changelist for latest successful code build.')
                    trim(true)
                }
                stringParam {
                    name('current_changelist')
                    defaultValue('')
                    description('Changelist that will be used for the copy.')
                    trim(true)
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName('${JOB_NAME}.${ENV, var="source_changelist"}.${ENV, var="current_changelist"}')
                timeout {
                    absolute(120)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, branch_info.elipy_install_call, project)
                batchFile(branch_info.elipy_call + ' codecopy --code-branch ' + branch_info.code_branch +
                    ' --source-changelist %source_changelist% --current-changelist %current_changelist%')
            }
        }
    }

    /**
     * Adds generic job parameters for code build jobs.
     */
    static void code_job(def job, def project, def branch_info) {
        // Set values for variables.
        def modifiers = ['code', branch_info.platform, branch_info.config]
        def modifiers_nomaster = ['nomaster', branch_info.platform, branch_info.config]
        def user_credentials = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'user_credentials', '', project)

        def compress_symbols = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'compress_symbols', true, project)
        def oreans_protection = branch_info.oreans_protection ?: false
        def oreans_config = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'oreans_config', null, project)
        def oreans_artifactory_files = branch_info.oreans_artifactory_files != null ? branch_info.oreans_artifactory_files : true
        def denuvo_artifactory_files = branch_info.denuvo_artifactory_files != null ? branch_info.denuvo_artifactory_files : true
        def denuvo_wrapping = branch_info.denuvo_wrapping ?: false
        def denuvo_exclusion_path = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'denuvo_exclusion_path', null, project)
        def deploy_tests = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'deploy_tests', false, project)
        def deploy_frostedtests = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'deploy_frostedtests', false, project)
        def description_string = 'Compiles and deploys ' + branch_info.platform + ' in ' + branch_info.config + '.'
        def fake_ooa_wrapped_symbol = branch_info.getOrDefault('fake_ooa_wrapped_symbol', project.fake_ooa_wrapped_symbol)
        def fb_login_details = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'p4_fb_settings', [:], project)
        def frostbite_licensee = branch_info.frostbite_licensee
        def gensln_config = branch_info.gensln_config
        def mimalloc_enabled = branch_info?.mimalloc_enabled
        def havok_run = branch_info.havok_run ?: false
        def job_label = branch_info.job_label_statebuild ?: 'statebuild'
        def link_time_code_generation = LibCommonNonCps.get_setting_value(branch_info, [branch_info.platform, branch_info.config], 'link_time_code_generation', null, project)
        def nomaster = branch_info.nomaster ?: false
        def stressbulkbuild = branch_info.stressbulkbuild ?: false
        def run_unit_tests = branch_info.run_unit_tests ?: false
        def separate_symbol_store_upload = LibCommonNonCps.get_setting_value(branch_info, [], 'separate_symbol_store_upload', true, project)
        def skip_symbols_backup = branch_info.getOrDefault('skip_symbols_backup', true)
        def strip_symbols = branch_info.strip_symbols != null ? branch_info.strip_symbols : true
        def use_snowcache = branch_info.use_snowcache ?: false
        def use_snowcache_nomaster = branch_info.use_snowcache_nomaster ?: false
        def override_snowcache = branch_info.override_snowcache != null ? branch_info.override_snowcache : ''
        def zip_local_code_state = branch_info.zip_local_code_state ?: false
        def buildlayout_config_server = branch_info.buildlayout_config_server ?: ''
        def clean_packages = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'clean_packages', false, project)
        def is_outsource_build = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'is_outsource_build', false, project)
        def skip_symbols_to_symstore = separate_symbol_store_upload || branch_info.skip_symbols_to_symstore
        def trigger_downstream_sym_upload = separate_symbol_store_upload && nomaster == false
        def ignore_icepick_exit_code = branch_info.icepick_settings?.ignore_icepick_exit_code != null ? branch_info.icepick_settings?.ignore_icepick_exit_code : project.icepick_settings?.ignore_icepick_exit_code
        def p4_code_server = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_code_server', '', project)
        def enable_perfmarkers = LibCommonNonCps.get_setting_value(branch_info, [branch_info.config], 'enable_perfmarkers', false, project)
        def custom_tag = branch_info?.custom_tag
        def extra_job_args = branch_info?.extra_args
        def asan_enabled = branch_info.asan_enabled ?: false
        def ubsan_enabled = branch_info.ubsan_enabled ?: false
        def sndbs_enabled = branch_info.sndbs_enabled != null ? branch_info.sndbs_enabled : branch_info.sndbs_enabled_config != null ? branch_info.sndbs_enabled_config : false
        def tool_targets = branch_info.tool_targets ?: []
        def is_virtual_stream = LibCommonNonCps.get_setting_value(branch_info, [], 'is_virtual_stream', false)

        if (stressbulkbuild == true) {
            description_string = 'Compiles stressbulkbuild for ' + branch_info.platform + ' in ' + branch_info.config + '.'
            job_label = branch_info.code_branch + ' && code-stressbulkbuild && ' + branch_info.platform + ' && ' + branch_info.config
        } else if (nomaster == false) {
            def label = branch_info.code_branch + ' && code && '
            if (branch_info.statebuild_code == false) {
                job_label = label + branch_info.platform + ' && ' + branch_info.config
            }
            if (branch_info.config in branch_info.statebuild_code_list) {
                use_snowcache = false
                job_label = label + branch_info.platform + ' && ' + branch_info.config
            }
            if (branch_info.platform in branch_info.statebuild_code_list) {
                use_snowcache = false
                def _platform = branch_info.platform == 'tool' ? 'tool && release' : branch_info.platform
                job_label = label + _platform
            }
        } else if (nomaster == true) {
            description_string = 'Compiles nomaster for ' + branch_info.platform + ' in ' + branch_info.config + '.'
            if (branch_info.statebuild_code_nomaster == false) {
                job_label = branch_info.code_branch + ' && code-nomaster && ' + branch_info.platform + ' && ' + branch_info.config
            } else if (branch_info.platform == 'linux64server' && branch_info.statebuild_code_nomaster_linux64server == false) {
                job_label = branch_info.code_branch + ' && code-nomaster && ' + branch_info.platform + ' && ' + branch_info.config
            }
            if (use_snowcache_nomaster == true) {
                use_snowcache = true
            }
        }
        if (branch_info.build_frosted) {
            job_label = "${branch_info.branch_name} && frostedtests && cloud"
        }

        def dry_run_code = branch_info.dry_run_code ?: false
        def dry_run_config = branch_info.dry_run_config ?: false
        def dry_run = false
        if (dry_run_code == true || dry_run_config == true) {
            dry_run = true
        }

        def timeout_hours = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'timeout_hours', 3, project)
        if (nomaster == true) {
            timeout_hours = LibCommonNonCps.get_setting_value(branch_info, modifiers_nomaster, 'timeout_hours', 5, project)
        }
        def timeout_minutes = timeout_hours * 60

        def extra_args_list = LibCommonNonCps.get_setting_value(branch_info, [branch_info.platform], 'extra_code_args', [], project)
        def extra_args = extra_args_list.join(' ')
        if (oreans_protection == true) {
            extra_args += " --oreans-protection ${oreans_protection}"
        }
        if (oreans_config == true) {
            extra_args += " --oreans-config ${oreans_config}"
        }
        if (denuvo_artifactory_files == true || oreans_artifactory_files == true) {
            extra_args += ' --artifactory-user %AF2_GENERIC_USER% --artifactory-apikey %AF2_GENERIC_TOKEN%'
        }
        if (denuvo_wrapping == true) {
            extra_args += " --denuvo-wrapping ${denuvo_wrapping}"
        }
        if (denuvo_exclusion_path != null) {
            extra_args += ' --denuvo-exclusion ' + denuvo_exclusion_path
        }
        if (dry_run == true) {
            extra_args += " --dry-run ${dry_run}"
        }
        if (mimalloc_enabled != null) {
            extra_args += ' --framework-args -G:eaconfig.memory.mimalloc=' + mimalloc_enabled
        }
        if (havok_run == true) {
            extra_args += ' --framework-args -G:frostbite.feature.Havok=true'
        }
        if (asan_enabled) {
            extra_args += ' --framework-args -G:eaconfig.sanitize.address=on'
        }
        if (ubsan_enabled) {
            extra_args += ' --framework-args -G:eaconfig.sanitize.undefined=on --framework-args -G:eaconfig.sanitize.undefined.alignment=off --framework-args -G:eaconfig.sanitize=on'
        }
        if (sndbs_enabled) {
            extra_args += ' --framework-args -G:eaconfig.enablesndbs=true'
        }
        if (frostbite_licensee != null) {
            extra_args += ' --licensee ' + frostbite_licensee
        }
        if (link_time_code_generation != null) {
            if (link_time_code_generation == true) {
                extra_args += ' --framework-args -D:eaconfig.optimization.ltcg=on'
            } else if (link_time_code_generation == false) {
                extra_args += ' --framework-args -D:eaconfig.optimization.ltcg=off'
            }
        }
        if (nomaster == true) {
            extra_args += " --nomaster ${nomaster}"
        }
        if (stressbulkbuild == true) {
            extra_args += ' --stressbulkbuild true'
        }
        if (deploy_tests == true) {
            extra_args += " --deploy-tests ${deploy_tests}"
        }
        if (deploy_frostedtests == true) {
            extra_args += " --deploy-frostedtests ${deploy_frostedtests}"
        }
        if (skip_symbols_backup) {
            extra_args += " --skip-symbols-backup ${skip_symbols_backup}"
        }
        if (skip_symbols_to_symstore) {
            extra_args += " --skip-symbols-to-symstore ${skip_symbols_to_symstore}"
        }
        if (strip_symbols == false) {
            extra_args += ' --strip-symbols false'
        }
        if (user_credentials != '') {
            extra_args += ' --email %monkey_email% --password "%monkey_passwd%"'
        }
        if (zip_local_code_state == true) {
            extra_args += " --use-state-zip ${zip_local_code_state}"
        }
        if (use_snowcache == true) {
            extra_args += " --use-snowcache ${use_snowcache}"
        }
        if (override_snowcache != '') {
            extra_args += ' --snowcache-mode-override ' + override_snowcache
        }
        if (gensln_config && branch_info.config == 'retail') {
            extra_args += ' --gensln-config ' + gensln_config
        }
        if (buildlayout_config_server != '' && branch_info.platform == 'win64server') {
            extra_args += ' --buildlayout-config ' + buildlayout_config_server
        }
        if (clean_packages) {
            extra_args += " --clean-packages ${clean_packages}"
        }
        if (is_outsource_build) {
            extra_args += " --is-outsource-build ${is_outsource_build}"
        }
        for (value in branch_info.fb_env_values_code) {
            extra_args += " --fb-env-values ${value}"
        }
        if (custom_tag != null) {
            extra_args += " --custom-tag ${custom_tag}"
        }
        if (extra_job_args != null) {
            extra_args += " ${extra_job_args}"
        }
        for (value in tool_targets) {
            extra_args += " --tool-targets ${value}"
        }
        if (is_virtual_stream) {
            extra_args += ' --virtual-branch-override true '
        }
        // we need to clean the binaries if the VM is being used by multiple streams
        if (job_label.contains(branch_info.code_branch) == false) {
            extra_args += ' --clean-binaries true '
        }

        def compression_arg = compress_symbols ? '--compress-symbols true' : '--compress-symbols false'
        extra_args += ' ' + compression_arg

        if (project.icepick_settings && run_unit_tests) {
            extra_args += " --icepick-test ${project.icepick_settings.icepick_test}"
            if (ignore_icepick_exit_code != null) {
                extra_args += " --ignore-icepick-exit-code ${ignore_icepick_exit_code}"
            }
            if (branch_info.icepick_extra_framework_args) {
                extra_args += " --icepick-extra-framework-args \"${branch_info.icepick_extra_framework_args}\""
            }
            if (!branch_info.skip_icepick_settings_file) {
                extra_args += " --settings-files ${branch_info.icepick_settings_files ?: project.icepick_settings.settings_files}"
            }
            extra_args += " --data-directory \"${branch_info.dataset}\""
        }

        if (fake_ooa_wrapped_symbol) {
            extra_args += " --fake-ooa-wrapped-symbol ${fake_ooa_wrapped_symbol}"
        }

        if (project.commerce_debug_disable == true) { //disable debug mode for xb settings like store.ID in microstoft game file
            if ('xbsx'.equalsIgnoreCase(branch_info.platform) || 'xb1'.equalsIgnoreCase(branch_info.platform)) {
                extra_args += ' --framework-args -G:xbox.commerce.debug=false'
            }
        }

        if (branch_info.compile_unit_tests == true) {
            extra_args += ' --alltests true'
        }

        def elipy_call = branch_info.build_frosted ? branch_info.azure_elipy_call : branch_info.elipy_call
        if (project.is_cloud) {
            // TODO: Remove this when elipy calls for both preflights and builds are unified in AWS.
            elipy_call = elipy_call[elipy_call.indexOf('tnt\\bin\\fbcli\\cli.bat')..-1]
        }
        if (enable_perfmarkers) {
            extra_args += ' --framework-args -G:frostbite.retailperfmarkers=true'
        }

        // Add sections to the Jenkins job.
        job.with {
            description(description_string)
            label(job_label)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(branch_info.build_frosted ? branch_info.azure_workspace_root : branch_info.workspace_root)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                booleanParam('override_do_not_run_code_unittests', false, 'Overrides whether to run the code unit tests.')
                choiceParam('clean_local', ['false', 'true'], 'If true, TnT/Local will be deleted at the beginning of the run.')
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName('${JOB_NAME}.${ENV, var="code_changelist"}')
                timeout {
                    absolute(timeout_minutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
                credentialsBinding {
                    if (user_credentials != '') {
                        usernamePassword('monkey_email', 'monkey_passwd', user_credentials)
                    }
                    if (fb_login_details.p4_creds) {
                        usernamePassword('fb_p4_user', 'fb_p4_passwd', fb_login_details.p4_creds)
                    }
                }
            }
            steps {
                if (fb_login_details) {
                    batchFile("echo %fb_p4_passwd%|p4 -p ${fb_login_details.p4_port} -u %fb_p4_user% login & exit 0")
                }
                LibJobDsl.installElipy(delegate, branch_info.build_frosted ? branch_info.azure_elipy_install_call : branch_info.elipy_install_call, project)
                batchFile(elipy_call + ' codebuild ' + branch_info.platform + ' ' + branch_info.config +
                    ' --code-branch ' + branch_info.code_branch + ' --code-changelist %code_changelist%' +
                    ' --p4-port ' + p4_code_server + ' --p4-client ' + project.p4_code_client_env + ' --p4-user %P4_USER%' +
                    ' --clean %clean_local% --override-do-not-run-code-unittests %override_do_not_run_code_unittests%' +
                    ' --skip-deploy-tnt true' + ' ' + extra_args)
            }

            if (trigger_downstream_sym_upload) {
                if (branch_info.config != 'deprecation-test' && branch_info.config != 'steam') {
                    publishers {
                        downstreamParameterized {
                            trigger("${branch_info.branch_name}.symbolStoreUpload") {
                                condition('SUCCESS')
                                parameters {
                                    predefinedBuildParameters {
                                        properties(
                                            [
                                                'changelist=${code_changelist}',
                                                "platform=${branch_info.platform}",
                                                "config=${branch_info.config}",
                                                "compression_settings=${compression_arg}",
                                            ].join('\n')
                                        )
                                        textParamValueOnNewLine(false)
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Add job parameters for a job update p4 counter after code.start job is finished with success.
     */
    static void code_p4counter_updater(def job, def project, def branch_info) {
        def job_label = branch_info.job_label_statebuild ?: 'statebuild'
        def p4_code_server = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_code_server', false, project)
        job.with {
            description('P4 counter update after code start job done.')
            label(job_label)
            concurrentBuild()
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(branch_info.workspace_root)
            throttleConcurrentBuilds {
                maxPerNode(1)
                maxTotal(8)
            }
            parameters {
                stringParam {
                    name('code_countername')
                    defaultValue('')
                    description('Specifies p4 code counter name to use.')
                    trim(true)
                }
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist for p4 counter to set value to.')
                    trim(true)
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName('${JOB_NAME}')
                timeout {
                    absolute(100)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, branch_info.elipy_install_call, project)
                batchFile(branch_info.elipy_call + ' p4_counter' +
                    ' --port ' + p4_code_server +
                    ' --client ' + project.p4_code_client +
                    ' --user ' + project.p4_user_single_slash +
                    ' --countername %code_countername% --value %code_changelist%')
            }
        }
    }

    static void symbolStoreUpload(def job, def project, def branch_info) {
        def modifiers = ['symbolStoreUpload']
        def job_label_statebuild = branch_info.job_label_statebuild ?: 'statebuild'
        def job_label = LibCommonNonCps.get_setting_value(branch_info, [], 'symbol_upload_label', job_label_statebuild, project)
        def user_credentials = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'user_credentials', '', project)
        def fb_login_details = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'p4_fb_settings', [:], project)
        def frostbite_licensee = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'frostbite_licensee', null, project)

        job.with {
            description('Upload to symbol store after code job is done.')
            label(job_label)
            concurrentBuild()
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(project.workspace_root)

            def parametersToJob = [
                changelist: 'Code changelist',
                platform  : 'Code platform',
                config    : 'Code config',
            ]
            def buildDisplayName = '${JOB_NAME}'
            def command = "${branch_info.elipy_call} symbol_store_upload --branch ${branch_info.code_branch}"
            if (user_credentials != '') {
                command += ' --email %monkey_email% --password "%monkey_passwd%"'
            }

            parameters {
                parametersToJob.each { parameter, details ->
                    stringParam {
                        name(parameter)
                        defaultValue('')
                        description(details)
                        trim(true)
                    }
                    buildDisplayName += '.${ENV, var="' + parameter + '"}'
                    command += " --${parameter} %${parameter}%"
                }
                booleanParam('compression_settings', true, 'Compress (or not) the symbols we upload to the symbol store.')
                command += ' --compress-symbols %compression_settings%'
                if (frostbite_licensee != null) {
                    command += ' --licensee ' + frostbite_licensee
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(buildDisplayName)
                timeout {
                    absolute(120)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
                credentialsBinding {
                    if (user_credentials != '') {
                        usernamePassword('monkey_email', 'monkey_passwd', user_credentials)
                    }
                    if (fb_login_details.p4_creds) {
                        usernamePassword('fb_p4_user', 'fb_p4_passwd', fb_login_details.p4_creds)
                    }
                }
            }
            steps {
                if (fb_login_details) {
                    batchFile("echo %fb_p4_passwd%|p4 -p ${fb_login_details.p4_port} -u %fb_p4_user% login & exit 0")
                }
                batchFile(branch_info.elipy_install_call)
                batchFile(command)
            }
        }
    }
}
