package com.ea.lib.jobsettings

@SuppressWarnings('AbstractClassWithoutAbstractMethod')
abstract class JobSetting implements Serializable {
    private static final long serialVersionUID = 1L
    Boolean isDisabled
    Map branchInfo
    String branchName
    String codeBranch
    String codeFolder
    String dataBranch
    String dataFolder
    String projectName
    String buildName
    String description
    String elipyCmd
    String elipyInstallCall
    String azureElipyInstallCall
    String elipyCall
    String azureElipyCall
    String jobLabel
    String batchScript
    Integer timeoutMinutes
    String workspaceRoot
    String azureWorkspaceRoot
    Integer concurrentBuilds
    String cronTrigger
    Class branchFile
    Class masterFile
    Class projectFile
    String extraArgs
    String userCredentials

    protected void init(def branchFile, def masterFile, def projectFile, String branchName, Map masterFileBranches = [:]) {
        this.branchFile = branchFile
        this.branchInfo = (branchFile.general_settings + branchFile.standard_jobs_settings + [branch_name: branchName])
        this.branchName = branchName
        this.codeBranch = branchInfo.code_branch
        this.elipyCall = branchInfo.elipy_call
        this.azureElipyCall = branchInfo.azure_elipy_call
        this.elipyInstallCall = branchInfo.elipy_install_call
        this.azureElipyInstallCall = branchInfo.azure_elipy_install_call
        this.azureWorkspaceRoot = branchInfo.azure_workspace_root ?: 'E:\\dev'
        this.projectFile = projectFile
        this.projectName = projectFile.name
        this.masterFile = masterFile
        if (masterFileBranches) {
            this.branchInfo += masterFileBranches[branchName]
        }
        this.workspaceRoot = branchInfo.workspace_root
    }
}
