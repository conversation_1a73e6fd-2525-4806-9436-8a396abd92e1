"""
utility module for deleting
"""
import logging
from dice_elipy_scripts.utils.file_system import FolderTree
import os
import multiprocessing
from multiprocessing.pool import ThreadPool

from elipy2 import core, LOGGER


def delete_empty_folders(
    path, dry_run=False, files_equals_empty=None, stop_crawling_when_files_found=True
):
    # type: (str, bool, List[str], bool) -> List[Exception]
    """
    Crawls each retention category under build path and tries to remove empty-like folders.
    """
    empty_folders = set()
    exceptionlist = []
    files_equals_empty = files_equals_empty or []
    folder_tree = None
    LOGGER.info("Crawling {} for empty folders".format(path))

    for current, subfolders, files in os.walk(path):
        LOGGER.debug("Current folder {}".format(os.path.normpath(current)))
        LOGGER.debug("files {}".format(files))
        LOGGER.debug("subfolders {}".format(subfolders))

        if folder_tree and not folder_tree.has_branch(current):
            LOGGER.debug("update with leafless in: " + folder_tree.trunk)
            empty_folders.update(folder_tree.leafless_and_trimmed())
            folder_tree = None

        considered_empty = set(files).issubset(set([".DS_Store", "Thumbs.db"] + files_equals_empty))

        if not considered_empty:
            if stop_crawling_when_files_found:
                subfolders.clear()

            if folder_tree:
                folder_tree.append(branch=current, with_leaves=True)
        elif considered_empty and current != path:
            if folder_tree:
                folder_tree.append(branch=current, with_leaves=False)
            else:
                if subfolders:
                    folder_tree = FolderTree(trunk=current)
                else:
                    LOGGER.debug("added")
                    empty_folders.add(current)

    if folder_tree:
        empty_folders.update(folder_tree.leafless_and_trimmed())

    LOGGER.debug("Going to try to delete the following folders:")

    if dry_run:
        LOGGER.info("Would've tried to delete following folders if not a --dry-run:")

    if dry_run or LOGGER.level == logging.DEBUG:
        for folder in sorted(empty_folders):
            LOGGER.info(os.path.normpath(folder))

    if empty_folders and not dry_run:
        try:
            pool = ThreadPool(processes=multiprocessing.cpu_count() - 1)
            pool.map(core.delete_filer_folder, empty_folders)
            pool.close()
            pool.join()
        except Exception as exc:
            exceptionlist.append(exc)

    return exceptionlist
