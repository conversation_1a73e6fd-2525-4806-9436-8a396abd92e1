package schedulers

import com.ea.lib.model.branchsettings.UnitTestsConfiguration
import support.DeclarativePipelineSpockTest

class UnitTestsSchedulerSpec extends DeclarativePipelineSpockTest {
    class TestClassUnitTests {
        static String name = 'some_name'
        static String short_name = 's_n'
    }

    void setup() {
        binding.setVariable('env', [
            CODE_BRANCH            : 'code-branch',
            CODE_FOLDER            : 'code-folder',
            NON_VIRTUAL_CODE_BRANCH: 'kin-dev',
            NON_VIRTUAL_CODE_FOLDER: 'dev',
            PROJECT_NAME           : 'Santiago',
            P4_CHANGELIST          : '234',
            JOB_NAME               : 'my-job',
            BRANCH_NAME            : 'a-branch',
        ])
        helper.registerAllowedMethod('get_branchfile', [String, String]) {
            return [
                general_settings      : [
                    unittests: new UnitTestsConfiguration(
                        ignorePaths: ['/test/']
                    )
                ],
                standard_jobs_settings: [],
            ]
        }
        helper.registerAllowedMethod('ProjectClass', [String]) { projectName -> TestClassUnitTests }
    }

    void 'test UnitTestsScheduler runs'() {
        when:
        runScript('UnitTestsScheduler.groovy')
        printCallStack()
        then:
        testNonRegression()
    }
}
