package com.ea.lib.model.autotest.jobs

import com.ea.lib.model.autotest.Platform
import com.ea.lib.model.autotest.TestSuite

/**
 * Balancing Map of AutotestJobs.
 */
class AutotestJobsModel {
    /**
     * Map of Autotest jobs where the test suites have been balanced between platforms.
     */
    Map<String, AutotestModel> jobs
    /**
     * Jobs put into buckets that determine how many of them will run in parallel, used for running the jobs
     * The key is a unique identifier that has no meaning outside this class and the value is a list of job
     * models that should run in parallel.
     */
    Map<String, List<AutotestModel>> jobBuckets

    AutotestJobsModel() {
        jobs = [:]
        jobBuckets = [:]
    }

    /**
     * Creates and inserts an AutotestJob into the map
     * @param name Name of the job
     * @param platform The platform the test suite runs on
     * @param extraArguments Optional arguments that are specific to its platform
     * @param bucketKey A key that groups the AutotestJobs into a bucket that should run in parallel in Jenkins
     * @return The created AutotestJob
     */
    AutotestModel insertAutotestJob(String name, Platform platform, String extraArguments, String region, String bucketKey) {
        jobs[name] = new AutotestModel(name: name, platform: platform.toString(), extraArguments: extraArguments, region: region)
        return insertAutotestModelIntoJobBucket(bucketKey, jobs[name])
    }

    /**
     * Inserts the test suite into the AutotestJob (for the same platform) which has the least amount of test suites
     * when autotestModel is null. Otherwise insert the test suite into autotestModel
     * @param testSuite The test suite to insert
     * @param platform The platform the test suite belongs to
     * @param autotestModel which model to insert the test suite into. Null if parallel job
     * @return The job the test suite was inserted into. If now AutotestModel was passed as an argument and this
     * function fails to find an AutotestModel to add the test suite to, null is returned.
     */
    AutotestModel insertTestSuite(TestSuite testSuite, Platform platform, AutotestModel autotestModel) {
        if (autotestModel) {
            autotestModel.addTest(testSuite)
            return autotestModel
        }
        AutotestModel autotestJob = findJobWithFewestTestSuites(platform)
        if (autotestJob == null) {
            return null
        }
        autotestJob.addTest(testSuite)
        return autotestJob
    }

    /**
     * Removes all references to autotest models with no test suites
     */
    void removeEmptyAutotestModels() {
        // map.removeAll cannot be used prior version 2.5 (current version 2.4).
        // FindAll cannot be used either because of CPS issues
        Map<String, AutotestModel> modelsWithTestSuites = [:]
        jobs.each { entry ->
            if (entry.value.tests) {
                modelsWithTestSuites[entry.key] = entry.value
            }
        }
        jobs = modelsWithTestSuites
        jobBuckets.each { bucket ->
            bucket.value.removeAll { autotestModel ->
                autotestModel.tests.isEmpty()
            }
        }
        Map<String, List<AutotestModel>> bucketsWithTestSuites = [:]
        jobBuckets.each { entry ->
            if (entry.value) {
                bucketsWithTestSuites[entry.key] = entry.value
            }
        }
        jobBuckets = bucketsWithTestSuites
    }

    /**
     * Returns the AutotestJob models in an array
     * @return A List of AutotestJob models
     */
    List<AutotestModel> getAutotestJobs() {
        return jobs*.value
    }

    /**
     * Returns a String representation of the AutotestJobsModel
     * @return A description of the model
     */
    String getModelLog() {
        StringBuilder sb = new StringBuilder()
        sb.append('----- BUCKETS -----\n')
        jobBuckets.each {
            sb.append("BUCKET: ${it.key} ${it.value.size()}\n")
            it.value.each { model ->
                sb.append("- ${model.name} - tests size: ${model.tests.size()}\n")
                sb.append("Optional region: ${model.region}\n")
                sb.append("Optional extra_args: ${model.extraArguments}\n")
                sb.append('-- Test suites:\n')
                model.tests.each { test ->
                    sb.append("--- ${test.name}\n")
                }
            }
        }
        return sb.toString()
    }

    /**
     * Finds the AutotestJob with the fewest test suites on the given platform
     * @param platform The platform the test suite belongs to
     * @return An AutotestJob, null if none are found
     */
    private AutotestModel findJobWithFewestTestSuites(Platform platform) {
        def candidateJobs = jobs.findAll { it.value.platform == platform.name.toString() }
        if (candidateJobs.isEmpty()) {
            return null
        }
        AutotestModel jobWithFewestTestSuites
        candidateJobs.each {
            if (jobWithFewestTestSuites == null) {
                jobWithFewestTestSuites = it.value
            } else {
                if (it.value.tests.size() < jobWithFewestTestSuites.tests.size()) {
                    jobWithFewestTestSuites = it.value
                }
            }
        }
        return jobWithFewestTestSuites
    }

    /**
     * Inserts an AutoestModel into the jobBuckets. If the key doesn't exist, creates a new entry, otherwise it adds
     * the AutotestModel the list of AutotestModels on the given key.
     * @param key identifier
     * @param autotestModel the model to add
     * @return the added AutotestModel
     */
    // This method can be refactored away by using `.withDefault {[]}` on jobBuckets instantiation.
    private AutotestModel insertAutotestModelIntoJobBucket(String key, AutotestModel autotestModel) {
        if (jobBuckets[key]) {
            jobBuckets[key].add(autotestModel)
        } else {
            jobBuckets[key] = [autotestModel]
        }
        return autotestModel
    }
}
