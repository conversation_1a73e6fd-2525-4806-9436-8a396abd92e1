package com.ea.matrixfiles

import com.ea.lib.model.autotest.AutotestCategory
import com.ea.lib.model.autotest.FrostedAutotestCategory
import com.ea.lib.model.autotest.Name
import com.ea.lib.model.autotest.Platform
import com.ea.lib.model.autotest.TestInfo
import com.ea.lib.model.autotest.TestSuite

class FrostedAutotestMatrix extends AutotestMatrix {

    private static final String KIN_DEV_UNVERIFIED = 'kin-dev-unverified'
    private static final String DEV_NA_BATTLEFIELDGAME = 'dev-na-battlefieldgame'

    @Override
    List<String> getBranches() {
        return [KIN_DEV_UNVERIFIED, DEV_NA_BATTLEFIELDGAME]
    }

    @Override
    List<AutotestCategory> getTestCategories() {
        return [
            new FrostedAutotestCategory(
                name: 'frostedtests',
                testDefinition: 'frostedtests',
                runBilbo: false,
                needGameServer: false,
                uploadJournals: false,
                branches: [
                    branchConfiguration(KIN_DEV_UNVERIFIED, frostedTests_unverified(KIN_DEV_UNVERIFIED), 'H 4,12,20 * * 1-7'),
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, frostedTests('dev-na'), 'H 6,14,22 * * 1-7'),
                ]
            ),
            new FrostedAutotestCategory(
                name: 'frostedtests_extended',
                testDefinition: 'frostedtests_extended',
                runBilbo: false,
                needGameServer: false,
                uploadJournals: false,
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, frostedtests_extended('dev-na'), 'H 20 * * 1-7'),
                ]
            ),
        ]
    }

    @Override
    List<AutotestCategory> getManualTestCategories() {
        return []
    }

    @Override
    Map<String, List<Platform>> getPlatforms() {
        return [:]
    }

    @Override
    boolean shouldLevelsRunInParallel(String branchName) {
        return false
    }

    @Override
    Map getSlackSettings(String branchName) {
        return [:]
    }

    @Override
    Map getManualTestCategoriesSetting(String branchName) {
        return [:]
    }

    private static TestInfo frostedTests(String branchName) {
        def extraArgs = ['--frosted-runtime-args', '"-includeTestDataModules true"', '--frosted-launch-timeout', 1200, '--frosted-test-timeout', 480, '--alltests', '--ensemble-grpc', 'true', '--databaseId', "BattlefieldGameData.${branchName}.Win32.Debug"]
        return new TestInfo(
            testGroup: 'frostedtests',
            slackChannel: '#bf-sqr-bct-notify',
            timeoutHours: 6,
            platforms: [new Platform(name: Name.WIN64)],
            tests: [
                new TestSuite(
                    name: 'frostedtest_DUNLevelDesign',
                    extraArgs: extraArgs,
                    poolType: ''
                ),
                new TestSuite(
                    name: 'frostedtest_DUNLogicPrefabsAndSchematics',
                    extraArgs: extraArgs,
                    poolType: ''
                ),
                new TestSuite(
                    name: 'FrostEdTest_BFWorkflowsLevelArt',
                    extraArgs: extraArgs,
                    poolType: ''
                ),
                new TestSuite(
                    name: 'FrostEdTest_BFWorkflowsTechArt',
                    extraArgs: extraArgs,
                    poolType: ''
                ),
                new TestSuite(
                    name: 'FrostEdTest_BFWorkflowsTechDesignCore',
                    extraArgs: extraArgs,
                    poolType: ''
                ),
            ]
        )
    }

    private static TestInfo frostedTests_unverified(String branchName) {
        def extraArgs = ['--frosted-launch-timeout', 600, '--frosted-test-timeout', 480, '--alltests', '--databaseId', "BattlefieldGameData.${branchName}.Win32.Debug"]
        return new TestInfo(
            testGroup: 'frostedtests',
            slackChannel: '#bf-sqr-bct-notify',
            timeoutHours: 5,
            platforms: [new Platform(name: Name.WIN64)],
            tests: [
                new TestSuite(
                    name: 'frostedtest_DUNLevelDesign',
                    extraArgs: extraArgs,
                    poolType: ''
                ),
                new TestSuite(
                    name: 'frostedtest_DUNLogicPrefabsAndSchematics',
                    extraArgs: extraArgs,
                    poolType: ''
                ),
            ]
        )
    }

    private static TestInfo frostedtests_extended(String branchName) {
        return new TestInfo(
            testGroup: 'frostedtests_extended',
            slackChannel: '#bf-sqr-bct-notify',
            timeoutHours: 6,
            platforms: [new Platform(name: Name.WIN64)],
            tests: [
                new TestSuite(
                    name: 'frostedtest_EcsOrbitalPerformance',
                    extraArgs: ['--frosted-runtime-args', '"-includeTestDataModules true"', '--frosted-launch-timeout', 600, '--frosted-test-timeout', 3000, '--frosted-telemetry-threshold-ms', 1, '--frosted-test-additional-options', '"ForwardFrostEdPerfDataToReport:true"', '--alltests', '--ensemble-grpc', 'true', '--databaseId', "BattlefieldGameData.${branchName}.Win32.Debug"],
                    poolType: ''
                ),
            ]
        )
    }
}
