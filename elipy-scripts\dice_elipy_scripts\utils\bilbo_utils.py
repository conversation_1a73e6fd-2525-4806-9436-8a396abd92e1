"""
bilbo_utils.py
"""

from elipy2 import LOGGER, build_metadata
from typing import Optional

from elipy2.build_metadata import BuildMetadata


def get_latest_drone_build(
    code_branch: str, _bilbo: Optional[build_metadata.BuildMetadataManager] = None
) -> BuildMetadata:
    """Finds the latest drone build"""
    LOGGER.info("Fetching drone builds. Code branch: {}".format(code_branch))
    query_string = "type:drone AND branch.keyword:{} " "AND NOT deleted:*".format(code_branch)
    drone_query = {
        "query": {"query_string": {"query": query_string}},
        "size": 1,
        "sort": {"changelist.keyword": {"order": "desc"}},
    }
    drone_builds = list(_bilbo.get_builds_with_query(drone_query))
    LOGGER.info("{} drone build/s found.".format(len(drone_builds)))
    return drone_builds[0]


def get_latest_drone_changelist(
    code_branch: str, _bilbo: Optional[build_metadata.BuildMetadataManager] = None
) -> str:
    """Finds the latest drone changelist"""
    return get_latest_drone_build(code_branch, _bilbo).source["changelist"]


def verify_tests_have_passed(test_category):
    """
    Verifies whether or not all tests in the given category have passed.

    :param test_category: test category
    :return: True if they have, False otherwise
    """
    # regular test
    if "tests" in test_category:
        return _verify_tests(test_category)
    return False


def _verify_tests(test_category):
    """
    Verifies whether or not all tests in the given category have passed.

    :param test_category: test category to parse
    :return: True if they have passed, False otherwise
    """
    # make sure that all the tests have run.
    if test_category["status"] == "done":
        # Check if every individual test passed
        for test in test_category["tests"]:
            for platform in test_category["tests"][test]:
                if test_category["tests"][test][platform]["status"] != "successful":
                    return False
        return True
    return False
