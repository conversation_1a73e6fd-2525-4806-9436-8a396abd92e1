import com.ea.exceptions.CobraException
import com.ea.lib.LibJenkins
import com.ea.lib.model.JobReference
import com.sonyericsson.jenkins.plugins.bfa.model.FailureCauseBuildAction
import hudson.console.ModelHyperlinkNote
import hudson.model.Result

Result call(int retryCount, List<JobReference> jobReferences, Closure script) {
    return call(retryCount, jobReferences, false, script)
}

/**
 * retryOnFailureCause.groovy
 * Wrapper to add retry functionality to a task. The given script is retried {@code retryCount} times when
 * any downstream jobs fail. Throws an error if {@code retryCount} retries is reached.
 * @param retryCount How many retries. Set to zero to disable retry procedure
 * @param jobReferences An empty {@link List} that should be populated with every downstream job
 * @param allowFailure if set to true, currentResult won't be updated on a failure. False is recommended.
 * @param script Closure to retry
 * @return The final result
 */
Result call(int retryCount, List<JobReference> jobReferences, boolean allowFailure, Closure script) {
    Result finalResult = Result.SUCCESS
    int retries = 0
    waitUntil {
        if (retries > retryCount) {
            if (!allowFailure) {
                finalResult = finalResult.combine(Result.FAILURE)
            }
            currentBuild.result = finalResult.toString()
            DownstreamErrorReporting(currentBuild)
            throw new CobraException('Maximum number of retries reached. Exiting job.')
        }
        List<JobReference> tempJobReferences = []
        if (jobReferences) {
            StringBuilder sb = new StringBuilder()
            sb.append("[WARNING] ${jobReferences.size()} job(s) failed. BFA har marked those failures as retriable (retry failure cause). Retrying:")
            jobReferences.each {
                sb.append("\n- ${ModelHyperlinkNote.encodeTo("/${it.downstreamJob.rawBuild.url}", it.downstreamJob.rawBuild.fullDisplayName)}")
            }
            sb.append("\n Retry number ${retries}.")
            echo sb.toString()
            Map jobs = [:]
            jobReferences.each { buildReference ->
                jobs[buildReference.jobName] = {
                    def downstreamJob = build(job: buildReference.jobName, parameters: buildReference.parameters,
                        propagate: buildReference.propagate, wait: buildReference.wait)
                    tempJobReferences << new JobReference(downstreamJob: downstreamJob, jobName: buildReference.jobName,
                        parameters: buildReference.parameters, propagate: buildReference.propagate, wait: buildReference.wait,
                        buildCallback: buildReference.buildCallback)
                    if (buildReference.buildCallback) {
                        buildReference.buildCallback.call(downstreamJob)
                    }
                    LibJenkins.printFailureMessage(this, downstreamJob, allowFailure)
                    LibJenkins.printRunningJobs(this)
                }
            }
            parallel(jobs)
            jobReferences.clear()
            jobReferences.addAll(tempJobReferences)
        } else {
            script.call()
        }
        tempJobReferences.clear()
        jobReferences.each { job ->
            if (containsRetryFailureCause(job.downstreamJob)) {
                tempJobReferences << job
            } else if (containsGameFailureCause(job.downstreamJob) && !allowFailure) {
                String jobLink = ModelHyperlinkNote.encodeTo("/${job.downstreamJob.rawBuild.url}", job.downstreamJob.rawBuild.fullDisplayName)
                echo("Game team issue found in ${jobLink}, not rebuilding.")
                finalResult = finalResult.combine(Result.fromString(job.downstreamJob.result))
            } else if (job.downstreamJob.result != Result.SUCCESS.toString() && !allowFailure) {
                String jobLink = ModelHyperlinkNote.encodeTo("/${job.downstreamJob.rawBuild.url}", job.downstreamJob.rawBuild.fullDisplayName)
                echo("No retriable issue found in ${jobLink}, not rebuilding.")
                finalResult = finalResult.combine(Result.fromString(job.downstreamJob.result))
            }
        }
        jobReferences.clear()
        jobReferences.addAll(tempJobReferences)
        if (jobReferences && retryCount != 0) {
            retries++
            return false
        } else if (jobReferences) {
            finalResult = finalResult.combine(Result.FAILURE)
        }
        return true
    }
    currentBuild.result = finalResult.toString()
    DownstreamErrorReporting(currentBuild)
    return finalResult
}

/**
 * Returns true if the job should retry, false otherwise
 * @param downstreamJob the job to analyze
 * @return true if it should retry, false otherwise
 */
private static boolean containsRetryFailureCause(def downstreamJob) {
    def action = downstreamJob.rawBuild.getAction(FailureCauseBuildAction)
    if (action) {
        if (action.foundFailureCauses.any { it.categories && it.categories.contains('retry') }) {
            return true
        }
    }
    return false
}

/**
 * Returns true if the job failed due to a game team failure, false otherwise
 * @param downstreamJob the job to analyze
 * @param allowFailure if set to true, currentResult won't be updated on a failure. False is recommended.
 * @return true if it it's a game team failure, false otherwise
 */
private static boolean containsGameFailureCause(def downstreamJob) {
    def action = downstreamJob.rawBuild.getAction(FailureCauseBuildAction)
    if (action) {
        if (action.foundFailureCauses.any { it.categories && it.categories.contains('game') }) {
            return true
        }
    }
    return false
}
