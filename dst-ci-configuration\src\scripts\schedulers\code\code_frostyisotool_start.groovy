package scripts.schedulers.code

import com.ea.project.GetBranchFile

def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
def settings_map = branchfile.general_settings + branchfile.standard_jobs_settings
def project = ProjectClass(env.project_name)

/**
 * code_frostyisotool_start.groovy
 */
pipeline {
    agent { label '(scheduler && master) || scheduler || executor_agent' }
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    parameters {
        string name: 'code_changelist', description: 'Specifies code changelist to sync.', trim: true
        booleanParam name: 'clean_local', defaultValue: false, description: 'If true, TnT/Local will be deleted at the beginning of the run.'
        booleanParam name: 'submit', defaultValue: true, description: 'Submit the result to perforce. Uncheck this if you want dry-run'
    }
    stages {
        stage('Get changelist from Perforce') {
            steps {
                script {
                    def filter_paths = branchfile.general_settings?.filter_paths_code_frostyisotool ?: []
                    P4PreviewCode(project, 'frostyisotool', env.code_folder, env.code_branch, env.non_virtual_code_folder, env.non_virtual_code_branch, [], filter_paths, settings_map)
                }
            }
        }
        stage('Trigger FrostyIsoTool job') {
            steps {
                script {
                    def code_changelist = params.code_changelist ?: env.P4_CHANGELIST
                    def clean_local = params.clean_local
                    def submit = params.submit

                    def args = [
                        string(name: 'code_changelist', value: code_changelist),
                        booleanParam(name: 'clean_local', value: clean_local),
                        booleanParam(name: 'submit', value: submit),
                    ]

                    def inject_map = [
                        'code_changelist': code_changelist,
                    ]
                    EnvInject(currentBuild, inject_map)
                    currentBuild.displayName = env.JOB_NAME + '.' + code_changelist

                    def job_name = env.branch_name + '.code.frostyisotool.build'
                    def frostyisotool_job = build(job: job_name, parameters: args, propagate: false)

                    currentBuild.result = frostyisotool_job.result.toString()

                    def slack_settings = branchfile.standard_jobs_settings?.slack_channel_frostyisotool_drone
                    SlackMessageNew(currentBuild, slack_settings, project.short_name)

                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
}
