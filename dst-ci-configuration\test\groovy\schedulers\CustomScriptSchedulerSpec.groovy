package schedulers

import com.ea.lib.model.branchsettings.CustomScriptConfiguration
import support.DeclarativePipelineSpockTest

class CustomScriptSchedulerSpec extends DeclarativePipelineSpockTest {

    void setup() {
        binding.setVariable('env', [
            CODE_BRANCH            : 'code-branch',
            CODE_FOLDER            : 'code-folder',
            NON_VIRTUAL_CODE_BRANCH: 'kin-dev',
            NON_VIRTUAL_CODE_FOLDER: 'dev',
            P4_CHANGELIST          : '234',
            JOB_NAME               : 'my-job',
            BRANCH_NAME            : 'a-branch',
        ])
        binding.setVariable('params', [
            script_args: '--arg1 arg',
        ])
        helper.registerAllowedMethod('get_branchfile', [String, String]) { projectName, branchName ->
            [
                general_settings      : [
                    custom_script: [
                        scheduler_test: new CustomScriptConfiguration(
                            scriptPath: 'test/path',
                            executable: 'cmd',
                            jobName: 'jobName',
                            cronTrigger: '@daily',
                            referenceJob: '.data.start',
                            environmentVariables: 'P4PORT=P4_CODE_SERVER',
                        ),
                    ],
                ],
                standard_jobs_settings: [:],

            ]
        }
        helper.with {
            registerAllowedMethod('setPollScmTriggers', []) {}
            registerAllowedMethod('EnvInject', [Map, Map]) { currentBuild, injectMap ->
                binding.setVariable('env', binding.getVariable('env') + injectMap)
            }
            registerAllowedMethod('getLastStableCodeChangelist', [String]) { codeChangelist -> ['123'] }
        }
    }

    void 'test CustomScriptScheduler runs'() {
        when:
        runScript('CustomScriptScheduler.groovy')
        printCallStack()
        then:
        testNonRegression()
    }
}
