"""
patch_databuild.py

The patch_databuild script creates bundles with the difference between the
latest released version and the current version. Link to patching flow diagram
https://drive.google.com/file/d/1sMaszZ11mNt8Kx4j-F0D7jJN5F0d-PUz/view?usp=sharing

General setup:
    - Clean up the machine by killing running processes.
    - Initialize objects using packages from Elipy core, to be used later.
        - One instance of data.DataUtils().
        - One instance of filer.FilerUtils().
    - Set data directory, since the Frostbite build process sometimes gets an
      incorrect default value for this.
    - Copy code binaries for pipeline (produced when building the code platform tool)
      from a network share.
   - Clean the Avalanche state.
    - Set baseline branches and changelists.
    - Set Avalanche database info:
        - Database to export to.
        - Database to import from, if we have a baseline to import information for.
    - Cook the data.
    - Handle bundle folders:
        - Set new bundle paths.
        - Set paths also for combine bundles.
    - Export bundles:
        - If patching using AvalancheCLI:
            - Export head bundles.
            - If we have a baseline set:
                - Import baseline bundles.
                - Generate the delta for the patch.
        - Also export combine bundles.
    - Export the state from Avalanche.
    - Cleanup the Avalanche information.
    - Deploy the bundles.
        - Also deploy combine bundles.
    - Deploy the state.
    - Register the build in Metadata manager.
        - Also register combine bundles.


    Examples:
        * elipy --location criterion patch_databuild Data ps5 ShippingLevels
          --code-branch build-release --code-changelist 436395 --data-branch build-release
          --data-changelist 436395 --patch-type incremental --data-clean False
          --disc-code-branch build-release --disc-code-changelist 368467
          --disc-data-branch build-release --disc-data-changelist 368467 --first-patch
        * elipy --location criterion patch_databuild Data ps5 ShippingLevels
          --code-branch build-release --code-changelist 436395 --data-branch build-release
          --data-changelist 436395 --patch-type incremental --data-clean False
          --disc-code-branch build-release --disc-code-changelist 368467
          --disc-data-branch build-release --disc-data-changelist 368467 --first-patch
          --no-baseline-set --import-avalanche-state
        * elipy --location criterion patch_databuild Data ps5 ShippingLevels
          --code-branch build-release --code-changelist 436395 --data-branch build-release
          --data-changelist 436395 --patch-type incremental --data-clean False
          --disc-code-branch build-release --disc-code-changelist 368467
          --disc-data-branch build-release --disc-data-changelist 368467
          --patch-code-branch build-release --patch-code-changelist 417610
          --patch-data-branch build-release --patch-data-changelist 417610
        * elipy --location criterion patch_databuild Data ps5 ShippingLevels
          --code-branch build-release --code-changelist 436395 --data-branch build-release
          --data-changelist 436395 --patch-type incremental --data-clean False
          --disc-code-branch build-release --disc-code-changelist 368467
          --disc-data-branch build-release --disc-data-changelist 368467
          --patch-code-branch build-release --patch-code-changelist 417610
          --patch-data-branch build-release --patch-data-changelist 417610
          --enable-compression true --clean-master-version-check true
        * elipy --location dice patch_databuild kindata xb1 ShippingLevels
          --code-branch kin-release --code-changelist 17903931 --data-branch kin-release
          --data-changelist 5026337 --patch-type incremental --data-clean False
          --disc-code-branch kin-release --disc-code-changelist 12409338
          --disc-data-branch kin-release --disc-data-changelist 4592495
          --patch-code-branch kin-release --patch-code-changelist 17743749
          --patch-data-branch kin-release --patch-data-changelist 5019321
          --use-recompression-cache false --clean-master-version-check false
        *  elipy --location dice patch_databuild kindata xb1 ShippingLevels
          --code-branch kin-stage --code-changelist 18266389 --data-branch kin-stage
          --data-changelist 5038726 --patch-type incremental --data-clean False
          --pipeline-args -Pipeline.AbortOnPipelineError --pipeline-args true
          --disc-code-branch kin-release --disc-code-changelist 12409338
          --disc-data-branch kin-release --disc-data-changelist 4592495
          --patch-code-branch kin-release --patch-code-changelist 17903931
          --patch-data-branch kin-release --patch-data-changelist 5026337
          --use-recompression-cache false --clean-master-version-check false
"""
import click
import os
from dice_elipy_scripts.utils.data_build_utils import (
    get_export_compression_args,
    run_expression_debug_data,
)
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.state_utils import export_head_bundles, import_avalanche_data_state
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import (
    avalanche,
    build_metadata_utils,
    data,
    filer,
    filer_paths,
    LOGGER,
    running_processes,
    SETTINGS,
    frostbite_core,
)
from elipy2.cli import pass_context
from elipy2.exceptions import ELIPYException
from elipy2.telemetry import collect_metrics


@click.command(
    "patch_databuild",
    short_help="Build data aginst baseline state, and deploy delta and new state.",
)
@click.argument("data_dir")
@click.argument("platform")
@click.argument("assets", nargs=-1, required=True)
@click.option("--data-branch", help="Data branch.", required=True)
@click.option("--data-changelist", help="Data changelist number.", required=True)
@click.option("--code-branch", help="Code branch.", required=True)
@click.option("--code-changelist", help="Code changelist number", required=True)
@click.option("--pipeline-args", multiple=True, help="Pipeline arguments for data build.")
@click.option(
    "--first-patch/--not-first-patch",
    default=False,
    help="Flag for building the first patch.",
)
@click.option(
    "--baseline-set/--no-baseline-set",
    default=True,
    help="flag for baseline being set.",
)
@click.option("--dry-run", is_flag=True, help="Build patch without deploying")
@click.option(
    "--enable-compression",
    default=False,
    help="Add compression flag when exporting bundles",
)
@click.option(
    "--use-recompression-cache",
    default=False,
    help="Alternative Avalanche server to use for the recompression cache",
)
@click.option("--staging-stream", is_flag=True, help="Skip clean on staging streams.")
@click.option("--import-avalanche-state", is_flag=True, help="Skip clean on staging streams.")
@click.option("--disc-data-branch", help="Which branch the disc data was built on.", required=False)
@click.option(
    "--disc-data-changelist",
    required=False,
    help="Which changelist the disc data was deployed from.",
)
@click.option("--disc-code-branch", help="Which branch the disc code was built on.", required=False)
@click.option(
    "--disc-code-changelist",
    required=False,
    help="Which changelist the disc code was deployed from.",
)
@click.option(
    "--patch-data-branch",
    required=False,
    help="Which branch the latest live patch data was built on.",
)
@click.option(
    "--patch-data-changelist",
    required=False,
    help="Which changelist the latest live patch data was deployed from.",
)
@click.option(
    "--patch-code-branch",
    required=False,
    help="Which branch the latest live patch code was built on.",
)
@click.option(
    "--patch-code-changelist",
    required=False,
    help="Which changelist the latest live patch code was deployed from.",
)
@click.option(
    "--patch-type",
    type=click.Choice(["from_disk", "incremental", "disc_build"]),
    default="incremental",
    help="What kind of patch are we producing",
    required=True,
)
@click.option(
    "--clean-master-version-check",
    default=False,
    help="Run clean on master version update.",
)
@click.option(
    "--data-clean",
    default="false",
    help="Clean Avalanche if --data-clean true is passed.",
)
@click.option(
    "--skip-importing-baseline-state",
    is_flag=True,
    help="Skip importing Avalanche state for the baseline.",
)
@click.option(
    "--expression-debug-data",
    default=False,
    help="Export expression debug data after data cook.",
)
@click.option(
    "--clear-cache",
    is_flag=True,
    help="Clear the cache after importing state, but before cooking.",
)
@click.option(
    "--use-head-bundles-as-base-bundles",
    is_flag=True,
    help="Use head bundles as base bundles. Experimental, only use if requested by the game team.",
)
@click.option(
    "--virtual-branch-override",
    type=bool,
    default=False,
    help="Override the Perforce depot branch with the virtual branch used in the job",
)
@click.option(
    "--export-combine-bundles",
    default=False,
    help="Export bundles prepared for being used in the avalanche combine process.",
)
@click.option(
    "--standalone-baseline",
    is_flag=True,
    help="Use a standalone build as a baseline for a combined build.",
)
# pylint: disable=too-many-locals
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    data_dir,
    platform,
    assets,
    data_branch,
    data_changelist,
    code_branch,
    code_changelist,
    pipeline_args,
    first_patch,
    baseline_set,
    dry_run,
    enable_compression,
    use_recompression_cache,
    staging_stream,
    import_avalanche_state,
    disc_code_branch,
    disc_code_changelist,
    disc_data_branch,
    disc_data_changelist,
    patch_code_branch,
    patch_code_changelist,
    patch_data_branch,
    patch_data_changelist,
    patch_type,
    clean_master_version_check,
    data_clean,
    skip_importing_baseline_state,
    expression_debug_data,
    clear_cache,
    use_head_bundles_as_base_bundles,
    virtual_branch_override,
    export_combine_bundles,
    standalone_baseline,
):
    """
    Build data against baseline state, and deploy delta and new state.

    Examples:
        * elipy --location criterion patch_databuild Data ps5 ShippingLevels
          --code-branch build-release --code-changelist 436395 --data-branch build-release
          --data-changelist 436395 --patch-type incremental --data-clean False
          --disc-code-branch build-release --disc-code-changelist 368467
          --disc-data-branch build-release --disc-data-changelist 368467 --first-patch
        * elipy --location criterion patch_databuild Data ps5 ShippingLevels
          --code-branch build-release --code-changelist 436395 --data-branch build-release
          --data-changelist 436395 --patch-type incremental --data-clean False
          --disc-code-branch build-release --disc-code-changelist 368467
          --disc-data-branch build-release --disc-data-changelist 368467 --first-patch
          --no-baseline-set --import-avalanche-state
        * elipy --location criterion patch_databuild Data ps5 ShippingLevels
          --code-branch build-release --code-changelist 436395 --data-branch build-release
          --data-changelist 436395 --patch-type incremental --data-clean False
          --disc-code-branch build-release --disc-code-changelist 368467
          --disc-data-branch build-release --disc-data-changelist 368467
          --patch-code-branch build-release --patch-code-changelist 417610
          --patch-data-branch build-release --patch-data-changelist 417610
        * elipy --location criterion patch_databuild Data ps5 ShippingLevels
          --code-branch build-release --code-changelist 436395 --data-branch build-release
          --data-changelist 436395 --patch-type incremental --data-clean False
          --disc-code-branch build-release --disc-code-changelist 368467
          --disc-data-branch build-release --disc-data-changelist 368467
          --patch-code-branch build-release --patch-code-changelist 417610
          --patch-data-branch build-release --patch-data-changelist 417610
          --enable-compression true --clean-master-version-check true
        * elipy --location dice patch_databuild kindata xb1 ShippingLevels
          --code-branch kin-release --code-changelist 17903931 --data-branch kin-release
          --data-changelist 5026337 --patch-type incremental --data-clean False
          --disc-code-branch kin-release --disc-code-changelist 12409338
          --disc-data-branch kin-release --disc-data-changelist 4592495
          --patch-code-branch kin-release --patch-code-changelist 17743749
          --patch-data-branch kin-release --patch-data-changelist 5019321
          --use-recompression-cache true --clean-master-version-check true
    """
    # Adding sentry tags
    add_sentry_tags(__file__)

    metadata_manager = build_metadata_utils.setup_metadata_manager()

    # Clean up before running the job.
    running_processes.kill()

    # Initialize
    _data = data.DataUtils(platform, list(assets), monkey_build_label=data_changelist)
    _filer = filer.FilerUtils()
    pipeline_args = list(pipeline_args)
    indexing_args = []

    # Set data directory.
    _data.set_datadir(data_dir)

    bundles_dir_name = "bundles"
    if export_combine_bundles:
        bundles_dir_name = "combine_bundles"
    dest = filer_paths.get_head_bundles_path(
        data_branch, data_changelist, code_branch, code_changelist, platform, bundles_dir_name
    )
    if os.path.exists(dest) and not dry_run:
        raise ELIPYException(
            "Attempting to deploy to a path that already exists.\
            Possibly because a previous build succeeded in deploying before failing.\
            This can cause us to lose bundles and is not allowed."
        )

    # Fetch pipeline binary
    _filer.fetch_code(code_branch, code_changelist, "pipeline", "release")

    if virtual_branch_override:
        pipeline_args += ["-BuildSettings.ForceBranch", data_branch]
        pipeline_args += ["-stateId", data_branch]
        indexing_args += ["-stateId", data_branch]

    if data_clean.lower() == "true" or not staging_stream:
        _data.clean(extra_pipeline_args=indexing_args)

    export_db_name = avalanche.get_db_name(
        platform,
        data_changelist,
        code_changelist,
        prefix="baseline_{}".format(frostbite_core.get_licensee_id()),
    )
    pipeline_args += [frostbite_core.get_emit_arg(), "-exportState", export_db_name]

    import_state_args = []
    if baseline_set:
        if not skip_importing_baseline_state:
            baseline_state_path = os.path.join(
                frostbite_core.get_tnt_root(), "local", "baseline_state"
            )
            avalanche_baseline_data_cl = patch_data_changelist
            avalanche_baseline_data_branch = patch_data_branch
            avalanche_baseline_code_cl = patch_code_changelist
            avalanche_baseline_code_branch = patch_code_branch

            if first_patch:
                avalanche_baseline_data_cl = disc_data_changelist
                avalanche_baseline_data_branch = disc_data_branch
                avalanche_baseline_code_cl = disc_code_changelist
                avalanche_baseline_code_branch = disc_code_branch

            baseline_bundles_dir_name = bundles_dir_name
            if standalone_baseline:
                baseline_bundles_dir_name = "bundles"

            _filer.fetch_baseline_state(
                data_branch=avalanche_baseline_data_branch,
                data_changelist=avalanche_baseline_data_cl,
                code_branch=avalanche_baseline_code_branch,
                code_changelist=avalanche_baseline_code_cl,
                platform=platform,
                dest=baseline_state_path,
                bundles_dir_name=baseline_bundles_dir_name,
            )

            # Import baseline to Avalanche
            import_db_name = avalanche.get_db_name(
                platform,
                avalanche_baseline_data_cl,
                avalanche_baseline_code_cl,
                prefix="baseline_{}".format(frostbite_core.get_licensee_id()),
            )
            avalanche.import_baselinedb(baseline_state_path, import_db_name, prefix="")
            import_state_args = ["-importState", import_db_name]
    else:
        if import_avalanche_state:
            import_state_args = import_avalanche_data_state(
                data_branch, code_branch, platform, _filer, data_changelist
            )
    if not clear_cache:
        pipeline_args += import_state_args

    # Build data
    try:
        if clear_cache:
            _data.clear_cache(pipeline_args=import_state_args)

        _data.cook(
            pipeline_args=pipeline_args,
            indexing_args=indexing_args,
            collect_mdmps=True,
            clean_master_version_check=clean_master_version_check,
        )

        # Set main paths for all bundle types under TnT\Local.
        local_bundles = os.path.join(frostbite_core.get_tnt_root(), "local", bundles_dir_name)
        # Set path where we'll keep the bundles from the current build.
        local_headbundles = os.path.join(local_bundles, "head")
        # Set path where we'll copy the bundles from baseline to.
        local_basebundles = os.path.join(local_bundles, "base")
        # Set path where we'll write the delta bundles (current vs. baseline) to.
        local_deltabundles = os.path.join(local_bundles, "delta")
        # Set path where we'll deploy the local state after build
        local_state = os.path.join(local_bundles, "state")

        if not export_combine_bundles:
            # Deploy HEAD bundles to disk (input to ddelta)
            deploy_extra_args = []
            if enable_compression is True:
                deploy_extra_args = get_export_compression_args(platform)
            if use_recompression_cache is True:
                recompression_cache = SETTINGS.get("recompression_cache")[platform]
                LOGGER.info(
                    "Using Alternative Avalanche server for the "
                    "recompression cache {}".format(recompression_cache)
                )
                deploy_extra_args += ["--recompressionCache", recompression_cache]

            export_head_bundles(platform, local_headbundles, deploy_extra_args)

            if baseline_set:
                if first_patch or patch_type == "from_disk":
                    # For first patch, use head
                    _filer.fetch_baseline_bundles_head(
                        data_branch=disc_data_branch,
                        data_changelist=disc_data_changelist,
                        code_branch=disc_code_branch,
                        code_changelist=disc_code_changelist,
                        platform=platform,
                        dest=local_basebundles,
                    )
                else:
                    # Fetch base bundles from the network share
                    _filer.fetch_baseline_bundles(
                        data_branch=patch_data_branch,
                        data_changelist=patch_data_changelist,
                        code_branch=patch_code_branch,
                        code_changelist=patch_code_changelist,
                        platform=platform,
                        dest=local_basebundles,
                    )

                LOGGER.info("Patching using AvalancheCLI")
                if use_head_bundles_as_base_bundles:
                    # If the baseline for some reason is broken, we can try
                    # and use the current headbundles also as basebundles.
                    # This should only be tried when requested by the game team.
                    local_basebundles = local_headbundles
                avalanche.ddelta(local_headbundles, local_basebundles, local_deltabundles)

        else:
            # Deploy combine head bundles to disk (input to ddelta).
            # The ddelta step is located in the patchfrosty job for combine builds,
            # since we need to run combine for head and base before we run ddelta.
            combine_deploy_args = ["-s", "pre-combine.yaml"]
            export_head_bundles(
                platform,
                local_headbundles,
                deploy_extra_args=combine_deploy_args,
                include_platform=False,
                ordering_algorithm=None,
            )

        # Export state from filer --> PS script
        avalanche.export(export_db_name, local_state)

        if expression_debug_data:
            run_expression_debug_data(
                code_changelist,
                data_changelist,
                code_branch,
                data_branch,
                platform,
                builder_instance=_data,
                clean_master_version_check=clean_master_version_check,
            )
    finally:
        avalanche.drop(export_db_name)

    # Deploy to the network share, and register in Bilbo.
    if not dry_run:
        # Deploy head bundles for safekeeping
        _filer.deploy_head_bundles(
            local_headbundles,
            data_branch=data_branch,
            data_changelist=data_changelist,
            code_branch=code_branch,
            code_changelist=code_changelist,
            platform=platform,
            bundles_dir_name=bundles_dir_name,
        )

        if not export_combine_bundles and baseline_set:
            # Deploy delta bundles, we package this in the actual build,
            # and use as input for next patch if we ship this.
            _filer.deploy_delta_bundles(
                local_deltabundles,
                data_branch=data_branch,
                data_changelist=data_changelist,
                code_branch=code_branch,
                code_changelist=code_changelist,
                platform=platform,
            )

        # Register the bundles in Bilbo.
        metadata_manager.register_bundles(
            data_branch=data_branch,
            data_changelist=data_changelist,
            code_branch=code_branch,
            code_changelist=code_changelist,
            platform=platform,
            bundles_type=bundles_dir_name,
        )

        # Deploy the local state to Filer.
        _filer.deploy_state(
            local_state,
            data_branch=data_branch,
            data_changelist=data_changelist,
            code_branch=code_branch,
            code_changelist=code_changelist,
            platform=platform,
            bundles_dir_name=bundles_dir_name,
        )
