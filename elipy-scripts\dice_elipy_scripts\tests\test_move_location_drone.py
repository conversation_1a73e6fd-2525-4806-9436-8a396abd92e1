"""
test_move_location_drone.py

Unit testing for move_location_drone
"""
import os
import unittest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from mock import patch
from dice_elipy_scripts.move_location_drone import cli
from elipy2.exceptions import CoreException


class TestMoveLocationDrone(unittest.TestCase):
    OPTION_PARALLEL_COPY = "--parallel-copy"
    OPTION_CODE_BRANCH = "--code-branch"
    OPTION_CODE_CHANGELIST = "--code-changelist"
    OPTION_DATA_BRANCH = "--data-branch"
    OPTION_DATA_CHANGELIST = "--data-changelist"
    OPTION_PLATFORM = "--platform"
    OPTION_CONFIG = "--config"
    OPTION_OLD_DRONE_SETUP = "--old-drone-setup"
    OPTION_DEST_LOCATION = "--dest-location"

    VALUE_CODE_BRANCH = "code-branch"
    VALUE_CODE_CHANGELIST = "123456"
    VALUE_DATA_CHANGELIST = "234567"
    VALUE_PLATFORM = "tool"
    VALUE_CONFIG = "release"
    VALUE_DEST_LOCATION = "RippleEffect"

    FIXED_ARGS = [
        OPTION_CODE_BRANCH,
        VALUE_CODE_BRANCH,
        OPTION_CODE_CHANGELIST,
        VALUE_CODE_CHANGELIST,
        OPTION_DEST_LOCATION,
        VALUE_DEST_LOCATION,
    ]

    BASIC_ARGS = FIXED_ARGS + [
        OPTION_DATA_CHANGELIST,
        VALUE_DATA_CHANGELIST,
        OPTION_PLATFORM,
        VALUE_PLATFORM,
        OPTION_CONFIG,
        VALUE_CONFIG,
        OPTION_PARALLEL_COPY,
    ]

    def setUp(self):
        patcher_metrics = patch("elipy2.telemetry.collect_metrics")
        self.mock_metrics = patcher_metrics.start()
        self.addCleanup(patcher_metrics.stop)

        self.source_location = os.path.join("TnT", "Code", "source")
        self.dest_location = os.path.join("filer", "location", "destination")
        self.lock_file = os.path.join("filer", "location", f"{self.VALUE_CONFIG}.locked")
        self.marker_file = os.path.join(self.dest_location, ".copy_complete")

    @patch("elipy2.core.robocopy")
    @patch("os.path.exists")
    @patch("dice_elipy_scripts.move_location_drone.open")
    @patch("elipy2.filer_paths.get_code_build_path")
    def test_move_location_drone_success(self, mock_path, mock_open, mock_exists, mock_robocopy):
        mock_path.side_effect = [self.source_location, self.dest_location]
        mock_exists.return_value = False

        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)

        self.assertEqual(result.exit_code, 0)
        mock_robocopy.assert_called_once_with(
            self.source_location, self.dest_location, lock_file=self.lock_file
        )
        mock_open.assert_called_once()

    @patch("elipy2.core.robocopy")
    @patch("os.path.exists")
    @patch("dice_elipy_scripts.move_location_drone.open")
    @patch("elipy2.filer_paths.get_code_build_path")
    def test_move_location_drone_robocopy_exception(
        self, mock_path, mock_open, mock_exists, mock_robocopy
    ):
        mock_path.side_effect = [self.source_location, self.dest_location]
        mock_exists.return_value = False
        mock_robocopy.side_effect = CoreException("Robocopy failed")

        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)

        self.assertEqual(result.exit_code, 1)
        mock_robocopy.assert_called_once_with(
            self.source_location, self.dest_location, lock_file=self.lock_file
        )
        mock_open.assert_not_called()

    @patch("elipy2.core.robocopy")
    @patch("os.path.exists")
    @patch("dice_elipy_scripts.move_location_drone.open")
    @patch("elipy2.filer_paths.get_code_build_path")
    def test_move_location_drone_previous_copy_incomplete(
        self, mock_path, mock_open, mock_exists, mock_robocopy
    ):
        mock_path.side_effect = [self.source_location, self.dest_location]
        mock_exists.side_effect = lambda x: True if x == self.dest_location else False
        mock_robocopy.return_value = None

        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)

        self.assertEqual(result.exit_code, 0)
        mock_robocopy.assert_called_once_with(
            self.source_location, self.dest_location, lock_file=self.lock_file
        )
        mock_open.assert_called_once()

    @patch("elipy2.core.robocopy")
    @patch("os.path.exists")
    @patch("elipy2.filer_paths.get_code_build_path")
    def test_move_location_drone_previous_copy_complete(
        self, mock_path, mock_exists, mock_robocopy
    ):
        mock_path.side_effect = [self.source_location, self.dest_location]
        mock_exists.side_effect = (
            lambda x: True if x == self.marker_file or x == self.dest_location else False
        )
        mock_robocopy.return_value = None

        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)

        self.assertEqual(result.exit_code, 0)
        mock_robocopy.assert_not_called()


if __name__ == "__main__":
    unittest.main()
