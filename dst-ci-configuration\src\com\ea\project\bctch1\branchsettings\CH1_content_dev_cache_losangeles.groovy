package com.ea.project.bctch1.branchsettings

import com.ea.project.bctch1.BctCh1

class CH1_content_dev_cache_losangeles {
    // Settings for jobs
    static Class project = BctCh1
    static Map general_settings = [
        dataset                 : project.dataset,
        elipy_call              : project.elipy_call_eala + ' --use-fbenv-core',
        elipy_install_call      : project.elipy_install_call,
        frostbite_licensee      : project.frostbite_licensee,
        workspace_root          : project.workspace_root,
        azure_elipy_call        : project.azure_elipy_call,
        azure_elipy_install_call: project.azure_elipy_install_call,
        azure_workspace_root    : project.azure_workspace_root,
        p4_code_creds           : 'bct-la-p4',
        p4_code_server          : 'dicela-p4edge-fb.la.ad.ea.com:2001',
    ]
    static Map code_settings = [
        skip_code_build_if_no_changes: false,
        slack_channel_code           : [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        report_build_version         : ' --reporting-build-version-id %code_changelist%',
        poolbuild_data               : true,
        skip_frosty_trigger          : true,
        sndbs_enabled                : true,
    ]
    static Map data_settings = [
        data_reference_job     : 'CH1-content-dev-cache-losangeles.code.start',
        enable_daily_data_clean: false,
        enable_lkg_cleaning    : true,
        poolbuild_data         : true,
        slack_channel_data     : [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        statebuild_data        : false,
        statebuild_webexport   : false,
        webexport_allow_failure: false,
        webexport_branch       : false,
    ]
    static Map frosty_settings = [:]
    static Map standard_jobs_settings = code_settings + data_settings + frosty_settings + [
        asset                       : 'DevLevels',
        extra_data_args             : ['--pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        enable_lkg_p4_counters      : true,
        skip_icepick_settings_file  : true,
        statebuild_code             : true,
        strip_symbols               : false,
        job_label_statebuild        : 'ch1-content-dev-cache',
        poolbuild_label             : 'ch1-content-dev-cache',
        trigger_type_code           : 'scm',
        trigger_type_data           : 'none',
        timeout_hours_data          : 24,
        separate_symbol_store_upload: false,
        expression_debug_data       : false,
        skip_symbols_backup         : true,
        skip_symbols_to_symstore    : true,
    ]
    static Map icepick_settings = [
        ignore_icepick_exit_code: false
    ]
    static Map preflight_settings = [:]

    static List code_matrix = [
        [name: 'tool', configs: ['release']],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = [
        [name: '.data.start', args: []],
    ]
    static List data_matrix = [
        [name: 'win64'],
        [name: 'ps5'],
        [name: 'xbsx'],
        [name: 'validate-frosted'],
    ]
    static List data_downstream_matrix = []
    static List patchdata_matrix = []
    static List frosty_matrix = []
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = []
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
