{#
    Command:
        bilbo_register_smoke
            short_help: Registers a Smoked build in the configured metadata services.

    Arguments:

    Required variables:
        code_branch
            help: Perforce code branch/stream name.
            required: True
        code_changelist
            required: True
            help: Changelist number of code build used to verify data.

    Optional variables:
        data_changelist
            default: None
            help: Changelist number of data used to verify data.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    bilbo_register_smoke
    --code-branch {{ code_branch }}
    --code-changelist {{ code_changelist }}
    {%- if data_changelist %}
    --data-changelist {{ data_changelist }}
    {%- endif %}
  displayName: elipy bilbo_register_smoke
