"""
avalanchecli_nuke.py
"""
import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import avalanche, data
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command("avalanchecli_nuke", short_help="Perform Avalanchecli nuke -y on the localhost.")
@click.option("--datadir", help="Which datadir to nuke from.", required=True)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(_, datadir):  # pylint: disable=unused-argument
    """
    Perform Avalanchecli nuke -y on the localhost.
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    data.DataUtils.set_datadir(datadir)
    avalanche.nuke()
