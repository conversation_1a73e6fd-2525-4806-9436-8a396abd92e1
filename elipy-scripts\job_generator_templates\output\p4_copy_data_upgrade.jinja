{#
    Command:
        p4_copy_data_upgrade
            short_help: Copy code, and use this to upgrade data.

    Arguments:

    Required variables:
        copy_mapping
            required: True
            help: Perforce mapping for the copy step.
        p4_client
            required: True
            help: Perforce client/workspace.
        p4_port
            required: True
            help: Perforce port/server.
        source_branch
            required: True
            help: Perforce branch/stream name.
        source_changelist
            required: True
            help: Perforce changelist number.
        target_changelist
            required: True
            help: Perforce changelist number.

    Optional variables:
        batch_file
            default: UpgradeLocal.bat
            help: Batch file to run for the upgrade step.
        clean
            default: false
            help: Delete TnT/Local if --clean true is passed.
        data_directory
            default: None
            help: Which data directory to use for fetching licensee settings.
        domain_user
            default: None
            help: The user to authenticate to package server as DOMAIN\user.
        email
            default: None
            help: User email to authenticate to package server
        exclude_path
            default: []
            multiple: True
            help: Don't integrate path.
        force/__no_force
            default: False
        licensee
            multiple: True
            default: None
            help: What licensee should gensln be ran against.
        p4_user
            default: None
            help: Perforce user name.
        password
            default: None
            help: User credentials to authenticate to package server
        reverse/__no_reverse
            default: False
        stream/__no_stream
            default: False
        submit/__no_submit
            default: True
        submit_message
            default: ''
            help: Message to include in the submit message.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    p4_copy_data_upgrade
    --copy-mapping {{ copy_mapping }}
    --p4-client {{ p4_client }}
    --p4-port {{ p4_port }}
    --source-branch {{ source_branch }}
    --source-changelist {{ source_changelist }}
    --target-changelist {{ target_changelist }}
    {%- if batch_file %}
    --batch-file {{ batch_file }}
    {%- endif %}
    {%- if clean %}
    --clean {{ clean }}
    {%- endif %}
    {%- if data_directory %}
    --data-directory {{ data_directory }}
    {%- endif %}
    {%- if domain_user %}
    --domain-user {{ domain_user }}
    {%- endif %}
    {%- if email %}
    --email {{ email }}
    {%- endif %}
    {%- if exclude_path %}
    --exclude-path {{ exclude_path }}
    {%- endif %}
    {%- if force/__no_force %}
    --force/--no-force {{ force/__no_force }}
    {%- endif %}
    {%- if licensee %}
    --licensee {{ licensee }}
    {%- endif %}
    {%- if p4_user %}
    --p4-user {{ p4_user }}
    {%- endif %}
    {%- if password %}
    --password {{ password }}
    {%- endif %}
    {%- if reverse/__no_reverse %}
    --reverse/--no-reverse {{ reverse/__no_reverse }}
    {%- endif %}
    {%- if stream/__no_stream %}
    --stream/--no-stream {{ stream/__no_stream }}
    {%- endif %}
    {%- if submit/__no_submit %}
    --submit/--no-submit {{ submit/__no_submit }}
    {%- endif %}
    {%- if submit_message %}
    --submit-message {{ submit_message }}
    {%- endif %}
  displayName: elipy p4_copy_data_upgrade
