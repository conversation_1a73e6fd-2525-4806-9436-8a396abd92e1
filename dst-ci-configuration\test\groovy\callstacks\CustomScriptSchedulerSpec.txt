   CustomScriptScheduler.run()
      GetBranchFile.get_branchfile(null, null)
      CustomScriptScheduler.pipeline(groovy.lang.Closure)
         CustomScriptScheduler.allowBrokenBuildClaiming()
         CustomScriptScheduler.timestamps()
         CustomScriptScheduler.echo(Executing on agent [label:any])
         CustomScriptScheduler.stage(Trigger custom script job, groovy.lang.Closure)
            CustomScriptScheduler.script(groovy.lang.Closure)
               CustomScriptScheduler.retryOnFailureCause(3, [], groovy.lang.Closure)
                  CustomScriptScheduler.string({name=script_args, value=--arg1 arg})
                  CustomScriptScheduler.parallel({scheduler_test=groovy.lang.Closure})
                     CustomScriptScheduler.build({job=a-branch.custom-script.scheduler_test, parameters=[{name=script_args, value=--arg1 arg}], propagate=false})
                        CustomScriptScheduler.clone()
                        CustomScriptScheduler.toString()
                     LibJenkins.printRunningJobs(scripts.schedulers.all.CustomScriptScheduler)
