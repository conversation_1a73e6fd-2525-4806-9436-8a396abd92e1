{#
    Command:
        codecopy
            short_help: Copy old code build to current changelist.

    Arguments:

    Required variables:

    Optional variables:
        code_branch
            default: None
            help: Perforce branch/stream name.
        source_changelist
            default: None
            help: Perforce changelist number of the source.
        current_changelist
            default: None
            help: Perforce changelist number of the destination.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    codecopy
    {%- if code_branch %}
    --code-branch {{ code_branch }}
    {%- endif %}
    {%- if source_changelist %}
    --source-changelist {{ source_changelist }}
    {%- endif %}
    {%- if current_changelist %}
    --current-changelist {{ current_changelist }}
    {%- endif %}
  displayName: elipy codecopy
