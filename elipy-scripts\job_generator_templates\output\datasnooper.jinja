{#
    Command:
        datasnooper
            short_help: Run the datasnooper.

    Arguments:

    Required variables:

    Optional variables:
        datasnooper_path
            help: location of datasnooper.exe, relative from GAME_ROOT
            default: tnt/bin/cm/fbcli/datasnooper.exe
        dbmanifest_path
            help: location of dbmanifest file, relative from GAME_ROOT
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    datasnooper
    {%- if datasnooper_path %}
    --datasnooper-path {{ datasnooper_path }}
    {%- endif %}
    {%- if dbmanifest_path %}
    --dbmanifest-path {{ dbmanifest_path }}
    {%- endif %}
  displayName: elipy datasnooper
