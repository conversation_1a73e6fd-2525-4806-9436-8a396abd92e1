"""
__init__.py
"""

# we need to import elipy here so Sen<PERSON> is initialised before
# we start adding extra tags
import os
import platform
import elipy2
from elipy2 import LOGGER, SETTINGS

from sentry_sdk import configure_scope
from sentry_sdk.api import set_user

VERSION = "0.0.0"

try:
    import pkg_resources

    VERSION = pkg_resources.require("dice_elipy_scripts")[0].version
except Exception as exc:
    VERSION = "0.0.0"

LOGGER.info("elipy2 version: %s", elipy2.VERSION)
LOGGER.info("dice_elipy_scripts version: %s", VERSION)

with configure_scope() as scope:
    scope.set_context(
        "os",
        {
            "raw_description": platform.platform(),
            "name": platform.system(),
            "release": platform.release(),
            "version": platform.version(),
            "machine": platform.machine(),
        },
    )
    scope.set_tag("dice_elipy_scripts.version", VERSION)
    scope.set_tag("os.raw_description", platform.platform())
    scope.set_tag("os.name", platform.system())
    scope.set_tag("os.release", platform.release())
    scope.set_tag("os.version", platform.version())
    scope.set_tag("os.machine", platform.machine())

set_user({"username": os.environ.get("USERNAME", "UNKNOWN")})
