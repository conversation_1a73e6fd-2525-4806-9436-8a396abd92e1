"""
bilbo_register_frosty.py
"""
import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import filer_paths, LOGGER, build_metadata_utils
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command("bilbo_register_frosty", short_help="Registers a Frosty-generated build in Bilbo.")
@click.option("--code-branch", help="Perforce code branch/stream name.", required=True)
@click.option(
    "--code-changelist",
    required=True,
    help="Changelist number of code build used to verify data.",
)
@click.option("--data-branch", help="Perforce data branch/stream name.", required=True)
@click.option("--data-changelist", help="Changelist number of data built.", required=True)
@click.option(
    "--dataset",
    required=True,
    help="Which dataset has been validated using this code build.",
)
@click.option("--platform", help="Which platform the build is for.", required=True)
@click.option(
    "--package-type",
    required=True,
    help="Which package type the build is of (files, digital, patch).",
)
@click.option("--region", help="Which region/SKU the build is (ww, na, eu).", required=True)
@click.option(
    "--config",
    required=True,
    help="Which configuration the build is in (final, release, retail).",
)
@click.option(
    "--combine-code-branch",
    help="Branch for the second set of binaries used in the combine workflow.",
    default=None,
)
@click.option(
    "--combine-code-changelist",
    help="Changelist for the second set of binaries used in the combine workflow.",
    default=None,
)
@click.option(
    "--combine-data-branch",
    help="Branch for the second set of data used in the combine workflow.",
    default=None,
)
@click.option(
    "--combine-data-changelist",
    help="Changelist for the second set of data used in the combine workflow.",
    default=None,
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    code_branch,
    code_changelist,
    data_branch,
    data_changelist,
    dataset,
    platform,
    package_type,
    region,
    config,
    combine_code_branch,
    combine_code_changelist,
    combine_data_branch,
    combine_data_changelist,
):
    """
    Registers a Frosty-generated build in the configured metadata services.
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    metadata_manager = build_metadata_utils.setup_metadata_manager()

    LOGGER.info(
        "Registering Frosty build code {0}.CL{1} with data {2}.CL{3}".format(
            code_branch, code_changelist, data_branch, data_changelist
        )
    )

    # Log combine information if provided
    if (
        combine_code_branch
        and combine_code_changelist
        and combine_data_branch
        and combine_data_changelist
    ):
        LOGGER.info(
            "Including combine information: code {0}.CL{1} with data {2}.CL{3}".format(
                combine_code_branch,
                combine_code_changelist,
                combine_data_branch,
                combine_data_changelist,
            )
        )
        package_type_with_combine = package_type
        if "_combine" not in package_type_with_combine:
            package_type_with_combine += "_combine"

        dest = filer_paths.get_frosty_build_path(
            data_branch=data_branch,
            data_changelist=data_changelist,
            code_branch=code_branch,
            code_changelist=code_changelist,
            platform=platform,
            package_type=package_type_with_combine,
            region=region,
            config=config,
            combine_data_branch=combine_data_branch,
            combine_data_changelist=combine_data_changelist,
            combine_code_branch=combine_code_branch,
            combine_code_changelist=combine_code_changelist,
        )
    else:
        dest = filer_paths.get_frosty_build_path(
            data_branch=data_branch,
            data_changelist=data_changelist,
            code_branch=code_branch,
            code_changelist=code_changelist,
            platform=platform,
            package_type=package_type,
            region=region,
            config=config,
        )

    # pylint: disable=unexpected-keyword-arg
    metadata_manager.register_frosty_build(
        dest,
        data_changelist=data_changelist,
        data_branch=data_branch,
        code_changelist=code_changelist,
        code_branch=code_branch,
        platform=platform,
        package_type=package_type,
        region=region,
        config=config,
        dataset=dataset,
        combine_data_changelist=combine_data_changelist,
        combine_data_branch=combine_data_branch,
        combine_code_changelist=combine_code_changelist,
        combine_code_branch=combine_code_branch,
    )
