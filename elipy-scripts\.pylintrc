[MASTER]

ignore=tests

disable=logging-format-interpolation,
  locally-disabled,
  broad-except,
  dangerous-default-value,
  wrong-import-position,
  too-many-arguments,
  too-many-branches,
  locally-enabled,
  duplicate-code,
  too-many-locals,
  too-many-statements,
  logging-not-lazy,
  no-else-return,
  wrong-import-order,
  raise-missing-from,
  bad-option-value,
  consider-using-f-string,
  broad-exception-raised,                   ; TODO: remove exception
  cell-var-from-loop,                       ; TODO: remove exception
  consider-iterating-dictionary,            ; TODO: remove exception
  consider-using-generator,                 ; TODO: remove exception
  no-member,                                ; TODO: remove exception
  unspecified-encoding,                     ; TODO: remove exception
  use-dict-literal,                         ; TODO: remove exception
  use-implicit-booleaness-not-comparison,   ; TODO: remove exception
  use-list-literal                          ; TODO: remove exception



[TYPECHECK]

ignored-modules = fbenv,fbcli

[SIMILARITIES]

# Minimum lines number of a similarity.
min-similarity-lines=6

# Ignore comments when computing similarities.
ignore-comments=yes

# Ignore docstrings when computing similarities.
ignore-docstrings=yes

# Ignore imports when computing similarities.
ignore-imports=no
