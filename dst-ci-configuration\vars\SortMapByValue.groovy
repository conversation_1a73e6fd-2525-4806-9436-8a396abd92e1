import com.cloudbees.groovy.cps.NonCPS

/**
 * SortMapByValue.groovy
 * Sort a map by value. Default behaviour is to sort low -> high, this can be changed if needed.
 */
@NonCPS
Map call(def map, def high_to_low = false) {
    def sorted_map = null
    if (high_to_low == false) {
        sorted_map = map.sort { it.value }
    } else if (high_to_low == true) {
        sorted_map = map.sort { -it.value }
    }
    return sorted_map
}
