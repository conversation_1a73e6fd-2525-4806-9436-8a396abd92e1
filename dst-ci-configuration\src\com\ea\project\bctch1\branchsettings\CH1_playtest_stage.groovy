package com.ea.project.bctch1.branchsettings

import com.ea.project.bctch1.BctCh1

class CH1_playtest_stage {
    // Settings for jobs
    static Class project = BctCh1
    static Map general_settings = [
        dataset           : project.dataset,
        elipy_call        : project.elipy_call + ' --use-fbenv-core',
        elipy_install_call: project.elipy_install_call,
        frostbite_licensee: project.frostbite_licensee,
        workspace_root    : project.workspace_root,
    ]
    static Map code_settings = [
        skip_code_build_if_no_changes: true,
        slack_channel_code           : [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        sndbs_enabled                : true,
    ]
    static Map data_settings = [
        poolbuild_data    : true,
        slack_channel_data: [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
    ]
    static Map frosty_settings = [
        poolbuild_frosty    : true,
        slack_channel_frosty: [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        use_linuxclient     : true,
    ]
    static Map standard_jobs_settings = code_settings + data_settings + frosty_settings + [
        asset                       : 'PlaytestLevelsSantiago',
        extra_data_args             : ['--pipeline-args -Pipeline.MaxConcurrency --pipeline-args 12 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        extra_frosty_args           : ['--pipeline-args -Pipeline.MaxConcurrency --pipeline-args 12 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        quickscope_db               : 'kinpipeline',
        quickscope_import           : true,
        server_asset                : 'Game/Setup/Build/DevMPLevels',
        shift_branch                : true,
        skip_icepick_settings_file  : true,
        strip_symbols               : false,
        use_deprecated_blox_packages: true,
        use_super_bundles           : true,
    ]
    static Map icepick_settings = [
        ignore_icepick_exit_code: false
    ]
    static Map preflight_settings = [:]
    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'win64game', configs: ['final', 'performance']],
        [name: 'tool', configs: ['release']],
        [name: 'xbsx', configs: ['performance']],
        [name: 'ps5', configs: ['performance']],
        [name: 'linux64server', configs: ['final']],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = [
        [name: '.data.start', args: []],
    ]
    static List data_matrix = [
        [name: 'win64'],
        [name: 'server'],
        [name: 'xbsx'],
        [name: 'ps5'],
    ]
    static List data_downstream_matrix = [
        [name: '.frosty.start', args: ['code_changelist', 'data_changelist']],
    ]
    static List patchdata_matrix = []
    static List frosty_matrix = [
        [name: 'win64', variants: [[format: 'files', config: 'performance', region: 'ww', args: '']]],
        [name: 'ps5', variants: [[format: 'files', config: 'performance', region: 'dev', args: '']]],
        [name: 'xbsx', variants: [[format: 'files', config: 'performance', region: 'ww', args: '']]],
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww', args: '']]],
    ]
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = []
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
