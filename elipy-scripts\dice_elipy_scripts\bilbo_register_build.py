"""
bilbo_register_build.py
"""
import os
import click
import json
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import LOGGER, SETTINGS, build_metadata_utils
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command(
    "bilbo_register_build",
    short_help="""
Registers a build in the configured metadata services from a given build.json file.
""",
)
@click.option(
    "--build-json-path",
    help="Location of build.json file to insert into the configured metadata services",
    required=True,
)
@click.option(
    "--extra-location",
    multiple=True,
    required=False,
    help="Another location to register this build",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(_, build_json_path, extra_location):
    """
    Registers a build in the configured metadata services.

    Reads a build.json file in and inserts the data into the configured metadata
    services, overwriting any current entry that matches that build. Does not
    alter the original file when inserting the build.

    """
    # adding sentry tags
    add_sentry_tags(__file__)

    locations = list(extra_location)
    locations.insert(0, SETTINGS.location)

    for location in locations:
        bilbo_url = SETTINGS.get("bilbo_url", location)
        metadata_manager = build_metadata_utils.setup_metadata_manager(bilbo_url)
        LOGGER.info("Registering build {0} at {1}".format(build_json_path, bilbo_url))

        with open(build_json_path + "\\build.json", "r") as json_file:
            file_data = json.loads(json_file.read())
            metadata_manager.register_build_data(
                path=build_json_path, attributes=file_data, write_attributes_file=True
            )
