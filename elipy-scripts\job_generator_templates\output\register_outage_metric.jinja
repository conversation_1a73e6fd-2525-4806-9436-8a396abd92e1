{#
    Command:
        register_outage_metric
            short_help: Register outage data in ElasticSearch.

    Arguments:

    Required variables:
        end_date
            help: End date of measuring period, YYMMDD.
            required: True
        start_date
            help: Start date of measuring period, YYMMDD.
            required: True
        major_outage
            help: Hours of major outage.
            required: True
        significant_outage
            help: Hours of significant outage.
            required: True

    Optional variables:
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    register-outage-metric
    --end-date {{ end_date }}
    --start-date {{ start_date }}
    --major-outage {{ major_outage }}
    --significant-outage {{ significant_outage }}
  displayName: elipy register-outage-metric
