"""
submit_to_steam.py

Script to handle uploading builds to Steam.
"""
import os
import glob

import click
from elipy2 import LOGGER, filer_paths, secrets
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics

from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags


@click.command("upload_to_steam", short_help="Submits a build to Steam.")
@click.option("--code-branch", help="Branch/stream that the code/binary is coming from.")
@click.option("--code-changelist", help="Changelist of binaries.")
@click.option("--data-branch", help="Branch/stream that data is coming from.")
@click.option("--data-changelist", help="Changelist of data being used.")
@click.option("--platform", help="Which platform to deploy (default is win64).", default="win64")
@click.option("--config", help="Code debug level (default is final).", default="final")
@click.option("--region", help="Which region to deploy (default is ww).", default="ww")
@click.option(
    "--code-combine-branch",
    help="Branch/stream that the combine code/binary is coming from.",
    required=False,
)
@click.option(
    "--code-combine-changelist",
    help="Changelist of combine binaries.",
    required=False,
)
@click.option(
    "--data-combine-branch",
    help="Branch/stream that combine data is coming from.",
    required=False,
)
@click.option(
    "--data-combine-changelist",
    help="Changelist of combine data being used.",
    required=False,
)
@click.option(
    "--upload-patch",
    default=False,
    is_flag=True,
    help="Use --upload-patch to upload a patch.",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    code_branch,
    code_changelist,
    data_branch,
    data_changelist,
    platform="win64",
    config="final",
    region="ww",
    code_combine_branch="None",
    code_combine_changelist="None",
    data_combine_branch="None",
    data_combine_changelist="None",
    upload_patch=False,
):
    """
    Uploads a given build to Steam.
    """
    add_sentry_tags(__file__)
    package_type = get_package_type(upload_patch, code_combine_branch)
    source_path = filer_paths.get_frosty_build_path(
        data_branch=data_branch,
        data_changelist=data_changelist,
        code_branch=code_branch,
        code_changelist=code_changelist,
        platform=platform,
        package_type=package_type,
        region=region,
        config=config,
    )
    if code_combine_branch != "None":
        source_path = os.path.join(
            source_path,
            code_combine_branch,
            code_combine_changelist,
            data_combine_branch,
            data_combine_changelist,
        )
    script_name = next(iter(glob.glob(os.path.join(source_path, "app_build_*_*.vdf"))))
    steam_exe = "steamcmd.bat"
    steam_script_path = os.path.join(source_path, script_name)
    steam_cmd = os.path.join(source_path, steam_exe)
    LOGGER.info("Steam script path: {}".format(steam_script_path))
    if os.path.exists(steam_cmd) and os.path.exists(steam_script_path):
        LOGGER.info("Running upload from : {}".format(source_path))
        upload_to_steam(steam_script_path, steam_cmd)
    else:
        raise FileNotFoundError(
            "Could not find either {} or {}".format(steam_script_path, steam_cmd)
        )


def upload_to_steam(steam_script_path, steam_cmd):
    """
    Submits files to the appropriate S3 bucket using AWSManager.
    """
    credentials = secrets.get_secrets({"build_type": "steam"})
    try:
        k = next(iter(credentials))  # get first and only key
    except StopIteration as _:
        LOGGER.error("No secret in elipy config matching secret_context steam build in location ")
        raise

    secrets_dict = credentials[k]
    steam_user = secrets_dict["steam_account"]
    steam_password = secrets_dict["steam_password"]
    LOGGER.info(
        "Uploading to Steam with config {} with user {} ...".format(steam_script_path, steam_user)
    )

    steam_cmd = [
        steam_cmd,
        "+login",
        steam_user,
        '"' + steam_password + '"',
        "+run_app_build",
        steam_script_path,
        "+quit",
    ]
    # Run the command
    try:
        os.system(" ".join(steam_cmd))
        LOGGER.info("Build successfully uploaded to Steam")
    except Exception as exc:
        LOGGER.error("Error uploading to Steam: {}".format(exc))


def get_package_type(upload_patch: bool, code_combine_branch: str = "None") -> str:
    """
    Determines the package type based on whether a patch is being uploaded
    and if a code combine branch is specified.
    :param upload_patch: Boolean indicating if a patch is being uploaded.
    :param code_combine_branch: Optional branch name for code combine.
    """
    package_type = "steam"
    if upload_patch:
        package_type += "_patch"
    if code_combine_branch != "None":
        package_type += "_combine"
    return package_type
