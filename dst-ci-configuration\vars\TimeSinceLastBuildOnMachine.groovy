/**
 * TimeSinceLastBuildOnMachine.groovy
 * Returns time since a certain job was built on a certain machine.
 */
Integer call(def node_name, def job_name) {
    def job_item = Jenkins.get().getItem(job_name)
    def min_time_since_build = null
    job_item.builds.any { build ->
        if (node_name == build.builtOnStr) {
            def build_end_time = build.startTimeInMillis + build.duration
            def time_since_build = new Date().time - build_end_time
            if (min_time_since_build == null) {
                min_time_since_build = time_since_build
            } else if (time_since_build < min_time_since_build) {
                min_time_since_build = time_since_build
            }
        }
    }
    return min_time_since_build
}
