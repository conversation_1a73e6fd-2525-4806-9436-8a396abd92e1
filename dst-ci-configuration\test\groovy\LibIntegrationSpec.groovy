import com.ea.lib.jobs.LibIntegration
import javaposse.jobdsl.dsl.jobs.FreeStyleJob
import spock.lang.Specification
import spock.lang.Unroll

@Unroll
class LibIntegrationSpec extends Specification {

    void setup() {
        GroovySpy(LibIntegration, global: true)
    }

    void 'test that cherrypickCode calls with correct params'() {
        when:
        LibIntegration.cherrypickCode(Testjob, TestProject, branchInfo)
        then:
        1 * LibIntegration.integrateCherrypick(Testjob, TestProject, branchInfo, 'code', 'Performs a code cherrypick integration to a_branch.', ['--reverse-mapping', '//code/root/a_folder/a_branch']) >> null
    }

    void 'test that cherrypickD<PERSON> calls with correct params'() {
        when:
        LibIntegration.cherrypickData(Testjob, TestProject, branchInfo)
        then:
        1 * LibIntegration.integrateCherrypick(Testjob, TestProject, branchInfo, 'data', 'Performs a data cherrypick integration to a_branch.', ['--reverse-mapping', '//data/root/a_folder/a_branch']) >> null
    }

    FreeStyleJob Testjob

    class TestProject {
        static String p4_code_root = '//code/root'
        static String p4_data_root = '//data/root'
    }

    private final Map branchInfo = [
        code_branch: 'a_branch',
        code_folder: 'a_folder',
        data_branch: 'a_branch',
        data_folder: 'a_folder',
    ]
}
