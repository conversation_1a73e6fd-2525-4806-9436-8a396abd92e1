package com.ea.project.fb1

import com.ea.exceptions.CobraException

class LibBaseline {
    static final Baseline[] disc_matrix = [
        new Baseline('dev-na-battlefieldgame-first-patch', 'win64', '11850169', 'dev-na-dice-next-build', '11856090', 'dev-na-dice-next-build-data'),
        new Baseline('dev-na-battlefieldgame-first-patch', 'ps4', '11850169', 'dev-na-dice-next-build', '11856090', 'dev-na-dice-next-build-data'),
        new Baseline('dev-na-battlefieldgame-first-patch', 'xb1', '11850169', 'dev-na-dice-next-build', '11856090', 'dev-na-dice-next-build-data'),
        new Baseline('dev-na-battlefieldgame-first-patch', 'ps5', '11850169', 'dev-na-dice-next-build', '11856090', 'dev-na-dice-next-build-data'),
        new Baseline('dev-na-battlefieldgame-first-patch', 'xbsx', '11850169', 'dev-na-dice-next-build', '11856090', 'dev-na-dice-next-build-data'),
    ]

    static final Baseline[] patch_matrix = []

    static Baseline get_disc_baseline_for(String name, String platform) {
        return get_baseline_from_matrix(disc_matrix, name, platform)
    }

    static Baseline get_patch_baseline_for(String name, String platform) {
        return get_baseline_from_matrix(patch_matrix, name, platform)
    }

    private static Baseline get_baseline_from_matrix(Baseline[] matrix, String name, String platform) {
        for (baseline in matrix) {
            if (baseline.name == name && baseline.platform == platform) {
                return baseline
            }
        }
        throw new CobraException("No baseline configuration matches name: ${name} as well as platform: ${platform}!")
    }
}

class Baseline {
    /*
    This is the master baseline values. All other baselines should reference its values per default.
    */
    static String default_code_changelist = '11850169'
    static String default_code_branch = 'dev-na-dice-next-build'
    static String default_data_changelist = '11856090'
    static String default_data_branch = 'dev-na-dice-next-build-data'
    String name, platform, code_changelist, code_branch, data_changelist, data_branch

    // EXCEPTIONAL Constructor
    Baseline(name, platform, code_changelist, code_branch, data_changelist, data_branch) {
        this.name = name
        this.platform = platform
        this.code_changelist = code_changelist
        this.code_branch = code_branch
        this.data_changelist = data_changelist
        this.data_branch = data_branch
    }
    // EXCEPTIONAL Constructor
    Baseline(name, platform, branches, code_changelist, data_changelist) {
        this(name, platform, code_changelist, branches, data_changelist, branches)
    }
    // DEFAULT Constructor
    Baseline(name, platform) {
        this(name, platform, default_code_changelist, default_code_branch, default_data_changelist, default_data_branch)
    }
}
