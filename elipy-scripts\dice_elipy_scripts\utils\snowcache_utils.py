"""
Snowcache helper functions
"""

from requests.exceptions import ReadTimeout, HTTPError, ConnectTimeout

from elipy2.config import ConfigManager
from elipy2 import LOGGER
from elipy2.avalanche_web_api.server import Server as AvalancheServer

SNOWCACHE_MODES = ["", "upload", "forceupload", "uploadanddownload"]


def get_avalanche_host(  # pylint: disable=invalid-name
    SETTINGS: ConfigManager, platform: str
) -> str:
    """
    Pull avalanche host from settings object
    """
    avalanche_host = SETTINGS.get("snowcache_host")[platform]
    return avalanche_host


def get_snowcache_host_arg(  # pylint: disable=invalid-name
    SETTINGS: ConfigManager, platform: str
) -> str:
    """
    Logic to build the avalanche host framework arg using a passed ConfigManager and platform
    """
    avalanche_host = get_avalanche_host(SETTINGS, platform)
    avalanche_host_arg = "-G:package.SnowCache.avalancheHost={}".format(avalanche_host)
    return avalanche_host_arg


# pylint: enable=invalid-name


def _get_snowcache_mode(clean: bool, snowcache_mode_override: str):
    """
    Logic to determine which snowcache mode to use
    """

    if not snowcache_mode_override:
        if clean:  # Build from scratch and replace upstream cache
            snowcache_mode = "forceupload"
        else:  # Patch existing and download rebuilt from upstream cache
            snowcache_mode = "uploadanddownload"
    else:
        snowcache_mode = snowcache_mode_override.lower()

    return snowcache_mode


def get_snowcache_mode_args(clean: bool, snowcache_mode_override: str, use_snowcache: bool) -> list:
    """
    Function to hold logic building snowcache arg list to add to framework_args
    """
    snowcache_mode = _get_snowcache_mode(clean, snowcache_mode_override)

    if use_snowcache:
        result = ["-G:package.SnowCache.mode={}".format(snowcache_mode)]
    else:
        result = []

    return result


def clean_required(use_snowcache: bool, snowcache_mode_override: str, clean: bool) -> bool:
    """
    Function to centralise logic around whether to clean the local TNT dir or not
    """
    if clean:
        result = True
    else:
        result = use_snowcache and not snowcache_mode_override.lower() == "upload"
    return result


def avalanche_upstream_server_healthy(  # pylint: disable=invalid-name
    SETTINGS: ConfigManager,
    platform: str,
) -> bool:
    """
    Pings upstream avalanche server and returns a bool based on its current state as an
    upstream avalanche server. If the requirements aren't met, it returns False.
    """
    avalanche_host = get_avalanche_host(SETTINGS, platform)
    avalanche_api_endpoint = "http://{}:1338/".format(avalanche_host)
    avalanche_server = AvalancheServer(url=avalanche_api_endpoint)

    # Check for connection, timeout and http errors
    try:
        avalanche_server_status = avalanche_server.get_status()
    except (ReadTimeout, ConnectionError, HTTPError, ConnectTimeout) as exp:
        LOGGER.warning("Request to avalanche server failed: {}".format(exp))
        return False

    # Check avalanche service is running
    avalanche_service_state = avalanche_server_status["summary"].strip().lower()
    if not avalanche_service_state == "running":
        LOGGER.warning(
            "Upstream avalanche service not running ({})".format(avalanche_service_state)
        )
        return False

    # All criteria met
    return True


def validate_use_snowcache_param(  # pylint: disable=invalid-name
    SETTINGS: ConfigManager,
    platform: str,
    use_snowcache: bool,
) -> bool:
    """
    The use_snowcache param is made falsy if the upstream avalanche server is not healthy,
    otherwise it has no effect.
    """
    if use_snowcache:
        avalanche_healthy = avalanche_upstream_server_healthy(SETTINGS, platform)
        use_snowcache = avalanche_healthy
    return use_snowcache
