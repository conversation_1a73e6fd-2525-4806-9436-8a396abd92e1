import hudson.model.Item
import hudson.model.View
import javaposse.jobdsl.dsl.DslScriptLoader
import javaposse.jobdsl.dsl.GeneratedItems
import javaposse.jobdsl.dsl.GeneratedJob
import javaposse.jobdsl.dsl.GeneratedView
import javaposse.jobdsl.dsl.JobManagement
import javaposse.jobdsl.dsl.ScriptRequest
import javaposse.jobdsl.plugin.JenkinsJobManagement
import jenkins.model.Jenkins
import org.junit.ClassRule
import org.jvnet.hudson.test.JenkinsRule
import spock.lang.Shared
import spock.lang.Specification
import support.TestUtil

import java.nio.file.Path

/**
 * Tests that all dsl scripts in the jobs directory will compile. All config.xml's are written to build/debug-xml.
 *
 * This runs against the jenkins test harness. Plugins providing auto-generated DSL must be added to the build dependencies.
 */
@SuppressWarnings('PrivateFieldCouldBeFinal')
/* To me it looks like both `jenkinsRule` and `outputDir` can be final, but the seed test fails on loading when they are made final. I'm not
Java fluent enough to understand why. */
class JobScriptsSpec extends Specification {

    @Shared
    @ClassRule
    private JenkinsRule jenkinsRule = new JenkinsRule()

    @Shared
    private Path outputDir = Path.of('./build/debug-xml')

    @Shared
    private JobManagement jm = new JenkinsJobManagement(System.out, [WORKSPACE: '.', BUILD_URL: 'https://test.url'], Path.of('.').toFile())

    boolean setupSpec() {
        return outputDir.deleteDir()
    }

    void testSeeds(Closure filter) {
        // ignoreExisting controls whether or not to create jobs that already exist (conflicting job names). Due to a bug that causes a
        // crash in JenkinsJobManagement.createOrUpdateConfig we set it to true in order to not create conflicting jobs.
        TestUtil
            .jobFiles
            .findAll(filter)
            .parallelStream()
            .forEach { file ->
                def scriptRequest = new ScriptRequest(file.text, Path.of('.').toUri().toURL(), true)
                def items = new DslScriptLoader(jm)
                    .runScripts([
                        scriptRequest
                    ])
                writeItems(items, outputDir)
            }
    }

    void 'verify all seeds'() {
        when:
        testSeeds { true }

        then:
        noExceptionThrown()
    }

    void 'verify seed: autotests_seed.groovy'() {
        when:
        testSeeds { it.name == 'autotests_seed.groovy' }

        then:
        noExceptionThrown()
    }

    void 'verify seed: basic_jobs.groovy'() {
        when:
        testSeeds { it.name == 'basic_jobs.groovy' }

        then:
        noExceptionThrown()
    }

    void 'verify seed: cobra_jobs.groovy'() {
        when:
        testSeeds { it.name == 'cobra_jobs.groovy' }

        then:
        noExceptionThrown()
    }

    void 'verify seed: dvcs_seed.groovy'() {
        when:
        testSeeds { it.name == 'dvcs_seed.groovy' }

        then:
        noExceptionThrown()
    }

    void 'verify seed: integrations_seed.groovy'() {
        when:
        testSeeds { it.name == 'integrations_seed.groovy' }

        then:
        noExceptionThrown()
    }

    void 'verify seed: maintenance_seed.groovy'() {
        when:
        testSeeds { it.name == 'maintenance_seed.groovy' }

        then:
        noExceptionThrown()
    }

    void 'verify seed: preflight_seed.groovy'() {
        when:
        testSeeds { it.name == 'preflight_seed.groovy' }

        then:
        noExceptionThrown()
    }

    void 'verify seed: coverity_seed.groovy'() {
        when:
        testSeeds { it.name == 'coverity_seed.groovy' }

        then:
        noExceptionThrown()
    }

    void 'verify seed: test_jobs_seed.groovy'() {
        when:
        testSeeds { it.name == 'test_jobs_seed.groovy' }

        then:
        noExceptionThrown()
    }

    void 'verify seed: util_jobs.groovy'() {
        when:
        testSeeds { it.name == 'util_jobs.groovy' }

        then:
        noExceptionThrown()
    }

    void 'verify seed: views'() {
        when:
        testSeeds { it.name.contains('view_') }

        then:
        noExceptionThrown()
    }

    void 'verify seed: game_tools_jobs.groovy'() {
        when:
        testSeeds { it.name == 'game_tools_jobs.groovy' }

        then:
        noExceptionThrown()
    }

    /**
     * Write the config.xml for each generated job and view to the build dir.
     */
    private void writeItems(GeneratedItems items, Path outputDir) {
        Jenkins jenkins = jenkinsRule.jenkins
        items.jobs.each { GeneratedJob generatedJob ->
            String jobName = generatedJob.jobName
            Item item = jenkins.getItemByFullName(jobName)
            URL url = new URL(jenkins.rootUrl + item.url + 'config.xml')
            String text = url.text
            outputDir.resolve('jobs')
            TestUtil.writeFile(outputDir.resolve('jobs'), jobName, text)
        }

        items.views.each { GeneratedView generatedView ->
            String viewName = generatedView.name
            View view = jenkins.getView(viewName)
            URL url = new URL(jenkins.rootUrl + view.url + 'config.xml')
            String text = url.text
            TestUtil.writeFile(outputDir.resolve('views'), viewName, text)
        }
    }

}
