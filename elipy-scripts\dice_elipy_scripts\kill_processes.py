"""
kill_processes.py
"""
import os
import click

from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import running_processes
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command("kill_processes", short_help="Kill rogue processes.")
@click.argument("processes", default=None, nargs=-1)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
# pylint: disable=too-many-locals, invalid-name, unused-argument
def cli(_, processes):
    """
    Kill rogue processes.
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    if not processes:
        processes = [
            "Tool.Pipeline_Win64_release_Dll*",
            "MSBuild.exe",  # should be killed before cl.exe
            "cl.exe",
            "Casablanca.Main_Win64*",
            "frostyisotool.exe",
            "orbis-pub-cmd.exe",
            "orbis-ctrl.exe",
            "orbis-symupload.exe",
            "prospero-symupload.exe",
            "orbis-clang.exe",
            "nant.exe",
            "AvalancheCLI.exe",
            "mspdbsrv.exe",
            "vctip.exe",
            "snowcacheserver.exe",
            "Icepick.exe",
            "fbenvcore.exe",
        ]

    running_processes.kill(list(processes))
