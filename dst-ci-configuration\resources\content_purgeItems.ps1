<#
This script is used to purge items in general

1. To purge both files and directories
	- set Source to "$parentDir" or "$parentDir\*"
	- set DirectoryOnly to false

2. To purge just directories
	- set Source to "$parentDir" or "$parentDir\*"
	- set DirectoryOnly to true

3. To purge just files
	- set Source to "$parentDir\*.*"
	- set DirectoryOnly to false
#>

$env:DirectoryOnly = $False
$dirs_to_purge = "\\eauk-file.eu.ad.ea.com\Excalibur\Shift\Drone-OF3D\*.*",
                 "\\eauk-file.eu.ad.ea.com\Excalibur\Shift\Drone-GlassEgg\*.*",
                 "\\eauk-file.eu.ad.ea.com\Excalibur\Shift\Drone-Elite3D\*.*",
                 "\\eauk-file.eu.ad.ea.com\Excalibur\Shift\Drone-Sheer\*.*"

write-host "Dirs: $dirs_to_purge"
write-host "DirectoryOnly: $($env:DirectoryOnly)"

 foreach ($dir in $dirs_to_purge) {
    write-host "Purging in Dir: $dir"
    Get-ChildItem $dir | ?{ (-not [System.Convert]::ToBoolean($env:DirectoryOnly)) -or $_.PSIsContainer } | ForEach-Object {
      write-host "Purging: $($_.FullName)"
      remove-item -Recurse -Force $($_.FullName)
    }
 }
