/**
 * NeedsRebuildData.groovy
 * Checks for a certain job if there is a previous one on the same code and data cl that built successfully.
 */
boolean call(def job_name,
             def current_code_changelist,
             def current_data_changelist,
             def combine_code_changelist = '',
             def combine_data_changelist = '') {
    def jenkinsItem = Jenkins.get()?.getItem(job_name)
    if (jenkinsItem == null) {
        echo 'NeedsRebuildData: Jenkins item for job "' + job_name + '" is null.'
        return true
    }
    if (jenkinsItem.builds == null) {
        echo 'NeedsRebuildData: Jenkins item for job "' + job_name + '" has no builds property.'
        return true
    }
    if (jenkinsItem.builds) {
        last_jobs = jenkinsItem.getLastBuildsOverThreshold(10, Result.UNSTABLE)
        for (last_job in last_jobs) {
            if (last_job != null) {
                def env = last_job.getEnvironment(TaskListener.NULL)
                def last_code_changelist = env?.code_changelist
                def last_data_changelist = env?.data_changelist
                def last_combine_code_changelist = env?.combine_code_changelist
                def last_combine_data_changelist = env?.combine_data_changelist

                // Check if primary changelists match
                boolean primary_match = (last_code_changelist == current_code_changelist &&
                    last_data_changelist == current_data_changelist)

                // Check if combine changelists match (only if they exist)
                boolean combine_match = true
                if (combine_code_changelist != '' && combine_data_changelist != '') {
                    combine_match = (last_combine_code_changelist == combine_code_changelist &&
                        last_combine_data_changelist == combine_data_changelist)
                }

                // Proceed if primary match is true
                if (primary_match && combine_match) {
                    if (last_job.result != null) {
                        if (combine_code_changelist != null && combine_data_changelist != null) {
                            // Log with combine changelists
                            echo "Results of last $job_name run on code CL $current_code_changelist, data CL $current_data_changelist, combine code CL $combine_code_changelist, and combine data CL $combine_data_changelist was ${last_job.result}."
                        } else {
                            // Log only primary changelists
                            echo "Results of last $job_name run on code CL $current_code_changelist and data CL $current_data_changelist was ${last_job.result}."
                        }

                        if (last_job.result == Result.SUCCESS) {
                            echo 'Will not rebuild.'
                            return false
                        }
                        echo 'Rebuilding.'
                    }
                }
            }
        }
    }
    return true
}
