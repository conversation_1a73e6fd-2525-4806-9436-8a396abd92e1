"""
test_patch_databuild.py

Unit testing for patch_databuild
"""
import os
import unittest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import MagicMock, patch
from dice_elipy_scripts.patch_databuild import cli
from elipy2.config import ConfigManager

config_path = os.path.join(os.path.dirname(__file__), "data", "elipy_test.yml")
config_manager = ConfigManager(path=config_path)


@patch("elipy2.avalanche.drop", MagicMock())
@patch("elipy2.avalanche.export", MagicMock())
@patch("elipy2.avalanche.import_baselinedb", MagicMock())
@patch("elipy2.running_processes.kill", MagicMock())
@patch("elipy2.telemetry.upload_metrics", MagicMock())
@patch("os.path.join", MagicMock(side_effect=lambda *args: "\\".join(args)))
class TestPatchDatabuild(unittest.TestCase):
    ARGUMENT_DATA_DIR = "some-dir"
    ARGUMENT_PLATFORM = "win64"
    ARGUMENT_ASSETS = "some-asset"

    OPTION_DATA_BRANCH = "--data-branch"
    OPTION_DATA_CHANGELIST = "--data-changelist"
    OPTION_CODE_BRANCH = "--code-branch"
    OPTION_CODE_CHANGELIST = "--code-changelist"
    OPTION_PIPELINE_ARGS = "--pipeline-args"
    OPTION_FIRST_PATCH = "--first-patch"
    OPTION_NO_BASELINE_SET = "--no-baseline-set"
    OPTION_ENABLE_COMPRESSION = "--enable-compression"
    OPTION_USE_RECOMPRESSION_CACHE = "--use-recompression-cache"
    OPTION_STAGING_STREAM = "--staging-stream"
    OPTION_IMPORT_AVALANCHE_STATE = "--import-avalanche-state"
    OPTION_DISC_DATA_BRANCH = "--disc-data-branch"
    OPTION_DISC_DATA_CHANGELIST = "--disc-data-changelist"
    OPTION_DISC_CODE_BRANCH = "--disc-code-branch"
    OPTION_DISC_CODE_CHANGELIST = "--disc-code-changelist"
    OPTION_PATCH_DATA_BRANCH = "--patch-data-branch"
    OPTION_PATCH_DATA_CHANGELIST = "--patch-data-changelist"
    OPTION_PATCH_CODE_BRANCH = "--patch-code-branch"
    OPTION_PATCH_CODE_CHANGELIST = "--patch-code-changelist"
    OPTION_PATCH_TYPE = "--patch-type"
    OPTION_DATA_CLEAN = "--data-clean"
    OPTION_SKIP_IMPORTING_BASELINE_STATE = "--skip-importing-baseline-state"
    OPTION_EXPRESSION_DEBUG_DATA = "--expression-debug-data"
    OPTION_CLEAR_CACHE = "--clear-cache"
    OPTION_USE_HEAD_BUNDLES_AS_BASE_BUNDLES = "--use-head-bundles-as-base-bundles"
    OPTION_VIRTUAL_BRANCH_OVERRIDE = "--virtual-branch-override"
    OPTION_EXPORT_COMBINE_BUNDLES = "--export-combine-bundles"
    OPTION_STANDALONE_BASELINE = "--standalone-baseline"

    VALUE_DATA_BRANCH = "some-branch"
    VALUE_DATA_CHANGELIST = "1234"
    VALUE_CODE_BRANCH = "other-branch"
    VALUE_CODE_CHANGELIST = "5678"
    VALUE_PIPELINE_ARGS = "arg1"
    VALUE_ENABLE_COMPRESSION = "true"
    VALUE_USE_RECOMPRESSION_CACHE = "true"
    VALUE_DISC_DATA_BRANCH = "disc_data_branch"
    VALUE_DISC_DATA_CHANGELIST = "1122"
    VALUE_DISC_CODE_BRANCH = "disc_code_branch"
    VALUE_DISC_CODE_CHANGELIST = "2233"
    VALUE_PATCH_DATA_BRANCH = "patch_data_branch"
    VALUE_PATCH_DATA_CHANGELIST = "3344"
    VALUE_PATCH_CODE_BRANCH = "patch_code_branch"
    VALUE_PATCH_CODE_CHANGELIST = "4455"
    VALUE_PATCH_TYPE_1 = "disc_build"
    VALUE_EXPRESSION_DEBUG_DATA = "true"
    VALUE_VIRTUAL_BRANCH_OVERRIDE = "true"

    DEFAULT_ARGS = [
        ARGUMENT_DATA_DIR,
        ARGUMENT_PLATFORM,
        ARGUMENT_ASSETS,
        OPTION_DATA_BRANCH,
        VALUE_DATA_BRANCH,
        OPTION_DATA_CHANGELIST,
        VALUE_DATA_CHANGELIST,
        OPTION_CODE_BRANCH,
        VALUE_CODE_BRANCH,
        OPTION_CODE_CHANGELIST,
        VALUE_CODE_CHANGELIST,
        OPTION_PATCH_TYPE,
        VALUE_PATCH_TYPE_1,
    ]

    def setUp(self):
        self.patcher_import_avalanche = patch(
            "dice_elipy_scripts.patch_databuild.import_avalanche_data_state"
        )
        self.mock_import_avalanche = self.patcher_import_avalanche.start()
        self.mock_import_avalanche.return_value = ["-importState", "import_db_name"]

        self.patcher_export_head_bundles = patch(
            "dice_elipy_scripts.patch_databuild.export_head_bundles"
        )
        self.mock_export_head_bundles = self.patcher_export_head_bundles.start()

        self.patcher_get_export_compression_args = patch(
            "dice_elipy_scripts.patch_databuild.get_export_compression_args"
        )
        self.mock_get_export_compression_args = self.patcher_get_export_compression_args.start()
        self.mock_get_export_compression_args.return_value = ["compression", "args"]

        self.patcher_run_expression_debug_data = patch(
            "dice_elipy_scripts.patch_databuild.run_expression_debug_data"
        )
        self.mock_run_expression_debug_data = self.patcher_run_expression_debug_data.start()

        self.patcher_get_db_name = patch("elipy2.avalanche.get_db_name")
        self.mock_get_db_name = self.patcher_get_db_name.start()
        self.mock_get_db_name.return_value = "import_db_name"

        self.patcher_metadata_manager = patch("elipy2.build_metadata.BuildMetadataManager")
        self.mock_metadata_manager = self.patcher_metadata_manager.start()
        self.mock_metadata_manager.return_value = MagicMock()

        self.patcher_datautils = patch("elipy2.data.DataUtils")
        self.mock_datautils = self.patcher_datautils.start()
        self.mock_datautils.return_value = MagicMock()

        self.patcher_filerutils = patch("elipy2.filer.FilerUtils")
        self.mock_filerutils = self.patcher_filerutils.start()
        self.mock_filerutils.return_value = MagicMock()

        self.patcher_os_exists = patch("dice_elipy_scripts.patch_databuild.os.path.exists")
        self.mock_os_exists = self.patcher_os_exists.start()
        self.mock_os_exists.return_value = False

        self.patcher_settings_get = patch("elipy2.SETTINGS.get")
        self.mock_settings_get = self.patcher_settings_get.start()
        self.mock_settings_get.side_effect = config_manager.get

        self.patcher_fbcore_get_emit_arg = patch("elipy2.frostbite_core.get_emit_arg")
        self.mock_get_emit_arg = self.patcher_fbcore_get_emit_arg.start()
        self.mock_get_emit_arg.side_effect = MagicMock()

        self.patcher_ddelta = patch("elipy2.avalanche.ddelta")
        self.mock_ddelta = self.patcher_ddelta.start()

    def tearDown(self):
        patch.stopall()

    def test_basic_run(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 0
        assert self.mock_filerutils.return_value.fetch_code.call_count == 1
        assert self.mock_datautils.return_value.clear_cache.call_count == 0

    def test_pipeline_args_run(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.DEFAULT_ARGS + [self.OPTION_PIPELINE_ARGS, self.VALUE_PIPELINE_ARGS]
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_datautils.return_value.cook.assert_called_once_with(
            pipeline_args=[
                "arg1",
                self.mock_get_emit_arg(),
                "-exportState",
                "import_db_name",
                "-importState",
                "import_db_name",
            ],
            indexing_args=[],
            collect_mdmps=True,
            clean_master_version_check=False,
        )

    def test_clear_cache(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS + [self.OPTION_CLEAR_CACHE])
        assert result.exit_code == 0
        self.mock_datautils.return_value.clear_cache.assert_called_once_with(
            pipeline_args=["-importState", "import_db_name"]
        )

    def test_clear_cache_skip_import_baseline_state(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_ARGS
            + [self.OPTION_SKIP_IMPORTING_BASELINE_STATE, self.OPTION_CLEAR_CACHE],
        )
        assert result.exit_code == 0
        self.mock_datautils.return_value.clear_cache.assert_called_once_with(pipeline_args=[])

    def test_clear_cache_no_baseline_set(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.DEFAULT_ARGS + [self.OPTION_NO_BASELINE_SET, self.OPTION_CLEAR_CACHE]
        )
        assert result.exit_code == 0
        self.mock_datautils.return_value.clear_cache.assert_called_once_with(pipeline_args=[])

    def test_clear_cache_no_baseline_import_avalanche(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_ARGS
            + [
                self.OPTION_NO_BASELINE_SET,
                self.OPTION_IMPORT_AVALANCHE_STATE,
                self.OPTION_CLEAR_CACHE,
            ],
        )
        assert result.exit_code == 0
        self.mock_datautils.return_value.clear_cache.assert_called_once_with(
            pipeline_args=["-importState", "import_db_name"]
        )

    def test_fetch_baseline_bundles(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 0
        assert self.mock_filerutils.return_value.fetch_baseline_bundles.call_count == 1
        assert self.mock_filerutils.return_value.fetch_baseline_bundles_head.call_count == 0

    def test_fetch_baseline_bundles_first_patch(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS + [self.OPTION_FIRST_PATCH])
        assert result.exit_code == 0
        assert self.mock_filerutils.return_value.fetch_baseline_bundles.call_count == 0
        assert self.mock_filerutils.return_value.fetch_baseline_bundles_head.call_count == 1

    def test_ddelta(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 0
        self.mock_ddelta.assert_called_once_with(
            "tnt_root\\local\\bundles\\head",
            "tnt_root\\local\\bundles\\base",
            "tnt_root\\local\\bundles\\delta",
        )

    def test_ddelta_use_head_as_base(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.DEFAULT_ARGS + [self.OPTION_USE_HEAD_BUNDLES_AS_BASE_BUNDLES]
        )
        assert result.exit_code == 0
        self.mock_ddelta.assert_called_once_with(
            "tnt_root\\local\\bundles\\head",
            "tnt_root\\local\\bundles\\head",
            "tnt_root\\local\\bundles\\delta",
        )

    def test_virtual_branch_override(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_ARGS
            + [self.OPTION_VIRTUAL_BRANCH_OVERRIDE, self.VALUE_VIRTUAL_BRANCH_OVERRIDE],
        )
        assert result.exit_code == 0
        self.mock_datautils.return_value.cook.assert_called_once_with(
            pipeline_args=[
                "-BuildSettings.ForceBranch",
                self.VALUE_DATA_BRANCH,
                "-stateId",
                self.VALUE_DATA_BRANCH,
                self.mock_get_emit_arg(),
                "-exportState",
                "import_db_name",
                "-importState",
                "import_db_name",
            ],
            indexing_args=["-stateId", self.VALUE_DATA_BRANCH],
            collect_mdmps=True,
            clean_master_version_check=False,
        )

    def test_export_no_compression(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 0
        self.mock_export_head_bundles.assert_called_once_with(
            "win64", "tnt_root\\local\\bundles\\head", []
        )

    def test_export_enable_compression(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_ARGS + [self.OPTION_ENABLE_COMPRESSION, self.VALUE_ENABLE_COMPRESSION],
        )
        assert result.exit_code == 0
        self.mock_export_head_bundles.assert_called_once_with(
            "win64", "tnt_root\\local\\bundles\\head", ["compression", "args"]
        )

    def test_export_recompression_cache(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_ARGS
            + [self.OPTION_USE_RECOMPRESSION_CACHE, self.VALUE_USE_RECOMPRESSION_CACHE],
        )
        assert result.exit_code == 0
        self.mock_export_head_bundles.assert_called_once_with(
            "win64", "tnt_root\\local\\bundles\\head", ["--recompressionCache", "server.address"]
        )

    def test_export_combine_bundles(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_ARGS
            + [
                self.OPTION_EXPORT_COMBINE_BUNDLES,
                "true",
            ],
        )
        assert result.exit_code == 0
        self.mock_export_head_bundles.assert_called_once_with(
            "win64",
            "tnt_root\\local\\combine_bundles\\head",
            deploy_extra_args=["-s", "pre-combine.yaml"],
            include_platform=False,
            ordering_algorithm=None,
        )

    def test_fetch_baseline_combine_bundles(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_ARGS
            + [
                self.OPTION_EXPORT_COMBINE_BUNDLES,
                "true",
            ],
        )
        assert result.exit_code == 0
        assert self.mock_filerutils.return_value.fetch_baseline_bundles.call_count == 0
        assert self.mock_filerutils.return_value.fetch_baseline_bundles_head.call_count == 0

    def test_fetch_baseline_combine_bundles_first_patch(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_ARGS
            + [
                self.OPTION_FIRST_PATCH,
                self.OPTION_EXPORT_COMBINE_BUNDLES,
                "true",
            ],
        )
        assert result.exit_code == 0
        assert self.mock_filerutils.return_value.fetch_baseline_bundles.call_count == 0
        assert self.mock_filerutils.return_value.fetch_baseline_bundles_head.call_count == 0

    def test_ddelta_combine(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_ARGS
            + [
                self.OPTION_FIRST_PATCH,
                self.OPTION_EXPORT_COMBINE_BUNDLES,
                "true",
            ],
        )
        assert result.exit_code == 0
        assert self.mock_ddelta.call_count == 0

    def test_ddelta_combine_use_head_as_base(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_ARGS
            + [
                self.OPTION_USE_HEAD_BUNDLES_AS_BASE_BUNDLES,
                self.OPTION_FIRST_PATCH,
                self.OPTION_EXPORT_COMBINE_BUNDLES,
                "true",
            ],
        )
        assert result.exit_code == 0
        assert self.mock_ddelta.call_count == 0

    def test_run_expression_debug_data(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_ARGS
            + [self.OPTION_EXPRESSION_DEBUG_DATA, self.VALUE_EXPRESSION_DEBUG_DATA],
        )
        assert result.exit_code == 0
        self.mock_run_expression_debug_data.assert_called_once_with(
            self.VALUE_CODE_CHANGELIST,
            self.VALUE_DATA_CHANGELIST,
            self.VALUE_CODE_BRANCH,
            self.VALUE_DATA_BRANCH,
            self.ARGUMENT_PLATFORM,
            builder_instance=self.mock_datautils.return_value,
            clean_master_version_check=False,
        )

    def test_deploy_state(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 0
        self.mock_filerutils.return_value.deploy_state.assert_called_once_with(
            os.path.join("tnt_root", "local", "bundles", "state"),
            data_branch=self.VALUE_DATA_BRANCH,
            data_changelist=self.VALUE_DATA_CHANGELIST,
            code_branch=self.VALUE_CODE_BRANCH,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            platform=self.ARGUMENT_PLATFORM,
            bundles_dir_name="bundles",
        )

    def test_deploy_state_combine(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.DEFAULT_ARGS + [self.OPTION_EXPORT_COMBINE_BUNDLES, "true"]
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.deploy_state.assert_called_once_with(
            os.path.join("tnt_root", "local", "combine_bundles", "state"),
            data_branch=self.VALUE_DATA_BRANCH,
            data_changelist=self.VALUE_DATA_CHANGELIST,
            code_branch=self.VALUE_CODE_BRANCH,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            platform=self.ARGUMENT_PLATFORM,
            bundles_dir_name="combine_bundles",
        )

    def test_fetch_baseline_state(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_ARGS
            + [
                self.OPTION_PATCH_DATA_BRANCH,
                self.VALUE_PATCH_DATA_BRANCH,
                self.OPTION_PATCH_DATA_CHANGELIST,
                self.VALUE_PATCH_DATA_CHANGELIST,
                self.OPTION_PATCH_CODE_BRANCH,
                self.VALUE_PATCH_CODE_BRANCH,
                self.OPTION_PATCH_CODE_CHANGELIST,
                self.VALUE_PATCH_CODE_CHANGELIST,
            ],
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_baseline_state.assert_called_once_with(
            data_branch=self.VALUE_PATCH_DATA_BRANCH,
            data_changelist=self.VALUE_PATCH_DATA_CHANGELIST,
            code_branch=self.VALUE_PATCH_CODE_BRANCH,
            code_changelist=self.VALUE_PATCH_CODE_CHANGELIST,
            platform=self.ARGUMENT_PLATFORM,
            dest=os.path.join("tnt_root", "local", "baseline_state"),
            bundles_dir_name="bundles",
        )

    def test_fetch_baseline_state_first_patch(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_ARGS
            + [
                self.OPTION_FIRST_PATCH,
                self.OPTION_DISC_DATA_BRANCH,
                self.VALUE_DISC_DATA_BRANCH,
                self.OPTION_DISC_DATA_CHANGELIST,
                self.VALUE_DISC_DATA_CHANGELIST,
                self.OPTION_DISC_CODE_BRANCH,
                self.VALUE_DISC_CODE_BRANCH,
                self.OPTION_DISC_CODE_CHANGELIST,
                self.VALUE_DISC_CODE_CHANGELIST,
            ],
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_baseline_state.assert_called_once_with(
            data_branch=self.VALUE_DISC_DATA_BRANCH,
            data_changelist=self.VALUE_DISC_DATA_CHANGELIST,
            code_branch=self.VALUE_DISC_CODE_BRANCH,
            code_changelist=self.VALUE_DISC_CODE_CHANGELIST,
            platform=self.ARGUMENT_PLATFORM,
            dest=os.path.join("tnt_root", "local", "baseline_state"),
            bundles_dir_name="bundles",
        )

    def test_fetch_baseline_state_combine(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_ARGS
            + [
                self.OPTION_PATCH_DATA_BRANCH,
                self.VALUE_PATCH_DATA_BRANCH,
                self.OPTION_PATCH_DATA_CHANGELIST,
                self.VALUE_PATCH_DATA_CHANGELIST,
                self.OPTION_PATCH_CODE_BRANCH,
                self.VALUE_PATCH_CODE_BRANCH,
                self.OPTION_PATCH_CODE_CHANGELIST,
                self.VALUE_PATCH_CODE_CHANGELIST,
                self.OPTION_EXPORT_COMBINE_BUNDLES,
                "true",
            ],
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_baseline_state.assert_called_once_with(
            data_branch=self.VALUE_PATCH_DATA_BRANCH,
            data_changelist=self.VALUE_PATCH_DATA_CHANGELIST,
            code_branch=self.VALUE_PATCH_CODE_BRANCH,
            code_changelist=self.VALUE_PATCH_CODE_CHANGELIST,
            platform=self.ARGUMENT_PLATFORM,
            dest=os.path.join("tnt_root", "local", "baseline_state"),
            bundles_dir_name="combine_bundles",
        )

    def test_fetch_baseline_state_combine_standalone_baseline(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_ARGS
            + [
                self.OPTION_PATCH_DATA_BRANCH,
                self.VALUE_PATCH_DATA_BRANCH,
                self.OPTION_PATCH_DATA_CHANGELIST,
                self.VALUE_PATCH_DATA_CHANGELIST,
                self.OPTION_PATCH_CODE_BRANCH,
                self.VALUE_PATCH_CODE_BRANCH,
                self.OPTION_PATCH_CODE_CHANGELIST,
                self.VALUE_PATCH_CODE_CHANGELIST,
                self.OPTION_EXPORT_COMBINE_BUNDLES,
                "true",
                self.OPTION_STANDALONE_BASELINE,
            ],
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_baseline_state.assert_called_once_with(
            data_branch=self.VALUE_PATCH_DATA_BRANCH,
            data_changelist=self.VALUE_PATCH_DATA_CHANGELIST,
            code_branch=self.VALUE_PATCH_CODE_BRANCH,
            code_changelist=self.VALUE_PATCH_CODE_CHANGELIST,
            platform=self.ARGUMENT_PLATFORM,
            dest=os.path.join("tnt_root", "local", "baseline_state"),
            bundles_dir_name="bundles",
        )

    @patch("elipy2.filer_paths.get_head_bundles_path")
    @patch("os.path.exists")
    def test_dest_exists_exception(self, mock_exists, mock_get_head_bundles_path):
        mock_get_head_bundles_path.return_value = "bundles_path"
        mock_exists.side_effect = lambda x: True if x == "bundles_path" else False
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 1

    def test_clean_default(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_datautils.return_value.clean.assert_called_once_with(extra_pipeline_args=[])

    def test_clean_skip_for_staging_stream(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS + [self.OPTION_STAGING_STREAM])
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        assert self.mock_datautils.return_value.clean.call_count == 0

    def test_clean_use_for_staging_stream(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.DEFAULT_ARGS + [self.OPTION_STAGING_STREAM, self.OPTION_DATA_CLEAN, "true"]
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_datautils.return_value.clean.assert_called_once_with(extra_pipeline_args=[])

    def test_clean_virtual_branch_override(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.DEFAULT_ARGS + [self.OPTION_VIRTUAL_BRANCH_OVERRIDE, "true"]
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_datautils.return_value.clean.assert_called_once_with(
            extra_pipeline_args=["-stateId", self.VALUE_DATA_BRANCH]
        )
