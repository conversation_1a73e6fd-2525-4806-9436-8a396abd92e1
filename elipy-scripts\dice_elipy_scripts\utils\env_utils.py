"""
utility module for environment operations.
"""
from typing import Dict


def env_string_to_dict(var_string):
    """
    Returns a dict based on passed string.
    """
    env_variables = {}
    for variable in var_string.split(","):
        var = variable.split("=")
        env_variables.update({var[0]: var[1]})

    return env_variables


def extract_fb_env_values(values_list: list, build_step: str = "") -> Dict[str, str]:
    """
    Filters the Frostbite environment values based on the specified build step.

    :param values_list: A list of Frostbite environment values
    :param build_step (optional): The Frostbite build step to filter by
    :return: dict containing the Frostbite environment values for the specified build step
    """
    fb_env_values = {}
    try:
        for value_pair in values_list:
            key, value = value_pair.split("=", 1)
            if fb_env_values.get(key):
                # if the key already exists, we want to keep the value that matches the build step
                if build_step and value.lower().startswith(build_step.lower()):
                    fb_env_values[key] = value
            else:
                fb_env_values[key] = value
    except Exception as exc:
        raise ValueError(
            f"Error parsing Frostbite environment values: {values_list}. Exception: {exc}"
        )

    return fb_env_values
