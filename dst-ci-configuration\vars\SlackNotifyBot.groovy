/**
 * SlackNotifyBot.groovy
 * Trigger a slack bot which will notify people who've made commits after the latest successful build of a job.
 */
void call(def currentBuild, def slack_notify_bot = false) {
    if (Boolean.valueOf(slack_notify_bot)) {
        echo 'Build failed, notifying user(s) that have committed to this branch since the last successful build.'
        sh "/usr/bin/curl -s -XPOST http://dre-cobra-slack-bot.dre.dice.se?job_url=${currentBuild.absoluteUrl}"
    }
}
