"""
trigger_docker_image_gitlab_pipeline.py
"""
import os
import click
import requests
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import LOGGER
from elipy2.cli import pass_context
from elipy2.exceptions import ELIPYException
from elipy2.telemetry import collect_metrics


@click.command(
    "trigger_docker_image_gitlab_pipeline",
    short_help="Trigger the Docker image GitLab pipeline for a specific platform.",
)
@click.option(
    "--platform",
    type=click.Choice(["server", "client"]),
    required=True,
    help="Platform type to trigger the pipeline for.",
)
@click.option("--code-changelist", required=True, help="The changelist for the code branch.")
@click.option("--data-changelist", required=True, help="The changelist for the data branch.")
@click.option("--code-branch", required=True, help="The code branch to use.")
@click.option("--data-branch", required=True, help="The data branch to use.")
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(_, platform, code_changelist, data_changelist, code_branch, data_branch):
    """
    Trigger a pipeline job on gitlab.ea.com for the image repository, when a
    (patch)frosty build succeeds.
    """
    settings_map = {
        "server": {"repository_number": "4976", "token": "10d2eafc5598e2fa014ff0a5be0b46"},
        "client": {"repository_number": "9011", "token": "bc22decfcfe806e71aac1cced9195a"},
    }

    add_sentry_tags(__file__)

    api_url = (
        f"https://gitlab.ea.com/api/v4/projects/"
        f"{settings_map[platform]['repository_number']}/trigger/pipeline"
    )
    data = {
        "token": settings_map[platform]["token"],
        "ref": "master",
        "variables[CODE_BRANCH]": code_branch,
        "variables[CODE_CL]": code_changelist,
        "variables[DATA_BRANCH]": data_branch,
        "variables[DATA_CL]": data_changelist,
    }

    try:
        response = requests.post(api_url, data=data, timeout=10)  # timeout in seconds
        if response.status_code == 201:
            content = response.json()
            LOGGER.info("Pipeline triggered successfully! Pipeline URL: %s", content.get("web_url"))
        else:
            raise ELIPYException(
                f"Failed to trigger pipeline: HTTP {response.status_code} - {response.text}"
            )
    except requests.exceptions.Timeout:
        raise ELIPYException("The request timed out. Please try again later.")
    except requests.exceptions.RequestException as error:
        raise ELIPYException(f"An error occurred: {error}")
