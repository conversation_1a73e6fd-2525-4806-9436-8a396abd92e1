package com.ea.lib.jobs

import com.ea.lib.LibJobDsl
import com.ea.lib.jobsettings.NavmeshSettings

class LibNavmesh {
    /**
     * Adds generic job parameters for Navmesh start jobs.
     */
    static void navmesh_start(def job, def project, def branchFile, def masterFile, def branchName) {
        // Set values for variables.
        NavmeshSettings navmeshSettings = new NavmeshSettings()
        navmeshSettings.initializeNavmeshStart(branchFile, masterFile, project, branchName)

        // Add sections to the Jenkins job.
        job.with {
            description(navmeshSettings.description)
            disabled(navmeshSettings.isDisabled)
            logRotator(7, 100)
            quietPeriod(0)
            properties {
                disableConcurrentBuilds()
                disableResume()
                if (navmeshSettings.triggerType == 'scm') {
                    pipelineTriggers {
                        triggers {
                            pollSCM {
                                scmpoll_spec(navmeshSettings.triggerString)
                            }
                        }
                    }
                }
            }
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to sync.')
                    trim(true)
                }
                choiceParam('clean_data', ['False', 'True'], 'If True, Avalanche will be cleaned.')
            }
            environmentVariables {
                env('branch_name', branchName)
                env('data_branch', navmeshSettings.dataBranch)
                env('data_folder', navmeshSettings.dataFolder)
                env('data_reference_job', navmeshSettings.dataReferenceJob)
                env('frostbite_syncer_setup', navmeshSettings.frostbiteSyncerSetup)
                env('non_virtual_data_branch', navmeshSettings.nonVirtualDataBranch)
                env('non_virtual_data_folder', navmeshSettings.nonVirtualDataFolder)
                env('project_name', project.name)
            }
        }
    }

    /**
     * Adds generic job parameters for Navmesh build jobs.
     */
    static void navmesh_job(def job, def project, def branchFile, def masterFile, def branchName) {
        // Set values for variables.
        NavmeshSettings navmeshSettings = new NavmeshSettings()
        navmeshSettings.initializeNavmeshJob(branchFile, masterFile, project, branchName)

        // Add sections to the Jenkins job.
        job.with {
            description(navmeshSettings.description)
            label(navmeshSettings.jobLabel)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(navmeshSettings.workspaceRoot)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to sync.')
                    trim(true)
                }
                choiceParam('clean_data', ['False', 'True'], 'If True, Avalanche will be cleaned.')
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName('${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}')
                timeout {
                    absolute(navmeshSettings.timeoutMinutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, navmeshSettings.elipyInstallCall, project)
                batchFile(navmeshSettings.elipyCmd)
            }
        }
    }
}
