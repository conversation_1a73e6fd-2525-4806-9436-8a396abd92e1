"""
test_runner.py

An individual process is used for each test, protecting the whole run against crashes.
It also has retries and frosting reporting.
"""

import os
import click
from dice_elipy_scripts.utils.code_utils import run_gensln
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.licensee_helper import set_licensee
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import code, core, data, LOGGER, running_processes, frostbite_core
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command(
    "test_runner",
    short_help="""
        Runs a GTest project in a custom test runner that supports parallel execution and retries.
    """,
)
@click.option(
    "--custom-configs",
    help="File that contains customization of console arguments",
    multiple=True,
    required=True,
    type=str,
)
@click.option(
    "--pass-through",
    type=str,
    multiple=True,
    help='Arguments passed directly to GTest, for example --gtest "--gtest_repeat=10".',
)
@click.option("--email", default=None, help="User email to authenticate to package server.")
@click.option(
    "--domain-user",
    default=None,
    help="The user to authenticate to package server as DOMAIN\\user.",
)
@click.option(
    "--password",
    default=None,
    help="User credentials to authenticate to package server.",
)
@click.option("--licensee", multiple=True, default=None, help="Frostbite licensee.")
@click.option("--data-directory", default=None, help="Data directory.")
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    custom_configs,
    pass_through,
    email,
    domain_user,
    password,
    licensee,
    data_directory,
):
    """
    Run GTests through the Python test runner.
    """
    # Adding sentry tags
    add_sentry_tags(__file__)

    # Clean up before running the job.
    running_processes.kill()

    # Set data directory.
    if data_directory is not None:
        data.DataUtils.set_datadir(data_directory)

    test_runner = _get_test_runner_module()

    framework_args_gensln = set_licensee(
        licensee,
        [
            "-G:frostbite.build.notests=false",
            "-G:package.FBBuild.enableAllCppTests=true",
            "-G:package.FBBuild.enableAllCSharpTests=true",
        ],
    )

    # Initialize
    variant = "release"
    builder = code.CodeUtils("tool", variant)

    run_gensln(
        password=password,
        user=email,
        domain_user=domain_user,
        framework_args=framework_args_gensln,
        builder=builder,
    )

    # Build solution
    builder.buildsln()

    for custom_config in custom_configs:
        # Run the tests and report to Frosting
        test_runner.main(
            projects=None,
            variant=None,
            attempts=None,
            workers=None,
            test_filters=None,
            timeout=None,
            custom_config=custom_config,
            pass_through=list(pass_through),
            nofrosting=False,
            monkey=True,
        )


def _get_test_runner_module():
    try:
        # This is horrible. We need to find a better way around this.
        return core.import_module_from_file(
            "test_runner",
            os.path.join(
                frostbite_core.get_tnt_root(), "Code/DICE/BattlefieldGame/fbcli/test_runner.py"
            ),
        )
    except Exception as exc:
        LOGGER.error("Unable to import test_runner.py")
        raise exc
