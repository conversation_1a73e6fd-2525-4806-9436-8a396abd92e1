import com.ea.lib.jobsettings.CodeCoverageSettings
import spock.lang.Specification

class CodeCoverageSettingsSpec extends Specification {

    class BranchFile {
        static Map standard_jobs_settings = [
            branch_name       : 'branch',
            workspace_root    : 'workspace-root',
            disable_build     : true,
            elipy_call        : 'elipy-call',
            elipy_install_call: 'elipy-install-call',
            job_label         : 'build-release && util',
        ]
        static Map general_settings = [:]
    }

    class MasterFile {
        static Map branches = [branch: [data_branch            : 'data-branch',
                                        code_branch            : 'code-branch',
                                        code_folder            : 'game',
                                        non_virtual_code_branch: 'non-virtual-code-branch',
                                        non_virtual_code_folder: 'non-virtual-code-folder',
                                        codecoverage_args      :
                                            [reference_job: 'reference-job',
                                             run_script   : 'script']
        ]]
    }

    class ProjectFile {
        static String name = 'Kingston'
    }

    void "test that we get expected job settings in initializeCodeCoverageStart"() {
        when:
        CodeCoverageSettings codeCoverageSettings = new CodeCoverageSettings()
        codeCoverageSettings.initializeCodeCoverageStart(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(codeCoverageSettings) {
            branchName == 'branch'
            codeBranch == 'code-branch'
            codeFolder == 'game'
            cronTrigger == 'H 7 * * 6'
            description == 'Scheduler to codecoverage start job for branch'
            isDisabled == BranchFile.standard_jobs_settings.disable_build
            projectName == ProjectFile.name
            nonVirtualCodeBranch == 'non-virtual-code-branch'
            nonVirtualCodeFolder == 'non-virtual-code-folder'
            refJob == 'reference-job'
        }
    }

    void "test that we get expected job settings in initializeCodeCoverageRun"() {
        when:
        CodeCoverageSettings codeCoverageSettings = new CodeCoverageSettings()
        codeCoverageSettings.initializeCodeCoverageRun(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(codeCoverageSettings) {
            extraArgs == '--codecoverage-path ./autotest/script'
            buildName == '${JOB_NAME}.${ENV, var="code_changelist"}'
            jobLabel == 'build-release && util'
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} codecoverage --codecoverage-path ./autotest/script"
            description == 'Run CodeCoverage code on: branch'
        }
    }
}
