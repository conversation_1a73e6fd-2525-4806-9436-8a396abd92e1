package scripts.schedulers

import com.cloudbees.groovy.cps.NonCPS

/**
 * offlineAgent.groovy
 */
stage('KillSwitch') {
    timestamps {
        if ((!params.node) || (!params.reason)) {
            currentBuild.result = 'FAILURE'
            error('Require a valid node name and reason')
        } else {
            user = input submitterParameter: 'user', message: "Are you certain to take offline node: ${params.node}?"
            currentBuild.displayName = "${user}.${params.node}"
        }
    }
    node('master') {
        try {
            echo "Perform temperary offline on node ${params.node}"
            offlineNode(params.node, user + ': ' + params.reason)
        } catch (Exception e) {
            echo e.message
            throw e
        }
    }
}

@NonCPS
void offlineNode(String node, String reasons) {
    Jenkins.get().getComputer(node).setTemporarilyOffline(true, new hudson.slaves.OfflineCause.ByCLI(reasons))
    echo node
}
