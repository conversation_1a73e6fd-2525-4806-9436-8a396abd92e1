"""
move_location_bundles.py

This script allows us to copy bundles between different filer locations.
"""
import os
import click

from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import LOGGER, core, filer_paths
from elipy2.cli import pass_context
from elipy2.exceptions import ELIPYException
from elipy2.telemetry import collect_metrics


@click.command("move_location_bundles", short_help="Move data bundles to a different location.")
@click.option("--data-branch", required=True, help="Perforce data branch/stream name.")
@click.option("--data-changelist", required=True, help="Data changelist number.")
@click.option("--code-branch", required=True, help="Perforce code branch/stream name.")
@click.option("--code-changelist", required=True, help="Code changelist number.")
@click.option("--platform", required=True, help="Platform for the bundles.")
@click.option("--dest-location", required=True, help="Location to move the bundles to.")
@click.option("--source-location", default=None, help="Location to move the bundles from.")
@click.option(
    "--bundle-type",
    type=click.Choice(["bundles", "combine_bundles"]),
    default="bundles",
    help="Type of bundles to copy (bundles or combine_bundles).",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    data_branch,
    data_changelist,
    code_branch,
    code_changelist,
    platform,
    dest_location,
    source_location,
    bundle_type,
):
    """
    Move data bundles to a different location.

    This command copies bundles or combine_bundles for a specific platform from one filer location
    to another.

    Args:
        data_branch: Perforce data branch/stream name.
        data_changelist: Data changelist number.
        code_branch: Perforce code branch/stream name.
        code_changelist: Code changelist number.
        platform: Platform for the bundles.
        dest_location: Location to move the bundles to.
        source_location: Location to move the bundles from.
        bundle_type: Type of bundles to copy ("bundles" or "combine_bundles").
    """
    add_sentry_tags(__file__)

    # Determine source path
    if source_location:
        # Get source path from specified source location
        source_base = filer_paths.get_frosty_base_build_path(
            data_branch=data_branch,
            data_changelist=data_changelist,
            code_branch=code_branch,
            code_changelist=code_changelist,
            platform=platform,
            location=source_location,
        )
        source_path = os.path.join(source_base, bundle_type)
    else:
        # Use default path
        source_path = filer_paths.get_bundles_path(
            data_branch=data_branch,
            data_changelist=data_changelist,
            code_branch=code_branch,
            code_changelist=code_changelist,
            platform=platform,
            bundles_dir_name=bundle_type,
        )

    # Determine destination path
    dest_base = filer_paths.get_frosty_base_build_path(
        data_branch=data_branch,
        data_changelist=data_changelist,
        code_branch=code_branch,
        code_changelist=code_changelist,
        platform=platform,
        location=dest_location,
    )
    dest_path = os.path.join(dest_base, bundle_type)

    # Check if source path exists
    if not os.path.exists(source_path):
        raise ELIPYException(f"Source path does not exist: {source_path}")

    # Create destination directory if it doesn't exist
    os.makedirs(os.path.dirname(dest_path), exist_ok=True)

    # Check if destination already exists to avoid overwriting
    if os.path.exists(dest_path):
        raise ELIPYException(
            f"Destination path already exists: {dest_path}. "
            f"Will not overwrite existing bundles."
        )

    # Copy bundles from source to destination
    LOGGER.info("Copying %s from %s to %s", bundle_type, source_path, dest_path)
    core.robocopy(source_path, dest_path)
    LOGGER.info("Successfully copied %s to %s", bundle_type, dest_location)
