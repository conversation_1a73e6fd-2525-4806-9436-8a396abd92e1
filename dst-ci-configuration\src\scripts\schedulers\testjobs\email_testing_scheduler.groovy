package scripts.schedulers.testjobs

import com.ea.lib.LibJenkins
import com.sonyericsson.jenkins.plugins.bfa.BuildFailureScanner
import org.jenkinsci.plugins.workflow.cps.CpsThreadGroup

/**
 * email_testing_scheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        // here we need clone git repo, because we are calling PreflightEmailSending() which needs code in workspace
        stage('Sync DRE Git repository') {
            steps {
                GitSync('dst-preflights')
            }
        }
        stage('Trigger email testing jobs') {
            steps {
                script {
                    def args = []
                    def jobs = [:]

                    currentBuild.displayName = env.JOB_NAME + '.' + env.BUILD_NUMBER

                    def jobs_to_report = [
                        'job1',
                        'job2',
                    ]

                    if (params.success_report == 'false') {
                        jobs_to_report = jobs_to_report + 'job3' + 'job4'
                    }

                    def final_result = Result.SUCCESS

                    for (job_to_report in jobs_to_report) {
                        def job_name = 'email-testing.' + job_to_report
                        jobs[job_name] = {
                            def downstream_job = build(job: job_name, parameters: args, propagate: false)
                            final_result = final_result.combine(Result.fromString(downstream_job.result))
                            LibJenkins.printRunningJobs(this)
                        }
                    }

                    parallel(jobs)
                    currentBuild.result = final_result.toString()

                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
        stage('Scan for errors') {
            steps {
                script {
                    BuildFailureScanner.scanIfNotScanned(currentBuild.rawBuild, CpsThreadGroup.current().execution.owner.listener.logger)
                    currentBuild.rawBuild.save()
                }
            }
        }
    }
    post {
        always {
            PreflightEmailSending()
        }
    }
}
