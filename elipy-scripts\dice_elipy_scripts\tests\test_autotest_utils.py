"""
test_autotest_utils.py

Unit testing for autotest_utils
"""
import os
import time
from typing import Iterable
from unittest.mock import MagicMock, patch
from dice_elipy_scripts.utils.icepick_utils import (
    get_icepick_logs_dir,
    get_icepick_logs_files,
    save_icepick_logs,
)
from dice_elipy_scripts.utils.autotest_utils import register_autotest_results
from elipy2.exceptions import ELIPYException
import pytest


def glob_side_effect(glob_pattern: str) -> Iterable[str]:
    files = []
    if "atf.targets.*.log" in glob_pattern:
        files = ["aft.targets.1234.log", "aft.targets.12.log", "aft.targets.5.log"]
    elif "Icepick.*.log" in glob_pattern:
        files = ["Icepick.1234.log", "Icepick.5.log"]

    return files


def get_icepick_logs_files_side_effect(_patterns=None):
    data = {
        "atf.targets.*.log": [
            os.path.join("localappdata_path", "aft.targets.1234.log"),
            os.path.join("localappdata_path", "aft.targets.12.log"),
            os.path.join("localappdata_path", "aft.targets.5.log"),
        ],
        "Icepick.*.log": [
            os.path.join("localappdata_path", "Icepick.1234.log"),
            os.path.join("localappdata_path", "Icepick.5.log"),
        ],
    }
    return data


def getmtime_side_effect(file: str) -> float:
    data = {
        os.path.join("localappdata_path", "aft.targets.1234.log"): 0.1,
        os.path.join("localappdata_path", "aft.targets.12.log"): 0.2,
        os.path.join("localappdata_path", "aft.targets.5.log"): 0.3,
        os.path.join("localappdata_path", "Icepick.1234.log"): 0.1,
        os.path.join("localappdata_path", "Icepick.5.log"): 0.3,
    }

    value = data.get(file, 1)
    return value


@pytest.mark.usefixtures("settings_env_vars")
class TestIcepickUtils:
    def test_get_icepick_logs_dir(self):
        path = get_icepick_logs_dir()
        assert path == os.path.join("localappdata_path", "Icepick", "logs")

    @patch("glob.glob", MagicMock(side_effect=glob_side_effect))
    @patch("os.path.isfile", MagicMock(return_value=True))
    def test_get_icepick_logs_files(self):
        files = get_icepick_logs_files()
        assert len(files) == 2
        assert len(files["atf.targets.*.log"]) == 3
        assert len(files["Icepick.*.log"]) == 2

    @patch(
        "dice_elipy_scripts.utils.icepick_utils.get_icepick_logs_files",
        MagicMock(side_effect=get_icepick_logs_files_side_effect),
    )
    @patch("os.path.getmtime", MagicMock(side_effect=getmtime_side_effect))
    @patch("elipy2.core.robocopy")
    def test_save_icepick_logs(self, mock_robocopy: MagicMock):
        save_icepick_logs()
        assert mock_robocopy.call_count == 1
        mock_robocopy.assert_called_once_with(
            os.path.join("localappdata_path", "Icepick", "logs"),
            os.path.join("game_root", "logs"),
            extra_args=["aft.targets.1234.log", "Icepick.1234.log"],
        )

    @patch(
        "dice_elipy_scripts.utils.icepick_utils.get_icepick_logs_files",
        MagicMock(side_effect=get_icepick_logs_files_side_effect),
    )
    @patch("os.path.getmtime", MagicMock(side_effect=getmtime_side_effect))
    @patch("elipy2.core.robocopy", MagicMock(side_effect=TypeError("testing type error")))
    def test_save_icepick_logs_no_typeerror(self):
        try:
            save_icepick_logs()
        except TypeError as exc:
            assert False, f"'sum_x_y' raised an exception {exc}"


class TestRegisterAutotestResults:
    # Common test parameters
    CODE_BRANCH = "test-branch"
    CODE_CHANGELIST = "12345"
    DATA_BRANCH = "test-data-branch"
    DATA_CHANGELIST = "67890"
    TEST_DEFINITION = "test-definition"
    TEST_STATUS = "successful"
    TEST_SUITE = "test-suite"
    PLATFORM = "win64"
    BUILD_PATH = "\\\\path\\to\\build"

    @patch("dice_elipy_scripts.utils.autotest_utils.build_metadata_utils.setup_metadata_manager")
    @patch("dice_elipy_scripts.utils.autotest_utils.filer_paths.get_code_build_root_path")
    @patch("dice_elipy_scripts.utils.autotest_utils.time.sleep")
    def test_register_autotest_results_build_exists(
        self, mock_sleep, mock_get_path, mock_setup_manager
    ):
        """Test successful registration when the build exists"""
        # Setup mocks
        mock_metadata_manager = MagicMock()
        mock_setup_manager.return_value = mock_metadata_manager
        mock_get_path.return_value = self.BUILD_PATH

        # Mock that the build exists
        mock_metadata_manager.get_builds_matching.return_value = [MagicMock()]

        # Call the function
        register_autotest_results(
            should_register_in_bilbo=True,
            code_branch=self.CODE_BRANCH,
            code_changelist=self.CODE_CHANGELIST,
            data_branch=self.DATA_BRANCH,
            data_changelist=self.DATA_CHANGELIST,
            test_definition=self.TEST_DEFINITION,
            test_status=self.TEST_STATUS,
            test_suite=self.TEST_SUITE,
            platform=self.PLATFORM,
            max_retries=3,
            wait_time_seconds=1,
        )

        # Verify behavior
        mock_get_path.assert_called_once_with(self.CODE_BRANCH, self.CODE_CHANGELIST)
        mock_metadata_manager.get_builds_matching.assert_called_once_with(self.BUILD_PATH)
        mock_metadata_manager.register_autotest_build.assert_called_once_with(
            self.BUILD_PATH,
            data_changelist=self.DATA_CHANGELIST,
            data_branch=self.DATA_BRANCH,
            test_category=self.TEST_DEFINITION,
            test_name=self.TEST_SUITE,
            status=self.TEST_STATUS,
            platform=self.PLATFORM,
            write_attributes_file=False,
        )
        mock_sleep.assert_not_called()

    @patch("dice_elipy_scripts.utils.autotest_utils.build_metadata_utils.setup_metadata_manager")
    @patch("dice_elipy_scripts.utils.autotest_utils.filer_paths.get_code_build_root_path")
    @patch("dice_elipy_scripts.utils.autotest_utils.time.sleep")
    def test_register_autotest_results_build_appears_later(
        self, mock_sleep, mock_get_path, mock_setup_manager
    ):
        """Test retry behavior when the build doesn't exist initially but appears later"""
        # Setup mocks
        mock_metadata_manager = MagicMock()
        mock_setup_manager.return_value = mock_metadata_manager
        mock_get_path.return_value = self.BUILD_PATH

        # Mock that the build doesn't exist on first call but exists on second call
        mock_metadata_manager.get_builds_matching.side_effect = [[], [MagicMock()]]

        # Call the function
        register_autotest_results(
            should_register_in_bilbo=True,
            code_branch=self.CODE_BRANCH,
            code_changelist=self.CODE_CHANGELIST,
            data_branch=self.DATA_BRANCH,
            data_changelist=self.DATA_CHANGELIST,
            test_definition=self.TEST_DEFINITION,
            test_status=self.TEST_STATUS,
            test_suite=self.TEST_SUITE,
            platform=self.PLATFORM,
            max_retries=3,
            wait_time_seconds=1,
        )

        # Verify behavior
        assert mock_metadata_manager.get_builds_matching.call_count == 2
        mock_sleep.assert_called_once_with(1)
        mock_metadata_manager.register_autotest_build.assert_called_once()

    @patch("dice_elipy_scripts.utils.autotest_utils.build_metadata_utils.setup_metadata_manager")
    @patch("dice_elipy_scripts.utils.autotest_utils.filer_paths.get_code_build_root_path")
    @patch("dice_elipy_scripts.utils.autotest_utils.time.sleep")
    def test_register_autotest_results_max_retries(
        self, mock_sleep, mock_get_path, mock_setup_manager
    ):
        """Test maximum retries when the build never appears"""
        # Setup mocks
        mock_metadata_manager = MagicMock()
        mock_setup_manager.return_value = mock_metadata_manager
        mock_get_path.return_value = self.BUILD_PATH

        # Mock that the build never exists - return empty list for each call
        mock_metadata_manager.get_builds_matching.side_effect = [[], [], []]

        # Call the function with max_retries=2
        with pytest.raises(ELIPYException) as excinfo:
            register_autotest_results(
                should_register_in_bilbo=True,
                code_branch=self.CODE_BRANCH,
                code_changelist=self.CODE_CHANGELIST,
                data_branch=self.DATA_BRANCH,
                data_changelist=self.DATA_CHANGELIST,
                test_definition=self.TEST_DEFINITION,
                test_status=self.TEST_STATUS,
                test_suite=self.TEST_SUITE,
                platform=self.PLATFORM,
                max_retries=2,
                wait_time_seconds=1,
            )

        # Verify behavior
        error_msg = str(excinfo.value)
        assert "Build not found in Bilbo after 2 retries" in error_msg
        assert self.BUILD_PATH in error_msg
        assert mock_metadata_manager.get_builds_matching.call_count == 2
        # We expect sleep to be called once for the first retry
        # The second retry doesn't call sleep because it's the last retry
        mock_sleep.assert_called_once_with(1)
        mock_metadata_manager.register_autotest_build.assert_not_called()

    @patch("dice_elipy_scripts.utils.autotest_utils.build_metadata_utils.setup_metadata_manager")
    @patch("dice_elipy_scripts.utils.autotest_utils.filer_paths.get_code_build_root_path")
    @patch("dice_elipy_scripts.utils.autotest_utils.time.sleep")
    def test_register_autotest_results_index_error_handling(
        self, mock_sleep, mock_get_path, mock_setup_manager
    ):
        """Test IndexError handling during build check"""
        # Setup mocks
        mock_metadata_manager = MagicMock()
        mock_setup_manager.return_value = mock_metadata_manager
        mock_get_path.return_value = self.BUILD_PATH

        # Mock that checking for the build raises an IndexError first, then succeeds
        mock_metadata_manager.get_builds_matching.side_effect = [
            IndexError("Test exception"),
            [MagicMock()],
        ]

        # Call the function
        register_autotest_results(
            should_register_in_bilbo=True,
            code_branch=self.CODE_BRANCH,
            code_changelist=self.CODE_CHANGELIST,
            data_branch=self.DATA_BRANCH,
            data_changelist=self.DATA_CHANGELIST,
            test_definition=self.TEST_DEFINITION,
            test_status=self.TEST_STATUS,
            test_suite=self.TEST_SUITE,
            platform=self.PLATFORM,
            max_retries=2,
            wait_time_seconds=1,
        )

        # Verify behavior
        assert mock_metadata_manager.get_builds_matching.call_count == 2

        mock_sleep.assert_not_called()
        mock_metadata_manager.register_autotest_build.assert_called_once()

    @patch("dice_elipy_scripts.utils.autotest_utils.build_metadata_utils.setup_metadata_manager")
    @patch("dice_elipy_scripts.utils.autotest_utils.filer_paths.get_code_build_root_path")
    @patch("dice_elipy_scripts.utils.autotest_utils.time.sleep")
    def test_register_autotest_results_default_parameters(
        self, mock_sleep, mock_get_path, mock_setup_manager
    ):
        """Test that default parameters are used correctly"""
        # Setup mocks
        mock_metadata_manager = MagicMock()
        mock_setup_manager.return_value = mock_metadata_manager
        mock_get_path.return_value = self.BUILD_PATH

        # Mock that the build exists
        mock_metadata_manager.get_builds_matching.return_value = [MagicMock()]

        # Call the function without specifying max_retries and wait_time_seconds
        register_autotest_results(
            should_register_in_bilbo=True,
            code_branch=self.CODE_BRANCH,
            code_changelist=self.CODE_CHANGELIST,
            data_branch=self.DATA_BRANCH,
            data_changelist=self.DATA_CHANGELIST,
            test_definition=self.TEST_DEFINITION,
            test_status=self.TEST_STATUS,
            test_suite=self.TEST_SUITE,
            platform=self.PLATFORM,
        )

        # Verify behavior
        mock_metadata_manager.get_builds_matching.assert_called_once()
        mock_sleep.assert_not_called()
        mock_metadata_manager.register_autotest_build.assert_called_once()
