package schedulers

import support.DeclarativePipelineSpockTest

class FrostyOrchestratorSchedulerSpec extends DeclarativePipelineSpockTest {

    void setup() {
        binding.setVariable('env', [
            code_changelist: '100',
            data_changelist: '200',
            branch_name    : 'trunk-code-dev',
            JOB_NAME       : 'trunk-code-dev.deployment-data.start',
        ])
        binding.setVariable('params', [
            clean_data: false,
        ])
        binding.setVariable('currentBuild', [
            result: null,
        ])
        helper.with {
            registerAllowedMethod('retrieveIfTargetChangelistsAreHigher', [String, String]) { jobName, targetJobName ->
                [
                    code_changelist: '101',
                    data_changelist: '201',
                ]
            }
            registerAllowedMethod('EnvInject', [Map, Map]) { currentBuild, injectMap ->
                binding.setVariable('env', binding.getVariable('env') + injectMap)
            }
        }
    }

    void 'test FrostyOrchestratorScheduler triggers a job with correct changelists'() {
        when:
        runScript('FrostyOrchestratorScheduler.groovy')
        printCallStack()
        then:
        assertCalledOnceWith('build', [
            job       : 'trunk-code-dev.deployment-data.start',
            wait      : false,
            parameters: [
                [name: 'code_changelist', value: '101'],
                [name: 'data_changelist', value: '201'],
                [name: 'clean_data', value: false]
            ],
            propagate : false,
        ])
        testNonRegression()
    }

}
