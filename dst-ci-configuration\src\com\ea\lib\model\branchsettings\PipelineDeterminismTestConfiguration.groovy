package com.ea.lib.model.branchsettings

class PipelineDeterminismTestConfiguration {
    /**
     * Whether or not the job is enabled
     */
    boolean enabled = true
    /**
     * Agent labels
     */
    String label = 'statebuild'
    /**
     * Cron trigger string
     */
    String cronTrigger
    /**
     * Reference job to get last changelist
     */
    String perforceCodeServer
    /**
     * Perforce server to use to sync code
     */
    String perforceCodeCredentials
    /**
     * <PERSON> credentials to use to connect to Perforce code server
     */
    String perforceDataServer
    /**
     * Perforce server to use to sync data
     */
    String perforceDataCredentials
    /**
     * Jenkins credentials to use to connect to Perforce data server
     */
    String referenceJob
    /**
     * Slack channel to notify
     */
    String slackChannel
    /**
     * Job timeout in hours
     */
    int timeoutHours = 12
    /**
     * Job timeout in hours
     * @return the timeout
     */
    int getTimeoutMinutes() {
        return timeoutHours * 60
    }

    @Override
    String toString() {
        return "{com.ea.lib.model.branchsettings.PipelineDeterminismTestConfiguration: enabled: $enabled, referenceJob: $referenceJob}"
    }
}
