"""
codecoverage.py

This script to run Frostbite CodeCoverage TestAutomation.

You can find more details about the tool here:
    https://gitlab.ea.com/ease-qe/Scripts/-/blob/master/TestAutomation/CodeCoverageTestAutomation.bat
"""

import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import LOGGER, core, frostbite_core
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command("CodeCoverage", short_help="Run CodeCoverage.")
@click.option(
    "--codecoverage-path",
    default="TestAutomation/CodeCoverageTestAutomation.bat",
    help="Test script with relative path from Jenkins workspace",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(_, codecoverage_path):
    """
    Runs CodeCoverage.bat
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    codecoverage = os.path.join(frostbite_core.get_game_root(), codecoverage_path)
    # run autotesting script
    LOGGER.info("Running testautomation CodeCoverage")
    args = [codecoverage]
    _, stdout, _ = core.run(args)
    LOGGER.info("".join(stdout))
    LOGGER.info("Done")
