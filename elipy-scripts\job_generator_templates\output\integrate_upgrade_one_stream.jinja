{#
    Command:
        integrate_upgrade_one_stream
            short_help: Copy and/or integrate code, upgrade data locally or with integration.

    Arguments:

    Required variables:
        assets
            required: True
            multiple: True
            help: Assets to use for data cook.
        changelist
            required: True
            help: Perforce source changelist.
        licensee
            multiple: True
            required: True
            help: Frostbite licensee.
        p4_client
            required: True
            help: Perforce client/workspace.
        p4_port
            required: True
            help: Perforce port/server.

    Optional variables:
        code_clean
            default: false
            help: Delete TnT/Local if --code-clean true is passed.
        copy_mapping
            default: None
            help: Mapping for the copy step.
        copy_reverse
            is_flag: True
            help: Reverse the direction for the copy step.
        data_clean
            default: false
            help: Clean Avalanche if --data-clean true is passed.
        data_directory
            default: Data
            help: Which data directory to use.
        data_directory_source
            default: SourceData
            help: Which data directory to use.
        data_platform
            default: win64
            help: Platform to use for data cook.
        domain_user
            default: None
            help: The user to authenticate to package server as DOMA<PERSON>\user.
        email
            default: None
            help: User email to authenticate to package server.
        framework_args
            multiple: True
            help: Framework arguments for gensln.
        ignore_source_history
            is_flag: True
            help: Ignore source file history (sets the Perforce integrate flag -Di).
        integrate_mapping
            default: None
            help: Mapping for the integration step.
        integrate_reverse
            is_flag: True
            help: Reverse the direction for the integration step.
        integration_upgrade_script_path
            default: None
            help: Script path for the integration upgrade.
        last_changelist
            default: None
            help: Changelist for last successful build.
        local_upgrade
            is_flag: True
            help: Run the data upgrade on locally on the same stream, instead of a data upgrade integration.
        local_upgrade_script_path
            default: None
            help: Script path for the local upgrade.
        p4_path_source
            default: None
            help: Perforce path for the source.
        p4_user
            default: None
            help: Perforce user name.
        password
            default: None
            help: User credentials to authenticate to package server.
        pipeline_args
            multiple: True
            help: Pipeline arguments for the data cook.
        revert_branchid_file
            is_flag: True
            help: Revert the BranchId.py file.
        run_cook
            default: True
            help: Run a cook for the integrated/upgraded data.
        run_upgrade
            default: True
            help: Run an upgrade (local or FDU integration is controlled by --local-upgrade).
        shelve_cl
            default: False
            help: Shelve a changelist from the integration if we run with --no-submit.
        submit/__no_submit
            default: True
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    integrate_upgrade_one_stream
    --assets {{ assets }}
    --changelist {{ changelist }}
    --licensee {{ licensee }}
    --p4-client {{ p4_client }}
    --p4-port {{ p4_port }}
    {%- if code_clean %}
    --code-clean {{ code_clean }}
    {%- endif %}
    {%- if copy_mapping %}
    --copy-mapping {{ copy_mapping }}
    {%- endif %}
    {%- if copy_reverse %}
    --copy-reverse {{ copy_reverse }}
    {%- endif %}
    {%- if data_clean %}
    --data-clean {{ data_clean }}
    {%- endif %}
    {%- if data_directory %}
    --data-directory {{ data_directory }}
    {%- endif %}
    {%- if data_directory_source %}
    --data-directory-source {{ data_directory_source }}
    {%- endif %}
    {%- if data_platform %}
    --data-platform {{ data_platform }}
    {%- endif %}
    {%- if domain_user %}
    --domain-user {{ domain_user }}
    {%- endif %}
    {%- if email %}
    --email {{ email }}
    {%- endif %}
    {%- if framework_args %}
    --framework-args {{ framework_args }}
    {%- endif %}
    {%- if ignore_source_history %}
    --ignore-source-history {{ ignore_source_history }}
    {%- endif %}
    {%- if integrate_mapping %}
    --integrate-mapping {{ integrate_mapping }}
    {%- endif %}
    {%- if integrate_reverse %}
    --integrate-reverse {{ integrate_reverse }}
    {%- endif %}
    {%- if integration_upgrade_script_path %}
    --integration-upgrade-script-path {{ integration_upgrade_script_path }}
    {%- endif %}
    {%- if last_changelist %}
    --last-changelist {{ last_changelist }}
    {%- endif %}
    {%- if local_upgrade %}
    --local-upgrade {{ local_upgrade }}
    {%- endif %}
    {%- if local_upgrade_script_path %}
    --local-upgrade-script-path {{ local_upgrade_script_path }}
    {%- endif %}
    {%- if p4_path_source %}
    --p4-path-source {{ p4_path_source }}
    {%- endif %}
    {%- if p4_user %}
    --p4-user {{ p4_user }}
    {%- endif %}
    {%- if password %}
    --password {{ password }}
    {%- endif %}
    {%- if pipeline_args %}
    --pipeline-args {{ pipeline_args }}
    {%- endif %}
    {%- if revert_branchid_file %}
    --revert-branchid-file {{ revert_branchid_file }}
    {%- endif %}
    {%- if run_cook %}
    --run-cook {{ run_cook }}
    {%- endif %}
    {%- if run_upgrade %}
    --run-upgrade {{ run_upgrade }}
    {%- endif %}
    {%- if shelve_cl %}
    --shelve-cl {{ shelve_cl }}
    {%- endif %}
    {%- if submit/__no_submit %}
    --submit/--no-submit {{ submit/__no_submit }}
    {%- endif %}
  displayName: elipy integrate_upgrade_one_stream
