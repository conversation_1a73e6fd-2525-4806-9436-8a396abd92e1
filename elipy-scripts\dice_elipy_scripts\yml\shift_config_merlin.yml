# Duplicated settings lower in the settings list will get higher priority
buildtype: "QA"
milestone: "Production"
distribution_type: "InternalOnly"
retention_policy: "SpaceAvailable"
priority: ""
version: "2.0"
xbsx:
  content:
    files:
      file_names:
        - "builtLevels.json"
        - "*.buildlayout"
        - "*.BuildSettings"
        - "Logo.png"
        - "*.dll"
        - "SmallLogo.png"
        - "SplashScreen.png"
        - "StoreLogo.png"
        - "WideLogo.png"
        - "*.Main_Xbsx_*.exe"
        - "MicrosoftGame.config"
        - "gameos.xvd"
        - "nsal.json"
        - "package.mft"
        - "resources.pri"
        - "build.json"
      supplemental_files:
        - "*.xml"
        - "*.pdb"
      directory:
        - "Data"
        - "Scripts"
    digital:
      file_names:
        - "*appxmanifest.xml"
        - "MicrosoftGame.config"
        - "*neutral__*.xvc"
        - "*.ekb"
        - "Validator_*.xml"
      supplemental_files:
        - "build.json"
        - "*.pdb"
        - "*neutral__*[!c]" #This is kind of a hack. glob.glob doesn't support do not match string; only set of characters.
      directory:
        - ""
    patch:
      file_names:
        - "*appxmanifest.xml"
        - "MicrosoftGame.config"
        - "*neutral__*.xvc"
        - "*.ekb"
        - "Validator_*.xml"
      supplemental_files:
        - "build.json"
        - "*.pdb"
        - "*neutral__*[!c]" #This is kind of a hack. glob.glob doesn't support do not match string; only set of characters.
      directory:
        - ""
  settings:
    build-main:
      ww:
        final:
          files:
            sku_id: "2c50e0c5-237c-4f52-ac91-4f319423a894"
            sku_name: "FG - WW (build-main - files)"
ps5:
  content:
    files:
      file_names:
        - "builtLevels.json"
        - "build.json"
        - "*.prx"
        - "*.elf"
        - "*.buildlayout"
        - "*.BuildSettings"
        - "eboot.bin"
      supplemental_files:
        - "*.xml"
      directory:
        - "Data"
        - "Scripts"
        - "sce_sys"
    digital:
      file_names:
        - "*-V0100.pkg"
      upload_loop_filenames:
        - "*V0100.pkg"
        - "*remastered.pkg"
      supplemental_files:
        - "chunks*.gp5"
        - "build.json"
      directory:
        - ""
    patch:
      file_names:
        - "*-V0100.pkg"
      supplemental_files:
        - "chunks*.gp5"
        - "build.json"
      directory:
        - ""
    patch-remaster:
      file_names:
        - "*remastered.pkg"
      supplemental_files:
        - "chunks*.gp5"
        - "build.json"
      directory:
        - ""
  settings:
    build-main:
      ww:
        final:
          files:
            sku_id: "6ae80833-c844-4f83-b8b2-0eb772edc147"
            sku_name: "FG - WW (build-main - files)"
server:
  content:
    file_names:
      - '*'
    supplemental_files:
      - ""
  settings:
    build-main:
      final:
        files:
          sku_id: "987e67f5-72b9-4599-8612-eecc97670a76"
          sku_name: "Server - WW (build-main - windows - files)"
    build-uxr:
      final:
        files:
          sku_id: "56fda845-55d8-4db3-8549-e65e0f3b9153"
          sku_name: "Server - WW (build-uxr Win32 Files Final WW)"

win64:
  content:
    files:
      file_names:
        - '*'
      supplemental_files:
      - "*.xml"
    digital:
      file_names:
        - "Merlin.zip"
      supplemental_files:
        - "build.json"
        - "installerdata.xml"
      directory:
        - ""
      retail:
        supplemental_files:
          - "*.Main_*_retail*"
        directory:
          - ""
      final:
        supplemental_files:
          - "*.Main_*_retail*"
        directory:
          - ""
      performance:
        supplemental_files:
          - "*.Main_*_performance*"
    patch:
      file_names:
        - "Merlin.zip"
      supplemental_files:
        - "build.json"
        - "installerdata.xml"
        - "*.Main_*_retail*"
      directory:
        - ""
  settings:
    build-main:
      final:
        files:
          sku_id: "4725dfd7-bac9-44ef-b0e3-72468a2b321f"
          sku_name: "FG - WW (build-main - files)"
    build-uxr:
      final:
        files:
          sku_id: "45dc5445-44d7-4e3e-be4e-0becf3a4b7f5"
          sku_name: "FG - WW (build-uxr Win32 Files Final WW)"
linuxserver:
  content:
    file_names:
      - '*binaries.Zip'
    supplemental_files:
      - "build.json"
      - "builtLevels.json"
      - "*Symbols.zip"
  settings:
    build-main:
      final:
        digital:
          sku_id: "3c1e7ca2-82f0-46d0-acd4-a460b7e865b9"
          sku_name: "Server - WW (build-main - linux - digital - final)"
offsite_basic_drone:
  content:
    file_names:
      - '*'
    supplemental_files:
      - ""
  settings:
    build-main:
        sku_id: "0d26f56e-c33b-4370-8cf0-5908e78e884e"
        distribution_type: "ExternalTechPartners"
        sku_name: "Tool - WW (build-main Offsite Drone Build)"
    build-stage:
        distribution_type: "ExternalTechPartners"
        sku_name: "Tool - WW (build-stage Offsite Drone Build)"
    build-live-vehicles:
        distribution_type: "ExternalTechPartners"
        sku_name: "Tool - WW (build-live-vehicles Offsite Drone Build)"
