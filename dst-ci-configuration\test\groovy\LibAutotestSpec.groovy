import com.ea.lib.jobs.LibAutotest
import com.ea.lib.model.autotest.AutotestCategory
import com.ea.lib.model.autotest.TestSuite
import spock.lang.Specification

class LibAutotestSpec extends Specification {
    void "test game server condition"() {
        given:
        TestSuite testSuite = new TestSuite(
            needGameServer: testSuiteNeedServer
        )
        AutotestCategory testCategory = new AutotestCategory(
            needGameServer: testCategoryNeedServer
        )
        expect:
        LibAutotest.checkGameServer(testSuite, testCategory) == expectedResult
        where:
        testSuiteNeedServer | testCategoryNeedServer || expectedResult
        true                | true                   || true
        true                | false                  || true
        false               | true                   || false
        false               | false                  || false
        null                | true                   || true
        null                | false                  || false
    }
}
