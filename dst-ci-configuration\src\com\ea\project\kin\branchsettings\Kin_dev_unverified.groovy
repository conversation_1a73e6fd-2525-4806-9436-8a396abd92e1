package com.ea.project.kin.branchsettings

import com.ea.lib.LibPerforce
import com.ea.project.kin.Kingston

class Kin_dev_unverified {
    // Settings for jobs
    static Class project = Kingston
    static Map general_settings = [
        dataset                 : project.dataset,
        frostbite_licensee      : project.frostbite_licensee,
        elipy_install_call      : project.elipy_install_call,
        elipy_call              : project.elipy_call,
        workspace_root          : project.workspace_root,
        azure_elipy_call        : project.azure_elipy_call,
        azure_elipy_install_call: project.azure_elipy_install_call,
        azure_workspace_root    : project.azure_workspace_root,
        azure_fileshare         : [
            additional_tools_to_include: ['win64', 'tool'],
            secret_context             : 'kingston_azure_fileshare',
            target_build_share         : 'bfkingston',
        ],
        gametool_settings       : [
            gametools: [
                (LibPerforce.GAMETOOL_DRONE): [],
            ],
        ],
    ]
    static Map standard_jobs_settings = [
        asset                             : 'ShippingLevels',
        clean_local_nomaster              : true,
        custom_tests                      : [
            custom_configs: [
                'kin-dev-unverified.json',
                'kin-dev-unverified-ade.json',
            ],
        ],
        enable_daily_data_clean           : true,
        enable_lkg_cleaning               : true,
        enable_lkg_p4_counters            : true,
        extra_data_args                   : ['--pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        fake_ooa_wrapped_symbol           : true,
        frosty_asset                      : 'Game/Setup/Build/ReleaseLooseFileLevels',
        frosty_digital_asset              : 'ShippingLevels',
        frosty_only_build_on_new_code     : true,
        import_avalanche_autotest         : false,
        linux_docker_images               : false,
        marvin_trigger_upload_and_test    : false,
        offsite_code_token                : '<EMAIL>:1180acfb166f9612424e10e603d75acc0e',
        offsite_drone_builds              : true,
        offsite_job_remote                : 'http://dice-la-jenkins-tools.la.ad.ea.com:8080/job/GetNewDrone_kin-dev-unverified/buildWithParameters?token=2Zm67RaPGVd6^&code_changelist=%code_changelist%^&cause=%BUILD_URL%^&share_root=\\\\filer.dice.ad.ea.com\\Builds\\Kingston\\code\\kin-dev-unverified',
        poolbuild_frosty                  : true,
        poolbuild_label_frosty_xb1_files  : 'poolbuild_frosty',
        poolbuild_label_frosty_xbsx_files : 'poolbuild_frosty',
        poolbuild_label_frosty_ps4_files  : 'poolbuild_frosty',
        poolbuild_label_frosty_ps5_files  : 'poolbuild_frosty',
        poolbuild_label_frosty_win64_files: 'poolbuild_frosty',
        remote_masters_to_receive_code    : [
            [name: 'kin-preflight-jenkins.cobra.dre.ea.com', allow_failure: false],
            [name: 'aws-kin-staging.dre.dice.se', allow_failure: true],
        ],
        remote_masters_to_receive_data    : [
            [name: 'kin-preflight-jenkins.cobra.dre.ea.com', allow_failure: false],
            [name: 'aws-kin-staging.dre.dice.se', allow_failure: true],
        ],
        // autotest_remote_settings               : [
        //     p4_code_server            : 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
        //     p4_code_creds             : 'dice-p4buildedge03-fb',
        // ],
        remote_masters_trigger_pipeline   : true,
        report_build_version              : ' --reporting-build-version-id %code_changelist%',
        server_asset                      : 'Game/Setup/Build/ReleaseLooseFileLevels',
        shift_branch                      : true,
        slack_channel_code                : [channels: ['#kin-build-notify']],
        slack_channel_data                : [channels: ['#kin-build-notify']],
        slack_channel_frosty              : [channels: ['#kin-build-notify', '#earo-frosty-notify']],
        statebuild_code                   : false,
        statebuild_data                   : false,
        timeout_hours_data                : 6,
        use_linuxclient                   : true,
        use_super_bundles                 : false,
        skip_icepick_settings_file        : true,
        move_location_parallel            : true,
        new_locations                     : [
            earo: [
                elipy_call_new_location: project.elipy_call_earo + ' --use-fbenv-core',
            ],
        ],
    ]
    static Map preflight_settings = [
        concurrent_code            : 10,  // we have 10 lanes for code preflight
        use_icepick_test           : true,
        pre_preflight              : false,
        statebuild_codepreflight   : false,
        slack_channel_preflight    : [channels: ['#cobra-build-preflight']],
        codepreflight_reference_job: 'kin-dev-unverified.code.lastknowngood',
        datapreflight_reference_job: 'kin-dev-unverified.data.lastknowngood',
        extra_postpreflight_args   : "--framework-args -D:ondemandp4proxymapfile=${general_settings.workspace_root}\\tnt\\build\\framework\\data\\P4ProtocolOptimalProxyMap.xml", // for postpreflight use
        extra_codepreflight_args   : "--framework-args -D:ondemandp4proxymapfile=${general_settings.workspace_root}\\tnt\\build\\framework\\data\\P4ProtocolOptimalProxyMap.xml",
        p4_code_server             : 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
        p4_code_creds              : 'dice-p4buildedge03-fb',
    ]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'win64game', configs: ['final', 'release', 'retail', 'performance']],
        [name: 'ps4', configs: ['final', 'release', 'retail', 'performance']],
        [name: 'xb1', configs: ['final', 'release', 'retail', 'performance']],
        [name: 'win64server', configs: ['final', 'release']],
        [name: 'linux64server', configs: ['final', 'release']],
        [name: 'tool', configs: [[name: 'release', compile_unit_tests: true, run_unit_tests: true],]],
        [name: 'ps5', configs: ['final', 'release', 'retail', 'performance']],
        [name: 'xbsx', configs: ['final', 'release', 'retail', 'performance']],
        [name: 'xb1GDK', configs: [[name: 'final', allow_failure: true],
                                   [name: 'release', allow_failure: true],
                                   [name: 'retail', allow_failure: true]]],
    ]
    static List bilbo_move_matrix = [
        [name: 'win64game', configs: ['final', 'release', 'performance']],
        [name: 'tool', configs: [[name: 'release']]],
        [name: 'win64server', configs: ['final', 'release']],
        [name: 'xbsx', configs: ['final', 'release', 'performance']],
        [name: 'ps5', configs: ['final', 'release', 'performance']],
        [name: 'linux64server', configs: ['final', 'release']],
        [name: 'ps4', configs: ['final', 'release', 'performance']],
        [name: 'xb1', configs: ['final', 'release', 'performance']],
        [name: 'xb1GDK', configs: ['final', 'release']],
    ]
    static List code_nomaster_matrix = [
        [name: 'win64game', configs: ['retail']],
        [name: 'ps4', configs: ['debug']],
        [name: 'xb1', configs: ['final']],
        [name: 'ps5', configs: ['final']],
        [name: 'xbsx', configs: ['release']],
        [name: 'linux64server', configs: ['final']],
        [name: 'tool', configs: ['release', 'debug']],
    ]
    static List code_downstream_matrix = [
        [name: '.code.p4counterupdater', args: ['code_changelist', 'code_countername']],
        [name: '.code.lastknowngood', args: ['code_changelist']],
        [name: '.data.start', args: []],
    ]
    static List data_matrix = [
        'win64',
        'ps4',
        'xb1',
        'server',
        'ps5',
        'xbsx',
    ]
    static List data_downstream_matrix = [
        [name: '.frosty.start', args: []],
        [name: '.data.lastknowngood', args: ['code_changelist', 'data_changelist']],
        [name: '.data.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.data.p4cleancounterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.code.tool.release.copy-build-to-azure', args: ['code_changelist']],
    ]
    static List patchdata_matrix = []
    static List frosty_matrix = [
        [name: 'win64', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release --additional-configs retail'],
                                   [format: 'digital', config: 'final', region: 'ww', args: ''],
                                   [format: 'digital', config: 'retail', region: 'ww', args: ''],
                                   [format: 'files', config: 'performance', region: 'ww', args: '']]],
        [name: 'ps4', variants: [[format: 'files', config: 'final', region: 'eu', args: ' --additional-configs release --additional-configs retail'],
                                 [format: 'digital', config: 'final', region: 'eu', args: ''],
                                 [format: 'digital', config: 'retail', region: 'eu', args: ''],
                                 [format: 'digital', config: 'final', region: 'na', args: ''],
                                 [format: 'digital', config: 'retail', region: 'na', args: ''],
                                 [format: 'files', config: 'performance', region: 'eu', args: '']]],
        [name: 'ps5', variants: [[format: 'files', config: 'final', region: 'dev', args: ' --additional-configs release --additional-configs retail'],
                                 [format: 'digital', config: 'final', region: 'dev', args: ''],
                                 [format: 'digital', config: 'retail', region: 'dev', args: ''],
                                 [format: 'files', config: 'performance', region: 'eu', args: '']]],
        [name: 'xb1', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release --additional-configs retail'],
                                 [format: 'digital', config: 'final', region: 'ww', args: ''],
                                 [format: 'digital', config: 'retail', region: 'ww', args: ''],
                                 [format: 'files', config: 'performance', region: 'ww', args: '']]],
        [name: 'xbsx', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release --additional-configs retail'],
                                  [format: 'digital', config: 'final', region: 'ww', args: ''],
                                  [format: 'digital', config: 'retail', region: 'ww', args: ''],
                                  [format: 'files', config: 'performance', region: 'ww', args: '']]],
        [name: 'server', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release'],
                                    [format: 'digital', config: 'final', region: 'ww', args: '']]],
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww', args: ''],
                                         [format: 'files', config: 'final', region: 'ww', args: '']]],
    ]
    static List frosty_downstream_matrix = [
        [name: '.frosty.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
    ]
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = [
        [name: '.spin.linuxserver.digital.final.ww', args: ['code_changelist', 'data_changelist']],
    ]
    static List code_preflight_matrix = [
        [name: 'win64game', configs: ['retail']],
        [name: 'ps4', configs: ['release']],
        [name: 'xb1', configs: ['final']],
        [name: 'xbsx', configs: ['retail']],
        [name: 'ps5', configs: ['retail']],
        [name: 'win64server', configs: ['final']],
        [name: 'linux64server', configs: ['release']],
        [name: 'tool', configs: ['release'], sync_code_and_data: true],
    ]
    static List data_preflight_matrix = []
    static List spin_upload_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww']]],
    ]
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = [
        [platform: 'tool', content_type: ['code'], config: ['release']]
    ]
}
