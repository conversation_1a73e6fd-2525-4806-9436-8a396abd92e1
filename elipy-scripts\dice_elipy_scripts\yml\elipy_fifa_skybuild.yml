# Mandatory
default:
  name: "fifa"
  script_path:
    - "TnT\\Bin\\Python\\virtual\\Lib\\site-packages\\dice_elipy_scripts"
    - "Python\\virtual\\Lib\\site-packages\\dice_elipy_scripts"
  studio_location: "fifa"
  project_name: "fifa"
  avalanche_symbol_server: ""
  symbol_stores_share: ""
  symbol_stores_suffix: ""
  vault_symstore: "false"
  vault_destination: ""
  vault_verification_config_path: ""
  md5_exf_path: ""
  ant_local_dir: ""

  p4_package_server: "ro-perforceproxy.eamobile.ad.ea.com:1680"

  metadata_manager:
    primary:
      name: "bilbo_v2"
      attributes_filename: "build.json"
    secondary:
      - name: "bilbo"
        attributes_filename: "bilbo_v1_build.json"

  gamescripts_script_path: "Code\\FIFAGame\\fbcli\\buildgamescripts.py"
  skip_increment_client_version: true

  bilbo_api_version: 2
  bilbo_url: ""
  build_share: "\\\\eamobile.ad.ea.com\\sports\\DREBUILDS\\fc\\devg5\\skybuild"
  shift_tool_url: "https://artifacts.ea.com/artifactory/list/dre-generic-federated/cobra/shiftsubmission/5.1.0"

  metrics_url: "https://dre-metrics-eck.cobra.dre.ea.com"
  metrics_port: 80

  jenkins_metrics_url: "https://criterion-metrics-eck.cobra.dre.ea.com"
  jenkins_metrics_port: 443

  game_binaries: ['FIFATrial.exe', 'FIFA.exe']

  # ps4_disk_code_branch: ShippingPS4
  # ps4_disk_code_changelist: 970080
  # ps4_disk_data_branch: ShippingPS4
  # ps4_disk_data_changelist: 970080

  elsa_patch: "false"
  use_onefs_api: "false"
  skip_frosty_game_config_flags: "true"
  shift_config_file: "shift_config_exc.yml"
  shift_retention: 100
  release_candidate_retention: 56 # max_config(56) * number of release candidates
  shift_submission_path: "\\\\eamobile.ad.ea.com\\sports\\DREBUILDS\\fc\\devg5\\skybuild\\Shift\\submit"
  tasks_to_kill:
    - 'AvalancheCLI.exe'
    - 'FrostyIsoTool.exe'
    - 'msbuild.exe'
    - 'nant.exe'
    - 'devenv.exe'
    - 'Tool.Pipeline_Win64_release_Dll.exe'
    - 'mspdbsrv.exe'
    - 'vctip.exe'
    - 'snowcacheserver.exe'
    - 'cl.exe'
    - 'animationapp.exe'
    - 'prospero-clang.exe'
    - 'Icepick.exe'
    - 'eapm.exe'

  secrets:
    # Get Roboto server connection keys for bundling into Frosty server builds
    - where:
        build_type: frosty
        platform: server
      url: 'https://ess.ea.com'
      namespace: 'cds-dre-prod'
      role_id: '21b402e7-62a0-c293-5381-df0ab93085f9' # Role ID name is elipy-certrenderer
      secret_id_envvar: 'VAULT_ONLINE_EXC_PROD_SECRET_ID' # Matches a Jenkins Credential
      files:
        - path: '/cobra/automation/projects/excalibur/certificates'
          key: 'NFS22_GAMESERVER_SERVER.client.int.eadp.ea.com.key'
          to: '{GAME_DATA_DIR}\Config\Frosty\Server\Cert\NFS22_GAMESERVER_SERVER.client.int.eadp.ea.com.key'

  path_retention:
    - \\eamobile.ad.ea.com\sports\DREBUILDS\fc\devg5\skybuild\Shift\submit: 40
