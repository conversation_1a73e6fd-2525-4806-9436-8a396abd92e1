/**
 * HasRecentSuccessfulJob.groovy
 * Checks if there are any recent jobs on a certain machine, used for some sync jobs.
 */
boolean call(String node_name, String job_name, int max_recent_time) {
    def job = Jenkins.get().getItem(job_name)
    return job.builds.any { build ->
        if (node_name == build.builtOnStr) {
            if (build.inProgress) {
                return true
            }
            build_end_time = build.startTimeInMillis + build.duration
            time_since_build = new Date().time - build_end_time
            if (time_since_build < max_recent_time && build.result == Result.SUCCESS) {
                echo 'Node: ' + node_name + ', found recent ' + job_name + ' run ' + time_since_build + ' milliseconds ago.'
                return true
            }
        }
        return false
    }
}
