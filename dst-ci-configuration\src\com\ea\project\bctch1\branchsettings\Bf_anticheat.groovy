package com.ea.project.bctch1.branchsettings

import com.ea.project.bctch1.BctCh1

class Bf_anticheat {
    // Settings for jobs
    static Class project = BctCh1
    static Map general_settings = [
        dataset              : project.dataset,
        elipy_call           : project.elipy_call_eala + ' --use-fbenv-core',
        elipy_install_call   : project.elipy_install_call,
        frostbite_licensee   : project.frostbite_licensee,
        workspace_root       : project.workspace_root,
        job_label_statebuild : 'statebuild_eala',
        poolbuild_label      : 'poolbuild_eala',
        p4_code_creds        : 'bct-la-p4',
        p4_data_creds        : 'bct-la-p4',
        p4_code_server       : 'dicela-p4edge-fb.la.ad.ea.com:2001',
        p4_data_server       : 'dicela-p4edge-fb.la.ad.ea.com:2001',
        webexport_script_path: 'Code\\DICE\\BattlefieldGame\\fbcli\\webexport.py',
    ]
    static Map code_settings = [
        skip_code_build_if_no_changes: false,
        slack_channel_code           : [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        statebuild_code              : true,
    ]
    static Map data_settings = [
        data_reference_job     : 'bf-anticheat.code.start',
        extra_data_args        : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true --pipeline-args -Pipeline.UpdateAssetIndeterminismIsError --pipeline-args false '],
        poolbuild_data         : true,
        statebuild_data        : true,
        timeout_hours_data     : 8,
        timeout_hours_webexport: 8,
        webexport_branch       : true,
        webexport_allow_failure: true,
    ]
    static Map frosty_settings = [
        extra_frosty_args   : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        frosty_reference_job: 'bf-anticheat.data.start',
        poolbuild_frosty    : true,
        skip_frosty_trigger : true,
        slack_channel_frosty: [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        statebuild_frosty   : true,
        timeout_hours_frosty: 5,
        use_linuxclient     : true,
    ]
    static Map standard_jobs_settings = code_settings + data_settings + frosty_settings + [
        asset                    : 'ShippingLevels',
        enable_eac               : true,
        enable_lkg_p4_counters   : true,
        server_asset             : 'ShippingLevels',
        shift_branch             : true,
        shift_every_build        : false,
        shift_reference_job      : 'bf-anticheat.frosty.start',
        use_super_bundles        : true,
        trigger_string_code      : 'TZ=Europe/Stockholm\nH 3 * * 1-6\nH 6 * * 7',
        trigger_type_code        : 'scm',
        trigger_type_data        : 'none',
        enable_perfmarkers_retail: true,
    ]
    static Map icepick_settings = [:]
    static Map preflight_settings = [:]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'linux64server', configs: ['final']],
        [name: 'tool', configs: ['release']],
        [name: 'win64game', configs: ['final', 'retail']],
        [name: 'win64server', configs: ['final']],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = [
        [name: '.data.start', args: []],
    ]
    static List data_matrix = [
        [name: 'server'],
        [name: 'win64'],
    ]
    static List data_downstream_matrix = [
        [name: '.frosty.start', args: []],
    ]
    static List patchdata_matrix = []
    static List frosty_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww', args: '']]],
        [name: 'server', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'win64', variants: [[format: 'digital', config: 'final', region: 'ww', args: ''],
                                   [format: 'digital', config: 'retail', region: 'ww', args: '']]],
    ]
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = []
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
