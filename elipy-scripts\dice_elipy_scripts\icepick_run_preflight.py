# pylint: disable=line-too-long
"""
icepick_run_preflight.py

"""
# pylint: enable=line-too-long
import os
import click
import time
from dice_elipy_scripts.utils import frosty_build_utils
from dice_elipy_scripts.utils.licensee_helper import set_licensee
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from dice_elipy_scripts.utils.icepick_utils import (
    icepick_clean,
    save_icepick_logs,
)
from dice_elipy_scripts.utils.preflight_utils import (
    raise_if_cl_not_exists,
    raise_if_wrong_stream,
)
from elipy2 import (
    filer,
    frostbite_core,
    LOGGER,
)
from elipy2 import running_processes, p4, data
from elipy2.cli import pass_context
from elipy2.frostbite import icepick
from elipy2.telemetry import collect_metrics


# pylint: disable=too-many-locals
@click.command(
    "icepick_run_preflight",
    short_help="Run icepick test after preflight run.",
    context_settings=dict(ignore_unknown_options=True),
)
@click.argument("p4_port")
@click.argument("p4_client")
@click.argument("platform", required=True)
@click.argument("run-args", nargs=-1, type=click.UNPROCESSED)
@click.option(
    "--test-suites",
    "--ts",
    help="Space-separated list of test suite(s) to run.",
    required=True,
)
@click.option(
    "--settings-files",
    "--sf",
    help="Settings files relative to the data folder.",
    multiple=True,
)
@click.option(
    "--cook-assets",
    "--ca",
    default=False,
    type=bool,
    help="Cook required assets before running the test suite.",
)
@click.option(
    "--code-branch", "--cb", default=None, required=True, help="Branch to fetch code from."
)
@click.option(
    "--code-changelist",
    "--cc",
    default=None,
    required=True,
    help="Which code changelist to use.",
)
@click.option(
    "--custom-test-suite-data",
    "--ctsd",
    default="",
    help="Custom test suite metadata to pass to Icepick.",
)
@click.option(
    "--pending-changelist",
    "--pc",
    default=None,
    help="Which data changelist to unshelve.",
    required=True,
)
@click.option(
    "--data-changelist",
    "--dc",
    default=None,
    help="Which data changelist to use.",
    required=True,
)
@click.option(
    "--frosting-report",
    "--fr",
    default=True,
    help="Toggle to send frosting report.",
)
@click.option(
    "--build-type",
    "--bt",
    default="static",
    type=click.Choice(["static", "dll", None]),
    help="Static",
)
@click.option("--autobuild", "--ab", default=False, help="Autobuild")
@click.option("--config", "-c", default="final", help="Config")
@click.option(
    "--server-config",
    "--sc",
    help="Server config (default is config value).",
    default="final",
)
@click.option(
    "--server-assets",
    "--sas",
    help="what server asset to build if needed.",
    multiple=True,
    default=["preflightlevels"],
)
@click.option("--licensee", "-l", multiple=True, default=None, help="Licensee to use")
@click.option(
    "--password",
    "-p",
    default=None,
    help="User credentials to authenticate to package server",
)
@click.option("--email", "-e", default=None, help="User email to authenticate to package server")
@click.option("--user", default=None, help="Perforce user name.")
@click.option(
    "--domain-user",
    "--du",
    default=None,
    help="The user to authenticate to package server as DOMAIN\\user",
)
@click.option(
    "--test-group",
    "--tg",
    default="preflight_skybuild",
    help="Set test group name as part of metadata.",
)
@click.option("--inert-run", "--ir", default=False, help="Run but don't do anything")
@click.option(
    "--extra-framework-args",
    "--efa",
    default=None,
    help="Extra arguments for Icepick to pass to any Framework commands it starts",
)
@click.option(
    "--server-platform",
    "--sp",
    default="server",
    help="Which server platform to use, can be either server (windows) or linuxserver.",
)
@click.option(
    "--custom-tag",
    "--cut",
    default=None,
    help="Extra folder before changelist to fetch code from.",
)
@click.option(
    "--target-build-share",
    "--tbs",
    help="Elipy config key to find buildshare in alternate_build_shares.",
    default=None,
)
@click.option(
    "--use-local-codebuilds",
    "--ulcb",
    default=False,
    help="Use builds that are already in the machine or fetch them from the filer",
)
@click.option(
    "--keep-local-bin",
    "--klb",
    default=False,
    help="Skip deleting local/bin folder before icepick run",
)
@click.option(
    "--skip-unshelve",
    "--sun",
    default=False,
    help="Skip any p4 unshelve in case it was executed already",
)
@click.option(
    "--skip-revert",
    "--sre",
    default=False,
    help="Skip any p4 revert in case those are not necessary for the build",
)
@click.option(
    "--do-warmup/--not-warmup",
    default=False,
    help="--do-warmup to warm up AWS agent; --not-warmup/not set, normal preflight.",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    p4_port,
    p4_client,
    platform,
    run_args,
    test_suites,
    settings_files,
    cook_assets,
    code_branch,
    code_changelist,
    custom_test_suite_data,
    pending_changelist,
    data_changelist,
    frosting_report,
    build_type,
    autobuild,
    config,
    server_config,
    server_assets,
    licensee,
    password,
    email,
    user,
    domain_user,
    test_group,
    inert_run,
    extra_framework_args,
    server_platform,
    custom_tag,
    target_build_share,
    use_local_codebuilds,
    keep_local_bin,
    skip_unshelve,
    skip_revert,
    do_warmup,
):
    """
    Run icepick test after a preflight.
    """

    if inert_run:
        LOGGER.info("Inert run, sleep for 3 seconds and exit...")
        time.sleep(3)
        return

    icepick_run_args = list(run_args)
    server_assets = list(server_assets)
    test_suite_names = test_suites.split()

    # adding sentry tags
    add_sentry_tags(__file__, "autotest")

    running_processes.kill()

    # ensure the licensee is set
    if licensee:
        frostbite_licensee = list(licensee)
    else:
        core_licensee_id = frostbite_core.get_licensee_id()
        frostbite_licensee = [core_licensee_id]

    set_licensee(frostbite_licensee, list())

    # Unshelve pending changelist
    perforce = p4.P4Utils(port=p4_port, user=user, client=p4_client)
    if not skip_revert:
        perforce.revert()

    if not do_warmup:
        raise_if_cl_not_exists(perforce, pending_changelist)
        raise_if_wrong_stream(perforce, pending_changelist)

    try:
        if not do_warmup and not skip_unshelve:
            perforce.unshelve(pending_changelist)

        _filer = filer.FilerUtils()

        icepicker = icepick.IcepickUtils()
        # clean machine ready for icepick
        icepick_clean(icepicker, keep_local_bin=keep_local_bin)

        if password is not None and email is not None:
            frosty_build_utils.authenticate_eapm_credstore(
                user=email, password=password, domain_user=domain_user
            )

        icepick_settings_files_list = list(settings_files)
        icepick_settings_files_list = (
            icepick.IcepickUtils.settings_files_relative_to_absolute_paths(
                icepick_settings_files_list
            )
        )

        LOGGER.info("Using Icepick settings files: {}".format("".join(icepick_settings_files_list)))

        frosty_build_utils.install_required_sdks(
            password=password,
            user=email,
            domain_user=domain_user,
            platform=platform.lower(),
        )

        # Fetch game binaries.
        code_platform = platform
        if platform.lower() == "win64":
            code_platform = "win64game"

        if build_type == "dll":
            code_platform = "win64-dll"

        LOGGER.info("build type is {}, using {} platform".format(build_type, code_platform))

        if not use_local_codebuilds:
            LOGGER.info("Fetching necessary builds from filer")
            _filer.fetch_code(
                code_branch,
                code_changelist,
                code_platform,
                config,
                mirror=False,
                custom_tag=custom_tag,
                target_build_share=target_build_share,
                use_bilbo=False,
            )

            # Fetch game server binaries and cook if needed
            _filer.fetch_code(
                code_branch,
                code_changelist,
                server_platform,
                server_config,
                mirror=False,
                custom_tag=custom_tag,
                target_build_share=target_build_share,
                use_bilbo=False,
            )
        else:
            LOGGER.info("Using local builds")

        pipeline_args = data.DataUtils.get_clean_master_version_args()

        if cook_assets:
            icepicker.run_icepick_cook(
                platform,
                test_suite_names,
                config,
                pipeline_args,
                ignore_icepick_exit_code=False,
            )

        for test_suite in test_suite_names:
            LOGGER.info("Running test suite: {}".format(test_suite))

            if test_suite.endswith(".xml"):
                test_suite = os.path.join(frostbite_core.get_game_data_dir(), test_suite)

            test_suite_run_args = icepick_run_args.copy()
            LOGGER.info("-- test_suite_run_args --")
            for arg in test_suite_run_args:
                LOGGER.info(arg)
            LOGGER.info("---!---")

            test_suite_data = (
                f"code_changelist:{code_changelist};" f"data_changelist:{data_changelist};"
            )

            if custom_test_suite_data:
                test_suite_data += f"{custom_test_suite_data};"

            icepicker.run_icepick(
                platform=platform,
                test_suite=test_suite,
                test_group=test_group,
                config=config,
                settings_file_list=icepick_settings_files_list,
                send_frosting_report=frosting_report,
                lease=None,
                build_type=build_type,
                autobuild=autobuild,
                run_args=test_suite_run_args,
                ignore_icepick_exit_code=False,
                cook=False,
                extra_framework_args=extra_framework_args,
                custom_test_suite_data=test_suite_data,
            )

    finally:
        if not skip_revert:
            perforce.revert()
        save_icepick_logs()
