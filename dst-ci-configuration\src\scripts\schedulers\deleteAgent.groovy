package scripts.schedulers

import com.cloudbees.groovy.cps.NonCPS

/**
 * deleteAgent.groovy
 * This job is to loop over agents in the current cluster and check when
 * it is offline with reason 'ReadyToBeDeleted' then disconnect it from master
 * This job can be manually triggered or based on a cron timmer
 */

stage('Disconnect Node') {
    node('master') {
        doGetNodeList()
    }
}

@NonCPS
void doGetNodeList() {
    def agentNameList = ''
    for (agent in Jenkins.get().nodes) {
        def computer = agent.computer
        def agentName = agent.computer.name
        if ((computer.offline) && (computer.offlineCauseReason.contains('[Taint] Tainted'))) {
            echo "we need to disconnect this node ${agentName} from master"
            if (Jenkins.get().getComputer("${agentName}").idle) {
                Jenkins.get().getComputer("${agentName}").doDoDelete()
                echo "${agentName} is disconnected/deleted"
                agentNameList += agentName + ' '
            } else {
                ehco "${agentName} is not idle, wait for next run"
            }
        } else {
            echo "${agentName} is ${computer.offline} offline with reason ${computer.offlineCauseReason}"
        }
    }
    currentBuild.displayName = "${agentNameList}"
}
