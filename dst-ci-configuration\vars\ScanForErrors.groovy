import com.sonyericsson.jenkins.plugins.bfa.BuildFailureScanner
import org.jenkinsci.plugins.workflow.cps.CpsThreadGroup

/**
 * ScanForErrors.groovy
 * Scans the build for errors, so we can use this information already before the build has finished completely.
 */
void call(def currentBuild, def scan_this_build = false) {
    if (scan_this_build.toBoolean() == true) {
        BuildFailureScanner.scanIfNotScanned(currentBuild.rawBuild, CpsThreadGroup.current().execution.owner.listener.logger)
        currentBuild.rawBuild.save()
    }
}
