{#
    Command:
        pipeline_determinism_test
            short_help: Runs pipeline_determinism_test script.
            context_settings: dict(ignore_unknown_options=True)

    Arguments:

    Required variables:
        code_branch
            type: str
            required: True
            help: Code branch to fetch tool binaries.
        code_changelist
            type: str
            required: True
            help: Code changelist to fetch tool binaries.
        p4_port
            type: str
            required: True
            help: P4 PORT for pipeline determinism test script

    Optional variables:
        script_args
            type: str
            default: ''
            help: Optional arguments to pass to the command.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    pipeline_determinism_test
    --code-branch {{ code_branch }}
    --code-changelist {{ code_changelist }}
    --p4-port {{ p4_port }}
    {%- if script_args %}
    --script-args {{ script_args }}
    {%- endif %}
  displayName: elipy pipeline_determinism_test
