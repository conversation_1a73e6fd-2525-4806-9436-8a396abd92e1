package com.ea.project.bct.branchsettings

import com.ea.project.bct.Bct

class Trunk_content_dev_test {
    // Settings for jobs
    static Class project = Bct
    static Map general_settings = [
        dataset                 : project.dataset,
        elipy_call              : project.elipy_call_criterion + ' --use-fbenv-core',
        elipy_install_call      : project.elipy_install_call,
        frostbite_licensee      : project.frostbite_licensee,
        workspace_root          : project.workspace_root,
        azure_elipy_call        : project.azure_elipy_call,
        azure_elipy_install_call: project.azure_elipy_install_call,
        azure_workspace_root    : project.azure_workspace_root,
        p4_code_creds           :'perforce-battlefield-criterion',
        p4_code_server          :'oh-p4edge-fb.eu.ad.ea.com:2001',
    ]
    static Map code_settings = [
        skip_code_build_if_no_changes: false,
        slack_channel_code           : [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        report_build_version         : ' --reporting-build-version-id %code_changelist%',
        poolbuild_data               : true,
        skip_frosty_trigger          : true,
        sndbs_enabled                : true,
    ]
    static Map data_settings = [:]
    static Map frosty_settings = [:]
    static Map standard_jobs_settings = code_settings + data_settings + frosty_settings + [
        server_asset              : 'Granite/Game/Levels/MP/MP_Coastal/MP_Coastal',
        asset                     : 'Granite/Game/Levels/MP/MP_Coastal/MP_Coastal',
        extra_data_args           : ['--disable-caches true --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        enable_lkg_p4_counters    : true,
        skip_icepick_settings_file: true,
        statebuild_code           : true,
        strip_symbols             : false,
        job_label_statebuild      : '24_core && criterion',
        poolbuild_label           : '24_core && criterion',
        poolbuild_label_xbsx      : '18_core && criterion',
        poolbuild_label_ps5       : '12_core && criterion',
        trigger_type_code         : 'non',
        trigger_type_data         : 'none',
        timeout_hours_data        : 24,
    ]
    static Map icepick_settings = [
        ignore_icepick_exit_code: false
    ]
    static Map preflight_settings = [:]

    static List code_matrix = [
        [name: 'tool', configs: ['release', [name: 'steam', allow_failure: true]]],
        [name: 'win64game', configs: ['final', [name: 'steam', allow_failure: true]]],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = []

    static List data_matrix = [
        [name: 'win64'],
        [name: 'ps5'],
        [name: 'xbsx'],
    ]
    static List data_downstream_matrix = []
    static List patchdata_matrix = []
    static List frosty_matrix = []
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = []
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
