"""
test_offsitebuild.py

Unit testing for offsitebuild
"""
import unittest
import os
import json
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import call, MagicMock, mock_open, patch
from dice_elipy_scripts.offsitebuild import cli, offsitebuild
from dice_elipy_scripts.offsitebuild import (
    update_eacopy_file_list,
    get_basic_drone_source,
    get_full_drone_source,
    is_qa_qualify,
    add_fingerprint,
)
from elipy2 import LOGGER
from elipy2.bilbo import Build
from elipy2.exceptions import CoreException


@patch("os.path.join", MagicMock(side_effect=lambda *args: "\\".join(args)))
@patch("elipy2.telemetry.collect_metrics", MagicMock())
@patch("elipy2.telemetry.upload_metrics", MagicMock())
@patch("dice_elipy_scripts.offsitebuild.add_sentry_tags", MagicMock())
class TestOffsiteBuildsCli(unittest.TestCase):
    OPTION_CODE_CHANGELIST = "--code-changelist"
    OPTION_CODE_BRANCH = "--code-branch"
    OPTION_DESTINATION = "--destination"
    OPTION_BASIC_DRONE_BUILD = "--basic-drone-build"
    OPTION_BASIC_DRONE_ZIP = "--basic-drone-zip"
    OPTION_QA_VERIFIED_BUILDS_ONLY = "--qa-verified-builds-only"
    OPTION_FORCE_UPDATE_ZIP = "--force-update-zip"
    OPTION_OUTSOURCER = "--outsourcer"

    VALUE_CODE_CHANGELIST = "1234"
    VALUE_CODE_BRANCH = "code_branch"
    VALUE_DESTINATION = "dest"
    VALUE_OUTSOURCER_1 = "outsourcer_1"
    VALUE_OUTSOURCER_2 = "outsourcer_2"

    BASIC_ARGS = [
        OPTION_CODE_CHANGELIST,
        VALUE_CODE_CHANGELIST,
        OPTION_CODE_BRANCH,
        VALUE_CODE_BRANCH,
    ]

    def setUp(self):
        self.patcher_path = patch("pathlib.Path")
        self.mock_path = self.patcher_path.start()
        self.mock_path.return_value = MagicMock()

        self.patcher_create_zip = patch("elipy2.core.create_zip")
        self.mock_create_zip = self.patcher_create_zip.start()

        self.patcher_robocopy = patch("elipy2.core.robocopy")
        self.mock_robocopy = self.patcher_robocopy.start()

        self.patcher_get_build_share_path = patch("elipy2.filer_paths.get_build_share_path")
        self.mock_get_build_share_path = self.patcher_get_build_share_path.start()
        self.mock_get_build_share_path.return_value = "\\build_share"

        self.patcher_get_code_build_root_path = patch("elipy2.filer_paths.get_code_build_root_path")
        self.mock_get_code_build_root_path = self.patcher_get_code_build_root_path.start()
        self.mock_get_code_build_root_path.return_value = "\\\\build_share\\code\\code_branch\\1234"

        self.patcher_get_offsite_build = patch("elipy2.filer_paths.get_offsite_build")
        self.mock_get_offsite_build = self.patcher_get_offsite_build.start()
        self.mock_get_offsite_build.return_value = "\\\\build_share\\offsite\\code_branch\\1234.zip"

        self.patcher_get_offsite_basic_build = patch("elipy2.filer_paths.get_offsite_basic_build")
        self.mock_get_offsite_basic_build = self.patcher_get_offsite_basic_build.start()
        self.mock_get_offsite_basic_build.return_value = (
            "\\\\build_share\\offsite_basic\\code_branch\\1234.zip"
        )

        self.patcher_get_offsite_basic_drone_build = patch(
            "elipy2.filer_paths.get_offsite_basic_drone_build"
        )
        self.mock_get_offsite_basic_drone_build = self.patcher_get_offsite_basic_drone_build.start()
        self.mock_get_offsite_basic_drone_build.return_value = (
            "\\\\build_share\\offsite_basic_done\\code_branch\\1234\\1234.zip"
        )

        self.patcher_get_outsourcer_build = patch("elipy2.filer_paths.get_outsourcer_build")
        self.mock_get_outsourcer_build = self.patcher_get_outsourcer_build.start()
        self.mock_get_outsourcer_build.return_value = (
            "\\\\build_share\\outsourcer_1\\code_branch\\1234"
        )

    def tearDown(self):
        patch.stopall()

    @patch("os.path.exists", MagicMock(return_value=False))
    @patch("dice_elipy_scripts.offsitebuild.update_eacopy_file_list")
    @patch("dice_elipy_scripts.offsitebuild.get_full_drone_source")
    @patch(
        "dice_elipy_scripts.offsitebuild.Path", MagicMock(parents=["short_path", "shorter_path"])
    )
    def test_basic_args(self, mock_get_full_drone_source, mock_update_file_list):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        assert mock_update_file_list.call_count == 0
        assert mock_get_full_drone_source.call_count == 1

    @patch("os.path.exists", MagicMock(return_value=False))
    @patch("dice_elipy_scripts.offsitebuild.update_eacopy_file_list")
    @patch("dice_elipy_scripts.offsitebuild.get_basic_drone_source")
    @patch(
        "dice_elipy_scripts.offsitebuild.Path", MagicMock(parents=["short_path", "shorter_path"])
    )
    def test_basic_drone_build(self, mock_drone_source, mock_update_file_list):
        mock_drone_source.return_value = "temp_dir"
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_BASIC_DRONE_BUILD])
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        mock_update_file_list.assert_called_once_with("temp_dir")
        assert mock_drone_source.call_count == 1

    @patch("os.path.exists", MagicMock(return_value=False))
    @patch("dice_elipy_scripts.offsitebuild.update_eacopy_file_list")
    @patch("dice_elipy_scripts.offsitebuild.get_basic_drone_source")
    @patch(
        "dice_elipy_scripts.offsitebuild.Path", MagicMock(parents=["short_path", "shorter_path"])
    )
    def test_basic_drone_zip(self, mock_drone_source, mock_update_file_list):
        mock_drone_source.return_value = "temp_dir"
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_BASIC_DRONE_ZIP])
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        mock_update_file_list.assert_called_once_with("temp_dir")
        assert mock_drone_source.call_count == 1

    @patch("os.path.exists", MagicMock(return_value=False))
    @patch("dice_elipy_scripts.offsitebuild.update_eacopy_file_list", MagicMock())
    @patch(
        "dice_elipy_scripts.offsitebuild.get_basic_drone_source", MagicMock(return_value="temp_dir")
    )
    @patch(
        "dice_elipy_scripts.offsitebuild.Path", MagicMock(parents=["short_path", "shorter_path"])
    )
    def test_create_zip_called(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_BASIC_DRONE_ZIP])
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        assert self.mock_create_zip.call_count == 1

    @patch("os.path.exists", MagicMock(return_value=False))
    @patch("dice_elipy_scripts.offsitebuild.update_eacopy_file_list", MagicMock())
    @patch(
        "dice_elipy_scripts.offsitebuild.get_basic_drone_source", MagicMock(return_value="temp_dir")
    )
    @patch(
        "dice_elipy_scripts.offsitebuild.Path", MagicMock(parents=["short_path", "shorter_path"])
    )
    @patch("elipy2.core.delete_folder")
    def test_create_zip_failure(self, mock_delete_folder):
        self.mock_create_zip.side_effect = CoreException()
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_BASIC_DRONE_ZIP])
        assert "Usage:" not in result.output
        assert result.exit_code == 1
        assert self.mock_create_zip.call_count == 1
        assert mock_delete_folder.call_count == 2
        mock_delete_folder.assert_has_calls(
            [
                call("\\\\build_share\\offsite_basic_done\\code_branch\\1234\\1234.zip.tmp"),
                call("\\\\build_share\\offsite_basic_done\\code_branch\\1234\\1234.zip.tmp1"),
            ]
        )

    @patch("elipy2.local_paths.get_platform_path")
    @patch("elipy2.code.CodeUtils.remove_files_for_eacopy")
    def test_update_eacopy_file_list(self, mock_remove_files, mock_local_path):
        mock_local_path.return_value = "win64-dll"
        update_eacopy_file_list("path")
        mock_remove_files.assert_called_once_with(
            platform="tool",
            config="release",
            exclude_pattern=".pdb",
            file_path=os.path.join("path", "win64-dll", "release"),
        )

    @patch("dice_elipy_scripts.offsitebuild.add_fingerprint")
    @patch("os.makedirs", MagicMock())
    @patch("elipy2.local_paths.get_platform_path")
    def test_get_basic_drone_source(self, mock_get_platform_path, mock_add_fingerprint):
        mock_get_platform_path.return_value = "platform_path"
        assert (
            get_basic_drone_source("code_root_path", "temp_dir", "fingerprint_path") == "temp_dir"
        )
        self.mock_robocopy.assert_has_calls(
            [
                call(
                    "code_root_path",
                    "temp_dir",
                    extra_args=["build.json"],
                    include_empty_dirs=False,
                    quiet=True,
                ),
                call(
                    "code_root_path\\platform_path",
                    "temp_dir\\platform_path",
                    extra_args=["/XF", "*.pdb", "*.map"],
                    quiet=True,
                ),
            ]
        )
        mock_add_fingerprint.assert_called_once_with("temp_dir\\fingerprint_path")

    @patch("dice_elipy_scripts.offsitebuild.add_fingerprint")
    def test_get_full_drone_source(self, mock_add_fingerprint):
        assert get_full_drone_source("code_root_path", "temp_dir", "fingerprint_path") == "temp_dir"
        self.mock_robocopy.assert_called_once_with("code_root_path", "temp_dir", quiet=True)
        mock_add_fingerprint.assert_called_once_with("temp_dir\\fingerprint_path")

    @patch("os.path.exists", MagicMock(return_value=False))
    @patch("dice_elipy_scripts.offsitebuild.update_eacopy_file_list")
    @patch("dice_elipy_scripts.offsitebuild.get_basic_drone_source")
    def test_outsourcers(self, mock_drone_source, mock_update_file_list):
        mock_drone_source.return_value = "temp_dir"
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_OUTSOURCER, self.VALUE_OUTSOURCER_1]
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        mock_update_file_list.assert_called_once_with("temp_dir")
        self.mock_robocopy.assert_called_once_with(
            "temp_dir",
            "\\\\build_share\\outsourcer_1\\code_branch\\1234",
            quiet=True,
        )

    @patch("os.path.exists", MagicMock(return_value=False))
    @patch("dice_elipy_scripts.offsitebuild.update_eacopy_file_list")
    @patch("dice_elipy_scripts.offsitebuild.get_basic_drone_source")
    def test_outsourcers_multiple(self, mock_drone_source, mock_update_file_list):
        mock_drone_source.return_value = "temp_dir"
        self.mock_get_outsourcer_build.side_effect = [
            "\\\\build_share\\outsourcer_1\\code_branch\\1234",
            "\\\\build_share\\outsourcer_2\\code_branch\\1234",
        ]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_OUTSOURCER,
                self.VALUE_OUTSOURCER_1,
                self.OPTION_OUTSOURCER,
                self.VALUE_OUTSOURCER_2,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        mock_update_file_list.assert_called_once_with("temp_dir")
        self.mock_robocopy.assert_has_calls(
            [
                call("temp_dir", "\\\\build_share\\outsourcer_1\\code_branch\\1234", quiet=True),
                call("temp_dir", "\\\\build_share\\outsourcer_2\\code_branch\\1234", quiet=True),
            ]
        )

    @patch("dice_elipy_scripts.offsitebuild.offsitebuild")
    def test_offsitebuild_internal(self, mock_offsitebuild):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        mock_offsitebuild.assert_called_once_with(
            self.VALUE_CODE_CHANGELIST,
            self.VALUE_CODE_BRANCH,
            None,
            [],
            False,
            False,
            False,
            False,
        )

    @patch("dice_elipy_scripts.offsitebuild.offsitebuild")
    def test_offsitebuild_internal_outsourcers(self, mock_offsitebuild):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_OUTSOURCER,
                self.VALUE_OUTSOURCER_1,
                self.OPTION_OUTSOURCER,
                self.VALUE_OUTSOURCER_2,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        mock_offsitebuild.assert_called_once_with(
            self.VALUE_CODE_CHANGELIST,
            self.VALUE_CODE_BRANCH,
            None,
            [self.VALUE_OUTSOURCER_1, self.VALUE_OUTSOURCER_2],
            False,
            False,
            False,
            False,
        )

    @patch("elipy2.secrets.get_secrets")
    @patch("pickle.dumps")
    @patch("dice_elipy_scripts.offsitebuild.open", new_callable=mock_open())
    def test_add_fingerprint(self, mock_open_file, mock_pickle_dumps, mock_get_secrets):
        mock_get_secrets.return_value = {"ess_data": {"fingerprint": "fingerprint_string"}}
        mock_pickle_dumps.return_value = "pickle_return"
        add_fingerprint("fingerprint\\file.bin")
        mock_pickle_dumps.assert_called_once_with("fingerprint_string")
        mock_open_file.assert_called_once_with("fingerprint\\file.bin", "wb")
        mock_open_file.return_value.__enter__().write.assert_called_once_with("pickle_return")


class TestOffsiteBuildsWithMetadata:
    @staticmethod
    def prepare_bilbo_response(file_name):
        data_file = os.path.join(os.path.dirname(__file__), "data", file_name)
        with open(data_file) as bilbo_response:
            LOGGER.info("Reading mock response")
            to_return = []
            for hit in json.load(bilbo_response)["hits"]["hits"]:
                bld = Build()
                for key, val in iter(list(hit.items())):
                    bld.__dict__[key[:1].lower() + key[1:]] = val
                to_return.append(bld)
            return to_return

    def test_is_qa_qualify_verified(self, fixture_metadata_manager):
        fixture_metadata_manager.get_all_builds.return_value = self.prepare_bilbo_response(
            "bilbo_response_offsitebuild_verified.json"
        )
        assert is_qa_qualify("code_branch", "1234")

    def test_is_qa_qualify_not_verified(self, fixture_metadata_manager):
        fixture_metadata_manager.get_all_builds.return_value = self.prepare_bilbo_response(
            "bilbo_response_offsitebuild_not_verified.json"
        )
        assert not is_qa_qualify("code_branch", "5678")


class TestOffsiteBuildOffsiteBuild:
    @patch("elipy2.core.create_zip")
    @patch("os.path.exists")
    @patch("elipy2.core.robocopy", MagicMock())
    @patch(
        "dice_elipy_scripts.offsitebuild.Path", MagicMock(parents=["short_path", "shorter_path"])
    )
    @patch("dice_elipy_scripts.offsitebuild.add_sentry_tags", MagicMock())
    def test_destination_exists(self, mock_os_path_exists, mock_create_zip):
        # Hack to get around an issue with mocking throw_if_files_found
        mock_os_path_exists.return_value = True
        offsitebuild(
            TestOffsiteBuildsCli.VALUE_CODE_CHANGELIST,
            TestOffsiteBuildsCli.OPTION_CODE_BRANCH,
            None,
            [],
        )
        assert mock_create_zip.call_count == 0

    @patch("elipy2.core.create_zip")
    @patch("os.path.exists")
    @patch("elipy2.core.robocopy", MagicMock())
    @patch(
        "dice_elipy_scripts.offsitebuild.Path", MagicMock(parents=["short_path", "shorter_path"])
    )
    @patch("dice_elipy_scripts.offsitebuild.add_sentry_tags", MagicMock())
    def test_destination_exists_provided(self, mock_os_path_exists, mock_create_zip):
        # Hack to get around an issue with mocking throw_if_files_found
        mock_os_path_exists.return_value = True
        offsitebuild(
            TestOffsiteBuildsCli.VALUE_CODE_CHANGELIST,
            TestOffsiteBuildsCli.OPTION_CODE_BRANCH,
            "\\some\\dest\\path",
            [],
        )
        assert mock_create_zip.call_count == 0
