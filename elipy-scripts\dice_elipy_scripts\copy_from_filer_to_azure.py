"""
copy_from_filer_to_azure.py

Push filer contents to Azure storage.
"""
import os
import datetime
import time

import click

from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.azure_filer_utils import authenticate_filer
from dice_elipy_scripts.utils.licensee_helper import set_licensee
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import (
    LOGGER,
    filer_paths,
    local_paths,
    filer,
    core,
    build_metadata_utils,
)
from elipy2.cli import pass_context
from elipy2.exceptions import ELIPYException
from elipy2.telemetry import collect_metrics

SUPPORTED_PLATFORMS = ["ps4", "win64game", "win64server", "linux64server", "ps5", "xbsx", "tool"]
SUPPORTED_CONTENT_TYPES = ["code"]


# pylint: disable=logging-fstring-interpolation
@click.command("copy_from_filer_to_azure", help="Deploy content at source to destination")
@click.option(
    "--content-type",
    help="Content type to copy to Azure (e.g. 'code' for codebuild). Determines path logic.",
    default=None,
    type=click.Choice(SUPPORTED_CONTENT_TYPES),
)
@click.option("--source", default=None, help="Skip path logic and use this path as source")
@click.option(
    "--destination", default=None, help="Skip path logic and use this path as destination"
)
@click.option(
    "--platform",
    help="platform (used by path generation logic)",
    default=None,
    type=click.Choice(SUPPORTED_PLATFORMS + ["all"]),
)
@click.option(
    "--additional-tools-to-include",
    help="Additional tool(s) to pull from network share (pipeline and frosted are always pulled)",
    default=(),
    multiple=True,
    type=str,
)
@click.option("--config", help="Build config (used by path generation logic)", default=None)
@click.option("--code-branch", help="Branch/stream (used by path generation logic)", default=None)
@click.option(
    "--code-changelist", help="Code changelist(used by path generation logic)", default=None
)
@click.option("--elipy-config-location", help="Which elipy config to use", default=None)
@click.option(
    "--target-build-share",
    help="Elipy config key to find buildshare in alternate_build_shares.",
    default=None,
)
@click.option(
    "--secret-context", help="Elipy config secrets 'where' key for filer auth", default=None
)
@click.option("--force-overwrite", help="Overwrite existing contents at destination", default=False)
@click.option("--licensee", multiple=True, default=None, help="Licensee to use")
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    content_type,
    source,
    destination,
    platform,
    additional_tools_to_include,
    config,
    code_branch,
    code_changelist,
    elipy_config_location,
    target_build_share,
    secret_context,
    force_overwrite,
    licensee,
):
    """
    Push the contents of a given path to Azure storage.
    """
    add_sentry_tags(__file__)

    verify_content_type(content_type)

    _filer = filer.FilerUtils()

    set_licensee(list(licensee), list())

    if content_type == "code":
        source, destination = get_code_src_and_dest(
            _filer,
            source,
            destination,
            code_branch,
            code_changelist,
            platform,
            additional_tools_to_include,
            config,
            elipy_config_location,
            target_build_share,
        )
    elif not source and destination:
        LOGGER.info(
            f"Source and destination params required if content type not in {SUPPORTED_PLATFORMS}"
        )

    LOGGER.info(f"Using elipy_config_location {elipy_config_location}")

    _filer = authenticate_filer(
        _filer,
        secret_context,
        target_build_share=target_build_share,
        location=elipy_config_location,
    )

    verify_source(source)
    verify_destination(destination, force_overwrite=force_overwrite)

    core.robocopy(source, destination, purge=force_overwrite, extra_args=["/XF", "*.pdb"])

    register_build_in_bilbo(filer_paths.get_code_build_root_path(code_branch, code_changelist))


def register_build_in_bilbo(source):
    """
    Register azure upload in Bilbo
    """
    if core.use_bilbo():
        bilbo = build_metadata_utils.setup_metadata_manager()
        timestamp = datetime.datetime.fromtimestamp(time.time()).strftime("%Y-%m-%d %H:%M")
        bilbo.register_azure_build(source, timestamp)
        LOGGER.info("Build registered in Bilbo with timestamp {}".format(timestamp))


def verify_source(source):
    """
    Check for issues with the source path and throw an exception if any are found.
    """
    if not os.path.exists(source):
        raise ELIPYException(
            f"Source path '{source}' does not exist. Cannot copy from filer to Azure."
        )


def verify_destination(destination, force_overwrite=False):
    """
    Check for issues with the destination path and throw an exception if any are found.
    """
    if os.path.exists(destination) and not force_overwrite:
        raise ELIPYException(
            "Attempting to deploy to a path that already exists.\
            Possibly because a previous build succeeded in deploying before failing.\
            This can cause us to lose binaries and symbols and is not allowed."
        )
    if os.path.exists(destination) and force_overwrite:
        LOGGER.warning(
            "Destination path '{}' already exists.\
            Overwriting existing contents.".format(
                destination,
            )
        )


def verify_content_type(content_type):
    """
    Verify that the content type is supported.
    """
    if content_type not in SUPPORTED_CONTENT_TYPES:
        msg = f"Copying content type '{content_type}' from filer to Azure is not supported."
        LOGGER.error(msg)
        raise ELIPYException(msg)


def get_code_src_and_dest(
    _filer,
    source,
    destination,
    code_branch,
    code_changelist,
    platform,
    additional_tools_to_include,
    config,
    elipy_config_location,
    target_build_share,
):
    """
    Helper function to get path to the source and the path to the destination.
    This is tailored for used for codebuilds.
    """
    LOGGER.info("Generating source and destination paths for a code build")
    if not source:
        platforms = [platform]
        # Pipeline and frosted need fetched too for tool builds (https://go.ea.com/slack_link)
        if platform == "tool":
            platforms.extend(["pipeline", "frosted"])
            if additional_tools_to_include:
                platforms.extend(additional_tools_to_include)
        if additional_tools_to_include and platform != "tool":
            LOGGER.warning(
                "Additional tools to include are only supported for tool builds.\
                Ignoring additional tools to include."
            )
        for plt in platforms:
            _filer.fetch_code(
                code_branch,
                code_changelist,
                plt,
                config,
                mirror=False,
                exclude_pdb_files=True,
                use_bilbo=False,
            )

        source = local_paths.get_local_build_path(platform, config)

    if not destination:
        destination = filer_paths.get_code_build_platform_path(
            code_branch,
            code_changelist,
            platform,
            location=elipy_config_location,
            target_build_share=target_build_share,
        )

    if platform != "tool":
        destination = os.path.join(destination, config)

    LOGGER.info(f"Source path: {source}")
    LOGGER.info(f"Destination path: {destination}")

    return source, destination


# pylint: enable=logging-fstring-interpolation
