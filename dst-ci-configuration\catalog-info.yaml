apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: dst-ci-configuration
  description: |
    Repository for the Cobra platform JobDSL and the shared pipeline library `dst-lib`.

    This project provides Jenkins JobDSL scripts and shared Groovy code to automate CI/CD pipelines, autotests, and build processes for the Cobra platform. It emphasizes maintainability, testability (using Spock), and follows Jenkins Shared Library best practices.
  tags:
    - groovy
    - jenkins
    - pipelines
    - autotests
    - build
    - ci
    - cd
  annotations:
    backstage.io/techdocs-ref: dir:.
  links:
    - url: https://dashing.cobra.dre.ea.com/
      title: Dashing
      icon: dashboard
    - url: https://bct-autotest-jenkins.cobra.dre.ea.com/
      title: VM Pipeline Schedules for Daily and Weekly Updates and Creations
      icon: cicd
    - url: https://bct-ch1-autotest-jenkins.cobra.dre.ea.com/
      title: bct-ch1-autotest-jenkins.cobra.dre.ea.com
      icon: cicd
    - url: https://bct-ch1-dev-jenkins.cobra.dre.ea.com/
      title: bct-ch1-dev-jenkins.cobra.dre.ea.com
      icon: cicd
    - url: https://bct-ch1-rel-jenkins.cobra.dre.ea.com/
      title: bct-ch1-rel-jenkins.cobra.dre.ea.com
      icon: cicd
    - url: https://bct-dev-jenkins.cobra.dre.ea.com/
      title: bct-dev-jenkins.cobra.dre.ea.com
      icon: cicd
    - url: https://bct-preflight-jenkins.cobra.dre.ea.com/
      title: bct-preflight-jenkins.cobra.dre.ea.com
      icon: cicd
    - url: https://exc-dev-jenkins.cobra.dre.ea.com/
      title: exc-dev-jenkins.cobra.dre.ea.com
      icon: cicd
    - url: https://exc-rel-jenkins.cobra.dre.ea.com/
      title: exc-rel-jenkins.cobra.dre.ea.com
      icon: cicd
    - url: https://fb1-jenkins.cobra.dre.ea.com/
      title: fb1-jenkins.cobra.dre.ea.com
      icon: cicd
    - url: https://kin-autotest-jenkins.cobra.dre.ea.com/
      title: kin-autotest-jenkins.cobra.dre.ea.com
      icon: cicd
    - url: https://kin-dev-jenkins.cobra.dre.ea.com/
      title: kin-dev-jenkins.cobra.dre.ea.com
      icon: cicd
    - url: https://kin-preflight-jenkins.cobra.dre.ea.com/
      title: kin-preflight-jenkins.cobra.dre.ea.com
      icon: cicd
    - url: https://kin-release-jenkins.cobra.dre.ea.com/
      title: kin-release-jenkins.cobra.dre.ea.com
      icon: cicd
    - url: https://mer-dev-jenkins.cobra.dre.ea.com/
      title: mer-dev-jenkins.cobra.dre.ea.com
      icon: cicd
    - url: https://test1-jenkins.cobra.dre.ea.com/
      title: test1-jenkins.cobra.dre.ea.com
      icon: cicd
spec:
  type: library
  owner: dre-cobra
  lifecycle: production
  system: pipelines
  dependsOn:
    - component:default/container-images.jenkins
    - component:default/elipy.elipy-scripts
