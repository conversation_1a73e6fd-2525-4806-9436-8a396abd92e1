"""
integrate.py
"""
import os
import sys
from io import TextIOWrapper, BytesIO
import click
import argparse
from elipy2 import core, exceptions, frostbite_core, LOGGER, p4
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics
from dice_elipy_scripts.utils.dbxmerge import DBXMerge
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.integration_utils import compile_code, cook_data, submit_integration
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags


@click.command("integrate", short_help="Performs a Perforce integration.")
@click.argument("port", required=True)
@click.argument("client", required=True)
@click.argument("mapping", required=True)
@click.argument("changelist", required=True)
@click.option("--reverse/--no-reverse", default=False)
@click.option("--submit/--no-submit", default=True)
@click.option("--stream/--no-stream", default=False)
@click.option("--stream-merge/--no-stream-merge", default=True)
@click.option("--safe-resolve/--no-safe-resolve", default=True)
@click.option(
    "--shelve-cl/--no-shelve-cl", default=False, help="Shelve changelist for failed integration."
)
@click.option("--accept-theirs/--no-accept-theirs", default=False)
@click.option("--user", default=None, help="Perforce user name.")
@click.option(
    "--exclude-path",
    default=[],
    multiple=True,
    help="Don't integrate path. The path shall be relative to the workspace.",
)
@click.option("--exclude-accept-yours", is_flag=True, help="Exclude paths by accepting yours.")
@click.option("--submit-message", default="", help="Message to include in submit message.")
@click.option("--reverse-mapping", default=None, help="Branch from which we are integrating.")
@click.option(
    "--source-file-path",
    default=None,
    help="Source of files for interchanges and file integrations.",
)
@click.option(
    "--data-dir",
    default="kindata",
    help="Specify which data directory to use (relative to GAME_ROOT).",
)
@click.option("--platform", default="win64", help="Platform to cook")
@click.option("--assets", default="Game/Levels/MP/MP_Orbital", help="Asset to cook.")
@click.option("--cook", is_flag=True, help="Cook with last known good before submit.")
@click.option(
    "--merge-verification",
    is_flag=True,
    help="Verification with gensln and buildsln before submit.",
)
@click.option("--licensee", multiple=True, default=None, help="Licensee to use")
@click.option("--email", default=None, help="User email to authenticate to package server")
@click.option(
    "--domain-user",
    default=None,
    help="The user to authenticate to package server as DOMAIN\\user",
)
@click.option(
    "--password",
    default=None,
    help="User credentials to authenticate to package server",
)
@click.option("--cl-by-cl", is_flag=True, help="Integrate one cl at a time.")
@click.option("--tags", help="Description tag to filter cl-by-cl integrations.", multiple=True)
@click.option("--use-file-path", is_flag=True, help="Specify path for defining integration scope.")
@click.option(
    "--ignore-source-history",
    is_flag=True,
    help="Ignore source file history (sets the Perforce integrate flag -Di).",
)
@click.option("--cherrypick", is_flag=True, help="Cherrypick one specific changelist.")
@click.option("--remote-p4server", default=None, help="Remote p4 to fetch correct integrating CL.")
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    port,
    client,
    mapping,
    changelist,
    reverse,
    submit,
    stream,
    stream_merge,
    safe_resolve,
    shelve_cl,
    accept_theirs,
    user,
    exclude_path,
    exclude_accept_yours,
    submit_message,
    reverse_mapping,
    source_file_path,
    data_dir,
    platform,
    assets,
    cook,
    merge_verification,
    licensee,
    email,
    domain_user,
    password,
    cl_by_cl,
    tags,
    use_file_path,
    ignore_source_history,
    cherrypick,
    remote_p4server,
):
    """
    Performs a Perforce integration.
    """
    # Adding sentry tags.
    add_sentry_tags(__file__)

    perforce = p4.P4Utils(port=port, client=client, user=user)
    perforce.revert(quiet=True)

    if accept_theirs:
        resolve_mode = "t"
    elif safe_resolve:
        resolve_mode = None
    else:
        resolve_mode = "m"

    if source_file_path is None:
        source_file_path = mapping

    if stream:
        if reverse:
            source = reverse_mapping + "/..."
            target = source_file_path + "/..."
        else:
            source = source_file_path + "/..."
            target = reverse_mapping + "/..."
    else:
        source = source_file_path + "/..."
        target = None

    if cl_by_cl:
        if target is None:
            LOGGER.error("To be able to integrate cl-by-cl, a target has to be specified!")
            raise exceptions.ELIPYException

        cl_list = perforce.interchanges(source, target, to_revision=changelist, use_file_paths=True)
    else:
        cl_list = [(changelist, user, submit_message)]

    for cl_number, cl_user, cl_descr in cl_list:
        if cl_by_cl and tags and not perforce.check_for_tags(cl_number, tags):
            LOGGER.info("Not integrating %s, no matches in %s.", cl_number, tags)
            continue

        if cl_by_cl or cherrypick:
            cl_number = f"{cl_number},{cl_number}"
        if remote_p4server:
            cl_number = str(_fetch_remote_cl(cl_number, remote_p4server, user, port))

        if stream_merge:
            perforce.merge(mapping, reverse=reverse, to_revision=cl_number, parent=reverse_mapping)
        else:
            if use_file_path:
                perforce.integrate(
                    mapping=source,
                    reverse=reverse,
                    stream=stream,
                    to_revision=cl_number,
                    parent=target,
                    use_file_paths=True,
                    ignore_source_history=ignore_source_history,
                )
            else:
                perforce.integrate(
                    mapping=mapping,
                    reverse=reverse,
                    stream=stream,
                    to_revision=cl_number,
                    parent=reverse_mapping,
                    use_file_paths=False,
                    ignore_source_history=ignore_source_history,
                )

        for exclude in exclude_path:
            if exclude_accept_yours:
                perforce.resolve(mode="y", path=exclude)
            else:
                perforce.revert(path=exclude)

        if not perforce.opened():
            LOGGER.info("No opened files, nothing to resolve.")
            return

        perforce.resolve(mode=resolve_mode)

        if DBXMerge.executable:
            dbx_files = perforce.unresolved("*.dbx", resolve_type="content")
            if dbx_files:
                with DBXMerge(perforce.port, perforce.user, perforce.client) as dbxmerge:
                    for dbx_file in dbx_files:
                        dbxmerge.resolve(dbx_file.local_path)

        if perforce.unresolved():
            LOGGER.error("Unable to automatically resolve merge. Reverting and aborting!")
            if shelve_cl:
                pending_cl = perforce.latest_pending_changelist()
                if pending_cl:
                    perforce.set_description(
                        pending_cl, "Shelved changelist from failed integration"
                    )
                    perforce.shelve(pending_cl, discard=False)
            perforce.revert(quiet=True)
            raise exceptions.AutomaticP4MergeResolveException

        try:
            if cook:
                code_branch = source_file_path.split("/")[-1]
                data_branch = reverse_mapping.split("/")[-1]
                cook_data(
                    assets=[assets],
                    data_directory=data_dir,
                    platform=platform,
                    clean_avalanche_drop_db=True,
                    code_branch=code_branch,
                    data_branch=data_branch,
                    import_avalanche=True,
                    p4_object=perforce,
                )
            elif merge_verification:
                compile_code(
                    licensee=list(licensee),
                    port=port,
                    user=user,
                    client=client,
                    password=password,
                    email=email,
                    domain_user=domain_user,
                )

            # Submit the integrated result to Perforce.
            message = f"Integrated from {source}@{cl_number}"
            if cl_by_cl:
                message += f" (submitted by {cl_user})"
            message += "."
            if cl_descr:
                message += f"\n{cl_descr}"
            submit_integration(p4_object=perforce, submit_message=message, submit=submit)
        finally:
            perforce.revert(quiet=True)


def _fetch_remote_cl(remote_changelist, remote_p4_server, p4user, current_p4_server):
    """
    Fetching correct CL from remote p4 server.
    """
    LOGGER.info("Running p4_crossfind.py.")
    try:
        p4_crossfind = core.import_module_from_file(
            "p4_crossfind",
            os.path.join(
                frostbite_core.get_tnt_root(),
                "Code",
                "DICE",
                "BattlefieldGame",
                "fbcli",
                "p4_crossfind.py",
            ),
        )
    except Exception as exception:
        LOGGER.error("Unable to import p4_crossfind.py")
        raise exception

    parser = argparse.ArgumentParser()
    parser.add_argument("cl_number", nargs="+", help="Changelist number to spot integrate.")
    parser.add_argument("source_port", help="Source perforce server (eg: dice-p4edge-bf:2001)")
    parser.add_argument("p4user", help="p4 user (eg: dice\\svc_01dre.san.build)")
    parser.add_argument("current_port", help="Current perforce server (eg: dice-p4edge-bf:2001)")

    possible_perform_lookup_args = (
        remote_changelist,
        remote_p4_server,
        p4user,
        current_p4_server,
    )
    args = parser.parse_args(possible_perform_lookup_args)

    sys.stdout = TextIOWrapper(BytesIO(), sys.stdout.encoding)
    p4_crossfind.perform_lookup(args)
    sys.stdout.seek(0)
    out = sys.stdout.read()
    LOGGER.info("Output from p4_crossfind.py: %s", out)
    remote_cl = out.split("\n")[-2]
    LOGGER.info("Fetched remote CL : %s", remote_cl)

    return remote_cl
