package com.ea.lib.jobs

import com.ea.lib.LastKnownGood

class LibRemoteTriggers {
    /**
     * Adds generic job parameters for a job sending code changelist to be used for remote code preflights.
     */
    static void code_lastknowngood_set(def job, def branch_info) {
        job.with {
            description('Set changelist to use for the latest code build.')
            label('master')
            logRotator(30, 200)
            quietPeriod(0)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Code changelist used for latest successful code build on ' + branch_info.code_branch)
                    trim(true)
                }
            }
            wrappers {
                timestamps()
                buildName('${JOB_NAME}.${code_changelist}')
                credentialsBinding {
                    for (remoteMaster in branch_info.remote_masters_to_receive_code) {
                        def remoteMasterName = remoteMaster.name.split('\\.')[0]
                        usernamePassword(
                            "jenkinsUser_${remoteMasterName.replace('-', '_')}",
                            "jenkinsAPIToken_${remoteMasterName.replace('-', '_')}",
                            "jenkins-api-token-${remoteMasterName}")
                    }
                }
            }
            steps {
                for (curlCall in LastKnownGood.codeRemoteTriggers(branch_info)) {
                    shell(curlCall)
                }
            }
        }
    }

    /**
     * Adds generic job parameters for a job receiving code changelist in remote code preflights.
     */
    static void code_lastknowngood_get(def job, def branch_info) {
        job.with {
            description('Get latest code changelist.')
            label('master')
            logRotator(30, 200)
            quietPeriod(0)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Code changelist used for latest successful code build on ' + branch_info.remote_master_code_branch)
                    trim(true)
                }
            }
            authenticationToken('remotebuild') // Config token which is used in the 'set' job.
            wrappers {
                timestamps()
                buildName('${JOB_NAME}.${code_changelist}')
            }
        }
    }

    /**
     * Adds generic job parameters for a job sending code and data changelists to be used for remote data preflights.
     */
    static void data_lastknowngood_set(def job, def branch_info) {
        job.with {
            description('Set changelist to use for the latest data build.')
            label('master')
            logRotator(30, 200)
            quietPeriod(0)
            parameters {
                stringParam { // need to set both code and data, preflight data job rely on both
                    name('code_changelist')
                    defaultValue('')
                    description('Code changelist used for latest successful code build on ' + branch_info.code_branch)
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Data changelist used for latest successful data build on ' + branch_info.data_branch)
                    trim(true)
                }
            }
            wrappers {
                timestamps()
                buildName('${JOB_NAME}.${data_changelist}.${code_changelist}')
                credentialsBinding {
                    for (remoteMaster in branch_info.remote_masters_to_receive_data) {
                        def remoteMasterName = remoteMaster.name.split('\\.')[0]
                        usernamePassword(
                            "jenkinsUser_${remoteMasterName.replace('-', '_')}",
                            "jenkinsAPIToken_${remoteMasterName.replace('-', '_')}",
                            "jenkins-api-token-${remoteMasterName}")
                    }
                }
            }
            steps {
                for (curlCall in LastKnownGood.dataRemoteTriggers(branch_info)) {
                    shell(curlCall)
                }
            }
        }
    }

    /**
     * Adds generic job parameters for a job receiving code and data changelists in remote data preflights.
     */
    static void data_lastknowngood_get(def job, def branch_info) {
        job.with {
            description('Get latest data changelist.')
            label('master')
            logRotator(30, 200)
            quietPeriod(0)
            parameters {
                stringParam {
                    name('code_changelist') // need to get both code and data, preflight data job rely on both
                    defaultValue('')
                    description('Code changelist used for latest successful code build on ' + branch_info.code_branch)
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Data changelist used for latest successful data build on ' + branch_info.remote_master_data_branch)
                    trim(true)
                }
            }
            authenticationToken('remotebuild') // Config token which is used in the 'set' job.
            wrappers {
                timestamps()
                buildName('${JOB_NAME}.${data_changelist}.${code_changelist}')
            }
        }
    }

    // Method for setting both code and data changelist at the same time.
    static void code_data_lastknowngood_set(def job, def branch_info) {
        // Set values for variables.
        def remote_job_name = branch_info.branch_name + '.lastknowngood_remote'
        if (branch_info.remote_masters_trigger_pipeline) {
            remote_job_name += '_pipeline'
        }
        // Add sections to the Jenkins job.
        job.with {
            description('Set code and data changelist used for the latest successful data build on ' + branch_info.branch_name + '.')
            label('master')
            logRotator(30, 200)
            quietPeriod(0)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Code changelist used for latest successful data build on ' + branch_info.branch_name)
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Data changelist used for latest successful data build on ' + branch_info.branch_name)
                    trim(true)
                }
            }
            wrappers {
                timestamps()
                buildName('${JOB_NAME}.${data_changelist}.${code_changelist}')
                credentialsBinding {
                    for (remoteMaster in branch_info.remote_masters_to_receive_changelists) {
                        def remoteMasterName = remoteMaster.name.split('\\.')[0]
                        usernamePassword(
                            "jenkinsUser_${remoteMasterName.replace('-', '_')}",
                            "jenkinsAPIToken_${remoteMasterName.replace('-', '_')}",
                            "jenkins-api-token-${remoteMasterName}")
                    }
                }
            }
            steps {
                for (curlCall in LastKnownGood.lastknowngoodRemoteTriggers(remote_job_name, branch_info.remote_masters_to_receive_changelists)) {
                    shell(curlCall)
                }
            }
        }
    }

    static void code_data_lastknowngood_get(def job, def branch_info) {
        // Set values for variables.
        def integration_trigger_list = branch_info.integration_triggered_from_reference ?: []
        // Add sections to the Jenkins job.
        job.with {
            description('Get code and data changelists used for the latest successful data build on ' + branch_info.remote_master_branch + '.')
            label('master')
            logRotator(30, 200)
            quietPeriod(0)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Code changelist used for latest successful data build on ' + branch_info.remote_master_branch)
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Data changelist used for latest successful data build on ' + branch_info.remote_master_branch)
                    trim(true)
                }
            }
            authenticationToken('remotebuild') // Config token which is used in the 'set' job.
            wrappers {
                timestamps()
                buildName('${JOB_NAME}.${data_changelist}.${code_changelist}')
            }
            if (!integration_trigger_list.isEmpty()) {
                def integration_trigger_string = integration_trigger_list.join(', ')
                publishers {
                    downstreamParameterized {
                        trigger(integration_trigger_string) {
                            parameters {
                                currentBuild()
                            }
                        }
                    }
                }
            }
        }
    }

    static void code_data_lastknowngood_pipeline(def job, def project, def branch_info) {
        // Set values for variables.
        def trigger_settings = branch_info.trigger_jobs_from_remote_master
        def description_string = 'Get code and data changelists used for the latest successful data build on ' + trigger_settings.remote_branch + '.'
        // Add sections to the Jenkins job.
        job.with {
            description(description_string)
            environmentVariables {
                env('project_name', project.name)
                env('branch_name', branch_info.branch_name)
            }
            logRotator(30, 200)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Code changelist used for latest successful data build on ' + trigger_settings.remote_branch)
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Data changelist used for latest successful data build on ' + trigger_settings.remote_branch)
                    trim(true)
                }
            }
            authenticationToken('remotebuild') // Config token which is used in the 'set' job.
            properties {
                disableConcurrentBuilds()
                disableResume()
            }
        }
    }
}
