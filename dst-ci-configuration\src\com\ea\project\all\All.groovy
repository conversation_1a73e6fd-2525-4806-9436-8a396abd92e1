package com.ea.project.all

import com.ea.project.Cobra

class All {
    static String name = 'test'
    static String short_name = 'test'
    static String frostbite_syncer_setup = 'test'
    static Boolean single_perforce_server = false
    static String presync_machines = 'test'

    static String dataset = 'ExampleData'
    static String vault_credentials = 'test'
    static String vault_variable = 'TEST'
    static String game_team_secrets_credential = 'gameteam-test'
    static String game_team_secrets_credential_extra = ''
    static String game_team_secrets_credential_online_prod = ''
    static String game_team_secrets_variable = 'GAMETEAM_SECRETS_TEST'
    static List vault_default_platforms = []
    static String workspace_root = 'test'
    static String location = 'test'
    static String elipy_scripts_config_file = 'test.yml'
    static String fbcli_call = 'test'
    static String elipy_install_call = 'test'
    static String elipy_setup_call = 'test'
    static String elipy_call = 'test'

    static String azure_workspace_root = 'test'
    static String azure_elipy_install_root = 'test'
    static String azure_elipy_setup_call = 'test'
    static String azure_elipy_install_call = 'test'
    static String azure_elipy_call = 'test'
    static String frostbite_licensee = 'test'

    static String p4_browser_url = 'test'
    static String p4_user_single_slash = 'test'
    static Map p4_extra_servers = [:]

    static String p4_code_root = 'test'
    static String p4_code_creds = 'test'
    static String p4_code_server = 'test'
    static String p4_code_client = 'test'
    static String p4_code_client_env = 'test'
    // In a single server setup where TnT and Data live in the same stream, we can't have two workspaces
    // since they'll be writing to the same files on disk.
    // Exception to this is if you set up virtual streams for code and data respectively, and make
    // sure they never overlap.
    static String p4_data_root = p4_code_root
    static String p4_data_creds = p4_code_creds
    static String p4_data_server = p4_code_server
    static String p4_data_client = p4_code_client
    static String p4_data_client_env = p4_code_client_env

    static Map p4_code_servers = [
        'frostbite_build_dice': p4_code_server,
    ]

    static List<Map> p4_data_servers = [
        [name: 'tunguska_build_dice', p4_port: p4_data_server],
    ]

    static Map icepick_settings = [:]
    static String webexport_script_path = 'test'
    static String fake_ooa_wrapped_symbol = 'test'
    static Boolean commerce_debug_disable = true
    static Boolean use_recompression_cache = true

    static Boolean is_cloud = false

    static String autotest_matrix = 'test'
    static List<Map> vault_secrets_project = Cobra.af2_vault_credentials
}
