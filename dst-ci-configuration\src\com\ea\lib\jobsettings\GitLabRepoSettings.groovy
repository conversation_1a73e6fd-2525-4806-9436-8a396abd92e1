package com.ea.lib.jobsettings

class GitLabRepoSettings extends JobSetting {

    String triggerString
    String repoUrl
    String email
    String repoBranch
    Boolean isProduction

    void initializeJenkinsRegistryJob(def masterFile) {
        this.masterFile = masterFile

        description = 'Running the Jenkinsfile the Jenkins Registry repository'
        triggerString = masterFile.MAINTENANCE_SETTINGS.JENKINS_REGISTRY_TRIGGER_STRING ?: 'H H * * *'
        email = masterFile.MAINTENANCE_SETTINGS.JENKINS_REGISTRY_EMAIL ?: '<EMAIL>'
        jobLabel = masterFile.MAINTENANCE_SETTINGS.JENKINS_REGISTRY_JOB_LABEL ?: 'master'
        repoBranch = masterFile.MAINTENANCE_SETTINGS.JENKINS_REGISTRY_BRANCH ?: 'main'
        repoUrl = masterFile.MAINTENANCE_SETTINGS.JENKINS_REGISTRY_REPO_URL ?: '*****************:DRE/JenkinsCI/jenkins_registry.git'
        isProduction = masterFile.MAINTENANCE_SETTINGS.IS_PRODUCTION != null ? masterFile.MAINTENANCE_SETTINGS.IS_PRODUCTION : true
    }
}
