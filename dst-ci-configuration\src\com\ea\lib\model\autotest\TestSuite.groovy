package com.ea.lib.model.autotest

/**
 * A model with configurations that are specific to a test suite when running an Autotest job
 */
class TestSuite {
    /**
     * Test suite name. Example: {@code OnlineJoinFlowTests}. Required.
     */
    String name
    /**
     *  A list of {@link Platform}s to run the tests on. {@code region} overrides - {@code platform} overrides.
     *  Note that setting {@code region} is only supported for sequential jobs, not for parallel.
     */
    List<Platform> platforms
    /**
     * Extra icepick args. Default {@code []}. Optional.
     */
    List extraArgs = []
    /**
     * How long should the tests run before timing out. Default {@code 1}. Optional.
     */
    int timeoutHours
    /**
     * Do these tests require the game server binaries. Default {@code false}.
     */
    Boolean needGameServer = null
    /**
     * Default {@code false}.
     */
    Boolean fetchTests = false
    /**
     * What pool should be used when running icepick. Optional.
     */
    String poolType = null
    /**
     * Defines which <PERSON> labels to run the Autotest Category on. Optional.
     */
    String jobLabel
    /**
     * Arguments to target specific PCs with different specs eg 'localhost;autofarm-pc-888'
     */
    String targetsWindows
}
