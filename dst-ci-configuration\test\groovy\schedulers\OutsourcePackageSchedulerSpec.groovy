package schedulers

import support.DeclarativePipelineSpockTest

class OutsourcePackageSchedulerSpec extends DeclarativePipelineSpockTest {

    final String CODE_CHANGELIST = '2345'

    void setup() {
        binding.setVariable('env', [
            branchName : 'build-outsourcer-code',
            JOB_NAME   : 'build-outsourcer-code.outsource-package.build',
            projectName: 'Bct',
        ])

        binding.setVariable('params', [
            code_changelist: '1234',
            clean_local    : 'true',
        ])

        helper.with {
            registerAllowedMethod('get_branchfile', [String, String]) { projectName, branchName ->
                [
                    standard_jobs_settings: [
                        outsource_package_info: [
                            slack_channel: '#test-channel',
                            reference_job: 'build-main.code.start',
                        ]
                    ],
                ]
            }
            registerAllowedMethod('getLastStableCodeChangelist', [String]) { jobName -> CODE_CHANGELIST }
            registerAllowedMethod('ProjectClass', [String]) { projectName -> [short_name: 'exc'] }
            registerAllowedMethod('SlackMessageNew', [Map, String, String])
            registerAllowedMethod('echo', [String])
        }
    }

    void 'test triggers downstream job when a new changelist is available'() {
        when:
        runScript('outsource_package_scheduler.groovy')
        printCallStack()
        then:
        assertCalledOnceWith('build', [
            job       : 'build-outsourcer-code.outsource-package.build',
            parameters: [
                [name: 'code_changelist', value: '1234'],
                [name: 'clean_local', value: 'true'],
            ],
            propagate : false,
        ])
        assertJobStatusSuccess()
        testNonRegression('outsource_package_scheduler_success')
    }

    void 'test does not trigger a job when no new changelist is available'() {
        given:
        binding.setVariable('params', [
            code_changelist: '2345',
        ])
        when:
        runScript('outsource_package_scheduler.groovy')
        printCallStack()
        then:
        assertCalledTimes('build', 0)
        assertCalledWith('echo', 'No new changelist to generate outsource package from, aborting.')
        assertJobStatusSuccess()
        testNonRegression('outsource_package_scheduler_failure')
    }
}
