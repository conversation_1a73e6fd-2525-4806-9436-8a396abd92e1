import com.ea.project.Cobra

/**
 * GitSync.groovy
 * Method to sync a Gitlab repository.
 * Repositories currently used are specified here, and can be synced using only the name in the method call.
 * Other repositories can be synced by specifying 'other' as sync type.
 * Then a url to the repository and a destination folder (relative to the workspace) has to be provided.
 */
void call(String sync_type, String git_url = 'git_url', String git_targetdir = 'git_targetdir', String git_credentials = Cobra.git_creds_ci) {
    def url = git_url
    def target = git_targetdir
    def credentials = git_credentials

    switch (sync_type) {
    // case 'elipy-setup':
    // 	url = Cobra.git_url_elipy_setup
    // 	target = Cobra.git_targetdir_ci
    // 	break
        case 'cherrypick':
            url = '*****************:dice/tools-and-workflows/integrationci.git'
            target = 'cherrypick_copy'
            break
        case 'codecoverage' || 'kin_maptool':
            credentials = 'monkey-commons-ssh-v2'
        case 'codecoverage':
            url = '*****************/:ease-qe/Scripts.git'
            target = 'autotest'
            break
        case 'kin_maptool':
            url = '*****************:ghost-tools/project-polar-bear.git'
            target = 'ppb'
    }

    checkout(
        changelog: false,
        poll: false,
        scm: [
            $class           : 'GitSCM',
            branches         : [[
                                    name: Cobra.git_branch_ci,
                                ]],
            browser          : [
                $class : 'GitLab',
                repoUrl: Cobra.git_browser_url_ci,
                version: Cobra.git_browser_version_ci,
            ],
            extensions       : [
                [
                    $class         : 'PathRestriction',
                    excludedRegions: '.*',
                    includedRegions: '',
                ],
                [
                    $class           : 'RelativeTargetDirectory',
                    relativeTargetDir: target,
                ]
            ],
            userRemoteConfigs: [[
                                    credentialsId: credentials,
                                    url          : url,
                                ]]
        ]
    )
}
