{#
    Command:
        antifreeze
            short_help: Run the Antifreeze data export.

    Arguments:

    Required variables:
        code_changelist
            required: True
            help: Perforce changelist number for code.
        data_changelist
            required: True
            help: Perforce changelist number for data.
        data_directory
            required: True
            help: Data directory to use for licensee settings and to find data builds.
        p4_code_client
            required: True
            help: Workspace in Perforce for code.
        p4_code_port
            required: True
            help: Perforce server for code.
        p4_data_client
            required: True
            help: Workspace in Perforce for data.
        p4_data_port
            required: True
            help: Perforce server for data.

    Optional variables:
        clean
            default: false
            help: Delete TnT/Local if --clean true is passed.
        code_branch
            default: None
            help: Perforce branch name for code.
        dry_run
            is_flag: True
            help: Don't submit the built Antifreeze files.
        framework_args
            multiple: True
            help: Framework arguments for gen sln.
        import_local
            is_flag: True
            help: Imports contents of TnT/Local from filer.
        p4_user
            default: None
            help: Perforce user name.
        password
            default: None
            help: User credentials to authenticate to package server
        email
            default: None
            help: User email to authenticate to package server
        domain_user
            default: None
            help: The user to authenticate to package server as DOMAIN\user
        licensee
            multiple: True
            default: None
            help: Licensee to use.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    antifreeze
    --code-changelist {{ code_changelist }}
    --data-changelist {{ data_changelist }}
    --data-directory {{ data_directory }}
    --p4-code-client {{ p4_code_client }}
    --p4-code-port {{ p4_code_port }}
    --p4-data-client {{ p4_data_client }}
    --p4-data-port {{ p4_data_port }}
    {%- if clean %}
    --clean {{ clean }}
    {%- endif %}
    {%- if code_branch %}
    --code-branch {{ code_branch }}
    {%- endif %}
    {%- if dry_run %}
    --dry-run {{ dry_run }}
    {%- endif %}
    {%- if framework_args %}
    --framework-args {{ framework_args }}
    {%- endif %}
    {%- if import_local %}
    --import-local {{ import_local }}
    {%- endif %}
    {%- if p4_user %}
    --p4-user {{ p4_user }}
    {%- endif %}
    {%- if password %}
    --password {{ password }}
    {%- endif %}
    {%- if email %}
    --email {{ email }}
    {%- endif %}
    {%- if domain_user %}
    --domain-user {{ domain_user }}
    {%- endif %}
    {%- if licensee %}
    --licensee {{ licensee }}
    {%- endif %}
  displayName: elipy antifreeze
