import com.ea.lib.jobsettings.PipelineWarningSettings
import spock.lang.Specification

class PipelineWarningSettingsSpec extends Specification {
    class BranchFile {
        static Map standard_jobs_settings = [
            workspace_root                : 'workspace-root',
            elipy_call                    : 'elipy-call',
            elipy_install_call            : 'elipy-install-call',
            disable_build_pipeline_warning: true,
            job_label_statebuild          : 'label',
            extra_data_args               : ['--extra-arg', 'value'],
            frostbite_licensee            : 'kingston',
        ]
        static Map general_settings = [
            dataset: 'a-dataset',
        ]
    }

    class MasterFile {
        static Map branches = [branch: [data_branch: 'data-branch', code_branch: 'code-branch']]
    }

    class ProjectFile {
        static String name = 'Kingston'
        static Map pipeline_warning_settings = [
            reference_job         : 'reference-job.start',
            timeout_hours         : 2,
            user_credentials      : 'asdf',
            import_avalanche_state: true,
            target_address        : 'an-address',
            platform              : 'ps5',
            asset                 : 'asset',
        ]
        static Map p4_fb_settings = [
            p4_creds: 'creds',
            p4_port : '1337',
        ]
    }

    void "test that we get expected job settings in initializePipelineWarningStart"() {
        when:
        PipelineWarningSettings settings = new PipelineWarningSettings()
        settings.initializePipelineWarningStart(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            isDisabled == BranchFile.standard_jobs_settings.disable_build_pipeline_warning
            pipelineWarningReferenceJob == ProjectFile.pipeline_warning_settings.reference_job
            description == 'Job that runs the pipeline warning extraction scripts for branch.'
            projectName == projectFile.name
            cronTrigger == '@midnight'
        }
    }

    void "test that we get expected job settings in initializePipelineWarningJob"() {
        def expectedExtraArgs = '--extra-arg value --licensee kingston --username %pwarn_user% --password "%pwarn_passwd%"' +
            ' --import-avalanche-state --target-address an-address'
        when:
        PipelineWarningSettings settings = new PipelineWarningSettings()
        settings.initializePipelineWarningJob(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            jobLabel == BranchFile.standard_jobs_settings.job_label_statebuild
            description == 'Job that runs the pipeline warning extraction scripts for branch.'
            extraArgs == expectedExtraArgs
            timeoutMinutes == ProjectFile.pipeline_warning_settings.timeout_hours * 60
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'
            userCredentials == ProjectFile.pipeline_warning_settings.user_credentials
            frostbiteLoginP4Creds == ProjectFile.p4_fb_settings.p4_creds
            frostbiteLoginP4Port == ProjectFile.p4_fb_settings.p4_port
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} pipeline_warning --data-directory ${BranchFile.general_settings.dataset}" +
                " --platform ${ProjectFile.pipeline_warning_settings.platform} --assets ${ProjectFile.pipeline_warning_settings.asset}" +
                ' --code-branch code-branch --code-changelist %code_changelist%' +
                ' --data-branch data-branch --data-changelist %data_changelist%' +
                " --data-clean %clean_data% ${expectedExtraArgs}"
        }
    }
}
