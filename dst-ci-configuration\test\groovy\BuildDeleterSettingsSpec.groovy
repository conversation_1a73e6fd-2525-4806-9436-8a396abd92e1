import com.ea.lib.jobsettings.BuildDeleterSettings
import spock.lang.Specification

class BuildDeleterSettingsSpec extends Specification {

    Map project = [name: 'Kingston', valut_variable: 'vault_variable', vault_credentials: 'vault_credentials']
    String projectWithLocation = 'Kingston'
    Map projectInfo = [
        disable_build     : true,
        label             : 'Label',
        workspace_root    : 'workspace-root',
        elipy_install_call: 'install',
        elipy_call        : 'elipy-call',
        extra_args        : '--some-args',
    ]

    void "test that we get expected job settings in initializeStart"() {
        given:
        boolean deleteEmptyFoldersArg = true
        int hoursFromMidnight = 1
        when:
        BuildDeleterSettings bilboSettings = new BuildDeleterSettings()
        bilboSettings.initializeStart(project, projectWithLocation, deleteEmptyFoldersArg, hoursFromMidnight, projectInfo)
        then:
        with(bilboSettings) {
            isDisabled == projectInfo.disable_build
            cronTrigger == "0 ${24 - hoursFromMidnight} * * 6"
            description == 'Runs build deletion using ELIPY2.'
            projectName == 'Kingston'
            projectWithLocation == this.projectWithLocation
            deleteEmptyFolders == deleteEmptyFoldersArg
        }
    }

    void "test initializeStart throws if hoursFromMidnight is an invalid value"() {
        when:
        BuildDeleterSettings bilboSettings = new BuildDeleterSettings()
        bilboSettings.initializeStart(project, projectWithLocation, false, 25, projectInfo)
        then:
        thrown(IllegalArgumentException)
    }

    void "test that we get expected job settings in initializeJob"() {
        when:
        BuildDeleterSettings bilboSettings = new BuildDeleterSettings()
        bilboSettings.initializeJob(projectInfo, projectWithLocation, project)
        then:
        with(bilboSettings) {
            isDisabled == projectInfo.disable_build
            description == 'Deletes builds using ELIPY2.'
            jobLabel == "deleter && ${projectInfo.label.toLowerCase()}"
            workspaceRoot == projectInfo.workspace_root
            buildName == '${JOB_NAME}.${BUILD_NUMBER}'
            vaultVariable == project.vault_variable
            vaultCredentials == project.vault_credentials
            elipyInstallCall == projectInfo.elipy_install_call
            elipyCmd == "${projectInfo.elipy_call} deleter ${projectInfo.extra_args} %delete_empty_folders%"
        }
    }
}
