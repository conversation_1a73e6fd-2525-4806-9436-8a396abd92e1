"""
test_code_utils.py

Unit testing for code_utils
"""
import os
import pytest
import unittest
from mock import call, MagicMock, mock_open, patch
import elipy2
from elipy2.exceptions import ELIPYException
from dice_elipy_scripts.utils.code_utils import (
    _add_metadata_file,
    _get_existing_files,
    _get_metadata_files,
    add_metadata_files,
    download_outsource_dependencies,
    modify_buildlayout,
    prepare_outsource_dependencies,
    run_gensln,
)


@patch("os.path.join", MagicMock(side_effect=lambda *args: "\\".join(args)))
class TestCodeUtils(unittest.TestCase):
    def setUp(self):
        self.patcher_authenticate_eapm_credstore = patch(
            "dice_elipy_scripts.utils.code_utils.authenticate_eapm_credstore"
        )
        self.mock_authenticate_eapm_credstore = self.patcher_authenticate_eapm_credstore.start()

        self.patcher_gensln = patch("elipy2.code.CodeUtils.gensln")
        self.mock_gensln = self.patcher_gensln.start()

        self.patcher_settings_get = patch("elipy2.SETTINGS.get")
        self.mock_settings_get = self.patcher_settings_get.start()

        self.patcher_filerutils = patch("elipy2.filer.FilerUtils")
        self.mock_filerutils = self.patcher_filerutils.start()

    def tearDown(self):
        patch.stopall()

    def test_run_gensln(self):
        builder = elipy2.code.CodeUtils("win64game", "release")
        run_gensln(
            builder, password=None, user=None, framework_args=[], alltests=False, nomaster=False
        )
        assert self.mock_authenticate_eapm_credstore.call_count == 0
        self.mock_gensln.assert_called_once_with(
            framework_args=[],
            alltests=False,
            nomaster=False,
            wsl=False,
            override_config=None,
            stressbulkbuild=False,
        )

    def test_run_gensln_wsl(self):
        builder = elipy2.code.CodeUtils("linux64", "release")
        run_gensln(
            builder,
            password=None,
            user=None,
            framework_args=[],
            alltests=False,
            nomaster=False,
            wsl=True,
        )
        assert self.mock_authenticate_eapm_credstore.call_count == 0
        self.mock_gensln.assert_called_once_with(
            framework_args=[],
            alltests=False,
            nomaster=False,
            wsl=True,
            override_config=None,
            stressbulkbuild=False,
        )

    def test_run_gensln_credstore(self):
        builder = elipy2.code.CodeUtils("win64game", "release")
        run_gensln(
            builder, password="pass", user="user", framework_args=[], alltests=False, nomaster=False
        )
        self.mock_authenticate_eapm_credstore.assert_called_once_with(
            "pass", "user", domain_user=None
        )
        self.mock_gensln.assert_called_once_with(
            framework_args=[],
            alltests=False,
            nomaster=False,
            wsl=False,
            override_config=None,
            stressbulkbuild=False,
        )

    def test_run_gensln_credstore_domain_user(self):
        builder = elipy2.code.CodeUtils("win64game", "release")
        run_gensln(
            builder,
            password="pass",
            user="user",
            domain_user="domain_user",
            framework_args=[],
            alltests=False,
            nomaster=False,
        )
        self.mock_authenticate_eapm_credstore.assert_called_once_with(
            "pass", "user", domain_user="domain_user"
        )
        self.mock_gensln.assert_called_once_with(
            framework_args=[],
            alltests=False,
            nomaster=False,
            wsl=False,
            override_config=None,
            stressbulkbuild=False,
        )

    @patch("os.path.isdir", MagicMock())
    @patch("os.remove", MagicMock())
    @patch("elipy2.core.run", MagicMock())
    @patch("elipy2.core.extract_zip", MagicMock())
    @patch("pathlib.Path", MagicMock())
    @patch("elipy2.core.import_module_from_file", MagicMock())
    @patch("elipy2.code.CodeUtils", MagicMock())
    def test_download_outsource_dependencies(self):
        output_path = download_outsource_dependencies("test_user", "test_api_key")
        assert output_path == os.path.join("game_root", "outsources_dependencies")

    @patch("elipy2.frostbite_core.get_game_root")
    @patch("os.path.exists")
    @patch("os.path.isdir", MagicMock())
    @patch("os.remove")
    @patch("elipy2.core.run", MagicMock())
    @patch("elipy2.core.extract_zip", MagicMock())
    @patch("pathlib.Path", MagicMock())
    @patch("elipy2.core.import_module_from_file", MagicMock())
    @patch("elipy2.code.CodeUtils", MagicMock())
    def test_download_outsource_dependencies_output_path_exists(
        self, mock_remove, mock_exists, mock_game_root
    ):
        mock_exists.side_effect = (
            lambda x: True if x == "game_root\\outsources_dependencies\\eigen-3.3.7.zip" else False
        )
        mock_game_root.return_value = "game_root"
        download_outsource_dependencies("test_user", "test_api_key")
        mock_remove.assert_called_once_with("game_root\\outsources_dependencies\\eigen-3.3.7.zip")

    @patch("os.path.isdir", autospec=True)
    @patch("os.remove", autospec=True)
    @patch("elipy2.core.robocopy", autospec=True)
    @patch("elipy2.core.extract_zip", autospec=True)
    @patch("pathlib.Path", autospec=True)
    def test_prepare_outsource_dependencies(self, *_):
        prepare_outsource_dependencies("base_path")

    def test__get_metadata_files(self):
        self.mock_settings_get.return_value = ["file1.txt", "file2.txt"]
        assert _get_metadata_files() == ["file1.txt", "file2.txt"]
        self.mock_settings_get.assert_called_once_with("metadata_files", None)

    def test__get_metadata_files_location(self):
        self.mock_settings_get.return_value = ["file1.txt", "file2.txt"]
        assert _get_metadata_files(location="loc") == ["file1.txt", "file2.txt"]
        self.mock_settings_get.assert_called_once_with("metadata_files", "loc")

    def test__get_metadata_files_exception(self):
        self.mock_settings_get.side_effect = Exception()
        assert _get_metadata_files() == []

    @patch("os.path.exists")
    @patch("elipy2.frostbite_core.get_game_root")
    def test__get_existing_files(self, mock_game_root, mock_exists):
        mock_game_root.return_value = "game_root"
        mock_exists.side_effect = [True, False]
        assert _get_existing_files(["file1.txt", "file2.txt"]) == ["game_root\\file1.txt"]

    @patch("os.path.basename")
    @patch("dice_elipy_scripts.utils.code_utils.open", new_callable=mock_open())
    def test__add_metadata_file(self, mock_open, mock_basename):
        mock_basename.return_value = "file.txt"
        mock_open.return_value.__enter__().read.return_value = (
            '//A comment line\n{"some_key": "some_value"}'
        )
        _add_metadata_file(
            "path\\to\\file.txt", self.mock_filerutils.return_value, "bilbo\\doc\\path"
        )

    @patch("dice_elipy_scripts.utils.code_utils._add_metadata_file")
    @patch("dice_elipy_scripts.utils.code_utils._get_existing_files")
    @patch("dice_elipy_scripts.utils.code_utils._get_metadata_files")
    @patch("elipy2.filer_paths.get_code_build_root_path")
    def test_add_metadata_files(
        self,
        mock_get_code_build_root_path,
        mock__get_metadata_files,
        mock__get_existing_files,
        mock__add_metadata_file,
    ):
        mock_get_code_build_root_path.return_value = "code\\build\\root"
        mock__get_metadata_files.return_value = ["file1.txt", "file2.txt", "file3.txt"]
        mock__get_existing_files.return_value = ["game_root\\file1.txt", "game_root\\file2.txt"]
        add_metadata_files(self.mock_filerutils.return_value, "branch", "1234")
        mock__add_metadata_file.assert_has_calls(
            [
                call(
                    "game_root\\file1.txt", self.mock_filerutils.return_value, "code\\build\\root"
                ),
                call(
                    "game_root\\file2.txt", self.mock_filerutils.return_value, "code\\build\\root"
                ),
            ]
        )

    @patch("elipy2.LOGGER.error")
    @patch("dice_elipy_scripts.utils.code_utils._add_metadata_file")
    @patch("dice_elipy_scripts.utils.code_utils._get_existing_files")
    @patch("dice_elipy_scripts.utils.code_utils._get_metadata_files")
    @patch("elipy2.filer_paths.get_code_build_root_path")
    def test_add_metadata_files_exception(
        self,
        mock_get_code_build_root_path,
        mock__get_metadata_files,
        mock__get_existing_files,
        mock__add_metadata_file,
        mock_error,
    ):
        mock_get_code_build_root_path.return_value = "code\\build\\root"
        mock__get_metadata_files.return_value = ["file1.txt", "file2.txt", "file3.txt"]
        mock__get_existing_files.return_value = ["game_root\\file1.txt", "game_root\\file2.txt"]
        mock__add_metadata_file.side_effect = Exception()
        add_metadata_files(self.mock_filerutils.return_value, "branch", "1234")
        assert mock_error.call_count == 2

    @patch("json.dump")
    @patch("json.load")
    @patch("dice_elipy_scripts.utils.code_utils.open", new_callable=mock_open())
    @patch("elipy2.local_paths.get_local_build_path")
    def test_modify_buildlayout(self, mock_get_local_build_path, mock_open, json_load, json_dump):
        mock_get_local_build_path.return_value = "local\\path"
        json_load.return_value = {"tags": {"Configuration": "old_value"}, "other_item": "value"}
        modify_buildlayout("buildlayout_config", ["licensee"], "platform", "config")
        json_dump.assert_called_once_with(
            {"tags": {"Configuration": "buildlayout_config"}, "other_item": "value"},
            mock_open.return_value.__enter__(),
        )

    @patch("json.dump", MagicMock())
    @patch("json.load", MagicMock())
    @patch("dice_elipy_scripts.utils.code_utils.open", new_callable=mock_open())
    @patch("elipy2.local_paths.get_local_build_path")
    def test_modify_buildlayout_failure_multiple_licensees(
        self, mock_get_local_build_path, mock_open
    ):
        mock_get_local_build_path.return_value = "local\\path"
        with pytest.raises(ELIPYException):
            modify_buildlayout(
                "buildlayout_config", ["licensee1", "licensee2"], "platform", "config"
            )
