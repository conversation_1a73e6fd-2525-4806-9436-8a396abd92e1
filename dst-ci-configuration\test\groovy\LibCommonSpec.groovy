import com.ea.lib.LibCommonCps
import com.ea.lib.LibCommonNonCps
import com.ea.lib.LibJobDsl
import hudson.model.FreeStyleProject
import org.codehaus.groovy.runtime.typehandling.GroovyCastException
import spock.lang.Specification
import support.TestBuild
import support.TestParent

class LibCommonSpec extends Specification {
    void "p4RevertScript: test code revert script"() {
        when:
        final EXPECTED_RESULT = [
            // The script below has been taken from a live Jenkins controller.
            '@ECHO OFF',
            'DEL /F /Q /S D:\\dev\\logs\\* > nul 2>&1',
            'mkdir D:\\dev\\logs 2> NUL',
            'START /wait taskkill /f /im python.exe >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'START /wait taskkill /f /im fbenvconfigservice.exe >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'p4.exe -p dice-p4buildedge02-fb.dice.ad.ea.com:2001 -u dice\\example_svc_user -c jenkins-%NODE_NAME%-codestream revert -w //... >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'RD /S /Q D:\\dev\\Python',
            'p4.exe -p dice-p4buildedge02-fb.dice.ad.ea.com:2001 -u dice\\example_svc_user -c jenkins-%NODE_NAME%-codestream print -m 1 -q D:\\dev\\TnT\\masterconfig.xml > NUL',
            'IF NOT ERRORLEVEL 1 del /s /q /f D:\\dev\\TnT\\Bin\\Python\\*.pyc >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'p4.exe -p dice-p4buildedge02-fb.dice.ad.ea.com:2001 -u dice\\example_svc_user -c jenkins-%NODE_NAME%-codestream clean -m D:\\dev\\TnT\\Bin\\Python/...',
            'exit 0',
        ].join('\n')
        then:
        LibCommonCps.p4RevertScript(PROJECT, branchInfo, true, false, true) == EXPECTED_RESULT
    }

    void "p4RevertScript: test code revert script with AWS"() {
        when:
        final EXPECTED_RESULT = [
            // The script below has been taken from a live Jenkins controller.

            '@ECHO OFF',
            'DEL /F /Q /S C:\\dev\\logs\\* > nul 2>&1',
            'mkdir C:\\dev\\logs 2> NUL',
            'IF EXIST C:\\JenkinsBuildtrack.txt (',
            "echo 'Jenkins has done build on this agent' ",
            'exit 0',
            ')',
            'IF EXIST C:\\ProgramData\\Amazon\\EC2-Windows\\Launch\\Log\\UserdataExecution.log (',
            'FOR /L %%i IN (1,1,30) DO (',
            'find "Userdata execution finished" C:\\ProgramData\\Amazon\\EC2-Windows\\Launch\\Log\\UserdataExecution.log > NUL && (',
            'goto :post_userdata',
            ') || (',
            'ping localhost -n 10 > NUL',
            ')',
            ')',
            ')',
            ':post_userdata',
            'powershell -F C:\\scripts\\syncp4.ps1 dice-p4buildedge02-fb.dice.ad.ea.com:2001 dice\\example_svc_user jenkins-%NODE_NAME%-codestream //dicestudio/kin/dev/kin-dev dice-p4buildedge02-fb code',
            'powershell -F C:\\scripts\\syncp4.ps1 p4-tunguska-build01.dice.ad.ea.com:2001 dice\\example_svc_user jenkins-%NODE_NAME%-kindatastream //data/kin/dev/kin-dev-unverified-aws-code-preflight p4-tunguska-build01 data',
            'del C:\\scripts\\temp_ps.txt',
            'exit 0',
        ].join('\n')
        then:
        LibCommonNonCps.p4AWSRevertScript(PROJECT, getBranchInfo(true)) == EXPECTED_RESULT
    }

    void "p4RevertScript test code and data revert script"() {
        when:
        final EXPECTED_RESULT = [
            // The script below has been taken from a live Jenkins controller.
            '@ECHO OFF',
            'DEL /F /Q /S D:\\dev\\logs\\* > nul 2>&1',
            'mkdir D:\\dev\\logs 2> NUL',
            'START /wait taskkill /f /im python.exe >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'START /wait taskkill /f /im fbenvconfigservice.exe >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'p4.exe -p dice-p4buildedge02-fb.dice.ad.ea.com:2001 -u dice\\example_svc_user -c jenkins-%NODE_NAME%-codestream revert -w //... >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'p4.exe -p p4-tunguska-build01.dice.ad.ea.com:2001 -u dice\\example_svc_user -c jenkins-%NODE_NAME%-kindatastream revert -w //... >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'RD /S /Q D:\\dev\\Python',
            'p4.exe -p dice-p4buildedge02-fb.dice.ad.ea.com:2001 -u dice\\example_svc_user -c jenkins-%NODE_NAME%-codestream print -m 1 -q D:\\dev\\TnT\\masterconfig.xml > NUL',
            'IF NOT ERRORLEVEL 1 del /s /q /f D:\\dev\\TnT\\Bin\\Python\\*.pyc >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'p4.exe -p dice-p4buildedge02-fb.dice.ad.ea.com:2001 -u dice\\example_svc_user -c jenkins-%NODE_NAME%-codestream clean -m D:\\dev\\TnT\\Bin\\Python/...',
            'exit 0',
        ].join('\n')
        then:
        LibCommonCps.p4RevertScript(PROJECT, branchInfo) == EXPECTED_RESULT
    }

    void "p4RevertScript: test code and data revert script with branch override"() {
        when:
        final EXPECTED_RESULT = [
            // The script below has been taken from a live Jenkins controller.
            '@ECHO OFF',
            'DEL /F /Q /S D:\\dev\\logs\\* > nul 2>&1',
            'mkdir D:\\dev\\logs 2> NUL',
            'START /wait taskkill /f /im python.exe >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'START /wait taskkill /f /im fbenvconfigservice.exe >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'p4.exe -p oh-p4edge-fb.eu.ad.ea.com:2001 -u dice\\example_svc_user -c jenkins-%NODE_NAME%-codestream revert -w //... >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'p4.exe -p oh-p4edge-tunguska.eu.ad.ea.com:2001 -u dice\\example_svc_user -c jenkins-%NODE_NAME%-kindatastream revert -w //... >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'RD /S /Q D:\\dev\\Python',
            'p4.exe -p oh-p4edge-fb.eu.ad.ea.com:2001 -u dice\\example_svc_user -c jenkins-%NODE_NAME%-codestream print -m 1 -q D:\\dev\\TnT\\masterconfig.xml > NUL',
            'IF NOT ERRORLEVEL 1 del /s /q /f D:\\dev\\TnT\\Bin\\Python\\*.pyc >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'p4.exe -p oh-p4edge-fb.eu.ad.ea.com:2001 -u dice\\example_svc_user -c jenkins-%NODE_NAME%-codestream clean -m D:\\dev\\TnT\\Bin\\Python/...',
            'exit 0',
        ].join('\n')
        then:
        LibCommonCps.p4RevertScript(PROJECT, getBranchInfo(false, true)) == EXPECTED_RESULT
    }

    void "p4RevertScript: test data revert script"() {
        when:
        final EXPECTED_RESULT = [
            // The script below has been taken from a live Jenkins controller.
            '@ECHO OFF',
            'DEL /F /Q /S D:\\dev\\logs\\* > nul 2>&1',
            'mkdir D:\\dev\\logs 2> NUL',
            'START /wait taskkill /f /im python.exe >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'START /wait taskkill /f /im fbenvconfigservice.exe >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'p4.exe -p p4-tunguska-build01.dice.ad.ea.com:2001 -u dice\\example_svc_user -c jenkins-%NODE_NAME%-kindatastream revert -w //... >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'exit 0',
        ].join('\n')
        then:
        LibCommonCps.p4RevertScript(PROJECT, branchInfo, false, true) == EXPECTED_RESULT
    }

    void "p4RevertScript: test revert script wipe python env false"() {
        when:
        boolean wipe_python_env = false
        final EXPECTED_RESULT = [
            // The script below has been taken from a live Jenkins controller.
            '@ECHO OFF',
            'DEL /F /Q /S D:\\dev\\logs\\* > nul 2>&1',
            'mkdir D:\\dev\\logs 2> NUL',
            'START /wait taskkill /f /im python.exe >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'START /wait taskkill /f /im fbenvconfigservice.exe >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'p4.exe -p dice-p4buildedge02-fb.dice.ad.ea.com:2001 -u dice\\example_svc_user -c jenkins-%NODE_NAME%-codestream revert -w //... >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'p4.exe -p dice-p4buildedge02-fb.dice.ad.ea.com:2001 -u dice\\example_svc_user -c jenkins-%NODE_NAME%-codestream print -m 1 -q D:\\dev\\TnT\\masterconfig.xml > NUL',
            'IF NOT ERRORLEVEL 1 del /s /q /f D:\\dev\\TnT\\Bin\\Python\\*.pyc >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'p4.exe -p dice-p4buildedge02-fb.dice.ad.ea.com:2001 -u dice\\example_svc_user -c jenkins-%NODE_NAME%-codestream clean -m D:\\dev\\TnT\\Bin\\Python/...',
            'exit 0',
        ].join('\n')
        then:
        LibCommonCps.p4RevertScript(PROJECT, branchInfo, true, false, wipe_python_env) == EXPECTED_RESULT
    }

    void "p4RevertScript: test revert script wipe python env true"() {
        when:
        boolean wipe_python_env = true
        final EXPECTED_RESULT = [
            // The script below has been taken from a live Jenkins controller.
            '@ECHO OFF',
            'DEL /F /Q /S D:\\dev\\logs\\* > nul 2>&1',
            'mkdir D:\\dev\\logs 2> NUL',
            'START /wait taskkill /f /im python.exe >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'START /wait taskkill /f /im fbenvconfigservice.exe >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'p4.exe -p dice-p4buildedge02-fb.dice.ad.ea.com:2001 -u dice\\example_svc_user -c jenkins-%NODE_NAME%-codestream revert -w //... >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'RD /S /Q D:\\dev\\Python',
            'p4.exe -p dice-p4buildedge02-fb.dice.ad.ea.com:2001 -u dice\\example_svc_user -c jenkins-%NODE_NAME%-codestream print -m 1 -q D:\\dev\\TnT\\masterconfig.xml > NUL',
            'IF NOT ERRORLEVEL 1 del /s /q /f D:\\dev\\TnT\\Bin\\Python\\*.pyc >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'p4.exe -p dice-p4buildedge02-fb.dice.ad.ea.com:2001 -u dice\\example_svc_user -c jenkins-%NODE_NAME%-codestream clean -m D:\\dev\\TnT\\Bin\\Python/...',
            'exit 0',
        ].join('\n')
        then:
        LibCommonCps.p4RevertScript(PROJECT, branchInfo, true, false, wipe_python_env) == EXPECTED_RESULT
    }

    void "p4RevertScript: test revert script PreflightServer set"() {
        when:
        def code_preflight = 'code.p4.server.ea.com:2001'
        def data_preflight = 'data.p4.server.ea.com:2001'
        final EXPECTED_RESULT = [
            '@ECHO OFF',
            'DEL /F /Q /S D:\\dev\\logs\\* > nul 2>&1',
            'mkdir D:\\dev\\logs 2> NUL',
            'START /wait taskkill /f /im python.exe >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'START /wait taskkill /f /im fbenvconfigservice.exe >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'p4.exe -p code.p4.server.ea.com:2001 -u dice\\example_svc_user -c jenkins-%NODE_NAME%-codestream revert -w //... >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'RD /S /Q D:\\dev\\Python',
            'p4.exe -p code.p4.server.ea.com:2001 -u dice\\example_svc_user -c jenkins-%NODE_NAME%-codestream print -m 1 -q D:\\dev\\TnT\\masterconfig.xml > NUL',
            'IF NOT ERRORLEVEL 1 del /s /q /f D:\\dev\\TnT\\Bin\\Python\\*.pyc >> D:\\dev\\logs\\initial_p4_code_and_data_revert.log 2>&1',
            'p4.exe -p code.p4.server.ea.com:2001 -u dice\\example_svc_user -c jenkins-%NODE_NAME%-codestream clean -m D:\\dev\\TnT\\Bin\\Python/...',
            'exit 0',
        ].join('\n')
        then:
        LibCommonCps.p4RevertScript(PROJECT, getBranchInfo(false, true), true, false, true, code_preflight, data_preflight) == EXPECTED_RESULT
    }

    void "get_setting_value: test Get Setting Value"() {
        when:
        def branch_info = [
            setting1_branch                       : 'branch_value',
            setting1_branch_testing               : 'nested_value',
            setting1_branch_testing_platform      : 'platform_value',
            enable_eac                            : true,
            enable_eac_frosty_win64_digital_retail: false,
        ]
        then:
        LibCommonNonCps.get_setting_value(branch_info, ['testing', 'platform'], 'setting1_branch', 'default', TestProjectSettings) == 'platform_value'
        LibCommonNonCps.get_setting_value(branch_info, ['testing'], 'setting1_branch', 'default', TestProjectSettings) == 'nested_value'
        LibCommonNonCps.get_setting_value(branch_info, [], 'setting1_branch') == 'branch_value'
        LibCommonNonCps.get_setting_value(branch_info, [], 'setting1_branch', 'default') == 'branch_value'
        LibCommonNonCps.get_setting_value(branch_info, [], 'setting1_branch', 'default', TestProjectSettings) == 'branch_value'
        LibCommonNonCps.get_setting_value(branch_info, [], 'setting1', 'default', TestProjectSettings) == 'project_value'
        LibCommonNonCps.get_setting_value(branch_info, [], 'non_existant_setting') == null
        LibCommonNonCps.get_setting_value(branch_info, [], 'non_existant_setting', 'default') == 'default'
        LibCommonNonCps.get_setting_value(branch_info, [], 'non_existant_setting', 'default', TestProjectSettings) == 'default'
        LibCommonNonCps.get_setting_value(branch_info, [], 'enable_eac', false, TestProjectSettings) == true
        LibCommonNonCps.get_setting_value(branch_info, ['frosty', 'win64', 'digital', 'retail', 'alpha'], 'enable_eac', false, TestProjectSettings) == false
    }

    void "get_setting_value: dice-build-jenkins"() {
        when:
        def branch_info = [
            setting1_branch                       : 'branch_value',
            setting1_branch_testing               : 'nested_value',
            setting1_branch_testing_platform      : 'platform_value',
            enable_eac                            : true,
            enable_eac_frosty_win64_digital_retail: false,
        ]

        def build_deleter_projects = [
            'diceupgradenext': [
                project_name   : TestProjectSettings,
                branch_folder  : 'release',
                branch_name    : 'dice-next-release-irt',
                extra_args     : '--clean-symstore-days 60',
                setting1_branch: 'testing_value',
            ]
        ]

        then:
        build_deleter_projects.eachWithIndex { current_project, project_info, i ->
            def project = project_info.project_name.metaClass.properties.collectEntries { [it.name, project_info.project_name."${it.name}"] }
            LibCommonNonCps.get_setting_value(branch_info, ['testing', 'platform'], 'setting1_branch', 'default', project) == 'testing_value'
        }
    }

    void "postclean_silverback: test FreeStyleProject"() {
        when:
        LibJobDsl.postclean_silverback(job, PROJECT, branchInfo, 'code.p4.server.ea.com', 'data.p4.server.ea.com', 'extra arg')
        then:
        thrown(MissingMethodException)
    }

    void "throw_if_any_are_null: test empty array"() {
        when:
        LibCommonNonCps.throw_if_any_are_null([])
        then:
        noExceptionThrown()
    }

    void "throw_if_any_are_null: test valid values"() {
        when:
        LibCommonNonCps.throw_if_any_are_null([
            'setting1': 1,
            'setting2': 2,
            'setting3': 3,
            'setting4': 'a',
            'setting5': 'b',
            'setting6': 'c',
        ])
        then:
        noExceptionThrown()
    }

    void "throw_if_any_are_null: test null values"() {
        when:
        LibCommonNonCps.throw_if_any_are_null([
            'setting1': null,
        ])
        then:
        thrown(IllegalArgumentException)
    }

    void "throw_if_any_are_null: test null and valid values"() {
        when:
        LibCommonNonCps.throw_if_any_are_null([
            'settings1': 1,
            'settings2': 2,
            'settings3': null,
            'settings4': 'a',
            'settings5': 'b',
        ])
        then:
        thrown(IllegalArgumentException)
    }

    class EmptyBranchfile {
        static Map standard_jobs_settings = [:]
    }

    @SuppressWarnings('EmptyClass')
    class EmptyProject {}

    class TestProject_a {
        static int clean_build_cadence = -1
    }

    class TestProject_b {
        static String clean_build_cadence = 'illegal string'
    }

    class TestBranchfile_a {
        static Map standard_jobs_settings = [clean_build_cadence: -1]
    }

    class TestBranchfile_b {
        static Map standard_jobs_settings = [clean_build_cadence: 'illegal string']
    }

    void 'isRecentCleanBuildByBuilds: test exception with bad project'() {
        when:
        LibCommonCps.isRecentCleanBuildByBuilds([], TestProject_a, EmptyBranchfile)
        then:
        thrown(IllegalArgumentException)

        when:
        LibCommonCps.isRecentCleanBuildByBuilds([], TestProject_b, EmptyBranchfile)
        then:
        thrown(GroovyCastException)
    }

    void 'isRecentCleanBuildByBuilds: test exception with bad branchfile file'() {
        when:
        LibCommonCps.isRecentCleanBuildByBuilds([], EmptyProject, TestBranchfile_a)
        then:
        thrown(IllegalArgumentException)

        when:
        LibCommonCps.isRecentCleanBuildByBuilds([], EmptyProject, TestBranchfile_b)
        then:
        thrown(GroovyCastException)
    }

    void 'isRecentCleanBuildByBuilds: test exception with bad default BuildsPassed'() {
        when:
        LibCommonCps.isRecentCleanBuildByBuilds([], EmptyProject, EmptyBranchfile, -1)
        then:
        thrown(IllegalArgumentException)

        when:
        LibCommonCps.isRecentCleanBuildByBuilds([], EmptyProject, EmptyBranchfile, 10.5)
        then:
        thrown(MissingMethodException)

        when:
        LibCommonCps.isRecentCleanBuildByBuilds([], EmptyProject, EmptyBranchfile, 'illegal string')
        then:
        thrown(MissingMethodException)
    }

    void 'isRecentCleanBuildByBuilds: test isRecentCleanBuildByBuilds correct execution true'() {
        given: 'a build history that results in true'
        def buildDescriptions = ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', 'clean-build']
        TestParent parent = new TestParent(buildDescriptions).init()
        TestBuild currentBuild = parent.builds.last()
        int buildsPassed = 15

        when:
        boolean cleanBuild = LibCommonCps.isRecentCleanBuildByBuilds(currentBuild, EmptyProject, EmptyBranchfile, buildsPassed)

        then:
        cleanBuild == true
    }

    void 'isRecentCleanBuildByBuilds: test isRecentCleanBuildByBuilds correct execution false'() {
        given: 'a build history that results in false'
        def buildDescriptions = ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '']
        TestParent parent = new TestParent(buildDescriptions).init()
        TestBuild currentBuild = parent.builds.last()
        int buildsPassed = 15
        when:
        boolean cleanBuild = LibCommonCps.isRecentCleanBuildByBuilds(currentBuild, EmptyProject, EmptyBranchfile, buildsPassed)
        then:
        cleanBuild == false
    }

    void 'isRecentCleanBuildByTime: test exceptions with days'() {
        when:
        LibCommonCps.isRecentCleanBuildByTime([], -1)
        then:
        thrown(IllegalArgumentException)

        when:
        LibCommonCps.isRecentCleanBuildByTime([], 1.5)
        then:
        thrown(MissingMethodException)

        when:
        LibCommonCps.isRecentCleanBuildByTime([], 'illegal string')
        then:
        thrown(MissingMethodException)
    }

    void 'isRecentCleanBuildByTime: test exceptions with hours'() {
        when:
        LibCommonCps.isRecentCleanBuildByTime([], 0, -1)
        then:
        thrown(IllegalArgumentException)

        when:
        LibCommonCps.isRecentCleanBuildByTime([], 0, 25)
        then:
        thrown(IllegalArgumentException)

        when:
        LibCommonCps.isRecentCleanBuildByTime([], 0, 5.5)
        then:
        thrown(MissingMethodException)

        when:
        LibCommonCps.isRecentCleanBuildByTime([], 0, 'illegal string')
        then:
        thrown(MissingMethodException)
    }

    void 'isRecentCleanBuildByTime: test exceptions with minutes'() {
        when:
        LibCommonCps.isRecentCleanBuildByTime([], 0, 0, -1)
        then:
        thrown(IllegalArgumentException)

        when:
        LibCommonCps.isRecentCleanBuildByTime([], 0, 0, 61)
        then:
        thrown(IllegalArgumentException)

        when:
        LibCommonCps.isRecentCleanBuildByTime([], 0, 0, 5.5)
        then:
        thrown(MissingMethodException)

        when:
        LibCommonCps.isRecentCleanBuildByTime([], 0, 0, 'illegal string')
        then:
        thrown(MissingMethodException)
    }

    void 'isRecentCleanBuildByTime: test exceptions with seconds'() {
        when:
        LibCommonCps.isRecentCleanBuildByTime([], 0, 0, 0, -1)
        then:
        thrown(IllegalArgumentException)

        when:
        LibCommonCps.isRecentCleanBuildByTime([], 0, 0, 0, 61)
        then:
        thrown(IllegalArgumentException)

        when:
        LibCommonCps.isRecentCleanBuildByTime([], 0, 0, 0, 5.5)
        then:
        thrown(MissingMethodException)

        when:
        LibCommonCps.isRecentCleanBuildByTime([], 0, 0, 0, 'illegal string')
        then:
        thrown(MissingMethodException)
    }

    class TestClassSACP {
        static String strProperty = 'test'
        static boolean boolProperty = true
        static int intProperty = 10
    }

    void 'safelyAccessClassProperty: test that class properties are properly accessed and returned'() {
        expect:
        LibCommonCps.safelyAccessClassProperty(item, property) == value

        where:
        item          | property              || value
        TestClassSACP | 'strProperty'         || 'test'
        TestClassSACP | 'boolProperty'        || true
        TestClassSACP | 'intProperty'         || 10
        TestClassSACP | 'nonExistentProperty' || null
    }

    private final Map PROJECT = [
        p4_code_client_env  : 'jenkins-%NODE_NAME%-codestream',
        p4_code_server      : 'dice-p4buildedge02-fb.dice.ad.ea.com:2001',
        p4_data_client_env  : 'jenkins-%NODE_NAME%-kindatastream',
        p4_data_server      : 'p4-tunguska-build01.dice.ad.ea.com:2001',
        p4_user_single_slash: 'dice\\example_svc_user',
    ]

    class TestProjectSettings {
        static String setting1 = 'project_value'
    }

    private Map getBranchInfo(aws = false, override = false) {
        def branchInfo = [workspace_root: "${aws ? 'C' : 'D'}:\\dev"]

        if (override) {
            branchInfo += [
                p4_code_server: 'oh-p4edge-fb.eu.ad.ea.com:2001',
                p4_data_server: 'oh-p4edge-tunguska.eu.ad.ea.com:2001',
            ]
        }

        return branchInfo
    }

    private Class getJob() {
        return FreeStyleProject
    }
}
