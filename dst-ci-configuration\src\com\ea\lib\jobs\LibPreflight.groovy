package com.ea.lib.jobs

import com.ea.lib.LibCommonNonCps
import com.ea.lib.LibJenkins
import com.ea.lib.LibJobDsl
import com.ea.lib.jobsettings.PrePreflightSettings

/**
 * DOCUMENTATION LINKS:
 *  - https://docs.google.com/document/d/1BJDoqDZG4VNCyvEUwaH5pnXMCoPTWZr-62CXaHRY7G0/edit?usp=sharing
 */
class LibPreflight {
    /**
     * Adds generic job parameters for a code preflight start job.
     */
    static void code_preflight_start(def job, def project, def branch_info) {
        // Set values for variables.
        def modifiers = ['preflight', 'code']
        def codepreflight_reference_job = branch_info.codepreflight_reference_job ?: branch_info.branch_name + '.code.start'
        def datapreflight_reference_job = branch_info.datapreflight_reference_job ?: branch_info.branch_name + '.data.start'
        def concurrent_builds = branch_info.concurrent_code ?: 6
        def disable_build = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'disable_build', false)
        def force_rebuild_default = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'force_rebuild', false)
        def retry_limit = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'retry_limit', 1, project)
        def use_last_known_good_code_cl = branch_info.use_last_known_good_code_cl ?: false
        def max_builds_tokeep = branch_info.max_builds_tokeep ?: 100
        def enable_custom_cl = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'enable_custom_cl', false, project)

        // Add sections to the Jenkins job.
        job.with {
            description('Scheduler job for code preflighting on ' + branch_info.branch_name + '.')
            disabled(disable_build)
            logRotator(7, max_builds_tokeep)
            quietPeriod(0)
            throttleConcurrentBuilds {
                maxTotal(concurrent_builds)
            }
            properties {
                disableResume()
            }
            parameters {
                stringParam {
                    name('unshelve_changelist')
                    defaultValue('')
                    description('Specifies code changelist to preflight')
                    trim(true)
                }
                stringParam {
                    name('preflighter')
                    defaultValue('anonymous')
                    description('Specifies who triggers the preflight.')
                    trim(true)
                }
                choiceParam('sync_to_head', ['false', 'true'], 'Set to true to ignore any lastknowngood changelist and tell perforce to sync to #head.')
                stringParam {
                    name('P4CL')
                    defaultValue('')
                    description('when enable_custom_changelist is true, this has highest priority to overwrite other settings')
                    trim(true)
                }
                choiceParam('clean_local', ['false', 'true'], 'If true, TnT/Local will be deleted at the beginning of the run.')
                choiceParam('only_warm_machine', ['not', 'do'], 'If set to "do", only run code_changelist to warm up machine, not care unshelve_changelist.')
                booleanParam('force_rebuild', force_rebuild_default, 'Forces a rebuild and does not take previous build results into account')
            }
            environmentVariables {
                env('branch_name', branch_info.branch_name)
                env('codepreflight_reference_job', codepreflight_reference_job)
                env('datapreflight_reference_job', datapreflight_reference_job)
                env('project_name', project.name)
                env('project_short_name', project.short_name)
                env('retry_limit', retry_limit)
                env('use_last_known_good_code_cl', use_last_known_good_code_cl)
                env('enable_custom_changelist', enable_custom_cl)
            }
        }
    }

    /**
     * Adds generic job parameters for a data preflight start job.
     */
    static void data_preflight_start(def job, def project, def branch_info) {
        // Set values for variables.
        def modifiers = ['preflight', 'data']
        def concurrent_builds = branch_info.concurrent_data ?: 6
        def datapreflight_reference_job = branch_info.datapreflight_reference_job ?: branch_info.branch_name + '.data.start'
        def max_builds_tokeep = branch_info.max_builds_tokeep ?: 100
        def enable_custom_cl = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'enable_custom_cl', false, project)
        def validate_direct_references_default = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'validate_direct_references', false, project)

        // Add sections to the Jenkins job.
        job.with {
            description('Scheduler job for data preflighting on ' + branch_info.branch_name + '.')
            logRotator(7, max_builds_tokeep)
            quietPeriod(0)
            throttleConcurrentBuilds {
                maxTotal(concurrent_builds)
            }
            properties {
                disableResume()
            }
            parameters {
                stringParam {
                    name('unshelve_changelist')
                    defaultValue('')
                    description('Specifies data changelist to preflight')
                    trim(true)
                }
                stringParam {
                    name('preflighter')
                    defaultValue('anonymous')
                    description('Specifies who triggers the preflight.')
                    trim(true)
                }
                stringParam {
                    name('P4CL')
                    defaultValue('')
                    description('if enable_custom_changelist is true, changelist(from checkmate) for datapreflight to sync')
                    trim(true)
                }
                booleanParam('validate_direct_references', validate_direct_references_default, 'If true, run the -f validateDirectReferences validation.')
                booleanParam('clean_index', false, 'If true, run the index step with -clean.')
            }
            environmentVariables {
                env('branch_name', branch_info.branch_name)
                env('dataset', branch_info.dataset)
                env('datapreflight_reference_job', datapreflight_reference_job)
                env('project_name', project.name)
                env('project_short_name', project.short_name)
                env('enable_custom_changelist', enable_custom_cl)
            }
        }
    }

    /**
     * Adds generic job parameters for a pre-preflight start job per platform
     */
    static void pre_preflight_start(def job, def project, def branch_info, def platform) {
        def modifiers = ['pre_preflight', 'data', platform]
        def trigger_type = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'trigger_type', 'cron', project)
        def trigger_string = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'trigger_string', 'H/30 * * * 1-5\nH/30 6-23 * * 6-7', project)
        def prepreflight_idle_length = LibCommonNonCps.get_setting_value(branch_info, [], 'prepreflight_idle_length', '45')
        job.with {
            description('Scheduler to run data pre-preflight on ' + platform)
            logRotator(7, 100)
            quietPeriod(0)
            properties {
                disableResume()
                pipelineTriggers {
                    triggers {
                        if (trigger_type == 'cron') {
                            cron {
                                spec(trigger_string)
                            }
                        }
                    }
                }
            }
            parameters {
                stringParam {
                    name('prepreflight_idle_length')
                    defaultValue(prepreflight_idle_length)
                    description('Specifies how long(unit:minutes) node should run pre-preflight if it was idle')
                    trim(true)
                }
            }
            environmentVariables {
                env('branch_name', branch_info.branch_name)
                env('project_name', project.name)
                env('platform', platform)
                env('dataset', branch_info.dataset)
            }
        }
    }

    /**
     * Adds generic job parameters for a code preflight build job.
     */
    static void code_preflight_job(def job, def project, def branch_info) {
        job.with {
            def modifiers = []
            def user_credentials = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'user_credentials', '', project)
            def frostbite_licensee = branch_info.frostbite_licensee
            def import_local = branch_info.import_local ?: false
            def ignore_icepick_exit_code = branch_info.icepick_settings?.ignore_icepick_exit_code != null ? branch_info.icepick_settings?.ignore_icepick_exit_code : project.icepick_settings?.ignore_icepick_exit_code
            def p4_compile = branch_info.p4_compile ?: false
            def p4_code_server = LibCommonNonCps.get_setting_value(branch_info, ['preflight'], 'p4_code_server', '', project)
            boolean is_cloud = branch_info.run_on_azure && !branch_info.force_on_prem

            def fb_login_details = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_fb_settings', [:], project)
            def max_builds_tokeep = branch_info.max_builds_tokeep ?: 100

            def timeout_hours = branch_info.timeout_hours_codepreflight ?: 2
            def timeout_minutes = timeout_hours * 60

            String elipy_install_call = is_cloud ? branch_info.azure_elipy_install_call : branch_info.elipy_install_call
            String elipy_call = is_cloud ? branch_info.azure_elipy_call : branch_info.elipy_call
            String custom_workspace = is_cloud ? branch_info.azure_workspace_root : branch_info.workspace_root

            String job_label = branch_info.job_label_statebuild ?: 'statebuild'
            if (branch_info.platform == 'ps4' && branch_info.statebuild_codepreflight_ps4 == false) {
                job_label = 'code && ps4'
                import_local = true
            } else if (is_cloud) {
                job_label = "${branch_info.code_branch} && code && ${branch_info.platform} && ${branch_info.config}"
            } else if (branch_info.statebuild_codepreflight == false) {
                job_label = branch_info.code_branch + ' && code && ' + branch_info.platform
            }
            job_label += is_cloud ? ' && cloud' : ' && !cloud'
            if (branch_info.nomaster_platform) {
                job_label.replace('code', 'code-nomaster')
            }

            def extra_args = branch_info.extra_codepreflight_args ?: ''
            if (project.icepick_settings && branch_info.use_icepick_test && branch_info.platform == 'tool' && branch_info?.nomaster_platform != true) {
                extra_args += " --icepick-test ${project.icepick_settings.icepick_preflight_test}"
                if (ignore_icepick_exit_code != null) {
                    extra_args += " --ignore-icepick-exit-code ${ignore_icepick_exit_code}"
                }
                if (branch_info.icepick_extra_framework_args) {
                    extra_args += " --icepick-extra-framework-args \"${branch_info.icepick_extra_framework_args}\""
                }
                if (!branch_info.skip_icepick_settings_file) {
                    extra_args += " --settings-files ${branch_info.icepick_settings_files ?: project.icepick_settings.settings_files}"
                }
                extra_args += " --reporting-branch ${branch_info.reporting_branch ?: branch_info.data_branch}"
                extra_args += ' --framework-args -G:package.FBBuild.enableAllCppTests=true'
                extra_args += ' --framework-args -G:package.FBBuild.enableAllCSharpTests=true'
            }
            if (branch_info?.nomaster_platform == true) {
                extra_args += ' --nomaster'
            }
            if (frostbite_licensee != null) {
                extra_args += ' --licensee ' + frostbite_licensee
            }
            if (import_local == true) {
                extra_args += ' --import-local'
            }
            if (p4_compile == true) {
                extra_args += ' --p4-compile'
            }
            if (user_credentials != '') {
                extra_args += ' --email %monkey_email% --password "%monkey_passwd%"'
            }
            if (is_cloud) {
                extra_args += ' --framework-args -G:P4ProxyMap.UseServerTag=buildfarm'
                extra_args += ' --framework-args -backupgeneratedfiles'
            }

            description('Code preflight for ' + branch_info.platform + ' in ' + branch_info.config + 'on ' + branch_info.code_branch + '.')
            label(job_label)
            logRotator(7, max_builds_tokeep)
            quietPeriod(0)
            concurrentBuild()
            customWorkspace(custom_workspace)
            throttleConcurrentBuilds {
                maxPerNode(1)
            }
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('unshelve_changelist')
                    defaultValue('')
                    description('Specifies code changelist to preflight')
                    trim(true)
                }
                choiceParam('clean_local', ['false', 'true'], 'If true, TnT/Local will be deleted at the beginning of the run.')
                choiceParam('only_warm_machine', ['not', 'do'], 'If set to "do", only run code_changelist to warm up machine, not care unshelve_changelist.')
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName('${JOB_NAME}.${ENV, var="unshelve_changelist"}')
                timeout {
                    absolute(timeout_minutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
                credentialsBinding {
                    if (user_credentials != '') {
                        usernamePassword('monkey_email', 'monkey_passwd', user_credentials)
                    }
                    if (fb_login_details.p4_creds) {
                        usernamePassword('fb_p4_user', 'fb_p4_passwd', fb_login_details.p4_creds)
                    }
                }
            }
            steps {
                if (fb_login_details) {
                    batchFile("echo %fb_p4_passwd%|p4 -p ${fb_login_details.p4_port} -u %fb_p4_user% login & exit 0")
                }

                LibJobDsl.installElipy(delegate, elipy_install_call, project)
                batchFile('ipconfig | find /i "IPv4" ')
                batchFile(elipy_call + ' codepreflight ' + p4_code_server + ' ' + project.p4_code_client_env +
                    ' ' + branch_info.platform + ' ' + branch_info.config + ' %unshelve_changelist%' +
                    ' --code-branch ' + branch_info.code_branch + ' --data-directory ' + branch_info.dataset +
                    ' --%only_warm_machine%-warmup --user %P4_USER% ' + extra_args)
            }
            // to keep the postpreflight for codepreflight even it only runs on aws
            // unless we only run agent once and remove it, or could leave dirty workspace
            publishers {
                if (is_cloud && !branch_info.keep_agent) {
                    postBuildScript {
                        buildSteps {
                            postBuildStep {
                                stopOnFailure(false) // Mandatory setting
                                results(['FAILURE'])
                                buildSteps {
                                    LibJenkins.removeNode('${ENV, var="NODE_NAME"}')
                                }
                            }
                        }
                        markBuildUnstable(false)  // Mandatory setting
                    }
                } else {
                    downstreamParameterized {
                        trigger('maintenance.' + branch_info.current_branch + '.code.postpreflight.' + branch_info.platform) {
                            //mock dataset as code
                            condition('FAILED')
                            parameters {
                                currentBuild()
                                sameNode()
                                predefinedProp('preflight_type', 'code')
                                predefinedProp('platform', branch_info.platform)
                                predefinedProp('config', branch_info.config)
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Adds generic job parameters for a data preflight build job.
     */
    static void data_preflight_job(def job, def project, def branch_info, def preflight_set) {
        // Set values for variables.
        def import_avalanche = true
        def job_label = branch_info.job_label_statebuild ?: 'statebuild'
        if (branch_info.statebuild_datapreflight == false) {
            import_avalanche = false
            job_label = branch_info.data_branch + ' && datapreflight && ' +
                preflight_set.platform + preflight_set.extra_label
        }
        // search order: branch.p4_data_server_preflight_data_$platform > branch.p4_data_server_preflight_data > project.p4_data_server
        def modifiers = ['preflight_data', preflight_set.platform]
        def p4_data_server = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'p4_data_server', [:], project)
        def clean_master_version_check = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'clean_master_version_check', false, project)
        def validate_direct_references_default = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'validate_direct_references', false, project)

        def content_layers = branch_info.content_layers_preflight ?: []

        def extra_args = branch_info.extra_datapreflight_args ?: ''
        if (import_avalanche == true) {
            extra_args += ' --import-avalanche-state'
        }
        if (clean_master_version_check == true) {
            extra_args += ' --clean-master-version-check'
        }
        for (layer in content_layers) {
            extra_args += ' --content-layers ' + layer
        }

        def max_builds_tokeep = branch_info.max_builds_tokeep ?: 100
        def timeout_hours = branch_info.timeout_hours_datapreflight ?: 4
        def timeout_minutes = timeout_hours * 60

        // Add sections to the Jenkins job.
        job.with {
            description('Data preflight for ' + preflight_set.platform + ' using ' + branch_info.dataset + ' on ' + branch_info.data_branch + '.')
            label(job_label)
            logRotator(7, max_builds_tokeep)
            quietPeriod(0)
            customWorkspace(branch_info.workspace_root)
            concurrentBuild()
            throttleConcurrentBuilds {
                maxPerNode(1)
            }
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('unshelve_changelist')
                    defaultValue('')
                    description('Specifies data changelist to preflight.')
                    trim(true)
                }
                booleanParam('validate_direct_references', validate_direct_references_default, 'If true, run the -f validateDirectReferences validation.')
                booleanParam('clean_index', false, 'If true, run the index step with -clean.')
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName('${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}.${ENV, var="unshelve_changelist"}')
                timeout {
                    absolute(timeout_minutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, branch_info.elipy_install_call, project)
                batchFile(branch_info.elipy_call + ' datapreflight ' + p4_data_server + ' ' + project.p4_data_client_env +
                    ' ' + preflight_set.platform + ' %unshelve_changelist%' +
                    ' --pipeline-branch ' + branch_info.code_branch + ' --pipeline-changelist %code_changelist%' +
                    ' --data-branch ' + branch_info.data_branch + ' --data-changelist %data_changelist%' +
                    ' --user %P4_USER% --assets ' + preflight_set.assets.join(' --assets ') + ' --datadir ' + branch_info.dataset +
                    ' --validate-direct-references %validate_direct_references% --clean-index %clean_index% ' + extra_args)
            }
            publishers {
                downstreamParameterized {
                    trigger('maintenance.' + branch_info.data_branch + '.data.postpreflight.' + preflight_set.platform) {
                        condition('FAILED')
                        parameters {
                            currentBuild()
                            sameNode()
                            predefinedProp('preflight_type', 'content')
                            predefinedProp('platform', preflight_set.platform)
                            predefinedProp('config', 'final')
                            predefinedProp('asset', preflight_set.assets.join(' --asset '))
                        }
                    }
                }
            }
        }
    }

    /**
     * Adds generic job parameters for pre-preflight build job.
     */

    static void pre_preflight_job(def job, def project_file, def branch_file, def master_file, def platform_name, def branch_name) {
        // preflight_set_platform is platform
        def jobSettings = new PrePreflightSettings()
        jobSettings.initialize(branch_file, master_file, project_file, platform_name, branch_name, '%code_changelist%', '%data_changelist%')

        job.with {
            description(jobSettings.description)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(jobSettings.workspaceRoot)
            concurrentBuild()
            throttleConcurrentBuilds {
                maxPerNode(1)
                maxTotal(jobSettings.concurrentBuilds)
            }
            parameters {
                stringParam {
                    name('node')
                    defaultValue('')
                    description('Specifies which node to run this on.')
                    trim(true)
                }
                stringParam {
                    name('platform')
                    defaultValue('')
                    description('Specifies which platform this node preflights for.')
                    trim(true)
                }
                stringParam {
                    name('asset')
                    defaultValue('PreflightLevels.dbx')
                    description('Specifies asset to be used to cook.')
                    trim(true)
                }
                stringParam {
                    name('server_asset')
                    defaultValue('PreflightLevels')
                    description('Specifies server asset to be used to cook.')
                    trim(true)
                }
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to sync.')
                    trim(true)
                }
            }
            properties {
                groovyLabelAssignmentProperty {
                    secureGroovyScript {
                        script('return binding.getVariables().get("node")')
                        sandbox(true)
                    }
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                colorizeOutput()
                buildName('${JOB_NAME}.${platform}.${asset}.${NODE_NAME}')
                timeout {
                    absolute(jobSettings.timeoutMinutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, jobSettings.elipyInstallCall, project_file)
                batchFile(jobSettings.elipyCmd)
            }
            publishers {
                downstreamParameterized {
                    trigger('maintenance.' + branch_name + '.data.postpreflight.' + platform_name) {
                        condition('FAILED')
                        parameters {
                            currentBuild()
                            sameNode()
                            predefinedProp('preflight_type', 'content')
                        }
                    }
                }
            }
        }
    }

    /**
     * Adds generic job parameters for a post-preflight build job.
     */
    static void post_preflight_job(def job, def project, def branch_info) {
        job.with {
            def modifiers = []
            def user_credentials = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'user_credentials', '', project)
            def extra_args = branch_info.extra_postpreflight_args ?: ''  // since we do not differicate code or data
            def concurrent_builds = branch_info.concurrent_code ?: 6
            def p4_data_server = branch_info.p4_data_server ?: project.p4_data_server
            def p4_code_server = branch_info.p4_code_server ?: project.p4_code_server
            def fb_login_details = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_fb_settings', [:], project)
            def timeout_hours = branch_info.timeout_hours_postpreflight ?: 3
            def timeout_minutes = timeout_hours * 60
            def clean_master_version_check = LibCommonNonCps.get_setting_value(branch_info, [], 'clean_master_version_check', false, project)

            if (clean_master_version_check == true) {
                extra_args += ' --clean-master-version-check'
            }
            if (user_credentials != '') {
                extra_args += ' --email %monkey_email% --password "%monkey_passwd%"'
            }
            description('Runs after a failed preflight, runs a maintenance job to clean, cook and put back into the preflight pool.')
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(branch_info.workspace_root)
            concurrentBuild()
            throttleConcurrentBuilds {
                maxTotal(concurrent_builds)
                maxPerNode(1)
            }
            parameters {
                stringParam {
                    name('preflight_type')
                    defaultValue('')
                    description('specifies if this is a code or content preflight machine.')
                    trim(true)
                }
                stringParam {
                    name('platform')
                    defaultValue('')
                    description('Specifies which platform this node preflights for.')
                    trim(true)
                }
                stringParam {
                    name('config')
                    defaultValue('none')
                    description('Specifies config to run code on.')
                    trim(true)
                }
                stringParam {
                    name('asset')
                    defaultValue('PreflightLevels.dbx')
                    description('Specifies asset to be used to cook.')
                    trim(true)
                }
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to sync.')
                    trim(true)
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName('${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}')
                timeout {
                    absolute(timeout_minutes)
                    failBuild()
                    writeDescription('Build failed due to no activity after {0} minutes')
                }
                credentialsBinding {
                    if (user_credentials != '') {
                        usernamePassword('monkey_email', 'monkey_passwd', user_credentials)
                    }
                    if (fb_login_details.p4_creds) {
                        usernamePassword('fb_p4_user', 'fb_p4_passwd', fb_login_details.p4_creds)
                    }
                }
            }
            steps {
                if (fb_login_details) {
                    batchFile("echo %fb_p4_passwd%|p4 -p ${fb_login_details.p4_port} -u %fb_p4_user% login & exit 0")
                }
                LibJobDsl.installElipy(delegate, branch_info.elipy_install_call, project)
                batchFile(branch_info.elipy_call + ' post_preflight --preflight_type %preflight_type% --platform %platform%' +
                    ' --config %config% --asset %asset% --datadir ' + branch_info.dataset + ' --p4_user %P4_USER%' +
                    ' --p4_client ' + project.p4_data_client_env + ' --p4_port ' + p4_data_server +
                    ' --p4_client_code ' + project.p4_code_client_env + ' --p4_port_code ' + p4_code_server +
                    ' ' + extra_args)
            }
        }
    }
}
