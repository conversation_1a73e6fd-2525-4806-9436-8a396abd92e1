package com.ea.lib.model.branchsettings

/**
 * Model used to configure <branch-name>.custom-script.<command-name>
 */
class CustomScriptConfiguration {
    /**
     * Whether or not the job is enabled
     */
    boolean enabled = true
    /**
     * The executable to run the script.
     */
    String executable
    /**
     * The arguments to add after executable.
     */
    String executableArgs = ''
    /**
     * The relative script path to run. Working directory is TnT.
     */
    String scriptPath
    /**
     * Default scriptArgs value
     */
    String defaultScriptArgs = ''
    /**
     * Jenkins argument description
     */
    String argumentDescription = 'Optional arguments to pass to the command'
    /**
     * Optional job name. Defaults to the key in the branch configuration
     */
    String jobName
    /**
     * Agent labels
     */
    String label = 'statebuild'
    /**
     * Agent labels
     */
    String cronTrigger
    /**
     * Reference job to get last changelist
     */
    String referenceJob
    /**
     * Slack channel to notify
     */
    String slackChannel
    /**
     * Comma seperated environment variables to add before running script. Example: VAR1=FOO,VAR2=BAR
     */
    String environmentVariables = ''
    /**
     * Job timeout in hours
     */
    int timeoutHours = 3
    /**
     * Job timeout in hours
     * @return the timeout
     */
    int getTimeoutMinutes() {
        return timeoutHours * 60
    }

    String getCommand() {
        return executableArgs ? "$executable $executableArgs $scriptPath" : "$executable $scriptPath"
    }

    @Override
    String toString() {
        return "{com.ea.lib.model.branchsettings.CustomScriptConfiguration: enabled: $enabled, command: $command}"
    }
}
