"""
test_gamescript_utils.py

Unit testing for gamescript_utils
"""
import unittest
import pytest
from elipy2.exceptions import ELIPYException
from dice_elipy_scripts.utils.gamescripts_utils import generate_gamescripts
from mock import patch


class TestGamescriptsUtils(unittest.TestCase):
    """
    Test gamescripts utils
    """

    def setUp(self):
        """
        Set up test gamescripts utils
        """
        self.patcher_settings_get = patch("elipy2.SETTINGS.get")
        self.mock_settings_get = self.patcher_settings_get.start()

        self.patcher_tnt_root = patch("elipy2.frostbite_core.get_tnt_root")
        self.mock_import_module = self.patcher_tnt_root.start()

        self.patcher_join = patch("os.path.join")
        self.mock_import_module = self.patcher_join.start()

    def tearDown(self):
        """
        Teardown test gamescripts utils
        """
        patch.stopall()

    @patch("elipy2.core.import_module_from_file")
    def test_generate_gamescripts_success(self, mock_import_module):
        """
        Test generate_gamescripts when everything goes well
        """
        mock_import_module.return_value.call_target.return_value = 0
        generate_gamescripts()
        self.mock_settings_get.assert_called_once_with("gamescripts_script_path")
        assert mock_import_module.return_value.call_target.call_count == 2

    @patch("elipy2.core.import_module_from_file")
    def test_generate_gamescripts_failure(self, mock_import_module):
        """
        Test generate_gamescripts when call_target returns non zero
        """
        mock_import_module.return_value.call_target.return_value = 1
        with pytest.raises(ELIPYException):
            generate_gamescripts()

        mock_import_module.return_value.call_target.assert_called_once_with("gensln")
