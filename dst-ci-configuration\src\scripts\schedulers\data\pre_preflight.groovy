package scripts.schedulers.data

import com.ea.lib.LibJenkins
import com.ea.lib.jobsettings.PrePreflightSettings
import com.ea.project.GetBranchFile
import com.ea.project.GetMasterFile

//@Library('dst-lib@issue_50_job') _

/**
 * pre_preflight.groovy
 */
Closure generateStage(Class branchFile, Class masterFile, Class projectFile, String nodeName, String branchName, String platform, String latestVerifiedCode, String latestVerifiedData) {
    def jobSettings = new PrePreflightSettings()
    jobSettings.initialize(branchFile, masterFile, projectFile, platform, branchName, latestVerifiedCode, latestVerifiedData)
    Closure buildStage = {
        node(nodeName) {
            ws(jobSettings.workspaceRoot) {
                stage('Pre-preflight') {
                    bat(script: jobSettings.scriptContent, returnStatus: true)
                    P4SyncDefault(projectFile, jobSettings.branchFile, jobSettings.branchInfo.code_folder, jobSettings.branchInfo.code_branch, 'code', jobSettings.latestVerifiedCode)
                    P4SyncDefault(projectFile, jobSettings.branchFile, jobSettings.branchInfo.data_folder, jobSettings.branchInfo.data_branch, 'data', jobSettings.latestVerifiedData)
                    bat(jobSettings.elipyInstallCall)
                    echo jobSettings.elipyCmd
                    bat(jobSettings.elipyCmd)
                    PostBuildScript(projectFile, jobSettings.branchFile, 'delete_git_lock_file')
                    PostBuildScript(projectFile, jobSettings.branchFile, 'kill_processes')
                    PostBuildScript(projectFile, jobSettings.branchFile, 'post_clean')
                    ArchiveLogs(jobSettings.branchInfo, 'pipeline')
                    ArchiveLogs(jobSettings.branchInfo, 'non_build_specfic')
                }
            }
        }
    }
    return buildStage
}

Class projectFile = ProjectClass(env.project_name)
String platform = env.platform
String branchName = env.branch_name
String dataset = env.dataset
String projectName = env.project_name
String buildUrl = env.BUILD_URL
Class branchFile = GetBranchFile.get_branchfile(projectName, branchName)
Class masterFile = GetMasterFile.get_masterfile(buildUrl)[0]
// Gathering necessary values and converting/sending if needed

// Value for how long ago a machine has built before warming up again (can be config from stringparam), default: 45
int prePreflightIdleMinutes = params.prepreflight_idle_length.toInteger()
int prePreflightIdleMilliseconds = prePreflightIdleMinutes * 60 * 1000

// To find all the nodes with label platform + branchName, aka the lane
List<String> jenkinsNodes = LibJenkins.getNodesToRunPreflightMaintenanceOn(dataset, branchName, platform, prePreflightIdleMilliseconds)
currentBuild.displayName = env.JOB_NAME + '.' + env.BUILD_ID

String dataPreflightReference = branchFile.preflight_settings?.datapreflight_reference_job ?: branchName + '.data.lastknowngood'
String latestVerifiedData = LibJenkins.getLastStableDataChangelist(dataPreflightReference) ?: ''
String latestVerifiedCode = LibJenkins.getLastStableCodeChangelist(dataPreflightReference) ?: ''

pipeline {
    agent none
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Trigger') {
            environment {
                code_changelist = "${latestVerifiedCode}"
                data_changelist = "${latestVerifiedData}"
            }
            steps {
                script {
                    parallel(jenkinsNodes.collectEntries { nodeName ->
                        [(nodeName): generateStage(branchFile, masterFile, projectFile, nodeName, branchName, platform, latestVerifiedCode, latestVerifiedData)]
                    })
                }
            }
        }
    }
}
