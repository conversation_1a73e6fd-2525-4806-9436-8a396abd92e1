import com.ea.lib.jobs.LibAutotestModelBuilder
import com.ea.lib.model.autotest.AutotestCategory
import com.ea.lib.model.autotest.Name
import com.ea.lib.model.autotest.Platform
import com.ea.lib.model.autotest.Region
import com.ea.matrixfiles.AutotestMatrix
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll
import support.TestDataUtil

class AutotestMatrixBuildsExistSpec extends Specification {

    private static final String DEFAULT_CONFIG = 'final'
    private static final Region DEFAULT_REGION = Region.WW

    @Shared
    List testData = TestDataUtil.prepareTestDataForAutotests()*.values()

    @Unroll
    void 'Code builds should be built for Autotests in #autotestMatrix.class'() {
        expect:
        validateDrone(autotestMatrix, branches, testCategoriesForBranches) == ''
        where:
        [autotestMatrix, branches, testCategoriesForBranches] << testData
    }

    @Unroll
    void 'Frosty builds should be built for Autotests in #autotestMatrix.class'() {
        expect:
        validate<PERSON><PERSON>ty(autotestMatrix, branches, testCategoriesForBranches) == ''
        where:
        [autotestMatrix, branches, testCategoriesForBranches] << testData
    }

    /**
     * Validates the AutotestMatrix and fails if no Drone builds are being built for the given branches
     * @param autotestMatrix The AutotestMatrix to validate
     * @param branches The branches that the AutotestMatrix is configured for
     * @param testCategories A List of testCategories from the AutotestMatrix to validate
     * @return An empty String if the test passed, a String describing why the test failed otherwise
     */
    private static String validateDrone(def autotestMatrix, def branches, def testCategories) {
        String failedTests = ''
        testCategories.each {
            String branchName = it.name
            Class branchSettings = branches.find {
                it.name == branchName
            }.branchSettings
            it.testCategories.each { AutotestCategory testCategory ->
                // Skipping empty code_matrix until we add support for data branch validation
                if (!testCategory.isTestWithLooseFiles && branchSettings.code_matrix) {
                    // For drone we need final and release...
                    String config = testCategory.testInfo.config ?: testCategory.config ?: DEFAULT_CONFIG
                    List platforms = getDronePlatforms(testCategory, autotestMatrix as AutotestMatrix, branchName)
                    platforms.each { def platform ->
                        String targetPlatform = testCategory.buildType == 'dll' ? 'tool' : platform.name.toString()
                        int indexOfConfiguration = branchSettings.code_matrix.findIndexOf { def entry ->
                            (entry.name == targetPlatform || entry.name == platform.name.toString()) && entry.configs.any {
                                it instanceof String ? it == config : it['name'] == config
                            }
                        }
                        if (indexOfConfiguration == -1) {
                            failedTests += "\n- [TestCategory \"${testCategory.name}\" in ${autotestMatrix.class} on branch " +
                                "\"${branchName}\" will fail when it runs. No Code build is being built on branch \"${branchName}\" " +
                                "for platform \"${platform.name}\", config: \"${config}\" in ${branchSettings} code_matrix.]\n"
                        }
                    }
                }
            }
        }
        return failedTests
    }

    /**
     * Validates the AutotestMatrix and fails if no Frosty builds are being built for the given branches
     * @param autotestMatrix The AutotestMatrix to validate
     * @param branches The branches that the AutotestMatrix is configured for
     * @param testCategories A List of testCategories from the AutotestMatrix to validate
     * @return An empty String if the test passed, a String describing why the test failed otherwise
     */
    private static String validateFrosty(def autotestMatrix, def branches, def testCategories) {
        String failedTests = ''
        testCategories.each { Map tc ->
            String branchName = tc.name
            Class branchSettings = branches.find {
                it.name == branchName
            }.branchSettings
            tc.testCategories.each { AutotestCategory testCategory ->
                if (testCategory.isTestWithLooseFiles && (branchSettings.frosty_matrix || branchSettings.frosty_for_patch_matrix)) {
                    String config = testCategory.testInfo.config ?: testCategory.config ?: DEFAULT_CONFIG
                    String region = testCategory.testInfo.region ?: testCategory.region ?: DEFAULT_REGION
                    String format = testCategory.testInfo.format ?: testCategory.format
                    List<Platform> platforms = getFrostyPlatforms(testCategory, autotestMatrix as AutotestMatrix, branchName)
                    platforms.each { Platform platform ->
                        region = platform.region
                        boolean frostyIsConfigured = isConfigured(branchSettings.frosty_matrix, platform, config, format, region)
                        boolean frostyForPatchIsConfigured = isConfigured(branchSettings.frosty_for_patch_matrix, platform, config, format, region)
                        if (!frostyIsConfigured && !frostyForPatchIsConfigured) {
                            failedTests += "\n- [TestCategory \"${testCategory.name}\" in ${autotestMatrix.class} on branch " +
                                "\"${branchName}\" will fail when it runs. No frosty build is being built on branch \"${branchName}\" " +
                                "for platform \"${platform.name}\", config: \"${config}\", format: \"${format}\", region: \"${region}\" " +
                                "in ${branchSettings} frosty_matrix or frosty_for_patch_matrix.]"
                        }
                    }
                }
            }
        }
        return failedTests
    }

    /**
     * Parses the frostyMatrix and returns true if the given platform is configured with the supplied arguments
     * @param frostyMatrix the configuration to parse
     * @param platform target platform
     * @param config target config
     * @param format target format
     * @param region target region
     * @return true if configured, false otherwise
     */
    private static boolean isConfigured(def frostyMatrix, def platform, String config, String format, String region) {
        return frostyMatrix.findIndexOf { def entry ->
            entry.name == platform.name.toString() && entry.variants.find {
                (it.config == config || it.args?.contains("--additional-configs ${config}")) && it.format == format && it.region == region
            }
        } != -1
    }

    /**
     * Retrieves the platforms that the tests are configured for (Drone builds). Takes overrides into account.
     * @param testCategory The testCategory to parse
     * @param autotestMatrix The AutotestMatrix the category comes from
     * @param branchName Which branch to retrieve the platforms for
     * @return A List of platforms in the following format: {@code [name: platformName, region: region]}
     */
    private static List<Platform> getDronePlatforms(AutotestCategory testCategory, AutotestMatrix autotestMatrix, String branchName) {
        List platforms
        if (hasRegionDefinedPerPlatform(testCategory)) {
            platforms = testCategory.testInfo.platforms.collect {
                Name name = it.name == Name.WIN64 ? Name.WIN64GAME : it.name
                new Platform(name: name, region: it.region)
            }
        } else {
            // Reconstruct the same structure as above
            Region region = testCategory.testInfo.region ?: testCategory.region ?: DEFAULT_REGION
            List<Platform> categoryDefaultPlatforms = autotestMatrix.getPlatforms(branchName)
            List<Platform> platformNames = LibAutotestModelBuilder.composePlatforms(testCategory.testInfo, categoryDefaultPlatforms)
            platforms = platformNames.collect {
                Name name = it.name == Name.WIN64 ? Name.WIN64GAME : it.name
                new Platform(name: name, region: region)
            }
        }
        return platforms
    }

    /**
     * Retrieves the platforms that the tests are configured for (frosty builds). Takes overrides into account.
     * @param testCategory The testCategory to parse
     * @param autotestMatrix The AutotestMatrix the category comes from
     * @param branchName Which branch to retrieve the platforms for
     * @return A List of platforms in the following format: {@code [name: platformName, region: region]}
     */
    private static List<Platform> getFrostyPlatforms(AutotestCategory testCategory, AutotestMatrix autotestMatrix, String branchName) {
        List platforms = testCategory.testInfo.platforms
        if (!hasRegionDefinedPerPlatform(testCategory)) {
            // Reconstruct the same structure as above
            Region region = testCategory.testInfo.region ?: testCategory.region ?: DEFAULT_REGION
            List<Platform> categoryDefaultPlatforms = autotestMatrix.getPlatforms(branchName)
            List<Platform> platformNames = LibAutotestModelBuilder.composePlatforms(testCategory.testInfo, categoryDefaultPlatforms)
            platforms = platformNames.collect {
                new Platform(name: it.name, region: region)
            }
        }
        testCategory.testInfo.tests.each {
            if (it.platforms) {
                platforms.addAll(it.platforms)
            }
        }
        return platforms
    }

    /**
     * Checks whether or not the platforms have overridden regions.
     * @param testCategory The testCategory to parse
     * @return true if {@code region} is present, false otherwise
     */
    private static boolean hasRegionDefinedPerPlatform(AutotestCategory testCategory) {
        List<Platform> platforms = testCategory.testInfo.platforms
        if (platforms) {
            return platforms.any {
                it.region
            }
        }
        return false
    }

}
