"""
test_clean_agent.py

Unit testing for clean_agent
"""
import unittest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import patch
from dice_elipy_scripts.clean_agent import cli


class TestIcepickRun(unittest.TestCase):
    DEFAULT_ARGS = [
        "--delete-tnt-local",
        "true",
        "--delete-packages",
        "True",
        "--delete-packagesdev",
        "TRUE",
        "--delete-pip-cache",
        True,
        "--delete-logs",
        "false",
        "--delete-localpackages",
        "True",
        "--delete-data-state",
        False,
        "--delete-temp",
        False,
        "--data-dir",
        "non_exist",
    ]

    @patch("elipy2.core.delete_folder")
    def test_clean_agent(self, patch_delete_folder):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 0
        assert patch_delete_folder.call_count == 5
