"""
test_integrate_compile_upgrade_cook.py

Unit testing for integrate_compile_upgrade_cook
"""
import unittest
import os
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import patch, MagicMock
from dice_elipy_scripts.integrate_compile_upgrade_cook import cli


@patch("elipy2.core.clean_temp", MagicMock())
@patch("elipy2.core.close_file_handles", MagicMock())
@patch("elipy2.running_processes.kill", MagicMock())
class TestIntegrate(unittest.TestCase):
    OPTION_ASSETS = "--assets"
    OPTION_CODE_CHANGELIST = "--code-changelist"
    OPTION_CODE_CLEAN = "--code-clean"
    OPTION_DATA_CLEAN = "--data-clean"
    OPTION_DATA_DIRECTORY = "--data-directory"
    OPTION_DATA_PLATFORM = "--data-platform"
    OPTION_DOMAIN_USER = "--domain-user"
    OPTION_FRAMEWORK_ARGS = "--framework-args"
    OPTION_IGNORE_SOURCE_HISTORY = "--ignore-source-history"
    OPTION_LICENSEE = "--licensee"
    OPTION_MAPPING = "--mapping"
    OPTION_NO_SUBMIT = "--no-submit"
    OPTION_P4_CLIENT_CODE = "--p4-client-code"
    OPTION_P4_CLIENT_DATA = "--p4-client-data"
    OPTION_P4_PORT_CODE = "--p4-port-code"
    OPTION_P4_PORT_DATA = "--p4-port-data"
    OPTION_P4_USER_CODE = "--p4-user-code"
    OPTION_P4_USER_DATA = "--p4-user-data"
    OPTION_PIPELINE_ARGS = "--pipeline-args"
    OPTION_SOURCE_BRANCH = "--source-branch"
    OPTION_SUBMIT_MESSAGE = "--submit-message"
    OPTION_USE_SNOWCACHE = "--use-snowcache"
    OPTION_SNOWCACHE_MODE_OVERRIDE = "--snowcache-mode-override"

    VALUE_ASSETS = "test_asset"
    VALUE_CODE_CHANGELIST = "1234"
    VALUE_CODE_CLEAN = "true"
    VALUE_DATA_CLEAN = "true"
    VALUE_DATA_DIRECTORY = "data_directory"
    VALUE_DATA_PLATFORM = "test_platform"
    VALUE_DOMAIN_USER = "domain_user"
    VALUE_FRAMEWORK_ARGS = "test_arg"
    VALUE_LICENSEE = "test_licensee"
    VALUE_MAPPING = "some_mapping"
    VALUE_P4_CLIENT_CODE = "p4_client_code"
    VALUE_P4_CLIENT_DATA = "p4_client_data"
    VALUE_P4_PORT_CODE = "p4_port_code"
    VALUE_P4_PORT_DATA = "p4_port_data"
    VALUE_P4_USER_CODE = "p4_user_code"
    VALUE_P4_USER_DATA = "p4_user_data"
    VALUE_PIPELINE_ARGS = "arg1"
    VALUE_SOURCE_BRANCH = "source_branch"
    VALUE_SUBMIT_MESSAGE = "submit_message"
    VALUE_SNOWCACHE_MODE_OVERRIDE = "forceupload"

    DEFAULT_ARGS = [
        OPTION_ASSETS,
        VALUE_ASSETS,
        OPTION_CODE_CHANGELIST,
        VALUE_CODE_CHANGELIST,
        OPTION_DATA_DIRECTORY,
        VALUE_DATA_DIRECTORY,
        OPTION_MAPPING,
        VALUE_MAPPING,
        OPTION_P4_CLIENT_CODE,
        VALUE_P4_CLIENT_CODE,
        OPTION_P4_CLIENT_DATA,
        VALUE_P4_CLIENT_DATA,
        OPTION_P4_PORT_CODE,
        VALUE_P4_PORT_CODE,
        OPTION_P4_PORT_DATA,
        VALUE_P4_PORT_DATA,
        OPTION_P4_USER_CODE,
        VALUE_P4_USER_CODE,
        OPTION_P4_USER_DATA,
        VALUE_P4_USER_DATA,
        OPTION_SOURCE_BRANCH,
        VALUE_SOURCE_BRANCH,
    ]

    def setUp(self):
        self.patcher_p4utils = patch("elipy2.p4.P4Utils")
        self.mock_p4utils = self.patcher_p4utils.start()
        self.mock_p4utils.return_value = MagicMock()
        self.mock_p4utils.return_value.unresolved.return_value = []

        self.patcher_run = patch("elipy2.core.run")
        self.mock_run = self.patcher_run.start()

        self.patcher_compile_code = patch(
            "dice_elipy_scripts.integrate_compile_upgrade_cook.compile_code"
        )
        self.mock_compile_code = self.patcher_compile_code.start()

        self.patcher_cook_data = patch(
            "dice_elipy_scripts.integrate_compile_upgrade_cook.cook_data"
        )
        self.mock_cook_data = self.patcher_cook_data.start()

        self.patcher_submit_integration = patch(
            "dice_elipy_scripts.integrate_compile_upgrade_cook.submit_integration"
        )
        self.mock_submit_integration = self.patcher_submit_integration.start()

        self.patcher_ensure_p4_config = patch("elipy2.core.ensure_p4_config")
        self.mock_ensure_p4_config = self.patcher_ensure_p4_config.start()

    def tearDown(self):
        patch.stopall()

    def test_basic_args(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 0

    def test_unresolved(self):
        self.mock_p4utils.return_value.unresolved.return_value = ["file1"]
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 1
        assert self.mock_p4utils.return_value.revert.call_count == 3

    def test_compile_code(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 0
        self.mock_compile_code.assert_called_once_with(
            licensee=[],
            password=None,
            email=None,
            domain_user=None,
            framework_args=[],
            overwrite_p4config=True,
            clean=False,
            use_snowcache=False,
            snowcache_mode_override="",
        )

    def test_compile_code_clean(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.DEFAULT_ARGS + [self.OPTION_CODE_CLEAN, self.VALUE_CODE_CLEAN]
        )
        assert result.exit_code == 0
        self.mock_compile_code.assert_called_once_with(
            licensee=[],
            password=None,
            email=None,
            domain_user=None,
            framework_args=[],
            overwrite_p4config=True,
            clean=True,
            use_snowcache=False,
            snowcache_mode_override="",
        )

    def test_compile_code_snowcache(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS + [self.OPTION_USE_SNOWCACHE])
        assert result.exit_code == 0
        self.mock_compile_code.assert_called_once_with(
            licensee=[],
            password=None,
            email=None,
            domain_user=None,
            framework_args=[],
            overwrite_p4config=True,
            clean=False,
            use_snowcache=True,
            snowcache_mode_override="",
        )

    def test_compile_code_snowcache_mode(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_ARGS
            + [
                self.OPTION_USE_SNOWCACHE,
                self.OPTION_SNOWCACHE_MODE_OVERRIDE,
                self.VALUE_SNOWCACHE_MODE_OVERRIDE,
            ],
        )
        assert result.exit_code == 0
        self.mock_compile_code.assert_called_once_with(
            licensee=[],
            password=None,
            email=None,
            domain_user=None,
            framework_args=[],
            overwrite_p4config=True,
            clean=False,
            use_snowcache=True,
            snowcache_mode_override=self.VALUE_SNOWCACHE_MODE_OVERRIDE,
        )

    @patch("elipy2.frostbite_core.get_game_data_dir")
    def test_upgrade_exception(self, mock_datadir):
        self.mock_run.side_effect = Exception()
        mock_datadir.return_value = "data_dir"
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        self.mock_p4utils.return_value.clean.assert_called_once_with(folder="data_dir/...")

    def test_cook(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 0
        self.mock_cook_data.assert_called_once_with(
            assets=[self.VALUE_ASSETS],
            data_directory=self.VALUE_DATA_DIRECTORY,
            platform="win64",
            clean_avalanche_cook=False,
            pipeline_args=[],
            use_local_code=True,
        )

    def test_cook_clean(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.DEFAULT_ARGS + [self.OPTION_DATA_CLEAN, self.VALUE_DATA_CLEAN]
        )
        assert result.exit_code == 0
        self.mock_cook_data.assert_called_once_with(
            assets=[self.VALUE_ASSETS],
            data_directory=self.VALUE_DATA_DIRECTORY,
            platform="win64",
            clean_avalanche_cook=True,
            pipeline_args=[],
            use_local_code=True,
        )

    def test_cook_data_platform(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.DEFAULT_ARGS + [self.OPTION_DATA_PLATFORM, self.VALUE_DATA_PLATFORM]
        )
        assert result.exit_code == 0
        self.mock_cook_data.assert_called_once_with(
            assets=[self.VALUE_ASSETS],
            data_directory=self.VALUE_DATA_DIRECTORY,
            platform=self.VALUE_DATA_PLATFORM,
            clean_avalanche_cook=False,
            pipeline_args=[],
            use_local_code=True,
        )

    def test_cook_pipeline_args(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.DEFAULT_ARGS + [self.OPTION_PIPELINE_ARGS, self.VALUE_PIPELINE_ARGS]
        )
        assert result.exit_code == 0
        self.mock_cook_data.assert_called_once_with(
            assets=[self.VALUE_ASSETS],
            data_directory=self.VALUE_DATA_DIRECTORY,
            platform="win64",
            clean_avalanche_cook=False,
            pipeline_args=[self.VALUE_PIPELINE_ARGS],
            use_local_code=True,
        )

    @patch.dict(os.environ, {"BUILD_URL": "https://jenkins.master/job/jenkins.job/123/"})
    def test_submit(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 0
        self.mock_submit_integration.assert_called_once_with(
            p4_object=self.mock_p4utils.return_value,
            submit_message="Integrated from source_branch@1234.",
            submit=True,
        )

    @patch.dict(os.environ, {"BUILD_URL": "https://jenkins.master/job/jenkins.job/123/"})
    def test_submit_with_message(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.DEFAULT_ARGS + [self.OPTION_SUBMIT_MESSAGE, self.VALUE_SUBMIT_MESSAGE]
        )
        assert result.exit_code == 0
        self.mock_submit_integration.assert_called_once_with(
            p4_object=self.mock_p4utils.return_value,
            submit_message="Integrated from source_branch@1234.\nsubmit_message",
            submit=True,
        )

    def test_no_submit(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS + [self.OPTION_NO_SUBMIT])
        assert result.exit_code == 0
        self.mock_submit_integration.assert_called_once_with(
            p4_object=self.mock_p4utils.return_value,
            submit_message="Integrated from source_branch@1234.",
            submit=False,
        )
