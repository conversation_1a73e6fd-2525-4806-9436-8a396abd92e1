"""
test_delete_utils.py

Unit testing for delete_utils
"""
import unittest
import os
import tempfile
from dice_elipy_scripts.utils.delete_utils import delete_empty_folders
from mock import patch


class TestDeleteUtils(unittest.TestCase):
    @staticmethod
    def createTempDir():
        test_folder = tempfile.TemporaryDirectory()

        for folder in [
            "code/empty",
            "code/emptywithemptysubfolder/empty",
            "code/emptywithmultipleemptysubfolders/e",
            "code/emptywithmultipleemptysubfolders/m",
            "code/emptywithmultipleemptysubfolders/p",
            "code/emptywithmultipleemptysubfolders/t",
            "code/emptywithmultipleemptysubfolders/y",
            "code/withbuilddotjson",
            "code/withfile/nocrawlinghere",
            "code/withfileinsubfolder/withfile",
            "code/notallempty/e",
            "code/notallempty/m",
            "code/notallempty/p",
            "code/notallempty/t",
            "code/notallempty/y",
            "code/notallempty/f",
            "code/notallempty/i",
            "code/notallempty/l",
        ]:
            os.makedirs(os.path.join(test_folder.name, folder))
        for file_path in [
            "code/withbuilddotjson/build.json",
            "code/withfile/file",
            "code/withfileinsubfolder/withfile/file",
            "code/notallempty/f/file",
            "code/notallempty/i/file",
            "code/notallempty/l/file",
        ]:
            open(os.path.join(test_folder.name, file_path), "w").close()

        return test_folder

    @staticmethod
    def create_expected_result(test_folder, with_files_filter):
        folders = [
            "code/empty",
            "code/emptywithemptysubfolder",
            "code/emptywithmultipleemptysubfolders",
            "code/notallempty/e",
            "code/notallempty/m",
            "code/notallempty/p",
            "code/notallempty/t",
            "code/notallempty/y",
        ]
        if with_files_filter:
            folders.append("code/withbuilddotjson")
        return [os.path.normpath(os.path.join(test_folder.name, folder)) for folder in folders]

    @patch("multiprocessing.pool.ThreadPool.map")
    def test_delete_empty_folders(self, mock_map):
        test_folder = None
        try:
            test_folder = TestDeleteUtils.createTempDir()
            delete_empty_folders(path=os.path.join(test_folder.name, "code"))
        finally:
            test_folder.cleanup()

        expected_result = TestDeleteUtils.create_expected_result(test_folder, False)
        mock_map.assert_called_once()
        first_unpack, _ = mock_map.call_args
        _, args = first_unpack
        self.assertEqual(sorted(args), sorted(expected_result))

    @patch("multiprocessing.pool.ThreadPool.map")
    def test_delete_empty_folders_with_files_filter(self, mock_map):
        test_folder = None
        try:
            test_folder = TestDeleteUtils.createTempDir()
            delete_empty_folders(
                path=os.path.join(test_folder.name, "code"),
                files_equals_empty=["build.json"],
            )
        finally:
            test_folder.cleanup()

        expected_result = TestDeleteUtils.create_expected_result(test_folder, True)
        mock_map.assert_called_once()
        first_unpack, _ = mock_map.call_args
        _, args = first_unpack
        self.assertEqual(sorted(args), sorted(expected_result))
