package com.ea.project.bct.branchsettings

import com.ea.project.bct.Bct

class Trunk_content_dev_metrics {
    // Settings for jobs
    static Class project = Bct
    static Map general_settings = [
        dataset             : project.dataset,
        elipy_call          : project.elipy_call + ' --use-fbenv-core',
        elipy_install_call  : project.elipy_install_call,
        frostbite_licensee  : project.frostbite_licensee,
        workspace_root      : project.workspace_root,
        job_label_statebuild: 'statebuild',
    ]
    static Map code_settings = [
        fake_ooa_wrapped_symbol      : false,
        skip_code_build_if_no_changes: true,
        slack_channel_code           : [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        sndbs_enabled                : true,
    ]
    static Map data_settings = [
        enable_daily_data_clean: true,
        enable_lkg_cleaning    : true,
        poolbuild_data         : true,
        slack_channel_data     : [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
    ]
    static Map frosty_settings = [:]
    static Map standard_jobs_settings = code_settings + data_settings + frosty_settings + [
        asset                              : 'ShippingLevels',
        clean_data_validation_pipeline_args: ' --disable-caches true',
        clean_build_validation_job_label   : 'trunk-content-dev-metrics',
        clean_local                        : true,
        timeout_hours_clean_data_validation: 20,
        enable_clean_build_validation      : true,
        enable_lkg_p4_counters             : true,
        extra_data_args                    : ['--pipeline-args -Pipeline.MaxConcurrency --pipeline-args 12 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true'],
        import_avalanche_autotest          : false,
        server_asset                       : 'Game/Setup/Build/DevMPLevels',
        skip_icepick_settings_file         : true,
        strip_symbols                      : false,
        trigger_type_code                  : 'cron',
        trigger_string_code                : 'H 0 * * *',
        use_deprecated_blox_packages       : true,
    ]
    static Map icepick_settings = [
        ignore_icepick_exit_code: false
    ]
    static Map preflight_settings = [:]

    // Matrix definitions for jobs
    static List code_matrix = [
        [
            name: 'win64game', configs: [
            [
                name         : 'release',
                fb_env_values: [
                    'fbenv.extratelemetry=gensln:Labels=gla.build.metrics,Win64_GenSln,CL%code_changelist%',
                    'fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,Win64_BuildSln,CL%code_changelist%',
                ]
            ]
        ]
        ],
        [
            name: 'ps5', configs: [
            [
                name         : 'release',
                fb_env_values: [
                    'fbenv.extratelemetry=gensln:Labels=gla.build.metrics,PS5_GenSln,CL%code_changelist%',
                    'fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,PS5_BuildSln,CL%code_changelist%',
                ]
            ]
        ]
        ],
        [
            name: 'xbsx', configs: [
            [
                name         : 'release',
                fb_env_values: [
                    'fbenv.extratelemetry=gensln:Labels=gla.build.metrics,XBSX_GenSln,CL%code_changelist%',
                    'fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,XBSX_BuildSln,CL%code_changelist%',
                ]
            ]
        ]
        ],
        [
            name: 'tool', configs: [
            [
                name         : 'release',
                fb_env_values: [
                    'fbenv.extratelemetry=gensln:Labels=gla.build.metrics,Tool_GenSln,CL%code_changelist%',
                    'fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,Tool_BuildSln,CL%code_changelist%',
                ]
            ]
        ]
        ],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = [
        [name: '.data.start', args: []],
    ]
    static List data_matrix = [
        [
            name               : 'win64',
            nightly_clean_build: true,
            fb_env_values      : [
                'fbenv.extratelemetry=cook:Labels=gla.cook.metrics',
            ]
        ],
    ]
    static List data_downstream_matrix = []
    static List patchdata_matrix = []
    static List frosty_matrix = []
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = []
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
