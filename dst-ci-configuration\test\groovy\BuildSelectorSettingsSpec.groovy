import com.ea.lib.jobsettings.BuildSelectorSettings
import spock.lang.Specification

class BuildSelectorSettingsSpec extends Specification {

    class BranchFile {
        static Map standard_jobs_settings = [
            workspace_root    : 'workspace-root',
            elipy_call        : 'elipy-call',
            elipy_install_call: 'elipy-install-call',
        ]
        static Map general_settings = [:]
    }

    class MasterFile {
        static Map autotest_branches = [branch: [data_branch: 'data-branch', code_branch: 'code-branch', build_frosted: true]]
    }

    class ProjectFile {
        static String workspace_root = 'E:\\dev'
        static String elipy_call = 'test-call'
        static String elipy_install_call = 'test-elipy-install'
    }

    void "test that we get expected job settings"() {
        when:
        BuildSelectorSettings settings = new BuildSelectorSettings()
        settings.initialize(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            jobLabel == 'data-branch'
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            elipyInstallCall == BranchFile.standard_jobs_settings.elipy_install_call
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} bilbo_select_autotest --code-branch code-branch --data-branch data-branch %is_test_with_loose_files% --platform %platform% --use-azure-drone-build %use_azure_drone_build% --use-shift-build %use_shift_build% --use-spin-build %use_spin_build% --use-latest-drone %use_latest_drone% %required_platforms% %client_platforms% --job-url \"%start_job_url%\" --build-timeout-hours \"%build_timeout_hours%\" --region %region% --config %config%"
        }
    }
}
