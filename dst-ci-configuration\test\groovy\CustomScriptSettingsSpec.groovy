import com.ea.lib.jobsettings.CustomScriptSettings
import com.ea.lib.model.branchsettings.CustomScriptConfiguration
import spock.lang.Specification

class CustomScriptSettingsSpec extends Specification {
    class BranchFile {
        static Map standard_jobs_settings = [
            workspace_root      : 'workspace-root',
            elipy_call          : 'elipy-call',
            elipy_install_call  : 'elipy-install-call',
            job_label_statebuild: 'label',
        ]
        static Map general_settings = [
            custom_script: [
                generate_sync_build_cache             : new CustomScriptConfiguration(
                    enabled: true,
                    executable: 'cmd.exe',
                    executableArgs: '/c',
                    scriptPath: 'Setup\\Drone\\GenerateSyncBuildCache.bat',
                    label: 'build-main && generate-sync-build-cache',
                    timeoutHours: 3,
                    defaultScriptArgs: 'default script args',
                    argumentDescription: 'Optional AutoBuildPath',
                    jobName: 'generate-sync-build-cache',
                    cronTrigger: '@daily',
                    environmentVariables: 'VAR1=FOO,VAR2=BAR'
                ),
                generate_sync_build_cache_without_args: new CustomScriptConfiguration(
                    enabled: true,
                    executable: 'cmd.exe',
                    scriptPath: 'Setup\\Drone\\GenerateSyncBuildCache.bat',
                    label: 'build-main && generate-sync-build-cache',
                    timeoutHours: 3,
                    argumentDescription: 'Optional AutoBuildPath',
                    jobName: 'generate-sync-build-cache',
                ),
            ]
        ]
    }

    class MasterFile {
        static Map branches = [branch: [data_branch: 'data-branch', code_branch: 'code-branch', code_folder: 'dev']]
    }

    class ProjectFile {
        static String name = 'Kingston'
    }

    void "test that we get expected job settings in initializeJob"() {
        when:
        CustomScriptSettings settings = new CustomScriptSettings()
        settings.initializeJob(BranchFile, MasterFile, ProjectFile, 'branch', 'generate_sync_build_cache')
        then:
        with(settings) {
            timeoutMinutes == 180
            jobLabel == 'build-main && generate-sync-build-cache'
            isDisabled == false
            description == 'Runs the following command: cmd.exe /c Setup\\Drone\\GenerateSyncBuildCache.bat'
            buildName == '${JOB_NAME}.${ENV, var="code_changelist"}'
            argumentDescription == 'Optional AutoBuildPath'
            defaultScriptArgs == 'default script args'
            jobName == 'generate-sync-build-cache'
            elipyCmd == 'elipy-call custom_script --executable "cmd.exe" --executable-args "/c"' +
                ' --script-path "Setup\\Drone\\GenerateSyncBuildCache.bat" --script-args "%script_args%"' +
                ' --env-variables "VAR1=FOO,VAR2=BAR"'
        }
    }

    void "test that we get expected job settings in initializeJob without any arguments"() {
        when:
        CustomScriptSettings settings = new CustomScriptSettings()
        settings.initializeJob(BranchFile, MasterFile, ProjectFile, 'branch', 'generate_sync_build_cache_without_args')
        then:
        with(settings) {
            timeoutMinutes == 180
            jobLabel == 'build-main && generate-sync-build-cache'
            isDisabled == false
            description == 'Runs the following command: cmd.exe Setup\\Drone\\GenerateSyncBuildCache.bat'
            buildName == '${JOB_NAME}.${ENV, var="code_changelist"}'
            argumentDescription == 'Optional AutoBuildPath'
            jobName == 'generate-sync-build-cache'
            elipyCmd == 'elipy-call custom_script --executable "cmd.exe" --executable-args ""' +
                ' --script-path "Setup\\Drone\\GenerateSyncBuildCache.bat" --script-args "%script_args%" --env-variables ""'
        }
    }

    void "test that we get expected job settings in initializeStart"() {
        when:
        CustomScriptSettings settings = new CustomScriptSettings()
        settings.initializeStart(BranchFile, MasterFile, ProjectFile, 'branch', 'generate_sync_build_cache')
        then:
        with(settings) {
            description == 'Runs the following command: cmd.exe /c Setup\\Drone\\GenerateSyncBuildCache.bat'
            argumentDescription == 'Optional AutoBuildPath'
            defaultScriptArgs == 'default script args'
            cronTrigger == '@daily'
        }
    }
}
