import com.ea.lib.jobsettings.MapToolSettings
import spock.lang.Specification

class MapToolSettingsSpec extends Specification {

    class BranchFile {
        static Map standard_jobs_settings = [
            branch_name   : 'branch',
            workspace_root: 'workspace-root',
            disable_build : true,
            job_label     : 'build-release && util',
        ]
        static Map general_settings = [:]
    }

    class MasterFile {
        static Map branches = [branch: [data_branch: 'data-branch',
                                        data_folder: 'game',
        ]]
    }

    class ProjectFile {
        static String name = 'Kingston'
    }

    void "test that we get expected job settings in initializeMapToolStart"() {
        when:
        MapToolSettings settings = new MapToolSettings()
        settings.initializeMapToolStart(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            branchName == 'branch'
            dataBranch == 'data-branch'
            dataFolder == 'game'
            jobLabel == 'build-release && util'
            projectName == ProjectFile.name
            description == 'https://jaas.ea.com/browse/COBRA-143 && https://jaas.ea.com/browse/COBRA-138'
            cronTrigger == '@midnight'
            isDisabled == BranchFile.standard_jobs_settings.disable_build
        }
    }
}
