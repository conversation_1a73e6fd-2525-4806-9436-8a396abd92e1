package scripts.schedulers.all

import com.ea.lib.LibCommonCps
import com.ea.lib.LibJenkins
import com.ea.lib.model.JobReference
import com.ea.project.GetBranchFile

def project = ProjectClass(env.project_name)

/**
 * code_preflight_scheduler.groovy
 */
pipeline {
    agent { label 'scheduler' }  // we only wanna job to get Running with 'Maximum Total Concurrent Builds', scheduler == master
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Trigger code preflight jobs') {
            steps {
                script {
                    List<JobReference> jobReferences = []
                    retryOnFailureCause(3, jobReferences) {
                        def cleanLocal = params.clean_local

                        // priority: input custom CL (if enable) > LKG CL from job> 'head' > ''
                        def lkgCodeChangelist = LibJenkins.getLastStableCodeChangelist(env.codepreflight_reference_job)
                        def lkgDataChangelist = LibJenkins.getLastStableDataChangelist(env.datapreflight_reference_job)
                        def syncChangeslist = ''
                        if (params.sync_to_head.toString() == 'true') {
                            syncChangeslist = 'now'
                        }

                        boolean enableCustomChangelist = env.enable_custom_changelist?.toBoolean()
                        def codeChangelist = enableCustomChangelist ? (params.P4CL ?: lkgCodeChangelist ?: syncChangeslist) : (lkgCodeChangelist ?: syncChangeslist)
                        def dataChangelist = enableCustomChangelist && params.P4CL && project.frostbite_syncer_setup ? codeChangelist : lkgDataChangelist ?: syncChangeslist

                        // warmer job use code_changelist as unshelve_changeslist if set to "do"
                        def unshelveChangelist = params.only_warm_machine == 'do' ? codeChangelist : params.unshelve_changelist

                        // this is what we pass down to downstream job
                        def args = [
                            string(name: 'unshelve_changelist', value: unshelveChangelist),
                            string(name: 'code_changelist', value: codeChangelist),
                            string(name: 'data_changelist', value: dataChangelist),
                            string(name: 'clean_local', value: cleanLocal),
                            string(name: 'only_warm_machine', value: params.only_warm_machine),
                        ]
                        def injectMap = [
                            'code_changelist'    : codeChangelist,
                            'data_changelist'    : dataChangelist,
                            'unshelve_changelist': unshelveChangelist,
                        ]
                        EnvInject(currentBuild, injectMap)
                        currentBuild.displayName = env.JOB_NAME + '.' + unshelveChangelist + '.' + params.preflighter

                        def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
                        def branchInfo = branchfile.general_settings + branchfile.standard_jobs_settings + branchfile.preflight_settings
                        def codePreflightMatrix = branchfile.code_preflight_matrix

                        if (branchInfo.trigger_ado) {
                            LibCommonCps.triggerAdoCodePreflights(this, branchfile, params.preflighter, unshelveChangelist, codeChangelist)
                        }

                        def jobs = [:]
                        for (platform in codePreflightMatrix) {
                            for (config in platform.configs) {
                                String separator = platform.nomaster_platform?.toBoolean() ? '.nomaster.' : '.'
                                def onPremJobName = "${env.branch_name}.code.preflight.${platform.name}${separator}${config}"
                                def cloudJobName = "${env.branch_name}.code.preflight.${platform.name}${separator}${config}.cloud"
                                def jobName = onPremJobName
                                List<String> labels = [env.branch_name, 'code', platform.name]
                                if (branchInfo.enable_hybrid_agents && !LibJenkins.onPremAgentsWithLabelsAvailable(labels)) {
                                   //jobName = cloudJobName
                                    echo 'Hybrid preflights are disabled. Putting job on onprem queue.'
                                }
                                labels += platform.nomaster_platform ? ['code-nomaster'] : []
                                if (params.force_rebuild || NeedsRebuildCodePreflight(onPremJobName, unshelveChangelist, 20) &&
                                    NeedsRebuildCodePreflight(cloudJobName, unshelveChangelist, 20)) {
                                    jobs[jobName] = {
                                        def downstreamJob = build(job: jobName, parameters: args, propagate: false)
                                        jobReferences << new JobReference(downstreamJob: downstreamJob, jobName: jobName, parameters: args, propagate: false)
                                        LibJenkins.printRunningJobs(this)
                                    }
                                }
                            }
                        }
                        parallel(jobs)
                    }
                }
            }
        }
        stage('Scan for errors') { steps { ScanForErrors(currentBuild, true) } }
    }
    post {
        always {
            emailext(
                to: '<EMAIL>',
                subject: 'preflight result',
                body: '${SCRIPT, template="email-pipeline-preflight.groovy"}',
                mimeType: 'text/html',
                presendScript: '${SCRIPT, template="preflight-email-presend-pipeline.groovy"}'
            )
        }
        failure {
            sendAlertIfFailedConsecutiveTimes env.JOB_NAME, env.JOB_URL, env.BUILD_NUMBER, '#cobra-red-pod-alerts', env.project_short_name, 5
        }
    }
}
