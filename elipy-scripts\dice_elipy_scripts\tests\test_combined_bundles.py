"""
test_combined_bundles.py

Unit tests for combined_bundles module.
"""

import os
import unittest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from dice_elipy_scripts.combined_bundles import cli
from elipy2.exceptions import ELIPYException
from unittest.mock import patch, MagicMock


class TestCombinedBundles(unittest.TestCase):
    """Test cases for combined_bundles CLI command."""

    def setUp(self):
        """Set up test fixtures."""
        self.runner = CliRunner()
        self.platform = "win64"
        self.data_directory = "Data"
        self.code_branch = "build-main-dre"
        self.code_changelist = "436418"
        self.data_branch = "build-main-dre"
        self.data_changelist = "436418"
        self.combine_code_branch = "build-release"
        self.combine_code_changelist = "436400"
        self.combine_data_branch = "build-release"
        self.combine_data_changelist = "436400"

    @patch("dice_elipy_scripts.combined_bundles.add_sentry_tags")
    @patch("elipy2.running_processes.kill")
    @patch("elipy2.frostbite.icepick.IcepickUtils.clean_local_frosty")
    @patch("elipy2.avalanche.combine")
    @patch("elipy2.filer.FilerUtils")
    @patch("elipy2.local_paths.get_local_bundles_path")
    def test_cli_basic_execution_success(
        self,
        mock_get_local_bundles_path,
        mock_filer_utils,
        mock_avalanche_combine,
        mock_clean_frosty,
        mock_kill,
        mock_sentry,
    ):
        """Test basic CLI execution without delta bundles."""
        # Setup mocks
        mock_get_local_bundles_path.side_effect = [
            "/path/to/deployed_bundles_combine",
            "/path/to/deployed_bundles_main",
            "/path/to/deployed_bundles_combine_second",
        ]

        # Mock the FilerUtils instance and its methods
        mock_filer_instance = MagicMock()
        mock_filer_utils.return_value = mock_filer_instance
        mock_filer_instance.fetch_code.return_value = None
        mock_filer_instance.fetch_head_bundles.return_value = None
        mock_filer_instance.deploy_avalanche_combine_output.return_value = None

        args = [
            self.platform,
            "--data-directory",
            self.data_directory,
            "--code-branch",
            self.code_branch,
            "--code-changelist",
            self.code_changelist,
            "--data-branch",
            self.data_branch,
            "--data-changelist",
            self.data_changelist,
            "--combine-code-branch",
            self.combine_code_branch,
            "--combine-code-changelist",
            self.combine_code_changelist,
            "--combine-data-branch",
            self.combine_data_branch,
            "--combine-data-changelist",
            self.combine_data_changelist,
        ]

        result = self.runner.invoke(cli, args)

        self.assertEqual(result.exit_code, 0)
        # Verify key functions were called
        mock_kill.assert_called_once()
        mock_clean_frosty.assert_called_once()
        mock_filer_instance.fetch_code.assert_called_once()

    @patch("dice_elipy_scripts.combined_bundles.add_sentry_tags")
    @patch("elipy2.running_processes.kill")
    @patch("elipy2.frostbite.icepick.IcepickUtils.clean_local_frosty")
    @patch("elipy2.avalanche.combine")
    @patch("elipy2.filer.FilerUtils")
    @patch("elipy2.local_paths.get_local_bundles_path")
    def test_fetch_head_bundles_uses_combine_bundles_dir_name(
        self,
        mock_get_local_bundles_path,
        mock_filer_utils,
        mock_avalanche_combine,
        mock_clean_frosty,
        mock_kill,
        mock_sentry,
    ):
        """Test that fetch_head_bundles is called with the correct bundles_dir_name."""
        # Setup mocks
        mock_get_local_bundles_path.side_effect = [
            "/path/to/deployed_bundles_combine",
            "/path/to/deployed_bundles_main",
            "/path/to/deployed_bundles_combine_second",
        ]

        # Mock the FilerUtils instance and its methods
        mock_filer_instance = MagicMock()
        mock_filer_utils.return_value = mock_filer_instance
        mock_filer_instance.fetch_code.return_value = None
        mock_filer_instance.fetch_head_bundles.return_value = None
        mock_filer_instance.deploy_avalanche_combine_output.return_value = None

        args = [
            self.platform,
            "--data-directory",
            self.data_directory,
            "--code-branch",
            self.code_branch,
            "--code-changelist",
            self.code_changelist,
            "--data-branch",
            self.data_branch,
            "--data-changelist",
            self.data_changelist,
            "--combine-code-branch",
            self.combine_code_branch,
            "--combine-code-changelist",
            self.combine_code_changelist,
            "--combine-data-branch",
            self.combine_data_branch,
            "--combine-data-changelist",
            self.combine_data_changelist,
        ]

        result = self.runner.invoke(cli, args)

        self.assertEqual(result.exit_code, 0)

        # Verify fetch_head_bundles calls
        self.assertEqual(mock_filer_instance.fetch_head_bundles.call_count, 2)

        # Check the first call's kwargs for bundles_dir_name
        first_call_kwargs = mock_filer_instance.fetch_head_bundles.call_args_list[0][1]
        self.assertEqual(first_call_kwargs["bundles_dir_name"], "combine_bundles")

        # Check the second call's kwargs for bundles_dir_name
        second_call_kwargs = mock_filer_instance.fetch_head_bundles.call_args_list[1][1]
        self.assertEqual(second_call_kwargs["bundles_dir_name"], "combine_bundles")

    def test_delta_bundles_missing_disc_params_raises_exception(self):
        """Test that missing disc baseline parameters raise exception."""
        args = [
            self.platform,
            "--data-directory",
            self.data_directory,
            "--code-branch",
            self.code_branch,
            "--code-changelist",
            self.code_changelist,
            "--data-branch",
            self.data_branch,
            "--data-changelist",
            self.data_changelist,
            "--combine-code-branch",
            self.combine_code_branch,
            "--combine-code-changelist",
            self.combine_code_changelist,
            "--combine-data-branch",
            self.combine_data_branch,
            "--combine-data-changelist",
            self.combine_data_changelist,
            "--create-delta-bundles",
            "--first-patch",
            # Missing disc baseline parameters
        ]

        result = self.runner.invoke(cli, args)
        self.assertNotEqual(result.exit_code, 0)
        self.assertIn("disc baseline parameters are required", str(result.exception))

    def test_delta_bundles_non_first_patch_missing_patch_params_raises_exception(self):
        """Test that missing patch baseline parameters for non-first patch raise exception."""
        args = [
            self.platform,
            "--data-directory",
            self.data_directory,
            "--code-branch",
            self.code_branch,
            "--code-changelist",
            self.code_changelist,
            "--data-branch",
            self.data_branch,
            "--data-changelist",
            self.data_changelist,
            "--combine-code-branch",
            self.combine_code_branch,
            "--combine-code-changelist",
            self.combine_code_changelist,
            "--combine-data-branch",
            self.combine_data_branch,
            "--combine-data-changelist",
            self.combine_data_changelist,
            "--create-delta-bundles",
            "--disc-code-branch",
            "disc-code-branch",
            "--disc-code-changelist",
            "123456",
            "--disc-data-branch",
            "disc-data-branch",
            "--disc-data-changelist",
            "123456",
            # Missing patch baseline parameters for non-first patch
        ]

        result = self.runner.invoke(cli, args)
        self.assertNotEqual(result.exit_code, 0)
        self.assertIn("patch baseline parameters are required", str(result.exception))

    def test_combined_output_folder_naming(self):
        """Test that the combined output folder path"""
        code_branch = "main-stream"
        code_changelist = "12345"
        data_branch = "main-data"
        data_changelist = "54321"
        combine_code_branch = "combine-stream"
        combine_code_changelist = "67890"
        combine_data_branch = "combine-data"
        combine_data_changelist = "09876"
        expected_folder = os.path.join(
            "combined_bundles",
            code_branch,
            code_changelist,
            data_branch,
            data_changelist,
            combine_code_branch,
            combine_code_changelist,
            combine_data_branch,
            combine_data_changelist,
        )
        actual_folder = os.path.join(
            "combined_bundles",
            code_branch,
            str(code_changelist),
            data_branch,
            str(data_changelist),
            combine_code_branch,
            str(combine_code_changelist),
            combine_data_branch,
            str(combine_data_changelist),
        )
        self.assertEqual(actual_folder, expected_folder)

    @patch("dice_elipy_scripts.combined_bundles.add_sentry_tags")
    @patch("elipy2.running_processes.kill")
    @patch("elipy2.frostbite.icepick.IcepickUtils.clean_local_frosty")
    @patch("elipy2.avalanche.combine")
    @patch("elipy2.filer.FilerUtils")
    @patch("elipy2.local_paths.get_local_bundles_path")
    def test_xbsx_platform_uses_smart_delivery_settings(
        self,
        mock_get_local_bundles_path,
        mock_filer_utils,
        mock_combine,
        mock_clean_frosty,
        mock_kill,
        mock_sentry,
    ):
        """Test that xbsx platform uses smart delivery settings file."""
        # Setup mocks
        mock_get_local_bundles_path.side_effect = [
            "/path/to/deployed_bundles_combine",
            "/path/to/deployed_bundles_main",
            "/path/to/deployed_bundles_combine_second",
        ]

        mock_filer_instance = MagicMock()
        mock_filer_utils.return_value = mock_filer_instance
        mock_filer_instance.fetch_code.return_value = None
        mock_filer_instance.fetch_head_bundles.return_value = None
        mock_filer_instance.deploy_avalanche_combine_output.return_value = None

        args = [
            "xbsx",  # Platform that should use smart delivery
            "--data-directory",
            self.data_directory,
            "--code-branch",
            self.code_branch,
            "--code-changelist",
            self.code_changelist,
            "--data-branch",
            self.data_branch,
            "--data-changelist",
            self.data_changelist,
            "--combine-code-branch",
            self.combine_code_branch,
            "--combine-code-changelist",
            self.combine_code_changelist,
            "--combine-data-branch",
            self.combine_data_branch,
            "--combine-data-changelist",
            self.combine_data_changelist,
        ]

        result = self.runner.invoke(cli, args)

        self.assertEqual(result.exit_code, 0)
        args_called = mock_combine.call_args[1]
        self.assertIn("project-combine-hres-smart-delivery.yaml", args_called["extra_combine_args"])

    @patch("dice_elipy_scripts.combined_bundles.add_sentry_tags")
    @patch("elipy2.running_processes.kill")
    @patch("elipy2.frostbite.icepick.IcepickUtils.clean_local_frosty")
    @patch("elipy2.avalanche.combine")
    @patch("elipy2.filer.FilerUtils")
    @patch("elipy2.local_paths.get_local_bundles_path")
    def test_non_xbsx_platform_uses_regular_settings(
        self,
        mock_get_local_bundles_path,
        mock_filer_utils,
        mock_combine,
        mock_clean_frosty,
        mock_kill,
        mock_sentry,
    ):
        """Test that non-xbsx platforms use regular settings file."""
        # Setup mocks
        mock_get_local_bundles_path.side_effect = [
            "/path/to/deployed_bundles_combine",
            "/path/to/deployed_bundles_main",
            "/path/to/deployed_bundles_combine_second",
        ]

        mock_filer_instance = MagicMock()
        mock_filer_utils.return_value = mock_filer_instance
        mock_filer_instance.fetch_code.return_value = None
        mock_filer_instance.fetch_head_bundles.return_value = None
        mock_filer_instance.deploy_avalanche_combine_output.return_value = None

        args = [
            "win64",  # Platform that should use regular settings
            "--data-directory",
            self.data_directory,
            "--code-branch",
            self.code_branch,
            "--code-changelist",
            self.code_changelist,
            "--data-branch",
            self.data_branch,
            "--data-changelist",
            self.data_changelist,
            "--combine-code-branch",
            self.combine_code_branch,
            "--combine-code-changelist",
            self.combine_code_changelist,
            "--combine-data-branch",
            self.combine_data_branch,
            "--combine-data-changelist",
            self.combine_data_changelist,
        ]

        result = self.runner.invoke(cli, args)

        self.assertEqual(result.exit_code, 0)
        # Verify that combine was called with regular settings
        args_called = mock_combine.call_args[1]
        self.assertIn("project-combine-hres.yaml", args_called["extra_combine_args"])


if __name__ == "__main__":
    unittest.main()
