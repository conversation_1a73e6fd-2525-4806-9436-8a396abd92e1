package com.ea.lib

import static java.lang.System.out

import com.ea.exceptions.CobraException
import com.ea.project.Cobra

/**
 * All source control related methods.
 */
class LibScm {

    static void cobraGit(context, String url, project = null) {
        cobraGit(context, [(url): Cobra.git_targetdir_ci], project)
    }

    static void cobraGit(context, Map urls, project = null) {
        context.with {
            for (repo in urls) {
                git {
                    remote {
                        name(Cobra.git_name_ci)
                        credentials(Cobra.git_creds_ci)
                        url(repo.key)
                    }
                    branch(Cobra.git_branch_ci)
                    extensions {
                        cloneOptions {
                            timeout(20) // Extend git timeout to 20 minutes
                            shallow(true)
                            noTags(true)
                        }
                        pathRestriction { // Don't want this to trigger builds
                            includedRegions('')
                            excludedRegions('.*')
                        }
                        relativeTargetDirectory(repo.value)
                    }
                    browser { gitLab(Cobra.git_browser_url_ci, Cobra.git_browser_version_ci) }
                }
            }
        }

        if (Cobra.git_url_elipy_setup in urls && project?.vault_credentials && project?.vault_variable) {
            context.item.with {
                configure { contextJob ->
                    contextJob / 'buildWrappers' / 'org.jenkinsci.plugins.credentialsbinding.impl.SecretBuildWrapper' / 'bindings' << 'org.jenkinsci.plugins.credentialsbinding.impl.StringBinding' {
                        variable(project.vault_variable)
                        credentialsId(project.vault_credentials)
                    }
                }
            }
        }
    }

    /**
     * Syncs the main JobDSL repository.
     */
    static void git_ci(job) {
        job.with {
            scm {
                cobraGit(delegate, Cobra.git_url_ci)
            }
        }
    }

    /**
     * Syncs the elipy-setup repository
     */
    // static void git_elipy_sync(job, project = null) {
    // 	job.with {
    // 		scm {
    // 			cobraGit(delegate, Cobra.git_url_elipy_setup, project)
    // 		}
    // 	}
    // }

    /**
     * Sync Code and Data
     * Simplified method, assuming the same project for code and data.
     * frostbite_syncer_setup means code and data are on the same stream,
     * in which case so we should only sync code.
     */
    static void sync_code_and_data(def job, def project, def branch_info,
                                   String pin_cl_data = '', String pin_cl_code = '',
                                   String p4_data_creds = '', String p4_code_creds = '',
                                   def modifiers = []
    ) {
        if (project.frostbite_syncer_setup) {
            out.println('frostbit_syncer_setup is true. Syncing code only.')
            sync_code(job, project, branch_info, pin_cl_code, modifiers)
        } else {
            sync_code_and_data_different_projects(job, project, project, branch_info, pin_cl_data,
                pin_cl_code, p4_data_creds, p4_code_creds, modifiers
            )
        }
    }

    /**
     * Sync Code and Data
     * Method allowing different projects for code and data.
     */
    static void sync_code_and_data_different_projects(def job, def code_project, def data_project, def branch_info,
                                                      String pin_cl_data = '', String pin_cl_code = '',
                                                      String p4_data_creds = '', String p4_code_creds = '',
                                                      def modifiers = []
    ) {
        String default_p4_code_creds = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'p4_code_creds', '', code_project)
        String default_p4_data_creds = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'p4_data_creds', '', data_project)
        String code_creds = p4_code_creds ?: default_p4_code_creds
        String data_creds = p4_data_creds ?: default_p4_data_creds
        String p4_code_root = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'p4_code_root', '', code_project)
        String p4_data_root = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'p4_data_root', '', data_project)
        String p4_code_client = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'p4_code_client', '', code_project)
        String p4_data_client = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'p4_data_client', '', data_project)
        job.with {
            multiscm {
                for (p4_server in code_project.p4_extra_servers) {
                    // Preview a Perforce server to create a token there, for jobs requiring this.
                    perforce {
                        credential(p4_server.p4_creds)
                        workspace {
                            def workspace_type = p4_server.workspace_type ?: 'streams'
                            def workspace_name_postfix = p4_server.workspace_name_postfix ?: ''
                            def workspace_name = p4_server.p4_creds + workspace_name_postfix + '-previewonly'
                            switch (workspace_type) {
                                case 'streams':
                                    streamSpec {
                                        charset('none')
                                        pinHost(true)
                                        streamName(p4_server.p4_stream)
                                        format(workspace_name)
                                    }
                                    break
                                case 'manual':
                                    manualSpec {
                                        charset('none')
                                        pinHost(true)
                                        name(workspace_name)
                                        cleanup(false)
                                        syncID('')
                                        spec {
                                            view(p4_server.view)
                                            allwrite(false)
                                            clobber(true)
                                            compress(false)
                                            locked(false)
                                            modtime(false)
                                            rmdir(false)
                                            streamName('')
                                            line('LOCAL')
                                            changeView('')
                                            type('WRITABLE')
                                            serverID('')
                                            backup(true)
                                        }
                                    }
                                    break
                                default:
                                    throw new CobraException('Unsupported workspace type: ' + workspace_type)
                            }
                        }
                        populate {
                            previewOnly { // PreviewOnly workspaces only check if there's new files, they don't sync anything.
                                quiet(true)
                                pin('')
                            }
                        }
                        browser { swarm { url(code_project.p4_browser_url) } }
                    }
                }
                perforce {
                    credential(code_creds)
                    workspace {
                        streamSpec {
                            charset('none')
                            pinHost(true)
                            streamName(p4_code_root + '/' + branch_info.code_folder + '/' + branch_info.code_branch)
                            format(p4_code_client)
                        }
                    }
                    populate {
                        syncOnly {
                            have(true)
                            force(false)
                            modtime(false)
                            quiet(true)
                            revert(true)
                            pin(pin_cl_code)
                            parallel {
                                enable(true)
                                path('')
                                threads('8')
                                minfiles('0')
                                minbytes('0')
                            }
                        }
                    }
                    browser { swarm { url(code_project.p4_browser_url) } }
                }
                perforce {
                    credential(data_creds)
                    workspace {
                        streamSpec {
                            charset('none')
                            pinHost(true)
                            streamName(p4_data_root + '/' + branch_info.data_folder + '/' + branch_info.data_branch)
                            format(p4_data_client)
                        }
                    }
                    populate {
                        syncOnly {
                            have(true)
                            force(false)
                            modtime(false)
                            quiet(true)
                            revert(true)
                            pin(pin_cl_data)
                            parallel {
                                enable(true)
                                path('')
                                threads('8')
                                minfiles('0')
                                minbytes('0')
                            }
                        }
                    }
                    browser { swarm { url(data_project.p4_browser_url) } }
                }
            }
        }
    }

    // Sync Code
    static void sync_code(def job, def project, def branch_info, String pin_cl_code = '', modifiers = []) {
        String p4_code_creds = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'p4_code_creds', '', project)
        String p4_code_root = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'p4_code_root', '', project)
        String p4_code_client = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'p4_code_client', '', project)
        job.with {
            multiscm {
                for (p4_server in project.p4_extra_servers) {
                    // Preview a Perforce server to create a token there, for jobs requiring this.
                    perforce {
                        credential(p4_server.p4_creds)
                        workspace {
                            def workspace_type = p4_server.workspace_type ?: 'streams'
                            def workspace_name_postfix = p4_server.workspace_name_postfix ?: ''
                            def workspace_name = p4_server.p4_creds + workspace_name_postfix + '-previewonly'
                            switch (workspace_type) {
                                case 'streams':
                                    streamSpec {
                                        charset('none')
                                        pinHost(true)
                                        streamName(p4_server.p4_stream)
                                        format(workspace_name)
                                    }
                                    break
                                case 'manual':
                                    manualSpec {
                                        charset('none')
                                        pinHost(true)
                                        name(workspace_name)
                                        cleanup(false)
                                        syncID('')
                                        spec {
                                            view(p4_server.view)
                                            allwrite(false)
                                            clobber(true)
                                            compress(false)
                                            locked(false)
                                            modtime(false)
                                            rmdir(false)
                                            streamName('')
                                            line('LOCAL')
                                            changeView('')
                                            type('WRITABLE')
                                            serverID('')
                                            backup(true)
                                        }
                                    }
                                    break
                                default:
                                    throw new CobraException('Unsupported workspace type: ' + workspace_type)
                            }
                        }
                        populate {
                            previewOnly { // PreviewOnly workspaces only check if there's new files, they don't sync anything.
                                quiet(true)
                                pin('')
                            }
                        }
                        browser { swarm { url(project.p4_browser_url) } }
                    }
                }
                perforce {
                    credential(p4_code_creds)
                    workspace {
                        streamSpec {
                            charset('none')
                            pinHost(true)
                            streamName(p4_code_root + '/' + branch_info.code_folder + '/' + branch_info.code_branch)
                            format(p4_code_client)
                        }
                    }
                    populate {
                        syncOnly {
                            have(true)
                            force(false)
                            modtime(false)
                            quiet(true)
                            revert(true)
                            pin(pin_cl_code)
                            parallel {
                                enable(true)
                                path('')
                                threads('8')
                                minfiles('0')
                                minbytes('0')
                            }
                        }
                    }
                    browser { swarm { url(project.p4_browser_url) } }
                }
                perforce {
                    credential(p4_code_creds)
                    workspace {
                        streamSpec {
                            charset('none')
                            pinHost(true)
                            streamName(p4_code_root + '/' + branch_info.code_folder + '/' + branch_info.code_branch)
                            format(p4_code_client)
                        }
                    }
                    populate {
                        syncOnly {
                            have(true)
                            force(false)
                            modtime(false)
                            quiet(true)
                            revert(true)
                            pin(pin_cl_code)
                            parallel {
                                enable(true)
                                path('')
                                threads('8')
                                minfiles('0')
                                minbytes('0')
                            }
                        }
                    }
                    browser { swarm { url(project.p4_browser_url) } }
                }
            }
        }
    }

    // Sync Code (Target)
    // Sync Data (Target)
    // Preview Data (Source)
    // Integrates Data using Code from the Target branch (Default behaviour).
    static void integrate_data_default(def job, def source_project, def target_project, def branch_info, def src_pin_cl_data = '') {
        integrate_data(job, source_project, target_project, branch_info.source_folder, branch_info.source_branch, branch_info.target_folder, branch_info.target_branch, src_pin_cl_data, target_project, branch_info.target_folder, branch_info.target_branch)
    }

    // Sync Code (Source)
    // Sync Data (Target)
    // Preview Data (Source)
    // Integrates Data using Code from the Source branch (because Target branch doesn't posses any Code).
    static void integrate_data_no_code(def job, def source_project, def target_project, def branch_info, def src_pin_cl_data = '') {
        integrate_data(job, source_project, target_project, branch_info.source_folder, branch_info.source_branch, branch_info.target_folder, branch_info.target_branch, src_pin_cl_data, source_project, branch_info.source_folder, branch_info.source_branch)
    }

    // Sync Code (Custom)
    // Sync Data (Target)
    // Preview Data (Source)
    // Integrates Data using Code from a custom branch (because neither Source nor Target branch do posses any Code).
    static void integrate_data_custom(def job, def source_project, def target_project, def branch_info, def src_pin_cl_data = '') {
        integrate_data(job, source_project, target_project, branch_info.source_folder, branch_info.source_branch, branch_info.target_folder, branch_info.target_branch, src_pin_cl_data, branch_info.code_project, branch_info.code_folder, branch_info.code_branch)
    }

    // Sync Code (Code branch)
    // Sync Data (Target)
    // Preview Data (Source)
    // Default method, implemented in different ways above.
    static void integrate_data(def job, def source_project, def target_project, def src_folder, def src_branch, def tgt_folder, def tgt_branch, def src_pin_cl_data, def code_project, def code_folder, def code_branch, def src_pin_cl_code = '', def branch_info = [:]) {
        String p4_code_creds_code_project = LibCommonNonCps.get_setting_value(branch_info, ['code_project'], 'p4_code_creds', '', code_project)
        String p4_code_root_code_project = LibCommonNonCps.get_setting_value(branch_info, ['code_project'], 'p4_code_root', '', code_project)
        job.with {
            multiscm {
                perforce {
                    credential(p4_code_creds_code_project)
                    workspace {
                        streamSpec {
                            charset('none')
                            pinHost(true)
                            streamName(p4_code_root_code_project + '/' + code_folder + '/' + code_branch)
                            format(code_project.p4_code_client)
                        }
                    }
                    populate {
                        syncOnly {
                            have(true)
                            force(false)
                            modtime(false)
                            quiet(true)
                            revert(true)
                            pin(src_pin_cl_code)
                            parallel {
                                enable(true)
                                path('')
                                threads('8')
                                minfiles('0')
                                minbytes('0')
                            }
                        }
                    }
                    filter {
                        pathFilter { // Ignore changes in this path
                            path(p4_code_root_code_project + '/' + code_folder + '/' + code_branch)
                        }
                    }
                    browser { swarm { url(code_project.p4_browser_url) } }
                }
                perforce {
                    credential(target_project.p4_data_creds)
                    workspace {
                        streamSpec {
                            charset('none')
                            pinHost(true)
                            streamName(target_project.p4_data_root + '/' + tgt_folder + '/' + tgt_branch)
                            format(target_project.p4_data_client)
                        }
                    }
                    populate {
                        syncOnly {
                            have(true)
                            force(false)
                            modtime(false)
                            quiet(true)
                            revert(true)
                            pin('')
                            parallel {
                                enable(true)
                                path('')
                                threads('8')
                                minfiles('0')
                                minbytes('0')
                            }
                        }
                    }
                    filter {
                        pathFilter { // Ignore changes in this path
                            path(target_project.p4_data_root + '/' + tgt_folder + '/' + tgt_branch)
                        }
                    }
                    browser { swarm { url(target_project.p4_browser_url) } }
                }
                perforce {
                    credential(source_project.p4_data_creds)
                    workspace {
                        streamSpec {
                            charset('none')
                            pinHost(true)
                            streamName(source_project.p4_data_root + '/' + src_folder + '/' + src_branch)
                            format('jenkins-' + source_project.short_name + '-${NODE_NAME}-${JOB_NAME}-previewdataintegrate')
                        }
                    }
                    populate {
                        previewOnly { // PreviewOnly workspaces only check if there's new files, they don't sync anything.
                            quiet(true)
                            pin(src_pin_cl_data)
                        }
                    }
                    browser { swarm { url(source_project.p4_browser_url) } }
                }
                // cobraGit(delegate, Cobra.git_url_elipy_setup, target_project)
            }
        }
    }

    // Code integrations
    // Sync Code (Target)
    // Preview Code (Source)
    static void integrate_code(def job, def source_project, def target_project, def branch_info, def src_pin_cl_code = '') {
        String p4_code_root_source = LibCommonNonCps.get_setting_value(branch_info, ['source'], 'p4_code_root', '', source_project)
        String p4_code_root_target = LibCommonNonCps.get_setting_value(branch_info, ['target'], 'p4_code_root', '', target_project)
        String p4_code_creds_source = LibCommonNonCps.get_setting_value(branch_info, ['source'], 'p4_code_creds', '', source_project)
        String p4_code_creds_target = LibCommonNonCps.get_setting_value(branch_info, ['target'], 'p4_code_creds', '', target_project)
        job.with {
            multiscm {
                perforce {
                    credential(p4_code_creds_target)
                    workspace {
                        streamSpec {
                            charset('none')
                            pinHost(true)
                            streamName(p4_code_root_target + '/' + branch_info.target_folder + '/' + branch_info.target_branch)
                            format(target_project.p4_code_client)
                        }
                    }
                    populate {
                        syncOnly {
                            revert(true)
                            have(true)
                            force(false)
                            modtime(false)
                            quiet(true)
                            pin('')
                            parallel {
                                enable(true)
                                path('')
                                threads('8')
                                minfiles('0')
                                minbytes('0')
                            }
                        }
                    }
                    filter {
                        pathFilter { // Ignore changes in this path
                            path(p4_code_root_target + '/' + branch_info.target_folder + '/' + branch_info.target_branch)
                        }
                    }
                    browser { swarm { url(target_project.p4_browser_url) } }
                }
                perforce {
                    credential(p4_code_creds_source)
                    workspace {
                        streamSpec {
                            charset('none')
                            pinHost(true)
                            streamName(p4_code_root_source + '/' + branch_info.source_folder + '/' + branch_info.source_branch)
                            format('jenkins-' + source_project.short_name + '-${NODE_NAME}-${JOB_NAME}-previewcodeintegrate')
                        }
                    }
                    populate {
                        previewOnly { // PreviewOnly workspaces only check if there's new files, they don't sync anything.
                            quiet(true)
                            pin(src_pin_cl_code)
                        }
                    }
                    browser { swarm { url(source_project.p4_browser_url) } }
                }
                // cobraGit(delegate, Cobra.git_url_elipy_setup, target_project)
            }
        }
    }

    // Code integrations
    // Sync Code (Target)
    // Preview Code (Source)
    //sync git cherrypick scripts
    static void integrate_code_cherrypick(def job, def source_project, def target_project, def branch_info, def src_pin_cl_code = '') {
        String p4_code_root_source = LibCommonNonCps.get_setting_value(branch_info, ['source'], 'p4_code_root', '', source_project)
        String p4_code_root_target = LibCommonNonCps.get_setting_value(branch_info, ['target'], 'p4_code_root', '', target_project)
        String p4_code_creds_source = LibCommonNonCps.get_setting_value(branch_info, ['source'], 'p4_code_creds', '', source_project)
        String p4_code_creds_target = LibCommonNonCps.get_setting_value(branch_info, ['target'], 'p4_code_creds', '', target_project)
        job.with {
            multiscm {
                perforce {
                    credential(p4_code_creds_target)
                    workspace {
                        streamSpec {
                            charset('none')
                            pinHost(true)
                            streamName(p4_code_root_target + '/' + branch_info.target_folder + '/' + branch_info.target_branch)
                            format(target_project.p4_code_client)
                        }
                    }
                    populate {
                        syncOnly {
                            revert(true)
                            have(true)
                            force(false)
                            modtime(false)
                            quiet(true)
                            pin('')
                            parallel {
                                enable(true)
                                path('')
                                threads('8')
                                minfiles('0')
                                minbytes('0')
                            }
                        }
                    }
                    filter {
                        pathFilter { // Ignore changes in this path
                            path(p4_code_root_target + '/' + branch_info.target_folder + '/' + branch_info.target_branch)
                        }
                    }
                    browser { swarm { url(target_project.p4_browser_url) } }
                }
                perforce {
                    credential(p4_code_creds_source)
                    workspace {
                        streamSpec {
                            charset('none')
                            pinHost(true)
                            streamName(p4_code_root_source + '/' + branch_info.source_folder + '/' + branch_info.source_branch)
                            format('jenkins-' + source_project.short_name + '-${NODE_NAME}-${JOB_NAME}-previewcodeintegrate')
                        }
                    }
                    populate {
                        previewOnly { // PreviewOnly workspaces only check if there's new files, they don't sync anything.
                            quiet(true)
                            pin(src_pin_cl_code)
                        }
                    }
                    browser { swarm { url(source_project.p4_browser_url) } }
                }
                cobraGit(
                    delegate,
                    [
                        //	(Cobra.git_url_elipy_setup): Cobra.git_targetdir_ci,
                        '*****************:dice/tools-and-workflows/integrationci.git': 'cherrypick_copy',
                    ],
                    target_project
                )
            }
        }
    }

    // Data integrations
    // Sync Code (Target)
    // Sync Data (Target)
    // Preview Data (Source)
    //sync git cherrypick scripts
    static void integrate_data_cherrypick(def job, def source_project, def target_project, def branch_info, def src_pin_cl_code = '') {
        String p4_code_root_source = LibCommonNonCps.get_setting_value(branch_info, ['source'], 'p4_code_root', '', source_project)
        String p4_code_root_target = LibCommonNonCps.get_setting_value(branch_info, ['target'], 'p4_code_root', '', target_project)
        String p4_code_creds_target = LibCommonNonCps.get_setting_value(branch_info, ['target'], 'p4_code_creds', '', target_project)
        job.with {
            multiscm {
                perforce {
                    credential(p4_code_creds_target)
                    workspace {
                        streamSpec {
                            charset('none')
                            pinHost(true)
                            streamName(p4_code_root_target + '/' + branch_info.target_folder + '/' + branch_info.target_branch)
                            format(target_project.p4_code_client)
                        }
                    }
                    populate {
                        syncOnly {
                            have(true)
                            force(false)
                            modtime(false)
                            quiet(true)
                            revert(true)
                            pin(src_pin_cl_code)
                            parallel {
                                enable(true)
                                path('')
                                threads('8')
                                minfiles('0')
                                minbytes('0')
                            }
                        }
                    }
                    filter {
                        pathFilter { // Ignore changes in this path
                            path(p4_code_root_target + '/' + branch_info.target_folder + '/' + branch_info.target_branch)
                        }
                    }
                    browser { swarm { url(target_project.p4_browser_url) } }
                }
                perforce {
                    credential(target_project.p4_data_creds)
                    workspace {
                        streamSpec {
                            charset('none')
                            pinHost(true)
                            streamName(target_project.p4_data_root + '/' + branch_info.target_folder + '/' + branch_info.target_branch)
                            format(target_project.p4_data_client)
                        }
                    }
                    populate {
                        syncOnly {
                            have(true)
                            force(false)
                            modtime(false)
                            quiet(true)
                            revert(true)
                            pin('')
                            parallel {
                                enable(true)
                                path('')
                                threads('8')
                                minfiles('0')
                                minbytes('0')
                            }
                        }
                    }
                    filter {
                        pathFilter { // Ignore changes in this path
                            path(target_project.p4_data_root + '/' + branch_info.target_folder + '/' + branch_info.target_branch)
                        }
                    }
                    browser { swarm { url(target_project.p4_browser_url) } }
                }
                perforce {
                    credential(source_project.p4_data_creds)
                    workspace {
                        streamSpec {
                            charset('none')
                            pinHost(true)
                            streamName(p4_code_root_source + '/' + branch_info.source_folder + '/' + branch_info.source_branch)
                            format('jenkins-' + source_project.short_name + '-${NODE_NAME}-${JOB_NAME}-previewdataintegrate')
                        }
                    }
                    populate {
                        previewOnly { // PreviewOnly workspaces only check if there's new files, they don't sync anything.
                            quiet(true)
                            pin('')
                        }
                    }
                    browser { swarm { url(source_project.p4_browser_url) } }
                }
                cobraGit(
                    delegate,
                    [
                        //	(Cobra.git_url_elipy_setup): Cobra.git_targetdir_ci,
                        '*****************:dice/tools-and-workflows/integrationci.git': 'cherrypick_copy',
                    ],
                    target_project
                )
            }
        }
    }

    // Sync Code (Target)
    // Sync Data (Target)
    // Sync Data (Source)
    // Integrates Data using Code from the Target branch (Default behaviour).
    static void downgrade_data_default(def job, def source_project, def target_project, def branch_info, def src_pin_cl_data = '') {
        downgrade_data(job, source_project, target_project, branch_info.source_folder, branch_info.source_branch, branch_info.target_folder, branch_info.target_branch, src_pin_cl_data, target_project, branch_info.virtual_target_code_folder, branch_info.virtual_target_code_branch)
    }
    // Sync Code (Code branch)
    // Sync Data (Target)
    // Sync Data (Source)
    // Default method, implemented in different ways above.
    static void downgrade_data(def job, def source_project, def target_project, def src_folder, def src_branch, def tgt_folder, def tgt_branch, def src_pin_cl_data, def code_project, def code_folder, def code_branch) {
        job.with {
            multiscm {
                perforce {
                    credential(code_project.p4_code_creds)
                    workspace {
                        streamSpec {
                            charset('none')
                            pinHost(true)
                            streamName(code_project.p4_code_root + '/' + code_folder + '/' + code_branch)
                            format(code_project.p4_code_client)
                        }
                    }
                    populate {
                        syncOnly {
                            have(true)
                            force(false)
                            modtime(false)
                            quiet(true)
                            revert(true)
                            pin('')
                            parallel {
                                enable(true)
                                path('')
                                threads('8')
                                minfiles('0')
                                minbytes('0')
                            }
                        }
                    }
                    filter {
                        pathFilter { // Ignore changes in this path
                            path(code_project.p4_code_root + '/' + code_folder + '/' + code_branch)
                        }
                    }
                    browser { swarm { url(code_project.p4_browser_url) } }
                }
                perforce {
                    credential(target_project.p4_data_creds)
                    workspace {
                        streamSpec {
                            charset('none')
                            pinHost(true)
                            streamName(target_project.p4_data_root + '/' + tgt_folder + '/' + tgt_branch)
                            format(target_project.p4_data_client)
                        }
                    }
                    populate {
                        syncOnly {
                            have(true)
                            force(false)
                            modtime(false)
                            quiet(true)
                            revert(true)
                            pin('')
                            parallel {
                                enable(true)
                                path('')
                                threads('8')
                                minfiles('0')
                                minbytes('0')
                            }
                        }
                    }
                    filter {
                        pathFilter { // Ignore changes in this path
                            path(target_project.p4_data_root + '/' + tgt_folder + '/' + tgt_branch)
                        }
                    }
                    browser { swarm { url(target_project.p4_browser_url) } }
                }
                perforce {
                    credential(source_project.p4_data_creds)
                    workspace {
                        streamSpec {
                            charset('none')
                            pinHost(true)
                            streamName(source_project.p4_data_root + '/' + src_folder + '/' + src_branch)
                            format(source_project.p4_data_client)
                        }
                    }
                    populate {
                        syncOnly {
                            have(true)
                            force(false)
                            modtime(false)
                            quiet(true)
                            revert(true)
                            pin(src_pin_cl_data)
                            parallel {
                                enable(true)
                                path('')
                                threads('8')
                                minfiles('0')
                                minbytes('0')
                            }
                        }
                    }
                    browser { swarm { url(source_project.p4_browser_url) } }
                }
                // cobraGit(delegate, Cobra.git_url_elipy_setup, target_project)
            }
        }
    }

    // Sync Code (preview code (target))
    // Sync Data (Target) - exclude sourcedata
    // Integrates Data using Code from the Source branch (because Target branch doesn't posses any Code).
    static void integrate_data_upgrade(def job, def source_project, def target_project, def preview_project, def branch_info, def src_pin_cl_data = '', def src_pin_cl_code = '') {
        integrate_data(job, source_project, target_project, branch_info.source_folder, branch_info.source_branch, branch_info.target_folder, branch_info.target_branch, src_pin_cl_data, preview_project, branch_info.preview_folder, branch_info.preview_branch, src_pin_cl_code, branch_info)
    }

    // Sync Code (Target)
    // Sync Data (Target)
    // Sync Data (Source)
    // Validating using code from a specified virtual code branch.
    static void data_upgrader_validator_default(def job, def branch_info, def pin_cl_code = '', def pin_cl_data_src = '', def pin_cl_data_tgt = '') {
        data_upgrader_validator(job, branch_info.source_project, branch_info.source_folder, branch_info.source_branch, branch_info.target_project, branch_info.target_folder, branch_info.target_branch, branch_info.target_project, branch_info.virtual_target_code_folder, branch_info.virtual_target_code_branch, pin_cl_code, pin_cl_data_src, pin_cl_data_tgt)
    }
    // Sync Code (Code branch)
    // Sync Data (Target)
    // Sync Data (Source)
    // Default method, implemented in different ways above.
    static void data_upgrader_validator(def job, def source_project, def src_folder, def src_branch, def target_project, def tgt_folder, def tgt_branch, def code_project, def code_folder, def code_branch, def pin_cl_code = '', def pin_cl_data_src = '', def pin_cl_data_tgt = '') {
        job.with {
            multiscm {
                perforce {
                    credential(code_project.p4_code_creds)
                    workspace {
                        streamSpec {
                            charset('none')
                            pinHost(true)
                            streamName(code_project.p4_code_root + '/' + code_folder + '/' + code_branch)
                            format(code_project.p4_code_client)
                        }
                    }
                    populate {
                        syncOnly {
                            have(true)
                            force(false)
                            modtime(false)
                            quiet(true)
                            revert(true)
                            pin(pin_cl_code)
                            parallel {
                                enable(true)
                                path('')
                                threads('8')
                                minfiles('0')
                                minbytes('0')
                            }
                        }
                    }
                    filter {
                        pathFilter { // Ignore changes in this path
                            path(code_project.p4_code_root + '/' + code_folder + '/' + code_branch)
                        }
                    }
                    browser { swarm { url(code_project.p4_browser_url) } }
                }
                perforce {
                    credential(target_project.p4_data_creds)
                    workspace {
                        streamSpec {
                            charset('none')
                            pinHost(true)
                            streamName(target_project.p4_data_root + '/' + tgt_folder + '/' + tgt_branch)
                            format(target_project.p4_data_client)
                        }
                    }
                    populate {
                        syncOnly {
                            have(true)
                            force(false)
                            modtime(false)
                            quiet(true)
                            revert(true)
                            pin(pin_cl_data_tgt)
                            parallel {
                                enable(true)
                                path('')
                                threads('8')
                                minfiles('0')
                                minbytes('0')
                            }
                        }
                    }
                    filter {
                        pathFilter { // Ignore changes in this path
                            path(target_project.p4_data_root + '/' + tgt_folder + '/' + tgt_branch)
                        }
                    }
                    browser { swarm { url(target_project.p4_browser_url) } }
                }
                perforce {
                    credential(source_project.p4_data_creds)
                    workspace {
                        streamSpec {
                            charset('none')
                            pinHost(true)
                            streamName(source_project.p4_data_root + '/' + src_folder + '/' + src_branch)
                            format(source_project.p4_data_client)
                        }
                    }
                    populate {
                        syncOnly {
                            have(true)
                            force(false)
                            modtime(false)
                            quiet(true)
                            revert(true)
                            pin(pin_cl_data_src)
                            parallel {
                                enable(true)
                                path('')
                                threads('8')
                                minfiles('0')
                                minbytes('0')
                            }
                        }
                    }
                    browser { swarm { url(source_project.p4_browser_url) } }
                }
                // cobraGit(delegate, Cobra.git_url_elipy_setup, target_project)
            }
        }
    }

    // Sync code for prebuild jobs.
    static void sync_prebuild(def job, def project, def branch_info, def pin_cl_code = '') {
        def prebuild_info = branch_info.prebuild_info
        def workspace_type = prebuild_info.workspace_type ?: 'manual'
        Map job_info = [
            local_path       : '-prebuild/TnT/Prebuild/... ',
            perforce_path    : prebuild_info.prebuild_path,
            workspace_postfix: '-prebuild',
            workspace_type   : workspace_type,

        ]
        sync_outsourcer_generic(job, project, branch_info, job_info, pin_cl_code)
    }

    // Sync code for outsource package jobs.
    static void sync_outsource_package(def job, def project, def branch_info, def pin_cl_code = '') {
        def outsource_package_info = branch_info.outsource_package_info
        def workspace_type = outsource_package_info.workspace_type ?: 'manual'
        Map job_info = [
            local_path       : '-outsource-package/TnT/Packages/... ',
            perforce_path    : outsource_package_info.perforce_path,
            workspace_postfix: '-outsource-package',
            workspace_type   : workspace_type,
        ]
        sync_outsourcer_generic(job, project, branch_info, job_info, pin_cl_code)
    }

    // Sync code from the branch that we are going to generate builds from.
    // Sync the path that we are going to submit the generated result to.
    // Generic method used for both prebuild jobs and outsource package jobs
    // (both jobs are related to the workflow for outsourcers).
    static void sync_outsourcer_generic(def job, def project, def branch_info, def job_info, def pin_cl_code) {
        def p4_code_creds = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_code_creds', '', project)
        def p4_code_root = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_code_root', '', project)
        def p4_code_client = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_code_client', '', project)

        def code_stream_name = p4_code_root + '/' + branch_info.code_folder + '/' + branch_info.code_branch

        job.with {
            multiscm {
                for (p4_server in project.p4_extra_servers) {
                    // Preview a Perforce server to create a token there, for jobs requiring this.
                    perforce {
                        credential(p4_server.p4_creds)
                        workspace {
                            def workspace_type = p4_server.workspace_type ?: 'streams'
                            def workspace_name_postfix = p4_server.workspace_name_postfix ?: ''
                            def workspace_name = p4_server.p4_creds + workspace_name_postfix + '-previewonly'
                            switch (workspace_type) {
                                case 'streams':
                                    streamSpec {
                                        charset('none')
                                        pinHost(true)
                                        streamName(p4_server.p4_stream)
                                        format(workspace_name)
                                    }
                                    break
                                case 'manual':
                                    manualSpec {
                                        charset('none')
                                        pinHost(true)
                                        name(workspace_name)
                                        cleanup(false)
                                        syncID('')
                                        spec {
                                            view(p4_server.view)
                                            allwrite(false)
                                            clobber(true)
                                            compress(false)
                                            locked(false)
                                            modtime(false)
                                            rmdir(false)
                                            streamName('')
                                            line('LOCAL')
                                            changeView('')
                                            type('WRITABLE')
                                            serverID('')
                                            backup(true)
                                        }
                                    }
                                    break
                                default:
                                    throw new CobraException('Unsupported workspace type: ' + workspace_type)
                            }
                        }
                        populate {
                            previewOnly { // PreviewOnly workspaces only check if there's new files, they don't sync anything.
                                quiet(true)
                                pin('')
                            }
                        }
                        browser { swarm { url(project.p4_browser_url) } }
                    }
                }
                perforce {
                    credential(p4_code_creds)
                    workspace {
                        streamSpec {
                            charset('none')
                            pinHost(true)
                            streamName(code_stream_name)
                            format(p4_code_client)
                        }
                    }
                    populate {
                        syncOnly {
                            have(true)
                            force(false)
                            modtime(false)
                            quiet(true)
                            revert(true)
                            pin(pin_cl_code)
                            parallel {
                                enable(true)
                                path('')
                                threads('8')
                                minfiles('0')
                                minbytes('0')
                            }
                        }
                    }
                    filter {
                        pathFilter { // Ignore changes in this path
                            path(code_stream_name)
                        }
                    }
                    browser { swarm { url(project.p4_browser_url) } }
                }
                if (job_info.workspace_type == 'manual') {
                    perforce {
                        credential(p4_code_creds)
                        workspace {
                            manualSpec {
                                charset('none')
                                pinHost(true)
                                name(p4_code_client + job_info.workspace_postfix)
                                cleanup(false)
                                spec {
                                    allwrite(false)
                                    backup(false)
                                    clobber(true)
                                    compress(false)
                                    line('LOCAL')
                                    locked(false)
                                    modtime(false)
                                    rmdir(true)
                                    serverID('')
                                    streamName('')
                                    type('WRITABLE')
                                    view(job_info.perforce_path + '/... //' + p4_code_client + job_info.local_path)
                                    changeView('')
                                }
                            }
                        }
                        populate {
                            syncOnly {
                                have(true)
                                force(false)
                                modtime(false)
                                quiet(true)
                                revert(true)
                                pin('')
                                parallel {
                                    enable(true)
                                    path('')
                                    threads('8')
                                    minfiles('0')
                                    minbytes('0')
                                }
                            }
                        }
                        filter {
                            pathFilter { // Ignore changes in this path
                                path(job_info.perforce_path)
                            }
                        }
                        browser { swarm { url(project.p4_browser_url) } }
                    }
                }
                // cobraGit(delegate, Cobra.git_url_elipy_setup, project)
            }
        }
    }

    /*
    generic git clone function()
    */

    static void codecoverage_sync_all(def job, def project, def branch_info, def git_url, def pin_cl_code = '', def git_targetdir = './') {
        def p4_code_creds = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_code_creds', '', project)
        def p4_code_root = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_code_root', '', project)
        def p4_code_client = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_code_client', '', project)

        job.with {
            multiscm {
                for (p4_server in project.p4_extra_servers) {
                    // Preview a Perforce server to create a token there, for jobs requiring this.
                    perforce {
                        credential(p4_server.p4_creds)
                        workspace {
                            def workspace_type = p4_server.workspace_type ?: 'streams'
                            def workspace_name_postfix = p4_server.workspace_name_postfix ?: ''
                            def workspace_name = p4_server.p4_creds + workspace_name_postfix + '-previewonly'
                            switch (workspace_type) {
                                case 'streams':
                                    streamSpec {
                                        charset('none')
                                        pinHost(true)
                                        streamName(p4_server.p4_stream)
                                        format(workspace_name)
                                    }
                                    break
                                case 'manual':
                                    manualSpec {
                                        charset('none')
                                        pinHost(true)
                                        name(workspace_name)
                                        cleanup(false)
                                        syncID('')
                                        spec {
                                            view(p4_server.view)
                                            allwrite(false)
                                            clobber(true)
                                            compress(false)
                                            locked(false)
                                            modtime(false)
                                            rmdir(false)
                                            streamName('')
                                            line('LOCAL')
                                            changeView('')
                                            type('WRITABLE')
                                            serverID('')
                                            backup(true)
                                        }
                                    }
                                    break
                                default:
                                    throw new CobraException('Unsupported workspace type: ' + workspace_type)
                            }
                        }
                        populate {
                            previewOnly { // PreviewOnly workspaces only check if there's new files, they don't sync anything.
                                quiet(true)
                                pin('')
                            }
                        }
                        browser { swarm { url(project.p4_browser_url) } }
                    }
                }
                perforce {
                    credential(p4_code_creds)
                    workspace {
                        streamSpec {
                            charset('none')
                            pinHost(true)
                            streamName(p4_code_root + '/' + branch_info.code_folder + '/' + branch_info.code_branch)
                            format(p4_code_client)
                        }
                    }
                    populate {
                        syncOnly {
                            have(true)
                            force(false)
                            modtime(false)
                            quiet(true)
                            revert(true)
                            pin(pin_cl_code)
                            parallel {
                                enable(true)
                                path('')
                                threads('8')
                                minfiles('0')
                                minbytes('0')
                            }
                        }
                    }
                    browser { swarm { url(project.p4_browser_url) } }
                }
                cobraGit(
                    delegate,
                    [
                        //	(Cobra.git_url_elipy_setup): Cobra.git_targetdir_ci,
                        (git_url): git_targetdir,
                    ],
                    project
                )
            }
        }
    }

    /*
    Sync Perforce code
    Sync Perforce data
    Sync the pipeline warning Gitlab repository
    */

    static void sync_pipeline_warning(def job, def project, def branch_info, def pin_cl_data = '', def pin_cl_code = '') {
        def pwarn_info = project.pipeline_warning_settings
        sync_code_data_git(job, project, branch_info, pwarn_info.tool_repository, pin_cl_data, pin_cl_code, 'pwarn')
    }

    /*
    Sync Perforce code
    Sync Perforce data
    Sync Git repository
    */

    static void sync_code_data_git(def job, def project, def branch_info, def git_url, def pin_cl_data = '', def pin_cl_code = '', def git_targetdir = './') {
        def p4_code_creds = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_code_creds', '', project)
        def p4_code_root = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_code_root', '', project)
        def p4_code_client = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_code_client', '', project)
        def p4_data_creds = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_data_creds', '', project)
        def p4_data_root = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_data_root', '', project)
        def p4_data_client = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_data_client', '', project)

        job.with {
            multiscm {
                for (p4_server in project.p4_extra_servers) {
                    // Preview a Perforce server to create a token there, for jobs requiring this.
                    perforce {
                        credential(p4_server.p4_creds)
                        workspace {
                            def workspace_type = p4_server.workspace_type ?: 'streams'
                            def workspace_name_postfix = p4_server.workspace_name_postfix ?: ''
                            def workspace_name = p4_server.p4_creds + workspace_name_postfix + '-previewonly'
                            switch (workspace_type) {
                                case 'streams':
                                    streamSpec {
                                        charset('none')
                                        pinHost(true)
                                        streamName(p4_server.p4_stream)
                                        format(workspace_name)
                                    }
                                    break
                                case 'manual':
                                    manualSpec {
                                        charset('none')
                                        pinHost(true)
                                        name(workspace_name)
                                        cleanup(false)
                                        syncID('')
                                        spec {
                                            view(p4_server.view)
                                            allwrite(false)
                                            clobber(true)
                                            compress(false)
                                            locked(false)
                                            modtime(false)
                                            rmdir(false)
                                            streamName('')
                                            line('LOCAL')
                                            changeView('')
                                            type('WRITABLE')
                                            serverID('')
                                            backup(true)
                                        }
                                    }
                                    break
                                default:
                                    throw new CobraException('Unsupported workspace type: ' + workspace_type)
                            }
                        }
                        populate {
                            previewOnly { // PreviewOnly workspaces only check if there's new files, they don't sync anything.
                                quiet(true)
                                pin('')
                            }
                        }
                        browser { swarm { url(project.p4_browser_url) } }
                    }
                }
                perforce {
                    credential(p4_code_creds)
                    workspace {
                        streamSpec {
                            charset('none')
                            pinHost(true)
                            streamName(p4_code_root + '/' + branch_info.code_folder + '/' + branch_info.code_branch)
                            format(p4_code_client)
                        }
                    }
                    populate {
                        syncOnly {
                            have(true)
                            force(false)
                            modtime(false)
                            quiet(true)
                            revert(true)
                            pin(pin_cl_code)
                            parallel {
                                enable(true)
                                path('')
                                threads('8')
                                minfiles('0')
                                minbytes('0')
                            }
                        }
                    }
                    browser { swarm { url(project.p4_browser_url) } }
                }
                perforce {
                    credential(p4_data_creds)
                    workspace {
                        streamSpec {
                            charset('none')
                            pinHost(true)
                            streamName(p4_data_root + '/' + branch_info.data_folder + '/' + branch_info.data_branch)
                            format(p4_data_client)
                        }
                    }
                    populate {
                        syncOnly {
                            have(true)
                            force(false)
                            modtime(false)
                            quiet(true)
                            revert(true)
                            pin(pin_cl_data)
                            parallel {
                                enable(true)
                                path('')
                                threads('8')
                                minfiles('0')
                                minbytes('0')
                            }
                        }
                    }
                    browser { swarm { url(project.p4_browser_url) } }
                }
                cobraGit(
                    delegate,
                    [
                        //	(Cobra.git_url_elipy_setup): Cobra.git_targetdir_ci,
                        (git_url): git_targetdir,
                    ],
                    project
                )
            }
        }
    }
}
