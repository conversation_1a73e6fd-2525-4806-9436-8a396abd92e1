[{"_index": "bilbo", "_type": "build", "_id": "\\\\build_share\\code\\code_branch\\900", "_score": null, "_source": {"changelist": "900", "branch": "code_branch", "type": "drone", "created": "2020-11-06T11:46:44.544542", "location": "battlefieldgame", "updated": "2020-11-06T13:51:39.366817", "build_promotion_level": "qa_verified", "verified_data": [{"branch": "dev-na-dice-next-build", "changelist": "900", "dataset": "DataSet", "timestamp": "2020-11-06T13:37:04.318569"}]}, "sort": [1604663204544]}, {"_index": "bilbo", "_type": "build", "_id": "\\\\build_share\\code\\code_branch\\1234", "_score": null, "_source": {"changelist": "1234", "branch": "code_branch", "type": "drone", "created": "2020-11-06T11:46:44.544542", "location": "battlefieldgame", "updated": "2020-11-06T13:51:39.366817", "build_promotion_level": "qa_verified", "verified_data": [{"branch": "dev-na-dice-next-build", "changelist": "1122", "dataset": "DataSet", "timestamp": "2020-11-06T13:37:04.318569"}]}, "sort": [1604663204544]}]