package com.ea.lib.jobs

import com.ea.lib.LibCommonNonCps

class LibDvcs {
    /**
     * Adds generic job parameters for dvcs start jobs.
     */
    static void dvcs_start(def job, def project_settings, def dvcs_settings, def branch_info) {
        def dvcs_command = dvcs_settings.command
        def remote_spec = dvcs_settings.remote_spec
        def branch_name = dvcs_settings.branch_name
        def workspace_root = project_settings.workspace_root
        def modifiers = ['dvcs', dvcs_command, remote_spec, branch_name]
        def skip_fetch = dvcs_settings.skip_fetch ?: false
        def skip_push = dvcs_settings.skip_push ?: false

        job.with {
            def trigger_string = LibCommonNonCps.get_setting_value(dvcs_settings, modifiers, 'trigger_string', 'H/5 * * * 1-6\nH/5 6-23 * * 7')
            def trigger_type = LibCommonNonCps.get_setting_value(dvcs_settings, modifiers, 'trigger_type', 'None')
            def slack_channel = LibCommonNonCps.get_setting_value(dvcs_settings, modifiers, 'slack_channel', null)
            def disable_build = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'disable_build', false)

            description('Triggers a dvcs push & fetch for remote spec ' + remote_spec + '.')
            disabled(disable_build)
            logRotator(7, 100)
            quietPeriod(0)
            properties {
                disableConcurrentBuilds()
                disableResume()
                pipelineTriggers {
                    triggers {
                        if (trigger_type == 'scm') {
                            pollSCM {
                                scmpoll_spec(trigger_string)
                            }
                        } else if (trigger_type == 'cron') {
                            cron {
                                spec(trigger_string)
                            }
                        }
                    }
                }
            }
            environmentVariables {
                env('project_name', project_settings.name)
                env('script_branch_name', branch_name)
                env('dvcs_command', dvcs_command)
                env('dvcs_remote_spec', remote_spec)
                env('slack_channel', slack_channel)
                env('workspace_root', workspace_root)
                env('skip_fetch', skip_fetch)
                env('skip_push', skip_push)
            }
        }
    }

    /**
     * Adds generic job parameters for dvcs worker jobs.
     */
    static void dvcs_worker(def job, def project_settings, def dvcs_settings, def dvcs_command) {
        def remote_spec = dvcs_settings.remote_spec
        def workspace_root = project_settings.workspace_root
        def modifiers = ['dvcs', dvcs_command, remote_spec]
        def user_credentials = LibCommonNonCps.get_setting_value(dvcs_settings, modifiers, 'user_credentials', '', project_settings)
        def p4_data_server = LibCommonNonCps.get_setting_value(dvcs_settings, modifiers, 'p4_data_server', '', project_settings)
        def p4_data_client_env = LibCommonNonCps.get_setting_value(dvcs_settings, modifiers, 'p4_data_client_env', '', project_settings)
        def dry_run = LibCommonNonCps.get_setting_value(dvcs_settings, modifiers, 'dry_run', false)
        def extra_args = LibCommonNonCps.get_setting_value(dvcs_settings, modifiers, 'extra_args', '')
        def timeout_hours = LibCommonNonCps.get_setting_value(dvcs_settings, modifiers, 'timeout_hours', 24)
        def job_label = LibCommonNonCps.get_setting_value(dvcs_settings, modifiers, 'job_label', 'statebuild')
        def timeout_minutes = timeout_hours * 60
        def fb_login_details = LibCommonNonCps.get_setting_value(dvcs_settings, [], 'p4_fb_settings', [:], project_settings)

        if (dry_run) {
            extra_args += ' --dry-run'
        }

        job.with {
            label(job_label)
            description('Runs the dvcs command ' + dvcs_command + ' for remote spec ' + remote_spec + '.')
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(workspace_root)
            wrappers {
                colorizeOutput()
                timestamps()
                buildName('${JOB_NAME}')
                timeout {
                    absolute(timeout_minutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
                credentialsBinding {
                    if (user_credentials != '') {
                        usernamePassword('monkey_email', 'monkey_passwd', user_credentials)
                    }
                    if (fb_login_details.p4_creds) {
                        usernamePassword('fb_p4_user', 'fb_p4_passwd', fb_login_details.p4_creds)
                    }
                }
            }
            steps {
                if (fb_login_details) {
                    batchFile("echo %fb_p4_passwd%|p4 -p ${fb_login_details.p4_port} -u %fb_p4_user% login & exit 0")
                }
                batchFile(dvcs_settings.elipy_install_call)
                batchFile(
                    dvcs_settings.elipy_call + ' p4_dvcs --cmd ' + dvcs_command + ' --remote-spec ' + remote_spec +
                        ' --port ' + p4_data_server +
                        ' --client ' + p4_data_client_env +
                        ' --user %P4_USER%' +
                        extra_args
                )
            }
        }
    }
}
