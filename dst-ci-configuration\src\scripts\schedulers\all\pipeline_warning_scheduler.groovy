package scripts.schedulers.all

import com.ea.lib.<PERSON><PERSON><PERSON><PERSON><PERSON>

def project = ProjectClass(env.project_name)

/**
 * pipeline_warning_scheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Get changelists to use') {
            steps {
                script {
                    def last_good_code = LibJenkins.getLastStableCodeChangelist(env.pipeline_warning_reference_job)
                    def last_good_data = LibJenkins.getLastStableDataChangelist(env.pipeline_warning_reference_job)
                    def code_changelist = params.code_changelist ?: last_good_code
                    def data_changelist = params.data_changelist ?: last_good_data
                    def clean_data = params.clean_data

                    if (code_changelist == null || data_changelist == null) {
                        echo 'Missing changelist, aborting build!'
                        currentBuild.result = Result.FAILURE.toString()
                        return
                    }

                    def last_pwarn_code = LibJenkins.getLastStableCodeChangelist(env.JOB_NAME)
                    def last_pwarn_data = LibJenkins.getLastStableDataChangelist(env.JOB_NAME)
                    if (code_changelist == last_pwarn_code && data_changelist == last_pwarn_data) {
                        // Skip building if we've already built on the same changelists with success.
                        echo('Already built on code changelist ' + last_pwarn_code + ' and data changelist ' + last_pwarn_data + ', aborting build.')
                        currentBuild.result = Result.UNSTABLE.toString()
                        return
                    }

                    echo "Last build was on code changelist $last_pwarn_code and data changelist ${last_pwarn_data}."
                    echo "Latest successful data job used code changelist $code_changelist and data changelist $data_changelist, proceeding."
                    def args = [
                        string(name: 'code_changelist', value: code_changelist),
                        string(name: 'data_changelist', value: data_changelist),
                        string(name: 'clean_data', value: clean_data),
                    ]
                    def inject_map = [
                        'code_changelist': code_changelist,
                        'data_changelist': data_changelist,
                    ]
                    EnvInject(currentBuild, inject_map)
                    currentBuild.displayName = env.JOB_NAME + '.' + data_changelist + '.' + code_changelist

                    def job_name = env.branch_name + '.pipeline-warning'
                    def pipeline_warning_job = build(job: job_name, parameters: args, propagate: false)

                    currentBuild.result = pipeline_warning_job.result.toString()

                    def slack_settings = project?.pipeline_warning_settings?.slack_channel
                    SlackMessageNew(currentBuild, slack_settings, project.short_name)

                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
}
