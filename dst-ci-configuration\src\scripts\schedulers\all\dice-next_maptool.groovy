package scripts.schedulers.all

import com.ea.project.GetBranchFile
import com.ea.lib.<PERSON><PERSON><PERSON><PERSON><PERSON>

def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
def project = ProjectClass(env.project_name)
def data_changelist = LibJ<PERSON>kins.getLastStableDataChangelist(env.branch_name + '.data.start')

/**
 * dice-next_maptool.groovy
 * this file is not in use after switch from dice-next to kin-dev
 */
pipeline {
    options {
        allowBrokenBuildClaiming()
    }
    agent {
        node {
            customWorkspace(project.workspace_root)
            label(env.job_label)
        }
    }
    stages {
        stage('Git clone maptool') {
            steps {
                GitSync('kin_maptool')
                stash name: 'ppbscripts', includes: 'ppb\\scripts\\*'
            }
        }
        stage('Sync data from Perforce and perform automation') {
            steps {
                P4SyncDefault(project, branchfile, env.data_folder, env.data_branch, 'data', data_changelist)
                P4PreviewServerToken(project)
                unstash 'ppbscripts'
                withCredentials([usernamePassword(credentialsId: 'maptool.kin', usernameVariable: 'KIN_MAPTOOL_USER', passwordVariable: 'KIN_MAPTOOL_PSW')]) {
                    echo "ppb\\scripts\\KingstonDataExtractor.exe ${params.game_dir}\\Source ${params.maptool_server} ${data_changelist} ${KIN_MAPTOOL_USER} ${KIN_MAPTOOL_PSW}"
                    bat script: "ppb\\scripts\\KingstonDataExtractor.exe ${params.game_dir}\\Source ${params.maptool_server} ${data_changelist} ${KIN_MAPTOOL_USER} ${KIN_MAPTOOL_PSW}"
                    echo "ppb\\scripts\\KingstonWorldcamUploader.exe ${params.filer_loc} ${params.maptool_server} ${KIN_MAPTOOL_USER} ${KIN_MAPTOOL_PSW}"
                    bat script: "ppb\\scripts\\KingstonWorldcamUploader.exe ${params.filer_loc} ${params.maptool_server} ${KIN_MAPTOOL_USER} ${KIN_MAPTOOL_PSW}"
                }
            }
        }
    }
}
