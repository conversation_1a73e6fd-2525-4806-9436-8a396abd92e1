package scripts.schedulers

import com.ea.lib.LibJenkins
import com.ea.project.GetBranchFile
import hudson.model.Result

def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
def settings_map = branchfile.general_settings + branchfile.standard_jobs_settings
def project = ProjectClass(env.project_name)

/**
 * codecoverage.groovy
 **/
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Get changelist from Perforce') {
            steps {
                script {
                    def ignore_paths = branchfile.general_settings?.ignore_paths_code_preview ?: []
                    P4PreviewCode(project, 'stream', env.code_folder, env.code_branch, env.non_virtual_code_folder, env.non_virtual_code_branch, ignore_paths, [], settings_map)
                }
            }
        }
        stage('Run Testautomation CodeCoverage') {
            steps {
                script {
                    def last_good_code = LibJenkins.getLastStableCodeChangelist(env.codecoverage_ref_job)
                    def code_changelist = params.code_changelist ?: last_good_code
                    // Can be overwritten by user manual input CL rather than check from jenkins job status

                    currentBuild.displayName = "${env.JOB_NAME}.${code_changelist}"
                    // Call job to invoke elipy-script, currently only one job
                    def jobs = [:]
                    def job_list = ['codecoverage']

                    def args = [ // Pass down parameters to freestyle job
                                 string(name: 'code_changelist', value: code_changelist),
                    ]
                    def final_result = Result.SUCCESS

                    for (job in job_list) {
                        def job_name = env.branch_name + '.' + job
                        jobs[job_name] = {
                            def downstream_job = build(job: job_name, parameters: args, propagate: false)
                            final_result = final_result.combine(Result.fromString(downstream_job.result))
                            LibJenkins.printRunningJobs(this)
                        }
                    }
                    parallel(jobs)
                    currentBuild.result = final_result.toString()
                    // If needed, set slack_channel_codecoverage to overwrite the standard one
                    def slack_settings = branchfile.standard_jobs_settings?.slack_channel_codecoverage
                    SlackMessageNew(currentBuild, slack_settings, project.short_name)
                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
}
