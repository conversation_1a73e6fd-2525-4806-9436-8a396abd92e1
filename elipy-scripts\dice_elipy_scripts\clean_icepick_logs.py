"""
clean_icepick_logs.py
"""
import os
import click

from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import LOGGER
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command("clean_icepick_logs", short_help="Clean a icepick logs.")
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(_):
    """
    Clean a icepick logs.
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    LOGGER.info("Deleteing Icepick logs...")
    delete_icepick_logs(os.path.join(os.getenv("LOCALAPPDATA"), "Icepick", "Logs"))
    delete_icepick_logs(os.path.join(os.getenv("LOCALAPPDATA"), "Logs"))


def delete_icepick_logs(path):
    """
    Delete Icepick logs
    """

    if os.path.exists(path):
        LOGGER.info("Removing Icepick logs: {0}".format(path))
        files = os.listdir(path)
        for item in files:
            os.remove(os.path.join(path, item))
    else:
        LOGGER.info("No Icepick logs to remove: {0}".format(path))
