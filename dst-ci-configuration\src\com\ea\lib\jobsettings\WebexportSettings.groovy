package com.ea.lib.jobsettings

import com.ea.lib.LibCommonNonCps

class WebexportSettings extends JobSetting {

    Boolean disableBuild
    Map fbLoginDetails
    Integer timeoutMinutes
    String p4Creds
    String p4Port

    void initialize(Class branchFile, Class masterFile, Class projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)

        this.buildName = '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'
        def modifiers = ['webexport']
        def asset = this.branchInfo.webexport_asset ?: this.branchInfo.asset
        this.disableBuild = LibCommonNonCps.get_setting_value(this.branchInfo, modifiers, 'disable_build', false)
        def clean_master_version_check = LibCommonNonCps.get_setting_value(this.branchInfo, modifiers, 'clean_master_version_check', true)
        def importAvalanche = this.branchInfo.webexport_import_avalanche ?: false
        def is_virtual_stream = LibCommonNonCps.get_setting_value(this.branchInfo, [], 'is_virtual_stream', false)
        def exportDataBranch = this.branchInfo.export_data_branch ?: false
        def poolbuild = LibCommonNonCps.get_setting_value(this.branchInfo, ['data', 'win64'], 'poolbuild', false)
        def poolbuildLabel = this.branchInfo.poolbuild_label ?: 'poolbuild'
        def webexportScriptPath = this.branchInfo.webexport_script_path ?: projectFile.webexport_script_path
        def webexportLabel = this.branchInfo.webexport_label ?: ''
        List<String> contentLayers = this.branchInfo.webexport_content_layers ?: this.branchInfo.content_layers ?: []

        this.jobLabel = this.branchInfo.job_label_statebuild ?: 'statebuild'

        if (this.branchInfo.statebuild_webexport == false) {
            importAvalanche = false
            jobLabel = this.branchInfo.data_branch + ' && webexport'
        } else if (webexportLabel != '') {
            importAvalanche = false
            jobLabel = webexportLabel
        } else if (poolbuild) {
            jobLabel = poolbuildLabel + ' && win64'
        }

        this.timeoutMinutes = (this.branchInfo.timeout_hours_webexport ?: 4) * 60

        this.fbLoginDetails = LibCommonNonCps.get_setting_value(this.branchInfo, [], 'p4_fb_settings', [:], projectFile)
        this.p4Creds = fbLoginDetails.p4_creds
        this.p4Port = fbLoginDetails.p4_port

        def extraArgs = this.branchInfo.extra_webexport_args ?: ''
        if (importAvalanche == true && exportDataBranch) {
            extraArgs += ' --import-avalanche-state'
        }
        if (clean_master_version_check) {
            extraArgs += ' --clean-master-version-check'
        }
        if (is_virtual_stream) {
            extraArgs += ' --branch-override ' + this.branchInfo.code_branch
        }
        if (contentLayers) {
            contentLayers.each { layer ->
                extraArgs += ' --content-layers ' + layer
            }
        }

        this.description = 'Builds ' + this.branchInfo.dataset + ' for win64 with code from ' + this.branchInfo.code_branch + '.'

        this.elipyCmd = this.elipyCall + ' webexport --asset ' + asset +
            ' --script-path ' + webexportScriptPath + ' --data-dir ' + this.branchInfo.dataset +
            ' --code-branch ' + this.branchInfo.code_branch + ' --code-changelist %code_changelist%' +
            ' --branch-name ' + this.branchInfo.data_branch + ' --data-changelist %data_changelist%' +
            ' --data-clean %clean_data% --aws-secret-key %AWS_SECRET_KEY% --aws-access-key-id %AWS_ACCESS_KEY_ID%' + extraArgs
    }
}
