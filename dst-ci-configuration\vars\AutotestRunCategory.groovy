import com.ea.exceptions.CobraException
import com.ea.lib.BuildSelector
import com.ea.lib.LibJenkins
import com.ea.lib.jobs.LibAutotestModelBuilder
import com.ea.lib.model.JobReference
import com.ea.lib.model.autotest.AutotestCategory
import com.ea.lib.model.autotest.Name
import com.ea.lib.model.autotest.Platform
import com.ea.lib.model.autotest.jobs.AutotestJobsModel
import com.ea.lib.model.autotest.jobs.AutotestModel
import com.ea.matrixfiles.AutotestMatrix
import com.ea.matrixfiles.AutotestMatrixFactory
import com.ea.project.GetBranchFile
import com.ea.project.GetMasterFile

/**
 * AutotestRunCategory.groovy
 * Runs the tests in the given testCategory and branch. Reports with a slack notification if configured.
 * @param testCategory test configuration
 * @param branchName which branch the tests reside on
 * @param autotestMatrixName which AutotestMatrix configuration to use
 * @param buildSelectorResult changelists split by platform determined by the build-selector
 * @param jobReferences jobReferences to retry on failure
 */
void call(AutotestCategory testCategory, String branchName, String autotestMatrixName, Map<String, Map> buildSelectorResult,
          List<JobReference> jobReferences) {
    AutotestMatrix autotestMatrix = AutotestMatrixFactory.getInstance(autotestMatrixName)
    List<Platform> defaultCategoryPlatforms = autotestMatrix.getPlatforms(branchName)
    boolean defaultShouldLevelsRunInParallel = autotestMatrix.shouldLevelsRunInParallel(branchName)
    def masterSettings = GetMasterFile.get_masterfile(BUILD_URL)[0]
    def branchFile = GetBranchFile.get_branchfile(env.projectName, branchName)

    Map jobs = [:]
    AutotestJobsModel autotestJobsModel = LibAutotestModelBuilder.composeJobs(testCategory,
        defaultCategoryPlatforms, defaultShouldLevelsRunInParallel, branchName, null)
    Map<String, List<AutotestModel>> jobBuckets = autotestJobsModel.jobBuckets
    jobBuckets.each { bucket ->
        jobs = [:]
        bucket.value.each { autotestJobModel ->
            Map targetBuildInfo = buildSelectorResult[Name.ANY.toString()] ?:
                buildSelectorResult[BuildSelector.composeResultKey(autotestJobModel.platform, autotestJobModel.region)]

            targetBuildInfo = appendTargetBuildInfo(targetBuildInfo, branchName, branchFile, masterSettings, testCategory, autotestJobModel)
            echo "Target build: ${targetBuildInfo}"
            if (targetBuildInfo) {
                def args = [
                    string(name: 'test_definition', value: testCategory.testDefinition),
                    string(name: 'code_changelist', value: targetBuildInfo.codeChangelist),
                    string(name: 'data_changelist', value: targetBuildInfo.dataChangelist),
                    string(name: 'client_build_id', value: targetBuildInfo.clientBuildId),
                    string(name: 'server_build_id', value: targetBuildInfo.serverBuildId),
                    booleanParam(name: 'clean', value: params.clean),
                ]
                jobs[autotestJobModel.name] = {
                    def autotestJob = build(job: autotestJobModel.name, parameters: args, propagate: false)
                    jobReferences << new JobReference(downstreamJob: autotestJob, jobName: autotestJobModel.name, parameters: args, propagate: false)
                    LibJenkins.printRunningJobs(this)
                }
            } else {
                echo "No changelists specified for ${autotestJobModel.platform}. Skipping."
            }
        }
        parallel(jobs)
    }
}

/**
 * Appends targetBuildInfo map with clientBuildId or serverBuildId, if they were not set but required.
 * @param targetBuildInfo initial targetBuildInfo map
 * @param branchName branch name of the project
 * @param branchFile branch file of the project
 * @param masterSettings master settings
 * @param testCategory autotest category
 * @param autotestJobModel autotest job model
 */
private static Map<String, String> appendTargetBuildInfo(targetBuildInfo, String branchName, branchFile,
                                                         masterSettings, testCategory, autotestJobModel) {
    def filerPathBase = '\\\\filer.dice.ad.ea.com\\builds'

    if (!targetBuildInfo.clientBuildId) {
        if (!testCategory.isManual) {
            throw new CobraException('clientBuildId is ' + targetBuildInfo.clientBuildId + ' for a non-manual category.')
        }
        if (testCategory.isTestWithLooseFiles) {
            def region = autotestJobModel.region ?: testCategory.testInfo.region ?: testCategory.region
            def config = testCategory.testInfo.config ?: testCategory.config

            targetBuildInfo.clientBuildId = getFrostyBuildId(
                filerPathBase,
                branchFile.project.name,
                branchFile.general_settings.frostbite_licensee,
                masterSettings.autotest_branches[branchName].data_branch,
                targetBuildInfo.dataChangelist,
                masterSettings.autotest_branches[branchName].code_branch,
                targetBuildInfo.codeChangelist,
                autotestJobModel.platform,
                testCategory.format,
                region,
                config
            )
        } else {
            targetBuildInfo.clientBuildId = getDroneBuildId(
                filerPathBase,
                branchFile.project.name,
                masterSettings.autotest_branches[branchName].code_branch,
                targetBuildInfo.codeChangelist
            )
        }
    }
    if (!targetBuildInfo.serverBuildId && testCategory.needGameServer) {
        if (!testCategory.isManual) {
            throw new CobraException('serverBuildId is ' + targetBuildInfo.serverBuildId + ' for a non-manual category.')
        }

        if (testCategory.isTestWithLooseFiles) {
            def serverPlatform = testCategory.testInfo.serverPlatform ?: testCategory.serverPlatform
            def serverRegion = testCategory.testInfo.serverRegion ?: testCategory.serverRegion
            def serverConfig = testCategory.testInfo.serverConfig ?: testCategory.serverConfig

            targetBuildInfo.serverBuildId = getFrostyBuildId(
                filerPathBase,
                branchFile.project.name,
                branchFile.general_settings.frostbite_licensee,
                masterSettings.autotest_branches[branchName].data_branch,
                targetBuildInfo.dataChangelist,
                masterSettings.autotest_branches[branchName].code_branch,
                targetBuildInfo.codeChangelist,
                serverPlatform,
                testCategory.format,
                serverRegion,
                serverConfig
            )
        } else {
            targetBuildInfo.serverBuildId = targetBuildInfo.clientBuildId
        }
    }
    return targetBuildInfo
}

/**
 * Returns a bilbo entry id for the given specific frosty build
 * @param filerPathBase filer network share base path
 * @param projectName name of the project
 * @param frostbiteLicensee frostbite licensee
 * @param dataBranch data branch in p4
 * @param dataChangelist data changelist to run tests
 * @param codeBranch code branch in p4
 * @param codeChangelist code changelist to run tests
 * @param platform build platform
 * @param format build format
 * @param region build region
 * @param config build config
 */
private static String getFrostyBuildId(filerPathBase, projectName, frostbiteLicensee, dataBranch, dataChangelist, codeBranch, codeChangelist,
                                       platform, format, region, config) {
    return "${filerPathBase}\\${projectName}\\frosty\\${frostbiteLicensee}\\${dataBranch}\\${dataChangelist}\\${codeBranch}" +
        "\\${codeChangelist}\\${platform}\\${format}\\${region}\\${config}"
}

/**
 * Returns a bilbo entry id for the given specific drone build
 * @param filerPathBase filer network share base path
 * @param projectName name of the project
 * @param codeBranch code branch in p4
 * @param codeChangelist code changelist to run tests
 */
private static String getDroneBuildId(filerPathBase, projectName, codeBranch, codeChangelist) {
    return "${filerPathBase}\\${projectName}\\code\\${codeBranch}\\${codeChangelist}"
}
