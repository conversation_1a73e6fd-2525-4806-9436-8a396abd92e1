"""
test_nant.py

Unit tests for nant.py
"""
import os
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from dice_elipy_scripts.nant import cli
from mock import patch

nant_config = os.path.join(os.path.dirname(__file__), "data", "test_nant.xml")


class TestNant:
    def __do_test(self, args):
        # setup
        self.patcher_fbcli_run = patch("elipy2.frostbite.fbcli.run")
        self.mock_fbcli_run = self.patcher_fbcli_run.start()
        runner = CliRunner()
        elipy_args = [
            "--package",
            args["package"],
            "--platforms",
            args["platforms"],
            "--targets",
            args["targets"],
        ]
        fbcli_args = [args["package"], args["platforms"], args["targets"]]
        fw_args = []
        for k, v in args.items():
            if k in ["vsver", "propertiesfile"]:
                elipy_args.extend([f"--{k}", v])
                fbcli_args.extend([f"-{k}", v])
            elif k == "framework_args":
                elipy_args.extend(["--framework_args", v])
                fw_args.extend([v])
            elif k == "validate_package_access":
                elipy_args.extend(["--validate_package_access", True])
                fbcli_args.extend(["-validate-package-access"])
            elif v is True:
                elipy_args.extend([f"--{k}", True])
                fbcli_args.extend([f"-{k}"])
        if fw_args:
            fbcli_args.extend(fw_args)

        # test
        runner.invoke(cli, elipy_args)
        self.mock_fbcli_run.assert_called_once_with("nant", method_args=fbcli_args)

        # teardown
        self.patcher_fbcli_run.stop()

    def test_nant_base(self):
        self.__do_test(
            {
                "package": "EAIO",
                "platforms": "pc64-vc-dll-release",
                "targets": "gensln",
            }
        )

    def test_nant_vsver(self):
        self.__do_test(
            {
                "package": "EAIO",
                "platforms": "pc64-vc-dll-release",
                "targets": "gensln",
                "vsver": "2022",
            }
        )

    def test_nant_propertiesfile(self):
        self.__do_test(
            {
                "package": "EAIO",
                "platforms": "pc64-vc-dll-release",
                "targets": "gensln",
                "propertiesfile": f"{nant_config}",
            }
        )

    def test_nant_framework_args(self):
        self.__do_test(
            {
                "package": "EAIO",
                "platforms": "pc64-vc-dll-release",
                "targets": "gensln",
                "framework_args": "-G:framework.validate-package-server-access=true",
            }
        )

    def test_nant_outsourcer(self):
        self.__do_test(
            {
                "package": "EAIO",
                "platforms": "pc64-vc-dll-release",
                "targets": "gensln",
                "outsourcer": True,
            }
        )

    def test_nant_outsourcer_non_proxy(self):
        self.__do_test(
            {
                "package": "EAIO",
                "platforms": "pc64-vc-dll-release",
                "targets": "gensln",
                "outsourcer_non_proxy_sdks": True,
            }
        )

    def test_nant_fwdwarn(self):
        self.__do_test(
            {
                "package": "EAIO",
                "platforms": "pc64-vc-dll-release",
                "targets": "gensln",
                "fwdwarn": True,
            }
        )

    def test_nant_fwderror(self):
        self.__do_test(
            {
                "package": "EAIO",
                "platforms": "pc64-vc-dll-release",
                "targets": "gensln",
                "fwderror": True,
            }
        )

    def test_nant_licensee_agnostic(self):
        self.__do_test(
            {
                "package": "EAIO",
                "platforms": "pc64-vc-dll-release",
                "targets": "gensln",
                "licensee_agnostic": True,
            }
        )

    def test_nant_ignore_deprecation(self):
        self.__do_test(
            {
                "package": "EAIO",
                "platforms": "pc64-vc-dll-release",
                "targets": "gensln",
                "ignoredeprecation": True,
            }
        )

    def test_nant_enable_deprecation(self):
        self.__do_test(
            {
                "package": "EAIO",
                "platforms": "pc64-vc-dll-release",
                "targets": "gensln",
                "enabledeprecation": True,
            }
        )

    def test_nant_enable_expirederror(self):
        self.__do_test(
            {
                "package": "EAIO",
                "platforms": "pc64-vc-dll-release",
                "targets": "gensln",
                "enableexpiredapierror": True,
            }
        )

    def test_nant_validate_package_access(self):
        self.__do_test(
            {
                "package": "EAIO",
                "platforms": "pc64-vc-dll-release",
                "targets": "gensln",
                "validate_package_access": True,
            }
        )
