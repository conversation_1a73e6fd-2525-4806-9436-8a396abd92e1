package support

import static com.lesfurets.jenkins.unit.global.lib.LibraryConfiguration.library
import static com.lesfurets.jenkins.unit.global.lib.ProjectSource.projectSource

import com.ea.project.all.mastersettings.EmptyMasterSettings
import com.lesfurets.jenkins.unit.declarative.DeclarativePipelineTest
import hudson.model.Result

class PipelineTestHelper extends DeclarativePipelineTest {

    @Override
    void setUp() {
        scriptRoots += [
            'src/scripts/schedulers/all/',
            'src/scripts/schedulers/aws/',
            'src/scripts/schedulers/gametool/',
            'src/scripts/schedulers/data/',
            'vars/',
        ]
        helper.registerSharedLibrary(library()
            .name('dst-ci-configuration')
            .defaultVersion('<notNeeded>')
            .allowOverride(true)
            .implicit(true)
            .targetPath('<notNeeded>')
            .retriever(projectSource())
            .build())
        super.setUp()
        registerCobraMethods()
    }

    private void registerCobraMethods() {
        helper.with {
            registerAllowedMethod('string', [Map]) { map -> map }
            registerAllowedMethod('booleanParam', [Map]) { map -> map }
            registerAllowedMethod('build', [Map]) {
                [
                    getNumber         : { 100500 },
                    getDescription    : { 'Dummy build description' },
                    getFullProjectName: { 'some_dir/some_job' },
                    getProjectName    : { 'some_job' },
                    result            : 'SUCCESS',
                ]
            }
            registerAllowedMethod('printFailureMessage', [Object, Object, boolean]) {}
            registerAllowedMethod('printRunningJobs', [Object]) {}
            registerAllowedMethod('SlackMessageNew', [Map, Map, String])
            registerAllowedMethod('SlackMessageNew', [Map, String, String])
            registerAllowedMethod('DownstreamErrorReporting', [Map])
            registerAllowedMethod('EnvInject', [Map, Map])
            registerAllowedMethod('allowBrokenBuildClaiming')
            registerAllowedMethod('emailext', [Map])
            registerAllowedMethod('ScanForErrors', [Map, boolean])
            registerAllowedMethod('get_branchfile', [String, String]) { projectName, branchName ->
                throw new UnsupportedOperationException('GetBranchFile.get_branchfile called. When writing unit tests, we can not ' +
                    'rely on configuration files that may change. Please mock it instead. ')
            }
            registerAllowedMethod('waitUntil', [Closure]) { Closure closure ->
                boolean isDone = false
                while (!isDone) {
                    isDone = closure.call()
                }
            }
            registerAllowedMethod('withCredentials', [List, Closure])
            registerAllowedMethod('P4PreviewCode', [Object, String, String, String, String, String, List, List, Object])
            registerAllowedMethod('P4SyncDefault', [Object, Object, String, String, String])
            registerAllowedMethod('P4SyncDefault', [Object, Object, String, String, String, String])
            registerAllowedMethod('P4SyncDefault', [Object, Object, String, String, String, String, boolean])
            registerAllowedMethod('get_masterfile', [String]) {
                return [EmptyMasterSettings]
            }
            registerAllowedMethod('retryOnFailureCause', [Integer, List, Closure]) { retryCount, list, closure ->
                closure.call()
                return Result.SUCCESS
            }
        }
    }

}
