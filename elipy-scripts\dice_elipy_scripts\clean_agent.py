"""
clean_agent.py
"""
import os
import collections
import click

from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import core, local_paths, LOGGER
from elipy2.exceptions import ELIPYException
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command("clean_agent", short_help="Clean resources on the current machine")
@click.option(
    "--delete-tnt-local",
    default=False,
    help="Delete the `d:\\dev\\tnt\\local` directory",
    type=click.BOOL,
)
@click.option(
    "--delete-packages",
    default=False,
    help="Delete the `d:\\packages` directory",
    type=click.BOOL,
)
@click.option(
    "--delete-packagesdev",
    default=False,
    help="Delete the `d:\\packagesdev` directory",
    type=click.BOOL,
)
@click.option(
    "--delete-pip-cache",
    default=False,
    help="Delete the `d:\\.pip-cache` directory",
    type=click.BOOL,
)
@click.option(
    "--delete-logs",
    default=False,
    help="Delete the `d:\\dev\\logs` directory",
    type=click.BOOL,
)
@click.option(
    "--delete-localpackages",
    default=False,
    help="Delete the `d:\\dev\\tnt\\LocalPackages` directory",
    type=click.BOOL,
)
@click.option(
    "--delete-data-state",
    default=False,
    help="Delete %GAME_DATA_DIR%\\.state directory",
    type=click.BOOL,
)
@click.option(
    "--delete-temp",
    default=False,
    help="Delete the `%temp%` directory",
    type=click.BOOL,
)
@click.option("--data-dir", default="", help="Delete %GAME_DATA_DIR%\\.state directory", type=str)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    delete_tnt_local,
    delete_packages,
    delete_packagesdev,
    delete_pip_cache,
    delete_logs,
    delete_localpackages,
    delete_data_state,
    delete_temp,
    data_dir,
):
    """
    Clean resources on the current machine.
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    deletion_candidate = collections.namedtuple("DeletionCandidate", "name path should_delete")
    candidates = [
        deletion_candidate("tnt_local", local_paths.get_tnt_local_path(), delete_tnt_local),
        deletion_candidate("packages", local_paths.get_packages_path(), delete_packages),
        deletion_candidate("packagesdev", local_paths.get_packagesdev_path(), delete_packagesdev),
        deletion_candidate("pip_cache", local_paths.get_pip_cache_path(), delete_pip_cache),
        deletion_candidate("logs", local_paths.get_logs_path(), delete_logs),
        deletion_candidate(
            "delete_localpackages",
            local_paths.get_tnt_localpackages_path(),
            delete_localpackages,
        ),
        deletion_candidate(
            "data_state",
            local_paths.get_dataset_state_path(data_dir),
            delete_data_state,
        ),
        deletion_candidate("temp", os.environ.get("TEMP"), delete_temp),
    ]

    paths_to_delete = [c for c in candidates if c.should_delete]

    failed_to_delete = False
    LOGGER.info("Start cleaning process.")
    for deletion_candidate in paths_to_delete:
        try:
            LOGGER.info("Trying deletion of {}".format(deletion_candidate))
            core.delete_folder(deletion_candidate.path, close_handles=True)
            LOGGER.info("deletion of {} done".format(deletion_candidate.path))
        except Exception as exc:
            LOGGER.info("Failed deleting {}".format(deletion_candidate.path))
            LOGGER.debug(exc)
            failed_to_delete = True

    if failed_to_delete:
        raise ELIPYException("Failed to delete some directiors when cleaning")
