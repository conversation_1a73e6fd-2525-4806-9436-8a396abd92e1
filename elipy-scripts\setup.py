import os
import subprocess
from setuptools import setup
from distutils.util import strtobool

MAJOR_VERSION = "10"
MINOR_VERSION = "2"
IS_PRERELEASE = bool(strtobool(os.environ.get("IS_PRERELEASE", "true")))
try:  # Get version from Jenkins job
    PATCH_VERSION = str(int(os.environ["CI_PIPELINE_IID"]))

    version_pattern = "%s.%s.%s"
    if IS_PRERELEASE:
        version_pattern = "%s.%sa1.dev%s"

    elipy_ver = version_pattern % (MAJOR_VERSION, MINOR_VERSION, PATCH_VERSION)
    environ = "prod"
except KeyError:  # Fallback to git tag/sha1
    environ = "dev"
    try:
        p = subprocess.run(["git", "rev-parse", "--short", "HEAD"], capture_output=True, check=True)
    except subprocess.CalledProcessError:
        elipy_ver = "0.0.1"
    else:
        elipy_ver = f"0.0.0+{p.stdout.decode('UTF-8').rstrip()}"

setup(
    name="dice_elipy_scripts",
    version="{ver}".format(ver=elipy_ver),
    description="Dice scripts for running Elipy",
    license="EA Digital Illusions CE AB",
    url="https://gitlab.ea.com/dre-cobra/elipy/elipy-scripts",
    author="DICE.Build.Engineers",
    author_email="<EMAIL>",
    python_requires=">=3.7, !=3.9, !=3.10, <3.12",
    packages=["dice_elipy_scripts", "dice_elipy_scripts.gametool", "dice_elipy_scripts.utils"],
    package_data={
        "dice_elipy_scripts": ["*.py", "yml/*.yml", "utils/*.py", "gametool/*.py"],
    },
    include_package_data=True,
    install_requires=[
        "api4jenkins==1.14",
        "click==8.1.3",
        "Deprecated==1.2.13",
        "dicttoxml==1.7.16",
        "pyyaml~=6.0.1",
        "sentry-sdk~=1.22.2",
        "setuptools==67.7.2",
        "winregistry==1.1.1",
    ],
    setup_requires=[
        "dicttoxml==1.7.16",
        "pylint==2.17.4",
        "pytest-mock==3.10.0",
        "pytest-runner==6.0.0",
        "requests_mock==1.9.3",
        "wheel==0.30.0",
        "winregistry==0.8.3",
    ],
    tests_require=[
        "Deprecated==1.2.13",
        "dicttoxml==1.7.16",
        "mock==2.0.0",
        "pylint==2.17.4",
        "pytest-cov==4.0.0",
        "pytest-env==0.8.1",
        "pytest-mock==3.10.0",
        "pytest-xdist==3.2.1",
        "pytest==7.3.1",
        "pyyaml~=6.0.1",
        "requests_mock==1.10.0",
        "six==1.16.0",
        "snowballstemmer==2.2.0",
        "virtualenv==20.26.3",
    ],
    zip_safe=False,
)
