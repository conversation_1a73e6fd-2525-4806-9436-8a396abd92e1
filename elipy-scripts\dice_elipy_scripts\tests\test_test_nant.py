"""
test_test_nant.py

Unit tests for test_nant
"""
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from dice_elipy_scripts.test_nant import cli
from mock import patch


class TestTestNant:
    def __do_test(self, args, flags=[]):
        # setup
        self.patcher_fbcli_run = patch("elipy2.frostbite.fbcli.run")
        self.mock_fbcli_run = self.patcher_fbcli_run.start()
        runner = CliRunner()
        elipy_args = args.copy()
        for crt_flag in flags:
            elipy_args.extend([crt_flag, "true"])
        fbcli_args = args + flags

        # test
        runner.invoke(cli, elipy_args)
        self.mock_fbcli_run.assert_called_once_with("test_nant", method_args=fbcli_args)

        # teardown
        self.patcher_fbcli_run.stop()

    def test_testname(self):
        self.__do_test(["NAntUnitTests"])

    def test_show_output(self):
        self.__do_test(["NAntUnitTests"], ["-show_output"])

    def test_retest_failures(self):
        self.__do_test(["NAntUnitTests"], ["-retest_failures"])

    def test_gensln(self):
        self.__do_test(["NAntUnitTests"], ["-gensln"])

    def test_linux(self):
        self.__do_test(["NAntUnitTests"], ["-linux"])
