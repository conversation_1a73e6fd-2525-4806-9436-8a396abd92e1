package com.ea.project.kin.branchsettings

class Kin_soaks {
    // Settings for jobs
    static Class project = com.ea.project.kin.Kingston
    static Map general_settings = [
        dataset           : project.dataset,
        elipy_install_call: project.elipy_install_call,
        elipy_call        : project.elipy_call,
        frostbite_licensee: project.frostbite_licensee,
        workspace_root    : project.workspace_root,
    ]
    static Map standard_jobs_settings = [
        asset                       : 'ShippingLevels',
        enable_lkg_p4_counters      : true,
        import_avalanche_state      : false,
        poolbuild_data              : true,
        poolbuild_frosty            : true,
        server_asset                : 'ShippingLevels',
        shift_branch                : false,
        timeout_hours_data          : 6,
        use_deprecated_blox_packages: true,
        use_linuxclient             : true,
        skip_icepick_settings_file  : true,
        statebuild_code             : false,
        frosty_reference_job        : 'kin-soaks.code.start',
        skip_clean_label            : true,
    ]
    static Map preflight_settings = [
        concurrent_data            : 4,
        pre_preflight              : true,
        statebuild_datapreflight   : false,
        slack_channel_preflight    : [channels: ['#cobra-build-preflight']],
        datapreflight_reference_job: 'future-dev-content.data.lastknowngood',
        trigger_type               : 'none',
        p4_code_server             : 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
        p4_code_creds              : 'dice-p4buildedge03-fb',
    ]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'ps4', configs: ['performance']],
        [name: 'xb1', configs: ['performance']],
        [name: 'tool', configs: ['release']],
        [name: 'linux64server', configs: ['final']],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = [
        [name: '.code.p4counterupdater', args: ['code_changelist', 'code_countername']],
        [name: '.frosty.start', args: []],
    ]
    static List data_matrix = []
    static List data_downstream_matrix = []
    static List patchdata_matrix = []
    static List frosty_matrix = [
        [name: 'ps4', variants: [[format: 'files', config: 'performance', region: 'eu', args: '']]],
        [name: 'xb1', variants: [[format: 'files', config: 'performance', region: 'ww', args: '']]],
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww', args: '']]],
    ]
    static List frosty_downstream_matrix = [
        [name: '.frosty.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
    ]
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = []
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
