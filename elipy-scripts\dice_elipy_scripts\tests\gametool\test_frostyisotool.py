"""
test_frostyisotool.py

Unit testing for frostyisotool
"""
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import patch, call, MagicMock
from dice_elipy_scripts.gametool.frostyisotool import cli
import os


@patch("dice_elipy_scripts.gametool.frostyisotool.set_licensee", MagicMock())
@patch("dice_elipy_scripts.gametool.frostyisotool.add_sentry_tags", MagicMock())
class TestSubmitFrostyIsoTool:
    OPTION_CHANGELIST = "--changelist"
    OPTION_CODE_CHANGELIST = "--code-changelist"
    OPTION_CLEAN = "--clean"
    OPTION_CONFIG = "--config"
    OPTION_P4_PORT = "--p4-port"
    OPTION_P4_CLIENT = "--p4-client"
    OPTION_PASSWORD = "--password"
    OPTION_EMAIL = "--email"
    OPTION_DOMAIN_USER = "--domain-user"
    OPTION_USER = "--user"
    OPTION_LICENSEE = "--licensee"
    OPTION_FRAMEWORK_ARGS = "--framework-args"
    OPTION_SUBMIT = "--submit"

    VALUE_CHANGELIST = "changelist"
    VALUE_CODE_CHANGELIST = "code_changelist"
    VALUE_CLEAN = "false"
    VALUE_CONFIG = "config"
    VALUE_P4_PORT = "p4_port"
    VALUE_P4_CLIENT = "p4_client"
    VALUE_PASSWORD = "password"
    VALUE_EMAIL = "email"
    VALUE_DOMAIN_USER = "domain_user"
    VALUE_USER = "user"
    VALUE_LICENSEE = "licensee"
    VALUE_FRAMEWORK_ARGS = "framework_args"

    DEFAULT_CL_ARGS = [
        OPTION_CHANGELIST,
        VALUE_CHANGELIST,
        OPTION_CONFIG,
        VALUE_CONFIG,
        OPTION_P4_PORT,
        VALUE_P4_PORT,
        OPTION_P4_CLIENT,
        VALUE_P4_CLIENT,
        OPTION_USER,
        VALUE_USER,
        OPTION_FRAMEWORK_ARGS,
        VALUE_FRAMEWORK_ARGS,
        OPTION_DOMAIN_USER,
        VALUE_DOMAIN_USER,
    ]

    @patch("dice_elipy_scripts.gametool.frostyisotool.p4.P4Utils")
    @patch("dice_elipy_scripts.gametool.frostyisotool.code.CodeUtils")
    def test_frostyisotool(self, mock_code_utils, mock_p4_utils):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_CL_ARGS)
        mock_code_utils.assert_called_once_with(
            platform=None,
            config=self.VALUE_CONFIG,
            monkey_build_label=self.VALUE_CHANGELIST,
            package="FrostyIsoTool",
            target="releasepackage",
        )
        mock_p4_utils.assert_called_once_with(
            self.VALUE_P4_PORT, user=self.VALUE_USER, client=self.VALUE_P4_CLIENT
        )
        mock_p4_utils.return_value.submit.assert_called_once()
        assert result.exit_code == 0

    @patch("dice_elipy_scripts.gametool.frostyisotool.p4.P4Utils", MagicMock())
    @patch("dice_elipy_scripts.gametool.frostyisotool.code.CodeUtils", MagicMock())
    @patch("dice_elipy_scripts.gametool.frostyisotool.authenticate_eapm_credstore")
    def test_submit_with_password_email(self, mock_authenticate_eapm_credstore):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_CL_ARGS
            + [
                self.OPTION_PASSWORD,
                self.VALUE_PASSWORD,
                self.OPTION_EMAIL,
                self.VALUE_EMAIL,
            ],
        )
        mock_authenticate_eapm_credstore.assert_called_once_with(
            self.VALUE_PASSWORD,
            self.VALUE_EMAIL,
            self.VALUE_DOMAIN_USER,
        )
        assert result.exit_code == 0

    @patch("dice_elipy_scripts.gametool.frostyisotool.p4.P4Utils")
    @patch("dice_elipy_scripts.gametool.frostyisotool.code.CodeUtils", MagicMock())
    def test_submit_with_no_submit(self, mock_p4_utils):
        frostyisotool_path = os.path.join("tnt_root", "Code", "Tools", "FrostyIsoTool", "...")
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_CL_ARGS
            + [
                self.OPTION_SUBMIT,
                False,
            ],
        )
        mock_p4_utils.return_value.submit.assert_not_called()
        mock_p4_utils.return_value.revert.assert_has_calls(
            [
                call(
                    path=frostyisotool_path,
                    quiet=True,
                    only_unchanged=True,
                    wipe=False,
                ),
                call(quiet=True),
            ]
        )
        assert result.exit_code == 0

    @patch("dice_elipy_scripts.gametool.frostyisotool.p4.P4Utils", MagicMock())
    @patch("dice_elipy_scripts.gametool.frostyisotool.code.CodeUtils")
    def test_submit_with_clean_flag(self, mock_code_utils):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_CL_ARGS + [self.OPTION_CLEAN, True],
        )
        mock_code_utils.return_value.clean_local.assert_called_once()
        assert result.exit_code == 0

    @patch("dice_elipy_scripts.gametool.frostyisotool.p4.P4Utils", MagicMock())
    @patch("dice_elipy_scripts.gametool.frostyisotool.code.CodeUtils")
    def test_submit_with_code_changelist(self, mock_code_utils):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_CL_ARGS
            + [
                self.OPTION_CODE_CHANGELIST,
                self.VALUE_CODE_CHANGELIST,
            ],
        )
        mock_code_utils.assert_called_once_with(
            platform=None,
            config=self.VALUE_CONFIG,
            monkey_build_label=self.VALUE_CODE_CHANGELIST,
            package="FrostyIsoTool",
            target="releasepackage",
        )
        assert result.exit_code == 0
