{#
    Command:
        test_nant
            short_help: Used to call fb test_nant. Runs the NAnt Regression                tests using the frostbite masterconfig.

    Arguments:
        test

    Required variables:

    Optional variables:
        _show_output
            type: bool
            default: False
            help: Print the output of the test as the test is               running, normally the output is written               to a log and only printed when the test fails.
        _retest_failures
            type: bool
            default: False
            help: Only tests that failed or were not               previously reached in the last run should be tested.
        _gensln
            type: bool
            default: False
            help: Generate a solution for a specific test.
        _linux
            type: bool
            default: False
            help: Runs test_nant in Windows Subsystem for Linux (WSL).               You need wsl installed and mono installed into it for this to work.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    test_nant
    test {{ test }}
    {%- if _show_output %}
    -show_output {{ _show_output }}
    {%- endif %}
    {%- if _retest_failures %}
    -retest_failures {{ _retest_failures }}
    {%- endif %}
    {%- if _gensln %}
    -gensln {{ _gensln }}
    {%- endif %}
    {%- if _linux %}
    -linux {{ _linux }}
    {%- endif %}
  displayName: elipy test_nant
