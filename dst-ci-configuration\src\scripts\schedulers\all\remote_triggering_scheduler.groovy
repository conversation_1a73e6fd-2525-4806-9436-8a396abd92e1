package scripts.schedulers.all

import com.ea.project.GetBranchFile
import com.ea.lib.LibJenkins

/**
 * remote_triggering_scheduler.groovy
 */

pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Trigger the downstream job(s)') {
            steps {
                script {
                    currentBuild.displayName = env.JOB_NAME + '.' + params.data_changelist + '.' + params.code_changelist
                    def args = [
                        string(name: 'code_changelist', value: params.code_changelist),
                        string(name: 'data_changelist', value: params.data_changelist),
                    ]

                    def last_good_code = LibJenkins.getLastStableCodeChangelist(env.JOB_NAME)
                    def last_good_data = LibJenkins.getLastStableDataChangelist(env.JOB_NAME)

                    def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
                    def trigger_settings = branchfile.standard_jobs_settings?.trigger_jobs_from_remote_master ?: []
                    def trigger_on_new_code = trigger_settings.trigger_on_new_code ?: []
                    def trigger_on_new_data = trigger_settings.trigger_on_new_data ?: []

                    if (params.code_changelist != last_good_code) {
                        for (job_name in trigger_on_new_code) {
                            build(job: job_name, wait: false, parameters: args, propagate: false)
                        }
                    }
                    if (params.data_changelist != last_good_data) {
                        for (job_name in trigger_on_new_data) {
                            build(job: job_name, wait: false, parameters: args, propagate: false)
                        }
                    }
                }
            }
        }
    }
}
