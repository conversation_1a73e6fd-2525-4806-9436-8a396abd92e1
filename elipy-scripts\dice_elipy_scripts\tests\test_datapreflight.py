"""
test_datapreflight.py

Unit testing for datapreflight
"""
import copy
import os
import pytest
import tempfile
from typing import List
import unittest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import call, MagicMock, patch
from elipy2.exceptions import ELIPYException
from dice_elipy_scripts.datapreflight import (
    cli,
    cook_all_dbx_assets,
    cook_dbx_assets_individually,
    cook_submitted_dbx_assets,
    validate_direct_dbx_references,
    get_p4_file_data,
)
from dice_elipy_scripts.utils.datapreflight_utils import (
    get_dbx_assets_to_cook,
    get_primary_instance_type,
)


@patch("dice_elipy_scripts.datapreflight.add_sentry_tags", MagicMock())
@patch("dice_elipy_scripts.datapreflight.avalanche", MagicMock())
@patch("dice_elipy_scripts.datapreflight.collect_metrics", MagicMock())
@patch("dice_elipy_scripts.datapreflight.running_processes", MagicMock())
@patch("dice_elipy_scripts.datapreflight.throw_if_files_found", MagicMock())
class TestDataPreflight(unittest.TestCase):
    ARGUMENT_P4PORT = "myjenkinsp4port"
    ARGUMENT_P4CLIENT = "myjenkinsclient"
    ARGUMENT_PLATFORM_1 = "ps4"
    ARGUMENT_PLATFORM_2 = "win64"
    ARGUMENT_CHANGELIST = "1234567"

    OPTION_USER = "--user"
    OPTION_USER_VALUE = "game_user"
    OPTION_ASSET = "--asset"
    OPTION_ASSETS = "--assets"
    OPTION_PIPELINE_BRANCH = "--pipeline-branch"
    OPTION_PIPELINE_BRANCH_VALUE = "code_branch"
    OPTION_PIPELINE_CHANGELIST = "--pipeline-changelist"
    OPTION_PIPELINE_CHANGELIST_VALUE = "1234"
    OPTION_DATA_BRANCH = "--data-branch"
    OPTION_DATA_BRANCH_VALUE = "data_branch"
    OPTION_DATADIR = "--datadir"
    OPTION_DATADIR_VALUE = "data_dir"
    OPTION_IMPORT_AVALANCHE_STATE = "--import-avalanche-state"
    OPTION_DATA_CHANGELIST = "--data-changelist"
    OPTION_DATA_CHANGELIST_VALUE = "1234"
    OPTION_COOK_DBX_ASSETS = "--cook-dbx-assets"
    OPTION_FAIL_ON_DBX_COOK = "--fail-on-dbx-cook"
    OPTION_CLEAN_MASTER_VERSION_CHECK = "--clean-master-version-check"
    OPTION_PIPELINE_ARGS = "--pipeline-args"
    OPTION_INDEXING_ARGS = "--indexing-args"
    OPTION_DO_WARM_MACHINE = "--do-warmup"
    OPTION_USE_LOCAL_CODEBUILDS = "--use-local-codebuilds"
    OPTION_SKIP_REVERT = "--skip-revert"
    OPTION_SKIP_UNSHELVE = "--skip-unshelve"
    OPTION_VALIDATE_DIRECT_REFERENCES = "--validate-direct-references"
    OPTION_CLEAN_INDEX = "--clean-index"
    OPTION_RESPONSE_FILE = "--use-response-file"
    OPTION_RESPONSE_FILE_VALUE = "False"
    OPTION_P4_UNSHELVE_FILE = "--p4-unshelve-file"
    VALUE_P4_OUTPUT_FILE = "p4unshelve_out.txt"

    VALUE_ASSET = "test_levels"
    VALUE_ASSETS_1 = "level1"
    VALUE_ASSETS_2 = "level2"
    VALUE_PIPELINE_ARGS = "arg1"
    VALUE_INDEXING_ARGS = "arg1"
    VALUE_VALIDATE_DIRECT_REFERENCES = "true"
    VALUE_CLEAN_INDEX = "true"
    VALUE_USE_RESPONSE_FILE = True

    PRE_PLATFORM_ARGS = [
        ARGUMENT_P4PORT,
        ARGUMENT_P4CLIENT,
    ]
    POST_PLATFORM_ARGS = [
        ARGUMENT_CHANGELIST,
        OPTION_USER,
        OPTION_USER_VALUE,
        OPTION_ASSETS,
        VALUE_ASSETS_1,
        OPTION_PIPELINE_BRANCH,
        OPTION_PIPELINE_BRANCH_VALUE,
        OPTION_PIPELINE_CHANGELIST,
        OPTION_PIPELINE_CHANGELIST_VALUE,
    ]
    DEFAULT_ARGS = PRE_PLATFORM_ARGS + [ARGUMENT_PLATFORM_1] + POST_PLATFORM_ARGS
    INDEX_ARGS = {"index": True, "platforms": ["index"], "is_local": True, "pipeline_args": []}

    def setUp(self):
        self.patcher_datautils = patch("elipy2.data.DataUtils", autospec=True)
        self.mock_datautils = self.patcher_datautils.start()

        self.patcher_filerutils = patch("elipy2.filer.FilerUtils", autospec=True)
        self.mock_filerutils = self.patcher_filerutils.start()

        self.patcher_p4utils = patch("elipy2.p4.P4Utils", autospec=True)
        self.mock_p4utils = self.patcher_p4utils.start()

        self.patcher_get_dbx_assets_to_cook = patch(
            "dice_elipy_scripts.datapreflight.get_dbx_assets_to_cook"
        )
        self.mock_get_dbx_assets_to_cook = self.patcher_get_dbx_assets_to_cook.start()

        self.patcher_extract_layers_from_file_list = patch(
            "dice_elipy_scripts.datapreflight.extract_layers_from_file_list"
        )
        self.mock_extract_layers_from_file_list = self.patcher_extract_layers_from_file_list.start()

        self.patcher_raise_if_cl_not_exists = patch(
            "dice_elipy_scripts.datapreflight.raise_if_cl_not_exists"
        )
        self.mock_raise_if_cl_not_exists = self.patcher_raise_if_cl_not_exists.start()

        self.patcher_raise_if_wrong_stream = patch(
            "dice_elipy_scripts.datapreflight.raise_if_wrong_stream"
        )
        self.mock_raise_if_wrong_stream = self.patcher_raise_if_wrong_stream.start()

        self.patcher_logger_info = patch("elipy2.LOGGER.info")
        self.mock_logger_info = self.patcher_logger_info.start()

    def tearDown(self):
        patch.stopall()

    def test_default_args(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 0
        self.mock_p4utils.return_value.unshelve.assert_called_once_with(self.ARGUMENT_CHANGELIST)

    def test_args_for_cook(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 0

    def test_warmup_machine(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS + [self.OPTION_DO_WARM_MACHINE])
        assert result.exit_code == 0
        self.mock_p4utils.return_value.unshelve.assert_not_called()

    def test_p4_missing_changelist(self):
        self.mock_raise_if_cl_not_exists.side_effect = ELIPYException()
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert isinstance(result.exception, ELIPYException)

    def test_p4_found_changelist(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 0
        self.mock_raise_if_cl_not_exists.assert_called_once_with(
            self.mock_p4utils.return_value, self.ARGUMENT_CHANGELIST
        )

    def test_p4_correct_stream(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 0
        self.mock_raise_if_wrong_stream.assert_called_once_with(
            self.mock_p4utils.return_value, self.ARGUMENT_CHANGELIST
        )

    def test_p4_wrong_stream(self):
        self.mock_raise_if_wrong_stream.side_effect = ELIPYException()
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 1

    def test_get_dbx_assets_to_cook_called(self):
        self.mock_p4utils.return_value.unshelve.return_value = ["file1.txt", "file2.dbx"]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.PRE_PLATFORM_ARGS
            + [self.ARGUMENT_PLATFORM_2]
            + self.POST_PLATFORM_ARGS
            + [self.OPTION_COOK_DBX_ASSETS],
        )
        assert result.exit_code == 0
        # TODO: add one test for when called with a layer name
        self.mock_get_dbx_assets_to_cook.assert_called_once_with(
            ["file1.txt", "file2.dbx"], "data", "source"
        )

    def test_extract_layers_from_file_list_called(self):
        self.mock_p4utils.return_value.unshelve.return_value = ["file1.txt", "file2.dbx"]
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS + ["--auto-content-layers", True])
        assert result.exit_code == 0
        self.mock_extract_layers_from_file_list.assert_called_once_with(
            p4_unshelved_files=["file1.txt", "file2.dbx"], datadir="data"
        )

    def test_get_dbx_assets_to_cook_not_called_no_flag(self):
        self.mock_p4utils.return_value.unshelve.return_value = ["file1.txt", "file2.dbx"]
        runner = CliRunner()
        result = runner.invoke(
            cli, self.PRE_PLATFORM_ARGS + [self.ARGUMENT_PLATFORM_2] + self.POST_PLATFORM_ARGS
        )
        assert result.exit_code == 0
        self.mock_get_dbx_assets_to_cook.assert_not_called()

    def test_get_dbx_assets_to_cook_not_called_non_win64(self):
        self.mock_p4utils.return_value.unshelve.return_value = ["file1.txt", "file2.dbx"]
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS + [self.OPTION_COOK_DBX_ASSETS])
        assert result.exit_code == 0
        self.mock_get_dbx_assets_to_cook.assert_not_called()

    def test_get_dbx_assets_to_cook_not_called_warmup(self):
        self.mock_p4utils.return_value.unshelve.return_value = ["file1.txt", "file2.dbx"]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.PRE_PLATFORM_ARGS
            + [self.ARGUMENT_PLATFORM_2]
            + self.POST_PLATFORM_ARGS
            + [self.OPTION_COOK_DBX_ASSETS, self.OPTION_DO_WARM_MACHINE],
        )
        assert result.exit_code == 0
        self.mock_get_dbx_assets_to_cook.assert_not_called()

    def helper_validate_direct_references_cook_args(self, content_layer: str) -> dict:
        """
        Helper to de-duplicate properties in VDR tests
        """
        cook_args = {
            "pipeline_args": [self.VALUE_PIPELINE_ARGS],
            "indexing_args": [self.VALUE_INDEXING_ARGS],
            "collect_mdmps": True,
            "clean_index": False,
            "clean_master_version_check": False,
            "trim": False,
            "only_indexing": False,
            "skip_indexing": True,
        }
        if content_layer:
            cook_args["pipeline_args"] += ["-activeContentLayer", content_layer]
        return cook_args

    def helper_validate_direct_references_common_args(
        self, use_response_file: bool, content_layer: str
    ) -> List[str]:
        """
        Helper to de-duplicate running datapreflight.cli in VDR tests
        """
        arg_list = []
        arg_list += self.PRE_PLATFORM_ARGS
        arg_list += [self.ARGUMENT_PLATFORM_2]
        arg_list += self.POST_PLATFORM_ARGS
        if content_layer:
            arg_list += ["--content-layer", content_layer]
        arg_list += [
            self.OPTION_VALIDATE_DIRECT_REFERENCES,
            self.VALUE_VALIDATE_DIRECT_REFERENCES,
            self.OPTION_PIPELINE_ARGS,
            self.VALUE_PIPELINE_ARGS,
            self.OPTION_INDEXING_ARGS,
            self.VALUE_INDEXING_ARGS,
        ]
        if use_response_file:
            arg_list += [
                self.OPTION_RESPONSE_FILE,
                self.VALUE_USE_RESPONSE_FILE,
            ]
        return arg_list

    @patch("dice_elipy_scripts.datapreflight.validate_direct_dbx_references")
    def test_validate_direct_references(self, mock_validate_direct_dbx_references):
        self.mock_p4utils.return_value.unshelve.return_value = ["file1.txt", "file2.dbx"]
        self.mock_get_dbx_assets_to_cook.return_value = ["file2.dbx"]
        content_layer = ""
        cook_args = self.helper_validate_direct_references_cook_args(content_layer)
        arg_list = self.helper_validate_direct_references_common_args(False, content_layer)
        runner = CliRunner()
        result = runner.invoke(cli, arg_list)
        assert result.exit_code == 0
        self.mock_get_dbx_assets_to_cook.assert_called_once_with(
            ["file1.txt", "file2.dbx"], "data", "source", exclude_files=False
        )
        mock_validate_direct_dbx_references.assert_called_once_with(
            ["file2.dbx"],
            self.ARGUMENT_PLATFORM_2,
            cook_args,
            False,
        )

    @patch("dice_elipy_scripts.datapreflight.validate_direct_dbx_references")
    def test_validate_direct_references_with_content_layer(
        self, mock_validate_direct_dbx_references
    ):
        self.mock_p4utils.return_value.unshelve.return_value = ["file1.txt", "file2.dbx"]
        self.mock_get_dbx_assets_to_cook.return_value = ["file2.dbx"]
        content_layer = "C1S2B1"
        cook_args_layer = self.helper_validate_direct_references_cook_args(content_layer)
        cook_args_source = self.helper_validate_direct_references_cook_args("")
        arg_list = self.helper_validate_direct_references_common_args(False, content_layer)
        runner = CliRunner()
        result = runner.invoke(cli, arg_list)
        assert result.exit_code == 0
        assert len(self.mock_get_dbx_assets_to_cook.call_args_list) == 2
        assert self.mock_get_dbx_assets_to_cook.call_args_list[0] == call(
            ["file1.txt", "file2.dbx"], "data", content_layer, exclude_files=False
        )
        assert self.mock_get_dbx_assets_to_cook.call_args_list[1] == call(
            ["file1.txt", "file2.dbx"], "data", "source", exclude_files=False
        )

        assert len(mock_validate_direct_dbx_references.call_args_list) == 2
        assert mock_validate_direct_dbx_references.call_args_list[0] == call(
            ["file2.dbx"],
            self.ARGUMENT_PLATFORM_2,
            cook_args_layer,
            False,
        )
        assert mock_validate_direct_dbx_references.call_args_list[1] == call(
            ["file2.dbx"],
            self.ARGUMENT_PLATFORM_2,
            cook_args_source,
            False,
        )

    @patch("dice_elipy_scripts.datapreflight.validate_direct_dbx_references")
    def test_validate_direct_references_use_response(self, mock_validate_direct_dbx_references):
        self.mock_p4utils.return_value.unshelve.return_value = ["file1.txt", "file2.dbx"]
        self.mock_get_dbx_assets_to_cook.return_value = ["file2.dbx"]
        content_layer = ""
        cook_args = self.helper_validate_direct_references_cook_args(content_layer)
        arg_list = self.helper_validate_direct_references_common_args(True, content_layer)
        runner = CliRunner()
        result = runner.invoke(cli, arg_list)
        assert result.exit_code == 0
        self.mock_get_dbx_assets_to_cook.assert_called_once_with(
            ["file1.txt", "file2.dbx"], "data", "source", exclude_files=False
        )
        mock_validate_direct_dbx_references.assert_called_once_with(
            ["file2.dbx"],
            self.ARGUMENT_PLATFORM_2,
            cook_args,
            True,
        )

    @patch("dice_elipy_scripts.datapreflight.validate_direct_dbx_references")
    def test_validate_direct_references_use_response_and_content_layer(
        self, mock_validate_direct_dbx_references
    ):
        self.mock_p4utils.return_value.unshelve.return_value = ["file1.txt", "file2.dbx"]
        self.mock_get_dbx_assets_to_cook.return_value = ["file2.dbx"]
        content_layer = "C1S2B1"
        cook_args_layer = self.helper_validate_direct_references_cook_args(content_layer)
        cook_args_source = self.helper_validate_direct_references_cook_args("")
        arg_list = self.helper_validate_direct_references_common_args(True, content_layer)
        runner = CliRunner()
        result = runner.invoke(cli, arg_list)
        assert result.exit_code == 0
        assert len(self.mock_get_dbx_assets_to_cook.call_args_list) == 2
        assert self.mock_get_dbx_assets_to_cook.call_args_list[0] == call(
            ["file1.txt", "file2.dbx"], "data", content_layer, exclude_files=False
        )
        assert self.mock_get_dbx_assets_to_cook.call_args_list[1] == call(
            ["file1.txt", "file2.dbx"], "data", "source", exclude_files=False
        )

        assert len(mock_validate_direct_dbx_references.call_args_list) == 2
        assert mock_validate_direct_dbx_references.call_args_list[0] == call(
            ["file2.dbx"],
            self.ARGUMENT_PLATFORM_2,
            cook_args_layer,
            True,
        )
        assert mock_validate_direct_dbx_references.call_args_list[1] == call(
            ["file2.dbx"],
            self.ARGUMENT_PLATFORM_2,
            cook_args_source,
            True,
        )

    def test_cook_called(self):
        cook_args = {
            "pipeline_args": [],
            "indexing_args": [],
            "collect_mdmps": True,
            "clean_index": False,
            "clean_master_version_check": False,
            "trim": False,
            "only_indexing": False,
            "skip_indexing": True,
        }
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 0
        self.mock_datautils.assert_called_once_with(self.ARGUMENT_PLATFORM_1, [self.VALUE_ASSETS_1])
        assert self.mock_datautils.return_value.cook.call_count == 2
        assert self.mock_datautils.return_value.cook.call_args_list[1] == call(**cook_args)

    def test_cook_called_source_layer(self):
        cook_args = {
            "pipeline_args": [],
            "indexing_args": [],
            "collect_mdmps": True,
            "clean_index": False,
            "clean_master_version_check": False,
            "trim": False,
            "only_indexing": False,
            "skip_indexing": True,
        }
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS + ["--content-layer", "source"])
        assert result.exit_code == 0
        self.mock_datautils.assert_called_once_with(self.ARGUMENT_PLATFORM_1, [self.VALUE_ASSETS_1])
        assert self.mock_datautils.return_value.cook.call_count == 2
        assert self.mock_datautils.return_value.cook.call_args_list[1] == call(**cook_args)

    def test_cook_called_source_layers(self):
        cook_args = {
            "pipeline_args": [],
            "indexing_args": [],
            "collect_mdmps": True,
            "clean_index": False,
            "clean_master_version_check": False,
            "trim": False,
            "only_indexing": False,
            "skip_indexing": True,
        }
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS + ["--content-layers", "source"])
        assert result.exit_code == 0
        self.mock_datautils.assert_called_once_with(self.ARGUMENT_PLATFORM_1, [self.VALUE_ASSETS_1])
        assert self.mock_datautils.return_value.cook.call_count == 2
        assert self.mock_datautils.return_value.cook.call_args_list[1] == call(**cook_args)

    def test_cook_called_content_layer(self):
        layer_name = "C1S2B1"
        cook_args = {
            "pipeline_args": ["-test"],
            "indexing_args": [],
            "collect_mdmps": True,
            "clean_index": False,
            "clean_master_version_check": False,
            "trim": False,
            "only_indexing": False,
            "skip_indexing": True,
        }
        content_layer_cook_args = copy.deepcopy(cook_args)
        content_layer_cook_args["pipeline_args"] += ["-activeContentLayer", layer_name]

        runner = CliRunner()
        result = runner.invoke(
            cli, self.DEFAULT_ARGS + ["--pipeline-args", "-test", "--content-layer", layer_name]
        )
        assert result.exit_code == 0
        self.mock_datautils.assert_called_once_with(self.ARGUMENT_PLATFORM_1, [self.VALUE_ASSETS_1])
        assert self.mock_datautils.return_value.cook.call_count == 3
        assert self.mock_datautils.return_value.cook.call_args_list[1] == call(
            **content_layer_cook_args
        )
        assert self.mock_datautils.return_value.cook.call_args_list[2] == call(**cook_args)

    def test_cook_called_content_layers(self):
        layer_name = "C1S2B1"
        cook_args = {
            "pipeline_args": ["-test"],
            "indexing_args": [],
            "collect_mdmps": True,
            "clean_index": False,
            "clean_master_version_check": False,
            "trim": False,
            "only_indexing": False,
            "skip_indexing": True,
        }

        indexing_cook_args = copy.deepcopy(cook_args)
        indexing_cook_args["only_indexing"] = True
        indexing_cook_args["skip_indexing"] = False

        content_layer_cook_args = copy.deepcopy(cook_args)
        content_layer_cook_args["pipeline_args"] += ["-activeContentLayer", layer_name]

        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_ARGS
            + [
                "--pipeline-args",
                "-test",
                "--content-layers",
                layer_name,
                "--content-layers",
                "Source",
            ],
        )
        assert result.exit_code == 0
        self.mock_datautils.assert_called_once_with(self.ARGUMENT_PLATFORM_1, [self.VALUE_ASSETS_1])
        assert self.mock_datautils.return_value.cook.call_count == 3
        assert self.mock_datautils.return_value.cook.call_args_list[0] == call(**indexing_cook_args)
        assert self.mock_datautils.return_value.cook.call_args_list[1] == call(
            **content_layer_cook_args
        )
        assert self.mock_datautils.return_value.cook.call_args_list[2] == call(**cook_args)

    def test_cook_called_pipeline_args(self):
        cook_args = {
            "pipeline_args": [self.VALUE_PIPELINE_ARGS],
            "indexing_args": [],
            "collect_mdmps": True,
            "clean_index": False,
            "clean_master_version_check": False,
            "trim": False,
            "only_indexing": False,
            "skip_indexing": True,
        }
        runner = CliRunner()
        result = runner.invoke(
            cli, self.DEFAULT_ARGS + [self.OPTION_PIPELINE_ARGS, self.VALUE_PIPELINE_ARGS]
        )
        assert result.exit_code == 0
        assert self.mock_datautils.return_value.cook.call_count == 2
        assert self.mock_datautils.return_value.cook.call_args_list[1] == call(**cook_args)

    def test_cook_called_pipeline_args_validate_direct_references(self):
        cook_args = {
            "pipeline_args": [self.VALUE_PIPELINE_ARGS],
            "indexing_args": [],
            "collect_mdmps": True,
            "clean_index": False,
            "clean_master_version_check": False,
            "trim": False,
            "only_indexing": False,
            "skip_indexing": True,
        }
        references_cook_args = {
            "pipeline_args": [self.VALUE_PIPELINE_ARGS]
            + [
                "-f",
                "validateDirectReferences",
                "-functionParameters",
                "\"{['LimitDirectRefErrorsToInput']=true}\"",
                "-Pipeline.MaxWarnings",
                "0",
            ],
            "indexing_args": [],
            "collect_mdmps": True,
            "clean_index": False,
            "clean_master_version_check": False,
            "trim": False,
            "only_indexing": False,
            "skip_indexing": True,
            "use_response_file": self.VALUE_USE_RESPONSE_FILE,
        }
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.PRE_PLATFORM_ARGS
            + [self.ARGUMENT_PLATFORM_2]
            + self.POST_PLATFORM_ARGS
            + [
                self.OPTION_PIPELINE_ARGS,
                self.VALUE_PIPELINE_ARGS,
                self.OPTION_VALIDATE_DIRECT_REFERENCES,
                self.VALUE_VALIDATE_DIRECT_REFERENCES,
                self.OPTION_RESPONSE_FILE,
                self.VALUE_USE_RESPONSE_FILE,
            ],
        )
        assert result.exit_code == 0
        self.mock_datautils.return_value.cook.assert_has_calls(
            [call(**references_cook_args), call(**cook_args)]
        )

    @patch("dice_elipy_scripts.datapreflight.import_avalanche_data_state")
    def test_cook_called_pipeline_and_avalanche_args(self, mock_import_avalanche_data_state):
        mock_import_avalanche_data_state.return_value = ["avalanche_arg"]
        cook_args = {
            "pipeline_args": ["avalanche_arg", self.VALUE_PIPELINE_ARGS],
            "indexing_args": [],
            "collect_mdmps": True,
            "clean_index": False,
            "clean_master_version_check": False,
            "trim": False,
            "only_indexing": False,
            "skip_indexing": True,
        }
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_ARGS
            + [
                self.OPTION_PIPELINE_ARGS,
                self.VALUE_PIPELINE_ARGS,
                self.OPTION_IMPORT_AVALANCHE_STATE,
            ],
        )
        assert result.exit_code == 0
        assert self.mock_datautils.return_value.cook.call_count == 2
        assert self.mock_datautils.return_value.cook.call_args_list[1] == call(**cook_args)

    def test_cook_called_multiple_assets(self):
        cook_args = {
            "pipeline_args": [],
            "indexing_args": [],
            "collect_mdmps": True,
            "clean_index": False,
            "clean_master_version_check": False,
            "trim": False,
            "only_indexing": False,
            "skip_indexing": True,
        }
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS + [self.OPTION_ASSETS, self.VALUE_ASSETS_2])
        assert result.exit_code == 0
        self.mock_datautils.assert_called_once_with(
            self.ARGUMENT_PLATFORM_1, [self.VALUE_ASSETS_1, self.VALUE_ASSETS_2]
        )
        assert self.mock_datautils.return_value.cook.call_count == 2
        assert self.mock_datautils.return_value.cook.call_args_list[1] == call(**cook_args)

    def test_cook_called_deprecated_asset_parameter(self):
        cook_args = {
            "pipeline_args": [],
            "indexing_args": [],
            "collect_mdmps": True,
            "clean_index": False,
            "clean_master_version_check": False,
            "trim": False,
            "only_indexing": False,
            "skip_indexing": True,
        }
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.ARGUMENT_P4PORT,
                self.ARGUMENT_P4CLIENT,
                self.ARGUMENT_PLATFORM_1,
                self.ARGUMENT_CHANGELIST,
                self.OPTION_USER,
                self.OPTION_USER_VALUE,
                self.OPTION_ASSET,
                self.VALUE_ASSET,
                self.OPTION_PIPELINE_BRANCH,
                self.OPTION_PIPELINE_BRANCH_VALUE,
                self.OPTION_PIPELINE_CHANGELIST,
                self.OPTION_PIPELINE_CHANGELIST_VALUE,
            ],
        )
        assert result.exit_code == 0
        self.mock_datautils.assert_called_once_with(self.ARGUMENT_PLATFORM_1, [self.VALUE_ASSET])
        assert self.mock_datautils.return_value.cook.call_count == 2
        assert self.mock_datautils.return_value.cook.call_args_list[1] == call(**cook_args)

    def test_cook_called_clean_index(self):
        cook_args = {
            "pipeline_args": [],
            "indexing_args": [],
            "collect_mdmps": True,
            "clean_index": True,
            "clean_master_version_check": False,
            "trim": False,
            "only_indexing": False,
            "skip_indexing": True,
        }
        runner = CliRunner()
        result = runner.invoke(
            cli, self.DEFAULT_ARGS + [self.OPTION_CLEAN_INDEX, self.VALUE_CLEAN_INDEX]
        )
        assert result.exit_code == 0
        assert self.mock_datautils.return_value.cook.call_count == 2
        assert self.mock_datautils.return_value.cook.call_args_list[1] == call(**cook_args)

    def test_validate_direct_dbx_references(self):
        validate_direct_dbx_references(
            ["asset_1.dbx, asset_2.dbx"],
            "win64",
            {"pipeline_args": ["arg1", "arg2"], "trim": False},
            False,
        )
        self.mock_datautils.assert_called_once_with("win64", ["asset_1.dbx, asset_2.dbx"])
        self.mock_datautils.return_value.cook.assert_called_once_with(
            pipeline_args=[
                "arg1",
                "arg2",
                "-f",
                "validateDirectReferences",
                "-functionParameters",
                "\"{['LimitDirectRefErrorsToInput']=true}\"",
                "-Pipeline.MaxWarnings",
                "0",
            ],
            trim=False,
            use_response_file=False,
        )

    def test_cook_all_dbx_assets(self):
        cook_all_dbx_assets(["asset1", "asset2"], "xb1", {}, False)
        assert self.mock_datautils.return_value.cook.call_count == 1

    def test_cook_all_dbx_assets_error(self):
        self.mock_datautils.return_value.cook.side_effect = Exception("test exception")
        result = cook_all_dbx_assets(["asset1", "asset2"], "xb1", {}, False)
        assert len(result) == 1

    @patch("dice_elipy_scripts.datapreflight.cook_dbx_assets_individually")
    def test_cook_submitted_dbx_assets_small(self, mock_cook_dbx_assets_individually):
        asset_list = ["asset1", "asset2"]
        cook_submitted_dbx_assets(asset_list, "win64", {"cook": "args"}, False, False)
        mock_cook_dbx_assets_individually.assert_called_once_with(
            asset_list, "win64", {"cook": "args"}
        )

    @patch("dice_elipy_scripts.datapreflight.cook_dbx_assets_individually")
    @patch("dice_elipy_scripts.datapreflight.cook_all_dbx_assets")
    def test_cook_submitted_dbx_assets_small_use_response(
        self, mock_cook_all_dbx_assets, mock_cook_dbx_assets_individually
    ):
        asset_list = ["asset1", "asset2"]
        cook_submitted_dbx_assets(asset_list, "win64", {"cook": "args"}, False, True)
        mock_cook_dbx_assets_individually.assert_not_called()
        mock_cook_all_dbx_assets.assert_called_once_with(
            asset_list, "win64", {"cook": "args"}, True
        )

    @patch("dice_elipy_scripts.datapreflight.cook_all_dbx_assets")
    def test_cook_submitted_dbx_assets_large_use_response(self, mock_cook_all_dbx_assets):
        asset_list = [
            "asset1",
            "asset2",
            "asset3",
            "asset4",
            "asset5",
            "asset6",
            "asset7",
            "asset8",
            "asset9",
            "asset10",
            "asset11",
            "asset12",
        ]
        cook_submitted_dbx_assets(asset_list, "win64", {"cook": "args"}, False, True)
        mock_cook_all_dbx_assets.assert_called_once_with(
            asset_list, "win64", {"cook": "args"}, True
        )

    @patch("dice_elipy_scripts.datapreflight.cook_all_dbx_assets")
    def test_cook_submitted_dbx_assets_large(self, mock_cook_all_dbx_assets):
        asset_list = [
            "asset1",
            "asset2",
            "asset3",
            "asset4",
            "asset5",
            "asset6",
            "asset7",
            "asset8",
            "asset9",
            "asset10",
            "asset11",
            "asset12",
        ]
        cook_submitted_dbx_assets(asset_list, "win64", {"cook": "args"}, False, False)
        mock_cook_all_dbx_assets.assert_called_once_with(
            asset_list, "win64", {"cook": "args"}, False
        )

    @patch("dice_elipy_scripts.datapreflight.cook_dbx_assets_individually")
    def test_cook_submitted_dbx_assets_error_not_failing(self, mock_cook_dbx_assets_individually):
        mock_cook_dbx_assets_individually.return_value = [Exception()]
        asset_list = ["asset1", "asset2"]
        cook_submitted_dbx_assets(asset_list, "win64", {"cook": "args"}, False, False)

    @patch("dice_elipy_scripts.datapreflight.cook_dbx_assets_individually")
    def test_cook_submitted_dbx_assets_error_failing(self, mock_cook_dbx_assets_individually):
        mock_cook_dbx_assets_individually.return_value = [Exception()]
        asset_list = ["asset1", "asset2"]
        with pytest.raises(ELIPYException):
            cook_submitted_dbx_assets(asset_list, "win64", {"cook": "args"}, True, False)

    @patch("dice_elipy_scripts.datapreflight.cook_all_dbx_assets")
    def test_cook_dbx_assets_individually(self, mock_cook_all_dbx_assets):
        asset_list = ["asset1", "asset2"]
        assert cook_dbx_assets_individually(asset_list, "win64", {"cook": "args"}) == []
        assert mock_cook_all_dbx_assets.call_count == 2

    @patch("dice_elipy_scripts.datapreflight.cook_all_dbx_assets")
    def test_cook_dbx_assets_individually_error(self, mock_cook_all_dbx_assets):
        mock_cook_all_dbx_assets.side_effect = [[], [Exception()]]
        asset_list = ["asset1", "asset2"]
        result = cook_dbx_assets_individually(asset_list, "win64", {"cook": "args"})
        assert len(result) == 1

    @patch("dice_elipy_scripts.datapreflight.filer.FilerUtils", return_value=MagicMock())
    def test_download_server_binaries(self, patch_filer):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)

        patch_filer.return_value.fetch_code.assert_called_with(
            self.OPTION_PIPELINE_BRANCH_VALUE,
            self.OPTION_PIPELINE_CHANGELIST_VALUE,
            "pipeline",
            "release",
            use_bilbo=False,
            target_build_share=None,
        )
        assert result.exit_code == 0

    @patch("dice_elipy_scripts.datapreflight.filer.FilerUtils", return_value=MagicMock())
    def test_ignore_download_server_binaries(self, patch_filer):
        runner = CliRunner()

        result = runner.invoke(cli, self.DEFAULT_ARGS + [self.OPTION_USE_LOCAL_CODEBUILDS, "true"])

        patch_filer.return_value.fetch_code.assert_not_called()
        assert result.exit_code == 0

    def test_skip_revert(self):
        runner = CliRunner()

        result = runner.invoke(cli, self.DEFAULT_ARGS + [self.OPTION_SKIP_REVERT, "true"])

        self.mock_p4utils.return_value.revert.assert_not_called()
        self.mock_p4utils.return_value.unshelve.assert_called_once()

        assert result.exit_code == 0

    def test_skip_unshelve(self):
        runner = CliRunner()

        result = runner.invoke(cli, self.DEFAULT_ARGS + [self.OPTION_SKIP_UNSHELVE, "true"])
        self.mock_p4utils.return_value.revert.assert_called()
        self.mock_p4utils.return_value.unshelve.assert_not_called()
        assert result.exit_code == 0

    @patch("dice_elipy_scripts.datapreflight.get_p4_file_data")
    def test_run_p4_output_file(self, get_p4_file_data):
        self.mock_p4utils.return_value.unshelve.return_value = ["file1.txt", "file2.dbx"]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_ARGS
            + [
                self.OPTION_P4_UNSHELVE_FILE,
                self.VALUE_P4_OUTPUT_FILE,
            ],
        )
        get_p4_file_data.return_value = []
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        assert self.mock_p4utils.return_value.unshelve.call_count == 0
        assert self.mock_datautils.return_value.cook.call_count == 2
        assert get_p4_file_data.call_count == 1

    @patch("dice_elipy_scripts.datapreflight.get_p4_file_data")
    def test_get_dbx_assets_to_cook_with_p4_unshelve_file(self, get_p4_file_data):
        self.mock_p4utils.return_value.unshelve.return_value = []
        get_p4_file_data.return_value = ["file1.txt", "file2.dbx"]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.PRE_PLATFORM_ARGS
            + [self.ARGUMENT_PLATFORM_2]
            + self.POST_PLATFORM_ARGS
            + [
                self.OPTION_COOK_DBX_ASSETS,
                self.OPTION_P4_UNSHELVE_FILE,
                self.VALUE_P4_OUTPUT_FILE,
            ],
        )
        assert result.exit_code == 0
        assert self.mock_p4utils.return_value.unshelve.call_count == 0
        self.mock_get_dbx_assets_to_cook.assert_called_once_with(
            ["file1.txt", "file2.dbx"], "data", "source"
        )

    def test_p4_output_file_not_exist(self):
        p4_files = get_p4_file_data("nonexistent.txt")
        self.mock_logger_info.assert_has_calls(
            [
                call("File nonexistent.txt does not exist."),
            ],
            any_order=True,
        )
        assert p4_files == []

    @patch("dice_elipy_scripts.utils.datapreflight_utils.get_primary_instance_type")
    @patch("dice_elipy_scripts.utils.datapreflight_utils.get_layer_hierarchy")
    def test_get_p4_file_data(self, get_layer_hierarchy, get_primary_instance_type):
        get_layer_hierarchy.return_value = ["TestLayer_01", "C1S2B1"]
        get_primary_instance_type.return_value = "Instance.Type"
        with tempfile.TemporaryDirectory() as temp_dir:
            with open(os.path.join(temp_dir, self.VALUE_P4_OUTPUT_FILE), "w") as fp:
                fp.write(
                    "[pitee] Loading settings from: C:\\_tasks\\P4_3819c081-d698-4728-ae1b-16e869c80352\\4.5.1\n"
                )
                fp.write(
                    "[pitee] Running command: pitee.exe 'C:\\_tasks\\P4_3819c081-d698-4728-ae1b-16e869c80352\\4.5.1\\p4.exe -p p4port:2001 -u DICE\\svc_act -c partitioned_ws_Glacier_CH1-content-dev_c1-dw3-ss-kzgl_c4b5d253 unshelve -s 22787633'\n"
                )
                fp.write(
                    "//bf/CH1/CH1-content-dev/bfdata/Source/Game/Dev/AssetGyms/AssetGym_Benchmarks/Terrain/TerrainLayers/87725AA3/Tile_L5_X16_Y17.dbx#1 - unshelved, opened for edit\n"
                )
                fp.write(
                    "//bf/CH1/CH1-content-dev/bfdata/Layers/TestLayer_01/Some/Random/Asset/Name.dbx#1 - unshelved, opened for edit\n"
                )
                fp.write(
                    "//bf/CH1/CH1-content-dev/TnT/dummy.txt#10 - unshelved, opened for delete\n"
                )
                fp.write(
                    "//bf/CH1/CH1-content-dev/TnT/dummy2.txt#none - unshelved, opened for add\n"
                )
                fp.close()
            results = get_p4_file_data(os.path.join(temp_dir, self.VALUE_P4_OUTPUT_FILE))
            assert results == [
                {
                    b"depotFile": b"//bf/CH1/CH1-content-dev/bfdata/Source/Game/Dev/AssetGyms/AssetGym_Benchmarks/Terrain/TerrainLayers/87725AA3/Tile_L5_X16_Y17.dbx",
                    b"action": b"edit",
                },
                {
                    b"depotFile": b"//bf/CH1/CH1-content-dev/bfdata/Layers/TestLayer_01/Some/Random/Asset/Name.dbx",
                    b"action": b"edit",
                },
                {
                    b"depotFile": b"//bf/CH1/CH1-content-dev/TnT/dummy.txt",
                    b"action": b"delete",
                },
                {
                    b"depotFile": b"//bf/CH1/CH1-content-dev/TnT/dummy2.txt",
                    b"action": b"add",
                },
            ]
            assets_to_cook = get_dbx_assets_to_cook(results, "bfdata", "TestLayer_01", False)
            assert assets_to_cook == [
                "Game/Dev/AssetGyms/AssetGym_Benchmarks/Terrain/TerrainLayers/87725AA3/Tile_L5_X16_Y17",
                "Some/Random/Asset/Name",
            ]
