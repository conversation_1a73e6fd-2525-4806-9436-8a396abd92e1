#!/bin/bash
DIR="$@"
JENKINS_VALIDATION_URL="https://dice-build-jenkins.cobra.dre.ea.com/pipeline-model-converter/validate"
AUTH_STRING="-u $JENKINS_USER:$JENKINS_API_TOKEN_DICE_BUILD_JENKINS"
REPORT_PATH="build/reports/schedulers"
[ ! -d $REPORT_PATH ] && mkdir -p $REPORT_PATH
VALIDATION_FILE="$REPORT_PATH/validation_result.html"
BRANCH_NAME=$(git branch --show-current)
GIT_FILES_FILTER=$([[ $BRANCH_NAME == 'master' ]] && echo 'HEAD~1..HEAD' || echo 'remotes/origin/master')
git fetch origin master
FILES_COMMAND="git diff --name-only $GIT_FILES_FILTER -- $DIR"
echo "DEBUG: $FILES_COMMAND"
FILES=$($FILES_COMMAND)

echo "Storing report in $VALIDATION_FILE"
echo "<!DOCTYPE html>
<html>
<head>
<style>
code {
    display: block;
    background: #1b1b1b;
    color: #f5d67b;
    white-space: pre;
    margin: 15px;
    padding: 15px;
    border-radius: 15px;
}
h1 {
    margin: 15px;
}
.content {
    width: fit-content;
    margin: 20px;
}
.error {
  color: red;
}
</style>
</head>
<body>
<div class=\"content\">
<h1>Schedulers validation results</h1>" > $VALIDATION_FILE

for file in $FILES; do
    echo "validating $file ..."
    RESPONSE=$(curl $AUTH_STRING -X POST -F "jenkinsfile=<$file" $JENKINS_VALIDATION_URL) 2>&1
    OUTPUT="$OUTPUT"$'\n'"$file:$RESPONSE"
    if echo "$RESPONSE" | grep -E "WorkflowScript|Errors" -q; then
        echo -n "<code class=\"error\">" >> $VALIDATION_FILE
    else
        echo -n "<code>" >> $VALIDATION_FILE
    fi
    echo "$file:$RESPONSE</code>" >> $VALIDATION_FILE
done

if ! test "$OUTPUT"; then
    OUTPUT="No changes to test"
    echo "<code>No changes to test</code>" >> $VALIDATION_FILE
fi

echo "</div>
</body>
</html>" >> $VALIDATION_FILE

echo "Results: $OUTPUT"
if grep -E "WorkflowScript|Errors" $VALIDATION_FILE -q; then
    exit 1
fi

