package scripts.schedulers.aws

import com.amazon.jenkins.ec2fleet.EC2FleetCloud
import com.amazon.jenkins.ec2fleet.FleetStateStats
import com.cloudbees.groovy.cps.NonCPS
import hudson.model.Label
import hudson.slaves.Cloud.CloudState
import jenkins.model.Jenkins

/**
 * update-fleet.groovy
 */
pipeline {
    options {
        allowBrokenBuildClaiming()
    }
    agent {
        label 'master'
    }
    stages {
        stage('run') {
            steps {
                script {
                    updateAllFleet(env.minSize.toInteger(), env.maxSize.toInteger(), env.idleTime.toInteger(), env.minSpareSize.toInteger())
                }
            }
        }
    }
}

@NonCPS
void updateAllFleet(int minSize, int maxSize, int idleTime, int minSpareSize) {
    Jenkins jenkins = Jenkins.get()
    Label label

    def currentFleets = jenkins.clouds
    def fleetNames = [] // list of name of fleets
    currentFleets.each { fleet ->
        fleetNames.add(fleet.name)
    }

    fleetNames.each { fleetName ->
        def f = jenkins.getCloud(fleetName)
        echo("Updating ${f.name} with min size: ${minSize} max size: ${maxSize} idle time:  ${idleTime} min spare size: ${minSpareSize}")

        EC2FleetCloud newFleet = new EC2FleetCloud(
            f.name, // fleetcloudname
            f.oldId,
            f.awsCredentialsId, // deprecated field, just put here as a placeholder
            f.awsCredentialsId, // awsCredentialsId
            f.region, // region, update to use sthlm .... no we still need Ireland
            f.endpoint,
            f.fleet, // fleet
            f.labelString, // labelString
            f.fsRoot, // fsRoot
            f.computerConnector, // computerConnector
            f.privateIpUsed, // if need to use privateIpUsed
            f.alwaysReconnect, // if need alwaysReconnect
            idleTime, // if need to allow downscale set > 0 in min
            minSize, // minSize
            maxSize, // maxSize
            minSpareSize, // minSpareSize
            f.numExecutors, // numExecutors
            f.addNodeOnlyIfRunning, // if need addNodeOnlyIfRunning
            f.restrictUsage, // restrictUsage allow execute only jobs with proper label
            f.maxTotalUses.toString(), // restrict how many jobs can be run on the same agent, this is not really useful in our case, use default -1 as 'no limit'
            f.disableTaskResubmit, // if disableTaskResubmit
            f.initOnlineTimeoutSec, // timeout for new node to become online
            f.initOnlineCheckIntervalSec, // poll every x seconds for new node availability
            f.scaleExecutorsByWeight, // if use EC2 ASG weighting when determining available executors
            f.cloudStatusIntervalSec, // poll AWS every x seconds for updates
            f.noDelayProvision // if scale up as soon as there's a build in queue
        )
        echo("Removing old fleet: ${f.name}")
        jenkins.clouds.remove(f)
        echo("Adding new fleet(same name): ${newFleet.name}")
        jenkins.clouds.add(newFleet)
        echo('Done updates for above fleet')
        newFleet.stats = new FleetStateStats(newFleet.stats, minSize)
        echo('Done updates status for Jenkins UI')
        newFleet.provision(new CloudState(label, 0), 0)
    }
    jenkins.save()
}
