{#
    Command:
        post_clean
            short_help: Clean a machine after a failed build.

    Arguments:

    Required variables:

    Optional variables:
        processes
            default: None
            multiple: True
        user
            default: None
        data_port
            default: None
        data_client
            default: None
        code_port
            default: None
        code_client
            default: None
        antifreeze
            is_flag: True
            help: For Antifreeze jobs, we need to clean the Antifreeze folder.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    post_clean
    {%- if processes %}
    --processes {{ processes }}
    {%- endif %}
    {%- if user %}
    --user {{ user }}
    {%- endif %}
    {%- if data_port %}
    --data-port {{ data_port }}
    {%- endif %}
    {%- if data_client %}
    --data-client {{ data_client }}
    {%- endif %}
    {%- if code_port %}
    --code-port {{ code_port }}
    {%- endif %}
    {%- if code_client %}
    --code-client {{ code_client }}
    {%- endif %}
    {%- if antifreeze %}
    --antifreeze {{ antifreeze }}
    {%- endif %}
  displayName: elipy post_clean
