{#
    Command:
        process_shift_subscription_downloads
            short_help: Processes builds downloaded by a Shift subscription.

    Arguments:

    Required variables:
        code_branch
            required: True
            help: Code branch to use.

    Optional variables:
        build_type
            default: code
            help: Type of build to process.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    process_shift_subscription_downloads
    --code-branch {{ code_branch }}
    {%- if build_type %}
    --build-type {{ build_type }}
    {%- endif %}
  displayName: elipy process_shift_subscription_downloads
