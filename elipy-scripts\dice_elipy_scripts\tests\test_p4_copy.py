"""
test_p4_copy.py

Unit testing for p4_copy
"""
import os
import unittest

from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import call, MagicMock, patch
from dice_elipy_scripts.p4_copy import cli


@patch("dice_elipy_scripts.p4_copy.add_sentry_tags", MagicMock())
class TestP4CopyDataUpgradeCli(unittest.TestCase):
    ARGUMENT_PORT = "port_arg"
    ARGUMENT_CLIENT = "client_arg"
    ARGUMENT_MAPPING = "mapping_arg"
    ARGUMENT_CHANGELIST = "1234"

    OPTION_NO_SUBMIT = "--no-submit"
    OPTION_EXCLUDE_PATH = "--exclude-path"
    OPTION_SUBMIT_MESSAGE = "--submit-message"
    OPTION_SOURCE_BRANCH = "--source-branch"
    OPTION_TARGET_BRANCH = "--target-branch"

    VALUE_EXCLUDE_PATH_1 = "exclude_path_1"
    VALUE_EXCLUDE_PATH_2 = "exclude_path_2"
    VALUE_SUBMIT_MESSAGE = "submit_message"
    VALUE_SOURCE_BRANCH = "source_branch"
    VALUE_TARGET_BRANCH = "target_branch"

    BASIC_ARGS = [
        ARGUMENT_PORT,
        ARGUMENT_CLIENT,
        ARGUMENT_MAPPING,
        ARGUMENT_CHANGELIST,
    ]

    def setUp(self):
        self.patcher_p4utils = patch("elipy2.p4.P4Utils")
        self.mock_p4utils = self.patcher_p4utils.start()
        self.mock_p4utils.return_value = MagicMock()

        self.patcher_submit_integration = patch("dice_elipy_scripts.p4_copy.submit_integration")
        self.mock_submit_integration = self.patcher_submit_integration.start()

    def tearDown(self):
        patch.stopall()

    def test_basic_args(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        assert self.mock_p4utils.return_value.revert.call_count == 2

    def test_exclude_path(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_EXCLUDE_PATH,
                self.VALUE_EXCLUDE_PATH_1,
                self.OPTION_EXCLUDE_PATH,
                self.VALUE_EXCLUDE_PATH_2,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        assert self.mock_p4utils.return_value.revert.call_count == 4
        self.mock_p4utils.return_value.revert.assert_has_calls(
            [call(), call(path="exclude_path_1"), call(path="exclude_path_2"), call(quiet=True)]
        )

    @patch.dict(os.environ, {"BUILD_URL": "jenkins-url.fake"})
    def test_submit_default(self):
        message = (
            f"Copied using branch mapping {self.ARGUMENT_MAPPING} at CL#{self.ARGUMENT_CHANGELIST}."
            + f"\n#branchguardian_bypass"
        )
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_submit_integration.assert_called_once_with(
            p4_object=self.mock_p4utils.return_value,
            submit_message=message,
            submit=True,
        )

    @patch.dict(os.environ, {"BUILD_URL": "jenkins-url.fake"})
    def test_submit_source_target_branches(self):
        message = (
            f"Copied from {self.VALUE_SOURCE_BRANCH} to {self.VALUE_TARGET_BRANCH} at "
            + f"CL#{self.ARGUMENT_CHANGELIST}.\n#branchguardian_bypass"
        )
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_SOURCE_BRANCH,
                self.VALUE_SOURCE_BRANCH,
                self.OPTION_TARGET_BRANCH,
                self.VALUE_TARGET_BRANCH,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_submit_integration.assert_called_once_with(
            p4_object=self.mock_p4utils.return_value,
            submit_message=message,
            submit=True,
        )

    @patch.dict(os.environ, {"BUILD_URL": "jenkins-url.fake"})
    def test_submit_extra_message(self):
        message = (
            f"Copied using branch mapping {self.ARGUMENT_MAPPING} at CL#{self.ARGUMENT_CHANGELIST}."
            + f"\n#branchguardian_bypass"
        )
        message += f"\n{self.VALUE_SUBMIT_MESSAGE}"
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_SUBMIT_MESSAGE, self.VALUE_SUBMIT_MESSAGE]
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_submit_integration.assert_called_once_with(
            p4_object=self.mock_p4utils.return_value,
            submit_message=message,
            submit=True,
        )

    def test_no_submit(self):
        message = (
            f"Copied using branch mapping {self.ARGUMENT_MAPPING} at CL#{self.ARGUMENT_CHANGELIST}."
            + f"\n#branchguardian_bypass"
        )
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_NO_SUBMIT])
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_submit_integration.assert_called_once_with(
            p4_object=self.mock_p4utils.return_value,
            submit_message=message,
            submit=False,
        )
