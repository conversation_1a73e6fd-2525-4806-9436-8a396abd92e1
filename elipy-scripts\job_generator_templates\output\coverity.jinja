{#
    Command:
        coverity
            short_help: Run coverity tool.
            context_settings: dict(ignore_unknown_options=True)

    Arguments:

    Required variables:
        code_branch
            required: True
            help: Perforce branch/stream name.
        code_changelist
            required: True
            help: Which code changelist to use.
        artifactory_user
            required: True
            help: Artifactory user for fetching the Coverity binaries.
        artifactory_apikey
            required: True
            help: Artifactory API key for fetching the Coverity binaries.
        artifactory_coverity_source_path
            required: True
            help: The source path to the Coverity binaries on Artifactory.
        coverity_user
            required: True
            help: Coverity server username.
        coverity_password
            required: True
            help: Coverity server password.

    Optional variables:
        config
            default: release
            help: Config
        clean
            type: click.BOOL
            default: False
            help: Delete TnT/Local
        clean_coverity_client
            default: False
            type: click.BOOL
            help: Force re-download the Coverity client
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    coverity
    --code-branch {{ code_branch }}
    --code-changelist {{ code_changelist }}
    --artifactory-user {{ artifactory_user }}
    --artifactory-apikey {{ artifactory_apikey }}
    --artifactory-coverity-source-path {{ artifactory_coverity_source_path }}
    --coverity-user {{ coverity_user }}
    --coverity-password {{ coverity_password }}
    {%- if config %}
    --config {{ config }}
    {%- endif %}
    {%- if clean %}
    --clean {{ clean }}
    {%- endif %}
    {%- if clean_coverity_client %}
    --clean-coverity-client {{ clean_coverity_client }}
    {%- endif %}
  displayName: elipy coverity
