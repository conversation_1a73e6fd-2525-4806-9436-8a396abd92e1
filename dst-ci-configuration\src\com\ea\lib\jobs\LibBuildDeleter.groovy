package com.ea.lib.jobs

import com.ea.lib.jobsettings.BuildDeleterSettings

class LibBuildDeleter {
    /**
     * Start build deleter job
     */
    static void start(def job, def project, def projectWithLocation, boolean deleteEmptyFolders = false, int hoursFromMidnight = 1, def projectInfo) {
        BuildDeleterSettings buildDeleterSettings = new BuildDeleterSettings()
        buildDeleterSettings.initializeStart(project, projectWithLocation, deleteEmptyFolders, hoursFromMidnight, projectInfo)
        job.with {
            description(buildDeleterSettings.description)
            disabled(buildDeleterSettings.isDisabled)
            logRotator(7, 100)
            quietPeriod(0)
            properties {
                throttleConcurrentBuilds {
                    maxTotal(buildDeleterSettings.parallelJobs)
                }
                disableResume()
                pipelineTriggers {
                    triggers {
                        cron {
                            spec(buildDeleterSettings.cronTrigger)
                        }
                    }
                }
            }
            environmentVariables {
                env('project_name', buildDeleterSettings.projectName)
                env('project_with_location', buildDeleterSettings.projectWithLocation)
                env('delete_empty_folders', buildDeleterSettings.deleteEmptyFolders)
            }
        }
    }

    /**
     * Delete build jobs
     */
    static void job(def job, def projectInfo, def projectWithLocation, def project) {
        BuildDeleterSettings buildDeleterSettings = new BuildDeleterSettings()
        buildDeleterSettings.initializeJob(projectInfo, projectWithLocation, project)
        job.with {
            description(buildDeleterSettings.description)
            disabled(buildDeleterSettings.isDisabled)
            label(buildDeleterSettings.jobLabel)
            concurrentBuild()
            logRotator(7, 100)
            parameters {
                choiceParam('delete_empty_folders', ['', '--empty-folders'], 'Set to second choice to crawl build_share categories for empty folders')
                stringParam('timeout', buildDeleterSettings.timeoutMinutes.toString(), 'Gives the possibility to override default timeout. Must be > 2.')
            }
            quietPeriod(0)
            customWorkspace(buildDeleterSettings.workspaceRoot)
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(buildDeleterSettings.buildName)
                timeout {
                    absolute('${ENV, var="timeout"}')
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
                credentialsBinding {
                    if (buildDeleterSettings.vaultCredentials) {
                        string(buildDeleterSettings.vaultVariable, buildDeleterSettings.vaultCredentials)
                    }
                }
            }
            steps {
                batchFile(buildDeleterSettings.elipyInstallCall)
                batchFile(buildDeleterSettings.elipyCmd)
            }
        }
    }
}
