{#
    Command:
        bilbo_register_drone
            short_help: Registers a Drone build in Bilbo.

    Arguments:

    Required variables:
        code_branch
            help: Perforce code branch/stream name.
            required: True
        code_changelist
            required: True
            help: Changelist number of code build used to verify data.
        data_branch
            help: Perforce data branch/stream name.
            required: True
        data_changelist
            help: Changelist number of data built.
            required: True
        dataset
            required: True
            help: Which dataset has been validated using this code build.

    Optional variables:
        old_drone_setup
            is_flag: True
            help: Skip deploying TnT (DICE Drone builds req.).
        extra_location
            multiple: True
            required: False
            help: Another locations to register this build
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    bilbo_register_drone
    --code-branch {{ code_branch }}
    --code-changelist {{ code_changelist }}
    --data-branch {{ data_branch }}
    --data-changelist {{ data_changelist }}
    --dataset {{ dataset }}
    {%- if old_drone_setup %}
    --old-drone-setup {{ old_drone_setup }}
    {%- endif %}
    {%- if extra_location %}
    --extra-location {{ extra_location }}
    {%- endif %}
  displayName: elipy bilbo_register_drone
