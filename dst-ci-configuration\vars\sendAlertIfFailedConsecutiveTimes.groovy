import com.ea.lib.LibJ<PERSON>kins

/**
 * sendAlertIfFailedConsecutiveTimes.groovy
 * Sends a Slack alert if {@code count} consecutive jobs have failed.
 * @param jobName The job to analyse
 * @param jobNumber The job number that ran
 * @param slackChannel which channel to send the notification to
 * @param projectShortName which project's slack token to use
 * @param count how many consecutive jobs need to fail before sending an alert
 */
void call(String jobName, String jobUrl, String jobNumber, String slackChannel, String projectShortName, int count = 5) {
    if (LibJenkins.hasLastNumberOfJobsFailed(jobName, jobNumber, count)) {
        Map slackSettings = [
            channels      : [slackChannel],
            message_color : 'danger',
            report_message: "The job <${jobUrl}|${jobName}> has failed at least ${count} consecutive times!",
        ]
        SlackMessageNew(currentBuild, slackSettings, projectShortName)
    }
}
