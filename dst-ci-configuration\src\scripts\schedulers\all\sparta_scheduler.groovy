package scripts.schedulers.all

import com.ea.project.GetBranchFile
import com.ea.lib.LibJenkins

def project = ProjectClass(env.project_name)

/**
 * sparta_scheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Trigger sparta job') {
            steps {
                script {
                    def code_job = env.branch_name + '.code.start'
                    if (env.branch_name == 'game-dev') {
                        code_job = env.branch_name + '.future-smoke.copy-up-from.' + env.branch_name + '-unverified'
                    }
                    def code_changelist = LibJenkins.getLastStableCodeChangelist(code_job)

                    // Sparta specific parts
                    def source_bundle_path = params.source_bundle_path
                    def bundle_changelist = params.bundle_changelist
                    def description = params.description
                    String no_submit = params.no_submit

                    def args = [
                        string(name: 'code_changelist', value: code_changelist),
                        string(name: 'source_bundle_path', value: source_bundle_path),
                        string(name: 'bundle_changelist', value: bundle_changelist),
                        string(name: 'description', value: description),
                        string(name: 'no_submit', value: no_submit),
                    ]

                    def inject_map = [
                        'code_changelist'   : code_changelist,
                        'source_bundle_path': source_bundle_path,
                        'bundle_changelist' : bundle_changelist,
                        'description'       : description,
                        'no_submit'         : no_submit,
                    ]
                    EnvInject(currentBuild, inject_map)
                    currentBuild.displayName = env.JOB_NAME + '.' + bundle_changelist

                    def sparta_job = build(job: env.branch_name + '.sparta.bundle', parameters: args, propagate: false)

                    currentBuild.result = sparta_job.result.toString()

                    def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
                    def slack_settings = branchfile.standard_jobs_settings?.slack_channel_sparta
                    SlackMessageNew(currentBuild, slack_settings, project.short_name)

                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
}
