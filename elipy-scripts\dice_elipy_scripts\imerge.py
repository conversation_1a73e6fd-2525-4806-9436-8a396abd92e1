"""
imerge.py

A wrapper around `p4 merge` that runs it on the specified set of CLs
and respects integration info in <PERSON><PERSON>'s comment.
"""
import click
import itertools
import os
import re
import yaml

from dice_elipy_scripts.utils.dbxmerge import DBXMerge
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags

from elipy2 import LOGGER
from elipy2.p4 import P4Utils
from elipy2.exceptions import ELIPYException, AutomaticP4MergeResolveException
from elipy2.telemetry import collect_metrics

# pylint: disable=invalid-name, redefined-argument-from-local, too-many-instance-attributes


class Merge:
    """
    A wrapper around `p4 merge`. It takes all CLs that should be integrated/merged,
    then groups them using the 'Integration' field from the Checkmate comment and
    merges each group using `p4 resolve` with corresponding options.
    """

    def __init__(self, p4, from_stream=None, safe_resolve=False, exclude_paths=()):
        self.p4 = p4

        sinfo = self.p4.stream_info()
        if from_stream:
            self.source_stream = from_stream.rstrip("/")
        else:
            self.source_stream = sinfo.parent
        self.target_stream = sinfo.stream
        self.first_cl = self.last_cl = None

        self.exclude_paths = exclude_paths
        self.resolve_modes = {
            "merge": "s" if safe_resolve else "m",
            "ignore": "y",
            "overwrite": "t",
        }

        if DBXMerge.executable:
            self.dbxmerge = DBXMerge(p4.port, p4.user, p4.client)
        else:
            self.dbxmerge = None

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.dbxmerge:
            self.dbxmerge.cleanup()

        if exc_type and issubclass(exc_type, ELIPYException):
            if exc_val:
                LOGGER.error(exc_val)
            self.p4.revert(quiet=True)

    def resolve(self, mode):
        """
        Resolves files in the default CL using `mode` for resolution
        """
        for path in self.exclude_paths:
            self.p4.revert(path=path, quiet=True)

        self.p4.resolve(mode=mode, quiet=True)
        if self.dbxmerge and mode == self.resolve_modes["merge"]:
            LOGGER.info("Looking for unresolved .dbx files...")
            for dbx_file in self.p4.unresolved("*.dbx", resolve_type="content"):
                self.dbxmerge.resolve(dbx_file.local_path)

        LOGGER.info("Looking for unresolved files...")
        unresolved = self.p4.unresolved()
        if unresolved:
            raise AutomaticP4MergeResolveException(
                "Unresolved files:\n" + "\n".join(f.local_path for f in unresolved)
            )

    def run(self, start_cl=None, stop_cl=None):
        """
        Merges `source_stream/...@start_cl,stop_cl` into the current stream
        grouping Cls by their integration mode first.
        """
        if start_cl:
            cl_range = f"{start_cl},{stop_cl or '#head'}"
        else:
            cl_range = stop_cl or None

        self.first_cl = None
        self.last_cl = None

        changelists = self.p4.interchanges(self.source_stream, self.target_stream, cl_range)
        if not changelists:
            return False

        for start_cl, stop_cl, resolve_mode in self.group(changelists):
            if not self.p4.merge(
                self.source_stream,
                parent=self.target_stream,
                reverse=False,
                to_revision=f"{start_cl},{stop_cl}",
                quiet=True,
            ):
                raise ELIPYException("Failed to run 'p4 merge'")

            self.resolve(resolve_mode)
            if not self.first_cl:
                self.first_cl = start_cl
            self.last_cl = stop_cl

        return True

    def submit(self, description=""):
        """
        Submits the default changelist
        """
        desc = (
            f"Integrated from {self.source_stream}/...@{self.first_cl},{self.last_cl}.\n"
            f"Jenkins URL: {os.environ.get('BUILD_URL', '<unknown>')}\n"
        )

        description = description.strip()
        if description:
            desc = f"{description}\n\n{desc}"

        LOGGER.debug(desc)
        self.p4.submit(desc)

    def group(self, changes):
        """
        Groups `changes` using their integration method
        """
        checkmate_comment = re.compile(r"\n(--- # CheckMate.*?)(?:\n\s*\n|$)", re.DOTALL)
        default_mode = self.resolve_modes["merge"]

        def get_resolve_mode(change):
            resolve_mode = default_mode
            match = checkmate_comment.search(change.desc)
            if match:
                try:
                    chm_info = yaml.safe_load(match.group(1))
                    resolve_mode = self.resolve_modes[
                        chm_info["Submission Info"]["Integration"].lower()
                    ]
                except (yaml.YAMLError, KeyError):
                    pass
            return resolve_mode

        for mode, cls in itertools.groupby(changes, get_resolve_mode):
            cls = list(cls)
            yield cls[0].changelist, cls[-1].changelist, mode


@click.command(
    "imerge",
    help="Runs `p4 merge` on the specified set of CLs grouping them by integration mode.",
)
@click.option(
    "--port",
    "p4_port",
    metavar="P4PORT",
    help="protocol:host:port of the Perforce service with which to communicate.",
)
@click.option("--client", "p4_client", metavar="P4CLIENT", help="Perforce client name.")
@click.option("--user", "p4_user", metavar="P4USER", help="Perforce user name.")
@click.option(
    "--no-submit",
    "skip_submit",
    is_flag=True,
    default=False,
    show_default=True,
    help="Skip submission of the merged files to the depot.",
)
@click.option(
    "--exclude-path",
    "exclude_paths",
    metavar="DEPO_PATH",
    multiple=True,
    help="Revert the path before running `p4 resolve`. Can be provided multiple times.",
)
@click.option(
    "--safe-resolve",
    "safe_resolve",
    is_flag=True,
    default=False,
    show_default=True,
    help="User `p4 resolve -as` for changelists with 'Integration: merge'.",
)
@click.option(
    "--description",
    metavar="DESCRIPTION",
    default="",
    help="Submit the changelist with the DESCRIPTION.",
)
@click.option(
    "--from",
    "from_stream",
    metavar="STREAM",
    help="Specifies a stream other than the parent stream to merge from.",
)
@click.option(
    "--start-changelist",
    "start_cl",
    metavar="NUMBER",
    help="Merge changes starting from the specified change number.",
)
@click.option(
    "--stop-changelist",
    "stop_cl",
    metavar="NUMBER",
    help="Merge changes up to and including the specified change number.",
)
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    p4_port,
    p4_client,
    p4_user,
    from_stream,
    skip_submit,
    exclude_paths,
    safe_resolve,
    description,
    start_cl,
    stop_cl,
):
    """
    The script entry point
    """
    add_sentry_tags(__file__)

    p4 = P4Utils(p4_port, p4_user, p4_client)

    with Merge(p4, from_stream, safe_resolve, exclude_paths) as merge:
        if merge.run(start_cl, stop_cl):
            if not skip_submit:
                merge.submit(description)
