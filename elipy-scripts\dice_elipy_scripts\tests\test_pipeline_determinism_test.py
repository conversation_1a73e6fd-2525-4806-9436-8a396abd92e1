"""
test_pipeline_determinism_test.py

Unit testing for pipeline_determinism_test
"""
from unittest.mock import patch

from click.testing import C<PERSON><PERSON>unner
from mock.mock import MagicMock

from dice_elipy_scripts.pipeline_determinism_test import cli


@patch("os.path.exists", MagicMock(return_value=True))
@patch("elipy2.frostbite_core.get_tnt_root", MagicMock(return_value="h:\\dev\\tnt"))
@patch("os.path.join", MagicMock(side_effect=lambda *args: "\\".join(args)))
@patch("dice_elipy_scripts.pipeline_determinism_test.add_sentry_tags", MagicMock())
class TestPipelineDeterminismTest:
    OPTION_CODE_BRANCH = "--code-branch"
    VALUE_CODE_BRANCH = "branch"
    OPTION_CODE_CHANGELIST = "--code-changelist"
    VALUE_CODE_CHANGELIST = "123"
    OPTION_SCRIPT_ARGS = "--script-args"
    VALUE_SCRIPT_ARGS = "--arg1 arg1"
    OPTION_P4_PORT = "--p4-port"
    VALUE_P4_PORT = "https://p4port:2000"

    @patch(
        "dice_elipy_scripts.pipeline_determinism_test.filer.FilerUtils",
        return_value=MagicMock(),
    )
    @patch("elipy2.core.run")
    def test_pipeline_determinism_test_with_args(self, mock_run, mock_filer):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.OPTION_CODE_BRANCH,
                self.VALUE_CODE_BRANCH,
                self.OPTION_CODE_CHANGELIST,
                self.VALUE_CODE_CHANGELIST,
                self.OPTION_SCRIPT_ARGS,
                self.VALUE_SCRIPT_ARGS,
                self.OPTION_P4_PORT,
                self.VALUE_P4_PORT,
            ],
        )
        env_vars = {
            "P4PORT": self.VALUE_P4_PORT,
            "P4CONFIG": ".p4config",
        }
        mock_filer.return_value.fetch_code.assert_called_with(
            self.VALUE_CODE_BRANCH,
            self.VALUE_CODE_CHANGELIST,
            "pipeline",
            "release",
        )
        mock_run.assert_called_once_with(
            [
                "python.exe",
                "h:\\dev\\tnt\\Code\\DICE\\BattlefieldGame\\fbcli\\pipeline_determinism_test.py",
                "--arg1 arg1",
            ],
            print_std_out=True,
            env_patch=env_vars,
        )
        assert result.exit_code == 0
