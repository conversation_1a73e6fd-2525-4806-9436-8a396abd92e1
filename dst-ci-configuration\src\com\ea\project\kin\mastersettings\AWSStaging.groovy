package com.ea.project.kin.mastersettings

import com.ea.project.kin.KingstonAWS

class AWSStaging {
    static Class project = KingstonAWS
    static Map branches = [:]
    static Map preflight_branches = [
        'stage-kin-dev-unverified': [
            code_folder: 'dev', code_branch: 'kin-dev-unverified',
            data_folder: 'dev', data_branch: 'kin-dev-unverified-aws-code-preflight',
        ],
    ]
    static Map autotest_branches = [:]
    static Map integrate_branches = [:]
    static Map copy_branches = [:]
    static Map feature_integrations = [:]
    static Map maintenance_branch = [:]
    static List dashboard_list = []
    static Map dvcs_configs = [:]
    static Map MAINTENANCE_SETTINGS = [
        IS_PRODUCTION: false,
    ]
}
