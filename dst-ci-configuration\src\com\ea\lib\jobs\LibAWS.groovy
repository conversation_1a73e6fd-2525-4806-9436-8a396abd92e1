package com.ea.lib.jobs

import javaposse.jobdsl.dsl.DslFactory
import javaposse.jobdsl.dsl.jobs.WorkflowJob

class LibAWS {

    static void downsize_fleet(def job, def branch_info) {
        def trigger_string = branch_info.aws_maint_down ?: 'H 22 * * 1-5'
        job.with {
            description('AWS maintanence job for cost saving nightly')
            disabled(false)
            logRotator(7, 50)
            properties {
                pipelineTriggers {
                    triggers {
                        cron {
                            spec(trigger_string) //by default: Mon-Fri 20P.M
                        }
                    }
                }
            }
            parameters {
                stringParam {
                    name('minSize')
                    defaultValue('0')
                    description('min number of ec2 in the fleet')
                    trim(true)
                }
                stringParam {
                    name('maxSize')
                    defaultValue('1')
                    description('max number of ec2 in the fleet')
                    trim(true)
                }
                stringParam {
                    name('minSpareSize')
                    defaultValue('0')
                    description('Minimum number of spare instances. Ensures there are always n available agents, up to "maxSize instances"')
                    trim(true)
                }
                stringParam {
                    name('idleTime')
                    defaultValue('30')
                    description('minutes before deleting ec2 if not been used')
                    trim(true)
                }
            }
        }
    }

    static void upsize_fleet(def job, def branch_info) {
        def trigger_string = branch_info.aws_maint_up ?: 'H 7 * * 1-5'
        job.with {
            description('AWS maintanence job for prepare working day load')
            disabled(false)
            logRotator(7, 50)
            properties {
                pipelineTriggers {
                    triggers {
                        cron {
                            spec(trigger_string) //by default: Mon-Sat 7A.M, to have it on Sat is for overwrite Friday evening downsize
                        }
                    }
                }
            }
            parameters {
                stringParam {
                    name('minSize')
                    defaultValue('1')
                    description('min number of ec2 in the fleet')
                    trim(true)
                }
                stringParam {
                    name('maxSize')
                    defaultValue('4')
                    description('max number of ec2 in the fleet')
                    trim(true)
                }
                stringParam {
                    name('minSpareSize')
                    defaultValue('0')
                    description('Minimum number of spare instances. Ensures there are always n available agents, up to "maxSize instances"')
                    trim(true)
                }
                stringParam {
                    name('idleTime')
                    defaultValue('120')
                    description('minutes before deleting ec2 if not been used')
                    trim(true)
                }
            }
        }
    }

    static WorkflowJob warm_agent(DslFactory dslFactory, String current_branch, String schedule) {
        return dslFactory.pipelineJob('aws.' + current_branch + '.warm_agent.start') {
            definition {
                cps {
                    script(dslFactory.readFileFromWorkspace('src/scripts/schedulers/aws/warm_agent.groovy'))
                    sandbox(true)
                }
            }
            description('AWS maintanence job for warm up nodes in the morning')
            disabled(false)
            environmentVariables(current_branch: current_branch)
            logRotator(7, 10)
            quietPeriod(0)
            properties {
                disableConcurrentBuilds()
                disableResume()
                if (schedule) {
                    pipelineTriggers {
                        triggers {
                            cron {
                                spec(schedule)
                            }
                        }
                    }
                }
            }
        }
    }
}
