package com.ea.project.all.mastersettings

import com.ea.project.all.All
import com.ea.project.fb1.Fb1Battlefieldgame
import com.ea.project.kin.Kingston

class DiceUpgradeValidator {
    static Class project = All
    static Map branches = [
        'dre-triggering'     : [
            project    : Kingston,
            code_folder: 'tasks',
            code_branch: 'dre-triggering',
            data_folder: 'tasks',
            data_branch: 'dre-triggering',
        ],
        'jenkins-upgrade-kin': [
            project    : Kingston,
            code_folder: 'dev',
            code_branch: 'kin-dev',
            data_folder: 'dev',
            data_branch: 'kin-dev',
        ]
    ]
    static Map preflight_branches = [:]
    static Map autotest_branches = [
        'dev-na-battlefieldgame': [
            project               : TestFb1Battlefieldgame,
            code_folder           : 'fbstream',
            code_branch           : 'dev-na-dice-next-build',
            data_folder           : 'fbstream',
            data_branch           : 'dev-na-dice-next-build-data',
            statebuild_autotest   : true,
            job_label_statebuild  : 'statebuild-fb1',
            enable_lkg_p4_counters: false,
        ],
        'kin-dev'               : [
            project               : Test<PERSON>ings<PERSON>,
            code_folder           : 'dev',
            code_branch           : 'kin-dev',
            data_folder           : 'dev',
            data_branch           : 'kin-dev',
            statebuild_autotest   : true,
            enable_lkg_p4_counters: false,
            job_label_statebuild  : 'statebuild-kin',
        ]
    ]
    static Map integrate_branches = [
        'kin-dev_to_dre-triggering': [
            cook_before_submit          : true,
            project                     : Kingston,
            source_folder               : 'dev',
            source_branch               : 'kin-dev',
            target_folder               : 'tasks',
            target_branch               : 'dre-triggering',
            code                        : true,
            data                        : true,
            parent_to_child             : true,
            elipy_call                  : Kingston.elipy_call,
            elipy_install_call          : Kingston.elipy_install_call,
            workspace_root              : Kingston.workspace_root,
            verified_integration        : true,
            accept_theirs               : true,
            slack_channel               : '#dice-build-upgrade',
            integration_reference_job   : 'trigger-from.dice-kin-dev',
            job_label_statebuild        : 'statebuild-kin',
            freestyle_job_trigger_matrix: [],
        ]
    ]
    static Map copy_branches = [
        'kin-dev_to_dre-triggering': [
            project                     : Kingston,
            source_folder               : 'dev',
            source_branch               : 'kin-dev',
            target_folder               : 'tasks',
            target_branch               : 'dre-triggering',
            code                        : true,
            data                        : true,
            parent_to_child             : true,
            elipy_call                  : Kingston.elipy_call,
            elipy_install_call          : Kingston.elipy_install_call,
            workspace_root              : Kingston.workspace_root,
            slack_channel               : '#dice-build-upgrade',
            job_label_statebuild        : 'statebuild-kin',
            freestyle_job_trigger_matrix: [],
        ]
    ]
    static Map feature_integrations = [:]
    static Map maintenance_branch = [
        'dev-na-battlefieldgame': [
            project    : Fb1Battlefieldgame,
            code_folder: 'fbstream',
            code_branch: 'dev-na-dice-next-build',
            data_folder: 'fbstream',
            data_branch: 'dev-na-dice-next-build-data',
        ]
    ]
    static List dashboard_list = []
    static Map dvcs_configs = [:]
    static Map MAINTENANCE_SETTINGS = [:]

    private static final String AUTOTEST_MATRIX = 'TestAutotestMatrix'

    private static class TestFb1Battlefieldgame extends Fb1Battlefieldgame {
        static String autotest_matrix = AUTOTEST_MATRIX
    }

    private static class TestKingston extends Kingston {
        static String autotest_matrix = AUTOTEST_MATRIX
    }
}
