import com.ea.lib.model.autotest.Name
import com.ea.lib.model.autotest.Platform
import com.ea.lib.model.autotest.Region
import spock.lang.Specification

class PlatformSpec extends Specification {

    /*void 'test Platform is unique by name'() {
        given:
        Platform platform1 = new Platform(name: Name.PS4, region: Region.EU)
        Platform platform2 = new Platform(name: Name.PS5, region: Region.NA)
        Platform platform3 = new Platform(name: Name.PS5, region: Region.EU)
        when:
        Set<Platform> platforms = [platform1, platform2, platform3]
        then:
        platforms.size() == 2
        platform2 == platform3
    }*/

    void 'test the Platform String representation equals to the name'() {
        given:
        Name name = Name.PS4
        Platform platform1 = new Platform(name: Name.PS4, region: Region.EU)
        when:
        String value = platform1
        then:
        value == name.value
    }

    void 'test Platforms are sorted by Name'() {
        given:
        Platform platform1 = new Platform(name: Name.WIN64)
        Platform platform2 = new Platform(name: Name.XBSX)
        Platform platform3 = new Platform(name: Name.PS5)
        Platform platform4 = new Platform(name: Name.PS4)
        when:
        List<Platform> platforms = [platform1, platform2, platform3, platform4].sort()
        then:
        platforms == [platform4, platform3, platform1, platform2]
    }

}
