package com.ea.project.bct.mastersettings

import com.ea.project.all.All
import com.ea.project.bct.Bct
import com.ea.project.bctch1.BctCh1

class BctPreflight {
    static Class project = All
    static Map branches = [:]
    static Map preflight_branches = [
        'CH1-code-dev'       : [project: BctCh1, code_folder: 'CH1', code_branch: 'CH1-code-dev', data_folder: 'CH1', data_branch: 'CH1-code-dev'],
        'CH1-content-dev'    : [project: BctCh1, code_folder: 'CH1', code_branch: 'CH1-content-dev', data_folder: 'CH1', data_branch: 'CH1-content-dev'],
        'CH1-stage'          : [project: BctCh1, code_folder: 'CH1', code_branch: 'CH1-stage', data_folder: 'CH1', data_branch: 'CH1-stage'],
        'task1'              : [project: Bct, code_folder: 'tasks', code_branch: 'task1', data_folder: 'tasks', data_branch: 'task1'],
        'trunk-code-dev'     : [project: Bct, code_folder: 'mainline', code_branch: 'trunk-code-dev', data_folder: 'mainline', data_branch: 'trunk-code-dev'],
        'trunk-code-dev-test': [project: Bct, code_folder: 'mainline', code_branch: 'trunk-code-dev', data_folder: 'mainline', data_branch: 'trunk-code-dev'],
        'trunk-content-dev'  : [project: Bct, code_folder: 'mainline', code_branch: 'trunk-content-dev', data_folder: 'mainline', data_branch: 'trunk-content-dev'],
    ]
    static Map autotest_branches = [:]
    static Map integrate_branches = [:]
    static Map copy_branches = [:]
    static Map feature_integrations = [:]
    static Map maintenance_branch = [
        'trunk-code-dev': [project: Bct, code_folder: 'mainline', code_branch: 'trunk-code-dev', data_folder: 'mainline', data_branch: 'trunk-code-dev'],
    ]
    static List dashboard_list = []
    static Map dvcs_configs = [:]
    static List code_downstream_matrix = []
    static Map MAINTENANCE_SETTINGS = [:]
}
