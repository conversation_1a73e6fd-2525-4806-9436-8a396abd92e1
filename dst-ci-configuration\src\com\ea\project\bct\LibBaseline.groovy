package com.ea.project.bct

import com.ea.exceptions.CobraException

class LibBaseline {
    static final Baseline[] disc_matrix = [
        new Baseline('bflabs', 'ps5', '21082313', 'bflabs'),
        new Baseline('bflabs', 'win64', '21643209', 'bflabs'),
        new Baseline('bflabs', 'xbsx', '21643209', 'bflabs'),
    ]
    static final Baseline[] patch_matrix = [
        new Baseline('bflabs', 'ps5', '21972488', 'bflabs'),
        new Baseline('bflabs', 'win64', '21972488', 'bflabs'),
        new Baseline('bflabs', 'xbsx', '21972488', 'bflabs'),
    ]

    static Baseline get_disc_baseline_for(String name, String platform) {
        return get_baseline_from_matrix(disc_matrix, name, platform)
    }

    static Baseline get_patch_baseline_for(String name, String platform) {
        return get_baseline_from_matrix(patch_matrix, name, platform)
    }

    private static Baseline get_baseline_from_matrix(Baseline[] matrix, String name, String platform) {
        for (baseline in matrix) {
            if (baseline.name == name && baseline.platform == platform) {
                return baseline
            }
        }
        throw new CobraException("No baseline configuration matches name: ${name} as well as platform: ${platform}!")
    }
}

class Baseline {
    /*
    This is the master baseline values. All other baselines should reference its values per default.
    */
    static String default_code_changelist = '123'
    static String default_code_branch = 'trunk-code-dev'
    static String default_data_changelist = '123'
    static String default_data_branch = 'trunk-code-dev'
    String name, platform, code_changelist, code_branch, data_changelist, data_branch

    // EXCEPTIONAL Constructor
    Baseline(name, platform, code_changelist, code_branch, data_changelist, data_branch) {
        this.name = name
        this.platform = platform
        this.code_changelist = code_changelist
        this.code_branch = code_branch
        this.data_changelist = data_changelist
        this.data_branch = data_branch
    }
    // EXCEPTIONAL Constructor
    Baseline(name, platform, changelist, branch) {
        this(name, platform, changelist, branch, changelist, branch)
    }
    // DEFAULT Constructor
    Baseline(name, platform) {
        this(name, platform, default_code_changelist, default_code_branch, default_data_changelist, default_data_branch)
    }
}
