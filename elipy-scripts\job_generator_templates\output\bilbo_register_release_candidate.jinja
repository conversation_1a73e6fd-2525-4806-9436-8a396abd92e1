{#
    Command:
        bilbo_register_release_candidate
            short_help: Registers a build in Bilbo as a Release Candidate.

    Arguments:

    Required variables:
        code_branch
            help: Perforce code branch/stream name.
            required: True
        code_changelist
            required: True
            help: Changelist number for code build to tag.
        data_branch
            help: Perforce data branch/stream name.
            required: True
        data_changelist
            help: Changelist number of data to tag.
            required: True

    Optional variables:
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    bilbo_register_release_candidate
    --code-branch {{ code_branch }}
    --code-changelist {{ code_changelist }}
    --data-branch {{ data_branch }}
    --data-changelist {{ data_changelist }}
  displayName: elipy bilbo_register_release_candidate
