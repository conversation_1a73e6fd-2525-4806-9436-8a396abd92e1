package scripts.schedulers.aws

/**
 * warm_agent.groovy
 * for AWS codepreflight to use
 */
pipeline {
    agent { label 'master' }
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Starting codepreflight.start job') {
            steps {
                script {
                    build wait: false, propagate: false, job: env.current_branch + '.codepreflight.start',
                        parameters: [
                            string(name: 'unshelve_changelist', value: ''), // warmer job uses code_changelist no need unshelve_changelist
                            string(name: 'preflighter', value: 'cobra-auto-warmer'),
                            string(name: 'sync_to_head', value: 'false'),
                            string(name: 'clean_local', value: 'false'),
                            string(name: 'only_warm_machine', value: 'do') // set to "do" pass down --do-warmup, set to 'not' pass down --not-warmup,
                        ]
                }
            }
        }
    }
}
