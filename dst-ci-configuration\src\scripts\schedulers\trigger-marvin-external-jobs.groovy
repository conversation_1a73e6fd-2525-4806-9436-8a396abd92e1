package scripts.schedulers

import org.apache.http.HttpException
import org.apache.http.HttpResponse
import org.apache.http.client.HttpClient
import org.apache.http.client.methods.HttpPost
import org.apache.http.entity.StringEntity
import org.apache.http.impl.client.HttpClientBuilder
import org.apache.http.util.EntityUtils

/**
 * trigger-marvin-external-jobs.groovy
 * This sends a POST request which triggers UploadDiceBuildsToS3,
 * followed by the triggering of tests.
 */

pipeline {
    options {
        allowBrokenBuildClaiming()
    }
    agent any
    stages {
        stage('Run') {
            steps {
                withCredentials([usernamePassword(usernameVariable: 'MARVIN_TEST_USER', passwordVariable: 'MARVIN_TEST_PASSWORD', credentialsId: env.TEST_CREDENTIALS)]) {
                    script {
                        String codeChangelist = params.code_changelist
                        String dataChangelist = params.data_changelist
                        currentBuild.displayName = "${currentBuild.projectName}.${dataChangelist}.${codeChangelist}"
                        String payload = '{' +
                            "\"data_cl\": \"${dataChangelist}\", " +
                            "\"code_cl\": \"${codeChangelist}\", " +
                            "\"stream\": \"${env.stream}\"" +
                            '}'
                        echo('Paylod: ' + payload)
                        HttpClient httpClient = HttpClientBuilder.create().build()
                        HttpPost request = new HttpPost(env.url)
                        request.entity = new StringEntity(payload)

                        [
                            'api-key'     : env.MARVIN_TEST_PASSWORD,
                            'user-id'     : env.MARVIN_TEST_USER,
                            'username'    : 'dice-dre',
                            'channel-id'  : 'C025WAXKWJH',
                            'Content-Type': 'application/json',
                        ].each { n, v ->
                            request.setHeader(n, v)
                        }

                        HttpResponse response = httpClient.execute(request)
                        int statusCode = response.statusLine.statusCode
                        String content = EntityUtils.toString(response.entity, 'UTF-8')
                        if (statusCode != 200) {
                            throw new HttpException(statusCode + ' ' + content)
                        } else {
                            echo('Status Code: ' + statusCode + ' ' + content)
                        }
                    }
                }
            }
        }
    }
}
