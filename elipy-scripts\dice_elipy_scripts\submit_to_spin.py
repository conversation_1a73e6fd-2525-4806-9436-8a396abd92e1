"""
submit_to_spin.py

<PERSON>ript to handle uploading builds to Spin.
Uploads from the network shares to S3.
The S3 bucket has been prepared by the Spin team.
"""
import datetime
import os
import time
import tempfile

import click
import fnmatch
from elipy2 import LOGGER, SETTINGS, aws, filer_paths, secrets, core, build_metadata_utils
from elipy2.cli import pass_context
from elipy2.exceptions import ELIPYException
from elipy2.telemetry import collect_metrics

from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags

PLATFORM_CONFIGURATIONS = {
    "linuxserver": {
        "s3_bucket": SETTINGS.get(key="spin_s3_bucket", default="s3-gameserver-builds"),
        "role_arn": SETTINGS.get(
            key="spin_s3_role", default="arn:aws:iam::771966611754:role/dre-s3-gameserver-builds"
        ),
        "access_key_id_key": "SPIN_USER_AWS_ACCESS_KEY_ID",
        "secret_access_key_key": "SPIN_USER_AWS_SECRET_ACCESS_KEY",
        "file_patterns": ["*_Binaries.zip", "build.json"],
    },
    "linux64": {
        "s3_bucket": SETTINGS.get(key="linux64_spin_s3_bucket", default="s3-gameserver-builds"),
        "role_arn": SETTINGS.get(
            key="spin_s3_role", default="arn:aws:iam::048550671784:role/dre-s3-gameserver-builds"
        ),
        "access_key_id_key": "LINUX64_SPIN_USER_AWS_ACCESS_KEY_ID",
        "secret_access_key_key": "LINUX64_SPIN_USER_AWS_SECRET_ACCESS_KEY",
        "file_patterns": ["*", "build.json"],
    },
    "mod-level-tools": {
        "s3_bucket": "ripple-portal-raw-artifacts",
        "access_key_id_key": "MOD_LEVEL_TOOLS_SPIN_USER_AWS_ACCESS_KEY_ID",
        "secret_access_key_key": "MOD_LEVEL_TOOLS_SPIN_USER_AWS_SECRET_ACCESS_KEY",
        "source_build_path": "\\\\filer.dice.ad.ea.com\\Builds\\Tools\\mod-level-tools",
        "file_patterns": ["ExportedAssets", "ExportedLevels", "ExportedLevelJson"],
        "exclude_patterns": "out",
        "aws_region": "us-west-2",
    },
}


@click.command("submit_to_spin", short_help="Submits a build to Spin.")
@click.option("--code-branch", help="Branch/stream that the code/binary is coming from.")
@click.option("--code-changelist", help="Changelist of binaries.")
@click.option("--data-branch", help="Branch/stream that data is coming from.")
@click.option("--data-changelist", help="Changelist of data being used.")
@click.option(
    "--platform", help="Which platform to deploy (default is linuxserver).", default="linuxserver"
)
@click.option(
    "--format",
    "build_format",
    help="Which format to deploy (default is digital).",
    default="digital",
)
@click.option("--config", help="Code debug level (default is final).", default="final")
@click.option("--region", help="Which region to deploy (default is ww).", default="ww")
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    code_branch,
    code_changelist,
    data_branch,
    data_changelist,
    platform,
    build_format,
    config,
    region,
):
    """
    Uploads a given build to Spin.
    """
    add_sentry_tags(__file__)
    path_units = [
        data_branch,
        data_changelist,
        code_branch,
        code_changelist,
        platform,
        build_format,
        region.lower(),
        config.lower(),
    ]
    # Retrieve platform-specific configuration and bucket
    spin_platform_config = get_platform_configuration(platform)
    source_build_path = (
        spin_platform_config.get("source_build_path")
        if spin_platform_config.get("source_build_path")
        else filer_paths.get_frosty_build_path(*path_units)
    )
    LOGGER.info("Source path: {}".format(source_build_path))
    files_list = collect_files_to_submit(source_build_path, spin_platform_config, code_changelist)

    LOGGER.info("Contents of source path: {}".format(os.listdir(source_build_path)))
    elipy_aws = prepare_aws_for_spin(spin_platform_config)
    dst_prefix = "/".join([SETTINGS.get("project_name")] + path_units)
    if spin_platform_config.get("exclude_patterns"):
        dst_prefix = os.path.join(tempfile.gettempdir(), code_changelist + ".zip")
    upload_to_spin(elipy_aws, files_list, source_build_path, dst_prefix, spin_platform_config)

    LOGGER.info("Build successfully uploaded to S3 bucket under {}".format(dst_prefix))


def get_platform_configuration(platform):
    """
    Return platform-specific configuration
    """
    config = PLATFORM_CONFIGURATIONS.get(platform)
    if not config:
        raise ValueError(f"Platform configuration for '{platform}' is not defined.")

    return config


def collect_files_to_submit(source_build_path, platform_config, code_changelist=None):
    """
    Picks out the files to submit to Spin (S3) based on platform.
    """
    if not os.path.exists(source_build_path):
        raise ELIPYException(
            "No source Frosty build directory found in source path {0}: {1}".format(
                source_build_path, os.listdir(source_build_path)
            )
        )

    file_patterns = platform_config.get("file_patterns")

    if file_patterns is None:
        raise ValueError("Unsupported platform. Please check your platform configuration.")

    file_patterns = list(set(file_patterns))

    collected_files = []

    for pattern in file_patterns:
        # Include files from file_patterns and "archive.zip" if "*" is in the pattern,
        # else include just the files
        if pattern == "*":
            source = source_build_path
            destination = "archive.zip"
            extra_args = ["-r", "-x!archive.zip"]
            core.create_zip(source, os.path.join(source, destination), additional_args=extra_args)
            collected_files.append(destination)
        else:
            exclude_patterns = platform_config.get("exclude_patterns")
            if exclude_patterns is not None and code_changelist:
                extra_args = ["-xr!{}".format(exclude_patterns)]

                destination = os.path.join(tempfile.gettempdir(), code_changelist + ".zip")
                LOGGER.info("extra_args: {}".format(extra_args))
                core.create_zip(
                    source_build_path,
                    destination,
                    additional_args=extra_args,
                )

            matching_files = fnmatch.filter(os.listdir(source_build_path), pattern)
            collected_files.extend(matching_files)

    if not collected_files:
        raise ELIPYException(
            f"No files found for the specified platform in source path {source_build_path}"
        )

    return collected_files


def prepare_aws_for_spin(platform_config):
    """
    Set up the AWSManager with platform-specific configuration.
    """
    role_arn = platform_config.get("role_arn")
    secret_context = "project_secrets"
    credentials = secrets.get_secrets({secret_context: True})
    if credentials:
        credentials = next(v for v in credentials.values())
    else:
        raise ELIPYException(
            "Required credentials for accessing ESS vault secrets under {} not found.".format(
                secret_context
            )
        )

    # Use platform-specific keys based on the platform name
    access_key_id = credentials.get(platform_config["access_key_id_key"])
    secret_access_key = credentials.get(platform_config["secret_access_key_key"])
    aws_region = platform_config.get("aws_region")

    if not access_key_id or not secret_access_key:
        raise ELIPYException(
            "AWS access key and/or secret access key not found in secrets configuration."
        )

    elipy_aws = aws.AWSManager(
        access_key_id=access_key_id,
        secret_access_key=secret_access_key,
        aws_region=aws_region,
    )
    if role_arn is not None:
        elipy_aws.assume_role(role_arn, duration_seconds=28800)
    return elipy_aws


def upload_to_spin(elipy_aws, files_list, source_build_path, dst_prefix, platform_config):
    """
    Submits files to the appropriate S3 bucket using AWSManager.
    """
    bucket = platform_config["s3_bucket"]
    if dst_prefix.endswith(".zip"):
        path = r"{}".format(dst_prefix)
        filename = os.path.basename(path)
        elipy_aws.upload_to_s3(src_filename=dst_prefix, bucket=bucket, dst_filename=filename)
    else:
        # UPS pipeline requires zip upload to be last
        files_list.sort(key=lambda file: (file.endswith(".zip")))

        for file in files_list:
            src_filename = os.path.join(source_build_path, file)
            dst_filename = "/".join(
                [
                    dst_prefix,
                    file,
                ]
            )
            LOGGER.info("Uploading file {} to S3 bucket {}".format(src_filename, dst_filename))
            elipy_aws.upload_to_s3(
                src_filename=src_filename, bucket=bucket, dst_filename=dst_filename
            )

        if core.use_bilbo():
            bilbo = build_metadata_utils.setup_metadata_manager()
            timestamp = datetime.datetime.fromtimestamp(time.time()).strftime("%Y-%m-%d %H:%M")
            bilbo.register_spin_build(source_build_path, timestamp)
            LOGGER.info("Build registered in Bilbo with timestamp {}".format(timestamp))

    return True
