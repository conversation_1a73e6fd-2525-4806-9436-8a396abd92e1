package com.ea.lib.jobsettings

import com.ea.lib.LibCommonNonCps
import com.ea.lib.model.branchsettings.UnitTestsConfiguration

class UnitTestsSettings extends JobSetting {
    String nonVirtualCodeBranch
    String nonVirtualCodeFolder
    private UnitTestsConfiguration configuration
    Map fbLoginDetails
    String userCredentials

    void initializeStart(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        configuration = (UnitTestsConfiguration) branchInfo.unittests
        description = "Sync ${branchInfo.branch_name} code and run unit tests"
        codeBranch = branchInfo.code_branch
        codeFolder = branchInfo.code_folder
        projectName = projectFile.name
        cronTrigger = configuration.trigger
        nonVirtualCodeBranch = configuration.nonVirtualCodeBranch ?: ''
        nonVirtualCodeFolder = configuration.nonVirtualCodeFolder ?: ''
        isDisabled = !configuration.enabled
    }

    void initializeJob(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        fbLoginDetails = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_fb_settings', [:], projectFile) as Map
        userCredentials = LibCommonNonCps.get_setting_value(branchInfo, [], 'user_credentials', null, projectFile)
        String credentials = userCredentials ? ' --email %monkey_email% --password "%monkey_passwd%"' : ''
        configuration = (UnitTestsConfiguration) branchInfo.unittests
        jobLabel = configuration.label
        description = 'Runs unit tests'
        buildName = '${JOB_NAME}.${ENV, var="CODE_CHANGELIST"}'
        timeoutMinutes = configuration.timeoutMinutes
        elipyCmd = "${this.elipyCall} unittests${credentials}"
    }
}
