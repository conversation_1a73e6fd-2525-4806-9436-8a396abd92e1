package com.ea.project.gnt

import com.ea.project.Cobra

class Granite {
    static String name = 'granite'
    static String short_name = 'gnt'
    static Boolean frostbite_syncer_setup = false
    static Boolean single_perforce_server = false
    static Boolean presync_machines = false
    static String user_credentials = 'svc_res01.gnt.build'
    static String vault_server_credentials = 'dice-online-cas-prod-secret-id'
    static String vault_server_variable = 'VAULT_ONLINE_CAS_PROD_SECRET_ID'
    static String vault_credentials = 'cobra-online-rob-prod-secret-id'
    static String vault_variable = 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
    static String game_team_secrets_credential = 'game-team-secrets-secret-id'
    static String game_team_secrets_credential_extra = ''
    static String game_team_secrets_credential_online_prod = ''
    static String game_team_secrets_variable = 'GAME_TEAM_SECRETS_SECRET_ID'

    static String dataset = 'bfdata'
    static String frostbite_licensee = 'BattlefieldGame'

    static String webexport_script_path = 'Scripts\\DICE\\webexport.py'
    static Boolean fake_ooa_wrapped_symbol = false
    static Boolean commerce_debug_disable = true
    static String workspace_root = 'D:\\dev'
    static String fbcli_call = 'tnt\\bin\\fbcli\\cli.bat x64'
    static String location = ''
    static String elipy_scripts_config_file = 'elipy_granite.yml'
    static String elipy_install_call = "${fbcli_call} && ${Cobra.elipy_install} $elipy_scripts_config_file >> ${workspace_root}\\logs\\install-elipy.log 2>&1"
    static String elipy_setup_call = "${fbcli_call} && ${Cobra.elipy_setup_env} $elipy_scripts_config_file >> ${workspace_root}\\logs\\setup-elipy-env.log 2>&1"
    static String elipy_call = "${elipy_setup_call} && elipy"

    static String azure_workspace_root = 'E:\\dev'
    static String azure_elipy_install_root = 'C:\\dev'
    static String azure_elipy_setup_call = "$fbcli_call && $azure_elipy_install_root\\ci\\setup-elipy-env.bat $elipy_scripts_config_file >> $azure_workspace_root\\logs\\setup-elipy-env.log 2>&1"
    static String azure_elipy_install_call = "$fbcli_call && $azure_elipy_install_root\\ci\\install-elipy.bat $elipy_scripts_config_file >> $azure_workspace_root\\logs\\install-elipy.log 2>&1"
    static String azure_elipy_call = "$azure_elipy_setup_call && elipy"

    static String p4_browser_url = 'https://swarm.frostbite.com/'
    static String p4_user_single_slash = '%USERDOMAIN%\\%USERNAME%'
    static Map p4_extra_servers = [:]

    static String p4_code_root = '//dicestudio/battlefield'
    static String p4_code_creds = 'perforce-p4buildedge01-fb-granite01'
    static String p4_code_server = 'dicela-p4edge-fb.la.ad.ea.com:2001'
    static String p4_code_client = 'jenkins-${NODE_NAME}-codestream'
    static String p4_code_client_env = 'jenkins-%NODE_NAME%-codestream'

    static String p4_data_root = '//data/battlefield'
    static String p4_data_creds = 'perforce-granite01'
    static String p4_data_server = 'res-p4buildedge-santiago.ad.ea.com:2001'
    static String p4_data_client = 'jenkins-${NODE_NAME}-' + dataset + 'stream'
    static String p4_data_client_env = 'jenkins-%NODE_NAME%-' + dataset + 'stream'

    static Map p4_code_servers = [
        'frostbite_build_dice': p4_code_server,
    ]

    static List<Map> p4_data_servers = [
        [name: 'granite_build_dice', p4_port: p4_data_server],
    ]

    static Map icepick_settings = [
        icepick_test          : 'KingstonUnitTests',
        icepick_preflight_test: 'PreflightUnitTests',
        settings_files        : 'Config/Icepick/IcepickSettings.ini',
    ]

    static String autotest_matrix = 'GntAutotestMatrix'

    static Boolean compress_symbols = true
    static Boolean compress_symbols_code_win64server = false
    static Boolean compress_symbols_code_win64game = false
    static Boolean compress_symbols_code_xb1 = false
    static Boolean compress_symbols_code_xbsx = false

    static Boolean is_cloud = false
    static Boolean clean_master_version_check = true
    static Boolean expression_debug_data = true
    static Boolean verify_post_vault = true
    static List<Map> vault_secrets_project = Cobra.af2_vault_credentials
}

