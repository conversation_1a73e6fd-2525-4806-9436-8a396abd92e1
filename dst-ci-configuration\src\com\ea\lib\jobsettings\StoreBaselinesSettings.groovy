package com.ea.lib.jobsettings

class StoreBaselinesSettings extends JobSetting {
    String description = 'Job for storing regular baseline builds'

    void initialize(
        Class branchFile, Class masterFile, Class projectFile,
        String branchName
    ) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)

        this.codeBranch = this.branchInfo.code_branch
        this.dataBranch = this.branchInfo.data_branch
        this.buildName = '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'

        this.jobLabel = this.branchInfo.job_label_statebuild ?: 'statebuild'
        def not_statebuild_frosty = this.branchInfo.statebuild_patchfrosty == false || this.branchInfo.statebuild_frosty == false
        if (this.branchInfo.statebuild_data == false && this.branchInfo.job_label_statebuild == null && not_statebuild_frosty) {
            // makes it possible to set job_label_statebuild to 'data_branch util' and have it run on a util machine instead
            this.jobLabel = "${this.dataBranch} && data && server"
            // This uses a machine that has synced data, and builds the job with the shortest build time.
        }

        def elipyCmd = this.elipyCall + ' backup_baseline --platform %platform%' +
            ' --baseline-code-branch %code_branch% --baseline-code-changelist %code_changelist%' +
            ' --baseline-data-branch %data_branch% --baseline-data-changelist %data_changelist%'
        this.elipyCmd = elipyCmd.toString()
    }
}
