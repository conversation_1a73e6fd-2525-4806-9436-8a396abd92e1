{#
    Command:
        integrate_compile_upgrade_cook
            short_help: Runs a code integration, with extra validation.

    Arguments:

    Required variables:
        assets
            required: True
            multiple: True
            help: Assets to use for data cook.
        code_changelist
            required: True
            help: Code changelist in Perforce.
        data_directory
            required: True
            help: Directory for game data.
        mapping
            required: True
            help: Mapping to use for the integration.
        p4_client_code
            required: True
            help: Perforce workspace for code.
        p4_client_data
            required: True
            help: Perforce workspace for data.
        p4_port_code
            required: True
            help: Perforce server for code.
        p4_port_data
            required: True
            help: Perforce server for data.
        source_branch
            required: True
            help: Source branch for code.

    Optional variables:
        code_clean
            default: false
            help: Delete TnT/Local if --code-clean true is passed.
        data_clean
            default: false
            help: Clean Avalanche if --data-clean true is passed.
        data_platform
            default: win64
            help: Platform to use for data cook.
        domain_user
            default: None
            help: User to authenticate to package server as, e.g. DOMAIN\user.
        email
            default: None
            help: User email to authenticate to package server.
        framework_args
            multiple: True
            help: Framework arguments for the code build.
        ignore_source_history
            is_flag: True
            help: Ignore source file history (sets the Perforce integrate flag -Di).
        licensee
            multiple: True
            default: None
            help: Game licensee, e.g. BattelfieldGame.
        p4_user_code
            default: None
            help: Perforce user name for code.
        p4_user_data
            default: None
            help: Perforce user name for data.
        password
            default: None
            help: User credentials to authenticate to package server.
        pipeline_args
            multiple: True
            help: Pipeline arguments for the data build.
        submit/__no_submit
            default: True
            help: Submit to Perforce or not.
        submit_message
            default: ''
            help: Message to include in the submit message.
        use_snowcache
            is_flag: True
            help: Use snowcache when building code.
        snowcache_mode_override
            type: click.Choice(SNOWCACHE_MODES, case_sensitive=False)
            default: ''
            help: Override the logically evaluated snowcache mode with this
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    integrate_compile_upgrade_cook
    --assets {{ assets }}
    --code-changelist {{ code_changelist }}
    --data-directory {{ data_directory }}
    --mapping {{ mapping }}
    --p4-client-code {{ p4_client_code }}
    --p4-client-data {{ p4_client_data }}
    --p4-port-code {{ p4_port_code }}
    --p4-port-data {{ p4_port_data }}
    --source-branch {{ source_branch }}
    {%- if code_clean %}
    --code-clean {{ code_clean }}
    {%- endif %}
    {%- if data_clean %}
    --data-clean {{ data_clean }}
    {%- endif %}
    {%- if data_platform %}
    --data-platform {{ data_platform }}
    {%- endif %}
    {%- if domain_user %}
    --domain-user {{ domain_user }}
    {%- endif %}
    {%- if email %}
    --email {{ email }}
    {%- endif %}
    {%- if framework_args %}
    --framework-args {{ framework_args }}
    {%- endif %}
    {%- if ignore_source_history %}
    --ignore-source-history {{ ignore_source_history }}
    {%- endif %}
    {%- if licensee %}
    --licensee {{ licensee }}
    {%- endif %}
    {%- if p4_user_code %}
    --p4-user-code {{ p4_user_code }}
    {%- endif %}
    {%- if p4_user_data %}
    --p4-user-data {{ p4_user_data }}
    {%- endif %}
    {%- if password %}
    --password {{ password }}
    {%- endif %}
    {%- if pipeline_args %}
    --pipeline-args {{ pipeline_args }}
    {%- endif %}
    {%- if submit/__no_submit %}
    --submit/--no-submit {{ submit/__no_submit }}
    {%- endif %}
    {%- if submit_message %}
    --submit-message {{ submit_message }}
    {%- endif %}
    {%- if use_snowcache %}
    --use-snowcache {{ use_snowcache }}
    {%- endif %}
    {%- if snowcache_mode_override %}
    --snowcache-mode-override {{ snowcache_mode_override }}
    {%- endif %}
  displayName: elipy integrate_compile_upgrade_cook
