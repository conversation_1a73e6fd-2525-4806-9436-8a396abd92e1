"""
bilbo_register_verified_for_preflight.py
"""
import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import filer_paths, LOGGER, build_metadata_utils
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command(
    "bilbo_register_verified_for_preflight",
    short_help="Registers a verified_for_preflight build in Bilbo.",
)
@click.option("--branch", help="Perforce code branch/stream name.", required=True)
@click.option(
    "--changelist",
    required=True,
    help="Changelist number of code build used to verify data.",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    branch,
    changelist,
):
    """
    Registers a verified_for_preflight build in the configured metadata services.
    """
    # adding sentry tags
    add_sentry_tags(__file__)
    metadata_manager = build_metadata_utils.setup_metadata_manager()

    source = filer_paths.get_code_build_root_path(branch, changelist)

    LOGGER.info("Registering verified_for_preflight build on {}.{}".format(branch, changelist))

    metadata_manager.tag_build_as_verified_for_preflight(source)
