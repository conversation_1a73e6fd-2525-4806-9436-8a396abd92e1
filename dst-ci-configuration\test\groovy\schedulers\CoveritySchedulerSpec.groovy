package schedulers

import support.DeclarativePipelineSpockTest

class CoveritySchedulerSpec extends DeclarativePipelineSpockTest {
    class TestClassCoverity {
        static String name = 'some_name'
        static String short_name = 's_n'
    }

    void setup() {
        binding.setVariable('env', [
            CODE_BRANCH            : 'code-branch',
            CODE_FOLDER            : 'code-folder',
            NON_VIRTUAL_CODE_BRANCH: 'trunk-code-dev',
            NON_VIRTUAL_CODE_FOLDER: 'mainline',
            P4_CHANGELIST          : '234',
            JOB_NAME               : 'my-job',
            BRANCH_NAME            : 'a-branch',
        ])
        binding.setVariable('params', [
            CLEAN_LOCAL: 'false',
        ])
        helper.registerAllowedMethod('ProjectClass', [String]) { projectName -> TestClassCoverity }
        helper.registerAllowedMethod('get_branchfile', [String, String]) { projectName, branchName ->
            [
                general_settings      : [
                    coverity_settings: [
                        trigger                : 'a cron trigger',
                        non_virtual_code_branch: 'trunk-code-dev',
                        non_virtual_code_folder: 'mainline',
                        run_coverity           : true,
                    ]
                ],
                standard_jobs_settings: [:],
            ]
        }
        helper.with {
            registerAllowedMethod('setPollScmTriggers', []) {}
            registerAllowedMethod('P4PreviewCode', [Object, String, String, String, String, String, List, List, Object]) {}
            registerAllowedMethod('EnvInject', [Map, Map]) { currentBuild, injectMap ->
                binding.setVariable('env', binding.getVariable('env') + injectMap)
            }
        }
    }

    void 'test CoverityScheduler runs'() {
        when:
        runScript('CoverityScheduler.groovy')
        printCallStack()
        then:
        testNonRegression()
    }
}
