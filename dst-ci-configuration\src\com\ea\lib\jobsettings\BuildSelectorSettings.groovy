package com.ea.lib.jobsettings

import com.ea.lib.LibCommonNonCps

class BuildSelectorSettings extends JobSetting {

    void initialize(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.autotest_branches as Map)
        jobLabel = LibCommonNonCps.get_setting_value(branchInfo, ['bilbo'], 'job_label_statebuild', 'statebuild')
        if (!branchInfo.statebuild_autotest) {
            jobLabel = branchInfo.data_branch
        }
        elipyCmd = "${this.elipyCall} bilbo_select_autotest" +
            " --code-branch ${branchInfo.code_branch} --data-branch ${branchInfo.data_branch}" +
            ' %is_test_with_loose_files% --platform %platform% --use-azure-drone-build %use_azure_drone_build%' +
            ' --use-shift-build %use_shift_build% --use-spin-build %use_spin_build% --use-latest-drone %use_latest_drone%' +
            ' %required_platforms% %client_platforms% --job-url "%start_job_url%" --build-timeout-hours "%build_timeout_hours%" --region %region%' +
            ' --config %config%'
    }
}
