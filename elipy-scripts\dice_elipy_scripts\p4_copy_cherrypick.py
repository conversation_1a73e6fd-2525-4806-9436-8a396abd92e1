"""
p4_copy_cherrypick.py
"""
import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import LOGGER
from elipy2 import p4, core, frostbite_core
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command("p4_copy_cherrypick", short_help="Calls copy and cherrypick script.")
@click.option("--port", required=True)
@click.option("--client", required=True)
@click.option("--user", default=None, help="Perforce user name.")
@click.option("--source-branch", default="", help="branch from which we are integrating.")
@click.option("--json-file", help="Config file with changelists to cherrypick.")
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(_, port, client, user, source_branch, json_file):
    """
    Calls copy and cherrypick script
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    LOGGER.info("Running cherrypick copy from {} with {}".format(source_branch, json_file))

    perforce = p4.P4Utils(port=port, client=client, user=user)
    perforce.revert()

    core.ensure_p4_config(
        root_dir=frostbite_core.get_tnt_root(), port=port, client=client, user=user
    )

    integrate_script = os.path.join(
        frostbite_core.get_game_root(), "cherrypick_copy", "integrate_stream.py"
    )
    cmd = ["python", integrate_script, "--jsonFile", json_file]
    try:
        core.run(cmd, print_std_out=True)
    finally:
        perforce.revert()
