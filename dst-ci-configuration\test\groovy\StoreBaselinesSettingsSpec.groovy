import com.ea.lib.jobsettings.StoreBaselinesSettings
import spock.lang.Specification

class StoreBaselinesSettingsSpec extends Specification {

    class BranchFile {
        static Map standard_jobs_settings = [
            workspace_root    : 'workspace-root',
            elipy_call        : 'elipy-call',
            elipy_install_call: 'elipy-install-call',
        ]
        static Map general_settings = [:]
    }
//add code branch and data branch
    class MasterFile {
        static Map branches = [
            'branch': [code_folder: 'dev', code_branch: 'code_branch', data_folder: 'dev', data_branch: 'data_branch'],
        ]
    }

    @SuppressWarnings('EmptyClass')
    class ProjectFile {}

    void "test that we get expected job settings"() {
        when:
        StoreBaselinesSettings settings = new StoreBaselinesSettings()
        settings.initialize(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            codeBranch == 'code_branch'
            dataBranch == 'data_branch'
            description == 'Job for storing regular baseline builds'
            jobLabel == 'statebuild'
            workspaceRoot == 'workspace-root'
            elipyInstallCall == 'elipy-install-call'
            elipyCmd == 'elipy-call backup_baseline --platform %platform% --baseline-code-branch %code_branch% --baseline-code-changelist %code_changelist% --baseline-data-branch %data_branch% --baseline-data-changelist %data_changelist%'
        }
    }

    void "test not_statebuild_frosty jobLabel logic frosty"() {
        when:
        StoreBaselinesSettings settings = new StoreBaselinesSettings()
        BranchFile.general_settings += [
            statebuild_data  : false,
            statebuild_frosty: false,
        ]
        settings.initialize(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            codeBranch == 'code_branch'
            dataBranch == 'data_branch'
            description == 'Job for storing regular baseline builds'
            jobLabel == "${dataBranch} && data && server"
            workspaceRoot == 'workspace-root'
            elipyInstallCall == 'elipy-install-call'
            elipyCmd == 'elipy-call backup_baseline --platform %platform% --baseline-code-branch %code_branch% --baseline-code-changelist %code_changelist% --baseline-data-branch %data_branch% --baseline-data-changelist %data_changelist%'
        }
    }

    void "test not_statebuild_frosty jobLabel logic patchfrosty"() {
        when:
        StoreBaselinesSettings settings = new StoreBaselinesSettings()
        BranchFile.general_settings += [
            statebuild_data       : false,
            statebuild_patchfrosty: false,
        ]
        settings.initialize(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            codeBranch == 'code_branch'
            dataBranch == 'data_branch'
            description == 'Job for storing regular baseline builds'
            jobLabel == "${dataBranch} && data && server"
            workspaceRoot == 'workspace-root'
            elipyInstallCall == 'elipy-install-call'
            elipyCmd == 'elipy-call backup_baseline --platform %platform% --baseline-code-branch %code_branch% --baseline-code-changelist %code_changelist% --baseline-data-branch %data_branch% --baseline-data-changelist %data_changelist%'
        }
    }
}
