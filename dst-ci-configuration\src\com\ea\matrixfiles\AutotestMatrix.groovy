package com.ea.matrixfiles

import com.ea.lib.model.autotest.AutotestCategory
import com.ea.lib.model.autotest.BranchConfiguration
import com.ea.lib.model.autotest.Platform
import com.ea.lib.model.autotest.TestInfo

/**
 * Autotest configurations used for creating jobs in Jenkins.
 * <p>
 * See <a href='https://docs.google.com/document/d/1pTxwXhm0T5Mstbg4uaBkjWruC5ntz7pZxqGsmBEcTwk/preview'>Flow Overview</a>
 * See <a href='https://docs.google.com/document/d/1mTfOtPPX93529M7EgmmaGBpmcuMoDyhZ7ZlYCKoVUzw/preview'>Jenkins Config</a>
 **/
abstract class AutotestMatrix {

    protected static final String DEFAULT = 'default'

    /**
     * Returns a {@link BranchConfiguration}
     * @param branchName name of the branch to add the configuration for
     * @param testInfo The configuration
     * @param crontTrigger Optional CRON trigger
     * @return a {@link BranchConfiguration}
     */
    static BranchConfiguration branchConfiguration(String branchName, TestInfo testInfo, String crontTrigger = '', List<String> downstreamAutotestCategories = []) {
        return new BranchConfiguration(branchName: branchName, testInfo: testInfo, cronTrigger: crontTrigger, downstreamAutotestCategories: downstreamAutotestCategories)
    }
    /**
     * Returns timeout_hours for the given test category. Prioritizes testInfo.timeout_hours,
     * but if it's not set it picks the highest timeout from the test suites.
     *
     * @param testCategory the test category to parse
     * @return the timeout in hours for the given test category
     */
    static int getTimeoutHours(AutotestCategory testCategory) {
        boolean canOverrideTimeout = true
        int timeoutHours = 1
        if (testCategory.testInfo.timeoutHours) {
            canOverrideTimeout = false
            timeoutHours = testCategory.testInfo.timeoutHours
        }
        if (canOverrideTimeout) {
            testCategory.testInfo.tests.each {
                if (it.timeoutHours > timeoutHours) {
                    timeoutHours = it.timeoutHours
                }
            }
        }
        return timeoutHours
    }
    /**
     * Returns the branches that the autotests are defined for.
     * <p>
     * It's important to add any new branches in the implementing method, otherwise the test suite won't validate them.
     *
     * @return the branches in an {@link List}
     */
    abstract List<String> getBranches()

    /**
     * A start job is created for every declared {@code testCategory} (one for each branch) and is triggered by a CRON trigger.
     * <p>
     * Additional child jobs are also created for every {@code testCategory} which trigger individual tests defined in {@code testCategory.testInfo}.
     *
     * @param branchName the branch to get the configurations for
     * @return all test categories, but populated with {@code testCategory.testInfo} and {@code testCategory.trigger} for the given branch.
     */
    List<AutotestCategory> getTestCategories(String branchName) {
        return filterCategories(testCategories, branchName)
    }

    /**
     * A start job is created for every declared {@code testCategory} (one for each branch) and is triggered by a CRON trigger.
     * <p>
     * Additional child jobs are also created for every {@code testCategory} which trigger individual tests defined in {@code testCategory.testInfo}.
     *
     * @return all test categories.
     */
    abstract List<AutotestCategory> getTestCategories()
    /**
     * A manual job is created for the manual test categories (one start job for each branch). It is designed to trigger all of the declared categories.
     * The job accepts changelists as arguments and has no CRON trigger, it's only triggered manually, hence the name.
     *
     * @param branchName the branch to get the configurations for
     * @return all manual test categories, but populated with {@code testCategory.testInfo} and {@code testCategory.trigger} for the given branch.
     */
    List<AutotestCategory> getManualTestCategories(String branchName) {
        return filterCategories(manualTestCategories, branchName)
    }
    /**
     * A manual job is created for the manual test categories (one start job for each branch). It is designed to trigger all of the declared categories.
     * The job accepts changelists as arguments and has no CRON trigger, it's only triggered manually, hence the name.
     *
     * @return all manual test categories.
     */
    abstract List<AutotestCategory> getManualTestCategories()
    /**
     * Returns a test category with the given name on a given branch.
     *
     * @param testCategory test configuration
     * @param branchName name of the branch to get test categories for
     * @return The configured test category, throws an exception if not found
     */
    AutotestCategory getTestCategory(String testCategory, String branchName) throws Exception {
        List<AutotestCategory> testCategories = getTestCategories(branchName)
        AutotestCategory category = testCategories.find { category -> category.name == testCategory }
        if (!category) {
            throw new IllegalArgumentException('Invalid name for test category: ' + testCategory)
        }
        return category
    }
    /**
     * Returns the default platforms for the given branch
     *
     * @param branchName name of the branch to get default platforms for
     */
    List<Platform> getPlatforms(String branchName) {
        return findPlatforms(platforms, branchName)
    }
    /**
     * Returns the default platforms
     */
    abstract Map<String, List<Platform>> getPlatforms()
    /**
     * Returns the requested setup for testing levels in parallel.
     *
     * @param branchName name of the branch to get the default parallel setting for
     * @return {@code true} if they should run in parallel, {@code false} otherwise.
     */
    abstract boolean shouldLevelsRunInParallel(String branchName)
    /**
     * After failed jobs a Slack notification is sent for the configured platforms.
     *
     * @param branchName name of the branch to get Slack settings for
     * @return the Slack settings for the given branch
     */
    abstract Map getSlackSettings(String branchName)
    /**
     * Manual {@code testCategories}> have an additional level of parallelism compared to regular {@code testCategories}.
     * These settings are used for controlling how many {@code testCategories} should run in parallel.
     *
     * @param branchName branch to get the configurations for
     * @return the additional configurations in addition to the manual {@code testCategories} returned by {@link #getManualTestCategories}
     */
    abstract Map getManualTestCategoriesSetting(String branchName)
    /**
     * Returns true if the given branch has any manual tests configured, false otherwise
     *
     * @param branchName bane of the branch to check for manual test categories for
     * @return {@code true} if there are any tests defined, {@code false} otherwise
     */
    boolean hasManualTestJobs(String branchName) {
        List<AutotestCategory> testCategories = getManualTestCategories(branchName)
        return testCategories.size() > 0
    }

    /**
     * Returns true if the given branch has any FrostEd tests configured, false otherwise
     *
     * @param branchName bane of the branch to check for FrostEd test categories for
     * @return {@code true} if there are any tests defined, {@code false} otherwise
     */
    boolean hasFrostedTestJobs(String branchName) {
        return getTestCategories(branchName).any { it.isFrostedAutotest }
    }

    /**
     * Returns the value for the given {@code branchName} in {@code collection}.
     *
     * @param branchName the key
     * @param collection the {@link Map} to parse
     * @return the value for the given key
     */
    // Only disable when you want a method to be able to return several different types.
    @SuppressWarnings('MethodReturnTypeRequired')
    protected static def getSetting(String branchName, Map collection) {
        for (item in collection.keySet()) {
            if (item == branchName) {
                return collection[item]
            }
        }
        return collection[DEFAULT]
    }

    /**
     * Returns all categories configured for the given branch. Populates {@code testCategory.testInfo} and {@code testCategory.trigger}.
     *
     * @param categories the categories to parse.
     * @param branchName The branch to retrieve the categories for
     * @return Categories configured for the given branch.
     */
    private static List<AutotestCategory> filterCategories(List<AutotestCategory> categories, String branchName) {
        return categories.findAll {
            it.branches.find {
                it.branchName == branchName
            }
        }.collect {
            def branchConfiguration = it.branches.find {
                it.branchName == branchName
            }
            it.testInfo = branchConfiguration.testInfo
            it.trigger = branchConfiguration.cronTrigger
            it.downstreamAutotestCategories = branchConfiguration.downstreamAutotestCategories
            it
        }
    }

    /**
     * Finds the platforms configured for the given branch
     * @param platforms platforms to parse
     * @param branchName the branch to look for
     * @return a list of platforms configured for the given branch. Returns an empty List if none are found.
     */
    private static List<Platform> findPlatforms(Map<String, List<Platform>> platforms, String branchName) {
        return platforms.find {
            it.key == branchName
        }?.value ?: []
    }
}
