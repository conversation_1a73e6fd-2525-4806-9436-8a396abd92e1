import com.ea.lib.LibSlack
import spock.lang.Specification
import spock.lang.Unroll
import support.Steps
import support.TestParent
import support.TestPipeLineContext

@Unroll
class LibSlackSpec extends Specification {
    String PROJECT = 'cob'
    String CHANNEL = 'test'
    String DOMAIN = LibSlack.DEFAULT_DOMAIN
    String TOKEN = LibSlack.AUTHENTICATION_CREDS[PROJECT]

    void setupSpec() {
        Steps.metaClass.slackSend = { Map it -> return }
    }

    void setup() {
        GroovySpy(Steps, global: true)
    }

    void 'forPipelines with settings = #setting'() {
        given:
        GroovySpy(LibSlack, global: true)

        when:
        LibSlack.forPipelines(null, setting, PROJECT)

        then:
        with(LibSlack) {
            callsToNoSettings * noSettings() >> null
            callsToExecuteForPipelines * executeForPipelines(*_) >> null
        }

        where:
        setting                 | callsToNoSettings | callsToExecuteForPipelines
        null                    | 1                 | 0
        ''                      | 1                 | 0
        [:]                     | 1                 | 0
        'channel'               | 0                 | 1
        [channels: ['channel']] | 0                 | 1
    }

    void 'executeForPipelines with alwaysNotify = #alwaysNotify AND skipForMultipleFailures = #skipForMultipleFailures AND build result history of #results'() {
        given:
        def job = new TestParent([''] * results.size(), results).init()
        def run = job.builds.last()
        Steps steps = Mock()
        def context = new TestPipeLineContext(run, steps)

        when:
        LibSlack.executeForPipelines(context, PROJECT, [CHANNEL], alwaysNotify, skipForMultipleFailures)

        then:
        calls * steps.slackSend(
            channel: CHANNEL,
            color: colour,
            message: "${run.displayName} - job $messagePart (<${run.absoluteUrl}|Open>)",
            teamDomain: DOMAIN,
            tokenCredentialId: TOKEN
        )

        where:
        results                | alwaysNotify | skipForMultipleFailures || calls | colour   | messagePart
        ['SUCCESS']            | false        | false                   || 0     | null     | null
        ['SUCCESS', 'SUCCESS'] | false        | false                   || 0     | null     | null
        ['SUCCESS']            | true         | false                   || 1     | 'good'   | 'working'
        ['SUCCESS', 'SUCCESS'] | true         | false                   || 1     | 'good'   | 'working'
        ['SUCCESS', 'FAILURE'] | false        | false                   || 1     | 'danger' | 'started to fail'
        ['FAILURE']            | false        | false                   || 1     | 'danger' | 'started to fail'
        ['FAILURE', 'FAILURE'] | false        | true                    || 0     | null     | null
        ['FAILURE', 'FAILURE'] | false        | false                   || 1     | 'danger' | 'still failing'
        ['FAILURE', 'SUCCESS'] | false        | false                   || 1     | 'good'   | 'back to normal'
        ['ABORTED']            | false        | false                   || 1     | 'danger' | 'aborted'
    }

    void 'executeForPipelines: override messageColour and reportMessage'() {
        given:
        def job = new TestParent(['']).init()
        def run = job.builds.last()
        Steps steps = Mock()
        def context = new TestPipeLineContext(run, steps)
        def colour = '#CF6BA9'
        def overrideMessage = 'I think it would be a very good idea.'

        when:
        LibSlack.executeForPipelines(context, PROJECT, [CHANNEL], true, false, colour, overrideMessage)

        then:
        1 * steps.slackSend(
            channel: CHANNEL,
            color: colour,
            message: overrideMessage,
            teamDomain: DOMAIN,
            tokenCredentialId: TOKEN
        )
    }
}
