/**
 * Manually added documentation for vars functions
 */
def closures = context(scope: closureScope())
contributor(closures) {
    method(name: 'AutotestRegisterBilboResult', type: 'hudson.model.Result', params: [runBilbo: 'java.lang.Boolean', status: 'java.lang.String', branchName: 'java.lang.String', dataset: 'java.lang.String', buildSelectorResult: 'Map', testDefinition: 'java.lang.String', currentResult: 'hudson.model.Result', jobReferences: 'List<JobReference>'], doc: 'Reports that all tests have run in the given testCategory')
    method(name: 'AutotestRunCategory', type: 'hudson.model.Result', params: [testCategory: 'com.ea.lib.model.autotest.AutotestCategory', branchName: 'java.lang.String', autotestMatrixName: 'java.lang.String', buildSelectorResult: 'Map', jobReferences: 'List<JobReference>'], doc: 'Runs the tests in the given testCategory and branch. Reports with a slack notification if configured.')
    method(name: 'AutotestRunCategory', type: 'hudson.model.Result', params: [testCategory: 'com.ea.lib.model.autotest.AutotestCategory', branchName: 'java.lang.String', autotestMatrixName: 'java.lang.String', buildSelectorResult: 'Map', jobReferences: 'List<JobReference>', currentResult: 'hudson.model.Result'], doc: 'Runs the tests in the given testCategory and branch. Reports with a slack notification if configured.')
    method(name: 'AutotestRunCategoryParallel', type: 'hudson.model.Result', params: [categoriesSettings: 'Map', branchName: 'java.lang.String', autotestMatrixName: 'java.lang.String', buildSelectorResult: 'Map', jobReferences: 'List<JobReference>'], doc: 'Run all the categories for a given branch in parallel')
    method(name: 'AutotestSetPipelineResult', type: 'void', params: [currentBuild: 'Object', result: 'hudson.model.Result'], doc: 'Sets the build\'s result and calls DownstreamErrorReporting')
    method(name: 'BuildSelector', type: 'hudson.model.Result', params: [autotestMatrix: 'com.ea.matrixfiles.AutotestMatrix', branchName: 'java.lang.String', jobUrl: 'java.lang.String', jobReferences: 'List<JobReference>', currentResult: 'hudson.model.Result'], doc: 'Runs the build-selector')
    method(name: 'BuildSelectorComposeJob', type: 'groovy.lang.Closure', params: [testCategory: 'com.ea.lib.model.autotest.AutotestCategory', jobName: 'java.lang.String', resultMap: 'Map', platform: 'java.lang.String', startJobUrl: 'java.lang.String', jobReferences: 'List<JobReference>'], doc: 'Composes a build trigger closure for the build-selector.')
    method(name: 'retryOnFailureCause', type: 'hudson.model.Result', params: [retryCount: 'java.lang.Integer', jobReferences: 'List', allowFailure: 'java.lang.Boolean', script: 'groovy.lang.Closure'], doc: 'Wrapper to add retry functionality to a task. The given script is retried `retryCount` times when any downstream jobs fail. Throws an error if `retryCount` retries is reached.')
    method(name: 'retryOnFailureCause', type: 'hudson.model.Result', params: [retryCount: 'java.lang.Integer', jobReferences: 'List', script: 'groovy.lang.Closure'], doc: 'Wrapper to add retry functionality to a task. The given script is retried `retryCount` times when any downstream jobs fail. Throws an error if `retryCount` retries is reached. `allowFailure` is set to false')
    method(name: 'sendAlertIfFailedConsecutiveTimes', type: 'void', params: [jobName: 'java.lang.String', jobUrl: 'java.lang.String', jobNumber: 'java.lang.String', slackChannel: 'java.lang.String', projectShortName: 'java.lang.String', count: 'java.lang.Integer'], doc: 'Sends a Slack alert if <count> consecutive jobs have failed.')
    method(name: 'EnvInject', type: 'void', params: [currentBuild: 'Object', injectMap: 'Map'], doc: 'Updates the parameters in a job that will later be available for other jobs.')
}
