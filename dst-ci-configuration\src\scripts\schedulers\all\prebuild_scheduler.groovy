package scripts.schedulers.all

import com.ea.project.GetBranchFile
import com.ea.lib.LibJenkins

def project = ProjectClass(env.project_name)

/**
 * prebuild_scheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Trigger prebuild jobs.') {
            steps {
                script {
                    def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
                    def reference_code_job = branchfile.standard_jobs_settings?.reference_job_prebuild ?: env.branch_name + '.code.start'
                    def last_good_code = LibJenkins.getLastStableCodeChangelist(reference_code_job)
                    def last_good_prebuild = LibJenkins.getLastStableCodeChangelist(env.branch_name + '.prebuild')
                    def code_changelist = params.code_changelist ?: last_good_code
                    if (code_changelist != last_good_prebuild) {
                        def clean_local = params.clean_local

                        def args = [
                            string(name: 'code_changelist', value: code_changelist),
                            string(name: 'clean_local', value: clean_local),
                        ]

                        def inject_map = [
                            'code_changelist': code_changelist,
                        ]
                        EnvInject(currentBuild, inject_map)
                        currentBuild.displayName = env.JOB_NAME + '.' + code_changelist

                        def job_name = env.branch_name + '.prebuild'
                        def prebuild_job = build(job: job_name, parameters: args, propagate: false)
                        // only when prebuild complete with success
                        if (prebuild_job.result.toString() == 'SUCCESS') {
                            if (env.outsource_validation.toBoolean()) { //trigger outsourcevalidation job
                                echo 'Trigger downstream job to validate outsource build:'
                                def valid_args = [
                                    string(name: 'code_changelist', value: code_changelist),
                                ]
                                build(job: env.branch_name + '.outsourcevalidation', parameters: valid_args, propagate: false)
                            }
                        }

                        if (prebuild_job.result.toString() == 'SUCCESS' && branchfile.standard_jobs_settings.prebuild_triggers_package) {
                            def outsource_package_job = env.branch_name + '.outsource-package.start'
                            def outsource_package_args = [
                                string(name: 'code_changelist', value: code_changelist),
                            ]
                            build(job: outsource_package_job, wait: false, parameters: outsource_package_args, propagate: false)
                        }

                        //only use prebuild result as final result, change it when we are ready for validation job
                        currentBuild.result = prebuild_job.result.toString()

                        def slack_settings = branchfile.standard_jobs_settings?.slack_channel_prebuild
                        SlackMessageNew(currentBuild, slack_settings, project.short_name)

                        DownstreamErrorReporting(currentBuild)
                    } else {
                        echo('No new changelist to generate prebuild from, aborting.')
                    }
                }
            }
        }
    }
}
