"""
p4_dvcs.py
"""
import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.p4_utils import contains_error_string
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import p4, LOGGER
from elipy2.exceptions import ELIPYException
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command("p4_dvcs", short_help="Runs a P4 DVCS command on the given server and remote spec.")
@click.option("--cmd", required=True, type=click.Choice(["push", "fetch"]))
@click.option("--remote-spec", required=True)
@click.option("--port", required=True)
@click.option("--user", required=True)
@click.option("--client", required=True)
@click.option("--dry-run", is_flag=True)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(_, cmd, remote_spec, port, user, client, dry_run):
    """
    Performs a p4 push/fetch on the for the give server and remote spec.
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    perforce = p4.P4Utils(port=port, client=client, user=user)
    # check if remote spec exists:
    remote_specs = perforce.remotes(remote_spec)
    if remote_spec not in remote_specs:
        raise ELIPYException("Remote spec '{}' not found on server '{}'".format(remote_spec, port))

    dvcs_cmds = {"push": perforce.push, "fetch": perforce.fetch}

    if cmd not in dvcs_cmds.keys():
        raise ELIPYException("Command name not supported: {}".format(cmd))

    output = dvcs_cmds[cmd](
        remote_spec=remote_spec,
        dry_run=dry_run,
        verbose=True,
        quiet=False,
        extra_args=["-Ocfi"],
    )

    errors = [line for line in output if contains_error_string(line)]

    if errors:
        error_str = os.linesep.join(errors)
        LOGGER.error("Error when running DVCS command: %s", error_str)
        raise ELIPYException(
            "Something went wrong when running command, please see log output for details"
        )
