"""
move_location_frosty.py
"""
import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import core, filer_paths, LOGGER
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


# pylint: disable=line-too-long
@click.command(
    "move_location_frosty",
    short_help="Register Frosty-generated build in the configured metadata services.",
)
@click.option("--code-branch", help="Perforce code branch/stream name", required=True)
@click.option(
    "--code-changelist",
    help="Changelist number of code build used to verify data.",
    required=True,
)
@click.option("--data-branch", help="Perforce data branch/stream name", required=True)
@click.option("--data-changelist", help="Changelist number of data built.", required=True)
@click.option("--platform", help="Which platform the build is for", required=True)
@click.option("--source-location", help="Location to move the build from", default=None)
@click.option("--dest-location", help="Location to move the build to", required=True)
@click.option(
    "--package-type",
    help="Which package type the build is of (files, digital, patch",
    required=True,
)
@click.option("--region", help="Which region/SKU the build is (WW, NA, EU)", required=True)
@click.option(
    "--config",
    help="Which configuration the build is in (Final, Retail)",
    required=True,
)
# pylint: enable=line-too-long


@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    code_branch,
    code_changelist,
    data_branch,
    data_changelist,
    platform,
    source_location,
    dest_location,
    package_type,
    region,
    config,
):
    """
    Moves a frosty build from one location to another. Buildshare of both locations needs
    to be defined in the yaml file. Default location will be used as source if no location
    is specified for that.
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    source = filer_paths.get_frosty_build_path(
        data_branch=data_branch,
        data_changelist=data_changelist,
        code_branch=code_branch,
        code_changelist=code_changelist,
        platform=platform,
        package_type=package_type,
        region=region,
        config=config,
        location=source_location,
    )

    destination = filer_paths.get_frosty_build_path(
        data_branch=data_branch,
        data_changelist=data_changelist,
        code_branch=code_branch,
        code_changelist=code_changelist,
        platform=platform,
        package_type=package_type,
        region=region,
        config=config,
        location=dest_location,
    )

    if os.path.exists(destination):
        LOGGER.info(
            "Attempting to deploy to a path that already exists.\
        Possibly because a previous build succeeded in deploying before failing. Skipping copy."
        )
    else:
        core.robocopy(source, destination)
