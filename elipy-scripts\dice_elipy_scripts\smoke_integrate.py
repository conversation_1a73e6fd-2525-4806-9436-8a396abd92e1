"""
smoke_integrate.py

This is a very DICE-specific script, that performs a "Smoke integration" between our unverified
and verified data streams.
"""
import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import LOGGER, exceptions, filer_paths, core, p4, build_metadata_utils
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics
from elipy2.build_metadata import BuildMetadataManager


@click.command("smoke_integrate", short_help="Performs a Perforce integration.")
@click.option(
    "--perforce-server",
    required=True,
    help="Perforce server to perform the integration on.",
)
@click.option(
    "--perforce-client",
    required=True,
    help="Perforce client to perform the integration with.",
)
@click.option(
    "--data-changelist",
    required=True,
    help="Which data changelist on from-stream was verified.",
)
@click.option(
    "--code-changelist",
    required=True,
    help="Which code changelist has been used for smoke.",
)
@click.option("--code-branch", required=True, help="Which code branch the binaries come from.")
@click.option(
    "--from-stream",
    required=True,
    help="Which stream to integrate from. Must be child of to-stream",
)
@click.option("--perforce-user", required=True, help="Perforce user name.")
@click.option("--submit/--no-submit", default=True, help="Use --no-submit to do a dry-run.")
@click.option("--exclude-path", default=[], multiple=True, help="Don't integrate path")
@click.option(
    "--force-redeployment",
    default=False,
    help="If True, redeploy zip file to networkshare",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    perforce_server,
    perforce_client,
    data_changelist,
    code_changelist,
    code_branch,
    from_stream,
    perforce_user,
    submit,
    exclude_path,
    force_redeployment,
):
    """
    Integrates game-dev-unverifed into game-dev.
    Also notifies the configured metadata services that the new integration has taken place.
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    LOGGER.info(
        "Performing integration using client %s on server %s from stream %s to parent",
        perforce_server,
        perforce_client,
        from_stream,
    )
    perforce = p4.P4Utils(port=perforce_server, client=perforce_client, user=perforce_user)
    metadata_manager = build_metadata_utils.setup_metadata_manager()
    perforce.revert()
    perforce.integrate(
        mapping=from_stream,
        stream=True,
        quiet=False,
        reverse=False,
        to_revision=data_changelist,
    )
    for exclude in exclude_path:
        perforce.revert(path=exclude)

    resolved, _ = perforce.resolve()
    if not resolved:
        LOGGER.error("Unable to automatically resolve merge. Reverting and aborting")
        perforce.revert(quiet=True)
        raise exceptions.AutomaticP4MergeResolveException

    deploy_changes(
        perforce,
        metadata_manager,
        code_branch,
        code_changelist,
        data_changelist,
        from_stream,
        force_redeployment,
        submit,
    )


def get_commit_message(from_stream: str, data_changelist: str) -> str:
    """
    Generate and return the commit message
    """
    message = "Integrated verified data from {0}".format(from_stream)
    if data_changelist:
        message += " at CL {0}".format(data_changelist)

    message += "\nJenkins URL: " + os.environ.get("BUILD_URL", "None")

    return message


def deploy_changes(
    perforce: p4.P4Utils,
    metadata_manager: BuildMetadataManager,
    code_branch: str,
    code_changelist: str,
    data_changelist: str,
    from_stream: str,
    force_redeployment: bool,
    should_submit: bool,
):
    """
    Commit changes to perforce, deploy binaries, update the configured metadata services
    """
    if should_submit:
        source = filer_paths.get_code_build_root_path(code_branch, code_changelist)
        offsite_build_exists = os.path.exists(
            filer_paths.get_offsite_build(code_branch, code_changelist)
        )
        if force_redeployment or not offsite_build_exists:
            core.create_zip(source, filer_paths.get_offsite_build(code_branch, code_changelist))

        if not offsite_build_exists:
            perforce.submit(message=get_commit_message(from_stream, data_changelist))
            # Use tag_data_only_as_smoked instead of tag_code_build_as_smoked for OneTrunk workflow
            # This tags only data as smoked without affecting the code build's smoke status
            metadata_manager.tag_data_only_as_smoked(path=source, data_changelist=data_changelist)
    else:
        LOGGER.info("Skipping submission since --no-submit flag was passed.")
        LOGGER.info(
            "Would have tagged code build %s",
            filer_paths.get_code_build_root_path(code_branch, code_changelist),
        )
