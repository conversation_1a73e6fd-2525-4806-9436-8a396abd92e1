"""
test_prebuild.py

Unit testing for prebuild
"""
import unittest
import os
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import patch, MagicMock
from dice_elipy_scripts.prebuild import cli


@patch("dice_elipy_scripts.prebuild.core.clean_temp", MagicMock())
@patch("dice_elipy_scripts.prebuild.core.close_file_handles", MagicMock())
@patch("dice_elipy_scripts.prebuild.p4.P4Utils.revert", MagicMock())
@patch("dice_elipy_scripts.prebuild.p4.P4Utils.clean", MagicMock())
class TestPrebuild(unittest.TestCase):
    OPTION_CLEAN = "--clean"
    OPTION_CODE_CHANGELIST = "--code-changelist"
    OPTION_CONFIG = "--config"
    OPTION_DATA_DIRECTORY = "--data-directory"
    OPTION_DRY_RUN = "--dry-run"
    OPTION_EMAIL = "--email"
    OPTION_FRAMEWORK_ARGS = "--framework-args"
    OPTION_INPUT_PARAM_PATH = "--input-param-path"
    OPTION_LICENSEE = "--licensee"
    OPTION_P4_CLIENT_CODE = "--p4-client-code"
    OPTION_P4_PORT = "--p4-port"
    OPTION_P4_USER = "--p4-user"
    OPTION_PASSWORD = "--password"
    OPTION_P4_CLIENT_PREBUILD = "--p4-client-prebuild"
    OPTION_PLATFORM_PREBUILD = "--platform-prebuild"
    OPTION_PLATFORM_SLN = "--platform-sln"
    OPTION_SKIP_NINTENDO_SWITCH = "--skip-nintendo-switch"
    OPTION_SKIP_PLATFORM = "--skip-platform"
    OPTION_VALIDATION = "--validation"

    VALUE_CLEAN = "True"
    VALUE_CODE_CHANGELIST = "1234"
    VALUE_CONFIG = "some_config"
    VALUE_DATA_DIRECTORY = "data_directory"
    VALUE_EMAIL = "<EMAIL>"
    VALUE_FRAMEWORK_ARGS = "some_arg"
    VALUE_INPUT_PARAM_PATH = "input_param_path"
    VALUE_LICENSEE = "some_licensee"
    VALUE_P4_CLIENT_CODE = "p4_client_code"
    VALUE_P4_PORT = "p4_port"
    VALUE_P4_USER = "p4_name"
    VALUE_PASSWORD = "pass_word"
    VALUE_P4_CLIENT_PREBUILD = "p4_client_prebuild"
    VALUE_PLATFORM_PREBUILD_1 = "platform_prebuild"
    VALUE_PLATFORM_PREBUILD_2 = "platform_prebuild_2"
    VALUE_PLATFORM_SLN = "platform_sln"
    VALUE_SKIP_PLATFORM = "platform_skipped"

    BASE_ARGS = [
        OPTION_CODE_CHANGELIST,
        VALUE_CODE_CHANGELIST,
        OPTION_CONFIG,
        VALUE_CONFIG,
        OPTION_P4_CLIENT_CODE,
        VALUE_P4_CLIENT_CODE,
        OPTION_P4_PORT,
        VALUE_P4_PORT,
        OPTION_P4_USER,
        VALUE_P4_USER,
        OPTION_P4_CLIENT_PREBUILD,
        VALUE_P4_CLIENT_PREBUILD,
        OPTION_PLATFORM_SLN,
        VALUE_PLATFORM_SLN,
    ]

    SUBMISSION_ARGS = [
        OPTION_INPUT_PARAM_PATH,
        VALUE_INPUT_PARAM_PATH,
        OPTION_PLATFORM_PREBUILD,
        VALUE_PLATFORM_PREBUILD_1,
    ]

    VALIDATION_ARGS = [OPTION_VALIDATION]

    def setUp(self):
        self.patcher_delete_folder = patch("dice_elipy_scripts.prebuild.core.delete_folder")
        self.mock_delete_folder = self.patcher_delete_folder.start()

        self.patcher_set_datadir = patch("dice_elipy_scripts.prebuild.data.DataUtils.set_datadir")
        self.mock_set_datadir = self.patcher_set_datadir.start()

        self.patcher_set_licensee = patch("dice_elipy_scripts.prebuild.set_licensee")
        self.mock_set_licensee = self.patcher_set_licensee.start()

        self.patcher_codeutils = patch("dice_elipy_scripts.prebuild.code.CodeUtils")
        self.mock_codeutils = self.patcher_codeutils.start()
        self.mock_codeutils.return_value = MagicMock()

        self.patcher_run_gensln = patch("dice_elipy_scripts.prebuild.run_gensln")
        self.mock_run_gensln = self.patcher_run_gensln.start()

        self.patcher_pkgprebuilds = patch("dice_elipy_scripts.prebuild.fbenv_layer.pkgprebuilds")
        self.mock_pkgprebuilds = self.patcher_pkgprebuilds.start()

        self.patcher_p4_reconcile = patch("dice_elipy_scripts.prebuild.p4.P4Utils.reconcile")
        self.mock_p4_reconcile = self.patcher_p4_reconcile.start()

        self.patcher_p4_submit = patch("dice_elipy_scripts.prebuild.p4.P4Utils.submit")
        self.mock_p4_submit = self.patcher_p4_submit.start()

        self.patcher_core_run = patch("dice_elipy_scripts.prebuild.core.run")
        self.mock_core_run = self.patcher_core_run.start()

    def tearDown(self):
        self.patcher_delete_folder.stop()
        self.patcher_set_datadir.stop()
        self.patcher_set_licensee.stop()
        self.patcher_codeutils.stop()
        self.patcher_run_gensln.stop()
        self.patcher_pkgprebuilds.stop()
        self.patcher_p4_reconcile.stop()
        self.patcher_p4_submit.stop()
        self.patcher_core_run.stop()

    def test_submission_run(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASE_ARGS + self.SUBMISSION_ARGS)
        assert result.exit_code == 0

    def test_clean_delete_folder(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASE_ARGS + self.SUBMISSION_ARGS + [self.OPTION_CLEAN, self.VALUE_CLEAN],
        )
        assert self.mock_delete_folder.call_count == 2

    def test_delete_folder_without_clean(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASE_ARGS + self.SUBMISSION_ARGS)
        assert self.mock_delete_folder.call_count == 1

    def test_data_directory(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASE_ARGS
            + self.SUBMISSION_ARGS
            + [self.OPTION_DATA_DIRECTORY, self.VALUE_DATA_DIRECTORY],
        )
        self.mock_set_datadir.assert_called_once_with(self.VALUE_DATA_DIRECTORY)
        assert result.exit_code == 0

    def test_prebuild_gensln(self):
        self.mock_set_licensee.return_value = ["arg1"]
        runner = CliRunner()
        result = runner.invoke(cli, self.BASE_ARGS + self.SUBMISSION_ARGS)
        assert result.exit_code == 0
        self.mock_run_gensln.assert_called_once_with(
            password=None,
            user=None,
            domain_user=None,
            framework_args=["arg1"],
            builder=self.mock_codeutils.return_value,
        )

    def test_nintendo_switch(self):
        self.mock_set_licensee.return_value = ["arg1"]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASE_ARGS + self.SUBMISSION_ARGS + [self.OPTION_SKIP_NINTENDO_SWITCH],
        )
        assert result.exit_code == 0
        self.mock_run_gensln.assert_called_once_with(
            password=None,
            user=None,
            domain_user=None,
            framework_args=["arg1", "-G:frostbite.pipeline.NX.supported=false"],
            builder=self.mock_codeutils.return_value,
        )

    def test_skip_platform(self):
        self.mock_set_licensee.return_value = ["arg1"]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASE_ARGS
            + self.SUBMISSION_ARGS
            + [self.OPTION_SKIP_PLATFORM, self.VALUE_SKIP_PLATFORM],
        )
        assert result.exit_code == 0
        self.mock_run_gensln.assert_called_once_with(
            password=None,
            user=None,
            domain_user=None,
            framework_args=[
                "arg1",
                "-G:frostbite.pipeline." + self.VALUE_SKIP_PLATFORM + ".supported=false",
            ],
            builder=self.mock_codeutils.return_value,
        )

    def test_prebuild_fbenv_call(self):
        self.mock_set_licensee.return_value = ["arg1"]
        runner = CliRunner()
        result = runner.invoke(cli, self.BASE_ARGS + self.SUBMISSION_ARGS)
        self.mock_pkgprebuilds.assert_called_once_with(
            platform=(self.VALUE_PLATFORM_PREBUILD_1,),
            input_param_file=os.path.join("tnt_root", self.VALUE_INPUT_PARAM_PATH),
            framework_args=["arg1"],
        )

    def test_prebuild_fbenv_call_multiple_pkg_platforms(self):
        self.mock_set_licensee.return_value = ["arg1"]
        extra_args = [
            self.OPTION_PLATFORM_PREBUILD,
            self.VALUE_PLATFORM_PREBUILD_2,
        ]
        runner = CliRunner()
        result = runner.invoke(cli, self.BASE_ARGS + self.SUBMISSION_ARGS + extra_args)
        self.mock_pkgprebuilds.assert_called_once_with(
            platform=(self.VALUE_PLATFORM_PREBUILD_1, self.VALUE_PLATFORM_PREBUILD_2),
            input_param_file=os.path.join("tnt_root", self.VALUE_INPUT_PARAM_PATH),
            framework_args=["arg1"],
        )

    def test_prebuild_reconcile(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASE_ARGS + self.SUBMISSION_ARGS)
        self.mock_p4_reconcile.assert_called_once_with(
            path=os.path.join("tnt_root", "Prebuild", "..."),
            options=["a", "d", "e", "f"],
            quiet=False,
        )

    def test_prebuild_submit(self):
        submit_message = (
            "Prebuilds generated from CL " + self.VALUE_CODE_CHANGELIST + ".\nJenkins URL: None"
        )
        runner = CliRunner()
        result = runner.invoke(cli, self.BASE_ARGS + self.SUBMISSION_ARGS)
        self.mock_p4_submit.assert_called_once_with(message=submit_message)

    def test_prebuild_dry_run(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASE_ARGS + self.SUBMISSION_ARGS + [self.OPTION_DRY_RUN])
        assert not self.mock_p4_submit.called

    def test_validation_run(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASE_ARGS + self.VALIDATION_ARGS)
        assert result.exit_code == 0

    def test_validation_install(self):
        installer = os.path.join(
            "tnt_root",
            "Code",
            "DICE",
            "BattlefieldGame",
            "fbcli",
            "outsource-package-install.py",
        )
        runner = CliRunner()
        result = runner.invoke(cli, self.BASE_ARGS + self.VALIDATION_ARGS)
        self.mock_core_run.assert_called_once_with(["python", installer, "TnT/OutsourcePackages/"])

    def test_validation_buildsln(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASE_ARGS + self.VALIDATION_ARGS)
        self.mock_codeutils.assert_called_once_with(
            self.VALUE_PLATFORM_SLN,
            self.VALUE_CONFIG,
            monkey_build_label=self.VALUE_CODE_CHANGELIST,
            p4_port=self.VALUE_P4_PORT,
            p4_user=self.VALUE_P4_USER,
            p4_client=self.VALUE_P4_CLIENT_PREBUILD,
        )
        self.mock_codeutils.return_value.buildsln.assert_called_once()

    @patch("elipy2.LOGGER.error")
    def test_validation_exception(self, mock_error):
        self.mock_core_run.side_effect = Exception()
        runner = CliRunner()
        result = runner.invoke(cli, self.BASE_ARGS + self.VALIDATION_ARGS)
        mock_error.assert_called_once()
        assert result.exit_code == 1
