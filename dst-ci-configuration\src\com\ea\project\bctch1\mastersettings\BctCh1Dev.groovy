package com.ea.project.bctch1.mastersettings

import com.ea.project.bct.Bct
import com.ea.project.bctch1.BctCh1

class BctCh1Dev {
    static Class project = BctCh1
    static Map branches = [
        'bf-anticheat'                    : [
            code_folder: 'tasks',
            code_branch: 'anticheat',
            data_folder: 'tasks',
            data_branch: 'anticheat'
        ],
        'CH1-code-dev'                    : [
            code_folder: 'CH1',
            code_branch: 'CH1-code-dev',
            data_folder: 'CH1',
            data_branch: 'CH1-code-dev',
            koala_code : true
        ],
        'CH1-content-dev-sanitizers'      : [
            code_folder            : 'CH1',
            code_branch            : 'CH1-content-dev-sanitizers',
            data_folder            : 'CH1',
            data_branch            : 'CH1-content-dev-sanitizers',
            non_virtual_code_branch: 'CH1-content-dev',
            non_virtual_data_branch: 'CH1-content-dev',
        ],
        'CH1-content-dev-clean'           : [
            code_folder            : 'CH1',
            code_branch            : 'CH1-content-dev-clean',
            data_folder            : 'CH1',
            data_branch            : 'CH1-content-dev-clean',
            non_virtual_code_branch: 'CH1-content-dev',
            non_virtual_data_branch: 'CH1-content-dev',
        ],
        'CH1-content-dev'                 : [
            code_folder   : 'CH1',
            code_branch   : 'CH1-content-dev',
            data_folder   : 'CH1',
            data_branch   : 'CH1-content-dev',
            koala_content : true,
            koala_onetrunk: true,
        ],
        'CH1-content-dev-cache-losangeles': [
            code_folder            : 'CH1',
            code_branch            : 'CH1-content-dev-cache',
            data_folder            : 'CH1',
            data_branch            : 'CH1-content-dev-cache',
            non_virtual_code_branch: 'CH1-content-dev',
            non_virtual_data_branch: 'CH1-content-dev'
        ],
        'CH1-content-dev-disc-build'      : [
            code_folder            : 'CH1',
            code_branch            : 'CH1-content-dev',
            data_folder            : 'CH1',
            data_branch            : 'CH1-content-dev-disc-build',
            non_virtual_data_branch: 'CH1-content-dev',
        ],
        'CH1-content-dev-first-patch'     : [
            code_folder            : 'CH1',
            code_branch            : 'CH1-content-dev',
            data_folder            : 'CH1',
            data_branch            : 'CH1-content-dev-first-patch',
            non_virtual_data_branch: 'CH1-content-dev',
        ],
        'CH1-content-dev-metrics'         : [
            code_folder            : 'CH1',
            code_branch            : 'CH1-content-dev-metrics',
            data_folder            : 'CH1',
            data_branch            : 'CH1-content-dev-metrics',
            non_virtual_code_branch: 'CH1-content-dev',
            non_virtual_data_branch: 'CH1-content-dev'
        ],
        'CH1-content-dev-C1S2B1'          : [
            code_folder            : 'CH1',
            code_branch            : 'CH1-content-dev',
            data_folder            : 'CH1',
            data_branch            : 'CH1-content-dev-C1S2B1',
            non_virtual_data_branch: 'CH1-content-dev',
        ],
        'CH1-content-dev-TOP'             : [
            code_folder            : 'CH1',
            code_branch            : 'CH1-content-dev',
            data_folder            : 'CH1',
            data_branch            : 'CH1-content-dev-TOP',
            non_virtual_data_branch: 'CH1-content-dev',
        ],
        'CH1-SP-content-dev'              : [
            code_folder: 'CH1',
            code_branch: 'CH1-SP-content-dev',
            data_folder: 'CH1',
            data_branch: 'CH1-SP-content-dev',
        ],
        'CH1-SP-content-dev-disc-build'   : [
            code_folder            : 'CH1',
            code_branch            : 'CH1-SP-content-dev',
            data_folder            : 'CH1',
            data_branch            : 'CH1-SP-content-dev-disc-build',
            non_virtual_data_branch: 'CH1-SP-content-dev',
        ],
        'CH1-SP-content-dev-first-patch'  : [
            code_folder            : 'CH1',
            code_branch            : 'CH1-SP-content-dev',
            data_folder            : 'CH1',
            data_branch            : 'CH1-SP-content-dev-first-patch',
            non_virtual_data_branch: 'CH1-SP-content-dev',
        ],
        'CH1-to-trunk'                    : [
            code_folder         : 'CH1',
            code_branch         : 'CH1-to-trunk',
            data_folder         : 'CH1',
            data_branch         : 'CH1-to-trunk',
            job_label_poolbuild : 'poolbuild_CH1-to-trunk',
            lob_label_statebuild: 'statebuild_CH1-to-trunk',
        ],
        '2024_1_dev-bf-to-CH1'            : [
            code_folder         : 'CH1',
            code_branch         : '2024_1_dev-bf-to-CH1',
            data_folder         : 'CH1',
            data_branch         : '2024_1_dev-bf-to-CH1',
            job_label_poolbuild : 'poolbuild_eala_2024_1_dev-bf-to-CH1',
            job_label_statebuild: 'statebuild_eala_2024_1_dev-bf-to-CH1',
        ],
        'CH1-marketing-dev'               : [
            code_folder: 'CH1',
            code_branch: 'CH1-marketing-dev',
            data_folder: 'CH1',
            data_branch: 'CH1-marketing-dev',
        ],
        'CH1-playtest-gnt'                : [
            code_folder            : 'CH1',
            code_branch            : 'CH1-content-dev',
            data_folder            : 'CH1',
            data_branch            : 'CH1-playtest-gnt',
            non_virtual_data_branch: 'CH1-content-dev',
        ],
        'CH1-playtest-gnt-na'             : [
            code_folder            : 'CH1',
            code_branch            : 'CH1-content-dev',
            data_folder            : 'CH1',
            data_branch            : 'CH1-playtest-gnt-na',
            non_virtual_data_branch: 'CH1-content-dev',
        ],
        'CH1-playtest-san'                : [
            code_folder            : 'CH1',
            code_branch            : 'CH1-content-dev',
            data_folder            : 'CH1',
            data_branch            : 'CH1-playtest-san',
            non_virtual_data_branch: 'CH1-content-dev',
        ],
        'CH1-playtest-san-s2'             : [
            code_folder            : 'CH1',
            code_branch            : 'CH1-content-dev',
            data_folder            : 'CH1',
            data_branch            : 'CH1-playtest-san-s2',
            non_virtual_data_branch: 'CH1-content-dev',
        ],
        'CH1-playtest-sp'                 : [
            code_folder            : 'CH1',
            code_branch            : 'CH1-content-dev',
            data_folder            : 'CH1',
            data_branch            : 'CH1-playtest-sp',
            non_virtual_data_branch: 'CH1-content-dev',
        ],
        'CH1-playtest-stage'              : [
            code_folder: 'CH1',
            code_branch: 'CH1-playtest-stage',
            data_folder: 'CH1',
            data_branch: 'CH1-playtest-stage',
        ],
        'task13'                          : [
            code_folder: 'tasks',
            code_branch: 'task13',
            data_folder: 'tasks',
            data_branch: 'task13',
        ],
        'task4'                           : [
            code_folder: 'tasks',
            code_branch: 'task4',
            data_folder: 'tasks',
            data_branch: 'task4',
        ],
        'task6'                           : [
            code_folder: 'tasks',
            code_branch: 'task6',
            data_folder: 'tasks',
            data_branch: 'task6',
        ],
        'task8'                           : [
            code_folder: 'tasks',
            code_branch: 'task8',
            data_folder: 'tasks',
            data_branch: 'task8',
        ],
        'task2'                           : [
            code_folder: 'tasks',
            code_branch: 'task2',
            data_folder: 'tasks',
            data_branch: 'task2',
        ],
    ]
    static Map preflight_branches = [:]
    static Map autotest_branches = [:]
    static Map integrate_branches = [
        'CH1-code-dev_to_CH1-content-dev'                      : [
            asset                       : 'DevLevels',
            elipy_call                  : project.elipy_call,
            elipy_install_call          : project.elipy_install_call,
            extra_args                  : '',
            frostbite_licensee          : project.frostbite_licensee,
            integrate_mapping           : 'BF_[CH1-code-dev]_to_[CH1-content-dev]_IgnoreData',
            job_label                   : 'CH1-code-dev_to_CH1-content-dev',
            integrate_upgrade_one_stream: true,
            local_upgrade               : true,
            integration_reference_job   : 'CH1-code-dev.data.start',
            slack_channel               : '#bct-build-notify',
            source_project              : project,
            source_folder               : 'CH1',
            source_branch               : 'CH1-code-dev',
            timeout_hours               : 6,
            target_project              : project,
            target_folder               : 'CH1',
            target_branch               : 'CH1-content-dev',
            verified_integration        : true,
            workspace_root              : project.workspace_root,
            freestyle_job_trigger_matrix: [],
        ],
        'CH1-code-dev-to-task5'                                : [
            asset                       : 'DevLevels',
            elipy_call                  : project.elipy_call,
            elipy_install_call          : project.elipy_install_call,
            extra_args                  : '',
            frostbite_licensee          : project.frostbite_licensee,
            integrate_mapping           : 'BF_[CH1-code-dev]_to_[task5]',
            integrate_upgrade_one_stream: true,
            skip_upgrade                : true,
            integration_reference_job   : 'CH1-code-dev.data.start',
            job_label                   : 'CH1-code-dev_to_task5',
            no_submit                   : true,
            slack_channel               : '#bct-build-notify',
            source_project              : project,
            source_folder               : 'CH1',
            source_branch               : 'CH1-code-dev',
            target_project              : project,
            target_folder               : 'tasks',
            target_branch               : 'task5',
            timeout_hours               : 6,
            trigger_type_integrate      : 'cron',
            trigger_string_integrate    : 'TZ=Europe/Stockholm \n H 4 * * *', // Once a day at 4am, stockholm time
            verified_integration        : true,
            workspace_root              : project.workspace_root,
        ],
        'CH1-content-dev-to-task6'                             : [
            project                     : project,
            source_folder               : 'CH1',
            source_branch               : 'CH1-content-dev',
            target_folder               : 'tasks',
            target_branch               : 'task6',
            code                        : true,
            data                        : false,
            parent_to_child             : true,
            data_only_source_branch     : false,
            get_integration_info        : true,
            elipy_call                  : project.elipy_call,
            elipy_install_call          : project.elipy_install_call,
            job_label_statebuild        : 'CH1-content-dev_to_task6',
            no_submit                   : true,
            workspace_root              : project.workspace_root,
            verified_integration        : true,
            slack_channel               : '#CH1-content-dev-integrates-task6',
            trigger_type_integrate      : 'none',
            integration_reference_job   : 'CH1-content-dev.autotest-to-integration.code',
            slack_always_notify         : true,
            freestyle_job_trigger_matrix: [],
        ],
        'CH1-content-dev-to-task8'                             : [
            project                     : project,
            source_folder               : 'CH1',
            source_branch               : 'CH1-content-dev',
            target_folder               : 'tasks',
            target_branch               : 'task8',
            code                        : true,
            data                        : false,
            parent_to_child             : true,
            data_only_source_branch     : false,
            get_integration_info        : true,
            elipy_call                  : project.elipy_call,
            elipy_install_call          : project.elipy_install_call,
            job_label_statebuild        : 'CH1-content-dev_to_task8',
            no_submit                   : true,
            workspace_root              : project.workspace_root,
            verified_integration        : true,
            slack_channel               : '#CH1-content-dev-integrates-task8',
            trigger_type_integrate      : 'none',
            integration_reference_job   : 'CH1-content-dev.autotest-to-integration.code',
            slack_always_notify         : true,
            freestyle_job_trigger_matrix: [],
        ],
        'CH1-content-dev-to-CH1-code-dev-branch-guardian'      : [
            asset                       : 'DevLevels',
            branch_guardian             : true,
            disable_build               : false,
            elipy_call                  : project.elipy_call,
            elipy_install_call          : project.elipy_install_call,
            extra_args                  : '',
            freestyle_job_trigger_matrix: [],
            frostbite_licensee          : project.frostbite_licensee,
            integrate_mapping           : 'BF_[CH1-content-dev]_to_[CH1-code-dev]_IgnoreData',
            integrate_upgrade_one_stream: true,
            job_label                   : 'CH1-content-dev-to-CH1-code-dev-branch-guardian',
            no_submit                   : false,
            data_upgrade                : true,
            preview_project             : project,
            preview_folder              : 'CH1',
            preview_branch              : 'CH1-content-dev',
            slack_channel               : '#bct-build-notify',
            source_project              : project,
            source_folder               : 'CH1',
            source_branch               : 'CH1-content-dev',
            target_project              : project,
            target_folder               : 'CH1',
            target_branch               : 'CH1-code-dev',
            timeout_hours               : 8,
            use_preview_dotnet_version  : false,
            verified_integration        : true,
            workspace_root              : project.workspace_root,
        ],
        'CH1-SP-content-dev-to-CH1-content-dev'                : [
            asset                       : 'DevLevels',
            branch_guardian             : true,
            disable_build               : false,
            elipy_call                  : project.elipy_call,
            elipy_install_call          : project.elipy_install_call,
            freestyle_job_trigger_matrix: [],
            frostbite_licensee          : project.frostbite_licensee,
            integrate_mapping           : 'BF_[CH1-content-dev]_to_[CH1-SP-content-dev]_IgnoreData',
            integrate_upgrade_one_stream: true,
            integration_reference_job   : 'CH1-SP-content-dev.data.start',
            integrate_reverse           : true,
            job_label                   : 'CH1-SP-content-dev-to-CH1-content-dev',
            no_submit                   : true,
            data_upgrade                : true,
            preview_project             : project,
            preview_folder              : 'CH1',
            preview_branch              : 'CH1-SP-content-dev',
            slack_channel               : '#bct-build-notify',
            source_project              : project,
            source_folder               : 'CH1',
            source_branch               : 'CH1-SP-content-dev',
            target_project              : project,
            target_folder               : 'CH1',
            target_branch               : 'CH1-content-dev',
            timeout_hours               : 8,
            verified_integration        : true,
            workspace_root              : project.workspace_root,
        ],
        'CH1-content-dev-to-CH1-SP-content-dev-branch-guardian': [
            copy_integrate_compile      : true,
            copy_mapping                : 'BF_[CH1-content-dev]_to_[CH1-SP-content-dev]_Copy',
            elipy_call                  : project.elipy_call,
            elipy_install_call          : project.elipy_install_call,
            freestyle_job_trigger_matrix: [
                [
                    upstream_job  : 'CH1-content-dev.code.copy-integrate-to.CH1-SP-content-dev',
                    downstream_job: 'CH1-content-dev.code.start',
                    args          : []
                ],
            ],
            frostbite_licensee          : project.frostbite_licensee,
            integrate_mapping           : 'BF_[CH1-content-dev]_to_[CH1-SP-content-dev]_Integrate',
            job_label_statebuild        : 'CH1-content-dev-to-CH1-SP-content-dev-branch-guardian',
            no_submit                   : false,
            override_branch_guardian    : true,
            slack_channel               : [
                channels                  : ['#bct-build-notify', '#bf-ch1-sp-notify'],
                skip_for_multiple_failures: false,
            ],
            source_project              : project,
            source_branch               : 'CH1-content-dev',
            source_folder               : 'CH1',
            target_branch               : 'CH1-SP-content-dev',
            target_folder               : 'CH1',
            target_project              : project,
            timeout_hours               : 8,
            trigger_type_integrate      : 'scm',
            verified_integration        : true,
            workspace_root              : project.workspace_root,
        ],
        'CH1-to-trunk-to-trunk-content-dev-branch-guardian'    : [
            asset                       : 'DevLevels',
            branch_guardian             : true,
            disable_build               : false,
            elipy_call                  : project.elipy_call,
            elipy_install_call          : project.elipy_install_call,
            extra_args                  : '',
            freestyle_job_trigger_matrix: [],
            frostbite_licensee          : project.frostbite_licensee,
            get_integration_info        : true,
            integrate_mapping           : 'BF_[CH1-to-trunk]_to_[trunk-content-dev]_IgnoreData',
            integrate_upgrade_one_stream: true,
            integration_reference_job   : 'CH1-to-trunk.autotest-to-integration.code',
            job_label                   : 'CH1-to-trunk-to-trunk-content-dev-branch-guardian',
            manual_trigger              : true,
            no_submit                   : false,
            preview_project             : project,
            preview_folder              : 'CH1',
            preview_branch              : 'CH1-to-trunk',
            slack_channel               : '#bct-build-notify',
            source_project              : project,
            source_folder               : 'CH1',
            source_branch               : 'CH1-to-trunk',
            target_project              : Bct,
            target_folder               : 'mainline',
            target_branch               : 'trunk-content-dev',
            timeout_hours               : 8,
            use_preview_dotnet_version  : false,
            verified_integration        : true,
            workspace_root              : project.workspace_root,
        ],
        'trunk-content-dev_to_CH1-to-trunk'                    : [
            asset                       : 'DevLevels',
            elipy_call                  : project.elipy_call,
            elipy_install_call          : project.elipy_install_call,
            extra_args                  : '',
            frostbite_licensee          : project.frostbite_licensee,
            integrate_mapping           : 'BF_[trunk-content-dev]_to_[CH1-to-trunk]_IgnoreData',
            job_label                   : 'trunk-content-dev_to_CH1-to-trunk-guardian',
            integrate_upgrade_one_stream: true,
            get_integration_info        : true,
            integration_reference_job   : 'trunk-content-dev.autotest-to-integration.code',
            slack_channel               : '#bct-build-notify',
            source_project              : Bct,
            source_folder               : 'mainline',
            source_branch               : 'trunk-content-dev',
            timeout_hours               : 6,
            target_project              : project,
            target_folder               : 'CH1',
            target_branch               : 'CH1-to-trunk',
            verified_integration        : true,
            workspace_root              : project.workspace_root,
            freestyle_job_trigger_matrix: [],
            branch_guardian             : true,
            no_submit                   : true,
        ],
        'CH1-content-dev-to-CH1-to-trunk-branch-guardian'      : [
            asset                       : 'DevLevels',
            branch_guardian             : true,
            disable_build               : false,
            elipy_call                  : project.elipy_call,
            elipy_install_call          : project.elipy_install_call,
            extra_args                  : '',
            freestyle_job_trigger_matrix: [],
            frostbite_licensee          : project.frostbite_licensee,
            get_integration_info        : true,
            integrate_mapping           : 'BF_[CH1-content-dev]_to_[CH1-to-trunk]_OnlyLicensee',
            integrate_upgrade_one_stream: true,
            integration_reference_job   : 'CH1-content-dev.autotest-to-integration.code',
            job_label                   : 'CH1-content-dev-to-CH1-to-trunk-branch-guardian',
            manual_trigger              : true,
            no_submit                   : false,
            preview_project             : project,
            preview_folder              : 'CH1',
            preview_branch              : 'CH1-content-dev',
            slack_channel               : '#bct-build-notify',
            source_project              : project,
            source_folder               : 'CH1',
            source_branch               : 'CH1-content-dev',
            target_project              : project,
            target_folder               : 'CH1',
            target_branch               : 'CH1-to-trunk',
            timeout_hours               : 8,
            use_preview_dotnet_version  : false,
            verified_integration        : true,
            workspace_root              : project.workspace_root,
        ],
        'CH1-content-dev-to-task2'                             : [
            project                     : project,
            source_folder               : 'CH1',
            source_branch               : 'CH1-content-dev',
            target_folder               : 'tasks',
            target_branch               : 'task2',
            code                        : true,
            data                        : false,
            parent_to_child             : true,
            data_only_source_branch     : false,
            elipy_call                  : project.elipy_call,
            elipy_install_call          : project.elipy_install_call,
            workspace_root              : project.workspace_root,
            verified_integration        : false,
            trigger_type_integrate      : 'cron',
            trigger_string_integrate    : 'H 0 * * *',
            slack_channel               : '#CH1-content-dev-integrates-task2',
            slack_always_notify         : true,
            freestyle_job_trigger_matrix: [],
        ],
    ]
    static Map copy_branches = [
        'CH1-content-dev_to_CH1_stage'         : [
            source_folder               : 'CH1', source_branch: 'CH1-content-dev',
            target_folder               : 'CH1', target_branch: 'CH1-stage',
            code                        : true, data: false, parent_to_child: true,
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call,
            workspace_root              : project.workspace_root, slack_channel: '#bct-build-notify',
            trigger_type_copy           : 'stop', trigger_string_copy: 'H * * * *',
            job_label_statebuild        : 'statebuild',
            freestyle_job_trigger_matrix: [],
        ],
        'CH1-SP-content-dev_to_CH1-content-dev': [
            source_folder               : 'CH1',
            source_branch               : 'CH1-SP-content-dev',
            target_folder               : 'CH1',
            target_branch               : 'CH1-content-dev',
            code                        : true,
            data                        : false,
            parent_to_child             : false,
            elipy_call                  : project.elipy_call,
            elipy_install_call          : project.elipy_install_call,
            workspace_root              : project.workspace_root,
            slack_channel               : '#bct-build-notify',
            trigger_string_copy         : 'H * * * *',
            job_label_statebuild        : 'statebuild',
            freestyle_job_trigger_matrix: [],
        ],
        'CH1-content-dev_to_CH1-SP-content-dev': [
            source_folder               : 'CH1',
            source_branch               : 'CH1-content-dev',
            target_folder               : 'CH1',
            target_branch               : 'CH1-SP-content-dev',
            code                        : true,
            data                        : false,
            parent_to_child             : true,
            elipy_call                  : project.elipy_call,
            elipy_install_call          : project.elipy_install_call,
            workspace_root              : project.workspace_root,
            slack_channel               : '#bct-build-notify',
            trigger_string_copy         : 'H * * * *',
            job_label_statebuild        : 'statebuild',
            freestyle_job_trigger_matrix: [],
        ],
        'CH1-stage_to_CH1-release'             : [
            source_folder               : 'CH1', source_branch: 'CH1-stage',
            target_folder               : 'CH1', target_branch: 'CH1-release',
            code                        : true, data: false, parent_to_child: true,
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call,
            workspace_root              : project.workspace_root, slack_channel: '#bct-build-notify',
            trigger_type_copy           : 'stop', trigger_string_copy: 'H * * * *',
            job_label_statebuild        : 'statebuild',
            freestyle_job_trigger_matrix: [],
        ],
        'CH1-content-dev_to_task4'             : [
            source_folder               : 'CH1', source_branch: 'CH1-content-dev',
            target_folder               : 'tasks', target_branch: 'task4',
            code                        : true, data: false, parent_to_child: true,
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call,
            workspace_root              : project.workspace_root, slack_channel: '#CH1-content-dev-integrates-task4',
            trigger_type_copy           : 'stop', trigger_string_copy: 'H * * * *',
            job_label_statebuild        : 'statebuild',
            freestyle_job_trigger_matrix: [],
        ],
        'CH1-content-dev_to_task6'             : [
            source_folder               : 'CH1', source_branch: 'CH1-content-dev',
            target_folder               : 'tasks', target_branch: 'task6',
            code                        : true, data: false, parent_to_child: true,
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call,
            workspace_root              : project.workspace_root, slack_channel: '#CH1-content-dev-integrates-task6',
            trigger_type_copy           : 'stop', trigger_string_copy: 'H * * * *',
            job_label_statebuild        : 'statebuild',
            freestyle_job_trigger_matrix: [],
        ],
        'CH1-content-dev_to_task8'             : [
            source_folder               : 'CH1', source_branch: 'CH1-content-dev',
            target_folder               : 'tasks', target_branch: 'task8',
            code                        : true, data: false, parent_to_child: true,
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call,
            workspace_root              : project.workspace_root, slack_channel: '#CH1-content-dev-integrates-task8',
            trigger_type_copy           : 'stop', trigger_string_copy: 'H * * * *',
            job_label_statebuild        : 'statebuild',
            freestyle_job_trigger_matrix: [],
        ],
        'CH1-content-dev_to-task2'             : [
            source_folder               : 'CH1', source_branch: 'CH1-content-dev',
            target_folder               : 'tasks', target_branch: 'task2',
            code                        : true, data: false, parent_to_child: true,
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call,
            workspace_root              : project.workspace_root, slack_channel: '#CH1-content-dev-integrates-task2',
            trigger_type_copy           : 'stop', trigger_string_copy: 'H * * * *',
            job_label_statebuild        : 'statebuild',
            freestyle_job_trigger_matrix: [],
        ],
    ]
    static Map feature_integrations = [:]
    static Map maintenance_branch = [
        'CH1-content-dev': [
            code_folder                       : 'CH1', code_branch: 'ch1-content-dev',
            data_folder                       : 'CH1', data_branch: 'ch1-content-dev',
            include_vault                     : true,
            include_register_release_candidate: true,
        ],
    ]
    static List dashboard_list = []
    static Map dvcs_configs = [:]
    static List code_downstream_matrix = []
    static Map MAINTENANCE_SETTINGS = [:]
}
