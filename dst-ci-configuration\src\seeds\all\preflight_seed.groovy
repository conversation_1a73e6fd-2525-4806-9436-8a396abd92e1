package all

import com.ea.lib.LibCommonCps
import com.ea.lib.LibJobDsl
import com.ea.lib.LibScm
import com.ea.lib.LibSlack
import com.ea.lib.jobs.LibPreflight
import com.ea.lib.jobs.LibRemoteTriggers
import com.ea.project.GetBranchFile
import com.ea.project.GetMasterFile

GetMasterFile.get_masterfile(BUILD_URL).each { masterSettings ->
    def branches = masterSettings.preflight_branches

    branches.each { current_branch, info ->
        out.println("   Processing branch: $current_branch")
        def project = masterSettings.project
        if (com.ea.project.all.All.isAssignableFrom(project)) {
            project = info.project
        }
        def branchfile = GetBranchFile.get_branchfile(project.name, current_branch)
        def code_preflight_matrix = branchfile.code_preflight_matrix
        def data_preflight_matrix = branchfile.data_preflight_matrix
        def general_settings = branchfile.general_settings
        def standard_jobs_settings = branchfile.standard_jobs_settings
        def preflight_settings = branchfile.preflight_settings
        def is_cloud = project.is_cloud ?: false
        def freestyle_jobs = []

        def branch_info = info + general_settings + standard_jobs_settings + preflight_settings + [branch_name: current_branch, project: project]
        String pipelineLogCodeBranch = branch_info.non_virtual_code_branch ?: branch_info.code_branch

        def is_code_statebuild = branch_info.statebuild_codepreflight != null ? branch_info.statebuild_codepreflight : true
        def is_data_statebuild = branch_info.statebuild_datapreflight != null ? branch_info.statebuild_datapreflight : true
        def is_maintenance_statebuild = is_code_statebuild || is_data_statebuild

        if (!code_preflight_matrix.isEmpty()) {
            def code_preflight_start = pipelineJob(current_branch + '.codepreflight.start') {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/all/code_preflight_scheduler.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibPreflight.code_preflight_start(code_preflight_start, project, branch_info)
        }

        if (!data_preflight_matrix.isEmpty()) {
            def data_preflight_start = pipelineJob(current_branch + '.datapreflight.start') {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/all/data_preflight_scheduler.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibPreflight.data_preflight_start(data_preflight_start, project, branch_info)
        }

        for (platform in code_preflight_matrix) {
            for (config in platform.configs) {
                // code preflight
                branch_info = branch_info + [platform: platform.name, config: config, current_branch: current_branch, force_on_prem: platform.force_on_prem]
                branch_info['nomaster_platform'] = platform.nomaster_platform?.toBoolean() ?: false
                boolean runOnAzure = branch_info.run_on_azure && !platform.force_on_prem
                String job_name = current_branch + '.code.preflight.' + platform.name + (branch_info['nomaster_platform'] ? '.nomaster.' : '.') + config
                def code_preflight_job = job(job_name) {}
                freestyle_jobs.add(code_preflight_job)
                LibPreflight.code_preflight_job(code_preflight_job, project, branch_info)
                if (!project.icepick_settings.isEmpty() && branch_info.use_icepick_test == true && platform.sync_code_and_data == true) {
                    // only when we have icepick_setting and turn on use_icepick_test and turn on sync_code_and_data on platform, we sync both code and data
                    LibScm.sync_code_and_data(code_preflight_job, project, branch_info, '${data_changelist}', '${code_changelist}', '', '', ['preflight'])
                } else {
                    LibScm.sync_code(code_preflight_job, project, branch_info, '${code_changelist}', ['preflight'])
                }
                LibJobDsl.kill_processes(code_preflight_job, branch_info, runOnAzure)

                if (is_cloud) {
                    LibJobDsl.initialAWSP4revert(code_preflight_job, project, branch_info)
                } else {
                    LibJobDsl.initialP4revert(code_preflight_job, project, branch_info, true, false, is_code_statebuild, null, null, runOnAzure, ['preflight'])
                    LibJobDsl.archive_non_build_logs(code_preflight_job, branch_info)
                }

                LibJobDsl.addVaultSecrets(code_preflight_job, branch_info)
                LibJobDsl.postclean_silverback(code_preflight_job, project, branch_info, null, null, runOnAzure, ['preflight'])
                // extra step for AWS codeprflight only
                if (is_cloud) {
                    LibJobDsl.aws_extra_steps(code_preflight_job)
                }

                if (branch_info.enable_hybrid_agents) {
                    def cloudCodePreflightJob = job("${current_branch}.code.preflight.${platform.name}.${config}.cloud") {}
                    freestyle_jobs.add(cloudCodePreflightJob)
                    String extraArgs = platform.name == 'tool' ? '--heartbeat-timeout 600000' : ''
                    LibPreflight.code_preflight_job(cloudCodePreflightJob, project, branch_info + [platform                : platform.name, config: config, current_branch: current_branch, force_on_prem: false, run_on_azure: true, p4_code_server: 'ssl:euwest-p4edge-fb.p4one.ea.com:2001', p4_code_creds: 'euwest-p4edge-fb.p4one.ea.com',
                                                                                                   extra_codepreflight_args: "--framework-args -D:ondemandp4proxymapfile=${branch_info.azure_workspace_root}\\tnt\\build\\framework\\data\\P4ProtocolOptimalProxyMap.xml --framework-args -D:eaconfig.optimization.ltcg=off ${extraArgs}",])
                    if (!project.icepick_settings.isEmpty() && branch_info.use_icepick_test == true && platform.sync_code_and_data == true) {
                        LibScm.sync_code_and_data(cloudCodePreflightJob, project, branch_info + [run_on_azure: true, p4_code_server: 'ssl:euwest-p4edge-fb.p4one.ea.com:2001', p4_code_creds: 'euwest-p4edge-fb.p4one.ea.com'], '${data_changelist}', '${code_changelist}', '', '', ['preflight'])
                    } else {
                        LibScm.sync_code(cloudCodePreflightJob, project, branch_info + [run_on_azure: true, p4_code_server: 'ssl:euwest-p4edge-fb.p4one.ea.com:2001', p4_code_creds: 'euwest-p4edge-fb.p4one.ea.com'], '${code_changelist}', ['preflight'])
                    }
                    LibJobDsl.kill_processes(cloudCodePreflightJob, branch_info + [run_on_azure: true, p4_code_server: 'ssl:euwest-p4edge-fb.p4one.ea.com:2001', p4_code_creds: 'euwest-p4edge-fb.p4one.ea.com'], true)
                    LibJobDsl.initialP4revert(cloudCodePreflightJob, project, branch_info + [run_on_azure: true, p4_code_server: 'ssl:euwest-p4edge-fb.p4one.ea.com:2001', p4_code_creds: 'euwest-p4edge-fb.p4one.ea.com'], true, false, is_code_statebuild, null, null, true, ['preflight'])
                    LibJobDsl.addVaultSecrets(cloudCodePreflightJob, branch_info)
                    LibJobDsl.archive_non_build_logs(cloudCodePreflightJob, branch_info)
                    LibJobDsl.postclean_silverback(cloudCodePreflightJob, project, branch_info + [run_on_azure: true, p4_code_server: 'ssl:euwest-p4edge-fb.p4one.ea.com:2001', p4_code_creds: 'euwest-p4edge-fb.p4one.ea.com'], null, null, true, ['preflight'])
                }

                // post-code-preflight
                def code_post_preflight_job = job('maintenance.' + current_branch + '.code.postpreflight.' + platform.name) {}
                freestyle_jobs.add(code_post_preflight_job)
                LibPreflight.post_preflight_job(code_post_preflight_job, project, branch_info)
                LibScm.sync_code_and_data(code_post_preflight_job, project, branch_info, '${data_changelist}', '${code_changelist}')
                LibJobDsl.with {
                    initialP4revert(code_post_preflight_job, project, branch_info, true, true, is_maintenance_statebuild)
                    addVaultSecrets(code_post_preflight_job, branch_info)
                    archive_pipelinelog(code_post_preflight_job, '*', pipelineLogCodeBranch)
                    archive_non_build_logs(code_post_preflight_job, branch_info)
                    postclean_silverback(code_post_preflight_job, project, branch_info)
                    kill_processes(code_post_preflight_job, branch_info)
                }
                // extra step for AWS codeprflight only
                if (is_cloud) {
                    LibJobDsl.aws_extra_steps(code_post_preflight_job)
                }
                def slack_always_notify = branch_info.slack_always_notify ?: false
                branch_info?.slack_channel_preflight?.channels.each { channel ->
                    LibSlack.slack_default(code_post_preflight_job, channel, project.short_name, slack_always_notify)
                }
            }
        }

        // Data preflights
        for (preflight_set in data_preflight_matrix) {
            // data preflight
            def data_preflight_job = job(current_branch + '.' + branch_info.dataset + '.preflight.' + preflight_set.name) {}
            freestyle_jobs.add(data_preflight_job)
            LibPreflight.data_preflight_job(data_preflight_job, project, branch_info, preflight_set)
            LibScm.sync_code_and_data(data_preflight_job, project, branch_info, '${data_changelist}', '${code_changelist}', preflight_set.p4_data_creds, preflight_set.p4_code_creds)
            LibJobDsl.with {
                archive_pipelinelog(data_preflight_job, branch_info.dataset, pipelineLogCodeBranch)
                kill_processes(data_preflight_job, branch_info)
                initialP4revert(data_preflight_job, project, branch_info, true, true, is_data_statebuild, preflight_set.p4_code_server, preflight_set.p4_data_server)
                addVaultSecrets(data_preflight_job, branch_info)
                archive_non_build_logs(data_preflight_job, branch_info)
                postclean_silverback(data_preflight_job, project, branch_info, preflight_set.p4_code_server, preflight_set.p4_data_server)
            }
            // post-preflight
            def data_post_preflight_job = job('maintenance.' + current_branch + '.data.postpreflight.' + preflight_set.name) {}
            freestyle_jobs.add(data_post_preflight_job)
            LibPreflight.post_preflight_job(data_post_preflight_job, project, branch_info)
            LibScm.sync_code_and_data(data_post_preflight_job, project, branch_info, '${data_changelist}', '${code_changelist}', preflight_set.p4_data_creds, preflight_set.p4_code_creds)
            LibJobDsl.with {
                initialP4revert(data_post_preflight_job, project, branch_info, true, true, is_maintenance_statebuild, preflight_set.p4_code_server, preflight_set.p4_data_server)
                addVaultSecrets(data_post_preflight_job, branch_info)
                archive_pipelinelog(data_post_preflight_job, '*', pipelineLogCodeBranch)
                archive_non_build_logs(data_post_preflight_job, branch_info)
                postclean_silverback(data_post_preflight_job, project, branch_info, preflight_set.p4_code_server, preflight_set.p4_data_server)
                kill_processes(data_post_preflight_job, branch_info)
            }
            def slack_always_notify = branch_info.slack_always_notify ?: false
            branch_info?.slack_channel_preflight?.channels.each { channel ->
                LibSlack.slack_default(data_post_preflight_job, channel, project.short_name, slack_always_notify)
            }
        }

        /*
        Preflight maintenance jobs
        */

        // Start job for pre-preflight maintenance jobs.
        if (branch_info.pre_preflight == true) {
            // we do not enable code prepreflight
            // def code_pre_preflight_start = pipelineJob('maintenance.' + current_branch + '.code.prepreflight.start') {
            // 	definition {
            // 		cps {
            // 			script(readFileFromWorkspace('src/scripts/schedulers/all/pre_preflight_scheduler.groovy'))
            // 			sandbox(true)
            // 		}
            // 	}
            // }
            // LibPreflight.pre_preflight_start(code_pre_preflight_start, project, branch_info,'code')

            // def code_pre_preflight_job = job('maintenance.' + current_branch + '.code.prepreflight') {}
            // freestyle_jobs.add(code_pre_preflight_job)
            // LibPreflight.pre_preflight_job(code_pre_preflight_job, project, branch_info)
            // LibScm.sync_code_and_data(code_pre_preflight_job, project, branch_info, '${data_changelist}', '${code_changelist}')
            // LibJobDsl.initialP4revert(code_pre_preflight_job, project, branch_info, true, true, is_maintenance_statebuild)
            // LibJobDsl.archive_pipelinelog(code_pre_preflight_job, '*', pipelineLogCodeBranch)
            // LibJobDsl.archive_non_build_logs(code_pre_preflight_job)
            // LibJobDsl.postclean_silverback(code_pre_preflight_job, project, branch_info)
            // LibJobDsl.kill_processes(code_pre_preflight_job, branch_info)

            // data prepreflight start
            // change from one job for all to several jobs per each platform
            for (preflight_set in data_preflight_matrix) {
                def data_pre_preflight_start = pipelineJob('maintenance.' + current_branch + '.data.prepreflight.' + preflight_set.name + '.start') {
                    definition {
                        cps {
                            script(readFileFromWorkspace('src/scripts/schedulers/all/data_pre_preflight_scheduler.groovy'))
                            sandbox(true)
                        }
                    }
                }
                LibPreflight.pre_preflight_start(data_pre_preflight_start, project, branch_info, preflight_set.name)

                def data_pre_preflight_job = job('maintenance.' + current_branch + '.data.prepreflight.' + preflight_set.name) {}
                freestyle_jobs.add(data_pre_preflight_job)
                LibPreflight.pre_preflight_job(data_pre_preflight_job, project, branchfile, masterSettings, preflight_set.platform, branch_info.branch_name)
                LibScm.sync_code_and_data(data_pre_preflight_job, project, branch_info, '${data_changelist}', '${code_changelist}', preflight_set.p4_data_creds, preflight_set.p4_code_creds)
                LibJobDsl.with {
                    initialP4revert(data_pre_preflight_job, project, branch_info, true, true, is_maintenance_statebuild, preflight_set.p4_code_server, preflight_set.p4_data_server)
                    addVaultSecrets(data_pre_preflight_job, branch_info)
                    archive_pipelinelog(data_pre_preflight_job, '*', pipelineLogCodeBranch)
                    archive_non_build_logs(data_pre_preflight_job, branch_info)
                    postclean_silverback(data_pre_preflight_job, project, branch_info, preflight_set.p4_code_server, preflight_set.p4_data_server)
                    kill_processes(data_pre_preflight_job, branch_info)
                }
            }
        }

        def code_lastknowngood = job(current_branch + '.code.lastknowngood') {}
        freestyle_jobs.add(code_lastknowngood)
        LibRemoteTriggers.code_lastknowngood_get(code_lastknowngood, branch_info)
        branch_info?.slack_channel_preflight?.channels.each { channel ->
            LibSlack.slack_default(code_lastknowngood, channel, project.short_name, true)
        }

        def data_lastknowngood = job(current_branch + '.data.lastknowngood') {}
        freestyle_jobs.add(data_lastknowngood)
        LibRemoteTriggers.data_lastknowngood_get(data_lastknowngood, branch_info)
        branch_info?.slack_channel_preflight?.channels.each { channel ->
            LibSlack.slack_default(data_lastknowngood, channel, project.short_name, true)
        }
        LibCommonCps.add_downstream_freestyle_job_triggers(freestyle_jobs, current_branch, branchfile.freestyle_job_trigger_matrix)
    }
}
