package com.ea.project.bct.branchsettings

import com.ea.lib.LibPerforce
import com.ea.lib.model.branchsettings.PipelineDeterminismTestConfiguration
import com.ea.project.bct.Bct

class Dev_na_to_trunk_sub {
    // Settings for jobs
    static Class project = Bct
    static Map general_settings = [
        dataset                                : project.dataset,
        frostbite_licensee                     : project.frostbite_licensee,
        elipy_install_call                     : project.elipy_install_call,
        elipy_call                             : project.elipy_call + ' --use-fbenv-core',
        workspace_root                         : project.workspace_root,
        azure_elipy_call                       : project.azure_elipy_call,
        azure_elipy_install_call               : project.azure_elipy_install_call,
        azure_workspace_root                   : project.azure_workspace_root,
        azure_fileshare                        : [
            additional_tools_to_include: ['frostedtests', 'win64'],
            secret_context             : 'glacier_azure_fileshare',
            target_build_share         : 'bfglacier',
        ],
        autotest_remote_settings               : [
            eala: [
                credentials   : 'monkey.bct',
                p4_code_creds : 'bct-la-p4',
                p4_code_server: 'dicela-p4edge-fb.la.ad.ea.com:2001',
                p4_data_creds : 'bct-la-p4',
                p4_data_server: 'dicela-p4edge-fb.la.ad.ea.com:2001',
            ],
            dice: [
                p4_code_creds : 'perforce-battlefield01',
                p4_code_server: 'dice-p4buildedge02-fb.dice.ad.ea.com:2001',
                p4_data_creds : 'perforce-battlefield01',
                p4_data_server: 'dice-p4buildedge02-fb.dice.ad.ea.com:2001',
            ]
        ],
        report_build_version                   : ' --reporting-build-version-id %code_changelist%',
        webexport_script_path                  : 'Code\\DICE\\BattlefieldGame\\fbcli\\webexport.py',
        gametool_settings                      : [
            gametools: [
                (LibPerforce.GAMETOOL_ICEPICK)                    : [
                    config        : 'release',
                    framework_args: ['-G:frostbite.use-prebuilt-native-binaries=true'],
                ],
                (LibPerforce.GAMETOOL_FROSTBITE_DATABASE_UPGRADER): [],
                (LibPerforce.GAMETOOL_FROSTYISOTOOL)              : [],
                (LibPerforce.GAMETOOL_DRONE)                      : [],
                (LibPerforce.GAMETOOL_FRAMEWORK)                  : [],
                (LibPerforce.GAMETOOL_FBENV)                      : [],
            ],
        ],
        pipeline_determinism_test_configuration: new PipelineDeterminismTestConfiguration(
            cronTrigger: '@daily',
            referenceJob: '.data.start',
        ),
    ]
    static Map code_settings = [
        deploy_frostedtests          : true,
        deploy_tests                 : true,
        fake_ooa_wrapped_symbol      : false,
        skip_code_build_if_no_changes: false,
        slack_channel_code           : [
            channels                  : ['#bf-dev-na-to-trunk-build-notify'],
            skip_for_multiple_failures: true,
        ],
        sndbs_enabled                : true,
    ]
    static Map data_settings = [
        slack_channel_data     : [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        enable_lkg_cleaning    : true,
        poolbuild_data         : true,
        skip_frosty_trigger    : true,
        timeout_hours_data     : 8,
        webexport_branch       : true,
        webexport_allow_failure: true,
    ]
    static Map frosty_settings = [
        poolbuild_frosty    : true,
        slack_channel_frosty: [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        timeout_hours_frosty: 8,
        use_linuxclient     : true,
    ]
    static Map standard_jobs_settings = code_settings + data_settings + frosty_settings + [
        asset                     : 'DevLevels',
        enable_lkg_p4_counters    : true,
        frosty_reference_job      : 'dev-na-to-trunk-sub.code.start',
        server_asset              : 'Game/Setup/Build/DevMPLevels',
        extra_data_args           : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        extra_frosty_args         : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        shift_branch              : true,
        shift_every_build         : true,
        skip_icepick_settings_file: true,
        strip_symbols             : false,
    ]
    static Map icepick_settings = [
        ignore_icepick_exit_code: false
    ]
    static Map preflight_settings = [:]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'win64game', configs: ['final', 'retail', 'release', 'performance']],
        [name: 'tool', configs: [[name: 'release', compile_unit_tests: true, run_unit_tests: true],]],
        [name: 'win64server', configs: ['final']],
        [name: 'xbsx', configs: ['final', 'retail', 'release', 'performance']],
        [name: 'ps5', configs: ['final', 'retail', 'release', 'performance']],
        [name: 'linux64server', configs: ['final']],
        [name: 'linux64', configs: ['final']],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = [
        [name: '.code.p4counterupdater', args: ['code_changelist', 'code_countername']],
        [name: '.data.start', args: ['code_changelist']],
        [name: '.frosty.start', args: []],
    ]
    static List data_matrix = [
        [name: 'win64'],
        [name: 'server'],
        [name: 'xbsx'],
        [name: 'ps5'],
        [name: 'linux64'],
        [name: 'validate-frosted', allow_failure: true, deployment_platform: false],
    ]
    static List data_downstream_matrix = [
        [name: '.data.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.data.p4cleancounterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.code.tool.release.copy-build-to-azure', args: ['code_changelist']],
    ]
    static List patchdata_matrix = []
    static List frosty_matrix = [
        [name: 'win64', variants: [[format: 'files', config: 'final', region: 'ww', args: ''],
                                   [format: 'digital', config: 'final', region: 'ww', args: ''],
                                   [format: 'files', config: 'performance', region: 'ww', args: '']]],
        [name: 'ps5', variants: [[format: 'digital', config: 'final', region: 'dev', args: ''],
                                 [format: 'files', config: 'final', region: 'dev', args: ' --additional-configs release'],
                                 [format: 'files', config: 'performance', region: 'dev', args: '']]],
        [name: 'xbsx', variants: [[format: 'digital', config: 'final', region: 'ww', args: ''],
                                  [format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release'],
                                  [format: 'files', config: 'performance', region: 'ww', args: '']]],
        [name: 'server', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww', args: ''],
                                         [format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'linux64', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
    ]
    static List frosty_downstream_matrix = [
        [name: '.frosty.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.shift.upload', args: ['code_changelist', 'data_changelist']],
        [name: '.spin.linuxserver.digital.final.ww', args: ['code_changelist', 'data_changelist']],
    ]
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww']]],
    ]
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = [
        [platform: 'tool', content_type: ['code'], config: ['release']]
    ]
    static List pipeline_determinism_test_matrix = [
        [platform: 'win64']
    ]
}
