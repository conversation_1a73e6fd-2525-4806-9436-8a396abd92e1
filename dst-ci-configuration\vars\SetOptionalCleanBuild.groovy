/**
 * SetOptionalCleanBuild.groovy
 * Adds 'clean-build' message in a build description for future reference, if necessary.
 */
void call(def currentBuild, def clean_local) {
    if (Boolean.parseBoolean(clean_local)) {
        if (currentBuild.description != null) {
            currentBuild.description = currentBuild.description + ' clean-build'
        } else {
            currentBuild.description = 'clean-build'
        }
    }
}
