"""
icepick_utils.py
https://electronic-arts.slack.com/archives/CSC3DPZTL/p1594715078190000
"""

import os
import glob
from typing import Dict, Iterable

from platform import node
from elipy2 import core, local_paths, LOGGER, p4, frostbite_core
from elipy2.telemetry import collect_metrics


@collect_metrics()
def icepick_clean(icepicker, keep_local_bin=False):
    """
    Run icepick clean operations
    """
    LOGGER.info("Running Icepick clean...")
    if not keep_local_bin:
        LOGGER.info("  Cleaning local/bin...")
        icepicker.clean_local_bin()
    LOGGER.info("  Cleaning local/autobuild...")
    icepicker.clean_local_autobuild()
    LOGGER.info("  Cleaning local/frosty...")
    icepicker.clean_local_frosty()
    LOGGER.info("  Cleaning icepicktemp...")
    icepicker.clean_icepicktemp()
    LOGGER.info("Done.")


@collect_metrics()
def sync_files_to_head(root_dir, p4_port, p4_client, files, user):
    """
    Sync the specified files to head
    """
    if files:
        LOGGER.info("    Syncing files to head for %s", p4_client)

        p4_utils = p4.P4Utils(p4_port, user=user, client=p4_client)
        p4_utils.set_environment()

        file_paths = [os.path.join(root_dir, file.strip("\\")) for file in files]

        for file in file_paths:
            p4_utils.sync(file)
        LOGGER.info("    Done")


@collect_metrics()
def sync_specified_files_to_head(
    p4_user, code_p4_port, code_files, data_p4_port, data_files, data_dir
):
    """
    Sync files to head
    """

    code_p4_client = f"jenkins-{node()}-codestream"
    data_p4_client = f"jenkins-{node()}-{data_dir}stream"

    default_code_files = [
        "\\Code\\DICE\\QDS\\...",
        "\\scripts\\DICE\\QDS\\...",
    ]

    default_data_files = ["\\Config\\Icepick\\TestSuites\\Kingston\\QDS\\..."]

    code_files_to_sync = code_files if code_files else default_code_files
    data_files_to_sync = data_files if data_files else default_data_files

    sync_files_to_head(
        frostbite_core.get_tnt_root(), code_p4_port, code_p4_client, code_files_to_sync, p4_user
    )
    sync_files_to_head(
        frostbite_core.get_game_data_dir(),
        data_p4_port,
        data_p4_client,
        data_files_to_sync,
        p4_user,
    )


def get_icepick_logs_dir() -> str:
    """
    Return the path to the icepick logs directory
    """
    app_data = os.getenv("LOCALAPPDATA")
    icepick_log_dir = os.path.join(app_data, "Icepick", "logs")
    return icepick_log_dir


def get_icepick_logs_files(patterns: Iterable[str] = None) -> Dict[str, Iterable[str]]:
    """
    Return a list of icepick files matching the pattern
    """
    patterns = patterns or ["atf.targets.*.log", "Icepick.*.log"]

    icepick_log_dir = get_icepick_logs_dir()
    log_files = {}
    for file_pattern in patterns:
        LOGGER.info("Processing pattern %s...", file_pattern)
        glob_pattern = os.path.join(icepick_log_dir, file_pattern)
        files = [f for f in glob.glob(glob_pattern) if os.path.isfile(f)]
        log_files[file_pattern] = files

    return log_files


def save_icepick_logs():
    """
    Move the icepick logs we care about to the logs folder so they can be uploaded to Jenkins
    """
    LOGGER.info("Saving icepick logs...")

    try:
        icepick_log_dir = get_icepick_logs_dir()
        pattern_files = get_icepick_logs_files()

        files_to_save = []
        for _, value in pattern_files.items():
            files = sorted(value, key=os.path.getmtime)
            files = [os.path.basename(f) for f in files]
            files = files[:1]
            files_to_save.extend(files)

        LOGGER.info("Copying %s" % files_to_save)
        core.robocopy(icepick_log_dir, local_paths.get_logs_path(), extra_args=files_to_save)
        LOGGER.info("Done copying files")
    except TypeError as _:
        LOGGER.exception("Failed to copy icepick logs")
