package schedulers

import support.DeclarativePipelineSpockTest

class PerforceDvcsSchedulerSpec extends DeclarativePipelineSpockTest {

    void setup() {
        binding.setVariable('env', [
            dvcs_remote_spec: 'remote-spec',
            slack_channel   : 'dvcs-slack-channel',
            skip_fetch      : 'false',
            skip_push       : 'false',
            JOB_NAME        : 'dvcs-job-name',
            projectName     : 'Bct',
        ])

        helper.with {
            registerAllowedMethod('SlackMessageNew', [Map, String, String])
            registerAllowedMethod('echo', [String])
            registerAllowedMethod('ProjectClass', [String]) { projectName -> [short_name: 'exc'] }
        }
    }

    void 'test fetch dvcs'() {
        when:
        runScript('perforce_dvcs_scheduler.groovy')
        printCallStack()
        then:
        assertCalledTimes('build', 2)
        assertJobStatusSuccess()
    }

    void 'test push dvcs'() {
        given:
        binding.setVariable('env', [
            skip_fetch: 'true',
            skip_push : 'false',
        ])
        when:
        runScript('perforce_dvcs_scheduler.groovy')
        printCallStack()
        then:
        assertCalledTimes('build', 1)
        assertJobStatusSuccess()
    }

    void 'test skip dvcs'() {
        given:
        binding.setVariable('env', [
            skip_fetch: 'true',
            skip_push : 'true',
        ])
        when:
        runScript('perforce_dvcs_scheduler.groovy')
        printCallStack()
        then:
        assertCalledTimes('build', 0)
        assertJobStatusSuccess()
    }
}
