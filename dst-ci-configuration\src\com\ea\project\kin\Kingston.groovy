package com.ea.project.kin

import com.ea.project.Cobra

class Kingston {
    static String name = 'kingston'
    static String short_name = 'kin'
    static Boolean frostbite_syncer_setup = false
    static Boolean single_perforce_server = false
    static Boolean presync_machines = false
    static String user_credentials = 'svc_kin01'
    static List<Map> vault_secrets_project = com.ea.project.Cobra.af2_vault_credentials
    static String vault_server_credentials = 'dice-online-cas-prod-secret-id'
    static String vault_server_variable = 'VAULT_ONLINE_CAS_PROD_SECRET_ID'
    static String vault_credentials = 'cobra-online-rob-prod-secret-id'
    static String vault_variable = 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
    static Boolean vault_win64_trial = true
    static String game_team_secrets_credential = 'game-team-secrets-secret-id'
    static String game_team_secrets_credential_extra = ''
    static String game_team_secrets_credential_online_prod = ''
    static String game_team_secrets_variable = 'GAME_TEAM_SECRETS_SECRET_ID'
    static List vault_default_platforms = [
        'linuxserver',
        'ps4',
        'ps5',
        'server',
        'win64',
        'xb1',
        'xbsx',
    ]

    static String dataset = 'kindata'
    static String frostbite_licensee = 'BattlefieldGame'

    static String workspace_root = 'D:\\dev'
    static String fbcli_call = 'tnt\\bin\\fbcli\\cli.bat x64'
    static String location = 'dice'
    static String elipy_scripts_config_file = 'elipy_kingston.yml'
    static String elipy_install_call = "${fbcli_call} && ${Cobra.elipy_install} $elipy_scripts_config_file >> ${workspace_root}\\logs\\install-elipy.log 2>&1"
    static String elipy_setup_call = "${fbcli_call} && ${Cobra.elipy_setup_env} $elipy_scripts_config_file >> ${workspace_root}\\logs\\setup-elipy-env.log 2>&1"
    static String elipy_call = "${elipy_setup_call} && elipy --location $location"
    static String elipy_call_criterion = "${elipy_setup_call} && elipy --location criterion"
    static String elipy_call_eala = "${elipy_setup_call} && elipy --location dicela"
    static String elipy_call_earo = "${elipy_setup_call} && elipy --location earo"

    static String azure_workspace_root = 'E:\\dev'
    static String azure_elipy_install_root = 'C:\\dev'
    static String azure_elipy_setup_call = "$fbcli_call && $azure_elipy_install_root\\ci\\setup-elipy-env.bat $elipy_scripts_config_file >> $azure_workspace_root\\logs\\setup-elipy-env.log 2>&1"
    static String azure_elipy_install_call = "$fbcli_call && $azure_elipy_install_root\\ci\\install-elipy.bat $elipy_scripts_config_file >> $azure_workspace_root\\logs\\install-elipy.log 2>&1"
    static String azure_elipy_call = "$azure_elipy_setup_call && elipy --location $location"

    static String p4_browser_url = 'https://swarm.frostbite.com/'
    static String p4_user_single_slash = '%USERDOMAIN%\\%USERNAME%'
    static Map p4_extra_servers = [:]

    static String p4_code_root = '//dicestudio/kin' // TODO double-check
    static String p4_code_creds = 'perforce-p4buildedge02-fb-kingston01'
    static String p4_code_server = 'dice-p4buildedge02-fb.dice.ad.ea.com:2001'  // new p4 server
    static String p4_code_client = 'jenkins-${NODE_NAME}-codestream'
    static String p4_code_client_env = 'jenkins-%NODE_NAME%-codestream'

    static String p4_data_root = '//data/kin'
    static String p4_data_creds = 'perforce-tunguska-kingston01' // data creds for kingston
    static String p4_data_server = 'p4-tunguska-build01.dice.ad.ea.com:2001'
    static String p4_data_client = 'jenkins-${NODE_NAME}-' + dataset + 'stream'
    static String p4_data_client_env = 'jenkins-%NODE_NAME%-' + dataset + 'stream'

    static Map p4_code_servers = [
        'frostbite_build_dice': p4_code_server,
        'frostbite_cri'       : 'oh-p4edge-fb.eu.ad.ea.com:2001',
    ]

    static List<Map> p4_data_servers = [
        [name: 'tunguska_build_dice', p4_port: p4_data_server],
        [name: 'tunguska_cri', p4_port: 'oh-p4edge-tunguska.eu.ad.ea.com:2001'],
    ]

    static Map icepick_settings = [
        icepick_test          : 'KingstonUnitTests',
        icepick_preflight_test: 'PreflightUnitTests',
        settings_files        : 'Config/Icepick/IcepickSettings.ini',
    ]
    static Map external_job = [
        server            : 'systest-auto.rws.ad.ea.com',
        job               : 'UploadDiceBuildToS3',
        test_url          : 'https://marvin.dev.dice.se/api/v1/async/gameserver/e2etest/ci',
        test_credentials  : 'marvin_test_credentials',
        upload_credentials: 'marvin_upload_credentials',
    ]
    static Map external_params = [
        token                : 'dre_integration',
        awsLibsPath          : 'Casablanca\\frosty\\Casablanca\\AwsDlls',
        bucket               : 'systest-kingston-marvin',
        titleCredentials     : 'dice-staging-gs-aws',
        allowDuplicatedCodeCL: 'True',
    ]

    static String webexport_script_path = 'Scripts\\DICE\\webexport.py'
    static String drone_exclude_path = 'TnT/Setup/Drone/...'
    static Boolean fake_ooa_wrapped_symbol = false
    static Boolean commerce_debug_disable = true

    static Boolean use_recompression_cache = true
    static Boolean use_recompression_cache_ps5 = false

    static Boolean is_cloud = false

    static String autotest_matrix = 'KinAutotestMatrix'

    static Integer timeout_hours_frosty = 10

    static String properties_file = 'job.properties'
    static Boolean compress_symbols = true
    static Boolean compress_symbols_code_win64server = false
    static Boolean compress_symbols_code_win64game = false
    static Boolean compress_symbols_code_xb1 = false
    static Boolean compress_symbols_code_xbsx = false

    static Boolean clean_master_version_check = true
    static Boolean expression_debug_data = true

    static Boolean shift_compression = true

    static Map pipeline_warning_settings = [
        platform        : 'win64',
        target_address  : 'https://kin-pwarn.ad.ea.com/',
        tool_repository : '*****************:ghost-tools/pwarn.git',
        user_credentials: 'maptool.kin',
    ]

    static String denuvo_exclusion_path = 'TnT\\Build\\DenuvoExclusionList'
    static Boolean verify_post_vault = true
    static Boolean enable_custom_cl = true
    static Boolean file_hashes_frosty = true

    static int retry_limit_data = 1
    static int retry_limit_patchdata = 1
}
