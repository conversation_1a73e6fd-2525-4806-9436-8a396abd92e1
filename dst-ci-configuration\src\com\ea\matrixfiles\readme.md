### Test Category

See <a href='https://gitlab.ea.com/dre-cobra/dst-ci-configuration/-/blob/master/src/com/ea/lib/model/autotest/AutotestCategory.groovy'>
AutotestCategory</a>

### Test Info

- platforms - Optional. Will default to global platform list. Options `List<Map<String, String>>` where we
  support `[[name: <name of platform>, region: <see region>]]` - region overrides
- parallel_limit - Optional. Default `1`. Options `int value multiple of platforms` - number of tests to run in
  parrallel for these tests
- run_levels_in_parallel - Optional. Default `false`. Options `[true, false]` - Should tests be ran in parallel
- strict_sequential - Optional. Default `false`. Options `[true, false]` - Should tests be ran strictly sequentially:
  triggers one test suite at a time
- show_test_results_on_jenkins - Optional. Default `false`. Options `[true, false]` - Should jen<PERSON> fail if the tests
  fail
- test_group - Required. Test group name (e.g. `lkgtests`) - Icepick grouping value (used for reporting)
- timeout_hours - Optional. Default `4`. Options `int value` - How long should the tests run before timing out
- region - Optional. Default `ww`. Options `[ww, eu, na]` - for tests using packages, what package should be used -
  region overrides test category `region`
- server_region - Optional. Default `ww`. Options `[ww, alpha, beta]` - for tests using packages, what package should be
  used - region overrides test category `server_region`
- required_platforms - Optional. Default `None`. Options `[<platform list>]` - when is_test_with_loose_files, this will
  look for CLs available on all required platforms
- tests - Required.
- extra_framework_args - Optional. Default `None`. Extra arguments for Icepick to pass to any Framework commands it
  starts. Overrides test category `extra_framework_args`.
- slack_channel - Optional. Default `None`. Overrides the test category and default channel to send Slack notifications
  to.

### Test

- name - Required. Test suite name (e.g. `OnlineJoinFlowTests`)
- extra_args - Optional. Default `None`. Options `String value` - Extra icepick args
- need_game_server - Optional. Default `false`. Options `[true, false]` - does this test need the game server binaries
- fetch_frosted - Optional. Default `false`. Options `[true, false]` - should this test fetch frosted binaries
- pool_type - Optional. Default `atf`. Options `[atf, ""]` - what pool should be used when running icepick
- platforms - Optional. Default `predefined platform list`.Options `List<Map<String, String>>` where we
  support `[[name: <name of platform>, region: <see region>]]` - region overrides - platform overrides. Note that
  setting region is only supported for sequential jobs, not for parallel.

If both `run_levels_in_parallel` and `strict_sequential` are set to `false`, the start job will trigger all platforms in
parallel for a given test suite, but one test suite at a time sequentially.
