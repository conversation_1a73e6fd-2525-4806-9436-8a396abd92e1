package com.ea.project.fb1.branchsettings

class DevNaBattlefieldGameFirstPatch {
    // Settings for jobs
    static Class project = com.ea.project.fb1.Fb1Battlefieldgame
    static Map general_settings = [
        dataset           : project.dataset,
        elipy_install_call: project.elipy_install_call,
        elipy_call        : project.elipy_call + ' --use-fbenv-core',
        frostbite_licensee: 'BattlefieldGame',
        workspace_root    : project.workspace_root,
    ]
    static Map standard_jobs_settings = [
        asset                       : 'DevLevels',
        claim_builds                : true,
        data_reference_job          : 'dev-na-battlefieldgame.code.start',
        enable_daily_data_clean     : true,
        fetch_baseline_reference_job: 'dev-na-battlefieldgame.store_regular_baseline.start',
        first_patch                 : true,
        frosty_digital_asset        : 'ShippingLevels',
        patch_branch                : 'dev-na-battlefieldgame-first-patch',
        poolbuild_patchdata         : true,
        poolbuild_patchfrosty       : true,
        same_baseline_config        : true,
        server_asset                : 'Game/Setup/Build/DevMPLevels',
        slack_channel_patchdata     : [
            channels                  : ['#bf-dev-na-build-notify'],
            skip_for_multiple_failures: true,
        ],
        slack_channel_patchfrosty   : [
            channels                  : ['#bf-dev-na-build-notify'],
            skip_for_multiple_failures: true,
        ],
        split_code_data_sync        : true,
        timeout_hours_frosty        : 4,
        timeout_hours_patchdata     : 15,
        trigger_string_patchdata    : 'H H/12 * * 1-5',
        use_dynamic_disc_baselines  : true,
        use_linuxclient             : true,
    ]
    static Map preflight_settings = [:]

    // Matrix definitions for jobs
    static List code_matrix = []
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = []
    static List data_matrix = []
    static List data_downstream_matrix = []
    static List patchdata_matrix = [
        'win64',
        'ps5',
        'xbsx',
    ]
    static List patchdata_downstream_matrix = [
        [name: '.patchfrosty.start', args: []],
    ]
    static List frosty_matrix = []
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = [
        [name: 'server', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww', args: '']]],
    ]
    static List patchfrosty_matrix = [
        [name: 'win64', variants: [[format: 'digital', config: 'final', region: 'ww', args: '']]],
        [name: 'ps5', variants: [[format: 'digital', config: 'final', region: 'dev', args: '']]],
        [name: 'xbsx', variants: [[format: 'digital', config: 'final', region: 'ww', args: '']]],
    ]
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = []
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
