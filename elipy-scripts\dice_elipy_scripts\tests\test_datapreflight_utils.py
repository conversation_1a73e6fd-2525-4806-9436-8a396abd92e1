"""
test_datapreflight_utils.py

Unit testing for datapreflight_utils
"""
import json
import os
import pytest
import unittest
from mock import mock_open, patch
from dice_elipy_scripts.utils.datapreflight_utils import (
    get_dbx_assets_to_cook,
    get_primary_instance_type,
    get_skipped_asset_types,
    get_layer_hierarchy,
    extract_layers_from_file_list,
)
from elipy2.exceptions import ELIPYException


class TestDataPreflightUtils(unittest.TestCase):
    FILE_LIST = [
        {b"depotFile": b"basedir/Source/not_a_dbx_file.txt", b"action": b"add"},
        {b"depotFile": b"basedir/Source/with_dot.dbx_but_really.txt", b"action": b"add"},
        {b"depotFile": b"basedir/Source/asset_to_add.dbx", b"action": b"add"},
        {b"depotFile": b"basedir/Source/asset_to_edit.dbx", b"action": b"edit"},
        {b"depotFile": b"basedir/Source/asset_to_delete.dbx", b"action": b"delete"},
        {b"depotFile": b"basedir/Source/Shaders/shader_asset.dbx", b"action": b"add"},
        {b"depotFile": b"basedir/Source/Animations/animation_asset.dbx", b"action": b"add"},
        {b"depotFile": b"basedir/Source/MaterialGrid/Part_/part_asset.dbx", b"action": b"add"},
        {b"depotFile": b"basedir/asset_not_in_source.dbx", b"action": b"add"},
        {b"depotFile": b"basedir/Shaders/shader_not_in_source.dbx", b"action": b"add"},
        {b"depotFile": b"basedir/Animations/animation_not_in_source.dbx", b"action": b"add"},
        {b"depotFile": b"basedir/MaterialGrid/Part_/part_not_in_source.dbx", b"action": b"add"},
        {b"depotFile": b"basedir/Source/not_included.txt", b"other": b"type"},
        {b"depotFile": b"basedir/Layers/C1S2B1/parent_layer_asset_to_edit.dbx", b"action": b"edit"},
        {
            b"depotFile": b"basedir/Layers/TestLayer_01/content_layer_asset_to_edit.dbx",
            b"action": b"edit",
        },
        {
            b"depotFile": b"TnT/Code/Engine/SomeSystem/SourceData.Test/Source/ModuleName/Some_SDM_Asset.dbx",
            b"action": b"edit",
        },
    ]

    @patch("dice_elipy_scripts.utils.datapreflight_utils.get_primary_instance_type")
    def test_get_dbx_assets_to_cook_default(self, mock_get_primary_instance_type):
        mock_get_primary_instance_type.return_value = "some.type"
        assets_to_cook = get_dbx_assets_to_cook(self.FILE_LIST, "basedir")
        assert len(assets_to_cook) == 2

    @patch("dice_elipy_scripts.utils.datapreflight_utils.get_primary_instance_type")
    @patch("dice_elipy_scripts.utils.datapreflight_utils.get_layer_hierarchy")
    def test_get_dbx_assets_to_cook_content_layer(
        self, mock_get_layer_hierarchy, mock_get_primary_instance_type
    ):
        mock_get_layer_hierarchy.return_value = ["TestLayer_01", "C1S2B1"]
        mock_get_primary_instance_type.return_value = "some.type"
        assets_to_cook = get_dbx_assets_to_cook(self.FILE_LIST, "basedir", "TestLayer_01")
        assert len(assets_to_cook) == 4

    @patch("dice_elipy_scripts.utils.datapreflight_utils.get_primary_instance_type")
    def test_get_dbx_assets_to_cook_dont_exclude_files(self, mock_get_primary_instance_type):
        mock_get_primary_instance_type.return_value = "some.type"
        assets_to_cook = get_dbx_assets_to_cook(self.FILE_LIST, "basedir", exclude_files=False)
        assert len(assets_to_cook) == 5

    @patch("dice_elipy_scripts.utils.datapreflight_utils.get_primary_instance_type")
    @patch("dice_elipy_scripts.utils.datapreflight_utils.get_skipped_asset_types")
    def test_get_dbx_assets_to_cook_skip_asset_type(
        self, mock_get_skipped_asset_types, mock_get_primary_instance_type
    ):
        mock_get_skipped_asset_types.return_value = [
            "ModuleName1.SkipType1",
            "ModuleName2.SkipType2",
        ]
        mock_get_primary_instance_type.side_effect = [
            "ModuleName1.SkipType1",
            "not.relevant",
            "ModuleName2.SkipType2",
            "not.relevant",
            "ModuleName3.SkipType3",
        ]
        assets_to_cook = get_dbx_assets_to_cook(self.FILE_LIST, "basedir", exclude_files=False)
        assert len(assets_to_cook) == 3

    @patch("dice_elipy_scripts.utils.datapreflight_utils.get_primary_instance_type")
    @patch("dice_elipy_scripts.utils.datapreflight_utils.get_skipped_asset_types")
    def test_get_dbx_assets_to_cook_skip_asset_type_without_module_name(
        self, mock_get_skipped_asset_types, mock_get_primary_instance_type
    ):
        mock_get_skipped_asset_types.return_value = ["ModuleName1.SkipType1", "SkipType2"]
        mock_get_primary_instance_type.side_effect = [
            "ModuleName1.SkipType1",
            "not.relevant",
            "ModuleName2.SkipType2",
            "not.relevant",
            "ModuleName3.SkipType3",
        ]
        assets_to_cook = get_dbx_assets_to_cook(self.FILE_LIST, "basedir", exclude_files=False)
        assert len(assets_to_cook) == 3

    def test_get_skipped_asset_types(self):
        data_file = os.path.join(os.path.dirname(__file__), "data", "SkipCookTypeList.json")
        assert get_skipped_asset_types(data_file) == [
            "ModuleName1.TypeName1",
            "ModuleName2.TypeName2",
            "TypeName3",
            "TypeName4",
        ]

    def test_get_skipped_asset_types_json_error(self):
        data_file = os.path.join(
            os.path.dirname(__file__), "data", "SkipCookTypeListBadFormat.json"
        )
        with pytest.raises(json.decoder.JSONDecodeError):
            get_skipped_asset_types(data_file)

    def test_get_layer_hierarchy(self):
        settings_file = os.path.join(os.path.dirname(__file__), "data", "contentLayers.json")
        assert get_layer_hierarchy("TestLayer_01", settings_file) == ["TestLayer_01", "C1S2B1"]

    def test_get_primary_instance_type(self):
        data_path = os.path.join(os.path.dirname(__file__), "data")
        layer_sub_path = "Source"
        data_file = "TestAssetFile"
        assert (
            get_primary_instance_type(
                asset_name=data_file, asset_base_path=data_path, asset_layer_path=layer_sub_path
            )
            == "ModuleName.TypeName"
        )

    def test_get_primary_instance_type_empty(self):
        data_path = os.path.join(os.path.dirname(__file__), "data")
        data_file = "TestAssetFileTypeInfoEmpty"
        with pytest.raises(ELIPYException):
            get_primary_instance_type(asset_name=data_file, asset_base_path=data_path)

    def test_get_primary_instance_type_missing(self):
        data_path = os.path.join(os.path.dirname(__file__), "data")
        data_file = "TestAssetFileTypeInfoMissing"
        with pytest.raises(ELIPYException):
            get_primary_instance_type(asset_name=data_file, asset_base_path=data_path)

    @patch("dice_elipy_scripts.utils.datapreflight_utils.open", new_callable=mock_open())
    def test_get_primary_instance_type_open_file(self, mock_open_file):
        data_path = os.path.join(os.path.dirname(__file__), "data")
        data_layer = "Source"
        data_file = "TestAssetFile"
        get_primary_instance_type(
            asset_name=data_file, asset_layer_path=data_layer, asset_base_path=data_path
        )
        mock_open_file.assert_called_once_with(
            os.path.join(data_path, data_layer, "TestAssetFile.dbx"), encoding="utf8"
        )

    @patch("dice_elipy_scripts.utils.datapreflight_utils.get_layer_hierarchy")
    def test_extract_layers_from_file_list_default(self, mock_get_layer_hierarchy):
        mock_get_layer_hierarchy.return_value = []
        layers = extract_layers_from_file_list(self.FILE_LIST, datadir="basedir")
        self.assertEqual(len(layers), 3)
        self.assertEqual(layers, ["Source", "C1S2B1", "TestLayer_01"])

    def test_extract_layers_from_file_list_empty(self):
        layers = extract_layers_from_file_list([], datadir="basedir")
        self.assertEqual([], layers)

    @patch("dice_elipy_scripts.utils.datapreflight_utils.get_layer_hierarchy")
    def test_extract_layers_from_file_list_without_datadir(self, mock_get_layer_hierarchy):
        file_list = [
            {
                b"depotFile": b"TnT/Code/Engine/SomeSystem/SourceData.Test/Source/ModuleName/Other_SDM_Asset.dbx",
                b"action": b"edit",
            },
            {
                b"depotFile": b"TnT/Code/Engine/SomeSystem/SourceData.Test/Source/ModuleName/Some_SDM_Asset.dbx",
                b"action": b"edit",
            },
        ]
        mock_get_layer_hierarchy.return_value = ["TestLayer_01", "C1S2B1"]
        layers = extract_layers_from_file_list(file_list, datadir="basedir")
        self.assertEqual(len(layers), 0)

    @patch("dice_elipy_scripts.utils.datapreflight_utils.get_layer_hierarchy")
    def test_extract_layers_from_file_list_specific_layer(self, mock_get_layer_hierarchy):
        file_list = [
            {
                b"depotFile": b"bfdata/Layers/TestLayer_01/Test/ContentLayers/Levels/CL_TestLevel_01/Prefabs/PF_CLTest_MarinaWarehouse_06_PropsA.dbx",
                b"action": b"edit",
            },
            {
                b"depotFile": b"TnT/Code/Engine/SomeSystem/SourceData.Test/Source/ModuleName/Some_SDM_Asset.dbx",
                b"action": b"edit",
            },
        ]
        mock_get_layer_hierarchy.return_value = ["C1S2B1"]
        layers = extract_layers_from_file_list(file_list, datadir="bfdata")
        self.assertEqual(["TestLayer_01", "C1S2B1"], layers)
        mock_get_layer_hierarchy.assert_called_with("TestLayer_01")

    @patch("dice_elipy_scripts.utils.datapreflight_utils.get_layer_hierarchy")
    def test_extract_layers_from_file_list_specific_layers(self, mock_get_layer_hierarchy):
        file_list = [
            {
                b"depotFile": b"bfdata/Layers/TestLayer_01/Test/ContentLayers/Levels/CL_TestLevel_01/Prefabs/PF_CLTest_MarinaWarehouse_06_PropsA.dbx",
                b"action": b"edit",
            },
            {
                b"depotFile": b"bfdata/Layers/TestLayer_02/Test/ContentLayers/Levels/CL_TestLevel_02/Prefabs/PF_CLTest_MarinaWarehouse_07_PropsA.dbx",
                b"action": b"add",
            },
            {
                b"depotFile": b"bfdata/Layers/TestLayer_03/Test/ContentLayers/Levels/CL_TestLevel_02/Prefabs/PF_CLTest_MarinaWarehouse_08_PropsA.dbx",
                b"action": b"delete",
            },
        ]
        mock_get_layer_hierarchy.side_effect = mock_get_layer_hierarchy_side_effect
        layers = extract_layers_from_file_list(file_list, datadir="bfdata")
        self.assertEqual(layers, ["TestLayer_01", "C1S2B1", "TestLayer_02", "TestLayer_03"])
        mock_get_layer_hierarchy.assert_any_call("TestLayer_01")
        mock_get_layer_hierarchy.assert_any_call("TestLayer_02")
        mock_get_layer_hierarchy.assert_any_call("TestLayer_03")


def mock_get_layer_hierarchy_side_effect(arg):
    if "TestLayer_01" in arg:
        return ["TestLayer_01", "C1S2B1"]
    elif "TestLayer_02" in arg:
        return ["TestLayer_02", "C1S2B1"]
    elif "TestLayer_03" in arg:
        return ["TestLayer_03", "TestLayer_01", "C1S2B1"]
    else:
        return []
