"""
navmesh.py
"""
import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import data, filer, p4, LOGGER, frostbite_core
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command("navmesh", short_help="Cook and submit Navmesh asset.")
@click.option("--data-dir", required=True)
@click.option("--asset", default="Levels\\MainLevel\\MainLevel_racePathfinding")
@click.option("--p4-port", required=True)
@click.option("--p4-client", required=True)
@click.option("--code-branch", default=None, help="Perforce branch/stream name.")
@click.option("--code-changelist", default=None, help="Perforce changelist number for code.")
@click.option("--data-changelist", default=None, help="Perforce changelist number for data.")
@click.option(
    "--pipeline-args",
    multiple=True,
    help="Pipeline arguments for data build.",
    default=[],
)
@click.option("--user", default=None, help="Perforce user name.")
@click.option("--dry-run", is_flag=True, help="Don't submit the navmesh.")
@click.option(
    "--data-clean",
    default="false",
    help="Clean Avalanche if --data-clean true is passed.",
)
@click.option("--p4-edit-list", multiple=True, help="Perforce files to open for edit", default=[])

# pylint: disable=too-many-statements
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    data_dir,
    asset,
    p4_port,
    user,
    p4_client,
    code_branch,
    code_changelist,
    data_changelist,
    pipeline_args,
    dry_run,
    data_clean,
    p4_edit_list,
):
    """
    Cook and submit Navmesh asset.
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    perforce = p4.P4Utils(port=p4_port, user=user, client=p4_client)
    perforce.revert()

    try:
        # Fetch pipeline binary
        _filer = filer.FilerUtils()
        _filer.fetch_code(code_branch, code_changelist, "pipeline", "release")

        builder = data.DataUtils("reimport", [asset], monkey_build_label=data_changelist)
        builder.set_datadir(data_dir)

        for path in p4_edit_list:
            full_path = os.path.join(frostbite_core.get_game_data_dir(), path)
            perforce.sync(full_path)
            perforce.edit(full_path)

        if data_clean.lower() == "true":
            builder.clean()

        builder.cook(pipeline_args=list(pipeline_args), collect_mdmps=True)

        # Submit navmesh
        perforce.reopen()
        if not dry_run:
            perforce.revert(wipe=False, only_unchanged=True)
            cl_description = "Navmesh for CL {}: ".format(data_changelist)
            cl_description += "\nJenkins URL: " + os.environ.get("BUILD_URL", "None")
            perforce.submit(message=cl_description)
        else:
            LOGGER.info("Dry run, will not submit navmesh.")
    finally:
        perforce.revert()
