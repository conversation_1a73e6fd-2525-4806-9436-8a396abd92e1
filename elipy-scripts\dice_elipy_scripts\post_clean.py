"""
post_clean.py
"""
import os
import time
import click

from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import core, local_paths, p4, running_processes, LOGGER, frostbite_core
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command("post_clean", short_help="Clean a machine after a failed build.")
@click.option("--processes", default=None, multiple=True)
@click.option("--user", default=None)
@click.option("--data-port", default=None)
@click.option("--data-client", default=None)
@click.option("--code-port", default=None)
@click.option("--code-client", default=None)
@click.option(
    "--antifreeze",
    is_flag=True,
    help="For Antifreeze jobs, we need to clean the Antifreeze folder.",
)
@click.option(
    "--keep-python",
    is_flag=True,
    help="Avoid to kill Python processes",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _, processes, user, data_port, data_client, code_port, code_client, antifreeze, keep_python
):
    """
    Clean a machine after a failed build.
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    LOGGER.info("Starting post clean")
    if not processes:
        processes = [
            "Engine.Fbapi.Pipeline.Test_Win64_release_Dll",
            "Tool.Pipeline_Win64_release_Dll*",
            "MSBuild.exe",  # should be killed before cl.exe
            "cl.exe",
            "Casablanca.Main_Win64*",
            "frostyisotool.exe",
            "orbis-pub-cmd.exe",
            "orbis-ctrl.exe",
            "orbis-symupload.exe",
            "prospero-symupload.exe",
            "orbis-clang.exe",
            "nant.exe",
            "AvalancheCLI.exe",
            "mspdbsrv.exe",
            "vctip.exe",
            "fbenvcore.exe",
        ]

    # Sleep for 1 min before killing processes that might do a crash dump.
    LOGGER.info("Waiting 60 seconds...")
    time.sleep(60)

    LOGGER.info("Killing processes...")
    running_processes.kill(list(processes))

    LOGGER.info("Deleting ThinSAN.tmp...")
    core.delete_folder(os.path.join("C:/", "ThinSAN.tmp"))

    LOGGER.info("Deleting ps log files")
    core.delete_folder(
        os.path.join(os.getenv("UserProfile"), "Documents", "PS_Logs"),
        close_handles=True,
    )

    if code_port and code_client:
        LOGGER.info("Reverting code...")
        perforce_code = p4.P4Utils(port=code_port, client=code_client, user=user)
        perforce_code.revert()

    if data_port and data_client:
        LOGGER.info("Reverting data...")
        perforce_data = p4.P4Utils(port=data_port, client=data_client, user=user)
        perforce_data.revert()
        if antifreeze:
            antifreeze_p4_path = local_paths.get_antifreeze_dir() + os.sep + "..."
            perforce_data.clean(folder=antifreeze_p4_path)

    LOGGER.info("Deleteing tnt logs...")
    delete_tnt_logs()
    LOGGER.info("Killing other fbenvconfigservice instances...")
    running_processes.kill(["fbenvconfigservice.exe"])
    if not keep_python:
        LOGGER.info("Killing other Python instances...")
        running_processes.kill(["python.exe"])
    LOGGER.info("Done!")


def delete_tnt_logs():
    """
    Delete Log files in TnT directory
    """
    tnt_path = frostbite_core.get_tnt_root()

    files = os.listdir(tnt_path)
    for item in files:
        if item.endswith(".log") or (item == "FrostyLogFile.txt"):
            os.remove(os.path.join(tnt_path, item))
