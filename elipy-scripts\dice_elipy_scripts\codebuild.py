r"""
codebuild.py

The codebuild script's main purpose is to produce binary files for the provided platform
and config, source index. It will also store the symbols and denuvo encrypt retail builds.

General setup:
- Clean up steps: Some things we run here is killing of running processes closing of file handles
and cleaning up of temp folders.

- Initialize objects using packages from Elipy core, to be used later.
    - One instance of code.CodeUtils().
    - One instance of filer.FileUtils().

- Set data directory (if we have any specified), default value is set when running cli.bat '
  but this is not always what we like to use.

- We have a special build type which checks for deprecation and forwarding header errors.
  - If the config is given as `deprecation-test`, set parameters needed for this build.
  - Run gensln and buildsln as usual, with with the parameters set above.
  - Skip uploading the result to the network share, since another job already does this for
    the same platform and config.

- Some options for the local state:
    - Delete the local state (TnT\Local) if clean is set too true.
    - If not, import the local state if building statelessly. An overview of how the stateless
      code build works,  when using the method of storing the state on the network share,
      can be found here:
      https://drive.google.com/file/d/1dQcXgEbaVz_x_GcyJtNiBZ_klPhjQV_Q/view?usp=sharing.
      We store the output of building code, both local/bin and local/build on the network
      share and copy that in to bootstrap the builds. If anything goes wrong with this we
      build from clean.

- Set licensee:
    - Optional step that we run when a licensee is sent as an argument. We need to set
      this when we don't build the default licensee on a stream. For example when we build
      dev-na where the default licensee is ExampleGame, but we like to build BattlefieldGame.

- Generate solution - generates the Visual Studio solution and installs packages and
  dependencies needed to build the game binaries.

- Build solution - builds the solution and generates the game binaries.

- Run unit tests (if activated): runs unit tests on the code with icepick. This can only run on
  the tools platform and only if the test has been generated when building the game,
  this is controlled with a flag passed to the build process.

- Run post-build processing:
    - Source indexing: maps the symbols to the specific files and versions in Perforce
    - Denuvo encryption: we encrypt pc game code that will be released to protect the game.
      To run this we fetch files in artifactory and look for settings in the elipy_config
      and then call out to the denuvo servers with this and they run the encryption.
      To get his set up we give denuvo some details and a build and they give us the
      files and the settings to use.

- Deploy the code to the network share:

- Handle symbols - all these are opt out in the script:
    - Backup symbols - store them on the network share
    - Upload symbols to Avalanche - avalanche has a symbol storage solution, this is deprecated
      but still used by some game teams.
    - Upload symbols to Symstore - There are separate symstore solutions for microsoft platforms
      and playstation platforms.
      Unfortunately linux platforms can not be used with either of these. Symstores are set up
      on the network share and the tools used for storing it is in perforce code. Dice has a
      symbol service setup that makes it easy for developers to find these symbols when debugging,
      Staffan Gustavsson is the POC for that setup.
    - Some game teams also run a Kobold symbol storage, that storage monitor the network share
      for things to store. These are mainly used for QE and not by developers, since
      originally there was no good support for developers to fetch the symbols from it.
    - The win64 retail exe is a special case here. Since that one is denuvo encrypted it's
      hard to use the symbols after that. So for dice projects we run a tool for faking the
      game binary. So we send the normal binary to the tool and modify the data
      to match the binary we will release and also rename it to the release name
      and then we store that symbol. When debugging a game crash developer will then
      find this symbol instead of the symbol from the denuvo encrypted origin wrapped exe.

There are two special cases:
    - For nomaster builds, we deploy the code to filer but skip symbol handling.
        - Code on the network share here is only used for the stateless setup.

Examples:
      * elipy codebuild tool release --code-branch build-main-dre --code-changelist 436418
        --p4-port oh-p4-criterion.eu.ad.ea.com:1666 --p4-client jenkins-et1-1f2b3d-codestream
      * elipy codebuild tool release --code-branch build-main-dre --code-changelist 436418
        --p4-port oh-p4-criterion.eu.ad.ea.com:1666 --p4-client jenkins-et1-1f2b3d-codestream
        --dry-run
      * elipy codebuild tool release --code-branch build-main-dre --code-changelist 436418
        --p4-port oh-p4-criterion.eu.ad.ea.com:1666 --p4-client jenkins-et1-1f2b3d-codestream
        --no-master
      * elipy --location criterion codebuild tool release --code-branch build-main-dre
        --code-changelist 436418
        --p4-port oh-p4-criterion.eu.ad.ea.com:1666 --p4-client jenkins-et1-1f2b3d-codestream
        --p4-user **** --clean false --skip-deploy-tnt  --artifactory-user <EMAIL>
        --artifactory-apikey **** --denuvo-exclusion TnT\Build\DenuvoExclusionList
        --skip-symbols-backup --email <EMAIL>
        --password "****" --compress-symbols true
      * elipy --location dice codebuild ps5 final --code-branch kin-dev --code-changelist 17965040
        --p4-port dice-p4buildedge02-fb.dice.ad.ea.com:2001
        --p4-client jenkins-ks2-5fe30f-codestream --p4-user DICE\svc_auto_kingston
        --clean false --skip-deploy-tnt --artifactory-user <EMAIL>
        --artifactory-apikey **** --denuvo-exclusion TnT\Build\DenuvoExclusionList
        --licensee BattlefieldGame
        --skip-symbols-backup --skip-symbols-to-symstore --email <EMAIL>
        --password "****" --use-snowcache --compress-symbols true --fake-ooa-wrapped-symbol true

"""
import click
import os
from dice_elipy_scripts.utils.code_utils import (
    add_metadata_files,
    download_outsource_dependencies,
    modify_buildlayout,
    prepare_outsource_dependencies,
    run_gensln,
)
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.env_utils import extract_fb_env_values
from dice_elipy_scripts.utils.licensee_helper import set_licensee
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from dice_elipy_scripts.utils.snowcache_utils import (
    clean_required,
    get_snowcache_host_arg,
    get_snowcache_mode_args,
    SNOWCACHE_MODES,
    validate_use_snowcache_param,
)
from dice_elipy_scripts.utils.state_utils import import_local_code_state
from elipy2 import (
    build_metadata_utils,
    code,
    core,
    data,
    oreans,
    denuvo,
    filer,
    filer_paths,
    frostbite_core,
    local_paths,
    LOGGER,
    running_processes,
    SETTINGS,
    symbols,
)
from elipy2.cli import pass_context
from elipy2.exceptions import BrokenSymbol, ELIPYException
from elipy2.frostbite import build_agent_utils, fbenv_layer, icepick, sdk_utils
from elipy2.telemetry import collect_metrics


@click.command("codebuild", short_help="Generate and build the solution.")
@click.argument("platform")
@click.argument("config")
@click.option("--alltests", default=False, help="Run alltests build.")
@click.option(
    "--artifactory-apikey", default=None, help="Artifactory apikey for fetching denuvo files."
)
@click.option(
    "--artifactory-user", default=None, help="Artifactory user for fetching denuvo files."
)
@click.option(
    "--buildlayout-config", default=None, help="Use different config in buildlayout file."
)
@click.option("--clean", default="false", help="Delete TnT/Local if --clean true is passed.")
@click.option(
    "--clean-packages", default=False, help="Clean the 'packages' and 'LocalPackages' directories."
)
@click.option("--code-branch", required=True, help="Perforce branch/stream name.")
@click.option("--code-changelist", required=True, help="Perforce changelist number.")
@click.option(
    "--compress-symbols",
    default=True,
    help="Compress symbols before uploading them to the symstore.",
)
@click.option(
    "--copy-symbols-to-kobold-inbox",
    default=False,
    type=bool,
    help="Copy symbols to a specified Kobold inbox",
)
@click.option(
    "--custom-tag", default=None, help="Extra folder before changelist to fetch code from."
)
@click.option(
    "--data-directory",
    default=None,
    help="Which data directory to use for fetching licensee settings.",
)
@click.option(
    "--deploy-frostedtests",
    default=False,
    help="Deploy frosted test files using FrostEd.Tests_Win64-Dll_release_Files.txt",
)
@click.option(
    "--deploy-tests",
    default=False,
    help="Deploy test files using Tests_Win64-Dll_release_Files.txt",
)
@click.option(
    "--oreans-protection", default=False, help="Run Oreans Virtualizer protection after build."
)
@click.option("--oreans-config", default=None, help="Config file for Oreans Virtualizer.")
@click.option("--denuvo-wrapping", default=False, help="Do Denuvo wrapping after build.")
@click.option("--denuvo-exclusion", default=None, help="Exclusion list for Denuvo.")
@click.option(
    "--domain-user", default=None, help="The user to authenticate to package server as DOMAIN\\user"
)
@click.option("--dry-run", default=False, help="Build code without deploying.")
@click.option("--email", default=None, help="User email to authenticate to package server")
@click.option(
    "--fake-ooa-wrapped-symbol", default=False, help="Fake ooa wrapped symbol for project"
)
@click.option(
    "--fb-env-values",
    default=None,
    multiple=True,
    help="Frostbite environment values. Example: var1=value1",
)
@click.option("--filer-password", default=None, help="password for creating a filer connection")
@click.option("--filer-user", default=None, help="username for creating a filer connection")
@click.option("--framework-args", multiple=True, help="Framework arguments for gensln.")
@click.option("--gensln-config", default=None, help="Use different config for gensln")
@click.option(
    "--icepick-extra-framework-args",
    default=None,
    help="Extra arguments for Icepick to pass to any Framework commands it starts",
)
@click.option("--icepick-run-args", multiple=True, help="Run arguments for icepick.")
@click.option("--icepick-test", default=None, help="Icepick tests to run.")
@click.option(
    "--ignore-icepick-exit-code",
    default=True,
    type=bool,
    help=(
        "Should icepick result be ignored. By ignoring it, "
        "the job will succeed when a unit test fails."
    ),
)
@click.option("--import-local", default=False, help="Imports contents of TnT/Local from filer.")
@click.option(
    "--install-dotnet-sdk", default=False, help="Install DotNetSdk before running the job."
)
@click.option(
    "--is-outsource-build", default=False, help="Are we building things for the oursourcers"
)
@click.option("--licensee", multiple=True, default=None, help="Licensee to use")
@click.option("--mirror", default=True, help="Deploy to filer with mirror option.")
@click.option("--msbuild-args", multiple=True, help="Msbuild arguments for buildsln")
@click.option("--nomaster", default=False, help="Run nomaster build.")
@click.option("--only-gensln", default=False, type=bool, help="Should we exit after running gensln")
@click.option(
    "--override-do-not-run-code-unittests",
    default=False,
    help="Overrides whether to run the code unit tests.",
)
@click.option("--p4-client", required=True, help="Perforce workspace name.")
@click.option("--p4-port", required=True, help="Perforce server address.")
@click.option("--p4-user", default=None, help="Perforce user name.")
@click.option("--password", default=None, help="User credentials to authenticate to package server")
@click.option("--settings-files", help="Settings files relative to the data folder.", multiple=True)
@click.option(
    "--skip-deploy-tnt", default=False, help="Skip deploying TnT (DICE Drone builds req.)."
)
@click.option("--skip-symbols-backup", default=False, help="Skip backup for symbols.")
@click.option(
    "--skip-symbols-to-symstore", default=False, help="Skip uploading symbols to Symstore."
)
@click.option(
    "--snowcache-mode-override",
    type=click.Choice(SNOWCACHE_MODES, case_sensitive=False),
    default="",
    help="Override the logically evaluated snowcache mode with this",
)
@click.option(
    "--stressbulkbuild",
    default=False,
    help="Pass the '-stressbulkbuild' arg to gensln",
)
@click.option(
    "--strip-symbols",
    default=True,
    help="Flag for stripping symbols (only applies to linuxserver).",
)
@click.option(
    "--tool-targets",
    multiple=True,
    default=["pipeline", "frosted", "win64-dll"],
    help="Target tool platform(s) to build.",
)
@click.option("--use-snowcache", default=False, help="Whether to enable Snowcache or not")
@click.option("--use-state-zip", default=False, help="Zip up local state")
@click.option(
    "--virtual-branch-override",
    type=bool,
    default=False,
    help="Override the Perforce depot branch with the virtual branch used in the job",
)
@click.option("--wsl", default=False, help="Run build in windows subsystem linux.")
@click.option(
    "--clean-binaries",
    type=bool,
    default=False,
    help="Clean the binaries before building",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    platform,
    config,
    alltests,
    artifactory_apikey,
    artifactory_user,
    buildlayout_config,
    clean,
    clean_packages,
    code_branch,
    code_changelist,
    compress_symbols,
    copy_symbols_to_kobold_inbox,
    custom_tag,
    data_directory,
    oreans_protection,
    oreans_config,
    denuvo_wrapping,
    denuvo_exclusion,
    deploy_frostedtests,
    deploy_tests,
    domain_user,
    dry_run,
    email,
    fake_ooa_wrapped_symbol,
    fb_env_values,
    filer_password,
    filer_user,
    framework_args,
    gensln_config,
    icepick_extra_framework_args,
    icepick_run_args,
    icepick_test,
    ignore_icepick_exit_code,
    import_local,
    install_dotnet_sdk,
    is_outsource_build,
    licensee,
    mirror,
    msbuild_args,
    nomaster,
    only_gensln,
    override_do_not_run_code_unittests,
    p4_client,
    p4_port,
    p4_user,
    password,
    settings_files,
    skip_deploy_tnt,
    skip_symbols_backup,
    skip_symbols_to_symstore,
    snowcache_mode_override,
    stressbulkbuild,
    strip_symbols,
    tool_targets,
    use_snowcache,
    use_state_zip,
    virtual_branch_override,
    wsl,
    clean_binaries,
):
    r"""
    Generate and build the solution.
    Deploy the built solution.

    Examples:
      * elipy codebuild tool release --code-branch build-main-dre --code-changelist 436418
        --p4-port oh-p4-criterion.eu.ad.ea.com:1666 --p4-client jenkins-et1-1f2b3d-codestream
      * elipy codebuild tool release --code-branch build-main-dre --code-changelist 436418
        --p4-port oh-p4-criterion.eu.ad.ea.com:1666 --p4-client jenkins-et1-1f2b3d-codestream
        --dry-run
      * elipy codebuild tool release --code-branch build-main-dre --code-changelist 436418
        --p4-port oh-p4-criterion.eu.ad.ea.com:1666 --p4-client jenkins-et1-1f2b3d-codestream
        --no-master
      * elipy --location criterion codebuild tool release --code-branch build-main-dre
        --code-changelist 436418
        --p4-port oh-p4-criterion.eu.ad.ea.com:1666 --p4-client jenkins-et1-1f2b3d-codestream
        --p4-user **** --clean false --skip-deploy-tnt  --artifactory-user <EMAIL>
        --artifactory-apikey **** --denuvo-exclusion TnT\Build\DenuvoExclusionList
        --skip-symbols-backup true --email <EMAIL>
        --password "****" --compress-symbols true
      * elipy --location dice codebuild ps5 final --code-branch kin-dev --code-changelist 17965040
        --p4-port dice-p4buildedge02-fb.dice.ad.ea.com:2001
        --p4-client jenkins-ks2-5fe30f-codestream --p4-user DICE\svc_auto_kingston
        --clean false --skip-deploy-tnt --artifactory-user <EMAIL>
        --artifactory-apikey **** --denuvo-exclusion TnT\Build\DenuvoExclusionList
        --licensee BattlefieldGame
        --skip-symbols-backup true --skip-symbols-to-symstore --email <EMAIL>
        --password "****" --use-snowcache --compress-symbols true --fake-ooa-wrapped-symbol true
    """
    code_build(
        platform,
        config,
        code_branch,
        code_changelist,
        p4_port,
        p4_client,
        p4_user,
        framework_args,
        msbuild_args,
        clean,
        oreans_protection,
        oreans_config,
        denuvo_wrapping,
        denuvo_exclusion,
        artifactory_user,
        artifactory_apikey,
        alltests,
        nomaster,
        wsl,
        dry_run,
        import_local,
        skip_deploy_tnt,
        deploy_tests,
        deploy_frostedtests,
        data_directory,
        skip_symbols_backup,
        skip_symbols_to_symstore,
        compress_symbols,
        strip_symbols,
        icepick_test,
        override_do_not_run_code_unittests,
        ignore_icepick_exit_code,
        settings_files,
        licensee,
        password,
        custom_tag,
        email,
        domain_user,
        fake_ooa_wrapped_symbol,
        use_state_zip,
        use_snowcache,
        snowcache_mode_override,
        gensln_config,
        buildlayout_config,
        icepick_extra_framework_args,
        icepick_run_args,
        clean_packages,
        is_outsource_build,
        only_gensln,
        tool_targets,
        filer_user,
        filer_password,
        mirror,
        stressbulkbuild,
        copy_symbols_to_kobold_inbox,
        fb_env_values,
        install_dotnet_sdk,
        virtual_branch_override,
        clean_binaries,
    )


from typing import Optional, Iterable


def code_build(
    platform: str,
    config: str,
    code_branch: str,
    code_changelist: str,
    p4_port: str,
    p4_client: str,
    p4_user: Optional[str] = None,
    framework_args: Optional[Iterable[str]] = (),
    msbuild_args: Optional[Iterable[str]] = (),
    clean: Optional[str] = "false",
    oreans_protection: Optional[bool] = False,
    oreans_config: Optional[str] = None,
    denuvo_wrapping: Optional[bool] = False,
    denuvo_exclusion: Optional[str] = None,
    artifactory_user: Optional[str] = None,
    artifactory_apikey: Optional[str] = None,
    alltests: Optional[bool] = False,
    nomaster: Optional[bool] = False,
    wsl: Optional[bool] = False,
    dry_run: Optional[bool] = False,
    import_local: Optional[bool] = False,
    skip_deploy_tnt: Optional[bool] = False,
    deploy_tests: Optional[bool] = False,
    deploy_frostedtests: Optional[bool] = False,
    data_directory: Optional[str] = None,
    skip_symbols_backup: Optional[bool] = False,
    skip_symbols_to_symstore: Optional[bool] = False,
    compress_symbols: Optional[bool] = False,
    strip_symbols: Optional[bool] = False,
    icepick_test: Optional[bool] = None,
    override_do_not_run_code_unittests: Optional[bool] = False,
    ignore_icepick_exit_code: Optional[bool] = True,
    settings_files: Optional[Iterable[str]] = (),
    licensee: Optional[Iterable[str]] = (),
    password: Optional[str] = None,
    custom_tag: Optional[str] = None,
    email: Optional[str] = None,
    domain_user: Optional[str] = None,
    fake_ooa_wrapped_symbol: Optional[bool] = False,
    use_state_zip: Optional[bool] = False,
    use_snowcache: Optional[bool] = False,
    snowcache_mode_override: Optional[str] = "",
    gensln_config: Optional[str] = None,
    buildlayout_config: Optional[str] = None,
    icepick_extra_framework_args: Optional[str] = None,
    icepick_run_args: Optional[Iterable[str]] = (),
    clean_packages: Optional[bool] = False,
    is_outsource_build: Optional[bool] = False,
    only_gensln: Optional[bool] = False,
    tool_targets: Optional[Iterable[str]] = (),
    filer_user: Optional[str] = None,
    filer_password: Optional[str] = None,
    mirror: Optional[bool] = True,
    stressbulkbuild: Optional[bool] = False,
    copy_symbols_to_kobold_inbox: Optional[bool] = False,
    fb_env_values: Optional[Iterable[str]] = (),
    install_dotnet_sdk: Optional[bool] = False,
    virtual_branch_override: Optional[bool] = False,
    clean_binaries: Optional[bool] = False,
):
    r"""
    TODO: populate doc string
    """

    # adding sentry tags
    add_sentry_tags(__file__)
    metadata_manager = build_metadata_utils.setup_metadata_manager()

    clean = clean.lower() == "true"
    platform = platform.lower()
    is_tool = platform == "tool"
    _filer = filer.FilerUtils()
    tool_targets = list(tool_targets)

    # Ensure avalanche healthy before using snowcache
    use_snowcache = validate_use_snowcache_param(SETTINGS, platform, use_snowcache)

    # Populate with new test data
    _filer.bilbo = metadata_manager

    # Authenticate filer if user/password specified
    if filer_user and filer_password:
        _filer.delete_network_connection()
        _filer.auth_network_connection(
            network_path=filer_paths.get_build_share_path(),
            username=filer_user,
            password=filer_password,
        )

    deprecation_test = False
    if config.lower() == "deprecation-test":
        # Special build to check for deprecation and forwarding header errors.
        # https://jaas.ea.com/browse/COBRA-250
        LOGGER.info("Running a test build to check for deprecation and forwarding header errors.")
        LOGGER.info("This corresponds to 'fb gensln tool release -enabledeprecation -fwderror'.")
        config = "release"
        deprecation_test = True
        dry_run = True  # This build will only check the status, not produce binaries for later use.

    steam = False
    if config.lower() == "steam":
        # Special build to compile with steam and validate the results
        # https://jaas.ea.com/browse/COBRA-3170
        LOGGER.info("Running a test build to check for steam.")
        if platform.lower() == "tool":
            config = "release"
        else:
            config = "final"
        steam = True
        dry_run = True  # This build will only check the status, not produce binaries for later use.

    # Pre-Check if the dest folder already exist in filer, abort job immediately
    dest = filer_paths.get_code_build_path(
        code_branch,
        code_changelist,
        platform,
        config,
        nomaster=nomaster,
        custom_tag=custom_tag,
        stressbulkbuild=stressbulkbuild,
    )

    if os.path.exists(dest) and not dry_run:
        raise ELIPYException(
            "Attempting to deploy to a path that already exists.\
            Possibly because a previous build succeeded in deploying before failing.\
            This can cause us to lose binaries and symbols and is not allowed."
        )

    builder = code.CodeUtils(
        platform,
        config,
        monkey_build_label=code_changelist,
        p4_port=p4_port,
        p4_user=p4_user,
        p4_client=p4_client,
    )
    _symbols = symbols.SymbolsUtils()

    # Set data directory.
    if data_directory is not None:
        data.DataUtils.set_datadir(data_directory)

    # ensure the licensee is set
    licensee_list = list(licensee)

    # Set framework args, handle licensee settings if needed.
    framework_args = set_licensee(licensee_list, list(framework_args))
    if not SETTINGS.get("skip_increment_client_version", default=False):
        builder.increment_client_version()

    if deprecation_test:
        framework_args.append("-G:frostbite.ignoreDeprecation=false")
        framework_args.append("-G:frostbite.disableForwardHeader=true")

    if steam:
        framework_args.append("-G:steam=true")

    if is_outsource_build:
        framework_args.append("-G:frostbite.pipeline.disable-platform-sdks=true")
        framework_args.append("-G:useProxyPackages=true")
        framework_args.append("-G:frostbite.usePrebuiltPackages=true")
        framework_args.append("-G:frostbite.is-outsource-build=true")
        framework_args.append("-G:disablePackageServer=true")

    if use_snowcache:
        framework_args.append(get_snowcache_host_arg(SETTINGS, platform))
        framework_args.append("-D:eaconfig.optimization.ltcg=off")

    if virtual_branch_override:
        framework_args.append("-G:frostbite.Engine.Communication.ForceBranch=true")

    msbuild_args = list(msbuild_args)

    if install_dotnet_sdk:
        sdk_utils.install_required_sdks(
            password=password, user=email, domain_user=domain_user, install_dotnet=True
        )

    build_frosted = is_tool and "frosted" in tool_targets
    code_arguments = [
        platform,
        config,
        code_branch,
        code_changelist,
        p4_port,
        p4_client,
        p4_user,
        framework_args,
        msbuild_args,
        oreans_protection,
        oreans_config,
        denuvo_wrapping,
        denuvo_exclusion,
        artifactory_user,
        artifactory_apikey,
        alltests,
        nomaster,
        wsl,
        dry_run,
        import_local,
        data_directory,
        strip_symbols,
        icepick_test,
        override_do_not_run_code_unittests,
        ignore_icepick_exit_code,
        settings_files,
        password,
        email,
        domain_user,
        fake_ooa_wrapped_symbol,
        use_snowcache,
        snowcache_mode_override,
        builder,
        _filer,
        _symbols,
        gensln_config,
        deprecation_test,
        icepick_extra_framework_args,
        icepick_run_args,
        clean_packages,
        is_outsource_build,
        only_gensln,
        build_frosted,
        stressbulkbuild,
        fb_env_values,
        clean_binaries,
    ]

    try:
        _code_build(clean, *code_arguments)
    except BrokenSymbol:
        LOGGER.warning("Retry with clean because of broken symbols")
        _code_build(True, *code_arguments)
    finally:
        # Revert Increment Client Version files when done
        builder.clean_platform_temp_files()

    if buildlayout_config is not None:
        modify_buildlayout(buildlayout_config, licensee_list, platform, config)

    if not dry_run and not only_gensln and not nomaster and not stressbulkbuild:
        # fetch pipeline
        # pushbuild if --use-fbcli
        # Deploy nomaster binaries if tnt/local is getting deployed.
        # We'll need them to do linking, so we need both of these together.
        # Deploy code build to filer and
        # ensure we only deploy TnT and houdini from one machine.
        if is_tool:
            extra_deploy = build_frosted and is_tool
            deploy_tnt = extra_deploy and not skip_deploy_tnt
            deploy_frostedtests = extra_deploy and deploy_frostedtests
        else:
            deploy_tnt = False
            deploy_frostedtests = False
        deploy_tests = deploy_tests and alltests
        deploy_frostedtests = deploy_frostedtests and alltests
        _filer.deploy_code(
            code_branch,
            code_changelist,
            platform,
            config,
            deploy_tnt=deploy_tnt,
            nomaster=nomaster,
            fbenv_copy_args=["/XF", "flag_to_include_incremental_state"],
            use_state_zip=use_state_zip,
            deploy_tests=deploy_tests,
            deploy_frostedtests=deploy_frostedtests,
            tool_targets=tool_targets,
            custom_tag=custom_tag,
            mirror=mirror,
        )

        add_metadata_files(_filer, code_branch, code_changelist, nomaster=nomaster)
        platforms = (
            tool_targets
            if is_tool and not frostbite_core.minimum_fb_version(year=2019, version_nr=1)
            else [platform]
        )

        for temp_platform in platforms:
            if not skip_symbols_backup:
                _symbols.backup_symbols(
                    branch=code_branch,
                    changelist=code_changelist,
                    platform=temp_platform,
                    config=config,
                )

            if not skip_symbols_to_symstore:
                _symbols.upload_symbols_to_sym_store(
                    platform=temp_platform,
                    changelist=code_changelist,
                    product_name="{}.{}".format(code_branch, temp_platform),
                    config=config,
                    compress=compress_symbols,
                )

            if copy_symbols_to_kobold_inbox:
                _symbols.copy_symbols_to_kobold_inbox(
                    code_branch, code_changelist, temp_platform, config
                )


def _code_build(
    clean,
    platform,
    config,
    code_branch,
    code_changelist,
    p4_port,
    p4_client,
    p4_user,
    framework_args,
    msbuild_args,
    oreans_protection,
    oreans_config,
    denuvo_wrapping,
    denuvo_exclusion,
    artifactory_user,
    artifactory_apikey,
    alltests,
    nomaster,
    wsl,
    dry_run,
    import_local,
    data_directory,
    strip_symbols,
    icepick_test,
    override_do_not_run_code_unittests,
    ignore_icepick_exit_code,
    settings_files,
    password,
    email,
    domain_user,
    fake_ooa_wrapped_symbol,
    use_snowcache,
    snowcache_mode_override,
    builder,
    _filer,
    _symbols,
    gensln_config,
    deprecation_test,
    icepick_extra_framework_args,
    icepick_run_args,
    clean_packages,
    is_outsource_build,
    only_gensln,
    build_frosted,
    stressbulkbuild,
    fb_env_values,
    clean_binaries,
):
    # Clean up before running the job.
    running_processes.kill()
    core.clean_temp()
    core.close_file_handles(local_paths.get_packages_path())
    core.close_file_handles(local_paths.get_tnt_localpackages_path())

    if clean_packages or is_outsource_build:
        core.delete_folder(local_paths.get_packages_path())
        core.delete_folder(local_paths.get_tnt_localpackages_path())

    # Clean the local bin directories
    if clean_binaries:
        local_bin_dir = os.path.join(frostbite_core.get_tnt_root(), "Local", "Bin")
        core.delete_folder(local_bin_dir, close_handles=True)

    if SETTINGS.get("enable_threshold_clean", default=False):
        build_agent_utils.generic_threshold_clean(workspacepath=frostbite_core.get_game_root())

    if is_outsource_build:
        download_dir = download_outsource_dependencies(artifactory_user, artifactory_apikey)
        prepare_outsource_dependencies(download_dir)

    if clean_required(use_snowcache, snowcache_mode_override, clean):
        builder.clean_local(close_handles=True)
    elif import_local:
        import_local_code_state(builder, _filer, code_branch, platform, config, nomaster)

    local_build_path = local_paths.get_local_build_path(platform, config)

    if not clean and os.path.exists(local_build_path):
        for pdb in _symbols.find_symbol_files(local_build_path, "pdb"):
            if os.stat(pdb).st_size > 1.8 * 2**30:
                # os.remove(pdb)
                LOGGER.info("{} is larger than 1.8 GB".format(os.path.basename(pdb)))

    snowcache_mode_args = get_snowcache_mode_args(clean, snowcache_mode_override, use_snowcache)

    if fb_env_values:
        gensln_fb_env_values = extract_fb_env_values(fb_env_values, "gensln")
        LOGGER.info(
            "Setting Frostbite environment values for gensln: {}".format(gensln_fb_env_values)
        )
        fbenv_layer.set_environment_values(gensln_fb_env_values)

    # Add additional snowcache args
    framework_args = framework_args + snowcache_mode_args

    run_gensln(
        password=password,
        user=email,
        domain_user=domain_user,
        framework_args=framework_args,
        builder=builder,
        alltests=alltests,
        nomaster=nomaster,
        wsl=wsl,
        gensln_config=gensln_config,
        stressbulkbuild=stressbulkbuild,
    )
    if only_gensln:
        return

    if fb_env_values:
        buildsln_fb_env_values = extract_fb_env_values(fb_env_values, "buildsln")
        LOGGER.info(
            "Setting Frostbite environment values for buildsln: {}".format(buildsln_fb_env_values)
        )
        fbenv_layer.set_environment_values(buildsln_fb_env_values)

    builder.buildsln(
        msbuild_args=msbuild_args,
        fail_on_first_error=not deprecation_test,
        build_frosted=build_frosted,
    )

    if (
        icepick_test
        and not override_do_not_run_code_unittests
        and platform == "tool"
        and not deprecation_test
    ):
        # Run icepick unittests
        data.DataUtils.set_datadir(data_directory)
        icepicker = icepick.IcepickUtils()
        icepicker.clean_icepicktemp()
        icepicker.run_icepick(
            platform="win64",
            test_suite=icepick_test,
            settings_file_list=list(settings_files),
            config=config,
            ignore_icepick_exit_code=ignore_icepick_exit_code,
            build_type="dll",
            run_args=[
                "--reporting-build-version-id",
                code_changelist,
                "--reporting-is-monkey",
                "true",
                "--reporting-public-suite",
                "true",
                "--reporting-branch",
                code_branch,
                "--disable-ensemble-plugin",
                "true",
            ]
            + list(icepick_run_args),
            lease=None,
            extra_framework_args=icepick_extra_framework_args,
        )

    if not dry_run and not nomaster and not stressbulkbuild:
        # Intention here is that if this run will distribute anything generated we want to
        # source index and check symbols.
        _symbols.source_index(
            platform=platform,
            config=config,
            p4_port=p4_port,
            p4_client=p4_client,
            p4_user=p4_user,
            strip_symbols=strip_symbols,
        )

        # oreans virtualizer
        if platform in ["win64game", "win64trial", "win64"]:
            oreans_protection = oreans_protection and config == "retail"
            if oreans_protection:
                oreans.protect(
                    platform=platform,
                    oreans_config=oreans_config,
                    artifactory_user=artifactory_user,
                    artifactory_api_key=artifactory_apikey,
                )

        # denuvo wrapping
        if platform in ["win64game", "win64trial", "win64"]:
            denuvo_wrapping = config == "retail" and denuvo_wrapping

            if denuvo_wrapping:
                denuvo.wrap(
                    platform=platform,
                    artifactory_user=artifactory_user,
                    artifactory_api_key=artifactory_apikey,
                    exclusion_path=denuvo_exclusion,
                )

            if fake_ooa_wrapped_symbol:
                _symbols.fake_ooawrap_bin(
                    platform=platform,
                    config=config,
                    changelist=code_changelist,
                    branch=code_branch,
                    denuvo_wrapping=denuvo_wrapping,
                )

        if platform not in ["linux64server", "ps4", "ps5"]:
            _symbols.verify_symbol_integrity(path_to_binaries=local_build_path)
