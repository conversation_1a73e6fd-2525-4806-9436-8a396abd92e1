{#
    Command:
        gametool_frostbite_database_upgrader
            short_help: Build the FDU gametool.
            context_settings: dict(ignore_unknown_options=True)

    Arguments:

    Required variables:
        code_changelist
            required: True
            help: Which code changelist to use.
        p4_port
            required: True
        p4_client
            required: True

    Optional variables:
        clean
            type: click.BOOL
            default: False
            help: Delete TnT/Local if --clean true is passed, otherwise no cleanup is performed.
        config
            default: release
        p4_user
            default: None
            help: Perforce user name
        submit
            type: click.BOOL
            default: True
            help: Set this to false for dry-run
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    gametool_frostbite_database_upgrader
    --code-changelist {{ code_changelist }}
    --p4-port {{ p4_port }}
    --p4-client {{ p4_client }}
    {%- if clean %}
    --clean {{ clean }}
    {%- endif %}
    {%- if config %}
    --config {{ config }}
    {%- endif %}
    {%- if p4_user %}
    --p4-user {{ p4_user }}
    {%- endif %}
    {%- if submit %}
    --submit {{ submit }}
    {%- endif %}
  displayName: elipy gametool_frostbite_database_upgrader
