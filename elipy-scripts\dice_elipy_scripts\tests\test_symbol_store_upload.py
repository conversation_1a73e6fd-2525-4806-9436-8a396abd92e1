"""
test_symbol_store_upload.py

Unit testing for symbol_store_upload
"""
import unittest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import MagicMock, patch
from dice_elipy_scripts.symbol_store_upload import cli


@patch("dice_elipy_scripts.symbol_store_upload.add_sentry_tags", MagicMock())
@patch("elipy2.telemetry.collect_metrics", MagicMock())
@patch("elipy2.telemetry.upload_metrics", MagicMock())
class TestSymbolStoreUploadCli(unittest.TestCase):
    OPTION_BRANCH = "--branch"
    OPTION_CHANGELIST = "--changelist"
    OPTION_CONFIG = "--config"
    OPTION_EMAIL = "--email"
    OPTION_DOMAIN_USER = "--domain-user"
    OPTION_PASSWORD = "--password"
    OPTION_PLATFORM = "--platform"
    OPTION_COMPRESS_SYMBOLS = "--compress-symbols"
    OPTION_LICENSEE = "--licensee"
    OPTION_P4_PORT = "--p4-port"
    OPTION_P4_CLIENT = "--p4-client"
    OPTION_P4_USER = "--p4-user"
    OPTION_NEEDS_INDEX = "--needs-index"
    OPTION_CUSTOM_SYMSTORE = "--custom-sym-store"
    OPTION_CUSTOM_BINARIES_LOCATION = "--custom-binaries-destination"

    VALUE_BRANCH = "some_branch"
    VALUE_CHANGELIST = 1234
    VALUE_CONFIG = "final"
    VALUE_EMAIL = "e_mail"
    VALUE_DOMAIN_USER = "domain_user"
    VALUE_PASSWORD = "pass_word"
    VALUE_PLATFORM = "win64"
    VALUE_COMPRESS_SYMBOLS = "false"
    VALUE_LICENSEE = "ExampleLicensee"
    VALUE_P4_PORT = "server:1666"
    VALUE_P4_CLIENT = "workspace"
    VALUE_P4_USER = "user"
    VALUE_NEEDS_INDEX = "true"
    VALUE_CUSTOM_SYMSTORE = "\\store\\symbols\\"
    VALUE_CUSTOM_BINARIES_LOCATION = "\\store\\binaries\\"

    BASIC_ARGS = [
        OPTION_BRANCH,
        VALUE_BRANCH,
        OPTION_CHANGELIST,
        VALUE_CHANGELIST,
        OPTION_CONFIG,
        VALUE_CONFIG,
        OPTION_PLATFORM,
        VALUE_PLATFORM,
        OPTION_P4_PORT,
        VALUE_P4_PORT,
        OPTION_P4_USER,
        VALUE_P4_USER,
        OPTION_P4_CLIENT,
        VALUE_P4_CLIENT,
    ]

    INDEX_ARGS = BASIC_ARGS + [
        OPTION_NEEDS_INDEX,
        VALUE_NEEDS_INDEX,
        OPTION_CUSTOM_SYMSTORE,
        VALUE_CUSTOM_SYMSTORE,
        OPTION_CUSTOM_BINARIES_LOCATION,
        VALUE_CUSTOM_BINARIES_LOCATION,
    ]

    CHECK_ARGS = [
        OPTION_BRANCH,
        VALUE_BRANCH,
        OPTION_CHANGELIST,
        VALUE_CHANGELIST,
        OPTION_CONFIG,
        VALUE_CONFIG,
        OPTION_PLATFORM,
        "tool",
        OPTION_P4_PORT,
        VALUE_P4_PORT,
        OPTION_P4_USER,
        VALUE_P4_USER,
        OPTION_P4_CLIENT,
        VALUE_P4_CLIENT,
        OPTION_NEEDS_INDEX,
        VALUE_NEEDS_INDEX,
        OPTION_CUSTOM_SYMSTORE,
        VALUE_CUSTOM_SYMSTORE,
        OPTION_CUSTOM_BINARIES_LOCATION,
        VALUE_CUSTOM_BINARIES_LOCATION,
    ]

    def setUp(self):
        self.patcher_install_required_sdks = patch(
            "dice_elipy_scripts.symbol_store_upload.install_required_sdks", autospec=True
        )
        self.mock_install_required_sdks = self.patcher_install_required_sdks.start()

        self.patcher_get_local_build_path = patch("elipy2.local_paths.get_local_build_path")
        self.mock_get_local_build_path = self.patcher_get_local_build_path.start()
        self.mock_get_local_build_path.return_value = "c:\\local\\build\\path"

        self.patcher_get_path_dirname = patch("os.path.dirname")
        self.mock_get_path_dirname = self.patcher_get_path_dirname.start()
        self.mock_get_path_dirname.return_value = "c:\\local\\tool\\path"

        self.patcher_get_code_build_path = patch("elipy2.filer_paths.get_code_build_path")
        self.mock_get_code_build_path = self.patcher_get_code_build_path.start()
        self.mock_get_code_build_path.return_value = "code\\build\\path"

        self.patcher_symbolsutils = patch("elipy2.symbols.SymbolsUtils", autospec=True)
        self.mock_symbolsutils = self.patcher_symbolsutils.start()

        self.patcher_filer = patch("elipy2.filer.FilerUtils", autospec=True)
        self.mock_filerutils = self.patcher_filer.start()

        self.patcher_set_licensee = patch(
            "dice_elipy_scripts.symbol_store_upload.set_licensee", autospec=True
        )
        self.mock_set_licensee = self.patcher_set_licensee.start()
        print(self.mock_set_licensee)

    def tearDown(self):
        patch.stopall()

    def test_basic_args(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0

    def test_upload_symbols_to_sym_store(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_symbolsutils.return_value.upload_symbols_to_sym_store.assert_called_once_with(
            self.VALUE_PLATFORM,
            path="code\\build\\path",
            changelist=self.VALUE_CHANGELIST,
            product_name=self.VALUE_BRANCH + "." + self.VALUE_PLATFORM,
            compress=True,
        )

    def test_upload_symbols_to_sym_store_dont_compress(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_COMPRESS_SYMBOLS, self.VALUE_COMPRESS_SYMBOLS]
        )
        assert result.exit_code == 0
        assert "Usage:" not in result.output
        self.mock_symbolsutils.return_value.upload_symbols_to_sym_store.assert_called_once_with(
            self.VALUE_PLATFORM,
            path="code\\build\\path",
            changelist=self.VALUE_CHANGELIST,
            product_name=self.VALUE_BRANCH + "." + self.VALUE_PLATFORM,
            compress=False,
        )

    def test_source_index(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.INDEX_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_symbolsutils.return_value.source_index.assert_called_once_with(
            platform=self.VALUE_PLATFORM,
            config=self.VALUE_CONFIG,
            p4_port=self.VALUE_P4_PORT,
            p4_client=self.VALUE_P4_CLIENT,
            p4_user=self.VALUE_P4_USER,
            path="c:\\local\\build\\path",
            custom_p4_cmd=False,
        )

    def test_verify_symbol(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.INDEX_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_symbolsutils.return_value.verify_symbol_integrity.assert_called_once_with(
            path_to_binaries="c:\\local\\build\\path"
        )

    def test_deploy_code(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.INDEX_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_filerutils.return_value.deploy_code.assert_called_once_with(
            branch=self.VALUE_BRANCH,
            changelist=self.VALUE_CHANGELIST,
            platform=self.VALUE_PLATFORM,
            config=self.VALUE_CONFIG,
            deploy_tests=False,
            deploy_frostedtests=False,
            custom_binaries_destination=self.VALUE_CUSTOM_BINARIES_LOCATION,
            skip_bilbo=False,
        )

    def test_deploy_code_skip_bilbo(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.INDEX_ARGS + ["--skip-bilbo", True])
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_filerutils.return_value.deploy_code.assert_called_once_with(
            branch=self.VALUE_BRANCH,
            changelist=self.VALUE_CHANGELIST,
            platform=self.VALUE_PLATFORM,
            config=self.VALUE_CONFIG,
            deploy_tests=False,
            deploy_frostedtests=False,
            custom_binaries_destination=self.VALUE_CUSTOM_BINARIES_LOCATION,
            skip_bilbo=True,
        )

    def test_upload_symbols_to_sym_store_with_index_custom_cmd(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.INDEX_ARGS + ["--custom-p4-cmd", "True"])
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_symbolsutils.return_value.source_index.assert_called_once_with(
            platform=self.VALUE_PLATFORM,
            config=self.VALUE_CONFIG,
            p4_port=self.VALUE_P4_PORT,
            p4_client=self.VALUE_P4_CLIENT,
            p4_user=self.VALUE_P4_USER,
            path="c:\\local\\build\\path",
            custom_p4_cmd=True,
        )

    def test_upload_symbols_to_sym_store_with_verify(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.CHECK_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_symbolsutils.return_value.verify_symbol_integrity.assert_called_once_with(
            path_to_binaries="c:\\local\\tool\\path",
        )

    def test_upload_symbols_to_sym_store_with_tool_path(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.CHECK_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_get_path_dirname.assert_called_once_with(
            "c:\\local\\build\\path",
        )

    def test_install_required_sdks(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_install_required_sdks.assert_called_once_with(
            None, None, None, self.VALUE_PLATFORM
        )

    def test_install_required_sdks_credentials(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_EMAIL,
                self.VALUE_EMAIL,
                self.OPTION_PASSWORD,
                self.VALUE_PASSWORD,
                self.OPTION_DOMAIN_USER,
                self.VALUE_DOMAIN_USER,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_install_required_sdks.assert_called_once_with(
            self.VALUE_PASSWORD, self.VALUE_EMAIL, self.VALUE_DOMAIN_USER, self.VALUE_PLATFORM
        )

    def test_set_licensee(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_LICENSEE, self.VALUE_LICENSEE])
        assert result.exit_code == 0
        self.mock_set_licensee.assert_called_once_with(list([self.VALUE_LICENSEE]), list())
