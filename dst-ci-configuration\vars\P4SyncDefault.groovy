/**
 * P4SyncDefault.groovy
 * Sync from Perforce, code or data.
 */

import com.ea.lib.LibCommonNonCps

void call(Class project, Class branchFile, String branchFolder, String branchName, String syncType, String syncChangelist = '', boolean runSync = true) {
    if (runSync == false) {
        // This makes it possible to have a sync method in a job type, but control per branch if it's used.
        return
    }

    Map branchInfo = branchFile.standard_jobs_settings + branchFile.general_settings
    boolean splitCodeDataSync = branchInfo.split_code_data_sync ?: false

    String p4Creds = ''
    String p4Client = ''
    String p4Root = ''
    if (syncType == 'code') {
        p4Creds = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_code_creds', '', project)
        p4Client = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_code_client', '', project)
        p4Root = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_code_root', '', project)
    } else if (syncType == 'data') {
        if (project.single_perforce_server == true) { // For some projects, code and data is stored on the same Perforce server.
            if (splitCodeDataSync == false) { // If we sync both in the same sync, the data sync step isn't needed.
                return
            }
        }
        p4Creds = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_data_creds', '', project)
        p4Client = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_data_client', '', project)
        p4Root = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_data_root', '', project)
    } else {
        throw new IllegalArgumentException('Unknown sync type: ' + syncType + ', aborting.')
    }

    checkout perforce(
        credential: p4Creds,
        workspace: streamSpec(
            charset: 'none',
            pinHost: true,
            streamName: p4Root + '/' + branchFolder + '/' + branchName,
            format: p4Client,
        ),
        populate: syncOnly(
            revert: true,
            have: true,
            force: false,
            modtime: false,
            quiet: true,
            pin: syncChangelist,
            parallel: [
                enable  : true,
                path    : '',
                threads : '0',
                minfiles: '0',
                minbytes: '0',
            ]
        )
    )
}
