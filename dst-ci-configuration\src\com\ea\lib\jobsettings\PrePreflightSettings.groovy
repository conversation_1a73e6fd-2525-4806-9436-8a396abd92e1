package com.ea.lib.jobsettings

import com.ea.lib.LibCommonCps
import com.ea.lib.LibCommonNonCps

class PrePreflightSettings extends JobSetting {
    String description = 'Runs if preflight machine is idle for too long.'
    String scriptContent
    List<String> serverAssets
    String latestVerifiedCode
    String latestVerifiedData

    void initialize(
        Class branchFile, Class masterFile, Class projectFile,
        String platformName, String branchName, String latestVerifiedCode, String latestVerifiedData
    ) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.preflight_branches as Map)
        this.branchInfo += branchFile.preflight_settings

        def get = { name, value ->
            LibCommonNonCps.get_setting_value(this.branchInfo, ['preflight_data', platformName], name, value, projectFile)
        }
        def p4DataServer = get('p4_data_server', [:])
        boolean cleanMasterVersionCheck = get('clean_master_version_check', false)
        def timeoutHours = get('timeout_hours_dataprepreflight', 2)
        def concurrentBuilds = get('concurrent_data', 10)
        def extraArgs = cleanMasterVersionCheck ? ' --clean-master-version-check' : ''
        def asset = get('asset', 'PreflightLevels.dbx')

        this.timeoutMinutes = timeoutHours * 60
        this.concurrentBuilds = concurrentBuilds
        this.scriptContent = LibCommonCps.p4RevertScript(projectFile, this.branchInfo, true, true, true)
        this.serverAssets = LibCommonCps.getDataPreflightPlatformAssets(branchFile.data_preflight_matrix, platformName)
        this.latestVerifiedData = latestVerifiedData
        this.latestVerifiedCode = latestVerifiedCode

        String serverAssets = this.serverAssets.join(' --server-asset ')
        def elipyCmd = this.elipyCall + ' pre_preflight' +
            ' --code-branch ' + this.branchInfo.code_branch + ' --code-changelist %code_changelist%' +
            ' --data-branch ' + this.branchInfo.data_branch + ' --data-changelist %data_changelist%' +
            " --platform %platform% --asset ${asset} --server-asset ${serverAssets} --datadir " + this.branchInfo.dataset +
            ' --p4-client ' + projectFile.p4_data_client_env + ' --p4-user %P4_USER% --p4-port ' + p4DataServer + extraArgs
        this.elipyCmd = elipyCmd.toString()
    }
}
