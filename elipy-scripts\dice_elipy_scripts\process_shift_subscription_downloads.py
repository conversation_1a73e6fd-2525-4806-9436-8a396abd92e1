"""
process_shift_subscription_downloads.py

Processes builds downloaded by a Shift subscription.
"""
import os
import click
import json
import traceback
import sys
from datetime import datetime
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import core, filer_paths, LOGGER, build_metadata_utils, running_processes, SETTINGS
from elipy2.cli import pass_context
from elipy2.exceptions import ELIPYException
from elipy2.telemetry import collect_metrics


@click.command(
    "process_shift_subscription_downloads",
    short_help="Processes builds downloaded by a Shift subscription.",
)
@click.option("--build-type", default="code", help="Type of build to process.")
@click.option("--code-branch", required=True, help="Code branch to use.")
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(_, build_type, code_branch):
    """
    Processes builds downloaded by a Shift subscription.
    Places the processed builds in the correct folder structure.
    """
    # Adding sentry tags.
    add_sentry_tags(__file__)

    # Clean up the machine before the build.
    running_processes.kill()

    implemented_build_types = ["code", "offsite_basic_drone"]
    if build_type.lower() not in implemented_build_types:
        raise ELIPYException(
            "This process has only been implemented the following build types: {}. "
            "Please add processing for {} builds to the script and retry.".format(
                implemented_build_types, build_type
            )
        )

    # Set up a metadata manager, so we can register the build in Bilbo after processing it.
    metadata_manager = build_metadata_utils.setup_metadata_manager(SETTINGS.get("bilbo_url"))

    shift_path = filer_paths.get_shift_subscription_download_path(build_type, code_branch)
    LOGGER.info("Shift subscription download path: {}".format(shift_path))

    dir_content = os.listdir(shift_path)
    subfolders = [x for x in dir_content if os.path.isdir(os.path.join(shift_path, x))]
    files = [x for x in dir_content if os.path.isfile(os.path.join(shift_path, x))]
    LOGGER.info("Found %s builds to process.", len(subfolders))

    failing_subfolders = []
    for subfolder in subfolders:
        try:
            info_file = find_file_with_prefix(files, subfolder)
            code_changelist = os.path.splitext(info_file)[0].split()[-1]
            LOGGER.info("Code changelist for the current build: %s", code_changelist)

            source_path = os.path.join(shift_path, subfolder)
            if build_type == "code":
                target_path = filer_paths.get_code_build_root_path(code_branch, code_changelist)
            elif build_type == "offsite_basic_drone":
                target_path = filer_paths.get_shift_processing_target_path(
                    build_type, code_branch, code_changelist
                )

            zip_source_path = os.path.join(source_path, code_changelist + ".zip")
            if os.path.exists(zip_source_path):
                LOGGER.info("Unzipping %s to %s", zip_source_path, target_path)
                core.extract_zip(zip_source_path, target_path)
            else:
                LOGGER.info("Copying %s to %s", source_path, target_path)
                core.robocopy(source_path, target_path)
            register_build_in_bilbo(target_path, code_changelist, metadata_manager)
            LOGGER.info("Removing processed build and info file.")
            core.delete_folder(os.path.join(shift_path, subfolder))
            core.delete_folder(os.path.join(shift_path, info_file))
            LOGGER.info("Done removing.")
        except Exception:
            failing_subfolders.append(subfolder)
            LOGGER.error("The processing failed for the following builds: {}".format(Exception))

    if failing_subfolders:
        raise ELIPYException(
            "The processing failed for the following builds: {}".format(failing_subfolders)
        )

    LOGGER.info("Done processing all builds in %s.", shift_path)


def find_file_with_prefix(file_list, prefix):
    """
    Finds a file with a certain prefix.
    The use case for this script is to find the info file that matches the content folder.
    The name of the info file starts with the Shit delivery ID, while the name of the content
    folder is the Shift delivery ID.
    """
    for filename in file_list:
        if filename.startswith(prefix):
            LOGGER.info("Found file: {}".format(filename))
            return filename
    raise ELIPYException("No info file found for this build. Please check the delivery folder.")


def register_build_in_bilbo(target_path, code_changelist, metadata_manager):
    """
    Register the build in Bilbo, using the metadata from the build.json file.
    Update source to point to the new build location,
    but keep the old location under original_source.
    """

    build_json_path = os.path.join(target_path, "build.json")

    if not os.path.exists(build_json_path):
        raise ELIPYException(
            "No build.json file found at path: {}. Cannot register build in Bilbo.".format(
                build_json_path
            )
        )
    try:
        created_timestamp = datetime.now().strftime("%Y-%m-%dT%H:%M:%S")
        with open(build_json_path, "r") as json_file:
            json_data = json.loads(json_file.read())
            verified_timestamp = json_data["verified_data"][0]["timestamp"]
            total_time = e2e_time(verified_timestamp, created_timestamp)
            json_data["original_source"] = json_data["source"]
            json_data["source"] = target_path
            json_data["shift_e2e_time"] = total_time
            json_data["changelist"] = code_changelist
            LOGGER.info("json_data: {}".format(json_data))
            metadata_manager.register_build_data(
                path=target_path,
                attributes=json_data,
                write_attributes_file=True,
                shift_copy=True,
            )
    except Exception:
        raise ELIPYException("Got those exception: {}".format(traceback.print_exc(file=sys.stdout)))


def e2e_time(verified_timestamp, created_timestamp):
    """
    Return the time differeent in H:M:S format between 2 timestamps.
    """
    begin = datetime.strptime(verified_timestamp, "%Y-%m-%dT%H:%M:%S.%f").timestamp()
    end = datetime.strptime(created_timestamp, "%Y-%m-%dT%H:%M:%S").timestamp()
    e2e_timestamp = end - begin
    hours, remainder = divmod(e2e_timestamp, 3600)
    minutes, seconds = divmod(remainder, 60)
    return f"{int(hours)}h:{int(minutes)}m:{int(seconds)}s"
