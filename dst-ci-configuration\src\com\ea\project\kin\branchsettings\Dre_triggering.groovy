package com.ea.project.kin.branchsettings

import com.ea.lib.LibPerforce

class Dre_triggering {
    // Settings for jobs
    static Class project = com.ea.project.kin.Kingston
    static Map general_settings = [
        dataset             : project.dataset,
        elipy_install_call  : project.elipy_install_call,
        elipy_call          : project.elipy_call,
        job_label_statebuild: 'statebuild-kin',
        workspace_root      : project.workspace_root,
        gametool_settings   : [
            gametools: [
                (LibPerforce.GAMETOOL_DRONE): [],
            ],
        ],
    ]
    static Map standard_jobs_settings = [
        asset                  : 'Preflightlevels',
        import_local           : false,
        retry_limit            : 1,
        server_asset           : 'Preflightlevels',
        slack_channel_code     : [channels: ['#dice-build-upgrade'], always_notify: true],
        slack_channel_data     : [channels: ['#dice-build-upgrade'], always_notify: true],
        slack_channel_frosty   : [channels: ['#dice-build-upgrade'], always_notify: true],
        webexport_branch       : false,
        use_recompression_cache: false,
    ]
    static Map preflight_settings = [:]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'win64game', configs: [[name: 'final', allow_failure: false]]],
        [name: 'ps4', configs: ['final', 'release']],
        [name: 'xb1', configs: [[name: 'final']]],
        //[name: 'linux64', configs: ['final']],
        [name: 'win64server', configs: ['final']],
        [name: 'linux64server', configs: ['final']],
        [name: 'tool', configs: ['release']],
        [name: 'ps5', configs: ['final', 'release', 'retail']],
        [name: 'xbsx', configs: ['final', 'release', 'retail']],
    ]
    static List code_nomaster_matrix = [
        [name: 'win64game', configs: [[name: 'final', allow_failure: false]]],
    ]
    static List code_downstream_matrix = []
    static List data_matrix = [
        'win64',
        'ps4',
        'xb1',
        //'linux64',
        'server',
        'ps5',
        'xbsx',
    ]
    static List data_downstream_matrix = [
        // [name: '.frosty.start', args: []],
    ]
    static List patchdata_matrix = [
        'win64',
//		'ps4',
//		'xb1'
    ]
    static List patchdata_downstream_matrix = [
        [name: '.patchfrosty.start', args: []],
    ]
    static List frosty_matrix = [
        [name: 'win64', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'ps4', variants: [[format: 'files', config: 'final', region: 'eu', args: ' --additional-configs release']]],
        [name: 'xb1', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
        //[name: 'linux64', variants: [[format: 'digital', config: 'final', region: 'ww', args: '']]],
        [name: 'server', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww', args: '']]],
        [name: 'ps5', variants: [[format: 'files', config: 'final', region: 'eu', args: '']]],
        [name: 'xbsx', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
    ]
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = [
//		[name: 'linux64',		variants: [[format: 'digital',	config: 'final', region: 'ww', args: '']]],
//		[name: 'server',		variants: [[format: 'files',	config: 'final', region: 'ww', args: '']]],
//		[name: 'linuxserver',	variants: [[format: 'digital',	config: 'final', region: 'ww', args: '']]]
    ]
    static List patchfrosty_matrix = [
//		[name: 'win64',	variants: [	[format: 'digital',	config: 'final', region: 'ww', args: '']]],
//		[name: 'ps4',	variants: [	[format: 'digital', config: 'final', region: 'eu', args: '']]], // Inactivating since the patch size is too large.
//		[name: 'xb1',	variants: [	[format: 'digital', config: 'final', region: 'ww', args: '']]]
    ]
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = []
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
