/**
 * PreflightEmailSending.groovy
 * Sends an email after a preflight to the preflighting user, with result and links to the jobs.
 */
void call() {
    emailext(
        to: '<EMAIL>',
        subject: '$DEFAULT_SUBJECT',
        body: '${SCRIPT, template="email-templates/email-pipeline-preflight.groovy"}',
        mimeType: 'text/html',
        presendScript: '${SCRIPT, template="email-templates/preflight-email-presend-pipeline.groovy"}'
    )
}
