"""
test_data_upgrade.py

Unit testing for data_upgrade
"""
import unittest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import call, MagicMock, patch
from dice_elipy_scripts.data_upgrade import cli


@patch("os.path.join", MagicMock(side_effect=lambda *args: "\\".join(args)))
@patch("elipy2.running_processes.kill", MagicMock())
@patch("dice_elipy_scripts.data_upgrade.add_sentry_tags", MagicMock())
class TestDataUpgrade(unittest.TestCase):
    OPTION_CODE_BRANCH = "--code-branch"
    OPTION_CODE_CHANGELIST = "--code-changelist"
    OPTION_DATA_CHANGELIST = "--data-changelist"
    OPTION_NO_SUBMIT = "--no-submit"
    OPTION_P4_PORT = "--p4-port"
    OPTION_P4_CLIENT = "--p4-client"
    OPTION_DATA_DIRECTORY = "--data-directory"
    OPTION_CLEAN = "--clean"
    OPTION_SUBMIT_FOLDER = "--submit-folder"

    VALUE_CODE_BRANCH = "code_branch"
    VALUE_CODE_CHANGELIST = "1234"
    VALUE_DATA_CHANGELIST = "5678"
    VALUE_P4_PORT = "p4_port"
    VALUE_P4_CLIENT = "p4_client"
    VALUE_DATA_DIRECTORY = "data_directory"
    VALUE_CLEAN = "true"
    VALUE_SUBMIT_FOLDER = "submit_folder"

    BASIC_ARGS = [
        OPTION_CODE_BRANCH,
        VALUE_CODE_BRANCH,
        OPTION_CODE_CHANGELIST,
        VALUE_CODE_CHANGELIST,
        OPTION_DATA_CHANGELIST,
        VALUE_DATA_CHANGELIST,
        OPTION_P4_PORT,
        VALUE_P4_PORT,
        OPTION_P4_CLIENT,
        VALUE_P4_CLIENT,
    ]

    def setUp(self):
        self.patcher_set_datadir = patch("elipy2.data.DataUtils.set_datadir")
        self.mock_set_datadir = self.patcher_set_datadir.start()

        self.patcher_codeutils = patch("elipy2.code.CodeUtils")
        self.mock_codeutils = self.patcher_codeutils.start()
        self.mock_codeutils.return_value = MagicMock()

        self.patcher_filerutils = patch("elipy2.filer.FilerUtils")
        self.mock_filerutils = self.patcher_filerutils.start()
        self.mock_filerutils.return_value = MagicMock()

        self.patcher_p4utils = patch("elipy2.p4.P4Utils")
        self.mock_p4utils = self.patcher_p4utils.start()
        self.mock_p4utils.return_value = MagicMock()

        self.patcher_set_licensee = patch("dice_elipy_scripts.data_upgrade.set_licensee")
        self.mock_set_licensee = self.patcher_set_licensee.start()
        self.mock_set_licensee.return_value = ["licensee_arg"]

        self.patcher_submit_integration = patch(
            "dice_elipy_scripts.data_upgrade.submit_integration"
        )
        self.mock_submit_integration = self.patcher_submit_integration.start()

        self.patcher_ensure_p4_config = patch("elipy2.core.ensure_p4_config")
        self.mock_ensure_p4_config = self.patcher_ensure_p4_config.start()

        self.patcher_core_run = patch("elipy2.core.run")
        self.mock_core_run = self.patcher_core_run.start()

        self.patcher_get_game_data_dir = patch("elipy2.frostbite_core.get_game_data_dir")
        self.mock_get_game_data_dir = self.patcher_get_game_data_dir.start()
        self.mock_get_game_data_dir.return_value = "game_data_dir"

    def tearDown(self):
        patch.stopall()

    def test_basic_run(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        assert self.mock_p4utils.return_value.revert.call_count == 2

    def test_data_directory(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_DATA_DIRECTORY, self.VALUE_DATA_DIRECTORY]
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_set_datadir.assert_called_once_with(self.VALUE_DATA_DIRECTORY)

    def test_fetch_binaries(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_code.assert_has_calls(
            [
                call(
                    self.VALUE_CODE_BRANCH,
                    self.VALUE_CODE_CHANGELIST,
                    "pipeline",
                    "release",
                    mirror=True,
                ),
                call(
                    self.VALUE_CODE_BRANCH,
                    self.VALUE_CODE_CHANGELIST,
                    "frosted",
                    "release",
                    mirror=False,
                ),
            ]
        )

    def test_clean(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_CLEAN, self.VALUE_CLEAN])
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_codeutils.return_value.clean_local.assert_called_once_with(close_handles=True)

    def test_run_exception(self):
        self.mock_core_run.side_effect = Exception()
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert self.mock_p4utils.return_value.revert.call_count == 2
        self.mock_p4utils.return_value.clean.assert_called_once_with(folder="game_data_dir/...")

    def test_submit_default(self):
        fdu_message = (
            f"Upgraded Data at CL#{self.VALUE_DATA_CHANGELIST} with Code at "
            + f"CL#{self.VALUE_CODE_CHANGELIST}.\n#branchguardian_bypass"
        )
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_submit_integration.assert_called_once_with(
            p4_object=self.mock_p4utils.return_value,
            submit_message=fdu_message,
            submit=True,
            data_upgrade=True,
            submit_folder=None,
        )

    def test_submit_reopen_file(self):
        fdu_message = (
            f"Upgraded Data at CL#{self.VALUE_DATA_CHANGELIST} with Code at "
            + f"CL#{self.VALUE_CODE_CHANGELIST}.\n#branchguardian_bypass"
        )
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_SUBMIT_FOLDER, self.VALUE_SUBMIT_FOLDER]
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_submit_integration.assert_called_once_with(
            p4_object=self.mock_p4utils.return_value,
            submit_message=fdu_message,
            submit=True,
            data_upgrade=True,
            submit_folder="submit_folder",
        )

    def test_no_submit(self):
        fdu_message = (
            f"Upgraded Data at CL#{self.VALUE_DATA_CHANGELIST} with Code at "
            + f"CL#{self.VALUE_CODE_CHANGELIST}.\n#branchguardian_bypass"
        )
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_NO_SUBMIT])
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_submit_integration.assert_called_once_with(
            p4_object=self.mock_p4utils.return_value,
            submit_message=fdu_message,
            submit=False,
            data_upgrade=True,
            submit_folder=None,
        )
