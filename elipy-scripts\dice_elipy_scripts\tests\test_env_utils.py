"""
test_env_utils.py

Unit testing for env_utils
"""
import unittest
from dice_elipy_scripts.utils.env_utils import env_string_to_dict, extract_fb_env_values


class TestEnvUtils(unittest.TestCase):
    def test_delete_empty_folders(self):
        env_string = "FOO=1,BAR=2"

        assert {"FOO": "1", "BAR": "2"} == env_string_to_dict(env_string)

    def test_extract_fb_env_values(self):
        values_list = ["key1=gensln:value1"]
        build_step = "gensln"
        expected_output = {"key1": "gensln:value1"}
        self.assertEqual(extract_fb_env_values(values_list, build_step), expected_output)

    def test_extract_fb_env_values_multiple_values(self):
        values_list = ["key1=gensln:value1", "key2=buildsln:value2"]
        build_step = "gensln"
        expected_output = {"key1": "gensln:value1", "key2": "buildsln:value2"}
        self.assertEqual(extract_fb_env_values(values_list, build_step), expected_output)

    def test_extract_fb_env_values_do_not_override_non_matching_build_step(self):
        values_list = ["key1=gensln:value1", "key1=buildsln:value2"]
        build_step = "gensln"
        expected_output = {"key1": "gensln:value1"}
        self.assertEqual(extract_fb_env_values(values_list, build_step), expected_output)

    def test_extract_fb_env_values_override_build_step(self):
        values_list = ["key1=gensln:value1", "key1=buildsln:value2"]
        build_step = "buildsln"
        expected_output = {"key1": "buildsln:value2"}
        self.assertEqual(extract_fb_env_values(values_list, build_step), expected_output)

    def test_extract_fb_env_values_throw_exception(self):
        env_string = ["key1"]
        build_step = "gensln"
        with self.assertRaises(ValueError):
            extract_fb_env_values(env_string, build_step)
