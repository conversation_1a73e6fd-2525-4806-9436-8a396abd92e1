import com.ea.lib.jobsettings.JobSetting
import com.ea.lib.jobsettings.PerforceCounterSettings
import spock.lang.Specification

class PerforceSettingsSpec extends Specification {

    class BranchFile {
        static Map standard_jobs_settings = [
            workspace_root      : 'workspace-root',
            elipy_call          : 'elipy-call',
            elipy_install_call  : 'elipy-install-call',
            job_label_statebuild: 'statebuild-label',
        ]
        static Map general_settings = [:]
    }

    class MasterFile {
        static Map autotest_branches = [branch: [data_branch: 'data-branch', code_branch: 'code-branch']]
    }

    class ProjectFile {
        static String p4_code_server = 'dice-p4buildedge02-fb.dice.ad.ea.com:2001'
        static String p4_code_client = 'jenkins-${NODE_NAME}-codestream'

        static String p4_data_server = 'dice-p4buildedge02-fb.dice.ad.ea.com:2001'
        static String p4_data_client = 'jenkins-${NODE_NAME}-codestream'

        static String p4_user_single_slash = '%USERDOMAIN%\\%USERNAME%'
    }

    void 'test that we get expected job settings in initializeAutotestStart'() {
        when:
        JobSetting settings = new PerforceCounterSettings()
        settings.initializeAutotestP4CounterUpdater(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            jobLabel == BranchFile.standard_jobs_settings.job_label_statebuild
            description == 'P4 counter update after data start job done.'
            elipyCmd == "elipy-call p4_counter --port ${ProjectFile.p4_code_server} --client ${ProjectFile.p4_code_client}" +
                " --user ${ProjectFile.p4_user_single_slash} --countername %code_countername% --value %code_changelist%" +
                " --extra-port ${ProjectFile.p4_data_server} --extra-client ${ProjectFile.p4_data_client}" +
                ' --extra-countername %data_countername% --extra-value %data_changelist%'
        }
    }
}
