ruleset {
        ruleset('rulesets/basic.xml')
        ruleset('rulesets/braces.xml')
        ruleset('rulesets/comments.xml') {
                ClassJavadoc(enabled: false)
        }
        ruleset('rulesets/concurrency.xml')
        ruleset('rulesets/convention.xml') {
                CompileStatic(enabled: false)
                CouldBeSwitchStatement(enabled: false)
                ImplicitClosureParameter(enabled: false)
                MethodParameterTypeRequired(enabled: false)
                NoDef(enabled: false)
                NoTabCharacter(enabled: false)
                VariableTypeRequired(enabled: false)
        }
        ruleset('rulesets/design.xml') {
                Instanceof(enabled: false)
                NestedForLoop(enabled: false)
        }
        ruleset('rulesets/exceptions.xml') {
                CatchException(enabled: false)
        }
        ruleset('rulesets/formatting.xml'){
                ClassEndsWithBlankLine(enabled: false)
                ClassStartsWithBlankLine(enabled: false)
                LineLength(enabled: false)
                SpaceAfterComma(enabled: false)
                SpaceAfterOpeningBrace(enabled: false)
                SpaceAroundClosureArrow(enabled: false)
                SpaceAroundMapEntryColon(enabled: false)
                SpaceAroundOperator(enabled: false)
                SpaceBeforeClosingBrace(enabled: false)
        }
        ruleset('rulesets/generic.xml') {
                IllegalRegex(
                        name: 'Do not use Jenkins.getInstance/Jenkins.instance',
                        regex: 'Jenkins.(getInstance|instance)'
                )
        }
        ruleset('rulesets/groovyism.xml') {
                AssignCollectionSort(enabled: false)
                GStringExpressionWithinString(enabled: false)
        }
        ruleset('rulesets/imports.xml')
        ruleset('rulesets/logging.xml')
        ruleset('rulesets/naming.xml') {
                // For Spock tests naming conventions
                // https://codenarc.org/codenarc-config-for-frameworks.html
                MethodName(doNotApplyToClassNames: '*Spec,*Specification')
                /* These are all rules we'd like to enforce, but there's to much work to change it in the codebase without much
                gain. Instead please refer to the Java Naming Conventions
                (https://www.oracle.com/java/technologies/javase/codeconventions-namingconventions.html) when refactoring and
                writing new code. */
                FieldName(enabled: false)
                ParameterName(enabled: false)
                PropertyName(enabled: false)
                VariableName(enabled: false)
        }
        ruleset('rulesets/security.xml') {
                        JavaIoPackageAccess(doNotApplyToFilesMatching: /.*(Spec|Test(s|Case|Util)?)\.groovy/)
                }
        ruleset('rulesets/serialization.xml')
        ruleset('rulesets/unnecessary.xml') {
                UnnecessaryBooleanExpression(enabled: false)
                UnnecessaryGetter(ignoreMethodNames: 'isEmpty')
                UnnecessaryReturnKeyword(enabled: false)
        }
        ruleset('rulesets/unused.xml')
}
