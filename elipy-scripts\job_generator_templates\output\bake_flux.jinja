{#
    Command:
        bake_flux
            short_help: Calculate irradiance using Flux and store it into static databases.

    Arguments:

    Required variables:
        level
            help: Level(s) to use. Repeatable option.
            multiple: True
            required: True
            type: str
        variation
            help: Variation(s) to use. Repeatable option.
            multiple: True
            required: True
            type: str
        code_branch
            required: True
            help: Perforce branch/stream name.
            type: str
        code_changelist
            required: True
            help: Perforce changelist number.
            type: str
        script_path
            required: True
            help: ''
            type: str
        p4_port
            required: True
        p4_client
            required: True

    Optional variables:
        proxyterrain
            help: Proxyterrain to use.
            required: False
            default: False
            type: bool
        zone
            help: Zone(s) to build. Repeatable option.
            required: False
            default: ''
            multiple: True
            type: str
        dry_run
            is_flag: True
            help: Don't submit the bake.
        user
            default: None
            help: Perforce user name.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    bake_flux
    --level {{ level }}
    --variation {{ variation }}
    --code-branch {{ code_branch }}
    --code-changelist {{ code_changelist }}
    --script-path {{ script_path }}
    --p4-port {{ p4_port }}
    --p4-client {{ p4_client }}
    {%- if proxyterrain %}
    --proxyterrain {{ proxyterrain }}
    {%- endif %}
    {%- if zone %}
    --zone {{ zone }}
    {%- endif %}
    {%- if dry_run %}
    --dry-run {{ dry_run }}
    {%- endif %}
    {%- if user %}
    --user {{ user }}
    {%- endif %}
  displayName: elipy bake_flux
