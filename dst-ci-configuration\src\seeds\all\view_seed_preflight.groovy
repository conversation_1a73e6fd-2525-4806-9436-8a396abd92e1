package all

import com.ea.lib.LibJobDsl
import com.ea.project.GetBranchFile
import com.ea.project.GetMasterFile
import jenkins.model.Jenkins

GetMasterFile.get_masterfile(BUILD_URL).each { masterSettings ->
    def branches = masterSettings.preflight_branches
    branches.each { branch_name, branch_info ->
        out.println("   Processing branch: $branch_name")
        def project = masterSettings.project
        if (com.ea.project.all.All.isAssignableFrom(project)) {
            project = branch_info.project
            out.println("       Is assignable from: $project")
        }
        def branchfile = GetBranchFile.get_branchfile(project.name, branch_name)
        out.println("       branchFile: $branchfile")
        def code_preflight_matrix = branchfile.code_preflight_matrix
        def data_preflight_matrix = branchfile.data_preflight_matrix

        //*******************************************************************************************
        // Code Preflight
        //*******************************************************************************************
        if (!code_preflight_matrix.isEmpty()) {
            out.println('        creating code preflight view')
            sectionedView("Code Preflight ${branch_name}") {
                filterBuildQueue()
                filterExecutors()
                sections {
                    listView {
                        name('Schedulers')
                        jobs {
                            regex("${branch_name}.(code.preflight|codepreflight).start")
                        }
                        columns {
                            status()
                            weather()
                            name()
                            lastSuccess()
                            lastFailure()
                            lastDuration()
                        }
                    }
                    listView {
                        name('Preflight')
                        jobs {
                            regex("${branch_name}.code.preflight.(?!scheduler|seed|template|start).*")
                        }
                        columns {
                            status()
                            weather()
                            name()
                            lastSuccess()
                            lastFailure()
                            lastDuration()
                        }
                    }
                    listView {
                        name('Preflight Maintenance')
                        jobs {
                            regex("maintenance.${branch_name}.code.postpreflight.*")
                        }
                        columns {
                            status()
                            weather()
                            name()
                            lastSuccess()
                            lastFailure()
                            lastDuration()
                        }
                    }
                    listView {
                        name('Others')
                        jobs {
                            name("${branch_name}.code.lastknowngood.*")
                        }
                        columns {
                            status()
                            weather()
                            name()
                            lastSuccess()
                            lastFailure()
                        }
                    }
                }
            }

            // buildMonitorView("Code Preflight Dashboard ${branch_name}") {
            // 	description("All jobs for code preflight for ${branch_name}")
            // 	jobs {
            // 		name("Code Preflight for ${branch_name}")
            // 		regex("${branch_name}.code.preflight.*")
            // 	}
            // }
        }

        //*******************************************************************************************
        // Data Preflight
        //*******************************************************************************************
        if (!data_preflight_matrix.isEmpty()) {
            out.println('        creating data preflight view')
            sectionedView("Data Preflight ${branch_name}") {
                filterBuildQueue()
                filterExecutors()
                sections {
                    listView {
                        name('Schedulers')
                        jobs {
                            regex("${branch_name}.datapreflight.start")
                        }
                        columns {
                            status()
                            weather()
                            name()
                            lastSuccess()
                            lastFailure()
                            lastDuration()
                        }
                    }
                    listView {
                        name('Preflight')
                        jobs {
                            regex("${branch_name}.*data.preflight.(?!scheduler|seed|template|start).*")
                        }
                        columns {
                            status()
                            weather()
                            name()
                            lastSuccess()
                            lastFailure()
                            lastDuration()
                        }
                    }
                    listView {
                        name('Preflight Maintenance')
                        jobs {
                            regex("(maintenance.${branch_name}.data.prepreflight.*)|(maintenance.${branch_name}.data.postpreflight.*)")
                        }
                        columns {
                            status()
                            weather()
                            name()
                            lastSuccess()
                            lastFailure()
                            lastDuration()
                        }
                    }
                    listView {
                        name('Others')
                        jobs {
                            name("${branch_name}.data.lastknowngood.*")
                        }
                        columns {
                            status()
                            weather()
                            name()
                            lastSuccess()
                            lastFailure()
                        }
                    }
                }
            }

            // buildMonitorView("Data Preflight Dashboard ${branch_name}") {
            // 	description("All jobs for data preflight for ${branch_name}")
            // 	jobs {
            // 		name("Data Preflight for ${branch_name}")
            // 		regex("${branch_name}.*data.preflight.*")
            // 	}
            // }
        }
    }

    //Set the default view. NOTE: the views are created after JobDSL is finished. This means that the second run will do the actual set.
    LibJobDsl.setPrimaryView(Jenkins.get(), branches.keySet()[0])
}
