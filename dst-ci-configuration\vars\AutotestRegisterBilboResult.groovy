import com.cloudbees.groovy.cps.NonCPS
import com.ea.lib.model.JobReference
import hudson.model.Result

/**
 * Reports that all tests have run in the given testCategory
 * @param runBilbo whether or not to run this function
 * @param registerSmoke whether to mark the build as smoked
 * @param status the status to report to <PERSON>il<PERSON>
 * @param branchName the branch the tests ran on
 * @param dataset the dataset to regist
 * @param buildSelectorResult which changelists and platforms the tests ran on
 * @param testDefinition The test definition
 * @param currentResult the job's Result
 * @param jobReferences jobReferences to retry on failure
 * @return modified finalResult
 */
Result call(boolean runBilbo, boolean registerSmoke, String status, String branchName, String dataset, Map<String, Map> buildSelectorResult,
            String testDefinition, def currentResult, List<JobReference> jobReferences) {
    def finalResult = currentResult
    if (runBilbo) {
        def jobs = [:]
        List<Map> uniqueChangelists = getUniqueChangelists(buildSelectorResult)
        uniqueChangelists.each { Map changelists ->
            def bilboJobName = branchName + '.bilbo.register-' + dataset + '-autotestutils'
            jobs["${bilboJobName}.${changelists.dataChangelist}.${changelists.codeChangelist}"] = {
                def args = [
                    string(name: 'test_definition', value: testDefinition),
                    string(name: 'code_changelist', value: changelists.codeChangelist),
                    string(name: 'data_changelist', value: changelists.dataChangelist),
                    string(name: 'status', value: status),
                    booleanParam(name: 'run_bilbo', value: runBilbo),
                    booleanParam(name: 'register_smoke', value: registerSmoke),
                ]
                def bilboRegister = build(job: bilboJobName, parameters: args, propagate: false)
                jobReferences << new JobReference(downstreamJob: bilboRegister, jobName: bilboJobName, parameters: args, propagate: false)
            }
        }
        parallel(jobs)
    }
    return finalResult
}

/**
 * AutotestRegisterBilboResult.groovy
 * Returns a flattened ArrayList consisting of unique changelist combinations
 * @param buildSelectorResult The map to flatten
 * @return A flattened ArrayList with unique changelist combinations
 */
@NonCPS
private static List<Map> getUniqueChangelists(Map<String, Map> buildSelectorResult) {
    List<Map> flattenedChangelists = buildSelectorResult.collectMany { [it.value] }
    return flattenedChangelists.unique { a, b ->
        a.dataChangelist == b.dataChangelist && a.codeChangelist == b.codeChangelist ? 0 : 1
    }
}
