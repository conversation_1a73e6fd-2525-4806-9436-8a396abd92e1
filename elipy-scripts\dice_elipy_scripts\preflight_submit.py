"""
preflight_submit.py
"""
import os

import click
from elipy2 import LOGGER, p4
from elipy2.cli import pass_context
from elipy2.exceptions import ELIPYException
from elipy2.telemetry import collect_metrics

from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags


@click.command("preflight_submit")
@click.argument("p4_port")
@click.argument("p4_client")
@click.argument("pending_changelist")
@click.option("--user", default=None, help="Perforce user name.")
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(_, p4_port, p4_client, pending_changelist, user):
    """
    Checks if user is caged, appends text to description that the change passed verification and
    then submits on behalf of user.
    """
    add_sentry_tags(__file__)
    perforce = p4.P4Utils(port=p4_port, user=user, client=p4_client)
    try:
        if perforce.append_swarm_review_description_for(
            pending_changelist,
            (
                f"\n\nPreflight: {os.environ.get('SYSTEM_TEAMFOUNDATIONCOLLECTIONURI')}"
                f"{os.environ.get('SYSTEM_TEAMPROJECT')}/_build/results?buildId="
                f"{os.environ.get('BUILD_BUILDID')}"
            ),
        ):
            url_help = (
                f"Go to {perforce.get_swarm_web_url_for(pending_changelist)} and submit manually"
            )

            if not perforce.is_user_caged_for(pending_changelist):
                if not perforce.submit_swarm_review_for(pending_changelist):
                    raise ELIPYException(f"Submission failed. {url_help}.")
            else:
                raise ELIPYException(
                    f"Preflighter is caged. {url_help} once the cage issue has been resolved."
                )
        else:
            raise ELIPYException("Appending changelist description failed.")

    except Exception as error:
        LOGGER.error("Submission failed, but your change is still good. The error was: %s", error)
