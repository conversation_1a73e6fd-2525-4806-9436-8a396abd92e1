"""
test_state_utils.py

Unit testing for state_utils
"""
import os
from mock import MagicMock, patch
from dice_elipy_scripts.utils import state_utils


class TestStateUtils:
    @patch("elipy2.filer_paths.get_code_build_path", MagicMock())
    @patch("os.path.isdir")
    @patch("elipy2.filer.FilerUtils")
    @patch("elipy2.code.CodeUtils")
    def test_import_local_code_state(self, mock_codeutils, mock_filerutils, mock_isdir):
        mock_codeutils.return_value = MagicMock()
        mock_filerutils.return_value = MagicMock()
        mock_filerutils.return_value.import_tnt_local_build.return_value = "1234"
        mock_isdir.side_effect = [True, False]
        state_utils.import_local_code_state(
            mock_codeutils.return_value,
            mock_filerutils.return_value,
            "code_branch",
            "ps5",
            "config",
            False,
        )
        mock_codeutils.return_value.clean_local.assert_called_once_with(close_handles=True)
        mock_filerutils.return_value.fetch_code.assert_called_once_with(
            "code_branch",
            "1234",
            "ps5",
            "config",
            nomaster=False,
            fbenv_copy_args=["/XF", "flag_to_include_incremental_state"],
        )

    @patch("elipy2.filer_paths.get_code_build_path", MagicMock())
    @patch("os.path.isdir")
    @patch("elipy2.filer.FilerUtils")
    @patch("elipy2.code.CodeUtils")
    def test_import_local_code_state_tool(self, mock_codeutils, mock_filerutils, mock_isdir):
        mock_codeutils.return_value = MagicMock()
        mock_filerutils.return_value = MagicMock()
        mock_filerutils.return_value.import_tnt_local_build.return_value = "1234"
        mock_isdir.side_effect = [True, True]
        state_utils.import_local_code_state(
            mock_codeutils.return_value,
            mock_filerutils.return_value,
            "code_branch",
            "tool",
            "config",
            False,
        )
        mock_codeutils.return_value.clean_local.assert_called_once_with(close_handles=True)
        mock_filerutils.return_value.fetch_code.assert_called_once_with(
            "code_branch",
            "1234",
            "tool",
            "config",
            nomaster=False,
            fbenv_copy_args=["/XF", "flag_to_include_incremental_state"],
        )

    @patch("elipy2.filer_paths.get_code_build_path", MagicMock())
    @patch("os.path.isdir")
    @patch("elipy2.filer.FilerUtils")
    @patch("elipy2.code.CodeUtils")
    def test_import_local_code_state_nomaster(self, mock_codeutils, mock_filerutils, mock_isdir):
        mock_codeutils.return_value = MagicMock()
        mock_filerutils.return_value = MagicMock()
        mock_filerutils.return_value.import_tnt_local_build.return_value = "1234"
        mock_isdir.side_effect = [True, False]
        state_utils.import_local_code_state(
            mock_codeutils.return_value,
            mock_filerutils.return_value,
            "code_branch",
            "ps5",
            "config",
            True,
        )
        mock_filerutils.return_value.fetch_code.assert_called_once_with(
            "code_branch",
            "1234",
            "ps5",
            "config",
            nomaster=True,
            fbenv_copy_args=["/XF", "flag_to_include_incremental_state"],
        )

    @patch("elipy2.filer_paths.get_code_build_path", MagicMock())
    @patch("elipy2.frostbite_core.minimum_fb_version")
    @patch("os.path.isdir")
    @patch("elipy2.filer.FilerUtils")
    @patch("elipy2.code.CodeUtils")
    def test_import_local_code_state_old_fbenv(
        self, mock_codeutils, mock_filerutils, mock_isdir, mock_minimum_fb_version
    ):
        mock_codeutils.return_value = MagicMock()
        mock_filerutils.return_value = MagicMock()
        mock_filerutils.return_value.import_tnt_local_build.return_value = "1234"
        mock_isdir.side_effect = [True, False]
        mock_minimum_fb_version.return_value = False
        state_utils.import_local_code_state(
            mock_codeutils.return_value,
            mock_filerutils.return_value,
            "code_branch",
            "ps5",
            "config",
            False,
        )
        assert mock_codeutils.return_value.clean_local.call_count == 0

    @patch("elipy2.filer_paths.get_code_build_path", MagicMock())
    @patch("os.path.isdir")
    @patch("elipy2.filer.FilerUtils")
    @patch("elipy2.code.CodeUtils")
    def test_import_local_code_state_dir_not_found(
        self, mock_codeutils, mock_filerutils, mock_isdir
    ):
        mock_codeutils.return_value = MagicMock()
        mock_filerutils.return_value = MagicMock()
        mock_filerutils.return_value.import_tnt_local_build.return_value = "1234"
        mock_isdir.side_effect = [False, False]
        state_utils.import_local_code_state(
            mock_codeutils.return_value,
            mock_filerutils.return_value,
            "code_branch",
            "ps5",
            "config",
            False,
        )
        mock_codeutils.return_value.clean_local.assert_called_once_with(close_handles=True)
        assert mock_filerutils.return_value.fetch_code.call_count == 0

    @patch("elipy2.filer_paths.get_code_build_path", MagicMock())
    @patch("os.path.isdir")
    @patch("elipy2.filer.FilerUtils")
    @patch("elipy2.code.CodeUtils")
    def test_import_local_code_state_exception(self, mock_codeutils, mock_filerutils, mock_isdir):
        mock_codeutils.return_value = MagicMock()
        mock_filerutils.return_value = MagicMock()
        mock_filerutils.return_value.import_tnt_local_build.return_value = "1234"
        mock_isdir.side_effect = [True, False]
        mock_filerutils.return_value.fetch_code.side_effect = Exception()
        state_utils.import_local_code_state(
            mock_codeutils.return_value,
            mock_filerutils.return_value,
            "code_branch",
            "ps5",
            "config",
            False,
        )
        assert mock_codeutils.return_value.clean_local.call_count == 2

    @patch("elipy2.avalanche.db_exists")
    @patch("elipy2.avalanche.get_temp_db_name", MagicMock())
    @patch("elipy2.filer.FilerUtils")
    def test_import_avalanche_data_state_db_exists(
        self, mock_filerutils, mock_db_exists, fixture_metadata_manager
    ):
        mock_filerutils.return_value = MagicMock()
        fixture_metadata_manager.get_last_successful.return_value = {
            "code_changelist": "1234",
            "data_changelist": "5678",
            "remote_host": ["source_host", "host_data"],
        }
        mock_db_exists.return_value = True
        assert (
            state_utils.import_avalanche_data_state(
                "data_branch",
                "code_branch",
                "platform",
                mock_filerutils.return_value,
                "data_changelist",
            )
            == []
        )
        fixture_metadata_manager.get_last_successful.assert_called_once_with(
            build_type="avalanchestate", branch="data_branch", platform="platform"
        )

    @patch("elipy2.avalanche.db_exists")
    @patch("elipy2.avalanche.get_temp_db_name", MagicMock())
    @patch("elipy2.filer.FilerUtils")
    def test_import_avalanche_data_state_no_data_branch_specified(
        self, mock_filerutils, mock_db_exists, fixture_metadata_manager
    ):
        mock_filerutils.return_value = MagicMock()
        fixture_metadata_manager.get_last_successful.return_value = {
            "code_changelist": "1234",
            "data_changelist": "5678",
            "remote_host": ["source_host", "host_data"],
        }
        mock_db_exists.return_value = True
        assert (
            state_utils.import_avalanche_data_state(
                "",
                "code_branch",
                "platform",
                mock_filerutils.return_value,
                "data_changelist",
            )
            == []
        )
        fixture_metadata_manager.get_last_successful.assert_called_once_with(
            build_type="avalanchestate", branch="code_branch", platform="platform"
        )

    @patch("elipy2.avalanche.check_avalanche_service_api")
    @patch("elipy2.avalanche.db_exists")
    @patch("elipy2.avalanche.get_temp_db_name", MagicMock())
    @patch("elipy2.filer.FilerUtils")
    def test_import_avalanche_data_state_not_running(
        self, mock_filerutils, mock_db_exists, mock_service_api, fixture_metadata_manager
    ):
        mock_filerutils.return_value = MagicMock()
        fixture_metadata_manager.get_last_successful.return_value = {
            "code_changelist": "1234",
            "data_changelist": "5678",
            "remote_host": ["source_host", "host_data"],
        }
        mock_db_exists.return_value = False
        mock_service_api.return_value = False
        assert (
            state_utils.import_avalanche_data_state(
                "data_branch",
                "code_branch",
                "platform",
                mock_filerutils.return_value,
                "data_changelist",
            )
            == []
        )

    @patch("elipy2.avalanche.remote_clone_db")
    @patch("elipy2.windows_tools.get_computer_name")
    @patch("elipy2.avalanche.check_avalanche_service_api")
    @patch("elipy2.avalanche.db_exists")
    @patch("elipy2.avalanche.get_temp_db_name")
    @patch("elipy2.filer.FilerUtils")
    def test_import_avalanche_data_state_remote_clone(
        self,
        mock_filerutils,
        mock_db_name,
        mock_db_exists,
        mock_service_api,
        mock_computer_name,
        mock_remote_clone_db,
        fixture_metadata_manager,
    ):
        mock_filerutils.return_value = MagicMock()
        fixture_metadata_manager.get_last_successful.return_value = {
            "code_changelist": "1234",
            "data_changelist": "5678",
            "remote_host": ["source_host", "host_data"],
        }
        mock_db_name.return_value = "db_name"
        mock_db_exists.return_value = False
        mock_service_api.return_value = True
        mock_computer_name.return_value = "computer_name"
        assert state_utils.import_avalanche_data_state(
            "data_branch",
            "code_branch",
            "platform",
            mock_filerutils.return_value,
            "data_changelist",
        ) == ["-importState", "db_name@computer_name"]
        mock_remote_clone_db.assert_called_once_with(
            source_db="db_name",
            dest_db="db_name",
            dest_host="computer_name",
            source_host="source_host",
            push_built_levels=False,
            complete_clone=True,
        )

    @patch("elipy2.avalanche.import_avalanche_state")
    @patch("elipy2.filer.FilerUtils")
    def test_import_avalanche_data_state_remote_host_not_existing(
        self, mock_filerutils, mock_import_state, fixture_metadata_manager
    ):
        mock_filerutils.return_value = MagicMock()
        fixture_metadata_manager.get_last_successful.return_value = {
            "code_changelist": "1234",
            "data_changelist": "5678",
        }
        mock_import_state.return_value = ["avalanche_arg"]
        assert state_utils.import_avalanche_data_state(
            "data_branch",
            "code_branch",
            "platform",
            mock_filerutils.return_value,
            "data_changelist",
        ) == ["avalanche_arg"]

    @patch("elipy2.filer.FilerUtils")
    def test_import_avalanche_data_state_changelists_missing(
        self, mock_filerutils, fixture_metadata_manager
    ):
        mock_filerutils.return_value = MagicMock()
        fixture_metadata_manager.get_last_successful.return_value = {
            "remote_host": ["source_host", "host_data"],
        }
        assert (
            state_utils.import_avalanche_data_state(
                "data_branch",
                "code_branch",
                "platform",
                mock_filerutils.return_value,
                "data_changelist",
            )
            == []
        )

    @patch("elipy2.filer.FilerUtils")
    def test_import_avalanche_data_state_exception(self, mock_filerutils, fixture_metadata_manager):
        mock_filerutils.return_value = MagicMock()
        fixture_metadata_manager.get_last_successful.side_effect = Exception()
        assert (
            state_utils.import_avalanche_data_state(
                "data_branch",
                "code_branch",
                "platform",
                mock_filerutils.return_value,
                "data_changelist",
            )
            == []
        )

    @patch("elipy2.avalanche.get_ops_chain")
    @patch("elipy2.avalanche.get_built_levels")
    @patch("elipy2.avalanche.get_avalanche_platform_name")
    @patch("elipy2.avalanche.deploy")
    @patch("elipy2.avalanche.get_full_database_name")
    def test_export_head_bundles(
        self, mock_db_name, mock_deploy, mock_platform_name, mock_built_levels, mock_ops_chain
    ):
        mock_db_name.return_value = "full_db_name"
        mock_platform_name.return_value = "Ps5"
        state_utils.export_head_bundles("ps5", "some_location")
        mock_deploy.assert_called_once_with(
            "full_db_name",
            "Ps5",
            os.path.join("some_location", "data"),
            extra_args=[],
            include_platform=True,
            ordering_algorithm="breadthFirst",
        )
        mock_built_levels.assert_called_once_with(
            "full_db_name", to_file=os.path.join("some_location", "Data", "builtLevels.json")
        )
        mock_ops_chain.assert_called_once_with(
            "full_db_name", to_file=os.path.join("some_location", "Data", "ops_chain")
        )

    @patch("elipy2.avalanche.get_ops_chain", MagicMock())
    @patch("elipy2.avalanche.get_built_levels", MagicMock())
    @patch("elipy2.avalanche.get_avalanche_platform_name")
    @patch("elipy2.avalanche.deploy")
    @patch("elipy2.avalanche.get_full_database_name")
    def test_export_head_bundles_extra_args(self, mock_db_name, mock_deploy, mock_platform_name):
        mock_db_name.return_value = "full_db_name"
        mock_platform_name.return_value = "Ps5"
        state_utils.export_head_bundles("ps5", "some_location", deploy_extra_args=["some_arg"])
        mock_deploy.assert_called_once_with(
            "full_db_name",
            "Ps5",
            os.path.join("some_location", "data"),
            extra_args=["some_arg"],
            include_platform=True,
            ordering_algorithm="breadthFirst",
        )

    @patch("elipy2.avalanche.get_ops_chain", MagicMock())
    @patch("elipy2.avalanche.get_built_levels", MagicMock())
    @patch("elipy2.avalanche.get_avalanche_platform_name")
    @patch("elipy2.avalanche.deploy")
    @patch("elipy2.avalanche.get_full_database_name")
    def test_export_head_bundles_skip_platform(self, mock_db_name, mock_deploy, mock_platform_name):
        mock_db_name.return_value = "full_db_name"
        mock_platform_name.return_value = "Ps5"
        state_utils.export_head_bundles("ps5", "some_location", include_platform=False)
        mock_deploy.assert_called_once_with(
            "full_db_name",
            "Ps5",
            os.path.join("some_location", "data"),
            extra_args=[],
            include_platform=False,
            ordering_algorithm="breadthFirst",
        )

    @patch("elipy2.avalanche.get_ops_chain", MagicMock())
    @patch("elipy2.avalanche.get_built_levels", MagicMock())
    @patch("elipy2.avalanche.get_avalanche_platform_name")
    @patch("elipy2.avalanche.deploy")
    @patch("elipy2.avalanche.get_full_database_name")
    def test_export_head_bundles_specify_ordering_algorithm(
        self, mock_db_name, mock_deploy, mock_platform_name
    ):
        mock_db_name.return_value = "full_db_name"
        mock_platform_name.return_value = "Ps5"
        state_utils.export_head_bundles("ps5", "some_location", ordering_algorithm="some_algorithm")
        mock_deploy.assert_called_once_with(
            "full_db_name",
            "Ps5",
            os.path.join("some_location", "data"),
            extra_args=[],
            include_platform=True,
            ordering_algorithm="some_algorithm",
        )

    @patch("elipy2.avalanche.get_ops_chain", MagicMock())
    @patch("elipy2.avalanche.get_built_levels", MagicMock())
    @patch("elipy2.avalanche.get_avalanche_platform_name")
    @patch("elipy2.avalanche.deploy")
    @patch("elipy2.avalanche.get_full_database_name")
    def test_export_head_bundles_skip_ordering_algorithm(
        self, mock_db_name, mock_deploy, mock_platform_name
    ):
        mock_db_name.return_value = "full_db_name"
        mock_platform_name.return_value = "Ps5"
        state_utils.export_head_bundles("ps5", "some_location", ordering_algorithm=None)
        mock_deploy.assert_called_once_with(
            "full_db_name",
            "Ps5",
            os.path.join("some_location", "data"),
            extra_args=[],
            include_platform=True,
            ordering_algorithm=None,
        )
