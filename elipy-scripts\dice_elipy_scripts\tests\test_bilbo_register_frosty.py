"""
test_bilbo_register_frosty.py

Unit testing for bilbo_register_frosty
"""
import os
import unittest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import patch, MagicMock
from dice_elipy_scripts.bilbo_register_frosty import cli
from elipy2.config import ConfigManager

config_path = os.path.join(os.path.dirname(__file__), "data", "elipy_test.yml")
config_manager = ConfigManager(path=config_path)


@patch("elipy2.telemetry.upload_metrics", MagicMock())
@patch("dice_elipy_scripts.bilbo_register_frosty.add_sentry_tags", MagicMock())
class TestBilboRegisterFrosty(unittest.TestCase):
    OPTION_CODE_BRANCH = "--code-branch"
    OPTION_CODE_CHANGELIST = "--code-changelist"
    OPTION_DATA_BRANCH = "--data-branch"
    OPTION_DATA_CHANGELIST = "--data-changelist"
    OPTION_DATASET = "--dataset"
    OPTION_PLATFORM = "--platform"
    OPTION_PACKAGE_TYPE = "--package-type"
    OPTION_REGION = "--region"
    OPTION_CONFIG = "--config"
    OPTION_COMBINE_CODE_BRANCH = "--combine-code-branch"
    OPTION_COMBINE_CODE_CHANGELIST = "--combine-code-changelist"
    OPTION_COMBINE_DATA_BRANCH = "--combine-data-branch"
    OPTION_COMBINE_DATA_CHANGELIST = "--combine-data-changelist"

    VALUE_CODE_BRANCH = "some-branch"
    VALUE_CODE_CHANGELIST = "1234"
    VALUE_DATA_BRANCH = "other-branch"
    VALUE_DATA_CHANGELIST = "5678"
    VALUE_DATASET = "TestData"
    VALUE_PLATFORM = "win64"
    VALUE_PACKAGE_TYPE = "files"
    VALUE_REGION = "ww"
    VALUE_CONFIG = "retail"
    VALUE_COMBINE_CODE_BRANCH = "combine-code-branch"
    VALUE_COMBINE_CODE_CHANGELIST = "9876"
    VALUE_COMBINE_DATA_BRANCH = "combine-data-branch"
    VALUE_COMBINE_DATA_CHANGELIST = "5432"

    DEFAULT_ARGS = [
        OPTION_CODE_BRANCH,
        VALUE_CODE_BRANCH,
        OPTION_CODE_CHANGELIST,
        VALUE_CODE_CHANGELIST,
        OPTION_DATA_BRANCH,
        VALUE_DATA_BRANCH,
        OPTION_DATA_CHANGELIST,
        VALUE_DATA_CHANGELIST,
        OPTION_DATASET,
        VALUE_DATASET,
        OPTION_PLATFORM,
        VALUE_PLATFORM,
        OPTION_PACKAGE_TYPE,
        VALUE_PACKAGE_TYPE,
        OPTION_REGION,
        VALUE_REGION,
        OPTION_CONFIG,
        VALUE_CONFIG,
    ]

    def setUp(self):
        self.patcher_setup_metadata_manager = patch(
            "elipy2.build_metadata_utils.setup_metadata_manager"
        )
        self.mock_setup_metadata_manager = self.patcher_setup_metadata_manager.start()
        self.mock_metadata_manager = MagicMock()
        self.mock_setup_metadata_manager.return_value = self.mock_metadata_manager

        self.patcher_get_frosty_build_path = patch("elipy2.filer_paths.get_frosty_build_path")
        self.mock_get_frosty_build_path = self.patcher_get_frosty_build_path.start()
        self.mock_get_frosty_build_path.return_value = "/mocked/frosty/build/path"

    def tearDown(self):
        patch.stopall()

    def test_basic_registration(self):
        """Test basic registration without combine parameters"""
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)

        assert result.exit_code == 0
        self.mock_get_frosty_build_path.assert_called_once_with(
            data_branch=self.VALUE_DATA_BRANCH,
            data_changelist=self.VALUE_DATA_CHANGELIST,
            code_branch=self.VALUE_CODE_BRANCH,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            platform=self.VALUE_PLATFORM,
            package_type=self.VALUE_PACKAGE_TYPE,
            region=self.VALUE_REGION,
            config=self.VALUE_CONFIG,
        )

        self.mock_metadata_manager.register_frosty_build.assert_called_once_with(
            "/mocked/frosty/build/path",
            data_changelist=self.VALUE_DATA_CHANGELIST,
            data_branch=self.VALUE_DATA_BRANCH,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            code_branch=self.VALUE_CODE_BRANCH,
            platform=self.VALUE_PLATFORM,
            package_type=self.VALUE_PACKAGE_TYPE,
            region=self.VALUE_REGION,
            config=self.VALUE_CONFIG,
            dataset=self.VALUE_DATASET,
            combine_data_changelist=None,
            combine_data_branch=None,
            combine_code_changelist=None,
            combine_code_branch=None,
        )

    def test_with_combine_parameters(self):
        """Test registration with all combine parameters provided"""
        runner = CliRunner()
        args = self.DEFAULT_ARGS + [
            self.OPTION_COMBINE_CODE_BRANCH,
            self.VALUE_COMBINE_CODE_BRANCH,
            self.OPTION_COMBINE_CODE_CHANGELIST,
            self.VALUE_COMBINE_CODE_CHANGELIST,
            self.OPTION_COMBINE_DATA_BRANCH,
            self.VALUE_COMBINE_DATA_BRANCH,
            self.OPTION_COMBINE_DATA_CHANGELIST,
            self.VALUE_COMBINE_DATA_CHANGELIST,
        ]
        result = runner.invoke(cli, args)

        assert result.exit_code == 0
        self.mock_get_frosty_build_path.assert_called_once_with(
            data_branch=self.VALUE_DATA_BRANCH,
            data_changelist=self.VALUE_DATA_CHANGELIST,
            code_branch=self.VALUE_CODE_BRANCH,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            platform=self.VALUE_PLATFORM,
            package_type=self.VALUE_PACKAGE_TYPE + "_combine",
            region=self.VALUE_REGION,
            config=self.VALUE_CONFIG,
            combine_data_branch=self.VALUE_COMBINE_DATA_BRANCH,
            combine_data_changelist=self.VALUE_COMBINE_DATA_CHANGELIST,
            combine_code_branch=self.VALUE_COMBINE_CODE_BRANCH,
            combine_code_changelist=self.VALUE_COMBINE_CODE_CHANGELIST,
        )

        self.mock_metadata_manager.register_frosty_build.assert_called_once_with(
            "/mocked/frosty/build/path",
            data_changelist=self.VALUE_DATA_CHANGELIST,
            data_branch=self.VALUE_DATA_BRANCH,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            code_branch=self.VALUE_CODE_BRANCH,
            platform=self.VALUE_PLATFORM,
            package_type=self.VALUE_PACKAGE_TYPE,
            region=self.VALUE_REGION,
            config=self.VALUE_CONFIG,
            dataset=self.VALUE_DATASET,
            combine_data_changelist=self.VALUE_COMBINE_DATA_CHANGELIST,
            combine_data_branch=self.VALUE_COMBINE_DATA_BRANCH,
            combine_code_changelist=self.VALUE_COMBINE_CODE_CHANGELIST,
            combine_code_branch=self.VALUE_COMBINE_CODE_BRANCH,
        )

    def test_with_partial_combine_parameters(self):
        """Test that partial combine parameters are handled correctly"""
        runner = CliRunner()
        # Only provide code branch and changelist, not data
        args = self.DEFAULT_ARGS + [
            self.OPTION_COMBINE_CODE_BRANCH,
            self.VALUE_COMBINE_CODE_BRANCH,
            self.OPTION_COMBINE_CODE_CHANGELIST,
            self.VALUE_COMBINE_CODE_CHANGELIST,
        ]
        result = runner.invoke(cli, args)

        assert result.exit_code == 0
        # Should use standard path without combine parameters
        self.mock_get_frosty_build_path.assert_called_once_with(
            data_branch=self.VALUE_DATA_BRANCH,
            data_changelist=self.VALUE_DATA_CHANGELIST,
            code_branch=self.VALUE_CODE_BRANCH,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            platform=self.VALUE_PLATFORM,
            package_type=self.VALUE_PACKAGE_TYPE,
            region=self.VALUE_REGION,
            config=self.VALUE_CONFIG,
        )

        # But should still pass the provided combine parameters to register_frosty_build
        self.mock_metadata_manager.register_frosty_build.assert_called_once_with(
            "/mocked/frosty/build/path",
            data_changelist=self.VALUE_DATA_CHANGELIST,
            data_branch=self.VALUE_DATA_BRANCH,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            code_branch=self.VALUE_CODE_BRANCH,
            platform=self.VALUE_PLATFORM,
            package_type=self.VALUE_PACKAGE_TYPE,
            region=self.VALUE_REGION,
            config=self.VALUE_CONFIG,
            dataset=self.VALUE_DATASET,
            combine_data_changelist=None,
            combine_data_branch=None,
            combine_code_changelist=self.VALUE_COMBINE_CODE_CHANGELIST,
            combine_code_branch=self.VALUE_COMBINE_CODE_BRANCH,
        )

    def test_with_package_type_already_containing_combine(self):
        """Test that _combine isn't added twice to package_type"""
        runner = CliRunner()
        # Set package type to already have _combine suffix
        args = [
            self.OPTION_CODE_BRANCH,
            self.VALUE_CODE_BRANCH,
            self.OPTION_CODE_CHANGELIST,
            self.VALUE_CODE_CHANGELIST,
            self.OPTION_DATA_BRANCH,
            self.VALUE_DATA_BRANCH,
            self.OPTION_DATA_CHANGELIST,
            self.VALUE_DATA_CHANGELIST,
            self.OPTION_DATASET,
            self.VALUE_DATASET,
            self.OPTION_PLATFORM,
            self.VALUE_PLATFORM,
            self.OPTION_PACKAGE_TYPE,
            self.VALUE_PACKAGE_TYPE + "_combine",
            self.OPTION_REGION,
            self.VALUE_REGION,
            self.OPTION_CONFIG,
            self.VALUE_CONFIG,
            self.OPTION_COMBINE_CODE_BRANCH,
            self.VALUE_COMBINE_CODE_BRANCH,
            self.OPTION_COMBINE_CODE_CHANGELIST,
            self.VALUE_COMBINE_CODE_CHANGELIST,
            self.OPTION_COMBINE_DATA_BRANCH,
            self.VALUE_COMBINE_DATA_BRANCH,
            self.OPTION_COMBINE_DATA_CHANGELIST,
            self.VALUE_COMBINE_DATA_CHANGELIST,
        ]
        result = runner.invoke(cli, args)

        assert result.exit_code == 0
        # Should not add _combine twice
        self.mock_get_frosty_build_path.assert_called_once_with(
            data_branch=self.VALUE_DATA_BRANCH,
            data_changelist=self.VALUE_DATA_CHANGELIST,
            code_branch=self.VALUE_CODE_BRANCH,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            platform=self.VALUE_PLATFORM,
            package_type=self.VALUE_PACKAGE_TYPE + "_combine",
            region=self.VALUE_REGION,
            config=self.VALUE_CONFIG,
            combine_data_branch=self.VALUE_COMBINE_DATA_BRANCH,
            combine_data_changelist=self.VALUE_COMBINE_DATA_CHANGELIST,
            combine_code_branch=self.VALUE_COMBINE_CODE_BRANCH,
            combine_code_changelist=self.VALUE_COMBINE_CODE_CHANGELIST,
        )
