/**
 * ProjectClass.groovy
 * Returns a project class file when given a project name.
 */
Class call(def project_name) {
    switch (project_name.toLowerCase()) {
        case 'bct': return com.ea.project.bct.Bct
        case 'bctch1': return com.ea.project.bctch1.BctCh1
        case 'cobra': return com.ea.project.Cobra
        case 'kingston': return com.ea.project.kin.Kingston
        case 'granite': return com.ea.project.gnt.Granite
        case 'kingston_aws': return com.ea.project.kin.KingstonAWS
        case 'fb1': return com.ea.project.fb1.Fb1Battlefieldgame
        case 'fb1-battlefieldgame': return com.ea.project.fb1.Fb1Battlefieldgame
        case 'fb1-casablanca': return com.ea.project.fb1.Fb1Casablanca
        case 'merlin': return com.ea.project.mer.Merlin
        case 'nfsupgrade': return com.ea.project.nfs.NFSUpgrade
        default: error 'ProjectClass called with invalid project name: ' + project_name
    }
}
