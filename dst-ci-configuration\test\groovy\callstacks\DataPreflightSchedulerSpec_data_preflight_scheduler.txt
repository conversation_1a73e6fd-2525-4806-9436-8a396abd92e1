   data_preflight_scheduler.run()
      data_preflight_scheduler.pipeline(groovy.lang.Closure)
         data_preflight_scheduler.allowBrokenBuildClaiming()
         data_preflight_scheduler.timestamps()
         data_preflight_scheduler.echo(Executing on agent [label:scheduler])
         data_preflight_scheduler.stage(Trigger data preflight jobs, groovy.lang.Closure)
            data_preflight_scheduler.script(groovy.lang.Closure)
               data_preflight_scheduler.retryOnFailureCause(3, [], groovy.lang.Closure)
                  LibJenkins.getLastStableCodeChangelist(kin-dev.data.lastknowngood)
                  LibJenkins.getLastStableDataChangelist(kin-dev.data.lastknowngood)
                  data_preflight_scheduler.string({name=code_changelist, value=2345})
                  data_preflight_scheduler.string({name=data_changelist, value=1234})
                  data_preflight_scheduler.string({name=unshelve_changelist, value=3456})
                  data_preflight_scheduler.booleanParam({name=validate_direct_references, value=false})
                  data_preflight_scheduler.booleanParam({name=clean_index, value=false})
                  data_preflight_scheduler.EnvInject({absoluteUrl=http://example.com/dummy, buildVariables={}, changeSets=[], currentResult=SUCCESS, description=dummy, displayName=#1, duration=1, durationString=1 ms, fullDisplayName=dummy #1, fullProjectName=dummy, id=1, keepLog=false, nextBuild=null, number=1, previousBuild=null, projectName=dummy, result=SUCCESS, startTimeInMillis=1, timeInMillis=1, upstreamBuilds=[]}, {code_changelist=2345, data_changelist=1234, unshelve_changelist=3456})
                  data_preflight_scheduler.echo(Running preflight on code changelist: 2345 and data changelist: 1234.)
                  GetBranchFile.get_branchfile(kingston, kin-dev)
                  data_preflight_scheduler.parallel({kin-dev.kindata.preflight.xb1=groovy.lang.Closure, kin-dev.kindata.preflight.ps5=groovy.lang.Closure})
                     data_preflight_scheduler.build({job=kin-dev.kindata.preflight.xb1, parameters=[{name=code_changelist, value=2345}, {name=data_changelist, value=1234}, {name=unshelve_changelist, value=3456}, {name=validate_direct_references, value=false}, {name=clean_index, value=false}], propagate=false})
                        data_preflight_scheduler.clone()
                        data_preflight_scheduler.toString()
                     LibJenkins.printRunningJobs(scripts.schedulers.all.data_preflight_scheduler)
                     data_preflight_scheduler.build({job=kin-dev.kindata.preflight.ps5, parameters=[{name=code_changelist, value=2345}, {name=data_changelist, value=1234}, {name=unshelve_changelist, value=3456}, {name=validate_direct_references, value=false}, {name=clean_index, value=false}], propagate=false})
                        data_preflight_scheduler.clone()
                        data_preflight_scheduler.toString()
                     LibJenkins.printRunningJobs(scripts.schedulers.all.data_preflight_scheduler)
         data_preflight_scheduler.stage(Scan for errors, groovy.lang.Closure)
            data_preflight_scheduler.ScanForErrors({absoluteUrl=http://example.com/dummy, buildVariables={}, changeSets=[], currentResult=SUCCESS, description=dummy, displayName=kin-dev.datapreflight.start.3456.Christofer, duration=1, durationString=1 ms, fullDisplayName=dummy #1, fullProjectName=dummy, id=1, keepLog=false, nextBuild=null, number=1, previousBuild=null, projectName=dummy, result=SUCCESS, startTimeInMillis=1, timeInMillis=1, upstreamBuilds=[]}, true)
         data_preflight_scheduler.emailext({to=<EMAIL>, subject=preflight result, body=${SCRIPT, template="email-pipeline-preflight.groovy"}, mimeType=text/html, presendScript=${SCRIPT, template="preflight-email-presend-pipeline.groovy"}})
