{#
    Command:
        frosty
            short_help: Deploys a playable game build with both binaries and data.

    Arguments:
        platform
        package_type
        config
        assets
            nargs: -1
            required: True

    Required variables:

    Optional variables:
        data_directory
            help: Which data directory to use for the working data set.
        code_branch
            help: Branch/stream to fetch the code/binary build from.
        code_changelist
            help: Changelist of binaries to fetch.
        data_branch
            help: Branch/stream that data is coming from.
        data_changelist
            help: Changelist of data being used.
        region
            help: Which region to deploy for (default is ww).
            default: ww
        use_deployed_bundles/__not_deployed_bundles
            help: Used deployed head bundles for frosty build.
            default: False
        pipeline_args
            multiple: True
            help: Pipeline arguments for data build.
        frosty_args
            multiple: True
            help: Frosty arguments.
        use_win64trial/__no_win64trial
            default: False
            help: Flag for using win64 trial for win64 patches.
        dry_run
            is_flag: True
            help: Build code without deploying.
        use_denuvo
            is_flag: True
            help: Use denuvo binaries.
        additional_configs
            multiple: True
            help: Additional configs to bundle with a loose files build.
        import_avalanche_state
            is_flag: True
            help: Imports Avalanche state from filer.
        data_clean
            default: false
            help: Clean Avalanche if --data-clean true is passed.
        use_recompression_cache
            is_flag: True
            help: Alternative Avalanche server to use for the recompression cache
        use_linuxclient
            is_flag: True
            help: Use linuxclient as the name for linux64 in FrostyIsoTool.
        disable_frosty_symbol_upload
            is_flag: True
            default: False
            help: Disable Frosty Symbol Upload.
        skip_streaming_install_package
            is_flag: True
            default: False
            help: set STREAMING_INSTALL_CREATE_SUBMISSION_PACKAGES to false
        trim/__no_trim
            default: True
        licensee
            multiple: True
            default: None
            help: Licensee to use
        password
            default: None
            help: User credentials to authenticate to package server
        email
            default: None
            help: User email to authenticate to package server
        domain_user
            default: None
            help: The user to authenticate to package server as DOMAIN\user
        clean_master_version_check
            is_flag: True
            help: Run clean on master version update.
        enable_eac
            is_flag: True
            help: Enable EasyAntiCheat
        expression_debug_data
            is_flag: True
            help: Export expression debug data after data cook.
        keep_intermediate_data
            is_flag: True
            help: Keep the intermediate folder after the build completes.
        build_gamescripts
            is_flag: True
            help: Enable building the gamescripts
        run_bespoke
            is_flag: True
            help: Run bespoke frosty instead of fbenv frosty
        file_hashes
            default: False
            help: Create a file with MD5 hashes for all files in the frosty output folder.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    frosty
    platform {{ platform }}
    package_type {{ package_type }}
    config {{ config }}
    assets {{ assets }}
    {%- if data_directory %}
    --data-directory {{ data_directory }}
    {%- endif %}
    {%- if code_branch %}
    --code-branch {{ code_branch }}
    {%- endif %}
    {%- if code_changelist %}
    --code-changelist {{ code_changelist }}
    {%- endif %}
    {%- if data_branch %}
    --data-branch {{ data_branch }}
    {%- endif %}
    {%- if data_changelist %}
    --data-changelist {{ data_changelist }}
    {%- endif %}
    {%- if region %}
    --region {{ region }}
    {%- endif %}
    {%- if use_deployed_bundles/__not_deployed_bundles %}
    --use-deployed-bundles/--not-deployed-bundles {{ use_deployed_bundles/__not_deployed_bundles }}
    {%- endif %}
    {%- if pipeline_args %}
    --pipeline-args {{ pipeline_args }}
    {%- endif %}
    {%- if frosty_args %}
    --frosty-args {{ frosty_args }}
    {%- endif %}
    {%- if use_win64trial/__no_win64trial %}
    --use-win64trial/--no-win64trial {{ use_win64trial/__no_win64trial }}
    {%- endif %}
    {%- if dry_run %}
    --dry-run {{ dry_run }}
    {%- endif %}
    {%- if use_oreans %}
    --use-oreans {{ use_oreans }}
    {%- endif %}
    {%- if use_denuvo %}
    --use-denuvo {{ use_denuvo }}
    {%- endif %}
    {%- if additional_configs %}
    --additional-configs {{ additional_configs }}
    {%- endif %}
    {%- if import_avalanche_state %}
    --import-avalanche-state {{ import_avalanche_state }}
    {%- endif %}
    {%- if data_clean %}
    --data-clean {{ data_clean }}
    {%- endif %}
    {%- if use_recompression_cache %}
    --use-recompression-cache {{ use_recompression_cache }}
    {%- endif %}
    {%- if use_linuxclient %}
    --use-linuxclient {{ use_linuxclient }}
    {%- endif %}
    {%- if disable_frosty_symbol_upload %}
    --disable-frosty-symbol-upload {{ disable_frosty_symbol_upload }}
    {%- endif %}
    {%- if skip_streaming_install_package %}
    --skip-streaming-install-package {{ skip_streaming_install_package }}
    {%- endif %}
    {%- if trim/__no_trim %}
    --trim/--no-trim {{ trim/__no_trim }}
    {%- endif %}
    {%- if licensee %}
    --licensee {{ licensee }}
    {%- endif %}
    {%- if password %}
    --password {{ password }}
    {%- endif %}
    {%- if email %}
    --email {{ email }}
    {%- endif %}
    {%- if domain_user %}
    --domain-user {{ domain_user }}
    {%- endif %}
    {%- if clean_master_version_check %}
    --clean-master-version-check {{ clean_master_version_check }}
    {%- endif %}
    {%- if enable_eac %}
    --enable-eac {{ enable_eac }}
    {%- endif %}
    {%- if expression_debug_data %}
    --expression-debug-data {{ expression_debug_data }}
    {%- endif %}
    {%- if keep_intermediate_data %}
    --keep-intermediate-data {{ keep_intermediate_data }}
    {%- endif %}
    {%- if build_gamescripts %}
    --build-gamescripts {{ build_gamescripts }}
    {%- endif %}
    {%- if run_bespoke %}
    --run-bespoke {{ run_bespoke }}
    {%- endif %}
    {%- if file_hashes %}
    --file-hashes {{ file_hashes }}
    {%- endif %}
  displayName: elipy frosty
