package com.ea.lib.model.autotest

/**
 * The base of the Autotest configurations
 */
class AutotestCategory {
    /**
     * Internal branch configurations.
     */
    List<BranchConfiguration> branches
    /**
     * Icepick {@code build_type}. Default {@code static}. Options {@code [static, dll]}
     */
    String buildType
    /**
     * I<PERSON>peick capture video. Default {@code false}
     */
    boolean captureVideo = false
    /**
     * Binary configs to use for tests. Default {@code final}. For instance {@code final} or {@code release}
     */
    String config
    /**
     * Server configs to use for tests. Default {@code final}. For instance {@code final} or {@code release}
     */
    String serverConfig
    /**
     * Custom test suite metadata to pass to Icepick. Optional.
     */
    String customTestSuiteData
    /**
     * Whether or not disable the automatic retry on failed downstream jobs due to node disconnects or other similar errors.
     * Default {@code false}
     */
    boolean disableAutomaticRetry = false
    /**
     *  What elipy command should be used for these tests. Default {@code icepick_run}. Options {@code [icepick_run, icepick_run_codetests]}
     */
    String elipyCmd = 'icepick_run'
    /**
     * Should p4 counters be enabled for these tests. Default {@code false}.
     */
    boolean enableP4Counters = false
    /**
     * Should sync the specified files to head. Default {@code false}.
     */
    boolean enableSyncFiles = false
    /**
     * Extra arguments for Icepick to pass to any Framework commands it starts. Optional.
     */
    String extraFrameworkArgs
    /**
     * Path to icepick settings files. Optional.
     */
    String forceIcepickSettingsFiles
    /**
     * For tests using packages, what package type should be used. Requires {@code isTestWithLooseFiles} and {@code region} to be set.
     * Optional. Options {@code [files, digital]}
     */
    String format
    /**
     * Overrides Frosting API address. Optional.
     */
    String frostingEndpointOverride
    /**
     * Whether or not the category is manually triggered. Default {@code false}.
     */
    boolean isManual = false
    /**
     * Should these test be using loose file builds. Default {@code false}.
     */
    boolean isTestWithLooseFiles = false
    /**
     * Whether or not it's a FrostEd Autotest. To create a Frosted Autotest, use {@link FrostedAutotestCategory}. Default {@code false}
     */
    boolean isFrostedAutotest = false
    /**
     * Define which Jenkins labels to run the Autotest Category on. Optional.
     */
    String jobLabel
    /**
     * Define which remote VM labels to run the Autotest Category on. Optional.
     */
    String remoteLabel
    /**
     * P4 server hostname for code. Optional.
     */
    String p4CodeServer
    /**
     * P4 server hostname for data. Optional.
     */
    String p4DataServer
    /**
     * P4 credentials for code. Optional.
     */
    String p4CodeCreds
    /**
     * P4 credentials for data. Optional.
     */
    String p4DataCreds
    /**
     * Name of test category. Example: {@code lkgtests}. Required.
     */
    String name
    /**
     * Do these tests require the game server binaries. Default {@code false}.
     */
    boolean needGameServer = false
    /**
     * For tests using packages, what region should be used. Default {@code ww}.
     */
    Region region
    /**
     * What platforms we require binaries to be built for when selecting a CL. Optional.
     */
    List<String> requiredPlatforms = []
    /**
     * What platforms we require binaries to be fetch for running large scale test. Optional.
     */
    List<String> clientPlatforms = []
    /**
     * Whether or not post results to Bilbo. Default {@code false}.
     */
    boolean runBilbo = false
    /**
     * What server asset to build if needed. Optional.
     */
    String serverAsset
    /**
     * Which server platform to use. Can be either {@code server} (windows) or {@code linuxserver}. Default {@code server}.
     */
    String serverPlatform
    /**
     * For tests using packages, what server region should be used. Default {@code ww}. Options {@code [ww, alpha, beta]}
     */
    String serverRegion
    /**
     * Should jenkins fail if any of the tests fail. Default {@code true}.
     */
    boolean showTestResultsOnJenkins = true
    /**
     * If set, sends Slack notifications to the given channel. Optional.
     */
    String slackChannel
    /**
     * Used to identify the category when reporting test results in Bilbo. Required.
     */
    String testDefinition
    /**
     * Branch specific configuration
     */
    TestInfo testInfo
    /**
     * Branch specific CRON trigger
     */
    String trigger
    /**
     * {@code --frosting-upload-trace-journal true}. Default {@code false}.
     */
    boolean uploadJournals = false
    /**
     * Whether or not to use the latest Drone build when selecting a CL. Default {@code false}.
     */
    boolean useLatestDrone = false
    /**
     * Whether or not it's a test with Shift builds. Default {@code false}.
     */
    boolean useShiftBuild = false
    /**
     * Whether or not it's a test with Spin builds. Default {@code false}.
     */
    boolean useSpinBuild = false
    /**
     * Whether or not to add {@code --use-temp-deployment} for tests deploying loose files.
     * Requires {@code isTestWithLooseFiles}, {@code format} and {@code region}.
     * Default {@code true}.
     */
    boolean useTempDeploymentIfLooseFiles = true
    /**
     * Timeout {@code --copy-build-to-share-timeout-seconds} for tests deploying loose files in seconds.
     * Requires {@code isTestWithLooseFiles}, {@code format} and {@code region}.
     * Default {@code 3000}.
     */
    int copyLooseFileBuildToWindowsShareTimeoutSeconds = 3000
    /**
     * Install pypiwin32 after after elipy installation
     */
    boolean installPypiwin32 = false
    /**
     * List of downstream test category names
     */
    List<String> downstreamAutotestCategories
    /**
     * How many jobs to split the test suites into if {@code runLevelsInParallel} has been set to {@code true}.
     * Defaults to the platforms count. Must be a multiple of the amount of platforms this {@link TestInfo} is configured for. Optional.
     */
    int getParallelLimit() {
        return this.testInfo.parallelLimit
    }
    /**
     * Should tests be ran in parallel. Default {@code false}. Optional.
     */
    Boolean getRunLevelsInParallel() {
        return this.testInfo.runLevelsInParallel
    }
    /**
     *  Whether or not to mark the build as smoked after successful run
     */
    boolean registerSmoke = false

    /**
     *  Whether or not to mark the build as verified_for_preflight after successful run.
     *  This is used to determine the CLs suitable for Skybuild preflights base images.
     */
    boolean registerVerifiedForPreflight = false

    /**
     *  Whether to use an existing build for the test run instead of building a new one.
     */
    boolean useExistingFilerBuild = false

    /**
     *  Custom tag used to download the autotest binaries.
     */
    String customTag
}
