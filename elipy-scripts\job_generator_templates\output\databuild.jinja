{#
    Command:
        databuild
            short_help: Cook the game data.

    Arguments:
        data_dir
        platform
        assets
            nargs: -1
            default: None
            required: False

    Required variables:
        code_branch
            required: True
            help: Perforce branch/stream name for binaries.
        code_changelist
            required: True
            help: Perforce changelist number for binaries.
        data_branch
            required: True
            help: Branch to fetch Avalanche state from.
        data_changelist
            required: True
            help: Changelist of data being cooked.

    Optional variables:
        pipeline_args
            multiple: True
            help: Pipeline arguments for data build.
        export_avalanche_state
            default: False
            help: Deploy Avalanche state to filer.
        import_avalanche_state
            default: False
            help: Imports Avalanche state from filer.
        data_clean
            default: false
            help: Clean Avalanche if --data-clean true is passed.
        db_name_prefix
            help: prefix for db name when exporting data to avalanche
        use_recompression_cache
            default: False
            help: Alternative Avalanche server to use for the recompression cache
        dry_run
            default: False
            help: Build patch without deploying.
        export_super_bundles
            default: False
            help: Export superbundles to filer.
        clone_host
            default: None
            help: Will clone db to host if host is added.
        trim
            default: True
        enable_compression
            default: False
            help: Add compression flag when exporting bundles.
        licensee
            multiple: True
            default: None
            help: Licensee to use.
        clean_master_version_check
            default: False
            help: Run clean on master version update.
        expression_debug_data
            default: False
            help: Export expression debug data after data cook.
        enlighten_mode
            default: None
            type: click.Choice([None, 'shelve', 'submit'])
            help: Build Enlighten data if specified: valid values are 'shelve' or 'submit'
        enlighten_asset_filter
            default: None
            help: GUID filter asset for Enlighten build, optional when using --enlighten-mode
        disable_caches
            default: False
            help: Run clean build with no cache
        p4_port
            help: Perforce server port, required when using --enlighten-mode.
        p4_client
            help: Perforce client, required when using --enlighten-mode.
        custom_tag
            default: None
            help: Extra folder before changelist to fetch code from.
        properties_file
            help: Properties file, used by the Jenkins plugin https://plugins.jenkins.io/envinject/
        icepick_suites
            multiple: True
            help: Switch using Ice Pick to cook.
        filer_user
            default: None
            help: username for creating a filer connection
        filer_password
            default: None
            help: password for creating a filer connection
        enable_hailstorm
            type: bool
            default: True
            help: Should Icepick cook use the hailstorm server or not
        fetch_pipeline
            type: bool
            default: True
            help: whether or not to fetch pipeline from filer.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    databuild
    data_dir {{ data_dir }}
    platform {{ platform }}
    assets {{ assets }}
    --code-branch {{ code_branch }}
    --code-changelist {{ code_changelist }}
    --data-branch {{ data_branch }}
    --data-changelist {{ data_changelist }}
    {%- if pipeline_args %}
    --pipeline-args {{ pipeline_args }}
    {%- endif %}
    {%- if export_avalanche_state %}
    --export-avalanche-state {{ export_avalanche_state }}
    {%- endif %}
    {%- if import_avalanche_state %}
    --import-avalanche-state {{ import_avalanche_state }}
    {%- endif %}
    {%- if data_clean %}
    --data-clean {{ data_clean }}
    {%- endif %}
    {%- if db_name_prefix %}
    --db-name-prefix {{ db_name_prefix }}
    {%- endif %}
    {%- if use_recompression_cache %}
    --use-recompression-cache {{ use_recompression_cache }}
    {%- endif %}
    {%- if dry_run %}
    --dry-run {{ dry_run }}
    {%- endif %}
    {%- if export_super_bundles %}
    --export-super-bundles {{ export_super_bundles }}
    {%- endif %}
    {%- if clone_host %}
    --clone-host {{ clone_host }}
    {%- endif %}
    {%- if trim %}
    --trim {{ trim }}
    {%- endif %}
    {%- if enable_compression %}
    --enable-compression {{ enable_compression }}
    {%- endif %}
    {%- if licensee %}
    --licensee {{ licensee }}
    {%- endif %}
    {%- if clean_master_version_check %}
    --clean-master-version-check {{ clean_master_version_check }}
    {%- endif %}
    {%- if expression_debug_data %}
    --expression-debug-data {{ expression_debug_data }}
    {%- endif %}
    {%- if enlighten_mode %}
    --enlighten-mode {{ enlighten_mode }}
    {%- endif %}
    {%- if enlighten_asset_filter %}
    --enlighten-asset-filter {{ enlighten_asset_filter }}
    {%- endif %}
    {%- if disable_caches %}
    --disable-caches {{ disable_caches }}
    {%- endif %}
    {%- if p4_port %}
    --p4-port {{ p4_port }}
    {%- endif %}
    {%- if p4_client %}
    --p4-client {{ p4_client }}
    {%- endif %}
    {%- if custom_tag %}
    --custom-tag {{ custom_tag }}
    {%- endif %}
    {%- if properties_file %}
    --properties-file {{ properties_file }}
    {%- endif %}
    {%- if icepick_suites %}
    --icepick-suites {{ icepick_suites }}
    {%- endif %}
    {%- if filer_user %}
    --filer-user {{ filer_user }}
    {%- endif %}
    {%- if filer_password %}
    --filer-password {{ filer_password }}
    {%- endif %}
    {%- if enable_hailstorm %}
    --enable-hailstorm {{ enable_hailstorm }}
    {%- endif %}
    {%- if fetch_pipeline %}
    --fetch-pipeline {{ fetch_pipeline }}
    {%- endif %}
  displayName: elipy databuild
