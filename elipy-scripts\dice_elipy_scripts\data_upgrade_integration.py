"""
data_upgrade_integration.py
"""
import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.integration_utils import compile_code, submit_integration
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import (
    core,
    data,
    exceptions,
    frostbite_core,
    local_paths,
    LOGGER,
    p4,
    running_processes,
)
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command("data_upgrade_integration", short_help="Performs a data upgrade integration.")
@click.option("--port", required=True)
@click.option("--client", required=True)
@click.option(
    "--changelist",
    default=None,
    help="Deprecated parameter (since 9.1), use --data-changelist instead.",
)
@click.option("--code-changelist", required=True, help="Perforce target code changelist.")
@click.option("--data-changelist", default=None, help="Perforce source data changelist.")
@click.option("--submit/--no-submit", default=True)
@click.option("--user", default=None, help="Perforce user name.")
@click.option("--licensee", multiple=True, required=True, help="Licensee for fdu call")
@click.option("--script-path", required=True, help="Script path for FDU call.")
@click.option("--data-directory", default=None, help="Which data directory to use.")
@click.option("--data-directory-source", default="SourceData", help="Which data directory to use.")
@click.option(
    "--password",
    default=None,
    help="User credentials to authenticate to package server.",
)
@click.option("--email", default=None, help="User email to authenticate to package server.")
@click.option(
    "--domain-user",
    default=None,
    help="The user to authenticate to package server as DOMAIN\\user.",
)
@click.option("--last-data-changelist", default=None, help="Data changelist for last build.")
@click.option("--p4-path-source", default=None, help="Perforce path for the source.")
@click.option("--revert-branchid-file", is_flag=True, help="Revert the BranchId.py file.")
@click.option("--clean", default="false", help="Delete TnT/Local if --clean true is passed.")
@click.option(
    "--p4-ignore",
    default=".p4ignore",
    help=".p4ignore file to set, if not will set the default of .p4ignore",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    port,
    client,
    changelist,
    code_changelist,
    data_changelist,
    submit,
    user,
    licensee,
    script_path,
    data_directory,
    data_directory_source,
    password,
    email,
    domain_user,
    last_data_changelist,
    p4_path_source,
    revert_branchid_file,
    clean,
    p4_ignore,
):
    """
    Performs a data upgrade integration.
    """
    # Adding sentry tags.
    add_sentry_tags(__file__)

    # Logic to handle a deprecated parameter.
    if data_changelist is None:
        if changelist is None:
            raise exceptions.ELIPYException(
                "No data changelist specified. Please set this with --data-changelist."
            )
        LOGGER.info(
            "The option --changelist has been deprecated, and will be removed in future releases. "
            "Please use --data-changelist instead."
        )
        data_changelist = changelist

    LOGGER.info("Running data upgrade at {}".format(data_changelist))
    perforce = p4.P4Utils(port=port, client=client, user=user)
    perforce.revert()
    running_processes.kill()

    data.DataUtils.set_datadir(data_directory)

    # Define data directory for the source data.
    source_data_dir = os.path.join(frostbite_core.get_game_root(), data_directory_source)

    # Create .p4config files for both datasets.
    core.ensure_p4_config(
        root_dir=source_data_dir, port=port, client=client, user=user, ignore=p4_ignore
    )
    core.ensure_p4_config(
        root_dir=frostbite_core.get_game_data_dir(),
        port=port,
        client=client,
        user=user,
        ignore=p4_ignore,
    )

    os.environ["P4CONFIG"] = ".p4config"

    perforce.sync(path=source_data_dir + "/...", to_revision=data_changelist)

    compile_code(
        licensee=list(licensee),
        password=password,
        email=email,
        domain_user=domain_user,
        port=port,
        user=user,
        client=client,
        clean=clean.lower() == "true",
    )

    message_data_changelist = (
        data_changelist
        if last_data_changelist == data_changelist or not last_data_changelist or not p4_path_source
        else perforce.changes_range(
            p4_path_source, last_data_changelist, changelist_end=data_changelist
        )
    )

    # Sync data again after code compile and before FDU
    perforce.sync(path=source_data_dir + "/...", to_revision=data_changelist)

    try:
        try:
            data.DataUtils.run_frostbite_data_upgrade(
                source_game_data_dir=source_data_dir,
                dest_game_data_dir=frostbite_core.get_game_data_dir(),
                licensee=licensee[0],
                scripts_path=script_path,
            )
        except Exception:
            perforce.clean(folder=frostbite_core.get_game_data_dir() + "/...")
            core.delete_folder(local_paths.get_tnt_local_path())
            raise

        # Submit the result to Perforce.
        fdu_message = (
            f"Upgraded Data from CL#{message_data_changelist} to Code CL#{code_changelist}."
        )
        submit_integration(
            p4_object=perforce,
            submit_message=fdu_message,
            submit=submit,
            data_upgrade=True,
            revert_branchid_file=revert_branchid_file,
        )
    finally:
        perforce.revert(quiet=True)
