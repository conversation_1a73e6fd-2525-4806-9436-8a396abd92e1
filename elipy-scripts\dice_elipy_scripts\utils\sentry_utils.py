"""
Adding Sentry helper functions
"""

import os
from sentry_sdk import configure_scope
from elipy2 import frostbite_core


def add_frostbite_sentry_tags():
    """
    Add frostbite specific sentry tags
    """
    try:
        fb_version = frostbite_core.read_fb_version()
        with configure_scope() as scope:
            scope.set_tag("elipy_scripts.fb_version", fb_version)
    except Exception:  # pylint: disable=broad-except
        # Ignore import errors since we don't want sentry related function to  break the script
        pass


def add_sentry_tags(file_path, elipy_category=None, extra_data=None):
    """
    Adding Sentry tags to make filtering easier
    """
    extra_data = extra_data or {}
    if elipy_category:
        extra_data.update({"category": elipy_category})

    extra_data.update({"basename": os.path.basename(file_path)})

    with configure_scope() as scope:
        for key, tag_value in extra_data.items():
            scope.set_tag("elipy_scripts." + key, tag_value)

    add_frostbite_sentry_tags()
