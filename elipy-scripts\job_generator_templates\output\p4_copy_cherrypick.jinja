{#
    Command:
        p4_copy_cherrypick
            short_help: Calls copy and cherrypick script.

    Arguments:

    Required variables:
        port
            required: True
        client
            required: True

    Optional variables:
        user
            default: None
            help: Perforce user name.
        source_branch
            default: ''
            help: branch from which we are integrating.
        json_file
            help: Config file with changelists to cherrypick.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    p4_copy_cherrypick
    --port {{ port }}
    --client {{ client }}
    {%- if user %}
    --user {{ user }}
    {%- endif %}
    {%- if source_branch %}
    --source-branch {{ source_branch }}
    {%- endif %}
    {%- if json_file %}
    --json-file {{ json_file }}
    {%- endif %}
  displayName: elipy p4_copy_cherrypick
