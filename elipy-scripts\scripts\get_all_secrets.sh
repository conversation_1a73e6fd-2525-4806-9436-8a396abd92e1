#!/bin/bash

echo "Getting all secrets..."

apk add --no-cache vault jq libcap
setcap cap_ipc_lock= /usr/sbin/vault

export VAULT_TOKEN="$(vault write -field=token ${VAULT_CLI_LOGIN_PATH} role=${VAULT_AUTH_ROLE} jwt=${VAULT_ID_TOKEN})"

## GET SECRET FROM ESS
ELIPY_SECRETS=$($CI_PROJECT_DIR/scripts/get_secret.sh "cobra/automation/artifactory/generic")

## GET THE PARTS OF THE SECRET WE CARE ABOUT
export PYPI_ARTIFACTORY_PASSWORD=$(echo $ELIPY_SECRETS | jq -r '.ARTIFACTORY_TOKEN')
export PYPI_ARTIFACTORY_USER=$(echo $ELIPY_SECRETS | jq -r '.ARTIFACTORY_USER')

echo "Done"
