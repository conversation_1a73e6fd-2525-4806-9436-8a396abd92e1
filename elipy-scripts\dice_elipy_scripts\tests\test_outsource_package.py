"""
test_outsource_package.py

Unit testing for outsource_package
"""
import unittest
import os
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import patch, MagicMock
from dice_elipy_scripts.outsource_package import cli


@patch("dice_elipy_scripts.outsource_package.core.clean_temp", MagicMock())
@patch("dice_elipy_scripts.outsource_package.p4.P4Utils.revert", MagicMock())
class TestOutsourcePackage(unittest.TestCase):
    OPTION_CLEAN = "--clean"
    OPTION_CODE_CHANGELIST = "--code-changelist"
    OPTION_CONFIG = "--config"
    OPTION_DATA_DIRECTORY = "--data-directory"
    OPTION_DRY_RUN = "--dry-run"
    OPTION_EMAIL = "--email"
    OPTION_FRAMEWORK_ARGS = "--framework-args"
    OPTION_LICENSEE = "--licensee"
    OPTION_P4_CLIENT_CODE = "--p4-client-code"
    OPTION_P4_CLIENT_PACKAGES = "--p4-client-packages"
    OPTION_P4_PORT = "--p4-port"
    OPTION_P4_USER = "--p4-user"
    OPTION_PASSWORD = "--password"
    OPTION_SCRIPT_PATH = "--script-path"

    VALUE_CLEAN = "True"
    VALUE_CODE_CHANGELIST = "1234"
    VALUE_CONFIG = "release"
    VALUE_DATA_DIRECTORY = "data_directory"
    VALUE_EMAIL = "<EMAIL>"
    VALUE_FRAMEWORK_ARGS = "some_arg"
    VALUE_LICENSEE = "some_licensee"
    VALUE_P4_CLIENT_CODE = "p4_client_code"
    VALUE_P4_CLIENT_PACKAGES = "p4_client_packages"
    VALUE_P4_PORT = "p4_port"
    VALUE_P4_USER = "p4_name"
    VALUE_PASSWORD = "pass_word"
    VALUE_SCRIPT_PATH = "script_path"

    BASE_ARGS = [
        OPTION_CODE_CHANGELIST,
        VALUE_CODE_CHANGELIST,
        OPTION_CONFIG,
        VALUE_CONFIG,
        OPTION_P4_CLIENT_CODE,
        VALUE_P4_CLIENT_CODE,
        OPTION_P4_CLIENT_PACKAGES,
        VALUE_P4_CLIENT_PACKAGES,
        OPTION_P4_PORT,
        VALUE_P4_PORT,
        OPTION_P4_USER,
        VALUE_P4_USER,
        OPTION_SCRIPT_PATH,
        VALUE_SCRIPT_PATH,
    ]

    def setUp(self):
        self.patcher_delete_folder = patch(
            "dice_elipy_scripts.outsource_package.core.delete_folder"
        )
        self.mock_delete_folder = self.patcher_delete_folder.start()

        self.patcher_set_datadir = patch(
            "dice_elipy_scripts.outsource_package.data.DataUtils.set_datadir"
        )
        self.mock_set_datadir = self.patcher_set_datadir.start()

        self.patcher_set_licensee = patch("dice_elipy_scripts.outsource_package.set_licensee")
        self.mock_set_licensee = self.patcher_set_licensee.start()

        self.patcher_import_module = patch(
            "dice_elipy_scripts.outsource_package.core.import_module_from_file"
        )
        self.mock_import_module = self.patcher_import_module.start()
        self.mock_import_module.return_value = MagicMock()

        self.patcher_codeutils = patch("dice_elipy_scripts.outsource_package.code.CodeUtils")
        self.mock_codeutils = self.patcher_codeutils.start()
        self.mock_codeutils.return_value = MagicMock()

        self.patcher_run_gensln = patch("dice_elipy_scripts.outsource_package.run_gensln")
        self.mock_run_gensln = self.patcher_run_gensln.start()

        self.patcher_p4_reconcile = patch(
            "dice_elipy_scripts.outsource_package.p4.P4Utils.reconcile"
        )
        self.mock_p4_reconcile = self.patcher_p4_reconcile.start()

        self.patcher_p4_submit = patch("dice_elipy_scripts.outsource_package.p4.P4Utils.submit")
        self.mock_p4_submit = self.patcher_p4_submit.start()

    def tearDown(self):
        self.patcher_delete_folder.stop()
        self.patcher_set_datadir.stop()
        self.patcher_set_licensee.stop()
        self.patcher_import_module.stop()
        self.patcher_codeutils.stop()
        self.patcher_run_gensln.stop()
        self.patcher_p4_reconcile.stop()
        self.patcher_p4_submit.stop()

    def test_basic_run(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASE_ARGS)
        assert result.exit_code == 0

    def test_clean_delete_folder(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASE_ARGS + [self.OPTION_CLEAN, self.VALUE_CLEAN])
        assert self.mock_delete_folder.call_count == 4

    def test_delete_folder_without_clean(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASE_ARGS)
        assert self.mock_delete_folder.call_count == 3

    def test_data_directory(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASE_ARGS + [self.OPTION_DATA_DIRECTORY, self.VALUE_DATA_DIRECTORY],
        )
        self.mock_set_datadir.assert_called_once_with(self.VALUE_DATA_DIRECTORY)
        assert result.exit_code == 0

    def test_import_module(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASE_ARGS)
        self.mock_import_module.assert_called_once_with(
            "outsource-package-install",
            os.path.join("tnt_root", self.VALUE_SCRIPT_PATH),
        )

    def test_run_gensln(self):
        self.mock_set_licensee.return_value = ["arg1"]
        runner = CliRunner()
        result = runner.invoke(cli, self.BASE_ARGS)
        self.mock_run_gensln.assert_called_once_with(
            password=None,
            user=None,
            domain_user=None,
            builder=self.mock_codeutils.return_value,
            framework_args=[
                "arg1",
                "-G:frostbite.pipeline.disable-platform-sdks=true",
                "-G:useProxyPackages=true",
                "-G:frostbite.usePrebuiltPackages=true",
                "-G:frostbite.is-outsource-build=true",
            ],
        )

    def test_run_package_script(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASE_ARGS)
        self.mock_import_module.return_value.build.assert_called_once_with(
            os.path.join("tnt_root", "Packages")
        )

    def test_p4_reconcile(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASE_ARGS)
        self.mock_p4_reconcile.assert_called_once_with(
            path=os.path.join("tnt_root", "Packages", "..."),
            options=["a", "d", "e", "f"],
            quiet=False,
        )

    def test_p4_submit(self):
        submit_message = (
            "Packages generated from CL " + self.VALUE_CODE_CHANGELIST + ".\nJenkins URL: None"
        )
        runner = CliRunner()
        result = runner.invoke(cli, self.BASE_ARGS)
        self.mock_p4_submit.assert_called_once_with(message=submit_message)

    def test_dry_run(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASE_ARGS + [self.OPTION_DRY_RUN])
        assert not self.mock_p4_submit.called
