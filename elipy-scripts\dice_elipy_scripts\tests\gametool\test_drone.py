"""
test_drone.py

Unit testing for drone
"""
import unittest
import os
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import patch, MagicMock
from dice_elipy_scripts.gametool.drone import cli


@patch("dice_elipy_scripts.gametool.drone.p4.P4Utils.sync", MagicMock())
@patch("dice_elipy_scripts.gametool.drone.p4.P4Utils.edit", MagicMock())
class TestDrone(unittest.TestCase):
    OPTION_CHANGELIST = "--changelist"
    OPTION_CODE_CHANGELIST = "--code-changelist"
    OPTION_CLEAN = "--clean"
    OPTION_CONFIG = "--config"
    OPTION_P4_PORT = "--p4-port"
    OPTION_P4_CLIENT = "--p4-client"
    OPTION_PASSWORD = "--password"
    OPTION_USER = "--user"
    OPTION_EMAIL = "--email"
    OPTION_LICENSEE = "--licensee"
    OPTION_FRAMEWORK_ARGS = "--framework-args"
    OPTION_DRY_RUN = "--dry-run"
    OPTION_DOMAIN_USER = "--domain-user"

    VALUE_CHANGELIST = "1234"
    VALUE_CODE_CHANGELIST = "5678"
    VALUE_CLEAN = "True"
    VALUE_CONFIG = "some_config"
    VALUE_P4_PORT = "p4_port"
    VALUE_P4_CLIENT = "p4_client"
    VALUE_PASSWORD = "pass_word"
    VALUE_USER = "user_name"
    VALUE_EMAIL = "<EMAIL>"
    VALUE_LICENSEE = "some_licensee"
    VALUE_FRAMEWORK_ARGS = "some_arg"
    VALUE_DOMAIN_USER = "DOMAIN\\user"

    CL_ARGS_OLD = [OPTION_CHANGELIST, VALUE_CHANGELIST]
    CL_ARGS_NEW = [OPTION_CODE_CHANGELIST, VALUE_CODE_CHANGELIST]
    P4_ARGS = [OPTION_P4_PORT, VALUE_P4_PORT, OPTION_P4_CLIENT, VALUE_P4_CLIENT]
    BASE_ARGS = CL_ARGS_NEW + P4_ARGS

    def setUp(self):
        self.patcher_clean_local = patch(
            "dice_elipy_scripts.gametool.drone.code.CodeUtils.clean_local"
        )
        self.mock_clean_local = self.patcher_clean_local.start()

        self.patcher_authenticate = patch(
            "dice_elipy_scripts.gametool.drone.authenticate_eapm_credstore"
        )
        self.mock_authenticate = self.patcher_authenticate.start()

        self.patcher_set_licensee = patch("dice_elipy_scripts.gametool.drone.set_licensee")
        self.mock_set_licensee = self.patcher_set_licensee.start()

        self.patcher_read_fb_version = patch(
            "dice_elipy_scripts.gametool.drone.frostbite_core.read_fb_version"
        )
        self.mock_read_fb_version = self.patcher_read_fb_version.start()
        self.mock_read_fb_version.return_value = "2021-PR1"

        self.patcher_nantonpackage = patch(
            "dice_elipy_scripts.gametool.drone.code.CodeUtils.nantonpackage"
        )
        self.mock_nantonpackage = self.patcher_nantonpackage.start()

        self.patcher_p4_reconcile = patch("dice_elipy_scripts.gametool.drone.p4.P4Utils.reconcile")
        self.mock_p4_reconcile = self.patcher_p4_reconcile.start()

        self.patcher_p4_submit = patch("dice_elipy_scripts.gametool.drone.p4.P4Utils.submit")
        self.mock_p4_submit = self.patcher_p4_submit.start()

        self.patcher_p4_revert = patch("dice_elipy_scripts.gametool.drone.p4.P4Utils.revert")
        self.mock_p4_revert = self.patcher_p4_revert.start()

    def tearDown(self):
        self.patcher_clean_local.stop()
        self.patcher_authenticate.stop()
        self.patcher_set_licensee.stop()
        self.patcher_read_fb_version.stop()
        self.patcher_nantonpackage.stop()
        self.patcher_p4_reconcile.stop()
        self.patcher_p4_submit.stop()
        self.patcher_p4_revert.stop()

    def test_drone(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.CL_ARGS_NEW + self.P4_ARGS)
        assert result.exit_code == 0

    def test_old_changelist_arg(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.CL_ARGS_OLD + self.P4_ARGS)
        assert result.exit_code == 0

    def test_no_changelist_failure(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.P4_ARGS)
        assert result.exit_code == 1

    def test_dry_run(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASE_ARGS + [self.OPTION_DRY_RUN])
        assert result.exit_code == 0

    def test_real_run(self):
        drone_path = os.path.join("tnt_root", "Setup", "Drone", "...")
        submit_message = (
            "Drone binaries for CL " + self.VALUE_CODE_CHANGELIST + "\nJenkins URL: None"
        )
        runner = CliRunner()
        result = runner.invoke(cli, self.BASE_ARGS)
        self.mock_p4_reconcile.assert_called_once_with(path=drone_path)
        self.mock_p4_submit.assert_called_once_with(message=submit_message)

    def test_clean_local(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASE_ARGS + [self.OPTION_CLEAN, self.VALUE_CLEAN])
        self.mock_clean_local.assert_called_once_with(close_handles=True)

    def test_authenticate(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASE_ARGS
            + [
                self.OPTION_PASSWORD,
                self.VALUE_PASSWORD,
                self.OPTION_EMAIL,
                self.VALUE_EMAIL,
                self.OPTION_DOMAIN_USER,
                self.VALUE_DOMAIN_USER,
            ],
        )
        self.mock_authenticate.assert_called_once_with(
            self.VALUE_PASSWORD, self.VALUE_EMAIL, self.VALUE_DOMAIN_USER
        )

    def test_authenticate_no_password(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASE_ARGS + [self.OPTION_EMAIL, self.VALUE_EMAIL])
        assert not self.mock_authenticate.called

    def test_authenticate_no_email(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASE_ARGS + [self.OPTION_PASSWORD, self.VALUE_PASSWORD])
        assert not self.mock_authenticate.called

    def test_dotnet_args_not_added(self):
        self.mock_set_licensee.return_value = ["arg1", "arg2"]
        runner = CliRunner()
        result = runner.invoke(cli, self.BASE_ARGS)
        self.mock_nantonpackage.assert_called_once_with(framework_args=["arg1", "arg2"])

    def test_dotnet_args_added(self):
        self.mock_read_fb_version.return_value = "2022-2-PR1"
        self.mock_set_licensee.return_value = ["arg1", "arg2"]
        runner = CliRunner()
        result = runner.invoke(cli, self.BASE_ARGS)
        self.mock_nantonpackage.assert_called_once_with(
            framework_args=[
                "arg1",
                "arg2",
                "-G:nant.net-family-target=dotnet",
                "-G:nant.packageserverondemand=true",
            ]
        )
