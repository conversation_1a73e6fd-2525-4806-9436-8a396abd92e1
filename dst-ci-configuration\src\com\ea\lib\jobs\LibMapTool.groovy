package com.ea.lib.jobs

import com.ea.lib.jobsettings.MapToolSettings

class LibMapTool {
    /**
     * Generic job for MapTool jobs, currently only for KIN use
     * Runs every midnight using last known good data changelist
     **/
    static void maptool_start(def job, def project, def branchFile, def masterFile, String branchName) {
        job.with {
            MapToolSettings settings = new MapToolSettings()
            settings.initializeMapToolStart(branchFile, masterFile, project, branchName)
            description(settings.description)
            disabled(settings.isDisabled)
            logRotator(31, 50)
            quietPeriod(0)
            properties {
                disableConcurrentBuilds()
                disableResume()
                pipelineTriggers {
                    triggers {
                        cron {
                            spec(settings.cronTrigger)
                        }
                    }
                }
            }
            parameters {
                stringParam {
                    name('maptool_server')
                    defaultValue('https://kin-maptool.dice.ad.ea.com')
                    description('Server for map tool')
                    trim(true)
                }
                stringParam {
                    name('game_dir')
                    defaultValue(project.workspace_root + '\\' + project.dataset)
                    description('Folder data should be gathered.')
                    trim(true)
                }
                stringParam {
                    name('filer_loc')
                    defaultValue('\\\\filer.dice.ad.ea.com\\Projects\\Battlefield\\Kingston\\Art\\World\\Levels\\ART_Renders')
                    description('Location of filer where camUploader will use')
                    trim(true)
                }
                stringParam {
                    name('juice_version')
                    defaultValue('\\\\filer.dice.ad.ea.com\\Builds\\JUICE\\Vision')
                    description('Location of filer where JUICE stores all versions')
                    trim(true)
                }
            }
            environmentVariables {
                env('project_name', settings.projectName)
                env('branch_name', settings.branchName)
                env('data_branch', settings.dataBranch)
                env('data_folder', settings.dataFolder)
                env('job_label', settings.jobLabel)
            }
        }
    }
}
