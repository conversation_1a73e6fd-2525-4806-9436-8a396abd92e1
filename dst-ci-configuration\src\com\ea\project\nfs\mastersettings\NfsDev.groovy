package com.ea.project.nfs.mastersettings

class NfsDev {
    static Class project = com.ea.project.nfs.NFSUpgrade
    static Map preflight_branches = [:]
    static Map branches = [
        'upgrade': [
            code_folder: 'nfs',
            code_branch: 'upgrade',
            data_folder: 'nfs',
            data_branch: 'upgrade',
        ],
    ]
    static Map autotest_branches = [:]
    static Map integrate_branches = [:]
    static Map copy_branches = [:]
    static Map feature_integrations = [:]
    static Map maintenance_branch = [
        'upgrade': [
            code_folder                       : 'nfs', code_branch: 'upgrade',
            data_folder                       : 'nfs', data_branch: 'upgrade',
            include_register_release_candidate: true,
            p4_delete_workspace_label         : 'upgrade && util'
        ]
    ]
    static List dashboard_list = []
    static Map dvcs_configs = [
        'nfs-frostbite': [
            project            : project,
            branch_name        : 'upgrade',
            slack_channel      : 'nfs-dvcs',
            slack_always_notify: true,
            dry_run            : false,
            trigger_type       : 'cron',
            trigger_string     : 'H/5 * * * 1-6',
            remote_spec        : 'nfs-frostbite',
            workspace_root     : project.workspace_root,
            code_folder        : 'nfs',
            code_branch        : 'upgrade',
            data_folder        : 'nfs',
            data_branch        : 'upgrade',
            elipy_install_call : project.elipy_install_call,
            elipy_call         : project.elipy_call,
            job_label          : 'dvcs',
        ]
    ]
    static List code_downstream_matrix = []
    static Map MAINTENANCE_SETTINGS = [:]
}
