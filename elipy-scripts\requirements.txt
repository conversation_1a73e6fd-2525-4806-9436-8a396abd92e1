# CI dependencies only in this file. Add any production dependencies to setup.py.
--index-url https://artifacts.ea.com/artifactory/api/pypi/dre-pypi-federated/simple
--extra-index-url https://artifacts.ea.com/artifactory/api/pypi/index-pypi-remote/simple
api4jenkins==1.14
black==23.3.0
click==8.1.3
Deprecated==1.2.13
dicttoxml==1.7.16
docutils==0.18.1
editorconfig-checker==2.7.1
elasticsearch==7.17.9
elipy2
m2r2==0.3.2
mistune==0.8.4
mock==5.0.2
pylint==2.17.4
pytest-cov==4.0.0
pytest-env==0.8.1
pytest-mock==3.10.0
pytest-runner==6.0.0
pytest-xdist==3.2.1
pytest==7.3.1
PyYAML~=6.0.1
requests==2.30.0
requests-mock==1.10.0
retry==0.9.2
sentry-sdk==1.22.2
setuptools==67.7.2
six==1.16.0
snowballstemmer==2.2.0
sphinx-rtd-theme==1.2.0
Sphinx==5.3.0
tox==4.5.1
virtualenv==20.26.3
wheel==0.40.0
winregistry==1.1.1
# CI dependencies only in this file. Add any production dependencies to setup.py.
