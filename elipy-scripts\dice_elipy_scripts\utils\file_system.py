"""
For gathering file system helper classes and functions
"""

from pathlib import Path
from elipy2 import frostbite_core, LOGGER, windows_tools


def print_drive_space():
    """
    Print the available space on the available drives.
    """
    try:
        drives = ["C:"]
        data_dir = Path(frostbite_core.get_game_data_dir())
        if data_dir:
            drives.append(data_dir.drive)
        computer = windows_tools.get_computer_name()
        for drive in drives:
            try:
                free_gbs = windows_tools.get_free_disk_space(computer=computer, dirname=drive)
                LOGGER.info("   Remaining space on %s drive: %sGB", drive, free_gbs)
            except Exception:
                pass
    except Exception as excinfo:
        LOGGER.warning("Failed to get free disk space: %s", excinfo)


class FolderTree:  # pylint: disable=old-style-class
    """
    Helper class to deal with part of a directory tree.

    Trunk is directory closest to root in this directory tree. Branch is any directory under trunk
    (directly or indirectly).
    """

    def __init__(self, trunk):
        self.trunk = trunk
        self._leafless_branches = set()
        self._branches_with_leaves = set()

    def append(self, branch, with_leaves):
        # type: (str, bool)
        """
        Adds branch to one of the lists of branches.
        """
        if self.has_branch(branch):
            (self._branches_with_leaves if with_leaves else self._leafless_branches).add(branch)
        else:
            raise Exception("{} is not a subdirectory of {}".format(branch, self.trunk))

    def has_branch(self, branch):
        # type: (str) -> bool
        """
        Determines if branch is a subfolder (direct or indirect) of trunk.
        """
        return branch.startswith(self.trunk)

    def leafless_and_trimmed(self):
        # type: () -> List[str]
        """
        Returns a list of branches (paths) in trunk that has no files or only build.json files. Each
        branch is as close to the trunk as possible.
        """
        if not self._branches_with_leaves:
            return {self.trunk}
        completely_leafless = set()
        completely_leafless.update(self._leafless_branches)

        for with_leaves in self._branches_with_leaves:
            LOGGER.debug("leaves: " + with_leaves)
            for leafless in self._leafless_branches:
                LOGGER.debug("\tleafless: " + leafless)
                if with_leaves.startswith(leafless):
                    completely_leafless.discard(leafless)
        close_to_trunk = set()
        close_to_trunk.update(completely_leafless)

        for outer in completely_leafless:
            LOGGER.debug("outer: " + outer)
            for inner in completely_leafless:
                LOGGER.debug("\tinner: " + inner)
                if outer != inner and inner.startswith(outer):
                    close_to_trunk.discard(inner)
        return close_to_trunk
