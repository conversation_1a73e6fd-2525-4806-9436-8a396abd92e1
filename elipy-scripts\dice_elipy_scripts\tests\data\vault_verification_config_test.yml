root:
  default:
    base: ['$DESTINATION_BASEPATH$', '$VERSION$']
    paths:
    - path_location: []
      min_items: 7

server:
  default:
    base: ['$DESTINATION_BASEPATH$', '$VERSION$', 'server', '$DATACHANGELIST$_$CODECHANGELIST$']
    paths:
    - path_location: []
      min_items: 3
    - path_location: ['bbb']
      min_items: 3
    - path_location: ['aaa', 'zzz', 'empty.txt']

linuxserver:
  default:
    base: ['$DESTINATION_BASEPATH$', '$VERSION$', 'linuxserver', '$DATACHANGELIST$_$CODECHANGELIST$']
    paths:
    - path_location: []
      min_items: 3
    - path_location: ['bbb']
      min_items: 3
    - path_location: ['aaa', 'zzz', 'empty.txt']

ps4:
  default:
    base: ['$DESTINATION_BASEPATH$', '$VERSION$', 'ps4', '$DATACHANGELIST$_$CODECHANGELIST$']
    paths:
    - path_location: []
      min_items: 3
    - path_location: ['bbb']
      min_items: 3
    - path_location: ['aaa', 'zzz', 'empty.txt']
  irc:
    base: [ '$DESTINATION_BASEPATH$', '$VERSION$', 'ps4', '$DATACHANGELIST$_$CODECHANGELIST$' ]
    paths:
      - path_location: [ ]
        min_items: 7
      - path_location: [ 'xxx' ]
        min_items: 7
      - path_location: [ 'yyy', 'zzz', 'empty.txt' ]

ps5:
  default:
    base: ['$DESTINATION_BASEPATH$', '$VERSION$', 'ps5', '$DATACHANGELIST$_$CODECHANGELIST$']
    paths:
    - path_location: []
      min_items: 3
    - path_location: ['bbb']
      min_items: 3
    - path_location: ['aaa', 'zzz', 'empty.txt']

xb1:
  default:
    base: ['$DESTINATION_BASEPATH$', '$VERSION$', 'xb1', '$DATACHANGELIST$_$CODECHANGELIST$']
    paths:
    - path_location: []
      min_items: 3
    - path_location: ['bbb']
      min_items: 3
    - path_location: ['aaa', 'zzz', 'empty.txt']

xbsx:
  default:
    base: ['$DESTINATION_BASEPATH$', '$VERSION$', 'xbsx', '$DATACHANGELIST$_$CODECHANGELIST$']
    paths:
    - path_location: []
      min_items: 3
    - path_location: ['bbb']
      min_items: 3
    - path_location: ['aaa', 'zzz', 'empty.txt']

win64:
  default:
    base: ['$DESTINATION_BASEPATH$', '$VERSION$', 'win64', '$DATACHANGELIST$_$CODECHANGELIST$']
    paths:
    - path_location: []
      min_items: 3
    - path_location: ['bbb']
      min_items: 3
    - path_location: ['aaa', 'zzz', 'empty.txt']
