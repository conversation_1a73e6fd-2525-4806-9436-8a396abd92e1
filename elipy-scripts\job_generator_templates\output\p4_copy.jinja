{#
    Command:
        p4_copy
            short_help: Performs a Perforce copy.

    Arguments:
        port
            required: True
        client
            required: True
        mapping
            required: True
        changelist
            required: True

    Required variables:

    Optional variables:
        reverse/__no_reverse
            default: False
        submit/__no_submit
            default: True
        stream/__no_stream
            default: False
        user
            default: None
            help: Perforce user name.
        force/__no_force
            default: False
        exclude_path
            default: []
            multiple: True
            help: Don't integrate path.
        submit_message
            default: ''
            help: Message to include in submit message.
        source_branch
            default: ''
            help: Branch from which we are integrating.
        target_branch
            default: ''
            help: Branch to which we are integrating.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    p4_copy
    port {{ port }}
    client {{ client }}
    mapping {{ mapping }}
    changelist {{ changelist }}
    {%- if reverse/__no_reverse %}
    --reverse/--no-reverse {{ reverse/__no_reverse }}
    {%- endif %}
    {%- if submit/__no_submit %}
    --submit/--no-submit {{ submit/__no_submit }}
    {%- endif %}
    {%- if stream/__no_stream %}
    --stream/--no-stream {{ stream/__no_stream }}
    {%- endif %}
    {%- if user %}
    --user {{ user }}
    {%- endif %}
    {%- if force/__no_force %}
    --force/--no-force {{ force/__no_force }}
    {%- endif %}
    {%- if exclude_path %}
    --exclude-path {{ exclude_path }}
    {%- endif %}
    {%- if submit_message %}
    --submit-message {{ submit_message }}
    {%- endif %}
    {%- if source_branch %}
    --source-branch {{ source_branch }}
    {%- endif %}
    {%- if target_branch %}
    --target-branch {{ target_branch }}
    {%- endif %}
  displayName: elipy p4_copy
