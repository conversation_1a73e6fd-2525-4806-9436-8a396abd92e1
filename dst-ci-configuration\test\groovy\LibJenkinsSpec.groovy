import com.ea.CobraLogger
import com.ea.lib.LibJenkins
import hudson.EnvVars
import hudson.console.ModelHyperlinkNote
import hudson.model.Cause
import hudson.model.Computer
import hudson.model.Fingerprint
import hudson.model.Item
import hudson.model.Node
import hudson.model.Queue
import hudson.model.Result
import hudson.model.Run
import hudson.model.TaskListener
import hudson.model.TopLevelItem
import hudson.util.RunList
import jenkins.model.Jenkins
import org.jenkinsci.plugins.workflow.job.WorkflowJob
import org.jenkinsci.plugins.workflow.job.WorkflowRun
import org.jenkinsci.plugins.workflow.support.steps.build.RunWrapper
import org.mockito.MockedStatic
import org.mockito.Mockito
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll
import support.Steps

class LibJenkinsSpec extends Specification {

    private final List<String> labels = ['kindata', 'ps4', 'kin-dev']

    @Shared
    // codenarc-disable PrivateFieldCouldBeFinal
    private EnvVars env100 = new EnvVars([
        code_changelist: '100',
        data_changelist: '200',
        NODE_NAME      : 'nodeName',
    ])

    @Shared
    // codenarc-disable PrivateFieldCouldBeFinal
    private EnvVars env101 = new EnvVars([
        code_changelist: '101',
        data_changelist: '201',
    ])

    @Shared
    Steps steps

    MockedStatic<Jenkins> jenkinsMock
    MockedStatic<ModelHyperlinkNote> modelHyperlinkNoteMock

    void setupSpec() {
        Steps.metaClass.echo = { String s -> CobraLogger.info(s) }
    }

    void setup() throws Exception {
        GroovySpy(Steps, global: true)
        steps = GroovyMock()
        RunList emptyBuilds = GroovyMock()
        RunList builds = Stub {
            byTimestamp(_ as long, _ as long) >> it
        }

        Computer computer1 = mockComputer(false, 0, builds, true)
        Computer computer2 = mockComputer(true, 0, emptyBuilds, false)
        Computer computer3 = mockComputer(false, 1, emptyBuilds, false)
        Computer computer4 = mockComputer(false, 0, builds, true)

        Node node1 = mockNode('node-1', 'kindata ps4 kin-dev', computer1) // OK
        Node node2 = mockNode('node-2', 'kindata ps5 kin-dev', null)
        Node node3 = mockNode('node-3', 'ps4', null)
        Node node4 = mockNode('node-4', 'kindata ps4 kin-dev', computer2) // offline
        Node node5 = mockNode('node-5', 'kindata ps4 kin-dev', computer3) // countBusy 1
        Node node6 = mockNode('node-6', 'kindata ps4 kin-dev', computer4) // OK
        Node node7 = mockNode('node-7', 'kindata ps4 kin-dev', null) // null computer

        Cause cause = Mockito.mock(Cause.UpstreamCause)
        Mockito.when(cause.pointsTo(Mockito.any(WorkflowRun))).thenReturn(true)

        WorkflowRun run = Mockito.mock(WorkflowRun)
        Mockito.when(run.getEnvironment(Mockito.any(TaskListener))).thenReturn(env100)
        Mockito.when(run.building).thenReturn(true)
        Mockito.when(run.getCause(Mockito.any())).thenReturn(cause)
        Mockito.when(run.url).thenReturn('url')
        Mockito.when(run.fullDisplayName).thenReturn('displayName')

        WorkflowRun run2 = Mockito.mock(WorkflowRun)
        Mockito.when(run2.building).thenReturn(false)

        RunList<Run> runList = Mockito.mock(RunList)
        Iterator<Run> iteratorMock = Mockito.mock(Iterator)
        Mockito.when(runList.iterator()).thenReturn(iteratorMock)
        Mockito.when(iteratorMock.hasNext()).thenReturn(true, true, false)
        Mockito.when(iteratorMock.next()).thenReturn(run, run2)

        WorkflowJob item = Mockito.mock(WorkflowJob)
        Mockito.when(item.lastStableBuild).thenReturn(run)
        Mockito.when(item.getBuildByNumber(Mockito.any(Integer))).thenReturn(run)
        Mockito.when(item.builds).thenReturn(runList)

        Node node = Mockito.mock(Node)

        Queue.Item queueItem1 = Mockito.mock(Queue.Item)
        Queue.Item queueItem2 = Mockito.mock(Queue.Item)
        Queue.Item[] itemQueue = [queueItem1, queueItem2]
        Queue queue = Mockito.mock(Queue)
        Mockito.when(queue.items).thenReturn(itemQueue)

        Jenkins jenkins = GroovyMock()
        jenkins.nodes >> [node1, node2, node3, node4, node5, node6, node7]
        jenkins.getItem(_ as String) >> item
        jenkins.getNode(_ as String) >> node
        jenkins.getAllItems(_ as Class<Item>) >> [item]
        jenkins.queue >> queue

        jenkinsMock = Mockito.mockStatic(Jenkins)
        jenkinsMock.when(Jenkins.&get).thenReturn(jenkins)

        modelHyperlinkNoteMock = Mockito.mockStatic(ModelHyperlinkNote)
        modelHyperlinkNoteMock
            .when(ModelHyperlinkNote.encodeTo(Mockito.anyString(), Mockito.anyString()) as MockedStatic.Verification)
            .thenAnswer { i -> "${i.getArgument(0)}/${i.getArgument(1)}" as String }
    }

    void cleanup() {
        jenkinsMock?.close()
        modelHyperlinkNoteMock?.close()
    }

    void 'getEnvironmentForLastStableBuild returns the last stable builds environment'() {
        when:
        EnvVars env = LibJenkins.getEnvironmentForLastStableBuild('job-name')
        then:
        env == env100
    }

    void 'getLastStableCodeChangelist returns the last stable code changelist'() {
        when:
        String codeChangelist = LibJenkins.getLastStableCodeChangelist('job-name')
        then:
        codeChangelist == '100'
    }

    void 'getLastStableDataChangelist returns the last stable data changelist'() {
        when:
        String dataChangelist = LibJenkins.getLastStableDataChangelist('job-name')
        then:
        dataChangelist == '200'
    }

    void 'getNodesWithLabels returns a list of Nodes that contain the specified labels'() {
        when:
        List<Node> nodes = LibJenkins.getNodesWithLabels(labels)
        then:
        nodes.size() == 5
        nodes.get(0).labelString == 'kindata ps4 kin-dev'
    }

    void 'getNodesWithPrefix returns all the nodes with the given prefix'() {
        when:
        List<Node> nodes = LibJenkins.getNodesWithPrefix('node-')
        then:
        nodes.size() == 7
    }

    void 'getJobsInQueue there are two items in the queue'() {
        when:
        Queue.Item[] queue = LibJenkins.jobsInQueue
        then:
        queue.length == 2
    }

    void 'onPremAgentsWithLabelsAvailable there is an idle onPrem computer with the given labels'() {
        when:
        boolean result = LibJenkins.onPremAgentsWithLabelsAvailable(labels)
        then:
        result
    }

    void 'getNodesToRunPreflightMaintenanceOn skips busy nodes'() {
        given:
        String dataset = 'kindata'
        String branchName = 'kin-dev'
        String platform = 'ps4'
        int nodeIdleMinutes = 60
        when:
        List<String> nodes = LibJenkins.getNodesToRunPreflightMaintenanceOn(dataset, branchName, platform, nodeIdleMinutes)
        then:
        nodes.size() == 2
    }

    void 'getItem returns an item with the given jobName'() {
        when:
        LibJenkins.getItem('jobName')
        then:
        1 * Jenkins.get().getItem('jobName')
    }

    void 'getRootUrl returns the base URL'() {
        when:
        LibJenkins.rootUrl
        then:
        1 * Jenkins.get().rootUrl
    }

    void 'getJobs returns all jobs'() {
        when:
        LibJenkins.jobs
        then:
        1 * Jenkins.get().items
    }

    void 'getLastBuildLog returns the latest build\'s log'() {
        given:
        TopLevelItem mockItem = GroovyMock()
        mockItem.lastBuild >> [logFile: [text: 'text']]
        GroovySpy(LibJenkins, global: true)
        LibJenkins.getItem(_ as String) >> mockItem
        when:
        String log = LibJenkins.getLastBuildLog('jobName')
        then:
        log == 'text'
    }

    void 'isLastBuildSuccess checks whether the last build was a success or not'() {
        given:
        TopLevelItem mockItem = GroovyMock()
        mockItem.lastBuild >> [result: Result.SUCCESS]
        GroovySpy(LibJenkins, global: true)
        LibJenkins.getItem(_ as String) >> mockItem
        when:
        boolean isSuccess = LibJenkins.isLastBuildSuccess('jobName')
        then:
        isSuccess
    }

    void 'hasLastNumberOfJobsFailed returns false because the five last jobs did not fail'() {
        given:
        TopLevelItem mockItem = GroovyMock()
        mockItem.getBuilds(_ as Fingerprint.RangeSet) >> [
            [result: Result.SUCCESS],
            [result: null],
            [result: Result.FAILURE],
            [result: Result.FAILURE],
            [result: Result.SUCCESS],
        ]
        GroovySpy(LibJenkins, global: true)
        LibJenkins.getItem(_ as String) >> mockItem
        when:
        boolean hasFailed = LibJenkins.hasLastNumberOfJobsFailed('test', '5', 5)
        then:
        !hasFailed
    }

    void 'hasLastNumberOfJobsFailed returns true because the last five runs failed'() {
        given:
        TopLevelItem mockItem = GroovyMock()
        mockItem.getBuilds(_ as Fingerprint.RangeSet) >> [
            [result: Result.FAILURE],
            [result: Result.FAILURE],
            [result: Result.FAILURE],
            [result: Result.FAILURE],
            [result: Result.FAILURE],
        ]
        GroovySpy(LibJenkins, global: true)
        LibJenkins.getItem(_ as String) >> mockItem
        when:
        boolean hasFailed = LibJenkins.hasLastNumberOfJobsFailed('test', '5', 5)
        then:
        hasFailed
    }

    void 'hasLastNumberOfJobsFailed returns false because five jobs have not run yet'() {
        when:
        boolean hasFailed = LibJenkins.hasLastNumberOfJobsFailed('test', '4', 5)
        then:
        !hasFailed
    }

    void 'removeNode tries to remove correct node'() {
        when:
        LibJenkins.removeNode('node-1')
        then:
        1 * Jenkins.get().removeNode(Jenkins.get().getNode('node-1'))
    }

    @Unroll
    void 'retrieveChangelistsFromJob returns code CL #expectedCodeChangelist data CL #expectedDataChangelist when #expected'() {
        when:
        GroovySpy(LibJenkins, global: true)
        LibJenkins.getEnvironmentForLastStableBuild(_ as String) >>> returnedEnvVars
        Map environment = LibJenkins.retrieveIfTargetChangelistsAreHigher('jobName', 'targetJobName')
        then:
        environment.code_changelist == expectedCodeChangelist
        environment.data_changelist == expectedDataChangelist
        where:
        returnedEnvVars  || expectedCodeChangelist | expectedDataChangelist | expected
        [env101, env100] || '101'                  | '201'                  | 'target job has built a higher CL'
        [env100, null]   || '100'                  | '200'                  | 'specified job has not built anything'
        [env100, env101] || null                   | null                   | 'specified job has built a higher CL'
        [null, null]     || null                   | null                   | 'target and specified job has not built anything'
        [null, env100]   || null                   | null                   | 'target job has not built anything'
    }

    void 'test printRemainingJobs prints one running job'() {
        given:
        steps.env >> [JOB_NAME: 'trunk-code-dev.codepreflight.start', BUILD_NUMBER: '4']
        when:
        LibJenkins.printRunningJobs(steps)
        then:
        1 * steps.echo('The following downstream builds are still running:\n- /url/displayName')
    }

    @Unroll
    void 'printFailureMessage #prints when allowFailure is #allowFailure and jobResult is #jobResult'() {
        given:
        Run run = Mockito.mock(Run)
        Mockito.when(run.getEnvironment(Mockito.any(TaskListener))).thenReturn(env100)
        Mockito.when(run.url).thenReturn('url')
        Mockito.when(run.fullDisplayName).thenReturn('fullDisplayName')
        RunWrapper downstreamJob = Mockito.mock(RunWrapper)
        Mockito.when(downstreamJob.rawBuild).thenReturn(run)
        Mockito.when(downstreamJob.result).thenReturn(jobResult)
        when:
        LibJenkins.printFailureMessage(steps, downstreamJob, allowFailure)
        then:
        calls * steps.echo("The failed url/fullDisplayName that completed on /computer/nodeName/nodeName is allowed to fail (controlled by 'allow_failure: true' in the settings).")
        where:
        allowFailure | jobResult                 || calls | prints
        true         | Result.FAILURE.toString() || 1     | 'prints'
        true         | Result.SUCCESS.toString() || 0     | 'does not print'
        false        | Result.FAILURE.toString() || 0     | 'does not print'
        false        | Result.FAILURE.toString() || 0     | 'does not print'
    }

    private Computer mockComputer(boolean isOffline, int countBusy, RunList builds, boolean isIdle) {
        Computer computer = GroovyMock()
        computer.offline >> isOffline
        computer.countBusy() >> countBusy
        computer.builds >> builds
        computer.idle >> isIdle
        computer.partiallyIdle >> isIdle
        return computer
    }

    private Node mockNode(String name, String labelString, Computer computer) {
        Node node = GroovyMock()
        node.nodeName >> name
        node.name >> name
        node.labelString >> labelString
        node.toComputer() >> computer
        return node
    }
}
