"""
test_imerge.py

Unit testing for imerge
"""
import unittest

from unittest.mock import patch, Mock

from elipy2.p4 import P4Utils, StreamInfo, ChangeInfo, FilePath
from elipy2.exceptions import ELIPYException, AutomaticP4MergeResolveException
from dice_elipy_scripts.imerge import Merge


def mkchange(cl, integration=None):
    desc = "Change description"
    if integration:
        desc += f"""\n
--- # CheckMate
Submission Info:
  Integration: {integration}
"""
    return ChangeInfo(cl, r"DICE\user", desc)


changelists = [
    mkchange("123"),
    mkchange("124", "merge"),
    mkchange("125", "XXX"),
    mkchange("126", "ignore"),
    mkchange("127", "Overwrite"),
    mkchange("128", "overwrite"),
]

m_grouped_changelists = [("123", "125", "m"), ("126", "126", "y"), ("127", "128", "t")]

s_grouped_changelists = [("123", "125", "s"), ("126", "126", "y"), ("127", "128", "t")]

stream_info = StreamInfo(
    name="future-dev-content",
    stream="//data/kin/future/future-dev-content",
    parent="//data/kin/dev/kin-dev",
    type="development",
    options=tuple("allsubmit unlocked notoparent fromparent mergedown".split()),
)


@patch("elipy2.LOGGER", Mock())
@patch("dice_elipy_scripts.utils.dbxmerge.DBXMerge.executable", None)
@patch("elipy2.p4.P4Utils.stream_info", Mock(return_value=stream_info))
@patch("elipy2.p4.P4Utils._p4", Mock(return_value=[]))
@patch("elipy2.p4.P4Utils.submit", Mock())
class TestP4(unittest.TestCase):
    def setUp(self):
        self.p4 = P4Utils("p4-server", "p4-user", "p4-client")

    def test_group_m(self):
        with Merge(self.p4, safe_resolve=False) as merge:
            self.assertEqual(list(merge.group(changelists)), m_grouped_changelists)

    def test_group_s(self):
        with Merge(self.p4, safe_resolve=True) as merge:
            self.assertEqual(list(merge.group(changelists)), s_grouped_changelists)

    def test_no_interchanges(self):
        with Merge(self.p4) as merge, patch(
            "elipy2.p4.P4Utils.interchanges", Mock(return_value=[])
        ):
            self.assertFalse(merge.run())

    def test_failed_merge(self):
        p4_merge = Mock(return_value=[])
        with patch("elipy2.p4.P4Utils.revert") as p4_revert, self.assertRaises(ELIPYException):
            with Merge(self.p4) as merge, patch(
                "elipy2.p4.P4Utils.interchanges", Mock(return_value=changelists)
            ), patch("elipy2.p4.P4Utils.merge", p4_merge):
                merge.run()
            p4_revert.assert_called_once()
        p4_merge.assert_called_once()

    def test_failed_resolve(self):
        p4_unresolved = Mock(
            return_value=[FilePath("d:/dev/file.txt", "//data/kin/dev/kin-dev/file.txt")]
        )
        with patch("elipy2.p4.P4Utils.revert") as p4_revert, self.assertRaises(
            AutomaticP4MergeResolveException
        ):
            with Merge(self.p4) as merge, patch(
                "elipy2.p4.P4Utils.interchanges", Mock(return_value=changelists)
            ), patch("elipy2.p4.P4Utils.unresolved", p4_unresolved), patch(
                "elipy2.p4.P4Utils.merge", Mock(return_value=["some data"])
            ):
                merge.run("123")
            p4_revert.assert_called_once()
        p4_unresolved.assert_called_once()

    def test_merge(self):
        p4_merge = Mock(return_value=["some data"])
        with patch("elipy2.p4.P4Utils.revert") as p4_revert:
            with Merge(self.p4, exclude_paths=["//data/kin/dev/..."]) as merge, patch(
                "elipy2.p4.P4Utils.interchanges", Mock(return_value=changelists)
            ), patch("elipy2.p4.P4Utils.unresolved", Mock(return_value=[])), patch(
                "elipy2.p4.P4Utils.merge", p4_merge
            ):
                self.assertTrue(merge.run("123", "128"))
                self.assertEqual(merge.first_cl, "123")
                self.assertEqual(merge.last_cl, "128")
                self.assertEqual(p4_merge.call_count, len(m_grouped_changelists))
                merge.submit("Integration completed")
            p4_revert.assert_called_with(path="//data/kin/dev/...", quiet=True)
