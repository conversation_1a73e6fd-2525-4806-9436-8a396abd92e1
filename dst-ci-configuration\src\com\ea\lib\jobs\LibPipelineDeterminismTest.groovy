package com.ea.lib.jobs

import com.ea.lib.LibJobDsl
import com.ea.lib.jobsettings.JobSetting
import com.ea.lib.jobsettings.PipelineDeterminismTestSettings

class LibPipelineDeterminismTest {
    /**
     * Start build pipeline determinism job
     */
    static void start(def job, def project, def branchFile, def masterFile, String branchName) {
        def settings = new PipelineDeterminismTestSettings()
        settings.initializeStart(branchFile, masterFile, project, branchName)
        job.with {
            description(settings.description)
            environmentVariables {
                env('BRANCH_NAME', settings.branchName)
                env('PROJECT_NAME', settings.projectName)
            }
            disabled(false)
            logRotator(7, 100)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync and get tool binaries.')
                    trim(true)
                }
            }
            properties {
                disableConcurrentBuilds()
                disableResume()
                if (settings.cronTrigger) {
                    pipelineTriggers {
                        triggers {
                            cron {
                                spec(settings.cronTrigger)
                            }
                        }
                    }
                }
            }
            quietPeriod(0)
        }
    }

    static void job(def job, def project, def branchFile, def masterFile, String branchName, String scriptArgs, String inputJobLabel = null) {
        JobSetting settings = new PipelineDeterminismTestSettings()
        settings.initializeJob(branchFile, masterFile, project, branchName)
        String jobLabel = inputJobLabel ?: settings.jobLabel
        job.with {
            description(settings.description)
            label(jobLabel)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(settings.workspaceRoot)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync and get tool binaries..')
                    trim(true)
                }
                stringParam {
                    name('script_args')
                    defaultValue(scriptArgs)
                    description('Arguments for script')
                    trim(true)
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(settings.buildName)
                timeout {
                    absolute(settings.timeoutMinutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                batchFile(settings.elipyCmd)
            }
        }
    }
}
