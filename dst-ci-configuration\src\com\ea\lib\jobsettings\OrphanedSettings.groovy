package com.ea.lib.jobsettings

class OrphanedSettings extends JobSetting {
    String slackChannel
    String projectShortName

    void initializeStartJob(def masterFile, def projectFile) {
        description = 'Maintenance job that periodically removes orphaned jobs from the Jenkins master'
        cronTrigger = masterFile.MAINTENANCE_SETTINGS.ORPHANED_TRIGGER ?: '0 6,16 * * *'
        slackChannel = masterFile.MAINTENANCE_SETTINGS.ORPHANED_SLACK_CHANNEL ?: '#cobra-jobdsl'
        projectShortName = masterFile.MAINTENANCE_SETTINGS.ORPHANED_PROJECT_SHORT_NAME ?: projectFile.short_name
    }
}
