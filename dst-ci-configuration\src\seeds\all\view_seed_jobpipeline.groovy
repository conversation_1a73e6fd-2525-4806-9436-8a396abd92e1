package all

import com.ea.lib.LibJobDsl
import com.ea.project.GetBranchFile
import com.ea.project.GetMasterFile
import com.ea.project.all.All
import jenkins.model.Jenkins

GetMasterFile.get_masterfile(BUILD_URL).each { masterSettings ->
    def branches = masterSettings.branches
    def dashboard_list = masterSettings.dashboard_list
    def integrate_branches = masterSettings.integrate_branches + masterSettings.feature_integrations
    def copy_branches = masterSettings.copy_branches

    if (BUILD_URL.contains('kin-dev-jenkins')) {
        buildMonitorView('TD-Build Police dashboard') {
            jobs {
                name('TD-Build Police dashboard')
                regex('(kin-dev\\.|kin-dev-unverified)((.*start.*)|(.*code.*)|(.*data.*)|(.*frosty.*))')
            }
            recurse(true)
        }
        //	buildMonitorView('Active branches dashboard') {
        //		jobs {
        //			regex("(${dashboard_branches.join('|')})\\.((code.start)|(code.drone.start)|(data.start)|(frosty.start))")
        //		}
        //		recurse(true)
        //	}
    }

    if (BUILD_URL.contains('kin-release-jenkins')) {
        buildMonitorView('TD-Build Police dashboard') {
            jobs {
                name('TD-Build Police dashboard')
                regex('(kin-stage)((.*start.*)|(.*code.*)|(.*data.*)|(.*frosty.*))')
            }
            recurse(true)
        }
    }

    if (BUILD_URL.contains('dice-cas-release')) {
        buildMonitorView('Dashboard') {
            jobs {
                regex("(${branches.keySet().join('|')})((.*start.*)|(.*integrate.*)|(.*merge-down.*))")
            }
            recurse(true)
        }
    }

    if (BUILD_URL.contains('wal-release')) {
        buildMonitorView('Dashboard') {
            jobs {
                regex("(${branches.keySet().join('|')})((.*start.*)|(.*integrate.*)|(.*merge-down.*))")
            }
            recurse(true)
        }
    }

    branches.each { branch_name, info ->
        def branch_pattern = info.branch_pattern ?: branch_name
        def project = masterSettings.project
        if (All.isAssignableFrom(project)) {
            project = info.project
        }
        def branchFile = GetBranchFile.get_branchfile(project.name, branch_name)
        def generalSettings = branchFile.general_settings
        def standardJobsSettings = branchFile.standard_jobs_settings
        def branch_info = info + generalSettings + standardJobsSettings + [branch_name: branch_name]
        sectionedView(branch_name) {
            sections {
                listView {
                    name('Schedulers')
                    width('FULL')
                    alignment('CENTER')
                    jobs {
                        regex("(.*(integrate-upgrade-to.*${branch_pattern}-upgrade.start))|(${branch_pattern}\\.((?!Performance|autotest|icepick|preflight|coverity|integrate-upgrade-to).)*(scheduler|seed|template|start|upgrade.data))")
                    }
                    columns {
                        status()
                        weather()
                        name()
                        lastSuccess()
                        lastFailure()
                        lastDuration()
                        buildButton()
                    }
                }
                listView {
                    name('Special jobs')
                    width('FULL')
                    alignment('CENTER')
                    jobs {
                        regex("${branch_pattern}\\.((?!Performance|autotest|icepick).)*(docker.image|sparta.bundle|shift(?!\\.start)|lastknowngood|upgrade-validator|success|trigger|stable|email|set|smoke|marvin|outsourcevalidation|outsource-package|prebuild|symbolStoreUpload|custom-script).*")
                    }
                    columns {
                        status()
                        weather()
                        name()
                        lastSuccess()
                        lastFailure()
                        lastDuration()
                        buildButton()
                    }
                }
                listView {
                    name('Spin jobs')
                    width('FULL')
                    alignment('CENTER')
                    jobs {
                        regex("${branch_pattern}\\.*(spin).*")
                    }
                    columns {
                        status()
                        weather()
                        name()
                        lastSuccess()
                        lastFailure()
                        lastDuration()
                        buildButton()
                    }
                }
                listView {
                    name('Enlighten jobs')
                    width('FULL')
                    alignment('CENTER')
                    jobs {
                        regex("${branch_pattern}\\.*(enlighten).*")
                    }
                    columns {
                        status()
                        weather()
                        name()
                        lastSuccess()
                        lastFailure()
                        lastDuration()
                        buildButton()
                    }
                }
                listView {
                    name('Bilbo jobs')
                    width('FULL')
                    alignment('CENTER')
                    jobs {
                        regex("${branch_pattern}.bilbo\\.((?!autotest).)*(dronebuild|local|move|remote).*")
                    }
                    columns {
                        status()
                        weather()
                        name()
                        lastSuccess()
                        lastFailure()
                        lastDuration()
                        buildButton()
                    }
                }
                listView {
                    name('Code jobs')
                    width('FULL')
                    alignment('CENTER')
                    jobs {
                        regex("${branch_pattern}.code\\.(?!scheduler|seed|template|start|merge|integrate|copy-to|nomaster|preflight|trigger).*")
                    }
                    columns {
                        status()
                        weather()
                        name()
                        lastSuccess()
                        lastFailure()
                        lastDuration()
                    }
                }
                listView {
                    name('Code nomaster jobs')
                    width('FULL')
                    alignment('CENTER')
                    jobs {
                        regex("${branch_pattern}.code.nomaster.(?!scheduler|seed|template|start|merge|integrate).*")
                    }
                    columns {
                        status()
                        weather()
                        name()
                        lastSuccess()
                        lastFailure()
                        lastDuration()
                    }
                }
                if (branchFile.general_settings.gametool_settings) {
                    listView {
                        name('Gametool jobs')
                        width('FULL')
                        alignment('CENTER')
                        jobs {
                            regex("${branch_pattern}.gametool.*")
                        }
                        columns {
                            status()
                            weather()
                            name()
                            lastSuccess()
                            lastFailure()
                            lastDuration()
                        }
                    }
                }
                if (branchFile.general_settings.unittests) {
                    listView {
                        name('Unit testing jobs')
                        width('FULL')
                        alignment('CENTER')
                        jobs {
                            regex("${branch_pattern}.unittests.*")
                        }
                        columns {
                            status()
                            weather()
                            name()
                            lastSuccess()
                            lastFailure()
                            lastDuration()
                            buildButton()
                        }
                    }
                }
                listView {
                    name('Data jobs')
                    width('FULL')
                    alignment('CENTER')
                    jobs {
                        regex("${branch_pattern}\\.(?!patch|icepick|frosty)(?i).*data\\.(?!scheduler|seed|template|start|merge|preflight|copy-to).*")
                    }
                    columns {
                        status()
                        weather()
                        name()
                        lastSuccess()
                        lastFailure()
                        lastDuration()
                    }
                }
                listView {
                    name('Patchdata jobs')
                    width('FULL')
                    alignment('CENTER')
                    jobs {
                        regex("${branch_pattern}.patchdata.(?!scheduler|seed|template|start).*")
                    }
                    columns {
                        status()
                        weather()
                        name()
                        lastSuccess()
                        lastFailure()
                        lastDuration()
                    }
                }
                if (branch_info.deployment_data_branch) {
                    listView {
                        name('Deployment data jobs')
                        width('FULL')
                        alignment('CENTER')
                        jobs {
                            regex("${branch_pattern}.(deployment-data|frosty-orchestrator).*")
                        }
                        columns {
                            status()
                            weather()
                            name()
                            lastSuccess()
                            lastFailure()
                            lastDuration()
                        }
                    }
                }
                listView {
                    name('Frosty jobs')
                    width('FULL')
                    alignment('CENTER')
                    jobs {
                        regex("${branch_pattern}.frosty.(?!scheduler|seed|template|start).*")
                    }
                    columns {
                        status()
                        weather()
                        name()
                        lastSuccess()
                        lastFailure()
                        lastDuration()
                    }
                }
                listView {
                    name('Patchfrosty jobs')
                    width('FULL')
                    alignment('CENTER')
                    jobs {
                        regex("${branch_pattern}.patchfrosty.(?!scheduler|seed|template|start).*")
                    }
                    columns {
                        status()
                        weather()
                        name()
                        lastSuccess()
                        lastFailure()
                        lastDuration()
                    }
                }
                listView {
                    name('Pipeline determinism jobs')
                    width('FULL')
                    alignment('CENTER')
                    jobs {
                        regex("${branch_pattern}.pipeline-determinism-test[.]?(?!scheduler|seed|template|start).*")
                    }
                    columns {
                        status()
                        weather()
                        name()
                        lastSuccess()
                        lastFailure()
                        lastDuration()
                    }
                }
                if (branchFile.general_settings.coverity_settings?.run_coverity) {
                    listView {
                        name('Coverity jobs')
                        width('FULL')
                        alignment('CENTER')
                        jobs {
                            regex("${branch_pattern}.coverity.*")
                        }
                        columns {
                            status()
                            weather()
                            name()
                            lastSuccess()
                            lastFailure()
                            lastDuration()
                        }
                    }
                }
                if (branch_pattern == '2024_1_dev-bf') {
                    listView {
                        name('Frosted Tests')
                        width('FULL')
                        alignment('CENTER')
                        jobs {
                            regex("${branch_pattern}.autotest.frostedtests_fbapi.win64.parallel.job-1")
                        }
                        columns {
                            status()
                            weather()
                            name()
                            lastSuccess()
                            lastFailure()
                            lastDuration()
                        }
                    }
                }
            }
        }

        if (branch_info.koala_code == true) {
            sectionedView('koala-code') {
                sections {
                    listView {
                        name('Code jobs')
                        width('FULL')
                        alignment('CENTER')
                        jobs {
                            regex("${branch_pattern}.(?:code.nomaster|code|data|frosty).*")
                        }
                        columns {
                            status()
                            weather()
                            name()
                            lastSuccess()
                            lastFailure()
                            lastDuration()
                        }
                    }
                }
            }
        }

        if (branch_info.koala_content == true) {
            sectionedView('koala-content') {
                sections {
                    listView {
                        name('Content jobs')
                        width('FULL')
                        alignment('CENTER')
                        jobs {
                            regex("${branch_pattern}.(?:data|frosty).*")
                        }
                        columns {
                            status()
                            weather()
                            name()
                            lastSuccess()
                            lastFailure()
                            lastDuration()
                        }
                    }
                }
            }
        }

        if (branch_info.koala_onetrunk == true) {
            sectionedView('koala-onetrunk') {
                sections {
                    listView {
                        name('Onetrunk jobs')
                        width('FULL')
                        alignment('CENTER')
                        jobs {
                            regex("${branch_pattern}.(?:code.nomaster|code|data|frosty).*")
                        }
                        columns {
                            status()
                            weather()
                            name()
                            lastSuccess()
                            lastFailure()
                            lastDuration()
                        }
                    }
                }
            }
        }

        if (branch_info.koala_branch == true) {
            sectionedView('Koala_' + branch_name) {
                sections {
                    listView {
                        name('Code jobs')
                        width('FULL')
                        alignment('CENTER')
                        jobs {
                            regex("${branch_pattern}.code(?!preflight).(?!start|nomaster|lastknowngood|preflight|p4).*")
                        }
                        columns {
                            status()
                            weather()
                            name()
                            lastSuccess()
                            lastFailure()
                            lastDuration()
                        }
                    }
                    listView {
                        name('Code nomaster jobs')
                        width('FULL')
                        alignment('CENTER')
                        jobs {
                            regex("${branch_pattern}.code.nomaster.(?!start).*")
                        }
                        columns {
                            status()
                            weather()
                            name()
                            lastSuccess()
                            lastFailure()
                            lastDuration()
                        }
                    }
                    listView {
                        name('Data jobs')
                        width('FULL')
                        alignment('CENTER')
                        jobs {
                            regex("${branch_pattern}.(?i)(kin|bf|example|DiceNext|)data.(?!start|export|lastknowngood|preflight|p4).*")
                        }
                        columns {
                            status()
                            weather()
                            name()
                            lastSuccess()
                            lastFailure()
                            lastDuration()
                        }
                    }
                    listView {
                        name('Frosty jobs')
                        width('FULL')
                        alignment('CENTER')
                        jobs {
                            regex("${branch_pattern}.frosty.(?!start|p4).*")
                        }
                        columns {
                            status()
                            weather()
                            name()
                            lastSuccess()
                            lastFailure()
                            lastDuration()
                        }
                    }
                }
            }

            buildMonitorView("LKGTests_${branch_pattern}") {
                description("All jobs for lkg test jobs for ${branch_pattern}")
                jobs {
                    name("LKGTest job for ${branch_pattern}")
                    regex("${branch_pattern}.lkgtests.(?!.*?manual)(?!.*?p4counterupdate).*")
                }
            }
        }
    }

    // Set the default view. NOTE: the views are created after JobDSL is finished. This means that the second run will do the actual set.
    LibJobDsl.setPrimaryView(Jenkins.get(), branches.keySet()[0])

    if (!integrate_branches.isEmpty() || !copy_branches.isEmpty()) {
        sectionedView('Integrations') {
            sections {
                listView {
                    name('Integrations')
                    width('FULL')
                    alignment('CENTER')
                    jobs {
                        regex('.*(merge-down|integrate|integrate-up|copy-up|copy-to|copy-code-to|cherrypick|changelist).*')
                    }
                    columns {
                        status()
                        weather()
                        name()
                        lastSuccess()
                        lastFailure()
                        lastDuration()
                        buildButton()
                    }
                }
                listView {
                    name('DVCS')
                    width('FULL')
                    alignment('CENTER')
                    jobs {
                        regex('.*dvcs.*')
                    }
                    columns {
                        status()
                        weather()
                        name()
                        lastSuccess()
                        lastFailure()
                        lastDuration()
                        buildButton()
                    }
                }
            }
        }
        buildMonitorView('Integrations Dashboard') {
            jobs {
                regex('.*(merge-down|integrate|integrate-up|copy-up|cherrypick).*')
            }
        }
    }

    dashboard_list.each { branch_name ->
        buildMonitorView("${branch_name} Dashboard") {
            jobs {
                regex("${branch_name}\\.(?!icepick)(code|.*data|bilbo|frosty|verified|shift|linux|data)\\.(?!preflight).*")
            }
        }
    }
}
