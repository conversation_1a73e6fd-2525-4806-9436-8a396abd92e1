import com.ea.exceptions.CobraException
import com.sonyericsson.jenkins.plugins.bfa.model.FailureCauseBuildAction

/**
 * IsGameFailure.groovy
 * Returns false if the last build for the given job needs to be rebuilt from DRE point of view and prints the failures for the build
 */
boolean call(def job_name, def allow_failure = false) {
    echo '[IsGameFailure] Start'
    def job = Jenkins.get().getItem(job_name)
    if (job == null) {
        throw new CobraException('Job ' + job_name + ' not found.')
    }

    def last_build = job.lastCompletedBuild
    if (last_build == null) {
        return false // Someone deleted all the builds ==> must build.
    }

    def action = last_build.getAction(FailureCauseBuildAction)
    if (action == null || (action != null && action.foundFailureCauses.isEmpty())) {
        return false // Rebuild for non scanned builds.
    }

    def result = false
    for (def cause in action.foundFailureCauses) {
        if (cause.categories?.contains('retry')) {
            return false
        }
        if (cause.categories?.contains('game')) {
            if (allow_failure == false) { // Only print this for jobs we don't allow to fail.
                def job_env = last_build.getEnvironment(TaskListener.NULL)
                def job_link_url = '/job/' + job_env.JOB_NAME + '/' + job_env.BUILD_NUMBER
                def job_link_message = job_env.JOB_NAME + ' #' + job_env.BUILD_NUMBER
                def job_link = hudson.console.ModelHyperlinkNote.encodeTo(job_link_url, job_link_message)

                echo("Game team issue found in ${job_link}, aborting the rebuild.")
            }
            result = true
        }
    }
    echo '[IsGameFailure] End'
    return result
}
