{#
    Command:
        move_location_drone
            short_help: Move Drone build to a different location.

    Arguments:

    Required variables:
        code_branch
            required: True
            help: Perforce code branch/stream name.
        code_changelist
            required: True
            help: Code changelist for the code build used to verify data.
        dest_location
            required: True
            help: Location to move the build to.

    Optional variables:
        source_location
            default: None
            help: Location to move the build from.
        data_changelist
            default: None
            help: Data changelist being verified.
        old_drone_setup
            is_flag: True
            help: Skip deploying TnT (DICE Drone builds req.)
        parallel_copy
            is_flag: True
            help: Copy platforms/configs in parallel (one per Elipy run).
        platform
            default: None
            help: Platform to copy.)
        config
            default: None
            help: Config to copy.)
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    move_location_drone
    --code-branch {{ code_branch }}
    --code-changelist {{ code_changelist }}
    --dest-location {{ dest_location }}
    {%- if source_location %}
    --source-location {{ source_location }}
    {%- endif %}
    {%- if data_changelist %}
    --data-changelist {{ data_changelist }}
    {%- endif %}
    {%- if old_drone_setup %}
    --old-drone-setup {{ old_drone_setup }}
    {%- endif %}
    {%- if parallel_copy %}
    --parallel-copy {{ parallel_copy }}
    {%- endif %}
    {%- if platform %}
    --platform {{ platform }}
    {%- endif %}
    {%- if config %}
    --config {{ config }}
    {%- endif %}
  displayName: elipy move_location_drone
