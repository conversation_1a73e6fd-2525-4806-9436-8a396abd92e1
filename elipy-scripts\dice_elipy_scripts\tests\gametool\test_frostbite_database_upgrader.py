"""
test_frostbite_database_upgrader.py

Unit testing for frostbite_database_upgrader
"""
from unittest.mock import patch

from click.testing import <PERSON><PERSON><PERSON>unner
from mock.mock import MagicMock

from dice_elipy_scripts.gametool.frostbite_database_upgrader import cli, build_fdu


@patch("os.path.join", MagicMock(side_effect=lambda *args: "\\".join(args)))
@patch("os.sep", "\\")
@patch("elipy2.frostbite_core.get_tnt_root", MagicMock(return_value="h:\\dev\\tnt"))
@patch("dice_elipy_scripts.gametool.frostbite_database_upgrader.add_sentry_tags", MagicMock())
class TestFrostbiteDatabaseUpgrader:
    OPTION_CODE_CHANGELIST = "--code-changelist"
    OPTION_CONFIG = "--config"
    OPTION_P4_PORT = "--p4-port"
    OPTION_P4_CLIENT = "--p4-client"
    OPTION_P4_USER = "--p4-user"

    VALUE_CODE_CHANGELIST = "code_changelist"
    VALUE_CONFIG = "release"
    VALUE_P4_PORT = "p4_port"
    VALUE_P4_CLIENT = "p4_client"
    VALUE_P4_USER = "p4_user"

    DEFAULT_ARGS = [
        OPTION_CODE_CHANGELIST,
        VALUE_CODE_CHANGELIST,
        OPTION_CONFIG,
        VALUE_CONFIG,
        OPTION_P4_PORT,
        VALUE_P4_PORT,
        OPTION_P4_CLIENT,
        VALUE_P4_CLIENT,
        OPTION_P4_USER,
        VALUE_P4_USER,
    ]

    FDU_BIN_P4_PATH = "h:\\dev\\tnt\\Code\\Utils\\FrostbiteDatabaseUpgrader\\bin\\..."

    @patch("dice_elipy_scripts.gametool.frostbite_database_upgrader.build_fdu")
    @patch("dice_elipy_scripts.gametool.frostbite_database_upgrader.p4.P4Utils")
    def test_build_frostbite_database_upgrader_happy_path(
        self,
        mock_p4_utils: MagicMock,
        mock_build_fdu: MagicMock,
    ):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)

        mock_p4_utils.assert_called_once_with(
            port=self.VALUE_P4_PORT, user=self.VALUE_P4_USER, client=self.VALUE_P4_CLIENT
        )
        mock_perforce = mock_p4_utils.return_value
        mock_perforce.set_environment.assert_called_once_with()

        mock_build_fdu.assert_called_once_with(self.VALUE_CODE_CHANGELIST, self.VALUE_CONFIG, False)

        mock_perforce.reconcile.assert_called_once_with(path=self.FDU_BIN_P4_PATH, options=["e"])
        mock_perforce.revert.assert_any_call(
            path=self.FDU_BIN_P4_PATH, wipe=False, only_unchanged=True
        )

        submit_message = "[Automated] Submitting rebuilt FDU binaries from cl {}".format(
            self.VALUE_CODE_CHANGELIST
        )
        mock_perforce.submit.assert_called_once_with(message=submit_message)
        mock_perforce.revert.assert_called_with(path=self.FDU_BIN_P4_PATH)
        assert mock_perforce.revert.call_count == 2

        mock_perforce.clean.assert_called_once_with(folder=self.FDU_BIN_P4_PATH)
        assert result.exit_code == 0

    @patch(
        "dice_elipy_scripts.gametool.frostbite_database_upgrader.build_fdu",
        MagicMock(side_effect=Exception),
    )
    @patch("dice_elipy_scripts.gametool.frostbite_database_upgrader.p4.P4Utils")
    def test_revert_and_clean_on_build_fdu_exception(self, mock_p4_utils):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)

        mock_perforce = mock_p4_utils.return_value
        mock_perforce.revert.assert_called_once_with(path=self.FDU_BIN_P4_PATH)
        mock_perforce.clean.assert_called_once_with(folder=self.FDU_BIN_P4_PATH)

        assert result.exit_code == 1

    @patch("dice_elipy_scripts.gametool.frostbite_database_upgrader.build_fdu", MagicMock())
    @patch("dice_elipy_scripts.gametool.frostbite_database_upgrader.p4.P4Utils")
    def test_revert_and_clean_on_submit_exception(self, mock_p4_utils):
        mock_perforce = mock_p4_utils.return_value
        mock_perforce.submit.side_effect = Exception

        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)

        mock_perforce.revert.assert_called_with(path=self.FDU_BIN_P4_PATH)
        assert mock_perforce.revert.call_count == 2
        mock_perforce.clean.assert_called_once_with(folder=self.FDU_BIN_P4_PATH)

        assert result.exit_code == 0


class TestBuildFDU:
    PLATFORM = "dbupgrade"
    CONFIG = "release"
    CODE_CHANGELIST = "123"

    @patch("dice_elipy_scripts.gametool.frostbite_database_upgrader.fbcli.run")
    @patch("dice_elipy_scripts.gametool.frostbite_database_upgrader.code.CodeUtils")
    def test_build_fdu(self, mock_code_utils: MagicMock, mock_run):
        build_fdu(self.CODE_CHANGELIST, self.CONFIG, True)
        mock_code_utils.assert_called_once_with(
            platform=self.PLATFORM,
            config=self.CONFIG,
            monkey_build_label=self.CODE_CHANGELIST,
        )
        mock_builder = mock_code_utils.return_value
        mock_builder.clean_local.assert_called_once_with(close_handles=True)
        mock_builder.buildsln.assert_called_once_with(fail_on_first_error=False)
