package com.ea.lib.model.autotest

/**
 * A model with configurations that are specific to a branch when running an Autotest job.
 */
class TestInfo {
    private static final TestSuite DEFAULT_TEST_SUITE = new TestSuite()
    /**
     * A {@link List} of names of {@link TestSuite}s to run. This is a bytecode size optimization to generate {@code tests} in this class
     * instead of {@code AutotestMatrix} children. If {@code tests} is given {@code testNames} is ignored.
     * */
    List<String> testNames = null
    /**
     * Extra icepick args. Optional.
     */
    List extraArgs = null
    /**
     * What pool should be used when running icepick. Optional.
     */
    String poolType = null
    /**
     * Do these tests require the game server binaries. Optional.
     */
    Boolean needGameServer = null
    /**
     * Optional.
     */
    Boolean fetchTests = null
    /**
     * A list of {@link Platform}s to run the tests on. Will default to global platform list. Optional.
     */
    List<Platform> platforms = []
    /**
     * How many jobs to split the test suites into if {@code runLevelsInParallel} has been set to {@code true}.
     * Defaults to the platforms count. Must be a multiple of the amount of platforms this {@link TestInfo} is configured for. Optional.
     */
    int parallelLimit
    /**
     * Should tests be ran in parallel. Default {@code false}. Optional.
     */
    Boolean runLevelsInParallel = null
    /**
     * Fail Jenkins job when test fails. Set to override value inherited from AutotestCategory. Default {@code null}, which means to keep
     * the value from AutotestCategory. Optional.
     */
    Boolean showTestResultsOnJenkins = null
    /**
     * Binary configs to use for tests. Default {@code final}. For instance {@code final} or {@code release}
     */
    String config
    /**
     * Server configs to use for tests. Default {@code final}. For instance {@code final} or {@code release}
     */
    String serverConfig
    /**
     * Custom test suite metadata to pass to Icepick. Optional.
     */
    String customTestSuiteData
    /**
     * Whether or not disable the automatic retry on failed downstream jobs due to node disconnects or other similar errors.
     * Default {@code false}
     */
    boolean disableAutomaticRetry = false
    /**
     * What elipy command should be used for these tests. Default {@code icepick_run}. Options {@code [icepick_run, icepick_run_codetests]}
     */
    String elipyCmd
    /**
     * Should sync the specified files to head. Default {@code false}.
     */
    Boolean enableSyncFiles = null
    /**
     * Path to icepick settings files. Optional.
     */
    String forceIcepickSettingsFiles
    /**
     * For tests using packages, what package type should be used. Requires {@code isTestWithLooseFiles} and {@code region} to be set.
     * For example {@code files} or {@code digital}. Optional.
     */
    String format
    /**
     * Test group name. Example: {@code lkgtests}. Icepick grouping value (used for reporting). Required.
     */
    String testGroup
    /**
     * How long in hours should the tests run before timing out. Default {@code 1}. Optional.
     */
    int timeoutHours
    /**
     * Extra arguments for Icepick to pass to any Framework commands it starts. Optional.
     */
    String extraFrameworkArgs
    /**
     * Define which Jenkins labels to run the Autotest Category on. Optional.
     */
    String jobLabel
    /**
     * Define which remote VM labels to run the Autotest Category on. Optional.
     */
    String remoteLabel
    /**
     * A {@link List} of {@link TestSuite}s to run.
     */
    List<TestSuite> tests = null

    List<TestSuite> getTests() {
        tests = tests ?: testNames.collect {
            new TestSuite(
                name: it,
                extraArgs: extraArgs ?: DEFAULT_TEST_SUITE.extraArgs,
                poolType: poolType != null ? poolType : DEFAULT_TEST_SUITE.poolType,
                needGameServer: needGameServer ?: DEFAULT_TEST_SUITE.needGameServer,
            )
        }

        return tests
    }
    /**
     * For tests using packages, what package should be used. {@code region} overrides test category {@code region}. Default {@code ww}.
     * Optional.
     */
    Region region
    /**
     * For tests using packages, what server region should be used. Default {@code ww}. Options {@code [ww, alpha, beta]}
     */
    String serverRegion
    /**
     * Overrides the test category and default channel to send Slack notifications to. Default {@code null}. Optional.
     */
    String slackChannel
    /**
     * When {@code isTestWithLooseFiles}, this will look for CLs available on all required platforms. Default {@code []}. Optional.
     *
     * TODO: migrate to enums
     */
    List<String> requiredPlatforms = []
    /**
     * When test with large scale test need more platforms to fetch as client. Default {@code []}. Optional.
     */
    List<String> clientPlatforms = []
    /**
     * What server asset to build if needed. Optional.
     */
    String serverAsset
    /**
     * Which server platform to use, can be either {@code server} (windows) or {@code linuxserver}. Default {@code server}.
     */
    String serverPlatform
    /**
     * Whether or not it's a test with Shift builds. Default {@code false}.
     */
    Boolean useShiftBuild = null
    /**
     * Whether or not it's a test with Spin builds. Default {@code false}.
     */
    Boolean useSpinBuild = null
    /**
     * Whether or not to use the latest Drone build when selecting a CL. Default {@code false}
     */
    Boolean useLatestDrone = null
    /**
     * Should tests be ran strictly sequentially: triggers one test suite at a time. Default {@code false}. Optional.
     */
    boolean strictSequential = false
    /**
     * Should tests run on category specific agents. Default {@code false}. Optional.
     */
    boolean hasPinnedAgents = false
    /**
     * Install pypiwin32 after after elipy installation
     */
    boolean installPypiwin32 = false
    /**
     * Number of times to run a test suite before considering it as passed
     */
    int numTestRuns = 1
}
