"""
Decorators and functions to throw an exception if any of
a given set of files are found
"""
from api4jenkins import <PERSON>
import os
from elipy2 import LOGGER, secrets
from elipy2.exceptions import ELIPYException


def throw_if_files_found(files_to_check=None):
    """Decorator to throw if a file is found

    :param files_to_check: will default to
    D:\\changelist.txt, D:\\data_changelist.txt, D:\\code_changelist.txt

    :returns: returns a function if files have not been found
    :raises: ELIPYException: Files have been found that should not exist.
    """

    _files_to_check = files_to_check or [f"D:\\{s}changelist.txt" for s in ["", "data_", "code_"]]

    def decorator(method):
        """
        Decorator function wrapper
        """

        def wrapper(*args, **kwargs):
            """
            Function for throwing if a given file is found
            """
            existing_files = [file for file in _files_to_check if os.path.exists(file)]
            if existing_files:
                LOGGER.warning("Files exist that shouldn't, attempting to automatically fix")
                credentials = secrets.get_secrets({"project_account": True})
                url = os.environ.get("JENKINS_URL")
                node_name = os.environ.get("NODE_NAME")

                if credentials and url and node_name:
                    # Assuming that there is only one ESS path for the project_account.
                    credentials = next(v for v in credentials.values())
                    controller = Jenkins(
                        url, auth=(credentials["username"], credentials["password"])
                    )
                    node = controller.nodes.get(node_name)
                    node.disable("[Taint] WaitingToBeIdle")
                    LOGGER.error(
                        "This Jenkins node has been marked for tainting: %s/computer/%s/",
                        url,
                        node_name,
                    )
                else:
                    LOGGER.error(
                        "Could not get credentials for tainting this Jenkins node. Please do so "
                        "manually: %s/computer/%s/markOffline",
                        url,
                        node_name,
                    )

                raise ELIPYException(
                    (
                        "Files have been found on disk that should not exist. "
                        "This means we have probably lost the VM redo file. "
                        "Files found: {0}"
                    ).format(existing_files)
                )

            value = method(*args, **kwargs)
            return value

        return wrapper

    return decorator
