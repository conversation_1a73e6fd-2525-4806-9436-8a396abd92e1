{#
    Command:
        p4_clean
            short_help: Performs a p4 clean on the given workspace.

    Arguments:
        workspace_type
            required: True
        port
            required: True
        client
            required: True
        user
            required: True
        datadir
            required: False
            default: None

    Required variables:

    Optional variables:
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    p4_clean
    workspace_type {{ workspace_type }}
    port {{ port }}
    client {{ client }}
    user {{ user }}
    datadir {{ datadir }}
  displayName: elipy p4_clean
