package scripts.schedulers.all

def project = ProjectClass(env.project_name)

/**
 * cherrypick_scheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Get changelist from Perforce') {
            steps {
                script {
                    if (env.data_stream.toBoolean() == true) {
                        P4PreviewData(project, 'cherrypick', env.target_folder, env.target_branch, env.non_virtual_data_folder, env.non_virtual_data_branch)
                    } else {
                        P4PreviewCode(project, 'cherrypick', env.target_folder, env.target_branch, env.non_virtual_code_folder, env.non_virtual_code_branch)
                    }
                }
            }
        }
        stage('Trigger cherrypick integration') {
            steps {
                script {
                    currentBuild.displayName = env.JOB_NAME + '.' + env.P4_CHANGELIST

                    def job_name = env.source_branch + '.cherrypick_integration.' + env.target_branch
                    if (env.data_stream.toBoolean() == true) {
                        job_name = env.source_branch + '.cherrypick_integration.data.' + env.target_branch
                    }
                    def cherry_job = build(job: job_name, parameters: [], propagate: false)
                    currentBuild.result = cherry_job.result.toString()

                    SlackMessageNew(currentBuild, env.slack_channel, project.short_name)
                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
}
