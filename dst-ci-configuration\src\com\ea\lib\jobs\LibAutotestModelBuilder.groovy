package com.ea.lib.jobs

import com.ea.exceptions.CobraException
import com.ea.lib.model.autotest.AutotestCategory
import com.ea.lib.model.autotest.Platform
import com.ea.lib.model.autotest.TestInfo
import com.ea.lib.model.autotest.TestSuite
import com.ea.lib.model.autotest.jobs.AutotestJobsModel
import com.ea.lib.model.autotest.jobs.AutotestModel

/**
 * Functions for creating and running parallel Autotest jobs
 */
class LibAutotestModelBuilder {

    private static final String parallelTestSuiteName = 'parallel'
    private static final String parallelUniqueBucketKey = 'parallel-jobs'
    private static final String manualJobSuffix = 'manual'
    private static final String serialJobSuffix = 'job'

    /**
     * Generates an Autotest job structure composed of an AutotestJobsModel.
     * @param testCategory The testCategory that the tests belong to
     * @param defaultCategoryPlatforms Default platforms for the category if none are defined in testInfo
     * @param defaultRunLevelsInParallel Default runLevelsInParallel if it's not defined in testInfo
     * @param branchName Name of the branch the jobs should be created for
     * @param out a logger if available, null otherwise
     * @return An AutotestJobs model
     */
    static AutotestJobsModel composeJobs(AutotestCategory testCategory, List<Platform> defaultCategoryPlatforms,
                                         boolean defaultRunLevelsInParallel, String branchName, def out) {
        boolean runLevelsInParallel = testCategory.runLevelsInParallel != null ? testCategory.runLevelsInParallel : defaultRunLevelsInParallel

        if (runLevelsInParallel) {
            List<Platform> platforms = composePlatforms(testCategory.testInfo, defaultCategoryPlatforms)
            int parallelLimit = testCategory.parallelLimit ?: platforms.size()
            return composeParallelJobsWithTestSuites(defaultCategoryPlatforms, branchName, testCategory, parallelLimit, out)
        }
        return composeSerialJobsWithTestSuites(defaultCategoryPlatforms, branchName, testCategory)
    }

    /**
     * Parses {@code testInfo} in search of unique platforms that aren't already present in {@code defaultCategoryPlatforms}
     * @param testInfo definition
     * @param defaultCategoryPlatforms {@link List} of platforms to intersect
     * @return {@link List} of platforms as an intersection of the platforms defined in {@code testInfo} and {@code defaultCategoryPlatforms}
     */
    static List<Platform> composePlatforms(TestInfo testInfo, List<Platform> defaultCategoryPlatforms) {
        List<Platform> platforms = intersectPlatformsFromTestInfo(testInfo, defaultCategoryPlatforms)
        return intersectPlatformsFromTestSuites(testInfo.tests, platforms)
    }

    /**
     * Parses {@code testInfo} in search of unique platforms that aren't already present in {@code defaultCategoryPlatforms}. A platform
     * is considered unique if both the platform name and region are unique.
     * @param testInfo definition
     * @param defaultCategoryPlatforms {@link List} of platforms to intersect
     * @return {@link List} of platforms as an intersection of the platforms defined in {@code testInfo} and {@code defaultCategoryPlatforms}
     */
    static List<Platform> composeBuildSelectorPlatforms(TestInfo testInfo, List<Platform> defaultCategoryPlatforms) {
        List<Platform> platforms = intersectPlatformsFromTestInfo(testInfo, defaultCategoryPlatforms)
        return intersectPlatformsAndRegionsFromTestSuites(testInfo.tests, platforms)
    }

    /**
     * Composes AutotestJobs for test suites that should run in parallel. Creates parallelLimit amount of jobs where parallelLimit
     * should be a multiple of the amount of platforms. Splits the jobs by platform.
     * @param defaultCategoryPlatforms The platforms the tests in the category should run on
     * @param branchName Name of the branch the jobs should be created for
     * @param testCategory The testCategory that the tests belong to
     * @param parallelLimit The amount of jobs the tests should run in parallel on
     * @param out a logger if available, null otherwise
     * @return An AutotestJobs model
     */
    private static AutotestJobsModel composeParallelJobs(List<Platform> defaultCategoryPlatforms, String branchName,
                                                         AutotestCategory testCategory, int parallelLimit, def out) {
        String categoryName = testCategory.name
        boolean isManual = testCategory.isManual
        String jobNameSuffix = getJobNameSuffix(isManual)
        List<Platform> platforms = composePlatforms(testCategory.testInfo, defaultCategoryPlatforms)
        int platformsCount = platforms.size()
        int validParallelLimit = parallelLimit
        if (!isParallelLimitValid(parallelLimit, platformsCount)) {
            if (out) {
                out.println('WARNING: ParallelLimit must be a multiple of the amount of platforms. ' +
                    "ParallelLimit: ${parallelLimit}. Platforms count: ${platformsCount}")
                out.println("Setting parallelLimit to ${platformsCount}")
            }
            validParallelLimit = platformsCount
        }
        AutotestJobsModel parallelJobs = new AutotestJobsModel()
        int suffixCounter = 0
        for (int i = 0; i < validParallelLimit; i++) {
            if (i % platformsCount == 0) {
                suffixCounter++
            }
            String suffix = "${jobNameSuffix}-${suffixCounter}"
            Platform platform = platforms[i % platformsCount]
            String name = LibAutotest.getAutotestJobName(branchName, categoryName, platform, parallelTestSuiteName, suffix)
            String region = getRegion(platform, testCategory)
            parallelJobs.insertAutotestJob(name, platform, null, region, parallelUniqueBucketKey)
        }
        return parallelJobs
    }

    /**
     * Returns the test category's region, null if not set.
     * @param platformName which platform to find the region for
     * @param testCategory test configuration to parse
     * @return the test category's region for the given branch
     */
    private static String getRegion(Platform platformName, AutotestCategory testCategory) {
        List<Platform> platforms = testCategory.testInfo.platforms
        Platform platform = platforms.find { it.toString() == platformName.toString() }
        return platform?.region ?: testCategory.testInfo.region ?: testCategory.region ?: null
    }

    /**
     * Composes AutotestJobs for test suites that should run in parallel. Creates parallelLimit amount of jobs where parallelLimit
     * should be a multiple of the amount of platforms. Splits the jobs by platform. Also adds test suites to the AutotestJob models.
     * @param defaultCategoryPlatforms The platforms the tests in the category should run on
     * @param branchName Name of the branch the jobs should be created for
     * @param testCategory The testCategory that the tests belong to
     * @param parallelLimit The amount of jobs the tests should run in parallel on
     * @param out a logger if available, null otherwise
     * @return An AutotestJobs model with test suites
     */
    private static AutotestJobsModel composeParallelJobsWithTestSuites(List<Platform> defaultCategoryPlatforms,
                                                                       String branchName, AutotestCategory testCategory, int parallelLimit, def out) {
        def tests = testCategory.testInfo.tests
        AutotestJobsModel autotestJobs = composeParallelJobs(defaultCategoryPlatforms, branchName, testCategory, parallelLimit, out)
        List<Platform> basePlatforms = testCategory.testInfo.platforms ?: defaultCategoryPlatforms

        tests.each { test ->
            List<Platform> platforms = test.platforms ?: basePlatforms
            platforms.each { platform ->
                AutotestModel autotestJob = autotestJobs.insertTestSuite(test, platform, null)
                if (autotestJob == null) {
                    throw new CobraException("No job found for test suite: ${test.name}, platform: ${platform}.")
                }
            }
        }
        autotestJobs.removeEmptyAutotestModels()
        return autotestJobs
    }

    /**
     * Composes AutotestJobs for test suites that should run in serial
     * @param defaultCategoryPlatforms The platforms the tests in the category should run on
     * @param branchName Name of the branch the jobs should be created for
     * @param testCategory The testCategory that the tests belong to
     * @return AutotestJobs model
     */
    private static AutotestJobsModel composeSerialJobsWithTestSuites(List<Platform> defaultCategoryPlatforms, String branchName, AutotestCategory testCategory) {
        boolean isManual = testCategory.isManual
        String jobNameSuffix = getJobNameSuffix(isManual)
        List<TestSuite> tests = testCategory.testInfo.tests
        String categoryName = testCategory.name
        boolean strictSequential = testCategory.testInfo.strictSequential
        List<Platform> basePlatforms = testCategory.testInfo.platforms ?: defaultCategoryPlatforms
        AutotestJobsModel serialJobs = new AutotestJobsModel()
        for (test in tests) {
            List<Platform> platforms = test.platforms ?: basePlatforms
            platforms.each { platform ->
                String name = LibAutotest.getAutotestJobName(branchName, categoryName, platform, test.name, jobNameSuffix)
                String extraArguments = platform.extraArgs
                String bucketKey = strictSequential ? test.name + platform : test.name
                AutotestModel autotestJob = serialJobs.insertAutotestJob(name, platform, extraArguments, platform.region.toString(), bucketKey)
                serialJobs.insertTestSuite(test, platform, autotestJob)
            }
        }
        return serialJobs
    }

    /**
     * Decides whether to use default platforms or not
     * @param testInfo definition
     * @param right Array of default platforms
     * @return List of platforms
     */
    private static List<Platform> intersectPlatformsFromTestInfo(TestInfo testInfo, List<Platform> right) {
        return testInfo.platforms ?: right
    }

    /**
     * Creates an array composed of the intersection of input platforms and the defined platforms in the tests, if any.
     * Doesn't add any duplicates.
     * @param tests The tests to parse
     * @param right Array of platforms to intersect
     * @return List of platforms as an intersection of the platforms defined in the tests and right
     */
    private static List<Platform> intersectPlatformsFromTestSuites(List<TestSuite> tests, List<Platform> right) {
        List<Platform> platforms = right.collect { new Platform(name: it.name) }
        tests.each { test ->
            test.platforms.each { platform ->
                boolean addToList = true
                for (platformToCompareTo in platforms) {
                    if (platformToCompareTo.toString() == platform.toString()) {
                        addToList = false
                        break
                    }
                }
                if (addToList) {
                    platforms << platform
                }
            }
        }
        return platforms
    }

    /**
     * Creates an array composed of the intersection of input platforms and the defined platforms in the tests, if any.
     * Doesn't add any duplicates. A platform is considered unique if both the platform name and region are unique.
     * @param tests The tests to parse
     * @param right Array of platforms to intersect
     * @return List of platforms as an intersection of the platforms defined in the tests and right
     */
    private static List<Platform> intersectPlatformsAndRegionsFromTestSuites(List<TestSuite> tests, List<Platform> right) {
        List<Platform> platforms = []
        platforms.addAll(right)
        tests.each { test ->
            test.platforms.each { platform ->
                boolean addToList = true
                for (platformToCompareTo in platforms) {
                    if (platformToCompareTo.toString() == platform.toString() && platformToCompareTo.region == platform.region) {
                        addToList = false
                        break
                    }
                }
                if (addToList) {
                    platforms << platform
                }
            }
        }
        return platforms
    }

    /**
     * ParallelLimit must be a multiple of the amount of platforms
     * @param parallelLimit the parallel limit to check
     * @param platformsCount how many platforms there are in the test category
     * @return True if the set parallelLimit is valid, falseNo job found for test suite otherwise
     */
    private static boolean isParallelLimitValid(int parallelLimit, int platformsCount) {
        return parallelLimit % platformsCount == 0
    }

    /**
     * Returns the job name suffix
     * @param isManual whether or not the job is manual or not
     * @return the suffix to append to the end of the job name
     */
    private static String getJobNameSuffix(boolean isManual) {
        return isManual ? manualJobSuffix : serialJobSuffix
    }

}
