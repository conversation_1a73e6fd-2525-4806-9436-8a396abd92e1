/**
 * Manually added documentation for pipelineJob: https://jenkinsci.github.io/job-dsl-plugin/#path/pipelineJob
 */
def closures = context(scope: closureScope())
contributor(closures) {
    if (enclosingCall('pipelineJob') || enclosingCall('with')) {
        method(name: 'pipelineTriggers', params: [body: 'Closure'])
        method(name: 'triggers', params: [body: 'Closure'])
        method(name: 'cron', params: [body: 'Closure'])
        method(name: 'spec', params: [spec: 'java.lang.String'])


        method(name: 'authenticationToken', params: [token: 'java.lang.String'], doc: 'Provide an authorization token in the form of a string so that only those who know it would be able to remotely trigger this project\'s builds.')
        method(name: 'authorization', params: [body: 'Closure'], doc: 'Creates permission records.')
        if (enclosingCall('authorization')) {
            method(name: 'blocksInheritance', params: [blocksInheritance: 'java.lang.Boolean'], doc: 'Blocks inheritance of the global authorization matrix.')
            method(name: 'permission', params: [permission: 'java.lang.String'], doc: 'Adds a specific permission.')
            method(name: 'permission', params: [permission: 'java.lang.String', user: 'java.lang.String'], doc: 'Adds a specific permission, but breaks apart the permission from the user name.')
            method(name: 'permission', params: [user: 'java.lang.String'], doc: 'Adds all available permissions for the user.')
            method(name: 'permission', params: [user: 'java.lang.String', permissionsList: 'Iterable<String>'], doc: 'Adds a set of permissions for a user.')
        }
        method(name: 'blockOn', params: [projectNames: 'Iterable<String>'], doc: 'Block build if certain jobs are running.')
        method(name: 'blockOn', params: [projectNames: 'Iterable<String>', body: 'Closure'], doc: 'Block build if certain jobs are running.')
        method(name: 'blockOn', params: [projectName: 'java.lang.String'], doc: 'Block build if certain job is running.')
        method(name: 'blockOn', params: [projectName: 'java.lang.String', body: 'Closure'], doc: 'Block build if certain job is running.')
        if (enclosingCall('blockOn')) {
            method(name: 'blockLevel', params: [blockLevel: 'java.lang.String'], doc: 'Possible values are \'GLOBAL\' and \'NODE\' (default).')
            method(name: 'scanQueueFor', params: [scanQueueFor: 'java.lang.String'], doc: 'Possible values are \'ALL\', \'BUILDABLE\' and \'DISABLED\' (default).')
        }
        method(name: 'compressBuildLog', doc: 'Compresses the log file after build completion.')
        method(name: 'concurrentBuild', params: [allowConcurrentBuild: 'java.lang.Boolean'], doc: 'Disables the job, so that no new builds will be executed until the project is re-enabled.')
        method(name: 'configure', params: [body: 'Closure'], doc: 'Allows direct manipulation of the generated XML.')
        method(name: 'definition', params: [body: 'Closure'], doc: 'Adds a workflow definition.')
        if (enclosingCall('definition')) {
            method(name: 'cps', params: [body: 'Closure'], doc: 'Defines a Groovy CPS DSL definition.')
            if (enclosingCall('cps')) {
                method(name: 'sandbox', params: [sandbox: 'java.lang.Boolean'], doc: 'Enables the Groovy sandbox for the script.')
                method(name: 'sandbox', doc: 'Enables the Groovy sandbox for the script.')
                method(name: 'script', params: [script: 'java.lang.String'], doc: 'Sets the workflow DSL script.')
            }
            method(name: 'cpsScm', params: [body: 'Closure'], doc: 'Loads a pipeline script from SCM.')
            if (enclosingCall('cpsScm')) {
                method(name: 'lightweight', params: [lightweight: 'java.lang.Boolean'], doc: 'If selected, try to obtain the Pipeline script contents directly from the SCM without performing a full checkout.')
                method(name: 'lightweight', doc: 'If selected, try to obtain the Pipeline script contents directly from the SCM without performing a full checkout.')
                method(name: 'cps', params: [body: 'Closure'], doc: 'Specifies where to obtain a source code repository containing the pipeline script.')
                if (enclosingCall('cps')) {
                    method(name: 'cloneWorkspace', params: [parentProject: 'java.lang.String', criteria: 'java.lang.String'], doc: 'Add a SCM source which copies the workspace of another project.')
                    // The rest are missing. Add them if we start using this.
                }
                method(name: 'scriptPath', params: [scriptPath: 'java.lang.String'], doc: 'Sets the relative location of the pipeline script within the source code repository.')
            }
        }
        method(name: 'deliveryPipelineConfiguration', params: [stageName: 'java.lang.String', taskName: 'java.lang.String'], doc: 'Sets the stage name and task name for the delivery pipeline view.')
        method(name: 'description', params: [descriptionString: 'java.lang.String'], doc: 'Sets a description for the item.')
        method(name: 'disabled', params: [shouldDisable: 'java.lang.Boolean'], doc: 'Disables the job, so that no new builds will be executed until the project is re-enabled.')
        method(name: 'displayName', params: [displayName: 'java.lang.String'], doc: 'Sets the name to display instead of the actual name.')
        method(name: 'environmentVariables', params: [body: 'Closure'], doc: 'Adds environment variables to the build.')
        method(name: 'environmentVariables', params: [vars: 'Map<Object, Object>', body: 'Closure'], doc: 'Adds environment variables to the build.')
        if (enclosingCall('environmentVariables')) {
            method(name: 'contributors', params: [body: 'Closure'], doc: 'Add environment and build variable contributors provided by other plugins.')
            if (enclosingCall('contributors')) {
                method(name: 'populateToolInstallations', doc: 'Populates the locations of installed tools as environment variables.')
            }
            method(name: 'env', params: [key: 'Object', value: 'Object'], doc: 'Adds an environment variable to the build.')
            method(name: 'envs', params: [map: 'Map<Object, Object>'], doc: 'Adds environment variables to the build.')
            method(name: 'groovy', params: [script: 'java.lang.String'], doc: 'Evaluates a Groovy script and inject a map result.')
            method(name: 'keepBuildVariables', params: [keepBuildVariables: 'java.lang.Boolean'], doc: 'Inject Jenkins build variables and also environment contributors and build variable contributors provided by other plugins.')
            method(name: 'keepSystemVariables', params: [keepSystemVariables: 'java.lang.Boolean'], doc: 'Injects Jenkins system variables and environment variables defined as global properties and as node properties.')
            method(name: 'loadFilesFromMaster', params: [loadFilesFromMaster: 'java.lang.Boolean'], doc: 'Load files (properties or scripts) from the master node.')
            method(name: 'overrideBuildParameters', params: [overrideBuildParameters: 'java.lang.Boolean'], doc: 'Allows environment variables to override build parameters.')
            method(name: 'overrideBuildParameters', doc: 'Allows environment variables to override build parameters.')
            method(name: 'propertiesFile', params: [propertiesFilePath: 'java.lang.String'], doc: 'Adds environment variables from a properties file.')
            method(name: 'script', params: [script: 'java.lang.String'], doc: 'Executes a script aimed at setting an environment such as creating folders, copying files, and so on.')
            method(name: 'scriptFile', params: [scriptFilePath: 'java.lang.String'], doc: 'Executes a script file aimed at setting an environment such as create folders, copy files, and so on.')
        }
        method(name: 'keepDependencies', params: [keep: 'java.lang.Boolean'], doc: 'Protects all builds that are referenced from builds of this project (via fingerprint) from log rotation.')
        method(name: 'logRotator', params: [daysToKeep: 'java.lang.Integer', numToKeep: 'java.lang.Integer', artifactDaysToKeep: 'java.lang.Integer', artifactNumToKeep: 'java.lang.Integer'], doc: 'Manages how long to keep records of the builds.')
        method(name: 'logRotator', params: [daysToKeep: 'java.lang.Integer', numToKeep: 'java.lang.Integer'], doc: 'Manages how long to keep records of the builds.')
        method(name: 'logRotator', params: [body: 'Closure'], doc: 'Manages how long to keep records of the builds.')
        if (enclosingCall('logRotator')) {
            method(name: 'artifactDaysToKeep', params: [artifactDaysToKeep: 'java.lang.Integer'], doc: 'If specified, artifacts from builds older than this number of days will be deleted, but the logs, history, reports, etc for the build will be kept.')
            method(name: 'artifactNumToKeep', params: [artifactNumToKeep: 'java.lang.Integer'], doc: 'If specified, only up to this number of builds have their artifacts retained.')
            method(name: 'daysToKeep', params: [daysToKeep: 'java.lang.Integer'], doc: 'If specified, build records are only kept up to this number of days.')
            method(name: 'numToKeep', params: [numToKeep: 'java.lang.Integer'], doc: 'If specified, only up to this number of build records are kept.')
        }
        method(name: 'notifications', params: [body: 'Closure'], doc: 'Configures notifications for the build.')
        if (enclosingCall('notifications')) {
            method(name: 'endpoint', params: [url: 'java.lang.String', protocol: 'java.lang.String', format: 'java.lang.String'], doc: 'Adds an endpoint which will receive notifications about the job status.')
            method(name: 'endpoint', params: [url: 'java.lang.String'], doc: 'Adds an endpoint which will receive notifications about the job status.')
            method(name: 'endpoint', params: [url: 'java.lang.String', protocol: 'java.lang.String', format: 'java.lang.String', body: 'Closure'], doc: 'Adds an endpoint which will receive notifications about the job status.')
            if (enclosingCall('endpoint')) {
                method(name: 'event', params: [event: 'java.lang.String'], doc: 'Sets the job lifecycle event triggering notification.')
                method(name: 'logLines', params: [logLines: 'java.lang.Integer'], doc: 'Sets the number lines of log messages to send.')
                method(name: 'timeout', params: [timeout: 'java.lang.Integer'], doc: 'Sets a timeout in milliseconds.')
            }
        }
        method(name: 'parameters', params: [body: 'Closure'], doc: 'Allows to parameterize the job.\n')
        if (enclosingCall('parameters')) {
            method(name: 'activeChoiceParam', params: [parameterName: 'java.lang.String', body: 'Closure'], doc: 'Defines a parameter that dynamically generates a list of value options for a build parameter using a Groovy script or a script from the Scriptler catalog.')
            method(name: 'activeChoiceReactiveParam', params: [parameterName: 'java.lang.String', body: 'Closure'], doc: 'Defines a parameter that dynamically generates a list of value options for a build parameter using a Groovy script or a script from the Scriptler catalog and that dynamically updates when the value of other job UI controls change.')
            method(name: 'activeChoiceReactiveReferenceParam', params: [parameterName: 'java.lang.String', body: 'Closure'], doc: 'Defines a parameter that dynamically generates a list of value options for a build parameter using a Groovy script or a script from the Scriptler catalog and that dynamically updates when the value of other job UI controls change.')
            if (enclosingCall('activeChoiceParam') || enclosingCall('activeChoiceReactiveParam') || enclosingCall('activeChoiceReactiveReferenceParam')) {
                method(name: 'choiceType', params: [choiceType: 'java.lang.String'], doc: 'Selects one of four different rendering options for the option values.')
                method(name: 'description', params: [description: 'java.lang.String'], doc: 'Sets a description for the parameter.')
                method(name: 'groovyScript', params: [body: 'Closure'], doc: 'Use a Groovy script to generate value options.')
                if (enclosingCall('groovyScript')) {
                    method(name: 'fallbackScript', params: [fallbackScript: 'java.lang.String'], doc: 'Provides alternate parameter value options in case the main script fails.')
                    method(name: 'script', params: [script: 'java.lang.String'], doc: 'Sets the script that will dynamically generate the parameter value options.')
                }
            }
            if (enclosingCall('activeChoiceParam')) {
                method(name: 'filterable ', params: [filterable: 'java.lang.String'], doc: 'If set, provides a text box filter in the UI control where a text filter can be typed.')
            }
            if (enclosingCall('activeChoiceReactiveParam')) {
                method(name: 'filterable ', params: [filterable: 'java.lang.String'], doc: 'If set, provides a text box filter in the UI control where a text filter can be typed.')
                method(name: 'referencedParameter', params: [referencedParameters: 'java.lang.String'], doc: 'Specifies a list of job parameters that trigger an auto-refresh.')

            }
            if (enclosingCall('activeChoiceReactiveReferenceParam')) {
                method(name: 'omitValueField', params: [omitValueField: 'java.lang.Boolean'], doc: 'Omits the hidden value field.')
                method(name: 'referencedParameter', params: [referencedParameters: 'java.lang.String'], doc: 'Specifies a list of job parameters that trigger an auto-refresh.')
            }
            method(name: 'booleanParam', params: [parameterName: 'java.lang.String', defaultValue: 'java.lang.Boolean', description: 'java.lang.String'], doc: 'Defines a simple boolean parameter.')
            method(name: 'buildSelectorParam', params: [parameterName: 'java.lang.String', body: 'Closure'], doc: 'Defines a parameter that allows to specify a build selector for the copy artifact plugin.')
            if (enclosingCall('buildSelectorParam')) {
                method(name: 'defaultBuildSelector', params: [body: 'Closure'], doc: 'Specifies the default build selector.')
                if (enclosingCall('defaultBuildSelector')) {
                    method(name: 'buildNumber', params: [buildNumber: 'java.lang.Integer'], doc: 'Selects a specific build.')
                    method(name: 'buildNumber', params: [buildNumber: 'java.lang.String'], doc: 'Selects a specific build.')
                    method(name: 'buildParameter', params: [parameterName: 'java.lang.String'], doc: 'Selects a build by parameter.')
                    method(name: 'latestSaved', doc: 'Selects the latest saved build (marked "keep forever").')
                    method(name: 'latestSuccessful', params: [stable: 'java.lang.Boolean'], doc: 'Selects the latest successful build.')
                    method(name: 'multiJobBuild', doc: 'Selects a build triggered by the current MultiJob build.')
                    method(name: 'permalink', params: [linkName: 'java.lang.String'], doc: 'Selects a build by permalink.')
                    method(name: 'upstreamBuild', params: [fallback: 'java.lang.Boolean'], doc: 'Selects the upstream build that triggered this job.')
                    method(name: 'upstreamBuild', params: [body: 'Closure'], doc: 'Selects the upstream build that triggered this job.')
                    if (enclosingCall('upstreamBuild')) {
                        method(name: 'allowUpstreamDependencies', params: [allowUpstreamDependencies: 'java.lang.Boolean'], doc: 'Allow upstream build whose artifacts feed into this build.')
                        method(name: 'fallbackToLastSuccessful', params: [fallbackToLastSuccessful: 'java.lang.Boolean'], doc: 'Use "Last successful build" as fallback.')
                    }
                    method(name: 'workspace', doc: 'Copies from workspace of latest completed build.')
                }
                method(name: 'description', params: [description: 'java.lang.String'], doc: 'Specifies the parameter description.')
            }
            method(name: 'choiceParam', params: [parameterName: 'java.lang.String', options: 'List<String>', description: 'java.lang.String'], doc: 'Defines a simple string parameter, which can be selected from a list.')
            method(name: 'credentialsParam', params: [paramName: 'java.lang.String', body: 'Closure'], doc: 'Defines a credentials parameter.')
            if (enclosingCall('credentialsParam')) {
                method(name: 'defaultValue', params: [defaultValue: 'java.lang.String'], doc: 'Sets the default value for the parameter.')
                method(name: 'description', params: [description: 'java.lang.String'], doc: 'Sets a description for the parameter.')
                method(name: 'type', params: [required: 'java.lang.Boolean'], doc: 'If set, a value must be selected.')
                method(name: 'type', params: [type: 'java.lang.String'], doc: 'Specifies the type of credentials which should be selectable by the parameter.')
            }
            method(name: 'fileParam', params: [fileLocation: 'java.lang.String', description: 'java.lang.String'], doc: 'Defines a parameters that accepts a file submission.')
            method(name: 'gitParam', params: [parameterName: 'java.lang.String', body: 'Closure'], doc: 'Defines a parameter that allows select a Git tag (or revision number).')
            if (enclosingCall('gitParam')) {
                method(name: 'branch', params: [branch: 'java.lang.String'], doc: 'Set the name of branch to look in.')
                method(name: 'defaultValue', params: [defaultValue: 'java.lang.String'], doc: 'Sets a default value for the parameter.')
                method(name: 'description', params: [description: 'java.lang.String'], doc: 'Sets a description for the parameter.')
                method(name: 'sortMode', params: [sortMode: 'java.lang.String'], doc: 'Specifies the sort order for tags.')
                method(name: 'tagFilter', params: [tagFilter: 'java.lang.String'], doc: 'Specifies a filter for tags.')
                method(name: 'type', params: [type: 'java.lang.String'], doc: 'Specifies the type of selectable values.')
            }
            method(name: 'globalVariableParam', params: [parameterName: 'java.lang.String', defaultValue: 'java.lang.String', description: 'java.lang.String'], doc: 'Defines a parameter that references a global variable.')
            method(name: 'labelParam', params: [parameterName: 'java.lang.String', body: 'Closure'], doc: 'Defines a parameter to select a label used to identify/restrict the node where this job should run on.')
            if (enclosingCall('labelParam')) {
                method(name: 'allNodes', params: [trigger: 'java.lang.String', eligibility: 'java.lang.String'], doc: 'Defines in which case a build on the next node should be triggered.')
                method(name: 'defaultValue', params: [defaultValue: 'java.lang.String'], doc: 'Sets a default value for the parameter.')
                method(name: 'description', params: [description: 'java.lang.String'], doc: 'Sets a description for the parameter.')
            }
            method(name: 'listTagsParam', params: [parameterName: 'java.lang.String', scmUrl: 'java.lang.Strin', tagFilterRegex: 'java.lang.String', sortNewestFirst: 'java.lang.Boolean', sortZtoA: 'java.lang.Boolean', maxTagsToDisplay: 'java.lang.String', defaultValue: 'java.lang.String', description: 'java.lang.String'], doc: 'Defines a parameter that allows to select a Subversion tag from which to create the working copy for the project.')
            method(name: 'listTagsParam', params: [parameterName: 'java.lang.String', scmUrl: 'java.lang.String', body: 'Closure'], doc: 'Defines a parameter that allows to select a Subversion tag from which to create the working copy for the project.')
            if (enclosingCall('listTagsParam')) {
                method(name: 'credentialsId', params: [credentialsId: 'java.lang.String'], doc: 'Sets credentials for authentication with the remote Subversion server.')
                method(name: 'defaultValue', params: [defaultValue: 'java.lang.String'], doc: 'Sets the default value for the parameter.')
                method(name: 'description', params: [description: 'java.lang.String'], doc: 'Sets a description for the parameter.')
                method(name: 'maxTagsToDisplay', params: [maxTagsToDisplay: 'java.lang.String'], doc: 'Specifies the maximum number of tags to display in the dropdown.')
                method(name: 'maxTagsToDisplay', params: [maxTagsToDisplay: 'java.lang.Integer'], doc: 'Specifies the maximum number of tags to display in the dropdown.')
                method(name: 'sortNewestFirst', params: [sortNewestFirst: 'java.lang.Boolean'], doc: 'If set, sorts tags from newest to oldest.')
                method(name: 'sortZtoA', params: [sortZtoA: 'java.lang.Boolean'], doc: 'It set, displays tags in reverse order (sorted Z to A).')
                method(name: 'tagFilterRegex', params: [tagFilterRegex: 'java.lang.String'], doc: 'Specifies a regular expression which will be used to filter the tags which are actually displayed when triggering a new build.')
            }
            method(name: 'matrixCombinationsParam', params: [parameterName: 'java.lang.String', defaultValue: 'java.lang.String', description: 'java.lang.String'], doc: 'Defines a parameter that allows to choose which matrix combinations to run.')
            method(name: 'nodeParam', params: [parameterName: 'java.lang.String', body: 'Closure'], doc: 'Defines a parameter to select a list of nodes where the job could potentially be executed on.')
            if (enclosingCall('nodeParam')) {
                method(name: 'allowedNodes', params: [nodes: 'List<String>'], doc: 'Specifies the nodes available for selection when job gets triggered manually.')
                method(name: 'defaultNodes', params: [defaultNodes: 'List<String>'], doc: 'Specifies the nodes used when job gets triggered by anything else then manually.')

                method(name: 'description', params: [description: 'java.lang.String'], doc: 'Sets a description for the parameter.')
                method(name: 'eligibility', params: [eligibility: 'java.lang.String'], doc: 'Defines how selected offline nodes should be handled.')
                method(name: 'trigger', params: [trigger: 'java.lang.String'], doc: 'Defines in which case a build on the next node should be triggered.')
            }
            method(name: 'nonStoredPasswordParam', params: [parameterName: 'java.lang.String', description: 'java.lang.String'], doc: 'Defines a parameter that allows to take in a user\'s password.')
            method(name: 'runParam', params: [parameterName: 'java.lang.String', jobToRun: 'java.lang.String', filter: 'java.lang.String'], doc: 'Defines a run parameter, where users can pick a single run of a certain project.')
            method(name: 'runParam', params: [parameterName: 'java.lang.String', jobToRun: 'java.lang.String', filter: 'java.lang.String'], doc: 'Defines a run parameter, where users can pick a single run of a certain project.')
            method(name: 'stringParam', params: [parameterName: 'java.lang.String', defaultValue: 'java.lang.String', description: 'java.lang.String'], doc: 'Defines a simple text parameter, where users can enter a string value.')
            method(name: 'stringParam', params: [body: 'Closure'], doc: 'Defines a simple text parameter, where users can enter a string value')
            if (enclosingCall('stringParam')) {
                method(name: 'name', params: [name: 'java.lang.String'], doc: 'The name of the parameter')
                method(name: 'defaultValue', params: [defaultValue: 'java.lang.String'], doc: 'Sets a default value for the item.')
                method(name: 'trim', params: [name: 'java.lang.Boolean'], doc: 'Whether or not to trim the input')
                method(name: 'description', params: [description: 'java.lang.String'], doc: 'Sets a description for the parameter.')
            }
            method(name: 'textParam', params: [parameterName: 'java.lang.String', defaultValue: 'java.lang.String', description: 'java.lang.String'], doc: 'Defines a simple text parameter, where users can enter a multi-line string value.')
        }
        method(name: 'previousNames', params: [regex: 'java.lang.String'], doc: 'Renames jobs matching the regular expression to the name of this job before the configuration is updated.')
        method(name: 'properties', params: [body: 'Closure'], doc: 'Adds custom properties to the job.')
        if (enclosingCall('properties')) {
            method(name: 'buildFailureAnalyzer', params: [scan: 'java.lang.String'], doc: 'Analyzes the causes of failed builds and presents the causes on the build page.')
            method(name: 'disableResume')
            method(name: 'customIcon', params: [iconFileName: 'java.lang.String'], doc: 'Allows to configure a custom icon for each job.')
            method(name: 'githubProjectUrl', params: [projectUrl: 'java.lang.String'], doc: 'Configures the GitHub project URL.')
            method(name: 'ownership', params: [body: 'Closure'], doc: 'Allows to configure job ownership.')
            if (enclosingCall('ownership')) {
                method(name: 'coOwnerIds', params: [userIds: 'java.lang.String...'], doc: 'Adds additional users, who have ownership privileges.')
                method(name: 'coOwnerIds', params: [userIds: 'Iterable<String>'], doc: 'Adds additional users, who have ownership privileges.')
                method(name: 'primaryOwnerId', params: [primaryOwnerId: 'java.lang.String'], doc: 'Sets the name of the primary owner of the job.')
            }
            method(name: 'priority', params: [value: 'java.lang.Integer'], doc: 'Sets the priority of the job.')
            method(name: 'rebuild', params: [body: 'Closure'], doc: 'Allows to configure job rebuild behaviour.')
            if (enclosingCall('rebuild')) {
                method(name: 'autoRebuild', params: [autoRebuild: 'java.lang.Boolean'], doc: 'Rebuilds job without asking for parameters.')
                method(name: 'rebuildDisabled', params: [rebuildDisabled: 'java.lang.Boolean'], doc: 'Disables job rebuilding.')
            }
            method(name: 'sidebarLinks', params: [body: 'Closure'], doc: 'Adds links in the sidebar of the project page.')
            if (enclosingCall('sidebarLinks')) {
                method(name: 'link', params: [url: 'java.lang.String', text: 'java.lang.String', icon: 'java.lang.String'])
            }
            method(name: 'wallDisplay', params: [body: 'Closure'], doc: 'Configures job appearance for wall display.')
            if (enclosingCall('wallDisplay')) {
                method(name: 'backgroundPicture', params: [picture: 'java.lang.String'], doc: 'Background picture to use for wall display.')
                method(name: 'name', params: [name: 'java.lang.String'], doc: 'Custom text to use for wall display.')
            }
            method(name: 'zenTimestamp', params: [pattern: 'java.lang.String'], doc: 'Changes the date pattern for the BUILD_ID or BUILD_TIMESTAMP variable.')
        }
        method(name: 'quietPeriod', params: [seconds: 'java.lang.Integer'], doc: 'Defines a timespan (in seconds) to wait for additional events (pushes, check-ins) before triggering a build.')
        method(name: 'throttleConcurrentBuilds', params: [body: 'Closure'], doc: 'Throttles the number of concurrent builds of a project running per node or globally.')
        if (enclosingCall('throttleConcurrentBuilds')) {
            method(name: 'categories', params: [categories: 'List<String>'], doc: 'Throttles this project as part of one or more categories.')
            method(name: 'maxPerNode', params: [maxPerNode: 'java.lang.Integer'], doc: 'Sets the maximum number of concurrent builds of this project (or category) to be allowed to run per node.')
            method(name: 'maxTotal', params: [maxTotal: 'java.lang.Integer'], doc: 'Sets the maximum number of concurrent builds of this project (or category) to be allowed to run at any one time, across all nodes.')
            method(name: 'throttleDisabled', params: [throttleDisabled: 'java.lang.Boolean'], doc: 'Disables the throttle.')
            method(name: 'throttleMatrixBuilds', params: [throttleMatrixBuilds: 'java.lang.Boolean'], doc: 'If set, throttles Matrix master builds.')
            method(name: 'throttleMatrixConfigurations', params: [throttleMatrixConfigurations: 'java.lang.Boolean'], doc: 'If set, throttles Matrix configuration builds.')
        }
        method(name: 'triggers', params: [body: 'Closure'])
        if (enclosingCall('triggers')) {
            method(name: 'bitbucketPush', doc: 'Trigger that runs jobs on push notifications from Bitbucket.')
            method(name: 'buildResult', params: [crontab: 'java.lang.String', body: 'Closure'], doc: 'Trigger that runs jobs on build results of others jobs')
            if (enclosingCall('buildResult')) {
                method(name: 'combinedJobs', params: [combinedJobs: 'java.lang.Boolean'], doc: 'Combine all jobs informations.')
            }
            method(name: 'cron', params: [cronString: 'java.lang.String'], doc: 'Triggers the job based on regular intervals.')
            method(name: 'dos', params: [cronString: 'java.lang.String', body: 'Closure'], doc: 'Trigger a build with a DOS script.')
            if (enclosingCall('dos')) {
                method(name: 'triggerScript', params: [triggerScript: 'java.lang.String'], doc: 'It sets the script that will be executed periodically which indicates that a build should be started when the script sets the CAUSE variable to something.')
            }
            method(name: 'gerrit', params: [body: 'Closure'], doc: 'Polls Gerrit for changes.')
            if (enclosingCall('gerrit')) {
                method(name: 'buildFailed', params: [verified: 'java.lang.Integer', codeReview: 'java.lang.Integer'], doc: 'The Verified and Code Review vote to set in Gerrit when the build fails.')
                method(name: 'buildNotBuilt', params: [verified: 'java.lang.Integer', codeReview: 'java.lang.Integer'], doc: 'The Verified and Code Review vote to set in Gerrit when no build was built.')
                method(name: 'buildStarted', params: [verified: 'java.lang.Integer', codeReview: 'java.lang.Integer'], doc: 'The Verified and Code Review vote to set in Gerrit when the build starts.')
                method(name: 'buildSuccessful', params: [verified: 'java.lang.Integer', codeReview: 'java.lang.Integer'], doc: 'The Verified and Code Review vote to set in Gerrit when the build is successful.')
                method(name: 'buildUnstable', params: [verified: 'java.lang.Integer', codeReview: 'java.lang.Integer'], doc: 'The Verified and Code Review vote to set in Gerrit when the build is unstable.')
                method(name: 'configure', params: [configureBlock: 'Closure'], doc: 'Allows direct manipulation of the generated XML.')
                method(name: 'events', params: [configureBlock: 'Closure'], doc: 'Specifies which type of Gerrit events should trigger the build.')
                if (enclosingCall('events')) {
                    method(name: 'changeAbandoned', doc: 'Trigger when a change is abandoned.')
                    method(name: 'changeMerged', doc: 'Trigger when a change is merged/submitted.')
                    method(name: 'changeRestored', doc: 'Trigger when a change is restored.')
                    method(name: 'commentAdded', doc: 'Trigger when a review comment is left with the indicated vote category and value.')
                    method(name: 'draftPublished', doc: 'Trigger when a draft change or patch set is published.')
                    method(name: 'patchsetCreated', doc: 'Trigger when a new change or patch set is uploaded.')
                    method(name: 'refUpdated', doc: 'Trigger when a reference (e.g., branch or tag) is updated.')
                }
                method(name: 'project', params: [projectName: 'java.lang.String', branches: 'List<String>'], doc: 'Specifies on which Gerrit projects to trigger a build on.')
                method(name: 'project', params: [projectName: 'java.lang.String', branch: 'java.lang.String'], doc: 'Specifies on which Gerrit projects to trigger a build on.')
            }
            method(name: 'githubPush', doc: 'Trigger that runs jobs on push notifications from GitHub.')
            method(name: 'githubPush', params: [body: 'Closure'], doc: 'Trigger that runs jobs on push notifications from GitHub.')
            if (enclosingCall('githubPush')) {
                method(name: 'buildOnMergeRequestEvents', params: [buildOnMergeRequestEvents: 'java.lang.Boolean'], doc: 'If set, builds on merge request events.')
                method(name: 'buildOnPushEvents', params: [buildOnPushEvents: 'java.lang.Boolean'], doc: 'If set, builds on push events request events.')
                method(name: 'commentTrigger', params: [commentTrigger: 'java.lang.String'], doc: 'When filled, commenting this phrase in the merge request will trigger a build.')
                method(name: 'enableCiSkip', params: [enableCiSkip: 'java.lang.Boolean'], doc: 'If set, enables [ci-skip].')
                method(name: 'excludeBranches', params: [excludeBranches: 'java.lang.String'], doc: 'Comma-separated list of source branches disabled to trigger a build from a push event.')
                method(name: 'includeBranches', params: [includeBranches: 'java.lang.String'], doc: 'Comma-separated list of source branches allowed to trigger a build from a push event.')
                method(name: 'rebuildOpenMergeRequest', params: [rebuildOpenMergeRequest: 'java.lang.String'], doc: 'Only rebuild open Merge Requests.')
                method(name: 'skipWorkInProgressMergeRequest', params: [skipWorkInProgressMergeRequest: 'java.lang.Boolean'], doc: 'If set, ignores work in progress pull requests.')
                method(name: 'targetBranchRegex', params: [targetBranchRegex: 'java.lang.String'], doc: 'The target branch regex allows to limit the execution of this job to certain branches.')
            }
            method(name: 'scm', params: [cronString: 'java.lang.String', body: 'Closure'], doc: 'Polls source control for changes at regular intervals.')
            if (enclosingCall('scm')) {
                method(name: 'ignorePostCommitHooks', params: [ignorePostCommitHooks: 'java.lang.Boolean'], doc: 'Ignore changes notified by SCM post-commit hooks.')
            }
            method(name: 'upstream', params: [projects: 'java.lang.String', threshold: 'java.lang.String'], doc: 'Starts a build on completion of an upstream job, i.e. adds the "Build after other projects are built" trigger.')
            method(name: 'urlTrigger', params: [crontab: 'java.lang.String', body: 'Closure'], doc: 'Adds DSL for adding and configuring the URL trigger plugin to a job.')
            if (enclosingCall('urlTrigger')) {
                method(name: 'configure', params: [configureBlock: 'Closure'], doc: 'Allows direct manipulation of the generated XML.')
                method(name: 'cron', params: [cron: 'java.lang.String'], doc: 'Sets the cron schedule.')
                method(name: 'restrictToLabel', params: [label: 'java.lang.String'], doc: 'Restricts execution to label.')
                method(name: 'url', params: [url: 'java.lang.String', body: 'Closure'], doc: 'Adds a monitored URL to the trigger.')
                if (enclosingCall('url')) {
                    method(name: 'check', params: [performCheck: 'java.lang.String'], doc: 'Enables checks to perform for URL.')
                    method(name: 'inspection', params: [performCheck: 'java.lang.String', body: 'Closure'], doc: 'Adds inspections of the returned content.')
                    if (enclosingCall('inspection')) {
                        method(name: 'path', params: [path: 'java.lang.String'], doc: 'Adds a JSON/XPATH path expression to the inspection.')
                        method(name: 'regexp', params: [exp: 'java.lang.String'], doc: 'Adds a RegExp for TEXT inspections.')
                    }
                    method(name: 'proxy', params: [active: 'java.lang.Boolean'], doc: 'Enables/Disables the use of the global proxy that is configured for Jenkins.')
                    method(name: 'status', params: [statusCode: 'java.lang.Integer'], doc: 'Define the expected status code of the response.')
                    method(name: 'timeout', params: [timeout: 'java.lang.Long'], doc: 'Defines how many seconds the trigger will wait when checking the URL.')
                }
            }
        }
        method(name: 'using', params: [templateName: 'java.lang.String'], doc: 'Creates a new job configuration, based on the job template referenced by the parameter and stores this.')
    }
}
