package com.ea.lib.jobsettings

import com.ea.lib.LibCommonNonCps

class StatusSettings extends JobSetting {
    String nonVirtualCodeBranch
    String nonVirtualCodeFolder

    void initializeProduceBuildStatusStart(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        def modifiers = ['status']
        description = 'Start job for produce build status job'
        isDisabled = LibCommonNonCps.get_setting_value(branchInfo, modifiers, 'disable_build', false)
        nonVirtualCodeBranch = branchInfo.non_virtual_code_branch ?: ''
        nonVirtualCodeFolder = branchInfo.non_virtual_code_folder ?: ''
        codeBranch = branchInfo.code_branch
        codeFolder = branchInfo.code_folder
        projectName = projectFile.name
    }

    void initializeProduceBuildStatusJob(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        description = 'Job for producing build status'
        jobLabel = branchInfo.job_label_statebuild ?: 'statebuild'
        def timeoutHours = branchInfo.timeout_hours_produce_build_status ?: 24
        timeoutMinutes = timeoutHours * 60
        buildName = '${JOB_NAME}.${BUILD_NUMBER}'
        batchScript = "tnt\\bin\\fbcli\\cli.bat x64 && ${workspaceRoot}\\TnT\\Code\\DICE\\Tools\\produceBuildStatus.bat"
    }
}
