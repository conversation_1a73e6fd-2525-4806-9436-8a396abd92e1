{#
    Command:
        copy_from_filer_to_azure
            help: Deploy content at source to destination

    Arguments:

    Required variables:

    Optional variables:
        content_type
            help: Content type to copy to Azure (e.g. 'code' for codebuild). Determines path logic.
            default: None
            type: click.Choice(SUPPORTED_CONTENT_TYPES)
        source
            default: None
            help: Skip path logic and use this path as source
        destination
            default: None
            help: Skip path logic and use this path as destination
        platform
            help: platform (used by path generation logic)
            default: None
            type: click.Choice(SUPPORTED_PLATFORMS + ['all'])
        additional_tools_to_include
            help: Additional tool(s) to pull from network share (pipeline and frosted are always pulled)
            default: None
            multiple: True
            type: str
        config
            help: Build config (used by path generation logic)
            default: None
        code_branch
            help: Branch/stream (used by path generation logic)
            default: None
        code_changelist
            help: Code changelist(used by path generation logic)
            default: None
        elipy_config_location
            help: Which elipy config to use
            default: None
        target_build_share
            help: Elipy config key to find buildshare in alternate_build_shares.
            default: None
        secret_context
            help: Elipy config secrets 'where' key for filer auth
            default: None
        force_overwrite
            help: Overwrite existing contents at destination
            default: False
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    copy_from_filer_to_azure
    {%- if content_type %}
    --content-type {{ content_type }}
    {%- endif %}
    {%- if source %}
    --source {{ source }}
    {%- endif %}
    {%- if destination %}
    --destination {{ destination }}
    {%- endif %}
    {%- if platform %}
    --platform {{ platform }}
    {%- endif %}
<<<<<<< HEAD
    {%- if additional_tools_to_include %}
    --additional-tools-to-include {{ additional_tools_to_include }}
    {%- endif %}
=======
>>>>>>> origin/job-generator-templates
    {%- if config %}
    --config {{ config }}
    {%- endif %}
    {%- if code_branch %}
    --code-branch {{ code_branch }}
    {%- endif %}
    {%- if code_changelist %}
    --code-changelist {{ code_changelist }}
    {%- endif %}
    {%- if elipy_config_location %}
    --elipy-config-location {{ elipy_config_location }}
    {%- endif %}
    {%- if target_build_share %}
    --target-build-share {{ target_build_share }}
    {%- endif %}
    {%- if secret_context %}
    --secret-context {{ secret_context }}
    {%- endif %}
    {%- if force_overwrite %}
    --force-overwrite {{ force_overwrite }}
    {%- endif %}
  displayName: elipy copy_from_filer_to_azure
