package com.ea.lib.jobs

import com.ea.lib.LibJobDsl
import com.ea.lib.jobsettings.StatusSettings

class LibStatus {
    /**
     * Adds generic job parameters for produce-build-status start jobs.
     */
    static void produce_build_status_start(def job, def project, def branchFile, def masterFile, String branchName) {
        job.with {
            StatusSettings statusSettings = new StatusSettings()
            statusSettings.initializeProduceBuildStatusStart(branchFile, masterFile, project, branchName)

            description(statusSettings.description)
            disabled(statusSettings.isDisabled)
            logRotator(7, 100)
            quietPeriod(0)
            properties {
                disableConcurrentBuilds()
                disableResume()
                pipelineTriggers {
                    triggers {
                        pollSCM {
                            scmpoll_spec('H/5 * * * 1-6\nH/5 6-23 * * 7')
                        }
                    }
                }
            }
            environmentVariables {
                env('branch_name', statusSettings.branchName)
                env('code_branch', statusSettings.codeBranch)
                env('code_folder', statusSettings.codeFolder)
                env('non_virtual_code_branch', statusSettings.nonVirtualCodeBranch)
                env('non_virtual_code_folder', statusSettings.nonVirtualCodeFolder)
                env('project_name', statusSettings.projectName)
            }
        }
    }

    /**
     * Adds generic job parameters for produce-build-status build jobs.
     */
    static void produce_build_status_job(def job, def project, def branchFile, def masterFile, String branchName) {
        job.with {
            StatusSettings statusSettings = new StatusSettings()
            statusSettings.initializeProduceBuildStatusJob(branchFile, masterFile, project, branchName)

            description(statusSettings.description)
            label(statusSettings.jobLabel)
            logRotator(7, 100)
            quietPeriod(0)
            properties {
                disableConcurrentBuilds()
                disableResume()
            }
            customWorkspace(statusSettings.workspaceRoot)
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(statusSettings.buildName)
                timeout {
                    absolute(statusSettings.timeoutMinutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, statusSettings.elipyInstallCall, project)
                batchFile(statusSettings.batchScript)
            }
        }
    }
}
