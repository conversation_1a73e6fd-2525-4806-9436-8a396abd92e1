package support

import com.lesfurets.jenkins.unit.MethodCall
import com.lesfurets.jenkins.unit.RegressionTest
import spock.lang.Specification

/**
 * Base class for declarative pipeline Spock tests.
 * All specifications testing pipelines must inherit from this class, either directly or indirectly.
 */
class DeclarativePipelineSpockTest extends Specification implements RegressionTest {

    @Delegate
    PipelineTestHelper pipelineTestHelper

    void setup() {
        callStackPath = 'test/groovy/callstacks/'
        pipelineTestHelper = new PipelineTestHelper()
        pipelineTestHelper.setUp()
    }

    /**
     * Assert that the mock was called at least once.
     * @param methodName name of the function
     * @return true if called, false otherwise
     */
    boolean assertCalled(String methodName) {
        return findCalls(methodName).size() > 0
    }

    /**
     * Assert that the mock was called exactly once
     * @param methodName name of the function
     * @return true if called exactly once, false otherwise
     */
    boolean assertCalledOnce(String methodName) {
        return assertCalledTimes(methodName, 1)
    }

    /**
     * This method is a convenient way of asserting that the last call has been made in a particular way.
     * @param methodName name of the function
     * @param args the arguments to compare against
     * @return true if the last call was called with {@code args}, false otherwise
     */
    boolean assertCalledWith(String methodName, def args) {
        return findCalls(methodName, args).last().args[0] == args
    }

    /**
     * This method is a convenient way of asserting that the last call has been made in a particular way.
     * @param methodName name of the function
     * @param args the arguments to compare against
     * @return true if the last call was called with {@code args}, false otherwise
     */
    boolean assertCalledWith(String methodName, def ... args) {
        return findCalls(methodName, args).size() > 0
    }

    /**
     * Assert that the mock was called exactly once and that call was with the specified arguments.
     * @param methodName name of the function
     * @param methodName name of the function
     * @return true if called once with {@code args}, false otherwise
     */
    boolean assertCalledOnceWith(String methodName, def args) {
        return findCalls(methodName, args).size() == 1
    }

    /**
     * Assert the mock has been called with the specified arguments.
     * @param methodName name of the function
     * @param methodName name of the function
     * @return true if the function has been called with {@code args}, false otherwise
     */
    boolean assertAnyCall(String methodName, def args) {
        return findCalls(methodName, args).size() > 0
    }

    /**
     * Assert that the mock was called {@code count} times
     * @param methodName name of the function
     * @param count the number of times the function should be called
     * @return true if called {@code count} times, false otherwise
     */
    boolean assertCalledTimes(String methodName, int count) {
        return findCalls(methodName).size() == count
    }

    /**
     * Assert the mock was never called.
     * @param methodName name of the function
     * @return true if not called, false otherwise
     */
    boolean assertNotCalled(String methodName) {
        return assertCalledTimes(methodName, 0)
    }

    private List<MethodCall> findCalls(String methodName, def args) {
        return helper.callStack.findAll { call ->
            call.methodName == methodName && call.args[0] == args
        }
    }

    private List<MethodCall> findCalls(String methodName, def ... args) {
        return helper.callStack.findAll { call ->
            call.methodName == methodName && call.args == args
        }
    }

    private List<MethodCall> findCalls(String methodName) {
        return helper.callStack.findAll { call -> call.methodName == methodName }
    }
}
