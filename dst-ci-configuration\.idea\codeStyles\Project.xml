<component name="ProjectCodeStyleConfiguration">
  <code_scheme name="Project" version="173">
    <option name="FORMATTER_TAGS_ENABLED" value="true" />
    <GroovyCodeStyleSettings>
      <option name="CLASS_COUNT_TO_USE_IMPORT_ON_DEMAND" value="999" />
      <option name="NAMES_COUNT_TO_USE_IMPORT_ON_DEMAND" value="999" />
      <option name="IMPORT_LAYOUT_TABLE">
        <value>
          <package name="" withSubpackages="true" static="true" />
          <emptyLine />
          <package name="" withSubpackages="true" static="false" />
          <emptyLine />
          <package name="javax" withSubpackages="true" static="false" />
          <package name="java" withSubpackages="true" static="false" />
          <emptyLine />
        </value>
      </option>
    </GroovyCodeStyleSettings>
    <codeStyleSettings language="Groovy">
      <option name="RIGHT_MARGIN" value="140" />
      <indentOptions>
        <option name="CONTINUATION_INDENT_SIZE" value="4" />
      </indentOptions>
    </codeStyleSettings>
  </code_scheme>
</component>
