package com.ea.project.kin.branchsettings

import com.ea.project.kin.Kingston

class Kin_dev_anticheat {
    // Settings for jobs
    static Class project = Kingston
    static Map general_settings = [
        dataset           : project.dataset,
        frostbite_licensee: project.frostbite_licensee,
        elipy_install_call: project.elipy_install_call,
        elipy_call        : project.elipy_call,
        workspace_root    : project.workspace_root,
    ]
    static Map standard_jobs_settings = [
        asset                    : 'ShippingLevels',
        denuvo_exclusion_path    : 'TnT\\Build\\DenuvoExclusionList',
        denuvo_wrapping          : true,
        extra_code_args          : [],
        enable_eac               : true, // ANTICHEAT_ENABLED=false
        frosty_reference_job     : 'kin-dev-anticheat.data.start',
        increase_version_by      : '2',
        first_patch_win64        : true,
        linux_docker_images      : false,
        poolbuild_data           : true,
        poolbuild_frosty         : true,
        poolbuild_patchdata      : true,
        server_asset             : 'ShippingLevels',
        shift_branch             : false,
        shift_every_build        : false,
        shift_reference_job      : 'kin-dev-anticheat.frosty.start',
        skip_frosty_trigger      : true,
        slack_channel_code       : [
            channels                  : ['#kin-build-notify'],
            skip_for_multiple_failures: true,
        ],
        slack_channel_data       : [
            channels                  : ['#kin-build-notify'],
            skip_for_multiple_failures: true,
        ],
        slack_channel_frosty     : [
            channels                  : ['#kin-build-notify'],
            skip_for_multiple_failures: true,
        ],
        slack_channel_patchfrosty: [
            channels                  : ['#earo-frosty-notify'],
            skip_for_multiple_failures: true,
        ],
        timeout_hours_data       : 8,
        timeout_hours_patchdata  : 11,
        timeout_hours_webexport  : 8,
        use_snowcache            : true, // May need changed to clean_local is true  (previously bug existed)
        use_super_bundles        : true,
        trigger_string_code      : 'TZ=Europe/Stockholm\nH 3 * * 1-6\nH 6 * * 7',
        trigger_type_code        : 'scm',
        trigger_type_data        : 'none',
        enable_perfmarkers_retail: true,
    ]
    static Map preflight_settings = [
        p4_code_server             : 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
        p4_code_creds              : 'dice-p4buildedge03-fb',
    ]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'win64game', configs: ['final', 'retail']],
        [name: 'win64server', configs: ['final']],
        [name: 'linux64server', configs: ['final']],
        [name: 'tool', configs: ['release']],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = [
        [name: '.data.start', args: []],
    ]
    static List data_matrix = [
        'win64',
    ]
    static List data_downstream_matrix = [
        [name: '.frosty.start', args: []],
    ]
    static List patchdata_matrix = [
    ]
    static List frosty_matrix = [
        [name: 'win64', variants: [[format: 'digital', config: 'final', region: 'ww', args: ''],
                                   [format: 'digital', config: 'retail', region: 'ww', args: '']]],
        [name: 'server', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww', args: '']]],
    ]
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = []
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
