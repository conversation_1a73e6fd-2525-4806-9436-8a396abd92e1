"""
data_upgrade.py
"""
import os
import click
from dice_elipy_scripts.utils.code_utils import run_gensln
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.integration_utils import submit_integration
from dice_elipy_scripts.utils.licensee_helper import set_licensee
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import code, core, data, filer, frostbite_core, p4, running_processes
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command("data_upgrade", short_help="Upgrade data locally on a branch.")
@click.option("--code-branch", required=True, help="Perforce branch/stream name.")
@click.option("--code-changelist", required=True, help="Perforce code changelist.")
@click.option("--data-changelist", required=True, help="Perforce data changelist.")
@click.option("--submit/--no-submit", default=True)
@click.option("--p4-port", required=True, help="Perforce port/server.")
@click.option("--p4-client", required=True, help="Perforce client/workspace.")
@click.option("--p4-user", default=None, help="Perforce user name.")
@click.option("--clean", default="false", help="Delete TnT/Local if --clean true is passed.")
@click.option(
    "--data-directory",
    default=None,
    help="Which data directory to use for fetching licensee settings.",
)
@click.option(
    "--licensee", multiple=True, default=None, help="What licensee should gensln be ran against."
)
@click.option(
    "--password",
    default=None,
    help="User credentials to authenticate to package server.",
)
@click.option("--email", default=None, help="User email to authenticate to package server.")
@click.option(
    "--domain-user",
    default=None,
    help="The user to authenticate to package server as DOMAIN\\user.",
)
@click.option("--submit-folder", default=None, help="Restrict the submit to one folder.")
@click.option("--batch-file", default="UpgradeLocal.bat", help="Upgrade batch file to run.")
@click.option(
    "--p4-ignore",
    default=".p4ignore",
    help=".p4ignore file to set, if not will set the default of .p4ignore",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
# pylint: disable=too-many-locals, invalid-name, too-many-statements, protected-access
def cli(
    _,
    code_branch,
    code_changelist,
    data_changelist,
    submit,
    p4_port,
    p4_client,
    p4_user,
    clean,
    data_directory,
    password,
    email,
    domain_user,
    licensee,
    submit_folder,
    batch_file,
    p4_ignore,
):
    """
    Upgrade data locally on a branch/stream, using UpgradeLocal.bat.
    """
    # Adding sentry tags.
    add_sentry_tags(__file__)

    running_processes.kill()

    if data_directory is not None:
        data.DataUtils.set_datadir(data_directory)

    perforce = p4.P4Utils(port=p4_port, client=p4_client, user=p4_user)
    perforce.revert()

    _filer = filer.FilerUtils()

    framework_args = set_licensee(list(licensee), [])
    builder = code.CodeUtils(
        "tool",
        "release",
        p4_port=p4_port,
        p4_user=p4_user,
        p4_client=p4_client,
        overwrite_p4config=False,
    )

    if clean.lower() == "true":
        builder.clean_local(close_handles=True)

    # Run to pull down the required packages.
    run_gensln(
        password=password,
        user=email,
        domain_user=domain_user,
        framework_args=framework_args,
        builder=builder,
    )

    # Data upgrade requires the binaries for both frosted and pipeline.
    mirror = True
    for platform in ["pipeline", "frosted"]:
        _filer.fetch_code(code_branch, code_changelist, platform, "release", mirror=mirror)
        mirror = False

    core.ensure_p4_config(
        root_dir=frostbite_core.get_game_data_dir(),
        port=p4_port,
        client=p4_client,
        user=p4_user,
        ignore=p4_ignore,
    )

    os.environ["P4CONFIG"] = ".p4config"

    # Sync data again after code compile and before FDU
    perforce.sync(path=frostbite_core.get_game_data_dir() + "/...", to_revision=data_changelist)

    try:
        try:
            upgrade_script = os.path.join(
                frostbite_core.get_tnt_root(), "Code", "DICE", "UpgradeScripts", batch_file
            )
            core.run([upgrade_script], print_std_out=True)
        except Exception:
            perforce.clean(folder=frostbite_core.get_game_data_dir() + "/...")
            raise

        # Submit the result to Perforce.
        fdu_message = f"Upgraded Data at CL#{data_changelist} with Code at CL#{code_changelist}."
        fdu_message += "\n#branchguardian_bypass"
        submit_integration(
            p4_object=perforce,
            submit_message=fdu_message,
            submit=submit,
            data_upgrade=True,
            submit_folder=submit_folder,
        )
    finally:
        perforce.revert(quiet=False)
