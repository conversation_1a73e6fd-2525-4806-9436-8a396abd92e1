package com.ea.lib.jobsettings

import com.ea.lib.LibCommonNonCps

class MaintenanceSettings extends JobSetting {

    String allMachineLabel
    Map parameters
    String waitHours
    String forceWaitHours
    Boolean restartNodes
    <PERSON>olean restartController
    <PERSON><PERSON><PERSON> hasCronTrigger
    String nodeNameDefaultValue
    Map p4Data
    Boolean vaultWin64Trial
    Boolean verifyPostVault
    Map fbLoginDetails
    String downstreamTrigger
    String fbLoginDetailsCall
    String elipyCmd2

    void initializeAvalancheMaintenanceStart(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.maintenance_branch as Map)
        def modifiers = ['maintenance']
        isDisabled = LibCommonNonCps.get_setting_value(branchInfo, modifiers, 'disable_build', false)
        description = 'Schedule Avalanche maintenance for all nodes on this master.'
        projectName = projectFile.name
    }

    void initializeAvalancheMaintenanceJob(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.maintenance_branch as Map)
        description = 'Performs Avalanche maintenance on the build node.'
        buildName = '${JOB_NAME}.${NODE_NAME}'
        elipyCmd = "${this.elipyCall} avalanche_maintenance"
    }

    void initializeAvalancheMaintenanceAzureJob(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.maintenance_branch as Map)
        workspaceRoot = this.azureWorkspaceRoot
        description = 'Performs Avalanche maintenance on the build node.'
        buildName = '${JOB_NAME}.${NODE_NAME}'
        elipyInstallCall = this.azureElipyInstallCall
        elipyCmd = "${this.azureElipyCall} avalanche_maintenance"
    }

    void initializeP4CleanCodestreamJob(projectFile, def branchInfo, String p4Port) {
        def modifiers = ['p4_clean_codestream']
        workspaceRoot = LibCommonNonCps.get_setting_value(branchInfo, modifiers, 'workspace_root', '', projectFile)
        elipyInstallCall = LibCommonNonCps.get_setting_value(branchInfo, modifiers, 'elipy_install_call', '', projectFile)
        elipyCall = LibCommonNonCps.get_setting_value(branchInfo, modifiers, 'elipy_call', '', projectFile)
        description = "Performs p4 clean on the build node codestream on ${p4Port}."
        buildName = '${JOB_NAME}.${NODE_NAME}'
        elipyCmd = "${this.elipyCall} p4_clean code ${p4Port} ${projectFile.p4_code_client_env} ${projectFile.p4_user_single_slash}"
    }

    void initializeP4CleanDatastreamJob(projectFile, def branchInfo, String p4Port, String dataset) {
        def modifiers = ['p4_clean_datastream']
        workspaceRoot = LibCommonNonCps.get_setting_value(branchInfo, modifiers, 'workspace_root', '', projectFile)
        elipyInstallCall = LibCommonNonCps.get_setting_value(branchInfo, modifiers, 'elipy_install_call', '', projectFile)
        elipyCall = LibCommonNonCps.get_setting_value(branchInfo, modifiers, 'elipy_call', '', projectFile)
        description = "Performs p4 clean on the build node datastream on ${p4Port} using dataset ${dataset}."
        buildName = '${JOB_NAME}.${NODE_NAME}'
        elipyCmd = "${this.elipyCall} p4_clean data ${p4Port} ${projectFile.p4_data_client_env} ${projectFile.p4_user_single_slash} ${dataset}"
    }

    void initializeP4DeleteWorkspace(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.maintenance_branch as Map)
        String codeWorkspace = 'jenkins-%node%-codestream'
        String dataWorkspace = "jenkins-%node%-${projectFile.dataset}stream"
        description = 'Remove workspace from perforce server.'
        jobLabel = branchInfo.p4_delete_workspace_label ?: 'statebuild'
        buildName = '${JOB_NAME}.${BUILD_NUMBER}'
        elipyCmd = "${this.elipyCall} p4_delete_workspace" +
            " --codeport ${projectFile.p4_code_server}" +
            " --codeclient ${projectFile.p4_code_client_env}" +
            " --dataport ${projectFile.p4_data_server}" +
            " --dataclient ${projectFile.p4_data_client_env}" +
            " --user ${projectFile.p4_user_single_slash}" +
            " --codeworkspace ${codeWorkspace}" +
            " --dataworkspace ${dataWorkspace}"
    }

    void initializeAvalancheCliNukeStart(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.maintenance_branch as Map)
        description = 'running avalanchecli nuke on build machines, could select more than one machine at the same time.'
        buildName = '${JOB_NAME}.${machine}'
        elipyCmd = "${this.elipyCall} avalanchecli_nuke --datadir ${branchInfo.dataset}"
    }

    void initializeAvalancheCliNukeAll(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.maintenance_branch as Map)
        description = 'running avalanchecli nuke on build machines, could select more than one machine at the same time.'
        cronTrigger = branchInfo.scheduled_nuke ?: '59 23 31 12 *' // Runs Sat. 7 A.M for autotest cluster, others do not run by default
        allMachineLabel = branchInfo.nuke_label ?: 'none'
        buildName = '${JOB_NAME}.${machine}'
        elipyCmd = "${this.elipyCall} avalanchecli_nuke --datadir ${branchInfo.dataset}"
    }

    void initializeAvalancheCliNukeJob(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.maintenance_branch as Map)
        description = 'Performs an avalanchecli nuke -y.'
        buildName = '${JOB_NAME}.${NODE_NAME}'
        elipyCmd = "${this.elipyCall} avalanchecli_nuke --datadir ${branchInfo.dataset}"
    }

    void initializeAvalancheCliDropAllDbsStart(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.maintenance_branch as Map)
        description = 'Drops all Avalanche databases on build machines, it is possible to select more than one machine at a time.'
        allMachineLabel = branchInfo.dropdb_label ?: 'statebuild'
        // Runs Mon-Sat 4 A.M for preflight cluster, others do not run by default
        cronTrigger = branchInfo.scheduled_dropdb ?: '59 23 31 12 *'
        buildName = '${JOB_NAME}.${machine}'
        elipyCmd = "${this.elipyCall} avalanchecli_drop_all_dbs"
    }

    void initializeAvalancheCliDropAllDbsJob(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.maintenance_branch as Map)
        description = 'Drop all avalanche dbs on localhost'
        buildName = '${JOB_NAME}.${NODE_NAME}'
        elipyCmd = "${this.elipyCall} avalanchecli_drop_all_dbs"
    }

    void initializeCleanAgentFolder(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.maintenance_branch as Map)
        description = 'Run a cleaning script against the selected agents'
        buildName = '${JOB_NAME}.${machine}'
        parameters = [
            delete_packages      : [arg: 'delete-packages', flag: false, description: 'Delete d:\\packages'],
            delete_packagesdev   : [arg: 'delete-packagesdev', flag: false, description: 'Delete d:\\packagesdev'],
            delete_pip_cache     : [arg: 'delete-pip-cache', flag: false, description: 'Delete d:\\.pip-cache'],
            delete_logs          : [arg: 'delete-logs', flag: false, description: 'Delete d:\\dev\\logs'],
            delete_tnt_local     : [arg: 'delete-tnt-local', flag: false, description: 'Delete d:\\dev\\tnt\\local'],
            delete_local_packages: [arg: 'delete-localpackages', flag: false, description: 'Delete d:\\dev\\tnt\\localpackages'],
            delete_temp          : [arg: 'delete-temp', flag: false, description: 'Delete %TEMP%'],
            delete_data_state    : [arg: 'delete-data-state', flag: false, description: 'Delete %GAME_DATA_DIR%\\.state'],
        ]
        elipyCmd = [
            "${this.elipyCall} clean_agent",
            *parameters.collect { /--$it.value.arg %$it.key%/ },
            "--data-dir ${branchInfo.dataset}",
        ].join(' ')
    }

    void initializeJenkinsShutdownJob(def branchInfo, boolean hasCronTrigger) {
        description = 'Shutdown the Jenkins controller when no jobs are running --> the supervisor will restart a fresh container.'
        cronTrigger = branchInfo.master_restart_timer ?: '0 0 * * 7'
        waitHours = branchInfo.wait_doquite_hours ?: 18
        forceWaitHours = branchInfo.wait_forcekill_hours ?: 6
        restartNodes = branchInfo.restart_nodes != null ? branchInfo.restart_nodes : true
        restartController = branchInfo.restart_controller != null ? branchInfo.restart_controller : true
        this.hasCronTrigger = hasCronTrigger
    }

    void initializeSyncStart(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        description = 'Triggers a sync on all prepare machines'
        cronTrigger = 'H/30 * * * 1-6\nH/30 6-23 * * 7'
        jobLabel = "prepare-${this.branchName}"
        projectName = projectFile.name
    }

    void initializeSyncJob(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        description = "Syncs a machine to ${branchInfo.branch_name} code and data."
        nodeNameDefaultValue = "prepare-${this.branchName}"
        buildName = '${JOB_NAME}.${NODE_NAME}.${P4_CHANGELIST}'
        batchScript = "echo 'Done'"
    }

    void initializeCodeWarmJob(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.maintenance_branch as Map)
        p4Data = [
            codeCreds :
                LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_code_creds', '', projectFile),
            codeRoot  :
                LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_code_root', '', projectFile),
            codeClient:
                LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_code_client', '', projectFile),
            browserUrl: projectFile.p4_browser_url,
        ]
        extraArgs = branchInfo.use_snowcache ? ' --use-snowcache' : ''
        def override_snowcache = branchInfo.override_snowcache != null ? branchInfo.override_snowcache : ''
        if (override_snowcache != '') {
            extraArgs += ' --snowcache-mode-override ' + override_snowcache
        }
        description = 'Code warms a machine.'
        codeFolder = branchInfo.code_folder
        codeBranch = branchInfo.code_branch
        timeoutMinutes = 1440
        buildName = '${JOB_NAME}.${NODE_NAME}.${code_changelist}'
        elipyCmd = "${this.elipyCall} codebuild %platform% %config%" +
            ' --code-branch %branch% --code-changelist %code_changelist% --clean %clean_local%' +
            " --p4-port %P4_PORT% --p4-client ${projectFile.p4_code_client_env} --p4-user %P4_USER% --dry-run ${extraArgs}"
    }

    void initializeDataWarmJob(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.maintenance_branch as Map)
        p4Data = [
            codeCreds :
                LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_code_creds', '', projectFile),
            codeRoot  :
                LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_code_root', '', projectFile),
            codeClient:
                LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_code_client', '', projectFile),
            dataCreds :
                LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_data_creds', '', projectFile),
            dataRoot  :
                LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_data_root', '', projectFile),
            dataClient:
                LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_data_client', '', projectFile),
            browserUrl: projectFile.p4_browser_url,
        ]
        codeFolder = branchInfo.code_folder
        codeBranch = branchInfo.code_branch
        dataFolder = branchInfo.data_folder
        dataBranch = branchInfo.data_branch
        timeoutMinutes = 1440
        description = 'Data warms a machine.'
        buildName = '${JOB_NAME}.${NODE_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}'
        elipyCmd = "${this.elipyCall} databuild %dataset% %platform% %asset%" +
            ' --code-branch %code_branch% --code-changelist %code_changelist%' +
            ' --data-branch %data_branch% --data-changelist %data_changelist%'
    }

    void initializeVaultStart(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.maintenance_branch as Map)
        vaultWin64Trial = LibCommonNonCps.get_setting_value([:], [], 'vault_win64_trial', true, projectFile)
        verifyPostVault = LibCommonNonCps.get_setting_value([:], [], 'verify_post_vault', false, projectFile)
        description = 'Triggers vaulting for build and symbols.'
        projectName = projectFile.name
    }

    void initializeVaultJob(def branchFile, def masterFile, def projectFile, String branchName, String jobType) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.maintenance_branch as Map)
        def extraArgsList = branchInfo.extra_vault_args ?: []
        extraArgs = extraArgsList.join(' ')
        jobLabel = branchInfo.job_label_vault ?: 'statebuild'
        fbLoginDetails = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_fb_settings', [:], projectFile)
        description = 'Vaults ' + jobType + '.'
        buildName = '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}.${ENV, var="platform"}'
        fbLoginDetailsCall = "echo %fb_p4_passwd%|p4 -p ${fbLoginDetails?.p4_port} -u %fb_p4_user% login & exit 0"
        downstreamTrigger = "store.baseline.${projectFile.short_name}.build"
        elipyCmd = "${this.elipyCall} vault --vault-type symbols --platform %platform%" +
            ' --code-branch %code_branch% --code-changelist %code_changelist%' +
            ' --data-branch %data_branch% --data-changelist %data_changelist%' +
            ' --build-url %BUILD_URL% --build-id %BUILD_NUMBER% --version "%version%"' +
            ' %vault_win64_trial% --md5-validation --build-location "%build_location%"' +
            ' --vault-verification-location "%vault_verification_location%"' +
            " ${extraArgs}"

        elipyCmd2 = "${this.elipyCall} vault --vault-type ${jobType} --platform %platform%" +
            ' --code-branch %code_branch% --code-changelist %code_changelist%' +
            ' --data-branch %data_branch% --data-changelist %data_changelist%' +
            ' --build-url %BUILD_URL% --build-id %BUILD_NUMBER% --version "%version%"' +
            ' --md5-validation %expression_debug_data% %verify_post_vault% --build-location "%build_location%"' +
            ' --vault-verification-location "%vault_verification_location%"' +
            " ${extraArgs}"
    }

    void initializeBaselineJob(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.maintenance_branch as Map)
        this.jobLabel = branchInfo.job_label_vault ?: 'statebuild'
        description = 'Stores the baseline.'
        buildName = '${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}.${ENV, var="platform"}'
        elipyCmd = "${this.elipyCall} backup_baseline --platform %platform%" +
            ' --baseline-code-branch %code_branch% --baseline-code-changelist %code_changelist%' +
            ' --baseline-data-branch %data_branch% --baseline-data-changelist %data_changelist%' +
            ' --build-location "%build_location%" --additional-baseline-locations "%additional_baseline_locations%"'
    }
}
