"""
test_backup_baseline.py

Unit testing for backup_baseline
"""
import unittest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from unittest.mock import call, MagicMock, patch
from dice_elipy_scripts.backup_baseline import cli
from dice_elipy_scripts.backup_baseline import copy_baseline_build
from elipy2.exceptions import ELIPYException


class TestBackupBaseline(unittest.TestCase):
    DEFAULT_ARGS = [
        "--platform",
        "ps5",
        "--baseline-data-branch",
        "data_branch",
        "--baseline-data-changelist",
        "4381933",
        "--baseline-code-branch",
        "code_branch",
        "--baseline-code-changelist",
        "11154536",
    ]

    def setUp(self):
        self.platform = "ps5"
        self.source = "c:\\drive_that_doesnt_exist"
        self.target = "c:\\target"

    @patch("dice_elipy_scripts.backup_baseline.file_check")
    def test_copy_baseline_build_raises_ELIPYException(self, mock_file_check):
        # Given failing file_check invocation
        mock_file_check.side_effect = ELIPYException
        # When copy_baseline_build is called
        with self.assertRaises(ELIPYException):
            # Then an ELIPYException is raised
            copy_baseline_build(self.source, self.target)

    def test_copy_baseline_build_calls_robocopy(self):
        # Setup mocks
        with patch("dice_elipy_scripts.backup_baseline.file_check") as mock_file_check:
            with patch("dice_elipy_scripts.backup_baseline.core.robocopy") as mock_robocopy:
                # Given non failing file_check
                mock_file_check.side_effect = None
                # When copy_baseline_build is called
                copy_baseline_build(self.source, self.target)
                # Then robocopy is called with the correct source and target
                mock_robocopy.assert_called_once_with(self.source, self.target)

    @patch("dice_elipy_scripts.backup_baseline.build_metadata_utils", MagicMock())
    @patch("dice_elipy_scripts.backup_baseline.copy_baseline_build", MagicMock())
    @patch("dice_elipy_scripts.backup_baseline.core.robocopy", MagicMock())
    @patch("dice_elipy_scripts.backup_baseline.file_check", MagicMock())
    @patch("elipy2.filer_paths.get_frosty_base_build_path")
    @patch("elipy2.filer_paths.get_baseline_build_path")
    def test_build_location_passed_to_get_frosty_base_build_path(
        self,
        mock_get_baseline_build_path,
        mock_get_frosty_path,
    ):
        """
        Test that build_location is correctly passed to get_frosty_base_build_path.
        """
        # Given the backup_baseline cli is executed with build_location parameter
        mock_get_baseline_build_path.return_value = "baseline\\path"
        test_args = self.DEFAULT_ARGS + ["--build-location", "event"]
        # When cli is invoked
        runner = CliRunner()
        result = runner.invoke(cli, test_args)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        # Then get_frosty_base_build_path is called with location=event
        mock_get_frosty_path.assert_called_once_with(
            data_changelist="4381933",
            code_changelist="11154536",
            data_branch="data_branch",
            code_branch="code_branch",
            platform="ps5",
            location="event",
        )
        mock_get_baseline_build_path.assert_called_once_with(
            data_changelist="4381933",
            code_changelist="11154536",
            data_branch="data_branch",
            code_branch="code_branch",
            location="event",
        )

    @patch("dice_elipy_scripts.backup_baseline.build_metadata_utils", MagicMock())
    @patch("dice_elipy_scripts.backup_baseline.copy_baseline_build", MagicMock())
    @patch("dice_elipy_scripts.backup_baseline.core.robocopy", MagicMock())
    @patch("dice_elipy_scripts.backup_baseline.file_check", MagicMock())
    @patch("elipy2.filer_paths.get_frosty_base_build_path")
    @patch("elipy2.filer_paths.get_baseline_build_path")
    def test_build_location_passed_to_get_frosty_base_build_path_two_dest_locations(
        self,
        mock_get_baseline_build_path,
        mock_get_frosty_path,
    ):
        """
        Test that build_location is correctly passed to get_frosty_base_build_path.
        """
        # Given the backup_baseline cli is executed with build_location parameter
        mock_get_baseline_build_path.return_value = "baseline\\path"
        test_args = self.DEFAULT_ARGS + [
            "--build-location",
            "event",
            "--additional-baseline-locations",
            "other_location",
        ]
        # When cli is invoked
        runner = CliRunner()
        result = runner.invoke(cli, test_args)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        # Then get_frosty_base_build_path is called with location=event
        mock_get_frosty_path.assert_called_once_with(
            data_changelist="4381933",
            code_changelist="11154536",
            data_branch="data_branch",
            code_branch="code_branch",
            platform="ps5",
            location="event",
        )
        mock_get_baseline_build_path.assert_has_calls(
            [
                call(
                    data_changelist="4381933",
                    code_changelist="11154536",
                    data_branch="data_branch",
                    code_branch="code_branch",
                    location="event",
                ),
                call(
                    data_changelist="4381933",
                    code_changelist="11154536",
                    data_branch="data_branch",
                    code_branch="code_branch",
                    location="other_location",
                ),
            ]
        )

    @patch("dice_elipy_scripts.backup_baseline.build_metadata_utils", MagicMock())
    @patch("dice_elipy_scripts.backup_baseline.copy_baseline_build", MagicMock())
    @patch("dice_elipy_scripts.backup_baseline.core.robocopy", MagicMock())
    @patch("dice_elipy_scripts.backup_baseline.file_check", MagicMock())
    @patch("elipy2.filer_paths.get_frosty_base_build_path")
    @patch("elipy2.filer_paths.get_baseline_build_path")
    def test_build_location_passed_to_get_frosty_base_build_path_three_dest_locations(
        self,
        mock_get_baseline_build_path,
        mock_get_frosty_path,
    ):
        """
        Test that build_location is correctly passed to get_frosty_base_build_path.
        """
        # Given the backup_baseline cli is executed with build_location parameter
        mock_get_baseline_build_path.return_value = "baseline\\path"
        test_args = self.DEFAULT_ARGS + [
            "--build-location",
            "event",
            "--additional-baseline-locations",
            "other_location,third_location",
        ]
        # When cli is invoked
        runner = CliRunner()
        result = runner.invoke(cli, test_args)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        # Then get_frosty_base_build_path is called with location=event
        mock_get_frosty_path.assert_called_once_with(
            data_changelist="4381933",
            code_changelist="11154536",
            data_branch="data_branch",
            code_branch="code_branch",
            platform="ps5",
            location="event",
        )
        mock_get_baseline_build_path.assert_has_calls(
            [
                call(
                    data_changelist="4381933",
                    code_changelist="11154536",
                    data_branch="data_branch",
                    code_branch="code_branch",
                    location="event",
                ),
                call(
                    data_changelist="4381933",
                    code_changelist="11154536",
                    data_branch="data_branch",
                    code_branch="code_branch",
                    location="other_location",
                ),
                call(
                    data_changelist="4381933",
                    code_changelist="11154536",
                    data_branch="data_branch",
                    code_branch="code_branch",
                    location="third_location",
                ),
            ]
        )

    @patch("dice_elipy_scripts.utils.sentry_utils.add_sentry_tags", MagicMock())
    @patch("dice_elipy_scripts.backup_baseline.build_metadata_utils")
    @patch("dice_elipy_scripts.backup_baseline.copy_baseline_build", MagicMock())
    @patch("dice_elipy_scripts.backup_baseline.core.robocopy", MagicMock())
    @patch("dice_elipy_scripts.backup_baseline.file_check", MagicMock())
    @patch("elipy2.filer_paths.get_frosty_base_build_path", MagicMock())
    @patch("elipy2.filer_paths.get_baseline_build_path")
    def test_metadata_manager_created_on_successful_copy_baseline_build(
        self,
        mock_get_baseline_build_path,
        mock_setup_metadata_manager,
    ):
        # Given non failing os.path.join and copy_baseline_build
        mock_get_baseline_build_path.return_value = "baseline\\path"
        test_args = self.DEFAULT_ARGS + ["--build-location", "Guildford"]
        # When the backup_baseline cli is executed
        runner = CliRunner()
        result = runner.invoke(cli, test_args)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        # A setup_metadata_manager is instantiated without args
        mock_setup_metadata_manager.setup_metadata_manager.assert_called_once_with()
        mock_get_baseline_build_path.assert_called_once_with(
            data_changelist="4381933",
            code_changelist="11154536",
            data_branch="data_branch",
            code_branch="code_branch",
            location="Guildford",
        )

    @patch("os.path.join", MagicMock(side_effect=lambda *args: "\\".join(args)))
    @patch("dice_elipy_scripts.utils.sentry_utils.add_sentry_tags", MagicMock())
    @patch("dice_elipy_scripts.backup_baseline.build_metadata_utils")
    @patch("dice_elipy_scripts.backup_baseline.copy_baseline_build", MagicMock())
    @patch("dice_elipy_scripts.backup_baseline.core.robocopy", MagicMock())
    @patch("dice_elipy_scripts.backup_baseline.file_check", MagicMock())
    @patch("elipy2.filer_paths.get_frosty_base_build_path", MagicMock())
    @patch("elipy2.filer_paths.get_baseline_build_path")
    def test_metadata_manager_calls_two_dest_locations(
        self,
        mock_get_baseline_build_path,
        mock_setup_metadata_manager,
    ):
        # Given non failing os.path.join and copy_baseline_build
        mock_get_baseline_build_path.side_effect = ["baseline\\path", "alt\\baseline\\path"]
        test_args = self.DEFAULT_ARGS + [
            "--build-location",
            "Guildford",
            "--additional-baseline-locations",
            "other_location",
        ]
        # When the backup_baseline cli is executed
        runner = CliRunner()
        result = runner.invoke(cli, test_args)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        # A setup_metadata_manager is instantiated without args
        mock_setup_metadata_manager.setup_metadata_manager.return_value.register_baseline_build.assert_has_calls(
            [
                call(
                    "baseline\\path\\ps5",
                    platform="ps5",
                    data_changelist="4381933",
                    data_branch="data_branch",
                    code_changelist="11154536",
                    code_branch="code_branch",
                ),
                call(
                    "alt\\baseline\\path\\ps5",
                    platform="ps5",
                    data_changelist="4381933",
                    data_branch="data_branch",
                    code_changelist="11154536",
                    code_branch="code_branch",
                ),
            ]
        )

    @patch("os.path.join", MagicMock(side_effect=lambda *args: "\\".join(args)))
    @patch("dice_elipy_scripts.utils.sentry_utils.add_sentry_tags", MagicMock())
    @patch("dice_elipy_scripts.backup_baseline.build_metadata_utils")
    @patch("dice_elipy_scripts.backup_baseline.copy_baseline_build", MagicMock())
    @patch("dice_elipy_scripts.backup_baseline.core.robocopy", MagicMock())
    @patch("dice_elipy_scripts.backup_baseline.file_check", MagicMock())
    @patch("elipy2.filer_paths.get_frosty_base_build_path", MagicMock())
    @patch("elipy2.filer_paths.get_baseline_build_path")
    def test_metadata_manager_calls_three_dest_locations(
        self,
        mock_get_baseline_build_path,
        mock_setup_metadata_manager,
    ):
        # Given non failing os.path.join and copy_baseline_build
        mock_get_baseline_build_path.side_effect = [
            "baseline\\path",
            "alt\\baseline\\path",
            "third\\baseline\\path",
        ]
        test_args = self.DEFAULT_ARGS + [
            "--build-location",
            "Guildford",
            "--additional-baseline-locations",
            "other_location,third_location",
        ]
        # When the backup_baseline cli is executed
        runner = CliRunner()
        result = runner.invoke(cli, test_args)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        # A setup_metadata_manager is instantiated without args
        mock_setup_metadata_manager.setup_metadata_manager.return_value.register_baseline_build.assert_has_calls(
            [
                call(
                    "baseline\\path\\ps5",
                    platform="ps5",
                    data_changelist="4381933",
                    data_branch="data_branch",
                    code_changelist="11154536",
                    code_branch="code_branch",
                ),
                call(
                    "alt\\baseline\\path\\ps5",
                    platform="ps5",
                    data_changelist="4381933",
                    data_branch="data_branch",
                    code_changelist="11154536",
                    code_branch="code_branch",
                ),
                call(
                    "third\\baseline\\path\\ps5",
                    platform="ps5",
                    data_changelist="4381933",
                    data_branch="data_branch",
                    code_changelist="11154536",
                    code_branch="code_branch",
                ),
            ]
        )

    @patch("dice_elipy_scripts.utils.sentry_utils.add_sentry_tags", MagicMock())
    @patch("dice_elipy_scripts.backup_baseline.build_metadata_utils", MagicMock())
    @patch("dice_elipy_scripts.backup_baseline.copy_baseline_build", MagicMock())
    @patch("dice_elipy_scripts.backup_baseline.core.robocopy", MagicMock())
    @patch("dice_elipy_scripts.backup_baseline.file_check", MagicMock())
    @patch("elipy2.filer_paths.get_frosty_base_build_path")
    @patch("elipy2.filer_paths.get_baseline_build_path")
    def test_no_build_location_passed_to_get_frosty_base_build_path(
        self,
        mock_get_baseline_build_path,
        mock_get_frosty_path,
    ):
        """
        Test that None is passed to get_frosty_base_build_path when no build_location is provided.
        """
        # Given the backup_baseline cli is executed without build_location parameter
        mock_get_baseline_build_path.return_value = "baseline\\path"
        test_args = self.DEFAULT_ARGS
        # When cli is invoked
        runner = CliRunner()
        result = runner.invoke(cli, test_args)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        # Then get_frosty_base_build_path is called with location=None
        mock_get_frosty_path.assert_called_once_with(
            data_changelist="4381933",
            code_changelist="11154536",
            data_branch="data_branch",
            code_branch="code_branch",
            platform="ps5",
            location=None,
        )
        mock_get_baseline_build_path.assert_called_once_with(
            data_changelist="4381933",
            code_changelist="11154536",
            data_branch="data_branch",
            code_branch="code_branch",
            location=None,
        )
