{#
    Command:
        integrate
            short_help: Performs a Perforce integration.

    Arguments:
        port
            required: True
        client
            required: True
        mapping
            required: True
        changelist
            required: True

    Required variables:

    Optional variables:
        reverse/__no_reverse
            default: False
        submit/__no_submit
            default: True
        stream/__no_stream
            default: False
        stream_merge/__no_stream_merge
            default: True
        safe_resolve/__no_safe_resolve
            default: True
        shelve_cl/__no_shelve_cl
            default: False
            help: Shelve changelist for failed integration.
        accept_theirs/__no_accept_theirs
            default: False
        user
            default: None
            help: Perforce user name.
        exclude_path
            default: []
            multiple: True
            help: Don't integrate path. The path shall be relative to the workspace.
        exclude_accept_yours
            is_flag: True
            help: Exclude paths by accepting yours.
        submit_message
            default: ''
            help: Message to include in submit message.
        reverse_mapping
            default: None
            help: Branch from which we are integrating.
        source_file_path
            default: None
            help: Source of files for interchanges and file integrations.
        data_dir
            default: kindata
            help: Specify which data directory to use (relative to GAME_ROOT).
        platform
            default: win64
            help: Platform to cook
        assets
            default: Game/Levels/MP/MP_Orbital
            help: Asset to cook.
        cook
            is_flag: True
            help: Cook with last known good before submit.
        merge_verification
            is_flag: True
            help: Verification with gensln and buildsln before submit.
        licensee
            multiple: True
            default: None
            help: Licensee to use
        email
            default: None
            help: User email to authenticate to package server
        domain_user
            default: None
            help: The user to authenticate to package server as DOMAIN\user
        password
            default: None
            help: User credentials to authenticate to package server
        cl_by_cl
            is_flag: True
            help: Integrate one cl at a time.
        tags
            help: Description tag to filter cl-by-cl integrations.
            multiple: True
        use_file_path
            is_flag: True
            help: Specify path for defining integration scope.
        ignore_source_history
            is_flag: True
            help: Ignore source file history (sets the Perforce integrate flag -Di).
        cherrypick
            is_flag: True
            help: Cherrypick one specific changelist.
        remote_p4server
            default: None
            help: Remote p4 to fetch correct integrating CL.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    integrate
    port {{ port }}
    client {{ client }}
    mapping {{ mapping }}
    changelist {{ changelist }}
    {%- if reverse/__no_reverse %}
    --reverse/--no-reverse {{ reverse/__no_reverse }}
    {%- endif %}
    {%- if submit/__no_submit %}
    --submit/--no-submit {{ submit/__no_submit }}
    {%- endif %}
    {%- if stream/__no_stream %}
    --stream/--no-stream {{ stream/__no_stream }}
    {%- endif %}
    {%- if stream_merge/__no_stream_merge %}
    --stream-merge/--no-stream-merge {{ stream_merge/__no_stream_merge }}
    {%- endif %}
    {%- if safe_resolve/__no_safe_resolve %}
    --safe-resolve/--no-safe-resolve {{ safe_resolve/__no_safe_resolve }}
    {%- endif %}
    {%- if shelve_cl/__no_shelve_cl %}
    --shelve-cl/--no-shelve-cl {{ shelve_cl/__no_shelve_cl }}
    {%- endif %}
    {%- if accept_theirs/__no_accept_theirs %}
    --accept-theirs/--no-accept-theirs {{ accept_theirs/__no_accept_theirs }}
    {%- endif %}
    {%- if user %}
    --user {{ user }}
    {%- endif %}
    {%- if exclude_path %}
    --exclude-path {{ exclude_path }}
    {%- endif %}
    {%- if exclude_accept_yours %}
    --exclude-accept-yours {{ exclude_accept_yours }}
    {%- endif %}
    {%- if submit_message %}
    --submit-message {{ submit_message }}
    {%- endif %}
    {%- if reverse_mapping %}
    --reverse-mapping {{ reverse_mapping }}
    {%- endif %}
    {%- if source_file_path %}
    --source-file-path {{ source_file_path }}
    {%- endif %}
    {%- if data_dir %}
    --data-dir {{ data_dir }}
    {%- endif %}
    {%- if platform %}
    --platform {{ platform }}
    {%- endif %}
    {%- if assets %}
    --assets {{ assets }}
    {%- endif %}
    {%- if cook %}
    --cook {{ cook }}
    {%- endif %}
    {%- if merge_verification %}
    --merge-verification {{ merge_verification }}
    {%- endif %}
    {%- if licensee %}
    --licensee {{ licensee }}
    {%- endif %}
    {%- if email %}
    --email {{ email }}
    {%- endif %}
    {%- if domain_user %}
    --domain-user {{ domain_user }}
    {%- endif %}
    {%- if password %}
    --password {{ password }}
    {%- endif %}
    {%- if cl_by_cl %}
    --cl-by-cl {{ cl_by_cl }}
    {%- endif %}
    {%- if tags %}
    --tags {{ tags }}
    {%- endif %}
    {%- if use_file_path %}
    --use-file-path {{ use_file_path }}
    {%- endif %}
    {%- if ignore_source_history %}
    --ignore-source-history {{ ignore_source_history }}
    {%- endif %}
    {%- if cherrypick %}
    --cherrypick {{ cherrypick }}
    {%- endif %}
    {%- if remote_p4server %}
    --remote-p4server {{ remote_p4server }}
    {%- endif %}
  displayName: elipy integrate
