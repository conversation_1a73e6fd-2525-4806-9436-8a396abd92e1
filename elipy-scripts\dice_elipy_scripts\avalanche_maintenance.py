"""
avalanche_maintenance.py
"""
import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import avalanche
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command(
    "avalanche_maintenance",
    short_help="Perform Avalanche maintenance on the host this is running on.",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(_):  # pylint: disable=unused-argument
    """
    Perform Avalanche maintenance on the host this is running on.
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    avalanche.cleanup_temp_dbs(cleanup_threshold=10)
    avalanche.avalanche_maintenance(drop_old_dbs=True)
