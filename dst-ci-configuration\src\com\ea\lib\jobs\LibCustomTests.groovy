package com.ea.lib.jobs

import com.ea.lib.LibJobDsl
import com.ea.lib.jobsettings.CustomTestsSettings

class LibCustomTests {
    /**
     * Adds generic job parameters for custom tests start jobs.
     */
    static void customTestsStart(def job, def project, def branchFile, def masterFile, String branchName) {
        CustomTestsSettings settings = new CustomTestsSettings()
        settings.initializeCustomTestsStart(branchFile, masterFile, project, branchName)
        job.with {
            description(settings.description)
            disabled(settings.isDisabled)
            logRotator(7, 100)
            quietPeriod(0)
            properties {
                disableConcurrentBuilds()
                disableResume()
                pipelineTriggers {
                    triggers {
                        if (settings.triggerType == 'scm') {
                            pollSCM {
                                scmpoll_spec(settings.cronTrigger)
                            }
                        } else if (settings.triggerType == 'cron') {
                            cron {
                                spec(settings.cronTrigger)
                            }
                        }
                    }
                }
            }
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
            }
            environmentVariables {
                env('branchName', settings.branchName)
                env('projectName', settings.projectName)
                env('customTestsReferenceJob', settings.customTestsReferenceJob)
            }
        }
    }

    /**
     * Adds generic job parameters for custom tests build jobs.
     */
    static void customTestsJob(def job, def project, def branchFile, def masterFile, String branchName) {
        CustomTestsSettings settings = new CustomTestsSettings()
        settings.initializeCustomTestsJob(branchFile, masterFile, project, branchName)
        job.with {
            description(settings.description)
            label(settings.jobLabel)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(settings.workspaceRoot)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(settings.buildName)
                injectPasswords {
                    injectGlobalPasswords()
                    maskPasswords()
                }
                timeout {
                    absolute(480)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
                credentialsBinding {
                    if (settings.userCredentials) {
                        usernamePassword('monkey_email', 'monkey_passwd', settings.userCredentials)
                    }
                    if (settings.fbLoginDetails.p4_creds) {
                        usernamePassword('fb_p4_user', 'fb_p4_passwd', settings.fbLoginDetails.p4_creds)
                    }
                }
            }
            steps {
                if (settings.fbLoginDetails) {
                    batchFile("echo %fb_p4_passwd%|p4 -p ${settings.fbLoginDetails.p4_port} -u %fb_p4_user% login & exit 0")
                }
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                batchFile(settings.elipyCmd)
            }
        }
    }
}
