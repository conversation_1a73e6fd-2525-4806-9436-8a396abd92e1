package com.ea.lib.jobs

import com.ea.lib.LibJobDsl
import com.ea.lib.jobsettings.BuildSelectorSettings

class LibBuildSelector {

    /**
     * Adds generic job parameters for autotest build selector jobs.
     */
    static void selectBuild(def job, def project, def branchInfo, def masterFile, String branchName) {
        def buildSelectorSettings = new BuildSelectorSettings()
        buildSelectorSettings.initialize(branchInfo, masterFile, project, branchName)
        job.with {
            logRotator(7, 100)
            quietPeriod(0)
            label(buildSelectorSettings.jobLabel)
            concurrentBuild()
            customWorkspace(buildSelectorSettings.workspaceRoot)
            parameters {
                choiceParam(
                    'is_test_with_loose_files',
                    ['--is-not-test-with-loose-files', '--is-test-with-loose-files'],
                    'Set to true to make sure that loose files are stored on filer when selecting build'
                )
                booleanParam('use_azure_drone_build', false, 'Only get CL for Drone builds that have been uploaded to Azure')
                booleanParam('use_shift_build', false, 'Only get CL for builds that have been pushed to Shift')
                booleanParam('use_spin_build', false, 'Only get CL for builds that have been uploaded to Spin')
                booleanParam('use_latest_drone', false, 'Get the latest Drone build CLs')
                stringParam {
                    name('platform')
                    defaultValue('None')
                    description('Platform - required parameter with a loose files build')
                    trim(true)
                }
                stringParam {
                    name('required_platforms')
                    defaultValue('')
                    description('List of arguments stating the required platforms for this build')
                    trim(true)
                }
                stringParam {
                    name('client_platforms')
                    defaultValue('')
                    description('List of client platforms need to run for this build. Applicable for large scale tests')
                    trim(true)
                }
                stringParam {
                    name('start_job_url')
                    defaultValue('%BUILD_URL%')
                    description('The URL to the start job')
                    trim(true)
                }
                stringParam {
                    name('build_timeout_hours')
                    defaultValue('1')
                    description('Mark frosty builds as in use for build-timeout-hours => won\'t be deleted')
                    trim(true)
                }
                stringParam {
                    name('config')
                    defaultValue('final')
                    description('Which frosty config to find builds for - optional and only applies for loose files builds')
                    trim(true)
                }
                stringParam {
                    name('region')
                    defaultValue('ww')
                    description('Which frosty region to find builds for - optional and only applies for loose files builds')
                    trim(true)
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName('${JOB_NAME}.${BUILD_NUMBER}')
                timeout {
                    absolute(60)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, buildSelectorSettings.elipyInstallCall, project)
                batchFile(buildSelectorSettings.elipyCmd)
                environmentVariables {
                    propertiesFile('autotest.properties')
                }
            }
        }
    }
}
