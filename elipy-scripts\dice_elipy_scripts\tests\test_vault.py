"""
test_vault.py

Unit testing for vault
"""
import os
import unittest
import sys

from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import patch, <PERSON>Mock, Mock, ANY

sys.modules["elipy2.shift_utils"] = MagicMock()
import dice_elipy_scripts.vault
from elipy2 import LOGGER
from elipy2.exceptions import ELIPYException


class TestVault(unittest.TestCase):
    OPTION_VAULT_TYPE = "--vault-type"
    OPTION_PLATFORM = "--platform"
    OPTION_DATA_BRANCH = "--data-branch"
    OPTION_DATA_CHANGELIST = "--data-changelist"
    OPTION_CODE_BRANCH = "--code-branch"
    OPTION_CODE_CHANGELIST = "--code-changelist"
    OPTION_VERSION = "--version"
    OPTION_BUILD_LOCATION = "--build-location"

    OPTION_MD5_VALIDATION_FLAG = "--md5-validation"
    OPTION_VERIFY_POST_VAULT_FLAG = "--verify-post-vault"

    VALUE_VAULT_TYPE = "build"
    VALUE_VAULT_TYPE_SYMBOLS = "symbols"
    VALUE_PLATFORM = "all"
    VALUE_DATA_BRANCH = "databranch"
    VALUE_DATA_CHANGELIST = "111"
    VALUE_CODE_BRANCH = "codebranch"
    VALUE_CODE_CHANGELIST = "222"
    VALUE_VERSION = "0.0.0"
    VALUE_BUILD_LOCATION = "build_location"

    BASIC_ARGS = [
        OPTION_PLATFORM,
        VALUE_PLATFORM,
        OPTION_VERSION,
        VALUE_VERSION,
        OPTION_CODE_BRANCH,
        VALUE_CODE_BRANCH,
        OPTION_CODE_CHANGELIST,
        VALUE_CODE_CHANGELIST,
        OPTION_DATA_CHANGELIST,
        VALUE_DATA_CHANGELIST,
        OPTION_DATA_BRANCH,
        VALUE_DATA_BRANCH,
        OPTION_MD5_VALIDATION_FLAG,
        OPTION_VERIFY_POST_VAULT_FLAG,
    ]

    VAULT_BUILD_ARGS = BASIC_ARGS + [
        OPTION_VAULT_TYPE,
        VALUE_VAULT_TYPE,
    ]

    VAULT_SYMBOLS_ARGS = BASIC_ARGS + [
        OPTION_VAULT_TYPE,
        VALUE_VAULT_TYPE_SYMBOLS,
    ]

    VERIFICATION_CONFIG_FILE_CONTENTS = {
        "root": {
            "default": {
                "base": ["$DESTINATION_BASEPATH$", "$VERSION$"],
                "paths": [{"path_location": [], "min_items": 7}],
            }
        },
        "server": {
            "default": {
                "base": [
                    "$DESTINATION_BASEPATH$",
                    "$VERSION$",
                    "server",
                    "$DATACHANGELIST$_$CODECHANGELIST$",
                ],
                "paths": [
                    {"path_location": [], "min_items": 3},
                    {"path_location": ["bbb"], "min_items": 3},
                    {"path_location": ["aaa", "zzz", "empty.txt"]},
                ],
            }
        },
        "linuxserver": {
            "default": {
                "base": [
                    "$DESTINATION_BASEPATH$",
                    "$VERSION$",
                    "linuxserver",
                    "$DATACHANGELIST$_$CODECHANGELIST$",
                ],
                "paths": [
                    {"path_location": [], "min_items": 3},
                    {"path_location": ["bbb"], "min_items": 3},
                    {"path_location": ["aaa", "zzz", "empty.txt"]},
                ],
            }
        },
        "ps4": {
            "default": {
                "base": [
                    "$DESTINATION_BASEPATH$",
                    "$VERSION$",
                    "ps4",
                    "$DATACHANGELIST$_$CODECHANGELIST$",
                ],
                "paths": [
                    {"path_location": [], "min_items": 3},
                    {"path_location": ["bbb"], "min_items": 3},
                    {"path_location": ["aaa", "zzz", "empty.txt"]},
                ],
            },
            "irc": {
                "base": [
                    "$DESTINATION_BASEPATH$",
                    "$VERSION$",
                    "ps4",
                    "$DATACHANGELIST$_$CODECHANGELIST$",
                ],
                "paths": [
                    {"path_location": [], "min_items": 7},
                    {"path_location": ["xxx"], "min_items": 7},
                    {"path_location": ["yyy", "zzz", "empty.txt"]},
                ],
            },
        },
        "ps5": {
            "default": {
                "base": [
                    "$DESTINATION_BASEPATH$",
                    "$VERSION$",
                    "ps5",
                    "$DATACHANGELIST$_$CODECHANGELIST$",
                ],
                "paths": [
                    {"path_location": [], "min_items": 3},
                    {"path_location": ["bbb"], "min_items": 3},
                    {"path_location": ["aaa", "zzz", "empty.txt"]},
                ],
            }
        },
        "xb1": {
            "default": {
                "base": [
                    "$DESTINATION_BASEPATH$",
                    "$VERSION$",
                    "xb1",
                    "$DATACHANGELIST$_$CODECHANGELIST$",
                ],
                "paths": [
                    {"path_location": [], "min_items": 3},
                    {"path_location": ["bbb"], "min_items": 3},
                    {"path_location": ["aaa", "zzz", "empty.txt"]},
                ],
            }
        },
        "xbsx": {
            "default": {
                "base": [
                    "$DESTINATION_BASEPATH$",
                    "$VERSION$",
                    "xbsx",
                    "$DATACHANGELIST$_$CODECHANGELIST$",
                ],
                "paths": [
                    {"path_location": [], "min_items": 3},
                    {"path_location": ["bbb"], "min_items": 3},
                    {"path_location": ["aaa", "zzz", "empty.txt"]},
                ],
            }
        },
        "win64": {
            "default": {
                "base": [
                    "$DESTINATION_BASEPATH$",
                    "$VERSION$",
                    "win64",
                    "$DATACHANGELIST$_$CODECHANGELIST$",
                ],
                "paths": [
                    {"path_location": [], "min_items": 3},
                    {"path_location": ["bbb"], "min_items": 3},
                    {"path_location": ["aaa", "zzz", "empty.txt"]},
                ],
            }
        },
    }

    def setUp(self):
        pass

    def tearDown(self):
        pass

    @patch("dice_elipy_scripts.vault.SETTINGS")
    @patch("dice_elipy_scripts.vault.vault.vault_build")
    @patch("dice_elipy_scripts.vault.run_expression_debug_data")
    @patch("dice_elipy_scripts.vault.vault.vault_symbols")
    @patch("dice_elipy_scripts.vault.post_vaulting_verification")
    def test_vault_build(
        self, mock_verify, mock_symbols, mock_expression, mock_build, mock_settings
    ):
        mock_settings.get.return_value = "/mock/vault/destination"
        runner = CliRunner()
        result = runner.invoke(dice_elipy_scripts.vault.cli, self.VAULT_BUILD_ARGS)
        if result.exception:
            LOGGER.info(result.exception)
        assert result.exit_code == 0
        assert mock_build.call_count >= 1
        mock_verify.assert_called_once()
        mock_symbols.assert_not_called()
        mock_expression.assert_not_called()

    @patch("dice_elipy_scripts.vault.SETTINGS")
    @patch("dice_elipy_scripts.vault.vault.vault_build")
    @patch("dice_elipy_scripts.vault.run_expression_debug_data")
    @patch("dice_elipy_scripts.vault.vault.vault_symbols")
    @patch("dice_elipy_scripts.vault.post_vaulting_verification")
    def test_vault_build(
        self, mock_verify, mock_symbols, mock_expression, mock_build, mock_settings
    ):
        mock_settings.get.return_value = "/mock/vault/destination"
        runner = CliRunner()
        result = runner.invoke(
            dice_elipy_scripts.vault.cli,
            [
                self.OPTION_PLATFORM,
                "all-but-gen4",
                self.OPTION_VERSION,
                self.VALUE_VERSION,
                self.OPTION_CODE_BRANCH,
                self.VALUE_CODE_BRANCH,
                self.OPTION_CODE_CHANGELIST,
                self.VALUE_CODE_CHANGELIST,
                self.OPTION_DATA_CHANGELIST,
                self.VALUE_DATA_CHANGELIST,
                self.OPTION_DATA_BRANCH,
                self.VALUE_DATA_BRANCH,
                self.OPTION_VAULT_TYPE,
                self.VALUE_VAULT_TYPE,
            ],
        )
        if result.exception:
            LOGGER.info(result.exception)
        assert result.exit_code == 0
        assert mock_build.call_count == 3
        mock_symbols.assert_not_called()
        mock_expression.assert_not_called()

    @patch("dice_elipy_scripts.vault.SETTINGS")
    @patch("dice_elipy_scripts.vault.vault.vault_build")
    @patch("dice_elipy_scripts.vault.run_expression_debug_data")
    @patch("dice_elipy_scripts.vault.get_symstore_vault_destination")
    @patch("dice_elipy_scripts.vault.vault.vault_symbols")
    @patch("dice_elipy_scripts.vault.filer_paths.get_frosty_base_build_path")
    @patch("dice_elipy_scripts.vault.get_builds_within_path")
    @patch("dice_elipy_scripts.vault.post_vaulting_verification")
    def test_vault_symbols(
        self,
        mock_verify,
        mock_get,
        mock_filer,
        mock_symbols,
        mock_symstore,
        mock_expression,
        mock_build,
        mock_settings,
    ):
        mock_settings.get.return_value = "/mock/vault/destination"
        mock_symstore.return_value = "/symstore/path"
        mock_filer.return_value = "/frosty/base/build/path"
        mock_get.return_value = []
        runner = CliRunner()
        result = runner.invoke(dice_elipy_scripts.vault.cli, self.VAULT_SYMBOLS_ARGS)
        if result.exception:
            LOGGER.info(result.exception)
        assert result.exit_code == 0
        assert mock_symbols.call_count >= 1
        mock_verify.assert_called_once()
        mock_expression.assert_not_called()
        mock_build.assert_not_called()

    @patch("dice_elipy_scripts.vault.SETTINGS")
    @patch("dice_elipy_scripts.vault.vault.vault_build")
    @patch("dice_elipy_scripts.vault.run_expression_debug_data")
    @patch("dice_elipy_scripts.vault.get_symstore_vault_destination")
    @patch("dice_elipy_scripts.vault.vault.vault_symbols")
    @patch("dice_elipy_scripts.vault.filer_paths.get_frosty_base_build_path")
    @patch("dice_elipy_scripts.vault.get_builds_within_path")
    @patch("dice_elipy_scripts.vault.post_vaulting_verification")
    def test_vault_symbols_with_empty_string_build_location(
        self,
        mock_verify,
        mock_get,
        mock_filer,
        mock_symbols,
        mock_symstore,
        mock_expression,
        mock_build,
        mock_settings,
    ):
        mock_settings.get.return_value = "/mock/vault/destination"
        mock_symstore.return_value = "/symstore/path"
        mock_filer.return_value = "/frosty/base/build/path"
        mock_get.return_value = []
        runner = CliRunner()
        result = runner.invoke(
            dice_elipy_scripts.vault.cli, self.VAULT_SYMBOLS_ARGS + [self.OPTION_BUILD_LOCATION, ""]
        )
        if result.exception:
            LOGGER.info(result.exception)
        assert result.exit_code == 0
        assert mock_symbols.call_count >= 1
        mock_symbols.assert_any_call(
            source=ANY,
            vault_destination=ANY,
            symstore_vault_destination=ANY,
            build_url=ANY,
            build_id=ANY,
            platform=ANY,
            change_list=ANY,
            code_branch=ANY,
            product_name=ANY,
            md5_validation=ANY,
            dry_run=ANY,
            location=None,
        )

    @patch("dice_elipy_scripts.vault.SETTINGS")
    @patch("dice_elipy_scripts.vault.vault.vault_build")
    @patch("dice_elipy_scripts.vault.run_expression_debug_data")
    @patch("dice_elipy_scripts.vault.get_symstore_vault_destination")
    @patch("dice_elipy_scripts.vault.vault.vault_symbols")
    @patch("dice_elipy_scripts.vault.filer_paths.get_frosty_base_build_path")
    @patch("dice_elipy_scripts.vault.get_builds_within_path")
    @patch("dice_elipy_scripts.vault.post_vaulting_verification")
    def test_vault_symbols_with_none_build_location(
        self,
        mock_verify,
        mock_get,
        mock_filer,
        mock_symbols,
        mock_symstore,
        mock_expression,
        mock_build,
        mock_settings,
    ):
        mock_settings.get.return_value = "/mock/vault/destination"
        mock_symstore.return_value = "/symstore/path"
        mock_filer.return_value = "/frosty/base/build/path"
        mock_get.return_value = []
        runner = CliRunner()
        result = runner.invoke(
            dice_elipy_scripts.vault.cli,
            self.VAULT_SYMBOLS_ARGS
            + [
                self.OPTION_BUILD_LOCATION,
                None,
            ],
        )
        if result.exception:
            LOGGER.info(result.exception)
        assert result.exit_code == 0
        assert mock_symbols.call_count >= 1
        mock_symbols.assert_any_call(
            source=ANY,
            vault_destination=ANY,
            symstore_vault_destination=ANY,
            build_url=ANY,
            build_id=ANY,
            platform=ANY,
            change_list=ANY,
            code_branch=ANY,
            product_name=ANY,
            md5_validation=ANY,
            dry_run=ANY,
            location=None,
        )

    @patch("dice_elipy_scripts.vault.SETTINGS")
    @patch("dice_elipy_scripts.vault.vault.vault_build")
    @patch("dice_elipy_scripts.vault.run_expression_debug_data")
    @patch("dice_elipy_scripts.vault.get_symstore_vault_destination")
    @patch("dice_elipy_scripts.vault.vault.vault_symbols")
    @patch("dice_elipy_scripts.vault.filer_paths.get_frosty_base_build_path")
    @patch("dice_elipy_scripts.vault.get_builds_within_path")
    @patch("dice_elipy_scripts.vault.post_vaulting_verification")
    def test_vault_symbols_with_value_build_location(
        self,
        mock_verify,
        mock_get,
        mock_filer,
        mock_symbols,
        mock_symstore,
        mock_expression,
        mock_build,
        mock_settings,
    ):
        mock_settings.get.return_value = "/mock/vault/destination"
        mock_symstore.return_value = "/symstore/path"
        mock_filer.return_value = "/frosty/base/build/path"
        mock_get.return_value = []
        runner = CliRunner()
        result = runner.invoke(
            dice_elipy_scripts.vault.cli,
            self.VAULT_SYMBOLS_ARGS
            + [
                self.OPTION_BUILD_LOCATION,
                self.VALUE_BUILD_LOCATION,
            ],
        )
        if result.exception:
            LOGGER.info(result.exception)
        assert result.exit_code == 0
        assert mock_symbols.call_count >= 1
        mock_symbols.assert_any_call(
            source=ANY,
            vault_destination=ANY,
            symstore_vault_destination=ANY,
            build_url=ANY,
            build_id=ANY,
            platform=ANY,
            change_list=ANY,
            code_branch=ANY,
            product_name=ANY,
            md5_validation=ANY,
            dry_run=ANY,
            location=self.VALUE_BUILD_LOCATION,
        )

    @patch("dice_elipy_scripts.vault.SETTINGS")
    @patch("dice_elipy_scripts.vault.vault.vault_build")
    @patch("dice_elipy_scripts.vault.run_expression_debug_data")
    @patch("dice_elipy_scripts.vault.get_symstore_vault_destination")
    @patch("dice_elipy_scripts.vault.vault.vault_symbols")
    @patch("dice_elipy_scripts.vault.filer_paths.get_frosty_base_build_path")
    @patch("dice_elipy_scripts.vault.get_builds_within_path")
    @patch("dice_elipy_scripts.vault.post_vaulting_verification")
    def test_vault_symbols_with_combined_builds(
        self,
        mock_verify,
        mock_get,
        mock_filer,
        mock_symbols,
        mock_symstore,
        mock_expression,
        mock_build,
        mock_settings,
    ):
        mock_settings.get.return_value = "/mock/vault/destination"
        mock_symstore.return_value = "/symstore/path"
        mock_filer.return_value = "/frosty/base/build/path"
        mock_build1 = Mock()
        mock_build1.id = os.path.join(
            "example",
            "path",
            "to",
            "build1",
            "final",
            "combine_build",
            "123",
            "combine_build",
            "123",
        )
        mock_build1.source = {"package_type": "combine_build"}
        mock_build2 = Mock()
        mock_build2.id = os.path.join(
            "example",
            "path",
            "to",
            "build2",
            "retail",
            "combine_build",
            "123",
            "combine_build",
            "123",
        )
        mock_build2.source = {"package_type": "combine_build"}
        mock_build3 = Mock()
        mock_build3.id = os.path.join("example", "path", "to", "build3", "performance", "234")
        mock_build3.source = {"package_type": "final"}
        mock_get.return_value = iter([mock_build1, mock_build2, mock_build3])
        runner = CliRunner()
        result = runner.invoke(
            dice_elipy_scripts.vault.cli,
            [
                self.OPTION_VAULT_TYPE,
                self.VALUE_VAULT_TYPE_SYMBOLS,
                self.OPTION_PLATFORM,
                "ps5",
                self.OPTION_VERSION,
                self.VALUE_VERSION,
                self.OPTION_CODE_BRANCH,
                self.VALUE_CODE_BRANCH,
                self.OPTION_CODE_CHANGELIST,
                self.VALUE_CODE_CHANGELIST,
                self.OPTION_DATA_CHANGELIST,
                self.VALUE_DATA_CHANGELIST,
                self.OPTION_DATA_BRANCH,
                self.VALUE_DATA_BRANCH,
                self.OPTION_MD5_VALIDATION_FLAG,
                self.OPTION_VERIFY_POST_VAULT_FLAG,
            ],
        )
        if result.exception:
            LOGGER.info(result.exception)
        assert result.exit_code == 0
        assert mock_symbols.call_count == 2  # only 1 distinct combine build
        mock_verify.assert_called_once()
        mock_expression.assert_not_called()
        mock_build.assert_not_called()

    @patch("dice_elipy_scripts.vault.SETTINGS")
    @patch(
        "dice_elipy_scripts.vault.get_config_path_post_vaulting_verification",
        return_value=os.path.join(
            os.path.dirname(os.path.abspath(__file__)),
            "data",
            "vault_verification_config_test.yml",
        ),
    )
    @patch(
        "dice_elipy_scripts.vault.get_destination_base_post_vaulting_verification",
        return_value=os.path.join(os.path.dirname(os.path.abspath(__file__)), "data", "test_vault"),
    )
    @patch("dice_elipy_scripts.vault.filer_paths.get_frosty_base_build_path")
    @patch("dice_elipy_scripts.vault.shift_check")
    @patch("dice_elipy_scripts.vault.vault.vault_build")
    @patch("dice_elipy_scripts.vault.filer_paths.get_expression_debug_data_path")
    @patch("dice_elipy_scripts.vault.run_expression_debug_data")
    @patch("dice_elipy_scripts.vault.get_symstore_vault_destination")
    @patch("dice_elipy_scripts.vault.vault.vault_symbols")
    @patch("dice_elipy_scripts.vault.get_builds_within_path", return_value=[])
    def test_post_vaulting_verification_run(
        self,
        mock_builds,
        mock_symbols,
        mock_symstore,
        mock_debug,
        mock_debug_path,
        mock_build,
        mock_shift,
        mock_filer,
        mock_get_dest_base,
        mock_get_conf_path,
        mock_settings,
    ):
        mock_settings.get.return_value = "/mock/vault/destination"
        runner = CliRunner()
        result = runner.invoke(
            dice_elipy_scripts.vault.cli,
            [
                self.OPTION_VAULT_TYPE,
                self.VALUE_VAULT_TYPE,
                self.OPTION_PLATFORM,
                self.VALUE_PLATFORM,
                self.OPTION_VERSION,
                self.VALUE_VERSION,
                self.OPTION_CODE_BRANCH,
                self.VALUE_CODE_BRANCH,
                self.OPTION_CODE_CHANGELIST,
                self.VALUE_CODE_CHANGELIST,
                self.OPTION_DATA_CHANGELIST,
                self.VALUE_DATA_CHANGELIST,
                self.OPTION_DATA_BRANCH,
                self.VALUE_DATA_BRANCH,
                self.OPTION_MD5_VALIDATION_FLAG,
                self.OPTION_VERIFY_POST_VAULT_FLAG,
            ],
        )
        mock_symbols.assert_not_called()
        assert mock_build.call_count >= 1
        assert mock_get_dest_base.call_count == 1
        assert mock_get_conf_path.call_count == 1
        if result.exception:
            LOGGER.info(result.exception)
        assert result.exit_code == 0

    @patch("os.path.join", MagicMock(side_effect=lambda *x: "\\".join(x)))
    @patch("os.path.exists", MagicMock(return_value=False))
    @patch(
        "dice_elipy_scripts.vault.get_config_path_post_vaulting_verification",
        MagicMock(),
    )
    @patch(
        "dice_elipy_scripts.vault.SETTINGS.load_auxiliary_file_contents",
        MagicMock(return_value=VERIFICATION_CONFIG_FILE_CONTENTS),
    )
    @patch(
        "dice_elipy_scripts.vault.get_destination_base_post_vaulting_verification",
        MagicMock(return_value="destination"),
    )
    def test_post_vaulting_verification_error(self):
        with self.assertRaises(ELIPYException) as context:
            # all should fail because VERIFICATION_CONFIG_FILE_CONTENTS
            # doesn't include versions and changelists test data
            dice_elipy_scripts.vault.post_vaulting_verification(
                "destination",
                ["server", "linuxserver", "ps4", "ps5", "xb1", "xbsx", "win64"],
                self.VALUE_VERSION,
                self.VALUE_DATA_CHANGELIST,
                self.VALUE_CODE_CHANGELIST,
            )
        self.assertEqual(
            "Post vault verification failed - 22 error(s) have occurred.",
            str(context.exception),
        )

    @patch("os.path.join", MagicMock(side_effect=lambda *x: "\\".join(x)))
    @patch("os.listdir", MagicMock(return_value="This files list must have 7 items".split()))
    @patch("os.path.exists", MagicMock(return_value=True))
    @patch(
        "dice_elipy_scripts.vault.get_config_path_post_vaulting_verification",
        MagicMock(),
    )
    @patch(
        "dice_elipy_scripts.vault.SETTINGS.load_auxiliary_file_contents",
        MagicMock(return_value=VERIFICATION_CONFIG_FILE_CONTENTS),
    )
    @patch(
        "dice_elipy_scripts.vault.get_destination_base_post_vaulting_verification",
        MagicMock(return_value="destination"),
    )
    def test_post_vaulting_verification_success_without_vault_layout_passed(self):
        dice_elipy_scripts.vault.post_vaulting_verification(
            "destination",
            ["server", "linuxserver", "ps4", "ps5", "xb1", "xbsx", "win64"],
            self.VALUE_VERSION,
            self.VALUE_DATA_CHANGELIST,
            self.VALUE_CODE_CHANGELIST,
        )

    @patch("os.path.join", MagicMock(side_effect=lambda *x: "\\".join(x)))
    @patch("os.listdir", MagicMock(return_value="This files list must have 7 items".split()))
    @patch("os.path.exists", MagicMock(return_value=True))
    @patch(
        "dice_elipy_scripts.vault.get_config_path_post_vaulting_verification",
        MagicMock(),
    )
    @patch(
        "dice_elipy_scripts.vault.SETTINGS.load_auxiliary_file_contents",
        MagicMock(return_value=VERIFICATION_CONFIG_FILE_CONTENTS),
    )
    @patch(
        "dice_elipy_scripts.vault.get_destination_base_post_vaulting_verification",
        MagicMock(return_value="destination"),
    )
    def test_post_vaulting_verification_success_with_vault_layout_passed(self):
        # An additional vault layout (irc) has been added to
        # the ps4 section of VERIFICATION_CONFIG_FILE_CONTENTS
        vault_layouts_under_test = ["default", "irc"]
        for vault_layout in vault_layouts_under_test:
            dice_elipy_scripts.vault.post_vaulting_verification(
                "destination",
                ["ps4"],
                self.VALUE_VERSION,
                self.VALUE_DATA_CHANGELIST,
                self.VALUE_CODE_CHANGELIST,
                vault_layout=vault_layout,
            )

    @patch("os.path.join", MagicMock(side_effect=lambda *x: "\\".join(x)))
    @patch("os.listdir", MagicMock(return_value="This files list must have 7 items".split()))
    @patch("os.path.exists", MagicMock(return_value=True))
    @patch(
        "dice_elipy_scripts.vault.get_config_path_post_vaulting_verification",
        MagicMock(),
    )
    @patch(
        "dice_elipy_scripts.vault.SETTINGS.load_auxiliary_file_contents",
        MagicMock(return_value=VERIFICATION_CONFIG_FILE_CONTENTS),
    )
    @patch(
        "dice_elipy_scripts.vault.get_destination_base_post_vaulting_verification",
        MagicMock(return_value="destination"),
    )
    def test_post_vaulting_verification_success_fails_when_vault_layout_invalid(self):
        # An additional vault layout (irc) has been added to
        # the ps4 section of VERIFICATION_CONFIG_FILE_CONTENTS
        invalid_layouts = [None, "", "IDONTEXIST"]
        for invalid_layout in invalid_layouts:
            with self.assertRaises(ELIPYException) as context:
                dice_elipy_scripts.vault.post_vaulting_verification(
                    "destination",
                    ["ps4"],
                    self.VALUE_VERSION,
                    self.VALUE_DATA_CHANGELIST,
                    self.VALUE_CODE_CHANGELIST,
                    vault_layout=invalid_layout,
                    vault_verification_location=None,
                )
        self.assertEqual(
            "Post vault verification failed - 1 error(s) have occurred.",
            str(context.exception),
        )

    @patch("os.path.join", MagicMock(side_effect=lambda *x: "\\".join(x)))
    @patch("os.listdir", MagicMock(return_value="This files list must have 7 items".split()))
    @patch("os.path.exists", MagicMock(return_value=True))
    @patch("dice_elipy_scripts.vault.get_config_path_post_vaulting_verification")
    @patch(
        "dice_elipy_scripts.vault.SETTINGS.load_auxiliary_file_contents",
        MagicMock(return_value=VERIFICATION_CONFIG_FILE_CONTENTS),
    )
    @patch(
        "dice_elipy_scripts.vault.get_destination_base_post_vaulting_verification",
        MagicMock(return_value="destination"),
    )
    def test_post_vaulting_verification(self, mock_get_config_path_post_vaulting_verification):
        dice_elipy_scripts.vault.post_vaulting_verification(
            "destination",
            ["ps4"],
            self.VALUE_VERSION,
            self.VALUE_DATA_CHANGELIST,
            self.VALUE_CODE_CHANGELIST,
            vault_layout="default",
            vault_verification_location=None,
        )
        mock_get_config_path_post_vaulting_verification.assert_called_once_with(
            vault_verification_location=None
        )

    @patch("os.path.join", MagicMock(side_effect=lambda *x: "\\".join(x)))
    @patch("os.listdir", MagicMock(return_value="This files list must have 7 items".split()))
    @patch("os.path.exists", MagicMock(return_value=True))
    @patch("dice_elipy_scripts.vault.get_config_path_post_vaulting_verification")
    @patch(
        "dice_elipy_scripts.vault.SETTINGS.load_auxiliary_file_contents",
        MagicMock(return_value=VERIFICATION_CONFIG_FILE_CONTENTS),
    )
    @patch(
        "dice_elipy_scripts.vault.get_destination_base_post_vaulting_verification",
        MagicMock(return_value="destination"),
    )
    def test_post_vaulting_verification_alt_location(
        self, mock_get_config_path_post_vaulting_verification
    ):
        dice_elipy_scripts.vault.post_vaulting_verification(
            "destination",
            ["ps4"],
            self.VALUE_VERSION,
            self.VALUE_DATA_CHANGELIST,
            self.VALUE_CODE_CHANGELIST,
            vault_layout="default",
            vault_verification_location="alt_location",
        )
        mock_get_config_path_post_vaulting_verification.assert_called_once_with(
            vault_verification_location="alt_location"
        )

    @patch(
        "os.environ.get",
        MagicMock(
            side_effect=lambda x: {
                "ELIPY_CONFIG": os.path.join("yml", "elipy_test.yml"),
            }[x]
        ),
    )
    @patch("elipy2.frostbite_core.get_game_root", MagicMock(return_value="C:"))
    @patch(
        "elipy2.SETTINGS.get",
        MagicMock(return_value="vault_verification_config_test.yml"),
    )
    def test_get_config_path_pvv(self):
        assert (
            os.path.join("C:", "yml", "vault_verification_config_test.yml")
            == dice_elipy_scripts.vault.get_config_path_post_vaulting_verification()
        )

    def test_get_config_files_contents(self):
        config_path = os.path.join(
            os.path.dirname(os.path.abspath(__file__)),
            "data",
            "vault_verification_config_test.yml",
        )
        assert (
            self.VERIFICATION_CONFIG_FILE_CONTENTS
            == dice_elipy_scripts.vault.SETTINGS.load_auxiliary_file_contents(config_path)
        )
        self.assertRaises(
            ELIPYException,
            dice_elipy_scripts.vault.SETTINGS.load_auxiliary_file_contents,
            config_path + "123",
        )

    @patch("elipy2.SETTINGS.get", MagicMock(return_value="C:\\test_vault"))
    def test_get_destination_base_pvv(self):
        assert (
            "C:\\test_vault"
            == dice_elipy_scripts.vault.get_destination_base_post_vaulting_verification(None)
        )
        assert (
            "C:\\test_vault"
            == dice_elipy_scripts.vault.get_destination_base_post_vaulting_verification(
                "C:\\test_vault"
            )
        )
