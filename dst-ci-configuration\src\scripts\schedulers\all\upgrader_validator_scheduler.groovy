package scripts.schedulers.all

def preview_project = ProjectClass(env.preview_project_name)

/**
 * upgrade_validator_scheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Check Perforce for SCM triggering.') {
            steps {
                script {
                    P4PreviewCode(preview_project, 'upgrade', env.preview_folder, env.preview_branch)
                }
            }
        }
        stage('Trigger validator for FrostbiteDatabaseUpgrader.') {
            steps {
                script {
                    // Get code changelist
                    def code_changelist = params.code_changelist ?: env.P4_CHANGELIST

                    def args = [
                        string(name: 'code_changelist', value: code_changelist),
                    ]
                    def inject_map = [
                        'code_changelist': code_changelist,
                    ]
                    EnvInject(currentBuild, inject_map)
                    currentBuild.displayName = env.JOB_NAME + '.' + code_changelist

                    def job_name = 'data-upgrader-validator.for.' + env.source_branch + '.to.' + env.target_branch
                    def validator_job = build(job: job_name, parameters: args, propagate: false)
                    currentBuild.result = validator_job.result.toString()

                    SlackMessageNew(currentBuild, env.slack_channel, preview_project.short_name)
                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
        stage('Scan for errors') { steps { ScanForErrors(currentBuild, env.slack_notify_bot) } }
    }
    post { failure { SlackNotifyBot(currentBuild, env.slack_notify_bot) } }
}
