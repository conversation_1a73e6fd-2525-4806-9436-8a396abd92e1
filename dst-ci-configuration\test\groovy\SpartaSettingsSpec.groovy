import com.ea.lib.jobsettings.SpartaSettings
import spock.lang.Specification

class SpartaSettingsSpec extends Specification {

    class BranchFile {
        static Map standard_jobs_settings = [
            branch_name         : 'branch',
            workspace_root      : 'workspace-root',
            disable_build       : true,
            elipy_call          : 'elipy-call',
            elipy_install_call  : 'elipy-install-call',
            job_label_statebuild: 'build-release && util',
            timeout_hours_sparta: 3,
            'dataset'           : 'data_set',
        ]
        static Map general_settings = [:]
    }

    class MasterFile {
        static Map branches = [branch: [data_branch: 'data-branch', code_branch: 'code-branch']]
    }

    class ProjectFile {
        static String name = 'Kingston'
    }

    void "test that we get expected job settings in initializeSpartaStart"() {
        when:
        SpartaSettings spartaSettings = new SpartaSettings()
        spartaSettings.initializeSpartaStart(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(spartaSettings) {
            branchName == 'branch'
            description == 'Scheduler to start a Sparta job on branch, using the latest verified binary build.'
            isDisabled == BranchFile.standard_jobs_settings.disable_build
            projectName == ProjectFile.name
        }
    }

    void "test that we get expected job settings in initializeSpartaBundleJob"() {
        when:
        SpartaSettings spartaSettings = new SpartaSettings()
        spartaSettings.initializeSpartaBundleJob(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(spartaSettings) {
            jobLabel == BranchFile.standard_jobs_settings.job_label_statebuild
            timeoutMinutes == BranchFile.standard_jobs_settings.timeout_hours_sparta * 60
            description == "Builds ${BranchFile.standard_jobs_settings.dataset} for win64 with code from ${MasterFile.branches.branch.code_branch}."
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${ENV, var="bundle_changelist"}'
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} sparta_bundle ${BranchFile.standard_jobs_settings.dataset}" +
                " --code-branch ${MasterFile.branches.branch.code_branch} --code-changelist %code_changelist%" +
                ' --source-bundle-path %source_bundle_path% --bundle-changelist %bundle_changelist% --description %description% --no-submit %no_submit%'
        }
    }
}
