/**
 * p4CheckoutManual.groovy
 */
void call(String viewName, String workspaceName, String p4CodeCredentials) {
    checkout perforce(
        credential: p4CodeCredentials,
        workspace: manualSpec(
            charset: 'none',
            pinHost: true,
            name: workspaceN<PERSON>,
            spec: clientSpec(
                allwrite: false,
                clobber: true,
                compress: false,
                locked: false,
                modtime: false,
                rmdir: false,
                streamName: '',
                line: 'LOCAL',
                view: viewName,
                changeView: '',
                type: 'WRITABLE',
                serverID: '',
                backup: true
            ),
            cleanup: false,
            syncID: ''
        ),
        populate: previewOnly(
            quiet: true
        )
    )
}
