/**
 * NeedsRebuildCodePreflight.groovy
 * Checks for a certain code preflight job if there is a previous one on the same unshelved code cl that built successfully.
 */
boolean call(def job_name, def current_changelist, def numOfBuilds = 10) {
    def jenkinsItem = Jenkins.get()?.getItem(job_name)
    if (jenkinsItem && jenkinsItem.builds) {
        def last_jobs = jenkinsItem.getLastBuildsOverThreshold(numOfBuilds, Result.UNSTABLE)
        for (last_job in last_jobs) {
            if (last_job != null && last_job.getEnvironment(TaskListener.NULL)?.unshelve_changelist != null && last_job.getEnvironment(TaskListener.NULL)?.unshelve_changelist == current_changelist) {
                if (last_job.result != null) {
                    echo "Result of last $job_name run on unshelved code CL $current_changelist was ${last_job.result}."

                    if (last_job.result == Result.SUCCESS) {
                        echo 'Will not rebuild.'
                        return false
                    }

                    echo 'Rebuilding.'
                }
            }
        }
    }
    return true
}
