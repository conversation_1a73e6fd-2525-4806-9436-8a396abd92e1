"""
outsource_package.py
"""
import os
import click

from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.licensee_helper import set_licensee
from dice_elipy_scripts.utils.code_utils import run_gensln
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import code, core, data, local_paths, LOGGER, p4, running_processes, frostbite_core
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command("outsource_package", short_help="Create packages for outsourcers.")
@click.option("--clean", default="false", help="Delete TnT/Local if --clean true is passed.")
@click.option("--code-changelist", required=True, help="Perforce code changelist number.")
@click.option("--config", default="release", help="Config to generate solution for.")
@click.option(
    "--data-directory",
    default=None,
    help="Which data directory to use for fetching licensee settings.",
)
@click.option("--dry-run", is_flag=True, help="Run without submitting to Perforce.")
@click.option("--email", default=None, help="User email to authenticate to package server.")
@click.option(
    "--domain-user",
    default=None,
    help="The user to authenticate to package server as DOMAIN\\user",
)
@click.option("--framework-args", multiple=True, help="Framework arguments for gensln.")
@click.option("--licensee", multiple=True, default=None, help="Licensee to use.")
@click.option("--p4-client-code", required=True, help="Perforce workspace name to sync code.")
@click.option(
    "--p4-client-packages",
    required=True,
    help="Perforce workspace to submit generated packages.",
)
@click.option("--p4-port", required=True, help="Perforce server address.")
@click.option("--p4-user", required=True, help="Perforce user name.")
@click.option(
    "--password",
    default=None,
    help="User credentials to authenticate to package server.",
)
@click.option("--script-path", required=True, help="Path to the script (relative to TnT).")
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
# pylint: disable=too-many-locals, invalid-name, too-many-statements, protected-access
def cli(
    _,
    clean,
    code_changelist,
    config,
    data_directory,
    dry_run,
    email,
    domain_user,
    framework_args,
    licensee,
    p4_client_code,
    p4_client_packages,
    p4_port,
    p4_user,
    password,
    script_path,
):
    """
    Create packages for outsourcers.
    These will be used when outsourcing code development to third party companies.
    External developers generally don't have access to the internal packages,
    and therefore need a separate solution for this.
    """

    # adding sentry tags
    add_sentry_tags(__file__)

    # Clean up before running the job.
    running_processes.kill()
    core.clean_temp()

    if clean.lower() == "true":
        core.delete_folder(local_paths.get_tnt_local_path(), close_handles=True)

    # Delete three packages folders on the machine.
    core.delete_folder(local_paths.get_packages_path(), close_handles=True)
    core.delete_folder(local_paths.get_tnt_localpackages_path(), close_handles=True)
    core.delete_folder(local_paths.get_tnt_packages_path(), close_handles=True)

    # Initialize
    if data_directory is not None:
        data.DataUtils.set_datadir(data_directory)

    # Import the script module
    try:
        # While the script is called outsource-package-install, it contains two parts:
        # build and deploy. We only use the build part for this script.
        outsource_package = core.import_module_from_file(
            "outsource-package-install", os.path.join(frostbite_core.get_tnt_root(), script_path)
        )
    except Exception as exc:
        LOGGER.info(
            "Unable to import {}".format(os.path.join(frostbite_core.get_tnt_root(), script_path))
        )
        raise exc

    # Set framework args, handle licensee settings if needed.
    framework_args = list(framework_args)
    framework_args = set_licensee(list(licensee), framework_args)

    # Add extra framework args
    framework_args.append("-G:frostbite.pipeline.disable-platform-sdks=true")
    framework_args.append("-G:useProxyPackages=true")
    framework_args.append("-G:frostbite.usePrebuiltPackages=true")
    framework_args.append("-G:frostbite.is-outsource-build=true")

    builder = code.CodeUtils(
        "tool",
        config,
        monkey_build_label=code_changelist,
        p4_port=p4_port,
        p4_user=p4_user,
        p4_client=p4_client_code,
    )
    try:
        # Increment Client Version
        builder.increment_client_version()
        # Generate solution
        run_gensln(
            password=password,
            user=email,
            domain_user=domain_user,
            framework_args=framework_args,
            builder=builder,
        )
    finally:
        # Revert Increment Client Version files when done
        builder.clean_platform_temp_files()

    # Run the outsource-package script.
    outsource_package.build(local_paths.get_tnt_packages_path())

    try:
        # Create a Perforce object, to be used for reconcile and submit.
        perforce = p4.P4Utils(port=p4_port, client=p4_client_packages, user=p4_user)

        # Reconcile difference between generated and check-in versions of the packages folder.
        LOGGER.info("Reconcile difference between generated and check-in versions of packages.")
        packages_p4_path = os.path.join(local_paths.get_tnt_packages_path(), "...")
        perforce.reconcile(path=packages_p4_path, options=["a", "d", "e", "f"], quiet=False)

        if not dry_run:
            # Submit to Perforce.
            LOGGER.info("Submitting the created packages to Perforce.")
            submit_message = "Packages generated from CL {}.".format(code_changelist)
            submit_message += "\nJenkins URL: " + os.environ.get("BUILD_URL", "None")
            perforce.submit(message=submit_message)
        else:
            LOGGER.info("Running with --dry-run, not submitting the result to Perforce.")
    finally:
        # Clean up the Perforce environment.
        perforce.revert(quiet=True)
