{#
    Command:
        nant
            short_help: Run nant using the fbcli subsystem

    Arguments:

    Required variables:
        package
            help: Name of package in masterconfig or a path to a .build file
            required: True
            type: str
        platforms
            help: Name of the platform(s)
            multiple: True
            required: True
            type: str
        targets
            help: Nant target(s)
            multiple: True
            required: True
            type: str

    Optional variables:
        vsver
            help: Visual Studio version to generate solutions for
            default: None
            type: str
        propertiesfile
            help: XML file containing additional arguments to framework
            default: None
            type: str
        framework_args
            help: Additional arguments to framework
            multiple: True
            default: None
            type: str
        outsourcer
            help: Enables outsourcing configurations
            type: bool
            default: False
        outsourcer_non_proxy_sdks
            help: Enables outsourcing configurations by using non-proxy SDKs
            type: bool
            default: False
        fwdwarn
            help: Forwarding headers will emit a warning
            type: bool
            default: False
        fwderror
            help: Forwarding headers will emit an error
            type: bool
            default: False
        licensee_agnostic
            help: Do not add licensee info to nant.exe's command line
            type: bool
            default: False
        ignoredeprecation
            help: Frostbite deprecations are ignored and will not fail the build
            type: bool
            default: False
        enabledeprecation
            help: Frostbite deprecations are enabled and will fail the build
            type: bool
            default: False
        enableexpiredapierror
            help: Frostbite APIs that are expired will fail the build
            type: bool
            default: False
        validate_package_access
            help: Validates all required packages as accessible
            type: bool
            default: False
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    nant
    --package {{ package }}
    --platforms {{ platforms }}
    --targets {{ targets }}
    {%- if vsver %}
    --vsver {{ vsver }}
    {%- endif %}
    {%- if propertiesfile %}
    --propertiesfile {{ propertiesfile }}
    {%- endif %}
    {%- if framework_args %}
    --framework_args {{ framework_args }}
    {%- endif %}
    {%- if outsourcer %}
    --outsourcer {{ outsourcer }}
    {%- endif %}
    {%- if outsourcer_non_proxy_sdks %}
    --outsourcer_non_proxy_sdks {{ outsourcer_non_proxy_sdks }}
    {%- endif %}
    {%- if fwdwarn %}
    --fwdwarn {{ fwdwarn }}
    {%- endif %}
    {%- if fwderror %}
    --fwderror {{ fwderror }}
    {%- endif %}
    {%- if licensee_agnostic %}
    --licensee_agnostic {{ licensee_agnostic }}
    {%- endif %}
    {%- if ignoredeprecation %}
    --ignoredeprecation {{ ignoredeprecation }}
    {%- endif %}
    {%- if enabledeprecation %}
    --enabledeprecation {{ enabledeprecation }}
    {%- endif %}
    {%- if enableexpiredapierror %}
    --enableexpiredapierror {{ enableexpiredapierror }}
    {%- endif %}
    {%- if validate_package_access %}
    --validate_package_access {{ validate_package_access }}
    {%- endif %}
  displayName: elipy nant
