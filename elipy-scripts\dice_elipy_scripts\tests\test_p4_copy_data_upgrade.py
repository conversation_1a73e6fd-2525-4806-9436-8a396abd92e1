"""
test_p4_copy_data_upgrade.py

Unit testing for p4_copy_data_upgrade
"""
import unittest

from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import patch, MagicMock
from dice_elipy_scripts.p4_copy_data_upgrade import cli


@patch("dice_elipy_scripts.p4_copy_data_upgrade.add_sentry_tags", MagicMock())
@patch("elipy2.core.ensure_p4_config", MagicMock())
@patch("elipy2.running_processes.kill", MagicMock())
class TestP4CopyDataUpgradeCli(unittest.TestCase):
    OPTION_CLEAN = "--clean"
    OPTION_COPY_MAPPING = "--copy-mapping"
    OPTION_DATA_DIRECTORY = "--data-directory"
    OPTION_EXCLUDE_PATH = "--exclude-path"
    OPTION_NO_SUBMIT = "--no-submit"
    OPTION_LICENSEE = "--licensee"
    OPTION_P4_CLIENT = "--p4-client"
    OPTION_P4_PORT = "--p4-port"
    OPTION_P4_USER = "--p4-user"
    OPTION_SOURCE_BRANCH = "--source-branch"
    OPTION_SOURCE_CHANGELIST = "--source-changelist"
    OPTION_SUBMIT_MESSAGE = "--submit-message"
    OPTION_TARGET_CHANGELIST = "--target-changelist"

    VALUE_CLEAN = "true"
    VALUE_COPY_MAPPING = "copy_mapping"
    VALUE_DATA_DIRECTORY = "data_directory"
    VALUE_EXCLUDE_PATH_1 = "exclude_path_1"
    VALUE_EXCLUDE_PATH_2 = "exclude_path_2"
    VALUE_LICENSEE = "licensee_1"
    VALUE_P4_CLIENT = "p4_client"
    VALUE_P4_PORT = "p4_port"
    VALUE_P4_USER = "p4_user"
    VALUE_SOURCE_BRANCH = "source_branch"
    VALUE_SOURCE_CHANGELIST = "1234"
    VALUE_SUBMIT_MESSAGE = "submit_message"
    VALUE_TARGET_CHANGELIST = "5678"

    BASIC_ARGS = [
        OPTION_COPY_MAPPING,
        VALUE_COPY_MAPPING,
        OPTION_P4_CLIENT,
        VALUE_P4_CLIENT,
        OPTION_P4_PORT,
        VALUE_P4_PORT,
        OPTION_SOURCE_BRANCH,
        VALUE_SOURCE_BRANCH,
        OPTION_SOURCE_CHANGELIST,
        VALUE_SOURCE_CHANGELIST,
        OPTION_TARGET_CHANGELIST,
        VALUE_TARGET_CHANGELIST,
    ]

    def setUp(self):
        self.patcher_codeutils = patch("elipy2.code.CodeUtils")
        self.mock_codeutils = self.patcher_codeutils.start()
        self.mock_codeutils.return_value = MagicMock()

        self.patcher_p4utils = patch("elipy2.p4.P4Utils")
        self.mock_p4utils = self.patcher_p4utils.start()
        self.mock_p4utils.return_value = MagicMock()

        self.patcher_core_run = patch("elipy2.core.run")
        self.mock_core_run = self.patcher_core_run.start()

        self.patcher_set_datadir = patch("elipy2.data.DataUtils.set_datadir")
        self.mock_set_datadir = self.patcher_set_datadir.start()

        self.patcher_get_game_data_dir = patch("elipy2.frostbite_core.get_game_data_dir")
        self.mock_get_game_data_dir = self.patcher_get_game_data_dir.start()
        self.mock_get_game_data_dir.return_value = "//perforce/path"

        self.patcher_get_game_root = patch("elipy2.frostbite_core.get_game_root")
        self.mock_get_game_root = self.patcher_get_game_root.start()
        self.mock_get_game_root.return_value = "game_root"

        self.patcher_get_tnt_root = patch("elipy2.frostbite_core.get_tnt_root")
        self.mock_get_tnt_root = self.patcher_get_tnt_root.start()
        self.mock_get_tnt_root.return_value = "tnt_root"

        self.patcher_compile_code = patch("dice_elipy_scripts.p4_copy_data_upgrade.compile_code")
        self.mock_compile_code = self.patcher_compile_code.start()

        self.patcher_submit_integration = patch(
            "dice_elipy_scripts.p4_copy_data_upgrade.submit_integration"
        )
        self.mock_submit_integration = self.patcher_submit_integration.start()

    def tearDown(self):
        patch.stopall()

    def test_basic_args(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0

    def test_set_datadir(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_DATA_DIRECTORY, self.VALUE_DATA_DIRECTORY]
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_set_datadir.assert_called_once_with(self.VALUE_DATA_DIRECTORY)

    def test_no_exclude(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        assert self.mock_p4utils.return_value.revert.call_count == 2

    def test_exclude_path(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_EXCLUDE_PATH, self.VALUE_EXCLUDE_PATH_1]
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        assert self.mock_p4utils.return_value.revert.call_count == 3

    def test_exclude_path_multiple(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_EXCLUDE_PATH,
                self.VALUE_EXCLUDE_PATH_1,
                self.OPTION_EXCLUDE_PATH,
                self.VALUE_EXCLUDE_PATH_2,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        assert self.mock_p4utils.return_value.revert.call_count == 4

    def test_compile_code(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_compile_code.assert_called_once_with(
            licensee=[],
            password=None,
            email=None,
            domain_user=None,
            port=self.VALUE_P4_PORT,
            user=None,
            client=self.VALUE_P4_CLIENT,
            overwrite_p4config=False,
            clean=False,
        )

    def test_compile_code_clean(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_CLEAN, self.VALUE_CLEAN])
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_compile_code.assert_called_once_with(
            licensee=[],
            password=None,
            email=None,
            domain_user=None,
            port=self.VALUE_P4_PORT,
            user=None,
            client=self.VALUE_P4_CLIENT,
            overwrite_p4config=False,
            clean=True,
        )

    def test_compile_code_licensee(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_LICENSEE, self.VALUE_LICENSEE])
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_compile_code.assert_called_once_with(
            licensee=[self.VALUE_LICENSEE],
            password=None,
            email=None,
            domain_user=None,
            port=self.VALUE_P4_PORT,
            user=None,
            client=self.VALUE_P4_CLIENT,
            overwrite_p4config=False,
            clean=False,
        )

    def test_upgrade_exception(self):
        self.mock_core_run.side_effect = Exception()
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        self.mock_p4utils.return_value.clean.assert_called_once_with(folder="//perforce/path/...")

    def test_submit(self):
        submit_message = (
            f"Copied code from source_branch at CL#{self.VALUE_SOURCE_CHANGELIST}, "
            f"using branch mapping copy_mapping. "
            f"Used this to upgrade data at CL#{self.VALUE_TARGET_CHANGELIST}."
        )
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_submit_integration.assert_called_once_with(
            p4_object=self.mock_p4utils.return_value,
            submit_message=submit_message,
            submit=True,
            data_upgrade=True,
        )

    def test_submit_extra_message(self):
        submit_message = (
            f"Copied code from source_branch at CL#{self.VALUE_SOURCE_CHANGELIST}, "
            f"using branch mapping {self.VALUE_COPY_MAPPING}. "
            f"Used this to upgrade data at CL#{self.VALUE_TARGET_CHANGELIST}."
        )
        submit_message += f"\n{self.VALUE_SUBMIT_MESSAGE}"
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_SUBMIT_MESSAGE, self.VALUE_SUBMIT_MESSAGE]
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_submit_integration.assert_called_once_with(
            p4_object=self.mock_p4utils.return_value,
            submit_message=submit_message,
            submit=True,
            data_upgrade=True,
        )

    def test_no_submit(self):
        submit_message = (
            f"Copied code from source_branch at CL#{self.VALUE_SOURCE_CHANGELIST}, "
            f"using branch mapping copy_mapping. "
            f"Used this to upgrade data at CL#{self.VALUE_TARGET_CHANGELIST}."
        )
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_NO_SUBMIT])
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_submit_integration.assert_called_once_with(
            p4_object=self.mock_p4utils.return_value,
            submit_message=submit_message,
            submit=False,
            data_upgrade=True,
        )

    def test_copy_mapping(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        self.mock_p4utils.return_value.copy_mapping.assert_called_once_with(
            mapping=self.VALUE_COPY_MAPPING,
            reverse=False,
            stream=False,
            force=False,
            to_revision=self.VALUE_SOURCE_CHANGELIST,
        )
