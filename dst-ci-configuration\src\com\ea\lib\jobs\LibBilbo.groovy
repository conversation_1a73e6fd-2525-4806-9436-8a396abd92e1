package com.ea.lib.jobs

import com.ea.lib.LibJobDsl
import com.ea.lib.jobsettings.BilboSettings

class LibBilbo {
    /**
     * Adds generic job parameters for Bilbo/Drone jobs.
     */
    static void bilbo_drone_job(def job, def project, def branchFile, def masterFile, String branchName) {
        job.with {
            BilboSettings bilboSettings = new BilboSettings()
            bilboSettings.initializeBilboDroneJob(branchFile, masterFile, project, branchName)

            description(bilboSettings.description)
            disabled(bilboSettings.isDisabled)
            label(bilboSettings.jobLabel)
            logRotator(-1, 50)
            quietPeriod(0)
            customWorkspace(bilboSettings.workspaceRoot)
            concurrentBuild()
            properties {
                throttleConcurrentBuilds {
                    maxTotal(4)
                }
                disableResume()
            }
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to sync')
                    trim(true)
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(bilboSettings.buildName)
                timeout {
                    absolute(120)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, bilboSettings.elipyInstallCall, project)
                batchFile(bilboSettings.elipyCmd)
            }
        }
    }

    /**
     * Adds generic job parameters for registering Bilbo in multiple locations and move it to multiple locations.
     * This version runs the move to the new location using separate jobs per platform/config, running in parallel.
     */
    static void bilbo_drone_job_move_location_parallel_dronebuild_start(def job, def project, def branchFile, def masterFile, String branchName) {
        BilboSettings bilboSettings = new BilboSettings()
        bilboSettings.initializeBilboDroneJobMoveLocationParallelDroneBuildStart(branchFile, masterFile, project, branchName)
        job.with {
            description(bilboSettings.description)
            environmentVariables {
                env('branch_name', bilboSettings.branchName)
                env('project_name', bilboSettings.projectName)
                env('retry_limit', bilboSettings.retryLimit)
            }
            logRotator(7, 50)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to register.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to register.')
                    trim(true)
                }
            }
            properties {
                throttleConcurrentBuilds {
                    maxTotal(5)
                }
                disableResume()
            }
            quietPeriod(0)
        }
    }

    /**
     * Adds generic job parameters for registering Bilbo in two locations and move it to a new location.
     * This version runs the move to the new location using separate jobs per platform/config, running in parallel.
     */
    static void bilbo_drone_job_move_location_parallel_start(def job, def project, def branchFile, def masterFile, String branchName, String location) {
        BilboSettings bilboSettings = new BilboSettings()
        bilboSettings.initializeBilboDroneJobMoveLocationParallelLocationStart(branchFile, masterFile, project, branchName, location)
        job.with {
            description(bilboSettings.description)
            environmentVariables {
                env('branch_name', bilboSettings.branchName)
                env('project_name', bilboSettings.projectName)
                env('retry_limit', bilboSettings.retryLimit)
            }
            logRotator(7, 50)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to register.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to register.')
                    trim(true)
                }
                stringParam {
                    name('location')
                    defaultValue(location)
                    description('Specifies location to register.')
                    trim(true)
                }
            }
            properties {
                throttleConcurrentBuilds {
                    maxTotal(3)
                }
                disableResume()
            }
            quietPeriod(0)
        }
    }

    /**
     * Adds generic job parameters for a job that copies code to a new location, as part of the 'Bilbo move location' job.
     * Each instance of the job copies one platform/config combination, to speed up the main job by running multiple combinations in parallel.
     */
    static void bilbo_drone_job_move_location_parallel_copy(def job, def project, def branchFile, def masterFile, String branchName,
                                                            String platform, String config, String location) {
        BilboSettings bilboSettings = new BilboSettings()
        bilboSettings.initializeBilboDroneJobMoveLocationParallelCopy(branchFile, masterFile, project, branchName, platform, config, location)
        bilbo_drone_job_move_location_parallel(job, project, bilboSettings)
    }

    /**
     * Adds generic job parameters for registering bilbo in a remote location.
     */
    static void bilbo_drone_job_move_location_parallel_remote(def job, def project, def branchFile, def masterFile, String branchName, String location) {
        BilboSettings bilboSettings = new BilboSettings()
        bilboSettings.initializeBilboDroneJobMoveLocationParallelRemote(branchFile, masterFile, project, branchName, location)
        bilbo_drone_job_move_location_parallel(job, project, bilboSettings)
    }

    /**
     * Adds generic job parameters for Bilbo jobs. description_string and elipy_command are specified above.
     */
    static void bilbo_drone_job_move_location_parallel(def job, def project, BilboSettings bilboSettings) {
        bilboSettings.initializeBilboDroneJobMoveLocationParallel()

        job.with {
            description(bilboSettings.description)
            label(bilboSettings.jobLabel)
            logRotator(7, 50)
            quietPeriod(0)
            customWorkspace(bilboSettings.workspaceRoot)
            concurrentBuild()
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to use.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to use.')
                    trim(true)
                }
            }
            wrappers {
                throttleConcurrentBuilds {
                    maxTotal(4)
                }
                colorizeOutput()
                timestamps()
                buildName(bilboSettings.buildName)
                timeout {
                    absolute(180)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, bilboSettings.elipyInstallCall, project)
                batchFile(bilboSettings.elipyCmd)
            }
        }
    }

    /**
     * Adds generic job parameters for registering bilbo in two locations and move it to a new location.
     */
    static void bilbo_frosty_job_move_location(def job, def project, def branchFile, def masterFile, def variant, String branchName, String platformName, String location) {
        job.with {
            BilboSettings bilboSettings = new BilboSettings()
            bilboSettings.initializeBilboFrostyJobMoveLocation(branchFile, masterFile, project, branchName, variant, platformName, location)

            description(bilboSettings.description)
            label(bilboSettings.jobLabel)
            logRotator(7, 50)
            quietPeriod(0)
            customWorkspace(bilboSettings.workspaceRoot)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to sync')
                    trim(true)
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(bilboSettings.buildName)
                timeout {
                    absolute(180)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, bilboSettings.elipyInstallCall, project)
            }
            steps {
                batchFile(bilboSettings.elipyCmd)
            }
            steps {
                batchFile(bilboSettings.elipyCmdNewLocation)
            }
        }
    }

    /**
     * Setup a bilbo bundles job_move_location job.
     * @param job The job to setup.
     * @param project The project settings object.
     * @param branchFile The branch file with job settings.
     * @param masterSettings The master settings file with job settings.
     * @param branchName The name of the branch.
     * @param platform The platform name.
     * @param bundleType The type of bundle (bundles or combine_bundles).
     * @param location The target location for the move.
     */
    static void setup_bilbo_bundles_job_move_location(def job, def project, def branchFile, def masterSettings,
                                              String branchName, String platform, String bundleType, String location) {
        BilboSettings settings = new BilboSettings()
        settings.initializeBilboBundlesJobMoveLocation(branchFile, masterSettings, project, branchName, platform, bundleType, location)

        job.with {
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to sync')
                    trim(true)
                }
            }

            disabled(settings.isDisabled)
            label(settings.jobLabel)
            description(settings.description)
            deliveryPipelineConfiguration('Bilbo', 'Move Location Bundles')

            logRotator {
                numToKeep(100)
            }

            environmentVariables {
                keepBuildVariables(true)
                keepSystemVariables(true)
                loadFilesFromMaster(false)
                env('location', location)
                env('dataset', settings.dataset)
                env('platform', platform)
                env('bundle_type', bundleType)
            }

            properties {
                if (settings.retryLimit > 1) {
                    rebuilderSettings {
                        autoRebuild(false)
                        rebuildDisabled(false)
                        maxWatchedBuilds(3)
                        rebuildIfDestabilized(false)
                        enableUserCollisionDetection(false)
                        rebuildLimit(settings.retryLimit)
                        cleanupOnly(false)
                    }
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
            }
            steps {
                batchFile(settings.elipyCmd)
            }
            steps {
                batchFile(settings.elipyCmdNewLocation)
            }
        }
    }

    /**
     * Adds generic job parameters for registering bundle jobs in a new location.
     */
    static void bilbo_bundles_job_move_location(def job, def project, def branchFile, def masterFile, String branchName,
                                                String platform, String bundleType, String location) {
        BilboSettings bilboSettings = new BilboSettings()
        bilboSettings.initializeBilboBundlesJobMoveLocation(branchFile, masterFile, project, branchName, platform, bundleType, location)

        // Set up generic parameters
        job.with {
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to use.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to use.')
                    trim(true)
                }
            }
            wrappers {
                throttleConcurrentBuilds {
                    maxTotal(4)
                }
                colorizeOutput()
                timestamps()
                buildName(bilboSettings.buildName)
                timeout {
                    absolute(180)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, bilboSettings.elipyInstallCall, project)
                batchFile(bilboSettings.elipyCmd)
            }
        }
    }

    /**
     * Adds generic job parameters for registering a job as smoked in bilbo
     */
    static void bilbo_register_smoke(def job, def project, def branchFile, def masterFile, String branchName) {
        job.with {
            BilboSettings bilboSettings = new BilboSettings()
            bilboSettings.initializeBilboRegisterSmoke(branchFile, masterFile, project, branchName)

            description('Registers a smoked build in bilbo.')
            label(bilboSettings.jobLabel)
            logRotator(7, 50)
            quietPeriod(0)
            customWorkspace(bilboSettings.workspaceRoot)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('not_available')
                    description('Specifies data changelist to sync')
                    trim(true)
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(bilboSettings.buildName)
                timeout {
                    absolute(120)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, bilboSettings.elipyInstallCall, project)
                batchFile(bilboSettings.elipyCmd)
            }
            if (bilboSettings.smokeDownstreamJob) {
                steps {
                    batchFile("echo \'Downstream projects triggered by job: \'")
                    for (downstreamJobName in bilboSettings.smokeDownstreamJob.split(',')) {
                        batchFile("echo $downstreamJobName")
                    }
                }
                publishers {
                    for (downstreamJobName in bilboSettings.smokeDownstreamJob.split(',')) {
                        downstreamParameterized {
                            // Trigger this to run using the same changelists
                            trigger(downstreamJobName) {
                                parameters {
                                    currentBuild()
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Adds generic job parameters for registering a build as verified_for_preflight in bilbo
     */
    static void bilbo_register_verified_for_preflight(def job, def project, def branchFile, def masterFile, String branchName, String changelistParamName = 'changelist') {
        job.with {
            BilboSettings bilboSettings = new BilboSettings()
            bilboSettings.initializeBilboRegisterVerifiedForPreflight(branchFile, masterFile, project, branchName, changelistParamName)

            description('Registers a build as verified_for_preflight in Bilbo.')
            label(bilboSettings.jobLabel)
            logRotator(7, 50)
            quietPeriod(0)
            customWorkspace(bilboSettings.workspaceRoot)
            parameters {
                stringParam {
                    name(changelistParamName)
                    defaultValue('')
                    description('Specifies changelist to register')
                    trim(true)
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(bilboSettings.buildName)
                timeout {
                    absolute(120)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, bilboSettings.elipyInstallCall, project)
                batchFile(bilboSettings.elipyCmd)
            }
        }
    }

    /**
     * Adds generic job parameters for Bilbo/Drone jobs, adapted for autotests.
     */
    static void bilbo_drone_autotest_job(def job, def project, def branchFile, def masterFile, String branchName) {
        job.with {
            BilboSettings bilboSettings = new BilboSettings()
            bilboSettings.initializeBilboAutotest(branchFile, masterFile, project, branchName)

            description(bilboSettings.description)
            label(bilboSettings.jobLabel)
            concurrentBuild()
            logRotator(7, 50)
            quietPeriod(0)
            customWorkspace(bilboSettings.workspaceRoot)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Code changelist')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Data changelist')
                    trim(true)
                }
                stringParam {
                    name('test_definition')
                    defaultValue('')
                    description('test_definition')
                    trim(true)
                }
                booleanParam {
                    name('run_bilbo')
                    defaultValue(false)
                    description('Should this job register in Bilbo')
                }
                booleanParam {
                    name('register_smoke')
                    defaultValue(false)
                    description('Should mark the build as smoked')
                }
                choiceParam('status', ['done', 'failed', 'inprogress'], 'Job status')
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(bilboSettings.buildName)
                timeout {
                    absolute(120)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, bilboSettings.elipyInstallCall, project)
                batchFile(bilboSettings.elipyCmd)
            }
        }
    }
    /**
     * Adds generic job parameters for registering a build as a release candidate not to be deleted in bilbo.
     */
    static void bilbo_register_release_candidate_job(def job, def project, def branchFile, def masterFile, String branchName) {
        job.with {
            BilboSettings bilboSettings = new BilboSettings()
            bilboSettings.initializeBilboRegisterReleaseCandidate(branchFile, masterFile, project, branchName)

            description('Job for registering a release candidate build in bilbo.')
            label(bilboSettings.jobLabel)
            logRotator(7, 50)
            quietPeriod(0)
            customWorkspace(bilboSettings.workspaceRoot)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist for the build in question.')
                    trim(true)
                }
                stringParam {
                    name('code_branch')
                    defaultValue('')
                    description('Specifies code branch for the build in question.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist for the build in question.')
                    trim(true)
                }
                stringParam {
                    name('data_branch')
                    defaultValue('')
                    description('Specifies data branch for the build in question.')
                    trim(true)
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(bilboSettings.buildName)
                timeout {
                    absolute(120)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, bilboSettings.elipyInstallCall, project)
                batchFile(bilboSettings.elipyCmd)
            }
        }
    }
}
