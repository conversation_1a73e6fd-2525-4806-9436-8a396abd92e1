package scripts.schedulers.data

import com.ea.lib.LibCommonCps
import com.ea.lib.LibJenkins
import com.ea.project.GetBranchFile

def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
def project = ProjectClass(env.project_name)

/**
 * data_start.groovy
 */
pipeline {
    agent { label '(scheduler && master) || scheduler || executor_agent' }
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Get changelist from Perforce') {
            steps {
                script {
                    def settings_map = branchfile.general_settings + branchfile.standard_jobs_settings
                    P4PreviewData(project, 'stream', env.data_folder, env.data_branch, env.non_virtual_data_folder, env.non_virtual_data_branch, settings_map)
                }
            }
        }
        stage('Trigger data jobs') {
            steps {
                script {
                    def last_good_code = LibJenkins.getLastStableCodeChangelist(env.data_reference_job)
                    def code_changelist = params.code_changelist ?: last_good_code
                    def data_changelist = params.data_changelist ?: env.P4_CHANGELIST
                    if (env.frostbite_syncer_setup.toBoolean() == true) {
                        data_changelist = params.data_changelist ?: code_changelist
                    }

                    def clean_data = (
                        LibCommonCps.isRecentCleanBuildByTime(currentBuild, 1) == false &&
                            env.enable_daily_data_clean.toBoolean() == true
                    ) ? 'True' : params.clean_data

                    if (code_changelist == null) {
                        echo 'Missing code changelist, aborting build!'
                        currentBuild.result = Result.FAILURE.toString()
                        return
                    }

                    def args = [
                        string(name: 'code_changelist', value: code_changelist),
                        string(name: 'data_changelist', value: data_changelist),
                        string(name: 'clean_data', value: clean_data),
                    ]
                    def jobs = [:]

                    def inject_map = [
                        'code_changelist': code_changelist,
                        'data_changelist': data_changelist,
                    ]
                    EnvInject(currentBuild, inject_map)
                    currentBuild.displayName = env.JOB_NAME + '.' + data_changelist + '.' + code_changelist

                    def data_matrix = branchfile.data_matrix
                    if (env.webexport_branch.toBoolean() == true) {
                        data_matrix += [
                            name         : 'webexport.win64',
                            allow_failure: env.webexport_allow_failure.toBoolean(),
                        ]
                    }

                    def final_result = Result.SUCCESS
                    def continue_build = true
                    for (def run = 0; run <= env.retry_limit.toInteger(); run++) { // Retry failed jobs if retry_limit > 0.
                        jobs = [:]
                        final_result = Result.SUCCESS
                        for (platform in data_matrix) {
                            Boolean allow_failure = false
                            def platform_name = platform
                            def custom_tag = null
                            if (platform instanceof Map) {
                                allow_failure = platform.allow_failure ?: false
                                platform_name = platform.name
                                custom_tag = platform?.custom_tag
                            }

                            def job_name = env.branch_name + '.' + env.dataset + '.' + platform_name
                            if (custom_tag != null) {
                                job_name += ".${custom_tag}"
                            }

                            if (NeedsRebuildData(job_name, code_changelist, data_changelist)) {
                                if (run > 0 && IsGameFailure(job_name, allow_failure)) {
                                    if (allow_failure == false) {
                                        final_result = Result.FAILURE
                                        // Set pipeline as failed if there are jobs from IsGameFailure category.
                                        continue_build = false
                                    }
                                    break
                                } else {
                                    jobs[job_name] = {
                                        def downstream_job = build(job: job_name, parameters: args, propagate: false)
                                        if (allow_failure == false) {
                                            final_result = final_result.combine(Result.fromString(downstream_job.result))
                                        }
                                        LibJenkins.printFailureMessage(this, downstream_job, allow_failure)
                                        LibJenkins.printRunningJobs(this)
                                    }
                                }
                            }
                        }
                        if (continue_build == false) {
                            break
                        }
                        parallel(jobs)
                        if (final_result == Result.SUCCESS) {
                            break
                        }
                    }

                    if (final_result == Result.SUCCESS && env.dry_run_data.toBoolean() == false) { // Only upload successful builds.
                        build(job: env.branch_name + '.bilbo.register-' + env.dataset + '-dronebuild', parameters: args, propagate: false, wait: false)
                    }
                    currentBuild.result = final_result.toString()

                    if (currentBuild.result.toString() == 'SUCCESS') { // Only trigger after successful builds.
                        def data_downstream_matrix = []
                        if (branchfile.standard_jobs_settings.deployment_data_branch == true) {
                            def downstream_matrix = branchfile.data_downstream_matrix
                            for (downstreamJob in downstream_matrix) {
                                def downstreamJobName = downstreamJob
                                if (downstreamJob instanceof Map) {
                                    downstreamJobName = downstreamJob.name ?: downstreamJobName
                                    if (!downstreamJobName.contains('.frosty.start')) {
                                        data_downstream_matrix.add(downstreamJob)
                                    }
                                }
                            }
                        } else {
                            data_downstream_matrix = branchfile.data_downstream_matrix
                        }
                        LibCommonCps.triggerDownstreamJobs(this, data_downstream_matrix, 'data', env.branch_name, branchfile, code_changelist, data_changelist)
                    }

                    def slack_settings = branchfile.standard_jobs_settings?.slack_channel_data
                    SlackMessageNew(currentBuild, slack_settings, project.short_name)

                    // Set this build as 'cleaned', if necessary
                    SetOptionalCleanBuild(currentBuild, clean_data)

                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
        stage('Scan for errors') { steps { ScanForErrors(currentBuild, env.slack_notify_bot_data) } }
    }
    post { failure { SlackNotifyBot(currentBuild, env.slack_notify_bot_data) } }
}
