# Duplicated settings lower in the settings list will get higher priority
buildtype: "QA"
milestone: "Production"
distribution_type: "ExternalReview"
retention_policy: "SpaceAvailable"
priority: ""
version: "2.0"

linuxserver:
  content:
    digital:
      file_names:
        - "*_Server_*_Binaries.zip"
      supplemental_files:
        - "build.json"
      directory:
        - ""
  settings:
    gnt-proto:
      ww:
        final:
          digital:
            sku_id: "f938f384-331a-43cf-ad2d-9c139c6092fa"
            sku_name: "Server - WW (gnt-proto LinuxServer Digital Final)"

ps5:
  content:
    files:
      file_names:
        - "builtLevels.json"
        - "build.json"
        - "*.prx"
        - "*.buildlayout"
        - "*.BuildSettings"
        - "eboot.bin"
      supplemental_files:
        - "*.xml"
      directory:
        - "Config"
        - "data"
        - "Scripts"
        - "sce_sys"
  settings:
    gnt-proto:
      ww:
        final:
          files:
            sku_id: "ee4354f7-5029-4809-8775-49b5275f9978"
            sku_name: "FG - WW (gnt-proto ps5 LooseFiles Final)"

server:
  content:
    digital:
      file_names:
        - "*_Server_*_Binaries.zip"
      supplemental_files:
        - "build.json"
      directory:
        - ""
  settings:
    gnt-proto:
      ww:
        final:
          digital:
            sku_id: "090913f3-b7b3-4eed-9476-58c104335141"
            sku_name: "FG - WW (gnt-proto Server Digital Final)"

win64:
  content:
    files:
      file_names:
        - "builtLevels.json"
        - "build.json"
        - "*.BuildSettings"
        - "*.buildlayout"
        - "*.Main_Win64_*.exe"
        - "*.dll"
      supplemental_files:
        - "*.xml"
      directory:
        - "Config"
        - "Data"
        - "Scripts"
  settings:
    gnt-proto:
      ww:
        final:
          files:
            sku_id: "2d699a4b-62f0-4dbf-b45d-e3151e74c2d0"
            sku_name: "FG - WW (gnt-proto Win64 LooseFiles Final)"

xbsx:
  content:
    files:
      file_names:
        - "builtLevels.json"
        - "*.buildlayout"
        - "*.BuildSettings"
        - "Logo.png"
        - "*.dll"
        - "SmallLogo.png"
        - "SplashScreen.png"
        - "StoreLogo.png"
        - "WideLogo.png"
        - "*.Main_Xbsx_*.exe"
        - "MicrosoftGame.config"
        - "gameos.xvd"
        - "nsal.json"
        - "package.mft"
        - "resources.pri"
        - "build.json"
      supplemental_files:
        - "*.xml"
      directory:
        - "Config"
        - "Data"
        - "Scripts"
  settings:
    gnt-proto:
      ww:
        final:
          files:
            sku_id: "8b8f9e08-fff7-4320-994a-816b84562214"
            sku_name: "FG - WW (gnt-proto XBSX LooseFiles Final)"
