package scripts.schedulers

import com.ea.project.GetMasterFile
import com.ea.project.GetBranchFile

def project = GetMasterFile.get_masterfile(currentBuild.absoluteUrl)[0].project
def branchFile = GetBranchFile.get_branchfile(project.name, env.branch_name)

/**
 * UploadToSteam.groovy
 */
pipeline {
    agent {
        node {
            customWorkspace(project.workspace_root)
            label(env.node_label)
        }
    }
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Set Display Name') {
            steps {
                SetDisplayName(currentBuild, [params.data_changelist, params.code_changelist])
            }
        }
        stage('Upload to Steam') {
            steps {
                InjectVaultSecrets(branchFile, project) {
                    bat(env.elipy_install_call)
                }
                withCredentials([string(credentialsId: project.vault_credentials, 'variable': project.vault_variable)]) {
                    bat([env.elipy_call, 'upload_to_steam',
                         '--code-branch', env.code_branch,
                         '--code-changelist', params.code_changelist,
                         '--data-branch', env.data_branch,
                         '--data-changelist', params.data_changelist ?: params.code_changelist,
                         '--platform', env.platform,
                         '--config', env.config,
                         '--region', env.region,
                         env.combine_args,
                    ].join(' '))
                }
            }
        }
    }
}
