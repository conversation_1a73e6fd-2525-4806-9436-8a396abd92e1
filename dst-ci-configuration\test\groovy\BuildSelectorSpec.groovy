import com.ea.exceptions.CobraException
import com.ea.lib.BuildSelector
import com.ea.lib.LibJenkins
import com.ea.lib.jobs.LibAutotestModelBuilder
import com.ea.lib.model.JobReference
import com.ea.lib.model.autotest.AutotestCategory
import com.ea.lib.model.autotest.Name
import com.ea.lib.model.autotest.Platform
import com.ea.lib.model.autotest.Region
import com.ea.lib.model.autotest.TestInfo
import com.ea.matrixfiles.AutotestMatrix
import hudson.model.Result
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll
import support.Steps

class BuildSelectorSpec extends Specification {
    @Shared String expectedDataChangelist = '1337'
    @Shared String expectedCodeChangelist = '13371337'
    @Shared String expectedClientBuildId = '\\\\client\\build\\id'
    @Shared String expectedServerBuildId = '\\\\server\\build\\id'
    @Shared Map looseFilesChangelists = [
        (Name.PS5.toString() + '-' + Region.EU.toString())  : [
            dataChangelist: '567',
            codeChangelist: '987',
            clientBuildId : '\\\\client\\build\\id',
            serverBuildId : '\\\\server\\build\\id',
        ],
        (Name.WIN64.toString() + '-' + Region.WW.toString()): [
            dataChangelist: '567',
            codeChangelist: '987',
            clientBuildId : '\\\\client\\build\\id',
            serverBuildId : '\\\\server\\build\\id',
        ],
        (Name.XBSX.toString() + '-' + Region.WW.toString()) : [
            dataChangelist: '567',
            codeChangelist: '987',
            clientBuildId : '\\\\client\\build\\id',
            serverBuildId : '\\\\server\\build\\id',
        ],
    ]
    @Shared Map droneChangelists = [
        (Name.ANY.toString()): [
            dataChangelist: '123',
            codeChangelist: '234',
            clientBuildId : '\\\\client\\build\\id',
            serverBuildId : '\\\\server\\build\\id',
        ]
    ]
    @Shared Map failedChangelists = [
        (Name.PS5.toString() + '-' + Region.EU.toString()): [
            dataChangelist: '567',
            codeChangelist: '987',
            clientBuildId : '\\\\client\\build\\id',
            serverBuildId : '\\\\server\\build\\id',
        ],
    ]
    @Shared Map looseFilesExpectedInjectMap = [target_build_info: looseFilesChangelists.inspect()]
    @Shared Map droneExpectedInjectMap = [target_build_info: droneChangelists.inspect()]
    @Shared Map failedExpectedInjectMap = [target_build_info: failedChangelists.inspect()]
    Steps steps
    Map currentBuild = [
        displayName: null,
        rawBuild   : [
            executor: [
                interrupt: { Result result -> throw new CobraException(result.toString()) }
            ]
        ],
        result     : null,
    ]
    Map env = [
        JOB_NAME    : 'job-name',
        testCategory: 'a-category',
        branchName  : 'a-branch',
    ]
    Map params = [:]
    @Shared Map nullResultsMap = [xbsx: [
        dataChangelist: null,
        codeChangelist: null,
        clientBuildId : null,
        serverBuildId : null,
    ]]
    @Shared Map nullResult = null
    @Shared Map foundResultsMap = [xbsx: [
        dataChangelist: '123',
        codeChangelist: '123',
        clientBuildId : '123',
        serverBuildId : '123',
    ]]
    @Shared Map mixedResults = [
        ps5 : [
            dataChangelist: null,
            codeChangelist: null,
            clientBuildId : null,
            serverBuildId : null,
        ],
        xbsx: [
            dataChangelist: '123',
            codeChangelist: '123',
            clientBuildId : '123',
            serverBuildId : '123',
        ],
    ]

    void setupSpec() {
        Steps.metaClass.retryOnFailureCause = { int retryCount, List<JobReference> jobReferences, boolean allowFailure, Closure script ->
            script.call()
            return Result.SUCCESS
        }
        Steps.metaClass.parallel = {}
        Steps.metaClass.echo = {}
        Steps.metaClass.EnvInject = { Map map, Map map2 -> void }
        Steps.metaClass.booleanParam = { Map args -> return args }
        Steps.metaClass.string = { Map args -> return args }
        Steps.metaClass.build = {
            return [
                buildVariables: [
                    data_changelist: expectedDataChangelist,
                    code_changelist: expectedCodeChangelist,
                    client_build_id: expectedClientBuildId,
                    server_build_id: expectedServerBuildId,
                ]
            ]
        }
    }

    void setup() {
        GroovySpy(Steps, global: true)
        steps = Mock(Steps)
        GroovySpy(LibAutotestModelBuilder, global: true)
        GroovySpy(LibJenkins, global: true)
        LibJenkins.printRunningJobs(_) >> null
        LibAutotestModelBuilder.composeBuildSelectorPlatforms(*_) >> [
            new Platform(name: Name.PS5, region: Region.EU),
            new Platform(name: Name.WIN64, region: Region.WW),
            new Platform(name: Name.XBSX, region: Region.WW),
        ]
    }

    void 'test composeJob closure populates resultMap'() {
        given:
        BuildSelector buildSelector = new BuildSelector(steps, currentBuild, env, params)
        AutotestCategory autotestCategory = new AutotestCategory(
            needGameServer: true,
            requiredPlatforms: [Name.PS5.toString()],
            testInfo: new TestInfo(
                useLatestDrone: true
            )
        )
        Map resultMap = [:]
        Platform platform = new Platform(name: Name.PS4)
        String startJobUrl = 'https:/ / dice.com '
        List<JobReference> jobReferences = []
        Closure closure = buildSelector.composeJob(autotestCategory, 'jobName', resultMap, platform, startJobUrl, jobReferences)
        when:
        closure.call()
        then:
        resultMap.size() == 1
        resultMap[platform.toString()].dataChangelist == expectedDataChangelist
        resultMap[platform.toString()].codeChangelist == expectedCodeChangelist
        resultMap[platform.toString()].clientBuildId == expectedClientBuildId
        resultMap[platform.toString()].serverBuildId == expectedServerBuildId
    }

    void 'test trigger with loose files - resultMap is correct with given changelists'() {
        given:
        Map expectedCurrentBuild = currentBuild.clone() as Map
        expectedCurrentBuild.displayName = 'job-name.Any.123.234'
        AutotestMatrix autotestMatrix = Mock()
        autotestMatrix.getTestCategory(_ as String, _ as String) >> new AutotestCategory(
            needGameServer: true,
            requiredPlatforms: [Name.PS5.toString()],
            testInfo: new TestInfo()
        )
        params.data_changelist = '123'
        params.code_changelist = '234'
        params.client_build_id = '\\\\client\\build\\id'
        params.server_build_id = '\\\\server\\build\\id'
        BuildSelector buildSelector = new BuildSelector(steps, currentBuild, env, params)
        when:
        Result result = buildSelector.trigger(autotestMatrix, 'a-branch', 'https://dice.com')
        then:
        1 * steps.EnvInject(expectedCurrentBuild, [target_build_info: [
            (Name.ANY.toString()): [
                dataChangelist: '123',
                codeChangelist: '234',
                clientBuildId : '\\\\client\\build\\id',
                serverBuildId : '\\\\server\\build\\id',
            ]
        ].inspect()])
        result == Result.SUCCESS
    }

    @Unroll
    void 'test trigger with #buildConfig - resultMap is populated with changelists'() {
        given:
        Map expectedCurrentBuild = currentBuild.clone() as Map
        expectedCurrentBuild.displayName = expectedDisplayName
        expectedCurrentBuild.result = expectedCurrentResult
        AutotestMatrix autotestMatrix = Mock()

        autotestMatrix.getTestCategory(_ as String, _ as String) >> new AutotestCategory(
            needGameServer: true,
            requiredPlatforms: [Name.PS5.toString()],
            testInfo: new TestInfo(),
            isTestWithLooseFiles: isTestWithLooseFiles
        )
        autotestMatrix.getPlatforms(_ as String) >> [
            new Platform(name: Name.WIN64),
        ]

        BuildSelector buildSelector = Spy(BuildSelector, constructorArgs: [steps, currentBuild, env, params]) {
            composeJob(_ as AutotestCategory, _ as String, _ as Map, _ as Platform, _ as String, _ as List) >> { args ->
                (args[2] as Map).putAll(composeChangelists)
                return {}
            }
        } as BuildSelector

        when:
        Result result = buildSelector.trigger(autotestMatrix, 'a-branch', 'https://dice.com')

        then:
        1 * steps.EnvInject(expectedCurrentBuild, expectedInjectMap)
        result == expectedResult

        where:
        isTestWithLooseFiles | composeChangelists    | buildConfig   || expectedInjectMap           | expectedResult  | expectedCurrentResult      | expectedDisplayName
        true                 | looseFilesChangelists | 'loose files' || looseFilesExpectedInjectMap | Result.SUCCESS  | null                       | 'job-name.ps5-eu.567.987.win64-ww.567.987.xbsx-ww.567.987'
        false                | droneChangelists      | 'drone'       || droneExpectedInjectMap      | Result.SUCCESS  | null                       | 'job-name.Any.123.234'
        true                 | failedChangelists     | 'loose files' || failedExpectedInjectMap     | Result.UNSTABLE | Result.UNSTABLE.toString() | 'job-name.ps5-eu.567.987'
    }

    void 'test trigger - the job is interrupted if no results are found'() {
        given:
        Map expectedCurrentBuild = currentBuild.clone() as Map
        expectedCurrentBuild.displayName = 'job-name.Any.123.234'
        AutotestMatrix autotestMatrix = Mock()
        autotestMatrix.getTestCategory(_ as String, _ as String) >> new AutotestCategory(
            needGameServer: true,
            requiredPlatforms: [Name.PS5.toString()],
            testInfo: new TestInfo()
        )
        BuildSelector buildSelector = Spy(BuildSelector, constructorArgs: [steps, currentBuild, env, params]) {
            composeJob(*_) >> { args ->
                return {}
            }
        } as BuildSelector

        when:
        buildSelector.trigger(autotestMatrix, 'a-branch', 'https://dice.com')

        then:
        thrown(CobraException)
    }

    @Unroll
    void 'test noChangelistsFound returns #expectedResult when #description'() {
        when:
        boolean result = BuildSelector.noChangelistsFound(resultsMap)
        then:
        result == expectedResult
        where:
        resultsMap      || expectedResult | description
        nullResultsMap  || true           | 'all values are null'
        nullResult      || true           | 'resultsMap is null'
        foundResultsMap || false          | 'changelists are found'
        mixedResults    || false          | 'there is a mix of found and null changelists'
    }

}
