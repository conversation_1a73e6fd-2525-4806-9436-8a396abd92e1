package com.ea.lib.jobs

import com.ea.lib.LibJobDsl
import com.ea.lib.jobsettings.PipelineWarningSettings

class LibPipelineWarning {
    /**
     * Start job that triggers the pipeline warning extraction scripts.
     * Requested in https://jaas.ea.com/browse/COBRA-161
     **/
    static void pipeline_warning_start(def job, def project, def branchFile, def masterFile, String branchName) {
        PipelineWarningSettings settings = new PipelineWarningSettings()
        settings.initializePipelineWarningStart(branchFile, masterFile, project, branchName)
        job.with {
            description(settings.description)
            disabled(settings.isDisabled)
            environmentVariables {
                env('branch_name', settings.branchName)
                env('pipeline_warning_reference_job', settings.pipelineWarningReferenceJob)
                env('project_name', settings.projectName)
            }
            logRotator(7, 100)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to sync.')
                    trim(true)
                }
                choiceParam('clean_data', ['False', 'True'], 'If True, Avalanche will be cleaned before the build.')
            }
            properties {
                disableConcurrentBuilds()
                disableResume()
                pipelineTriggers {
                    triggers {
                        cron {
                            spec(settings.cronTrigger)
                        }
                    }
                }
            }
            quietPeriod(0)
        }
    }

    /**
     * Build job that runs the pipeline warning extraction scripts.
     * Requested in https://jaas.ea.com/browse/COBRA-161
     **/
    static void pipeline_warning_job(def job, def project, def branch_info, def masterFile, String branchName) {
        PipelineWarningSettings settings = new PipelineWarningSettings()
        settings.initializePipelineWarningJob(branch_info, masterFile, project, branchName)
        job.with {
            customWorkspace(settings.workspaceRoot)
            description(settings.description)
            label(settings.jobLabel)
            logRotator(7, 100)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to sync.')
                    trim(true)
                }
                choiceParam('clean_data', ['False', 'True'], 'If True, Avalanche will be cleaned before the build.')
            }
            quietPeriod(0)
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(settings.buildName)
                timeout {
                    absolute(settings.timeoutMinutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
                credentialsBinding {
                    if (settings.userCredentials) {
                        usernamePassword('pwarn_user', 'pwarn_passwd', settings.userCredentials)
                    }
                    if (settings.frostbiteLoginP4Creds) {
                        usernamePassword('fb_p4_user', 'fb_p4_passwd', settings.frostbiteLoginP4Creds)
                    }
                }
            }
            steps {
                if (settings.frostbiteLoginP4Port) {
                    batchFile("echo %fb_p4_passwd%|p4 -p ${settings.frostbiteLoginP4Port} -u %fb_p4_user% login & exit 0")
                }
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                batchFile(settings.elipyCmd)
            }
        }
    }
}
