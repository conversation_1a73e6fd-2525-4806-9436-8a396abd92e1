{#
    Command:
        move_location_frosty
            short_help: Register Frosty-generated build in the configured metadata services.

    Arguments:

    Required variables:
        code_branch
            help: Perforce code branch/stream name
            required: True
        code_changelist
            help: Changelist number of code build used to verify data.
            required: True
        data_branch
            help: Perforce data branch/stream name
            required: True
        data_changelist
            help: Changelist number of data built.
            required: True
        platform
            help: Which platform the build is for
            required: True
        dest_location
            help: Location to move the build to
            required: True
        package_type
            help: Which package type the build is of (files, digital, patch
            required: True
        region
            help: Which region/SKU the build is (WW, NA, EU)
            required: True
        config
            help: Which configuration the build is in (Final, Retail)
            required: True

    Optional variables:
        source_location
            help: Location to move the build from
            default: None
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    move_location_frosty
    --code-branch {{ code_branch }}
    --code-changelist {{ code_changelist }}
    --data-branch {{ data_branch }}
    --data-changelist {{ data_changelist }}
    --platform {{ platform }}
    --dest-location {{ dest_location }}
    --package-type {{ package_type }}
    --region {{ region }}
    --config {{ config }}
    {%- if source_location %}
    --source-location {{ source_location }}
    {%- endif %}
  displayName: elipy move_location_frosty
