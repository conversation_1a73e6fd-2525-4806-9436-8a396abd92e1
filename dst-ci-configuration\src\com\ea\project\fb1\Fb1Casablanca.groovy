package com.ea.project.fb1

import com.ea.project.Cobra

class Fb1Casablanca {
    static String name = 'fb1-casablanca'
    static String short_name = 'fb1'
    static Boolean frostbite_syncer_setup = false
    static Boolean single_perforce_server = false
    static Boolean presync_machines = false
    static String user_credentials = 'monkey.commons'
    static String vault_credentials = 'cobra-online-rob-prod-secret-id'
    static String vault_variable = 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
    static String game_team_secrets_credential = 'game-team-secrets-secret-id'
    static String game_team_secrets_credential_extra = ''
    static String game_team_secrets_credential_online_prod = ''
    static String game_team_secrets_variable = 'GAME_TEAM_SECRETS_SECRET_ID'
    static String vault_server_credentials = ''
    static List vault_default_platforms = []

    static String dataset = 'kindata'
    static String frostbite_licensee = 'Casablanca'

    static String workspace_root = 'D:\\dev'
    static String fbcli_call = 'tnt\\bin\\fbcli\\cli.bat x64'
    static String location = 'casablanca'
    static String elipy_scripts_config_file = 'elipy_fb1.yml'
    static String elipy_install_call = "${fbcli_call} && ${Cobra.elipy_install} $elipy_scripts_config_file >> ${workspace_root}\\logs\\install-elipy.log 2>&1"
    static String elipy_setup_call = "${fbcli_call} && ${Cobra.elipy_setup_env} $elipy_scripts_config_file >> ${workspace_root}\\logs\\setup-elipy-env.log 2>&1"
    static String elipy_call = "${elipy_setup_call} && elipy --location $location"

    static String azure_workspace_root = 'E:\\dev'
    static String azure_elipy_install_root = 'C:\\dev'
    static String azure_elipy_setup_call = "$fbcli_call && $azure_elipy_install_root\\ci\\setup-elipy-env.bat $elipy_scripts_config_file >> $azure_workspace_root\\logs\\setup-elipy-env.log 2>&1"
    static String azure_elipy_install_call = "$fbcli_call && $azure_elipy_install_root\\ci\\install-elipy.bat $elipy_scripts_config_file >> $azure_workspace_root\\logs\\install-elipy.log 2>&1"
    static String azure_elipy_call = "$azure_elipy_setup_call && elipy --location $location"

    static String p4_browser_url = 'https://swarm.frostbite.com/'
    static String p4_user_single_slash = '%USERDOMAIN%\\%USERNAME%'
    static Map p4_extra_servers = [:]

    static String p4_code_root = '/'
    static String p4_code_creds = 'perforce-frostbite02-commons'
    static String p4_code_server = 'dice-p4buildedge02-fb.dice.ad.ea.com:2001'
    static String p4_code_client = 'jenkins-${NODE_NAME}-codestream'
    static String p4_code_client_env = 'jenkins-%NODE_NAME%-codestream'

    static String p4_data_root = '//data/battlefield-cas'
    static String p4_data_creds = 'perforce-tunguska-diceupgradenext'
    static String p4_data_server = 'p4-tunguska-build01.dice.ad.ea.com:2001'
    static String p4_data_client = 'jenkins-${NODE_NAME}-' + dataset + 'stream'
    static String p4_data_client_env = 'jenkins-%NODE_NAME%-' + dataset + 'stream'

    static Map p4_code_servers = [
        'frostbite_build_dice': p4_code_server
    ]

    static List<Map> p4_data_servers = [
        [name: 'tunguska_build_dice', p4_port: p4_data_server],
    ]

    static Map icepick_settings = [:]
    static String webexport_script_path = 'bin\\fbcli\\contrib\\webexport.py' // Casablanca value
    static String drone_exclude_path = 'TnT/Setup/Drone/...'
    static Boolean fake_ooa_wrapped_symbol = false
    static Boolean commerce_debug_disable = true
    static Boolean use_recompression_cache = true

    static Boolean is_cloud = false

    static String autotest_matrix = 'DunAutotestMatrix'
    static List<Map> vault_secrets_project = Cobra.af2_vault_credentials
}
