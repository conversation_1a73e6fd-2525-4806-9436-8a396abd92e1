"""
test_prepare_vm.py

Unit testing for prepare_vm
"""
import os
import pytest
import requests
import subprocess

from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import call

from unittest.mock import patch, MagicMock
from dice_elipy_scripts.prepare_vm import (
    cli,
    delete_tnt_logs,
    configure_avalanche,
    confirm_connectivity,
    confirm_fileshare,
    confirm_perforce,
)
from elipy2.exceptions import ELIPYException


@patch("elipy2.p4.P4Utils")
@patch("dice_elipy_scripts.prepare_vm.time.sleep")
@patch("dice_elipy_scripts.prepare_vm.LOGGER")
@patch("dice_elipy_scripts.prepare_vm.core.delete_folder")
@patch("dice_elipy_scripts.prepare_vm.running_processes.kill")
@patch.dict(os.environ, {"UserProfile": "/path/to/user/profile"})
def test_prepare_vm_self_hosted(
    mock_kill, mock_delete_folder, mock_logger, mock_sleep, mock_p4_utils
):
    mock_p4_instance = mock_p4_utils.return_value
    runner = CliRunner()
    runner.invoke(
        cli,
        [
            "--vm-type",
            "self-hosted",
            "--p4-user",
            "test_user",
            "--p4-port",
            "test_port",
            "--p4-client",
            "test_client",
        ],
    )

    mock_sleep.assert_called_once_with(60)
    mock_kill.assert_called_once()
    mock_delete_folder.assert_has_calls(
        [
            call("C:/ThinSAN.tmp"),
            call(os.path.join("/path/to/user/profile", "Documents", "PS_Logs"), close_handles=True),
        ]
    )
    mock_p4_utils.assert_called_once_with(port="test_port", client="test_client", user="test_user")
    mock_p4_instance.revert.assert_called()


@patch("dice_elipy_scripts.prepare_vm.configure_avalanche")
@patch("dice_elipy_scripts.prepare_vm.confirm_fileshare")
@patch("dice_elipy_scripts.prepare_vm.subprocess")
@patch("dice_elipy_scripts.prepare_vm.requests.get")
@patch.dict(os.environ, {"OTEL_RESOURCE_ATTRIBUTES": "environment=test_env"})
def test_prepare_vm_azure_hosted(
    mock_requests, mock_subprocess, mock_confirm_fileshare, mock_configure_avalanche
):
    mock_requests.return_value.status_code = 200
    mock_subprocess.return_value.run = MagicMock()

    runner = CliRunner()
    result = runner.invoke(
        cli,
        [
            "--vm-type",
            "azure-hosted",
            "--avalanche-server",
            "http://test-avalanche.com:1338",
            "--hailstorm",
            "https://test-hailstorm.com:443",
            "--package-server",
            "https://test-packages.com:443",
            "--file-share",
            "D:\\test-path",
            "--p4-port",
            "test-p4-server.com:1666",
        ],
    )

    mock_requests.assert_has_calls(
        [
            call("http://test-avalanche.com:1338", timeout=5),
            call("https://test-hailstorm.com:443", timeout=5),
            call("https://test-packages.com:443", timeout=5),
        ],
        any_order=True,
    )
    mock_confirm_fileshare.assert_called_once_with("D:\\test-path")
    mock_subprocess.run.assert_called_once_with(
        ["p4", "-p", "test-p4-server.com:1666", "info"],
        check=True,
        stdout=mock_subprocess.DEVNULL,
        stderr=mock_subprocess.DEVNULL,
    )
    mock_configure_avalanche.assert_called()
    assert result.exit_code == 0


@patch("dice_elipy_scripts.prepare_vm.requests.get")
def test_confirm_connectivity_404_error(mock_requests):
    mock_requests.return_value.status_code = 404

    with pytest.raises(ELIPYException):
        confirm_connectivity("http://test-avalanche.com:1338")


@patch("dice_elipy_scripts.prepare_vm.requests.get")
def test_confirm_connectivity_connection_error(mock_requests):
    mock_requests.side_effect = requests.exceptions.ConnectionError

    with pytest.raises(ELIPYException):
        confirm_connectivity("http://test-avalanche.com:1338")


@patch("dice_elipy_scripts.prepare_vm.os.path.exists")
def test_confirm_fileshare_path_not_exists(mock_path_exists):
    mock_path_exists.return_value = False

    with pytest.raises(ELIPYException):
        confirm_fileshare("test_path")


@patch("dice_elipy_scripts.prepare_vm.subprocess")
def test_confirm_perforce_process_error(mock_subprocess):
    mock_subprocess.run.side_effect = subprocess.CalledProcessError(
        returncode=1, cmd="mocked command"
    )

    with pytest.raises(ELIPYException):
        confirm_perforce("test-p4-server.com:1666")


@patch("elipy2.avalanche.restart_avalanche")
@patch("elipy2.avalanche.update_storage_settings")
@patch("elipy2.avalanche.set_upstream_node")
@patch("elipy2.avalanche.disable_upstream_sets")
@patch("elipy2.avalanche.disable_maintenance")
@patch("elipy2.avalanche.stop_avalanche")
def test_configure_avalanche(
    mock_stop_avalanche,
    mock_disable_maintenance,
    mock_disable_upstream_sets,
    mock_set_upstream_node,
    mock_update_storage_settings,
    mock_restart_avalanche,
):
    configure_avalanche("http://test-avalanche.com:1338", "test_path", 100)

    mock_stop_avalanche.assert_called_once()
    mock_disable_maintenance.assert_called_once()
    mock_disable_upstream_sets.assert_called_once()
    mock_set_upstream_node.assert_called_once_with("test-avalanche.com")
    mock_update_storage_settings.assert_called_once_with("test_path", 100)
    mock_restart_avalanche()
