# Mandatory
default:
  name: "sportsatg"
  script_path:
    - "TnT\\Bin\\Python\\virtual\\Lib\\site-packages\\dice_elipy_scripts"
    - "Python\\virtual\\Lib\\site-packages\\dice_elipy_scripts"
  build_share: "\\\\eac-fs1.eac.ad.ea.com\\drebuilds_sportsatg"
  project_name: "sportsatg"
  studio_location: "sportsatg"
  vault_symstore: "false"
  use_onefs_api: "false"
  handle_exe_path: "C:\\ProgramData\\chocolatey\\bin\\handle"
  p4_package_server: "eac-p4buildedge02-fb.eac.ad.ea.com:2001"
  gamescripts_script_path: ""
  skip_increment_client_version: true
  skip_frosty_game_config_flags: "false"
  game_binaries: [""] # this maybe required when I get to their build setup
  release_candidate_retention: 56 # max_config(56) * number of release candidates
  tasks_to_kill:
    - 'AvalancheCLI.exe'
    - 'FrostyIsoTool.exe'
    - 'msbuild.exe'
    - 'nant.exe'
    - 'devenv.exe'
    - 'Tool.Pipeline_Win64_release_Dll.exe'
    - 'mspdbsrv.exe'
    - 'vctip.exe'
    - 'snowcacheserver.exe'
    - 'cl.exe'
    - 'animationapp.exe'
    - 'prospero-clang.exe'
    - 'Icepick.exe'
    - 'eapm.exe'

eav:
  path_retention:
    - \\eac-fs1.eac.ad.ea.com\drebuilds_sportsatg\code\sports-atg-ml-no-raw: 5
    - \\eac-fs1.eac.ad.ea.com\drebuilds_sportsatg\code\sports-atg-il: 2
    - \\eac-fs1.eac.ad.ea.com\drebuilds_sportsatg\code\sports-atg-dl-2024_1_dev-sports-no-raw: 2
    - \\eac-fs1.eac.ad.ea.com\drebuilds_sportsatg\code\sports-atg-rl-FB_2024_1_X-sports-no-raw: 1
    - \\eac-fs1.eac.ad.ea.com\drebuilds_sportsatg\code\dev-na-sports-atg-rgt-noraw: 1
  build_share: "\\\\eac-fs1.eac.ad.ea.com\\drebuilds_sportsatg"
  retention_categories:
    code:
      - 'default': 5
      - 'sports-atg-ml-no-raw': 5
      - 'sports-atg-il': 2
      - 'sports-atg-dl-2024_1_dev-sports-no-raw': 2
      - 'sports-atg-rl-FB_2024_1_X-sports-no-raw': 1
      - 'dev-na-sports-atg-rgt-noraw': 1
    frosty\SportsATG:
      - 'default': 3
      - 'sports-atg-il': 1
      - 'sports-atg-ml-no-raw': 3
      - 'sports-atg-dl-2024_1_dev-sports-no-raw': 1
      - 'sports-atg-rl-FB_2024_1_X-sports-no-raw': 1
  kobold:
    inbox_paths:
      - 'default': '\\eac-fs1.eac.ad.ea.com\drebuilds_sportsatg\kobold\inbox\'
    included_file_extensions:
      - '*.dll'
      - '*.exe'
      - '*.map'
      - '*.bin'
      - '*.prx'
      - '*.elf'
      - '*.nss'
      - '*.nrs'
      - '*.dbg'
      - "*.pdb"
      - "*source-info.*"
      - "*.*_Linux64_*"
      - "*.so"
