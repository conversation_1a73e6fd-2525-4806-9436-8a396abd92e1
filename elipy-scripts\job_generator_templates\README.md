# Generate Job Generator templates

### Why?
The [Job Generator](https://gitlab.ea.com/DRE/automation/frostbite/job-generator)
is used today to generate Elipy commands as part of the ADO Pipeline generation process.

This is done through writing [Jinja templates](https://gitlab.ea.com/DRE/automation/frostbite/job-generator/-/tree/master/JobGenerator2.0/Data/common/templates/ado/step_templates)
manually that correspond to every elipy script.

This is a very tedious workflow and one that very much references the elipy script click args info directly
to the point where it feels like you're just writing the same info in two different places.

`generate_job_generator_templates.py` script was created to replicate all the click args info from every elipy script
in a different format, one that can be used by the Job Generator right away.

The whole point of this solution is to have Job Generator templates be automatically created
when changes happen to the click args of any elipy script.

This provides several benefits like:
- parity between elipy scripts click args and Job Generator generated elipy commands
- less time spent on writing templates from scratch

It's worth saying that this is not a production ready solution just yet, but will be improved as part of future work:
[Skybuild Remote-Preflight for Glacier Milestones](https://docs.google.com/document/d/1a8GGeqhn7dEyvIeRclrs7VPizdEsjHS2vspUhZAvcVA/edit#heading=h.ww90g671f1dc)

### How?
To run the script you can simply right-click on it and hit run. This will recreate all the templates.

The console output will display the info gathered from each elipy script file
and also the status of each template creation.
