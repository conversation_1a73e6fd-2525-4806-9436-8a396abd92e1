"""
move_location_drone.py
"""
import os
import click
from datetime import datetime, timezone
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import core, filer_paths, LOGGER
from elipy2.cli import pass_context
from elipy2.exceptions import ELIPYException, CoreException
from elipy2.telemetry import collect_metrics


@click.command("move_location_drone", short_help="Move Drone build to a different location.")
@click.option("--code-branch", required=True, help="Perforce code branch/stream name.")
@click.option(
    "--code-changelist",
    required=True,
    help="Code changelist for the code build used to verify data.",
)
@click.option("--source-location", default=None, help="Location to move the build from.")
@click.option("--dest-location", required=True, help="Location to move the build to.")
@click.option("--data-changelist", default=None, help="Data changelist being verified.")
@click.option(
    "--old-drone-setup",
    is_flag=True,
    help="Skip deploying TnT (DICE Drone builds req.)",
)
@click.option(
    "--parallel-copy",
    is_flag=True,
    help="Copy platforms/configs in parallel (one per Elipy run).",
)
@click.option("--platform", default=None, help="Platform to copy.)")
@click.option("--config", default=None, help="Config to copy.)")
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    code_branch,
    code_changelist,
    source_location,
    dest_location,
    data_changelist,
    old_drone_setup,
    parallel_copy,
    platform,
    config,
):
    """
    Moves a drone build from one location to another.
    Buildshare of both locations needs to be defined in the yaml file.
    Default location will be used as source if no location is specified for that.
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    if old_drone_setup and data_changelist is not None:
        target_changelist = str(max(int(code_changelist), int(data_changelist)))
    else:
        target_changelist = code_changelist

    # Set path to the folder for an individual platform/config combination.
    if parallel_copy:
        if platform is not None and config is not None:
            source = filer_paths.get_code_build_path(
                code_branch, code_changelist, platform, config, location=source_location
            )
            destination = filer_paths.get_code_build_path(
                code_branch, target_changelist, platform, config, location=dest_location
            )
        else:
            raise ELIPYException("Platform and config required for the parallel copy.")
    else:
        # Set path to the code root folder.
        source = filer_paths.get_code_build_root_path(
            code_branch, code_changelist, location=source_location
        )
        destination = filer_paths.get_code_build_root_path(
            code_branch, target_changelist, location=dest_location
        )

    # Create a lock file.
    lock_location = os.path.dirname(destination)
    if parallel_copy:
        lock_file = os.path.join(lock_location, config + ".locked")
    else:
        lock_file = os.path.join(lock_location, target_changelist + ".locked")

    # Copy code.
    marker_file = os.path.join(destination, ".copy_complete")
    if os.path.exists(destination):
        if os.path.exists(marker_file):
            LOGGER.info(
                "The destination folder %s has already been copied successfully. Skipping copy.",
                destination,
            )
            return
        LOGGER.info(
            "Attempting to deploy to a path that already exists.\
        The previous deployment may have been incomplete."
        )

    try:
        core.robocopy(source, destination, lock_file=lock_file)
        with open(marker_file, "w") as file:
            file.write(f"Copy completed at {datetime.now(timezone.utc).isoformat()}\n")
    except CoreException as exce:
        LOGGER.debug(exce)
        LOGGER.warning("Failed to copy log file to the output directory.")
        raise exce
