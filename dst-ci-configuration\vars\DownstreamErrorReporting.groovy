import com.sonyericsson.jenkins.plugins.bfa.model.FailureCause
import com.sonyericsson.jenkins.plugins.bfa.model.FailureCauseBuildAction
import com.sonyericsson.jenkins.plugins.bfa.model.FoundFailureCause
import com.sonyericsson.jenkins.plugins.bfa.model.indication.BuildLogIndication
import com.sonyericsson.jenkins.plugins.bfa.model.indication.FoundIndication
import hudson.model.Job
import jenkins.model.Jenkins
import org.jenkinsci.plugins.workflow.support.steps.build.BuildUpstreamNodeAction

/**
 * DownstreamErrorReporting.groovy
 * Reports the error of downstream jobs on the main page of a start job.
 *
 * Issue that triggered the creation of this method: https://gitlab.ea.com/dre-cobra/dst-ci-configuration/issues/21
 * Discussion about this method: https://gitlab.ea.com/dre-cobra/dst-ci-configuration/merge_requests/2248
 */
void call(def currentBuild) {
    def upstream_run = currentBuild.rawBuild

    // Define the time span where we should search for downstream jobs
    def start_time = upstream_run.startTimeInMillis
    def end_time = System.currentTimeMillis()
    if (!upstream_run.building) {
        end_time = start_time + upstream_run.duration
    }

    // Get all jobs with this job as the upstream job
    def upstream_run_ID = upstream_run.externalizableId
    def downstream_runs = Jenkins.get().getAllItems(Job).collectMany { job ->
        job.builds.byTimestamp(start_time, end_time).findAll { run ->
            run.getActions(BuildUpstreamNodeAction).any { it.upstreamRunId == upstream_run_ID }
        }
    }

    // For jobs with error, add error messages to the start job
    def foundFailureCauses = []
    for (failed_run in downstream_runs) {
        if (failed_run.getAction(FailureCauseBuildAction) != null) {
            def bfaAction = failed_run.getAction(FailureCauseBuildAction)
            for (def foundCause : bfaAction.foundFailureCauses) {
                def foundIndications = []
                def indications = []
                for (def indication : foundCause.indications) {
                    foundIndications.add(new FoundIndication(null /*build */, indication.pattern, indication.matchingFile, indication.matchingString))
                    indications.add(new BuildLogIndication(indication.pattern))
                }
                def description = foundCause.description
                if (!foundIndications.isEmpty()) {
                    description += '\nFailed Job URL: ' + failed_run.absoluteUrl + 'consoleFull#' + foundIndications[0].matchingHash + foundCause.id
                }
                foundFailureCauses.add(new FoundFailureCause(new FailureCause(foundCause.id, foundCause.name, description, '' /*comment*/, null /*lastOccurred*/, foundCause.categories, indications, null /*modifications*/), foundIndications))
            }
        }
    }
    currentBuild.rawBuild.removeActions(FailureCauseBuildAction)
    currentBuild.rawBuild.actions.add(new FailureCauseBuildAction(foundFailureCauses))
    currentBuild.rawBuild.save()
}
