"""
Test functionality of move_location_bundles.py
"""
import os
import unittest
from unittest.mock import MagicM<PERSON>, patch

from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>

from dice_elipy_scripts.move_location_bundles import cli


# Create a mock for the throw_if_files_found decorator
def mock_throw_if_files_found(*_):
    def decorator(func):
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)

        return wrapper

    return decorator


# Patch the decorator to prevent it from checking for files during tests
@patch(
    "dice_elipy_scripts.move_location_bundles.throw_if_files_found",
    mock_throw_if_files_found,
)
@patch("dice_elipy_scripts.move_location_bundles.add_sentry_tags", MagicMock())
@patch("elipy2.telemetry.upload_metrics", MagicMock())
@patch("elipy2.secrets.get_secrets", MagicMock(return_value={}))
class TestMoveLocationBundles(unittest.TestCase):
    """
    Test the move_location_bundles script
    """

    def setUp(self):
        """Set up test fixtures"""
        self.runner = CliRunner()
        self.basic_args = [
            "--data-branch",
            "data-branch",
            "--data-changelist",
            "12345",
            "--code-branch",
            "code-branch",
            "--code-changelist",
            "67890",
            "--platform",
            "win64",
            "--dest-location",
            "DiceStockholm",
        ]

    @patch("os.path.exists")
    @patch("os.makedirs", MagicMock())
    @patch("elipy2.core.robocopy", MagicMock())
    @patch("elipy2.filer_paths.get_bundles_path")
    @patch("elipy2.filer_paths.get_frosty_base_build_path")
    def test_move_location_bundles_standard(
        self, mock_get_frosty_path, mock_get_bundles_path, mock_path_exists
    ):
        """Test moving standard bundles between locations"""
        source_path = r"\\filer\bundles\source"
        dest_path = r"\\filer\bundles\dest"
        frosty_dest_path = r"\\filer\bundles\frosty_dest"

        # Set up mock return values
        mock_get_bundles_path.side_effect = [source_path, dest_path]
        mock_get_frosty_path.return_value = frosty_dest_path

        # Configure mock_path_exists to return True for source_path but False for dest_path
        def path_exists_side_effect(path):
            if path == source_path:
                return True
            if path == os.path.join(frosty_dest_path, "bundles"):
                return False
            # Return False for D:\changelist.txt and similar files
            if path.startswith("D:\\") and path.endswith("changelist.txt"):
                return False
            return False

        mock_path_exists.side_effect = path_exists_side_effect

        result = self.runner.invoke(cli, self.basic_args + ["--bundle-type", "bundles"])

        self.assertEqual(result.exit_code, 0)
        # Check that paths were calculated correctly
        mock_get_bundles_path.assert_any_call(
            data_branch="data-branch",
            data_changelist="12345",
            code_branch="code-branch",
            code_changelist="67890",
            platform="win64",
            bundles_dir_name="bundles",
        )

        # Check that frosty base path was calculated correctly for destination
        mock_get_frosty_path.assert_any_call(
            data_branch="data-branch",
            data_changelist="12345",
            code_branch="code-branch",
            code_changelist="67890",
            platform="win64",
            location="DiceStockholm",
        )

    @patch("os.path.exists")
    @patch("os.makedirs", MagicMock())
    @patch("elipy2.core.robocopy", MagicMock())
    @patch("elipy2.filer_paths.get_bundles_path")
    @patch("elipy2.filer_paths.get_frosty_base_build_path")
    def test_move_location_combine_bundles(
        self, mock_get_frosty_path, mock_get_bundles_path, mock_path_exists
    ):
        """Test moving combine_bundles between locations"""
        source_path = r"\\filer\bundles\combine_source"
        dest_path = r"\\filer\bundles\combine_dest"
        frosty_source_path = r"\\filer\bundles\frosty_source"
        frosty_dest_path = r"\\filer\bundles\frosty_dest"

        # Set up mock return values
        # Since we're now using source_location, we don't need get_bundles_path for source
        mock_get_bundles_path.return_value = dest_path
        mock_get_frosty_path.side_effect = [frosty_source_path, frosty_dest_path]

        # Configure mock_path_exists to return True for source path but False for dest path
        def path_exists_side_effect(path):
            if path == os.path.join(frosty_source_path, "combine_bundles"):
                return True
            if path == os.path.join(frosty_dest_path, "combine_bundles"):
                return False
            # Return False for D:\changelist.txt and similar files
            if path.startswith("D:\\") and path.endswith("changelist.txt"):
                return False
            return False

        mock_path_exists.side_effect = path_exists_side_effect

        result = self.runner.invoke(
            cli,
            self.basic_args
            + ["--bundle-type", "combine_bundles", "--source-location", "Guildford"],
        )

        self.assertEqual(result.exit_code, 0)
        # Check that frosty base path was calculated correctly for source and destination
        mock_get_frosty_path.assert_any_call(
            data_branch="data-branch",
            data_changelist="12345",
            code_branch="code-branch",
            code_changelist="67890",
            platform="win64",
            location="Guildford",
        )

        mock_get_frosty_path.assert_any_call(
            data_branch="data-branch",
            data_changelist="12345",
            code_branch="code-branch",
            code_changelist="67890",
            platform="win64",
            location="DiceStockholm",
        )

    @patch("os.path.exists")
    @patch("os.makedirs", MagicMock())
    @patch("elipy2.core.robocopy", MagicMock())
    @patch("elipy2.filer_paths.get_bundles_path")
    @patch("elipy2.filer_paths.get_frosty_base_build_path")
    def test_dest_path_already_exists(
        self, mock_get_frosty_path, mock_get_bundles_path, mock_path_exists
    ):
        """Test error case when destination path already exists"""
        source_path = r"\\filer\bundles\source"
        dest_path = r"\\filer\bundles\dest"
        frosty_dest_path = r"\\filer\bundles\frosty_dest"
        final_dest_path = os.path.join(frosty_dest_path, "bundles")

        # Set up mock return values
        mock_get_bundles_path.side_effect = [source_path, dest_path]
        mock_get_frosty_path.return_value = frosty_dest_path

        # Configure mock_path_exists to return True for source_path and dest_path
        # but False for the files checked by throw_if_files_found
        def path_exists_side_effect(path):
            if path == source_path:
                return True
            if path == final_dest_path:
                return True
            # Return False for D:\changelist.txt and similar files
            if path.startswith("D:\\") and path.endswith("changelist.txt"):
                return False
            return False

        mock_path_exists.side_effect = path_exists_side_effect

        result = self.runner.invoke(cli, self.basic_args)

        self.assertEqual(result.exit_code, 1)
        self.assertIn("Destination path already exists", str(result.exception))


if __name__ == "__main__":
    unittest.main()
