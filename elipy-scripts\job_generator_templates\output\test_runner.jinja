{#
    Command:
        test_runner
            short_help: Runs a GTest project in a custom test runner that supports parallel execution and retries.

    Arguments:
        projects
            type: str
            nargs: -1

    Required variables:

    Optional variables:
        attempts
            help: The amount of attempts on a single test
            type: int
            default: 3
        workers
            help: How many tests to run in parallel.
            type: int
            default: 1
        test_filter
            help: Only runs tests that contain this filter.
            type: str
            default: ''
        timeout
            help: In seconds, how long a test can run before it is failed.
            type: int
            default: 600
        pass_through
            type: str
            multiple: True
            help: Arguments passed directly to GTest, for example --gtest "--gtest_repeat=10".
        email
            default: None
            help: User email to authenticate to package server.
        domain_user
            default: None
            help: The user to authenticate to package server as DOMAIN\user.
        password
            default: None
            help: User credentials to authenticate to package server.
        licensee
            multiple: True
            default: None
            help: Frostbite licensee.
        data_directory
            default: None
            help: Data directory.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    test_runner
    projects {{ projects }}
    {%- if attempts %}
    --attempts {{ attempts }}
    {%- endif %}
    {%- if workers %}
    --workers {{ workers }}
    {%- endif %}
    {%- if test_filter %}
    --test-filter {{ test_filter }}
    {%- endif %}
    {%- if timeout %}
    --timeout {{ timeout }}
    {%- endif %}
    {%- if pass_through %}
    --pass-through {{ pass_through }}
    {%- endif %}
    {%- if email %}
    --email {{ email }}
    {%- endif %}
    {%- if domain_user %}
    --domain-user {{ domain_user }}
    {%- endif %}
    {%- if password %}
    --password {{ password }}
    {%- endif %}
    {%- if licensee %}
    --licensee {{ licensee }}
    {%- endif %}
    {%- if data_directory %}
    --data-directory {{ data_directory }}
    {%- endif %}
  displayName: elipy test_runner
