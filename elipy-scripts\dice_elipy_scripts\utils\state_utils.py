"""
state_utils.py

Functions to handle state imports and exports.
"""
import os
from typing import List, Optional
from elipy2 import (
    avalanche,
    code,
    build_metadata_utils,
    filer,
    filer_paths,
    frostbite_core,
    LOGGER,
    windows_tools,
)


def import_local_code_state(
    _builder: code.CodeUtils,
    _filer: filer.FilerUtils,
    code_branch: str,
    platform: str,
    config: str,
    nomaster: bool,
) -> None:
    """
    Function to handle logic around importing of code state.
    """
    if frostbite_core.minimum_fb_version(year=2019, version_nr=4):
        _builder.clean_local(close_handles=True)
    last_code_changelist = _filer.import_tnt_local_build(
        code_branch, platform=platform, config=config, nomaster=nomaster
    )
    code_build_path = filer_paths.get_code_build_path(
        code_branch, last_code_changelist, platform, config, nomaster=nomaster
    )
    code_pipeline_path = filer_paths.get_code_build_path(
        code_branch, last_code_changelist, "pipeline", config, nomaster=nomaster
    )
    if os.path.isdir(code_build_path) and (
        os.path.isdir(code_pipeline_path) or platform.lower() != "tool"
    ):
        try:
            _filer.fetch_code(
                code_branch,
                last_code_changelist,
                platform,
                config,
                nomaster=nomaster,
                fbenv_copy_args=["/XF", "flag_to_include_incremental_state"],
            )
        except Exception:
            LOGGER.warning("Failed to fetch build dependencies, doing a clean build.")
            _builder.clean_local(close_handles=True)


def import_avalanche_data_state(
    data_branch: str,
    code_branch: str,
    platform: str,
    _filer: filer.FilerUtils,
    data_changelist: str,
) -> List[str]:
    """
    Function to handle logic around importing of Avalanche state.
    If possible we try to run this with remote cloning of state.
    """
    extra_args = []
    try:
        if data_branch == "":
            data_branch = code_branch
        metadata_manager = build_metadata_utils.setup_metadata_manager()
        last_successful_build = metadata_manager.get_last_successful(
            build_type="avalanchestate", branch=data_branch, platform=platform
        )
        if (
            "code_changelist" in last_successful_build
            and "data_changelist" in last_successful_build
        ):
            if (
                "remote_host" in last_successful_build
                and last_successful_build["remote_host"] != []
            ):
                db_name = avalanche.get_temp_db_name(
                    platform,
                    last_successful_build["data_changelist"],
                    last_successful_build["code_changelist"],
                    data_branch,
                )
                # This checks if Avalanche vm recently build platform, disabling for testing on fb1
                # if not avalanche.reimport_needed(platform, data_branch, data_changelist):
                #    return []
                if avalanche.db_exists(db_name):
                    LOGGER.info(
                        "Skipped importing Avalanche state, already have {0}".format(db_name)
                    )
                else:
                    source_host = last_successful_build["remote_host"][0]
                    # Ensure Avalanche is running, in case its unavailable due to maintenance etc.
                    if avalanche.check_avalanche_service_api("http://" + source_host + ":1338"):
                        dest_host = windows_tools.get_computer_name()
                        avalanche.remote_clone_db(
                            source_db=db_name,
                            dest_db=db_name,
                            dest_host=dest_host,
                            source_host=source_host,
                            push_built_levels=False,
                            complete_clone=True,
                        )
                        extra_args = [
                            "-importState",
                            "{}@{}".format(db_name, dest_host),
                        ]
                    else:
                        LOGGER.info(
                            "Skipped importing Avalanche state,"
                            / "Avalanche service not running on {0}".format(source_host)
                        )

            else:
                extra_args = avalanche.import_avalanche_state(
                    platform,
                    data_branch,
                    last_successful_build["data_changelist"],
                    last_successful_build["code_changelist"],
                    _filer,
                    only_new_code=True,
                    current_data_changelist=data_changelist,
                )
        return extra_args
    except Exception as exc:
        LOGGER.info("Unable to import data state: {0}".format(exc))
        return []


def export_head_bundles(
    platform: str,
    bundles_location: str,
    deploy_extra_args: Optional[List[str]] = None,
    include_platform: Optional[bool] = True,
    ordering_algorithm: Optional[str] = "breadthFirst",
    db_name: Optional[str] = None,
) -> None:
    """
    Export super bundles from Avalanche.
    """
    if db_name is None:
        db_name = avalanche.get_full_database_name(platform)
    deploy_extra_args = deploy_extra_args or []
    # Deploy HEAD bundles to disk (input to ddelta).
    avalanche.deploy(
        db_name,
        avalanche.get_avalanche_platform_name(platform),
        os.path.join(bundles_location, "data"),
        extra_args=deploy_extra_args,
        include_platform=include_platform,
        ordering_algorithm=ordering_algorithm,
    )
    # Get builtLevels.json and place with HEAD bundles.
    avalanche.get_built_levels(
        db_name, to_file=os.path.join(bundles_location, "Data", "builtLevels.json")
    )
    # Get ops chain and place with HEAD bundles.
    avalanche.get_ops_chain(db_name, to_file=os.path.join(bundles_location, "Data", "ops_chain"))
