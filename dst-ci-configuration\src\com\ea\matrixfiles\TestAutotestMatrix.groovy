package com.ea.matrixfiles

import com.ea.lib.model.autotest.AutotestCategory
import com.ea.lib.model.autotest.FrostedAutotestCategory
import com.ea.lib.model.autotest.Name
import com.ea.lib.model.autotest.Platform
import com.ea.lib.model.autotest.Region
import com.ea.lib.model.autotest.TestInfo
import com.ea.lib.model.autotest.TestSuite

/**
 * See <a href='https://docs.google.com/document/d/1pTxwXhm0T5Mstbg4uaBkjWruC5ntz7pZxqGsmBEcTwk/preview'>Flow Overview</a>
 * See <a href='https://docs.google.com/document/d/1mTfOtPPX93529M7EgmmaGBpmcuMoDyhZ7ZlYCKoVUzw/preview'>Jenkins Config</a>
 **/
class TestAutotestMatrix extends AutotestMatrix {

    private static final String DEV_NA_BATTLEFIELDGAME = 'dev-na-battlefieldgame'
    private static final String KIN_DEV = 'kin-dev'
    private static final String LAB_KIN_DEV = 'lab.kin-dev'
    private static final String KIN_DEV_UNVERIFIED = 'kin-dev-unverified'
    private static final List EXTRA_ARGS_V2 = ['--timeout-client-level-load', '00:06:00', '--runtime-connect-timeout', 360]
    private final List<Platform> defaultPlatforms = [
        new Platform(name: Name.WIN64),
        new Platform(name: Name.PS5),
        new Platform(name: Name.XBSX),
    ]

    private final Map defaultSlackSettings = [channels: [], skip_for_multiple_failures: true]

    private final TestInfo boottestsDiceNext = new TestInfo(
        platforms: [
            new Platform(name: Name.PS5),
            new Platform(name: Name.XBSX)
        ],
        parallelLimit: 6,
        runLevelsInParallel: true,
        testGroup: 'boottestslist',
        timeoutHours: 2,
        tests: [
            new TestSuite(name: 'BootFlowStartupLevel', needGameServer: false),
            new TestSuite(name: 'BootAndDeployDiscarded'),
            new TestSuite(name: 'BootAndDeployIrreversible'),
            new TestSuite(name: 'BootAndDeployLonghaul'),
            new TestSuite(name: 'BootAndDeployTheWall'),
            new TestSuite(name: 'BootAndDeployKaleidoscope'),
            new TestSuite(name: 'BootAndDeployRidge'),
            new TestSuite(name: 'BootAndDeployTestRanges', needGameServer: false,
                platforms: [
                    new Platform(name: Name.WIN64),
                    new Platform(name: Name.PS5),
                    new Platform(name: Name.XBSX)
                ]),
            new TestSuite(name: 'BootAndDeployHourglass'),
            new TestSuite(name: 'BootAndDeployFrost'),
            new TestSuite(name: 'BootAndDeployLightHouse'),
            new TestSuite(name: 'BootAndDeployOasis'),
            new TestSuite(name: 'BootAndDeployPort'),
            new TestSuite(name: 'BootAndDeployRural'),
        ]
    )

    private final TestInfo smoketests = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64),
            new Platform(name: Name.PS5),
            new Platform(name: Name.XBSX)
        ],
        parallelLimit: 6,
        runLevelsInParallel: true,
        testGroup: 'smoketests',
        timeoutHours: 4,
        tests: [
            new TestSuite(name: 'BootAndDeployDiscarded', extraArgs: ['--runtime-connect-timeout', 120]),
            new TestSuite(name: 'BootAndDeployIrreversible', extraArgs: ['--runtime-connect-timeout', 120]),
            new TestSuite(name: 'BootAndDeployLonghaul', extraArgs: ['--runtime-connect-timeout', 120]),
            new TestSuite(name: 'BootAndDeployTheWall', extraArgs: ['--runtime-connect-timeout', 120]),
            new TestSuite(name: 'BootAndDeployKaleidoscope', extraArgs: ['--runtime-connect-timeout', 120]),
            new TestSuite(name: 'BootAndDeployRidge', extraArgs: ['--runtime-connect-timeout', 120]),
            new TestSuite(name: 'BootAndDeployHarbor', extraArgs: ['--runtime-connect-timeout', 120]),
            new TestSuite(name: 'BootAndDeployTestRanges', needGameServer: false),
            new TestSuite(name: 'BootAndDeployHourglass', extraArgs: ['--runtime-connect-timeout', 120]),
            new TestSuite(name: 'BootAndDeployFrost', extraArgs: ['--runtime-connect-timeout', 120]),
            new TestSuite(name: 'BootAndDeployLightHouse', extraArgs: ['--runtime-connect-timeout', 120]),
            new TestSuite(name: 'BootAndDeployOasis', extraArgs: ['--runtime-connect-timeout', 120]),
            new TestSuite(name: 'BootAndDeployPort', extraArgs: ['--runtime-connect-timeout', 120]),
            new TestSuite(name: 'BootFlowStartupLevel', extraArgs: ['--runtime-connect-timeout', 120], needGameServer: false),
            new TestSuite(name: 'BootAndDeployRural', extraArgs: ['--runtime-connect-timeout', 120]),
            new TestSuite(name: 'CriticalTest_AI', needGameServer: false, platforms: [new Platform(name: Name.WIN64)]),
            new TestSuite(name: 'CriticalFunctionality_GroundVehicles', needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'CriticalFunctionality_AirVehicles', needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'AutoPlayerFeatureTests', needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            ),
            new TestSuite(name: 'FeatureTest_ForSmokes', needGameServer: false,
                platforms: [new Platform(name: Name.WIN64)],
            )
        ]
    )

    private final TestInfo buildertests = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64),
            new Platform(name: Name.PS5),
            new Platform(name: Name.XBSX)
        ],
        testGroup: 'buildertests',
        tests: [
            new TestSuite(name: 'FeatureTest_MicaMutators', timeoutHours: 2, extraArgs: ['--timeout-client-level-load', '00:06:00', '--runtime-connect-timeout', 360]),
            new TestSuite(name: 'FeatureTest_ModBuilderLibrary', timeoutHours: 2, extraArgs: ['--timeout-client-level-load', '00:06:00', '--runtime-connect-timeout', 360]),
            new TestSuite(name: 'AutoPlayTestMicaMutators', timeoutHours: 2, extraArgs: ['--timeout-client-level-load', '00:06:00', '--runtime-connect-timeout', 360], needGameServer: true),
            new TestSuite(name: 'AutoPlayTestModBuilder', timeoutHours: 2, extraArgs: ['--timeout-client-level-load', '00:06:00', '--runtime-connect-timeout', 360], needGameServer: true),
        ]
    )

    private final TestInfo performanceLowPriority = new TestInfo(
        platforms: [
            new Platform(name: Name.WIN64, region: Region.WW),
            new Platform(name: Name.XBSX, region: Region.WW)
        ],
        testGroup: 'performancetests',
        tests: [
            new TestSuite(name: 'MemoryTrackAutoPlayTest_MemSnapShot', timeoutHours: 2, extraArgs: ['--timeout-client-level-load', '00:02:00', '--runtime-connect-timeout', 120, '--max-parallel-tests', 1]),
            new TestSuite(name: 'MemoryTrackFlythrough_MemSnapShot',
                platforms: [new Platform(name: Name.PS5, region: Region.DEV)],
                timeoutHours: 2, extraArgs: ['--timeout-client-level-load', '00:02:00', '--runtime-connect-timeout', 120, '--max-parallel-tests', 1]),
        ]
    )

    private final TestInfo releasetests = new TestInfo(
        config: 'release',
        parallelLimit: 3,
        runLevelsInParallel: true,
        testGroup: 'releasetests',
        timeoutHours: 2,
        numTestRuns: 3,
        tests: [
            new TestSuite(name: 'BootGameViewTests', platforms: [new Platform(name: Name.WIN64)], extraArgs: EXTRA_ARGS_V2),
        ]
    )

    private final TestInfo frostedTests_unverified = new TestInfo(
        testGroup: 'frostedtests',
        slackChannel: '#bf-sqr-bct-notify',
        timeoutHours: 3,
        platforms: [new Platform(name: Name.WIN64)],
        tests: [
            new TestSuite(
                name: 'frostedtest_DUNLevelDesign',
                extraArgs: ['--frosted-launch-timeout', 600, '--frosted-test-timeout', 480, '--alltests', '--databaseId', 'BattlefieldGameData.kin-dev-unverified.Win32.Debug'],
                poolType: ''
            ),
            new TestSuite(
                name: 'frostedtest_DUNLogicPrefabsAndSchematics',
                extraArgs: ['--frosted-launch-timeout', 600, '--frosted-test-timeout', 480, '--alltests', '--databaseId', 'BattlefieldGameData.kin-dev-unverified.Win32.Debug'],
                poolType: ''
            ),
        ]
    )

    private final Map collectingSlackSettings = [
        (DEFAULT)               : defaultSlackSettings,
        (DEV_NA_BATTLEFIELDGAME): defaultSlackSettings,
        (KIN_DEV)               : defaultSlackSettings,
        (KIN_DEV_UNVERIFIED)    : defaultSlackSettings,
        (LAB_KIN_DEV)           : defaultSlackSettings,
    ]
    private final Map runLevelsInParallel = [
        (DEFAULT)               : true,
        (DEV_NA_BATTLEFIELDGAME): false,
        (KIN_DEV)               : false,
        (KIN_DEV_UNVERIFIED)    : false,
        (LAB_KIN_DEV)           : false,
    ]

    @Override
    List<String> getBranches() {
        return [DEV_NA_BATTLEFIELDGAME, KIN_DEV, KIN_DEV_UNVERIFIED, LAB_KIN_DEV]
    }

    @Override
    List<AutotestCategory> getTestCategories() {
        return [
            new AutotestCategory(
                name: 'boottests',
                testDefinition: 'boottest',
                runBilbo: false,
                needGameServer: true,
                serverAsset: 'ShippingLevels',
                uploadJournals: false,
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, boottestsDiceNext, '')
                ]
            ),
            new AutotestCategory(
                name: 'smoketests',
                testDefinition: 'smoketests',
                runBilbo: false,
                needGameServer: true,
                uploadJournals: false,
                remoteLabel: 'criterion',
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, smoketests, ''),
                    branchConfiguration(KIN_DEV, smoketests, ''),
                    branchConfiguration(LAB_KIN_DEV, smoketests, '')
                ]
            ),
            new AutotestCategory(
                name: 'performance_low_priority',
                testDefinition: 'performance_low_priority',
                runBilbo: false,
                needGameServer: true,
                config: 'final',
                region: Region.EU,
                format: 'files',
                uploadJournals: false,
                captureVideo: false,
                isTestWithLooseFiles: true,
                branches: [
                    branchConfiguration(KIN_DEV, performanceLowPriority, ''),
                    branchConfiguration(LAB_KIN_DEV, performanceLowPriority, '')
                ]
            ),
            new AutotestCategory(
                name: 'buildertests',
                testDefinition: 'buildertests',
                runBilbo: false,
                needGameServer: true,
                serverAsset: 'PreflightLevels',
                uploadJournals: false,
                branches: [
                    branchConfiguration(KIN_DEV, buildertests, ''),
                    branchConfiguration(LAB_KIN_DEV, buildertests, '')
                ]),
            new AutotestCategory(
                name: 'releasetests',
                testDefinition: 'releasetests',
                runBilbo: false,
                needGameServer: false,
                uploadJournals: false,
                branches: [
                    branchConfiguration(KIN_DEV, releasetests, ''),
                    branchConfiguration(LAB_KIN_DEV, releasetests, '')
                ]),
            new FrostedAutotestCategory(
                name: 'frostedtests',
                testDefinition: 'frostedtests',
                runBilbo: false,
                needGameServer: false,
                uploadJournals: false,
                branches: [
                    branchConfiguration(KIN_DEV_UNVERIFIED, frostedTests_unverified, ''),
                ]),
        ]
    }

    @Override
    List<AutotestCategory> getManualTestCategories() {
        return [
            new AutotestCategory(
                name: 'boottests',
                testDefinition: 'boottest',
                runBilbo: false,
                needGameServer: true,
                serverAsset: 'ShippingLevels',
                uploadJournals: false,
                branches: [
                    branchConfiguration(DEV_NA_BATTLEFIELDGAME, boottestsDiceNext, '')
                ],
                isManual: true),
            new AutotestCategory(
                name: 'buildertests',
                testDefinition: 'buildertests',
                runBilbo: false,
                needGameServer: true,
                serverAsset: 'PreflightLevels',
                uploadJournals: false,
                branches: [
                    branchConfiguration(KIN_DEV, buildertests, ''),
                    branchConfiguration(LAB_KIN_DEV, buildertests, '')
                ],
                isManual: true),
        ]
    }

    @Override
    Map<String, List<Platform>> getPlatforms() {
        return [
            (DEFAULT)               : defaultPlatforms,
            (DEV_NA_BATTLEFIELDGAME): defaultPlatforms,
            (KIN_DEV)               : defaultPlatforms,
            (KIN_DEV_UNVERIFIED)    : defaultPlatforms,
            (LAB_KIN_DEV)           : defaultPlatforms,
        ]
    }

    @Override
    boolean shouldLevelsRunInParallel(String branchName) {
        return getSetting(branchName, runLevelsInParallel)
    }

    @Override
    Map getSlackSettings(String branchName) {
        return (Map) getSetting(branchName, collectingSlackSettings)
    }

    @Override
    Map getManualTestCategoriesSetting(String branchName) {
        Map settings = [
            parallel_limit: 1,
            categories    : getManualTestCategories(branchName),
        ]
        return settings
    }
}
