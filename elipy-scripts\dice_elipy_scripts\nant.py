"""
nant.py
"""
from builtins import str
import click
from elipy2.cli import pass_context
from elipy2.frostbite import fbcli


@click.command("nant", short_help="Run nant using the fbcli subsystem")
@click.option(
    "--package",
    help="Name of package in masterconfig or a path to a .build file",
    required=True,
    type=str,
)
@click.option(
    "--platforms",
    help="Name of the platform(s)",
    multiple=True,
    required=True,
    type=str,
)
@click.option(
    "--targets",
    help="Nant target(s)",
    multiple=True,
    required=True,
    type=str,
)
@click.option(
    "--vsver",
    help="Visual Studio version to generate solutions for",
    default=None,
    type=str,
)
@click.option(
    "--propertiesfile",
    help="XML file containing additional arguments to framework",
    default=None,
    type=str,
)
@click.option(
    "--framework_args",
    help="Additional arguments to framework",
    multiple=True,
    default=None,
    type=str,
)
@click.option(
    "--outsourcer",
    help="Enables outsourcing configurations",
    type=bool,
    default=False,
)
@click.option(
    "--outsourcer_non_proxy_sdks",
    help="Enables outsourcing configurations by using non-proxy SDKs",
    type=bool,
    default=False,
)
@click.option(
    "--fwdwarn",
    help="Forwarding headers will emit a warning",
    type=bool,
    default=False,
)
@click.option(
    "--fwderror",
    help="Forwarding headers will emit an error",
    type=bool,
    default=False,
)
@click.option(
    "--licensee_agnostic",
    help="Do not add licensee info to nant.exe's command line",
    type=bool,
    default=False,
)
@click.option(
    "--ignoredeprecation",
    help="Frostbite deprecations are ignored and will not fail the build",
    type=bool,
    default=False,
)
@click.option(
    "--enabledeprecation",
    help="Frostbite deprecations are enabled and will fail the build",
    type=bool,
    default=False,
)
@click.option(
    "--enableexpiredapierror",
    help="Frostbite APIs that are expired will fail the build",
    type=bool,
    default=False,
)
@click.option(
    "--validate_package_access",
    help="Validates all required packages as accessible",
    type=bool,
    default=False,
)
@pass_context
def cli(
    _,
    package,
    platforms,
    targets,
    vsver,
    propertiesfile,
    framework_args,
    outsourcer,
    outsourcer_non_proxy_sdks,
    fwdwarn,
    fwderror,
    licensee_agnostic,
    ignoredeprecation,
    enabledeprecation,
    enableexpiredapierror,
    validate_package_access,
):
    """
    Run Nant for the package.
    """

    args = [package]
    args.extend(list(platforms))
    args.extend(list(targets))
    if vsver:
        args.extend(["-vsver", vsver])
    if propertiesfile:
        args.extend(["-propertiesfile", propertiesfile])
    if outsourcer:
        args.extend(["-outsourcer"])
    if outsourcer_non_proxy_sdks:
        args.extend(["-outsourcer_non_proxy_sdks"])
    if fwdwarn:
        args.extend(["-fwdwarn"])
    if fwderror:
        args.extend(["-fwderror"])
    if licensee_agnostic:
        args.extend(["-licensee_agnostic"])
    if ignoredeprecation:
        args.extend(["-ignoredeprecation"])
    if enabledeprecation:
        args.extend(["-enabledeprecation"])
    if enableexpiredapierror:
        args.extend(["-enableexpiredapierror"])
    if validate_package_access:
        args.extend(["-validate-package-access"])
    # Framework args should come last...
    if framework_args:
        args.extend(framework_args)

    fbcli.run("nant", method_args=args)
