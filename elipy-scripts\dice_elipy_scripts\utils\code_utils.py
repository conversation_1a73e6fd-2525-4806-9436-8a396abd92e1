"""
code_utils.py

Utility module
"""
import json
import pathlib
import os
import re
from elipy2 import core, filer_paths, frostbite_core, local_paths, LOGGER, SETTINGS
from elipy2.exceptions import ELIPYException
from elipy2.frostbite import package_utils
from dice_elipy_scripts.utils.frosty_build_utils import authenticate_eapm_credstore


def run_gensln(
    builder,
    password=None,
    user=None,
    domain_user=None,
    framework_args=None,
    alltests=False,
    nomaster=False,
    wsl=False,
    gensln_config=None,
    stressbulkbuild=False,
):
    """
    Runs gensln and updates the shell vars.
    Also authenticates to the package server.
    """
    framework_args = framework_args or []

    if password is not None and user is not None:
        authenticate_eapm_credstore(password, user, domain_user=domain_user)

    gensln_args = {
        "framework_args": framework_args,
        "alltests": alltests,
        "nomaster": nomaster,
        "wsl": wsl,
        "override_config": gensln_config,
        "stressbulkbuild": stressbulkbuild,
    }

    builder.gensln(**gensln_args)


def _get_metadata_files(location=None):
    """
    Get a list of metadata files to append to the build entry.
    """
    # Get list of files from elipy
    key = "metadata_files"
    try:
        metadata_files = SETTINGS.get(key, location)
    except Exception as _:
        LOGGER.warning("Cannot find ELIPY setting " + key)
        metadata_files = []

    return metadata_files


def _get_existing_files(file_list):
    """
    Return a list of existing files.
    """
    existing_files = []
    for file_path in file_list:
        full_file_path = os.path.join(frostbite_core.get_game_root(), file_path)
        if os.path.exists(full_file_path):
            existing_files.append(full_file_path)
        else:
            LOGGER.warning("File does not exist: " + full_file_path)

    return existing_files


def _add_metadata_file(file_path, _filer, bilbo_doc_path):
    """
    Add the contents of a single json file to the code metadata entry.
    """
    LOGGER.info("Adding data from " + file_path)
    file_name = os.path.basename(file_path)
    with open(file_path, "r") as json_file:
        data = json_file.read()
        data = re.sub("//.*", "", data)  # Remove comments
        metadata = json.loads(data)

    # Sanitise file name
    key = re.sub(r"[\s|\.|-]", "_", file_name).lower()
    _filer.bilbo.set_attribute(bilbo_doc_path, key, metadata)


def add_metadata_files(_filer, branch, changelist, nomaster=False, location=None):
    """
    Adding metadata from files specified in the `metadata_files` elipy setting.
    """
    bilbo_doc_path = filer_paths.get_code_build_root_path(branch, changelist, nomaster=nomaster)
    metadata_files = _get_metadata_files(location=location)
    existing_files = _get_existing_files(metadata_files)

    for file_path in existing_files:
        try:
            _add_metadata_file(file_path, _filer, bilbo_doc_path)
        except Exception as exc:
            LOGGER.error("Failed to register extra metadata: {0}".format(exc))


def modify_buildlayout(buildlayout_config, licensee, platform, config):
    """
    Sets the config value in the buildlayout file to buildlayout_config.
    """
    if len(licensee) > 1:
        raise ELIPYException("Multiple licensees enabled and unable to modify buildlayout.")

    layout_file = os.path.join(
        local_paths.get_local_build_path(platform, config),
        "{}.Main.buildlayout".format(licensee[0]),
    )

    LOGGER.info(
        "Setting value of configuration in buildlayout file to {} ".format(buildlayout_config)
    )
    with open(layout_file, "r+") as file:
        data = json.load(file)
        data["tags"]["Configuration"] = buildlayout_config

        file.seek(0)
        json.dump(data, file)
        file.truncate()


def download_outsource_dependencies(artifactory_user, artifactory_apikey):
    """
    Outsourcer builds have different requirements than regular builds.
    """
    LOGGER.info("Downloading outsourcer build requirements...")

    game_root = frostbite_core.get_game_root()
    package_path = os.path.join(game_root, "TnT\\Packages")

    script_name = "outsource-package-install.py"
    LOGGER.info("   Running %s %s", script_name, package_path)
    core.import_module_from_file(
        "outsource-package-install",
        os.path.join(frostbite_core.get_tnt_root(), "bin", "fbcli", "contrib", script_name),
    ).deploy(package_path)
    LOGGER.info("   Done")

    packages_to_install = ["MSBuildTools"]
    for package in packages_to_install:
        LOGGER.info("   Installing %s", package)
        package_utils.nant_on_package(package, framework_args=["-G:config-use-FBConfig=true"])
        LOGGER.info("   Done")

    core.update_shell()

    output_path = os.path.join(game_root, "outsources_dependencies")
    core.delete_folder(output_path)
    pathlib.Path(output_path).mkdir(exist_ok=True, parents=True)

    items_to_download = [
        "eigen-3.3.7.zip",
        "antlr-3.5.2-complete.jar",
        "antlrworks-1.5.jar",
    ]

    for item in items_to_download:
        LOGGER.info("   Downloading %s", item)
        target_path = os.path.join(output_path, item)
        if os.path.exists(target_path):
            os.remove(target_path)

        artifactory_path = (
            "https://artifacts.ea.com/artifactory/dre-generic-federated/"
            "cobra/criterion/outsources_dependencies/"
            f"{item}"
        )
        core.run(
            [
                "curl",
                "-u",
                "{}:{}".format(artifactory_user, artifactory_apikey),
                artifactory_path,
                "-o",
                target_path,
            ]
        )
        LOGGER.info("   Done")
    LOGGER.info("Done")
    return output_path


def prepare_outsource_dependencies(source_path):
    """
    Move all dependencies into their expected folders.
    """
    game_root = frostbite_core.get_game_root()
    eigen_source = os.path.join(source_path, "eigen-3.3.7.zip")
    eigen_target = os.path.join(game_root, r"TnT\LocalPackages\Eigen\3.3.7.efd9867")
    pathlib.Path(eigen_target).mkdir(exist_ok=True, parents=True)
    core.extract_zip(eigen_source, eigen_target)

    antlr_target = os.path.join(game_root, r"TnT\LocalPackages\antlr\3.5.2")
    pathlib.Path(antlr_target).mkdir(exist_ok=True, parents=True)
    core.robocopy(
        source_path,
        antlr_target,
        extra_args=["antlr-3.5.2-complete.jar", "antlrworks-1.5.jar"],
    )
