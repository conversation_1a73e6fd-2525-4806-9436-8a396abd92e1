package com.ea.lib.jobs

import com.ea.lib.LibCommonCps
import com.ea.lib.LibCommonNonCps
import com.ea.lib.LibJobDsl
import com.ea.lib.jobsettings.StoreBaselinesSettings

class LibStoreBaselines {
    /**
     * Adds generic job parameters for store regular baseline start jobs.
     */
    static void store_regular_baseline_start(def job, def project, def branch_info) {
        // Set values for variables.
        def modifiers = ['baseline']
        def combine_target_branch = branch_info.combine_bundles?.is_target_branch ?: false
        def disable_build = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'disable_build', false)
        def platforms = branch_info.store_baseline_platforms ?: ['all', 'all-but-gen5', 'all-but-gen4', 'all-but-win64'] + LibCommonCps.VAULT_PLATFORMS
        def store_baseline_reference_job = branch_info.store_baseline_reference_job ?: branch_info.branch_name + '.frosty.start'
        def trigger_string = branch_info.trigger_string_regular_baseline_builds ?: 'TZ=Europe/Stockholm \n H 0 * * 1' //Once a week
        def trigger_type = branch_info.trigger_type_regular_baseline_builds ?: 'cron'

        // Add sections to the Jenkins job.
        job.with {
            description('Scheduler to trigger Store Regular Baseline Builds.')
            disabled(disable_build)
            logRotator(30, 100)
            quietPeriod(0)
            properties {
                disableConcurrentBuilds()
                disableResume()
                pipelineTriggers {
                    triggers {
                        if (trigger_type == 'scm') {
                            pollSCM {
                                scmpoll_spec(trigger_string)
                            }
                        } else if (trigger_type == 'cron') {
                            cron {
                                spec(trigger_string)
                            }
                        }
                    }
                }
            }
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist for the baseline build.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist for the baseline build.')
                    trim(true)
                }
                if (combine_target_branch) {
                    stringParam {
                        name('combine_code_changelist')
                        defaultValue('')
                        description('Specifies combined code changelist for the baseline build.')
                        trim(true)
                    }
                    stringParam {
                        name('combine_data_changelist')
                        defaultValue('')
                        description('Specifies combined data changelist for the baseline build.')
                        trim(true)
                    }
                }
                choiceParam('platform', platforms, 'Platform(s) to store baselines for')
            }
            environmentVariables {
                env('branch_name', branch_info.branch_name)
                env('combine_target_branch', combine_target_branch)
                env('project_name', project.name)
                env('store_baseline_reference_job', store_baseline_reference_job)
            }
        }
    }

    /**
     * Adds generic job parameters for store regular baseline build jobs.
     */
    static void store_regular_baseline_builds(def job, Class project_file, Class branch_file, Class master_file, String branchName) {
        StoreBaselinesSettings jobSettings = new StoreBaselinesSettings()
        jobSettings.initialize(branch_file, master_file, project_file, branchName)

        job.with {
            description(jobSettings.description)
            logRotator(30, 100)
            label(jobSettings.jobLabel)
            quietPeriod(0)
            customWorkspace(jobSettings.workspaceRoot)
            parameters {
                stringParam {
                    name('code_branch')
                    defaultValue(jobSettings.codeBranch)
                    description('')
                    trim(true)
                }
                stringParam {
                    name('data_branch')
                    defaultValue(jobSettings.dataBranch)
                    description('')
                    trim(true)
                }
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Code changelist')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Data changelist')
                    trim(true)
                }
                choiceParam('platform', ['all', 'all-but-gen5', 'all-but-gen4', 'all-but-win64'] + LibCommonCps.VAULT_PLATFORMS, 'Platform(s) to store baselines for')
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(jobSettings.buildName)
            }
            steps {
                LibJobDsl.installElipy(delegate, jobSettings.elipyInstallCall, project_file)
                batchFile(jobSettings.elipyCmd)
            }
        }
    }
}
