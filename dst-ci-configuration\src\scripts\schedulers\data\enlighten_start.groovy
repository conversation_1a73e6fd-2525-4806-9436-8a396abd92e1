package scripts.schedulers.data

import com.ea.lib.LibJenkins
import com.ea.project.GetBranchFile
import hudson.model.Result

def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
def project = ProjectClass(env.project_name)

/**
 * enlighten_start.groovy
 */
pipeline {
    agent { label '(scheduler && master) || scheduler || executor_agent' }
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Trigger Enlighten jobs') {
            steps {
                script {
                    def code_changelist = params.code_changelist ?: LibJenkins.getLastStableCodeChangelist(env.data_reference_job)
                    def data_changelist = params.data_changelist ?: LibJenkins.getLastStableDataChangelist(env.data_reference_job)

                    def args = [
                        string(name: 'code_changelist', value: code_changelist),
                        string(name: 'data_changelist', value: data_changelist),
                        string(name: 'clean_data', value: params.clean_data),
                    ]
                    def jobs = [:]

                    def inject_map = [
                        'code_changelist': code_changelist,
                        'data_changelist': data_changelist,
                    ]
                    EnvInject(currentBuild, inject_map)
                    currentBuild.displayName = env.JOB_NAME + '.' + data_changelist + '.' + code_changelist

                    def last_code_built = LibJenkins.getLastStableCodeChangelist(env.JOB_NAME)
                    def last_data_built = LibJenkins.getLastStableDataChangelist(env.JOB_NAME)
                    if (code_changelist == last_code_built && data_changelist == last_data_built) {
                        echo 'Last code build was on CL ' + last_data_built + '.' + last_code_built + ' and current is CL ' + data_changelist + '.' + code_changelist + ', aborting build.'
                        currentBuild.result = Result.UNSTABLE.toString()
                        return
                    }

                    def data_matrix = branchfile.data_matrix

                    def final_result = Result.SUCCESS
                    def continue_build = true
                    for (def run = 0; run <= env.retry_limit.toInteger(); run++) { // Retry failed jobs if retry_limit > 0.
                        jobs = [:]
                        final_result = Result.SUCCESS
                        for (platform in data_matrix) {
                            for (group in platform.enlighten_bake_group) {
                                for (level in group.enlighten_bake) {
                                    if (env.JOB_NAME.contains(group.group_name)) {
                                        def allow_failure = false
                                        def job_name = env.branch_name + '.enlighten.' + group.group_name + '.' + env.dataset + '.' + level.name
                                        if (NeedsRebuildData(job_name, code_changelist, data_changelist)) {
                                            if (run > 0 && IsGameFailure(job_name, allow_failure)) {
                                                if (allow_failure == false) {
                                                    final_result = Result.FAILURE
                                                    // Set pipeline as failed if there are jobs from IsGameFailure category.
                                                    continue_build = false
                                                }
                                                break
                                            } else {
                                                jobs[job_name] = {
                                                    def downstream_job = build(job: job_name, parameters: args, propagate: false)
                                                    final_result = final_result.combine(Result.fromString(downstream_job.result))
                                                    LibJenkins.printFailureMessage(this, downstream_job, allow_failure)
                                                    LibJenkins.printRunningJobs(this)
                                                    def job_env = downstream_job.rawBuild.getEnvironment(TaskListener.NULL)
                                                    def slack_report_message = "<${job_env?.BUILD_URL}|${job_env?.JOB_NAME}>"
                                                    def slack_message_color = 'good'
                                                    if (Result.fromString(downstream_job.result) == Result.SUCCESS) {
                                                        if (job_env?.SHELVE_CHANGELIST != null) {
                                                            slack_report_message += " - Shelved in ${job_env?.SHELVE_CHANGELIST}"
                                                        } else if (job_env?.SUBMIT_CHANGELIST != null) {
                                                            slack_report_message += " - Submitted in ${job_env?.SUBMIT_CHANGELIST}"
                                                        }
                                                    } else {
                                                        slack_report_message += ' - Job failed, no Enlighten data shelved or submitted'
                                                        slack_message_color = 'danger'
                                                    }
                                                    def slack_settings = [
                                                        channels                  : ['#bf-enlighten-bakes'],
                                                        always_notify             : true,
                                                        skip_for_multiple_failures: false,
                                                        report_message            : slack_report_message,
                                                        message_color             : slack_message_color,
                                                    ]
                                                    SlackMessageNew(downstream_job, slack_settings, project.short_name)
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            if (continue_build == false) {
                                break
                            }
                        }
                        if (continue_build == false) {
                            break
                        }
                        parallel(jobs)
                        if (final_result == Result.SUCCESS) {
                            break
                        }
                    }

                    currentBuild.result = final_result.toString()

                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
}
