package all

import com.ea.lib.LibCommonCps
import com.ea.lib.LibCommonNonCps
import com.ea.lib.LibJobDsl
import com.ea.lib.LibScm
import com.ea.lib.LibSlack
import com.ea.lib.jobs.LibAWS
import com.ea.lib.jobs.LibBilbo
import com.ea.lib.jobs.LibGitLabRepo
import com.ea.lib.jobs.LibJobMonitoring
import com.ea.lib.jobs.LibMaintenance
import com.ea.lib.jobs.LibOrphaned
import com.ea.project.Cobra
import com.ea.project.GetBranchFile
import com.ea.project.GetMasterFile
import com.ea.project.all.All

GetMasterFile.get_masterfile(BUILD_URL).each { masterSettings ->
    def branches = masterSettings.maintenance_branch
    def project = masterSettings.project

    branches.each { current_branch, info ->
        if (All.isAssignableFrom(project)) {
            project = info.project
        }

        def branchfile = GetBranchFile.get_branchfile(project.name, current_branch)
        def general_settings = branchfile.general_settings
        def standard_jobs_settings = branchfile.standard_jobs_settings
        def branch_info = info + general_settings + standard_jobs_settings + [project: project]
        def need_avalanche_maint = branch_info.avalanche_maint != null ? branch_info.avalanche_maint : true
        def freestyle_jobs = []

        LibOrphaned.startJob(this, masterSettings, project)
        LibJobMonitoring.startJob(this, masterSettings, project)
        LibGitLabRepo.jenkinsRegistryJob(this, masterSettings)

        project.p4_code_servers.each { name, p4_port ->
            def clean_codestream = job("p4_clean_codestream.${name}") {}
            LibJobDsl.addVaultSecrets(clean_codestream, branch_info)
            LibMaintenance.p4_clean_codestream_job(clean_codestream, project, [:], p4_port)
            LibJobDsl.postclean_silverback(clean_codestream, project, [:])
        }

        for (data_clean_job in project.p4_data_servers) {
            String dataset = data_clean_job.dataset ?: project.dataset
            def clean_datastream = job("p4_clean_datastream.${data_clean_job.name}") {}
            LibJobDsl.addVaultSecrets(clean_datastream, branch_info)
            LibMaintenance.p4_clean_datastream_job(clean_datastream, project, [:], data_clean_job.p4_port, dataset)
            LibJobDsl.postclean_silverback(clean_datastream, project, [:])
        }

        if (need_avalanche_maint) {
            LibMaintenance.avalanche_maintenance_start(
                LibJobDsl.standardPipelineJob(
                    this,
                    'avalanche_maintenance.start',
                    'src/scripts/schedulers/avalanche_maintenance.start.groovy'
                ),
                project,
                branchfile,
                masterSettings,
                current_branch as String
            )

            def avalanche_maintenance_job = job('avalanche_maintenance') {}
            def avalanche_maintenance_azure_job = job('avalanche_maintenance_azure') {}

            freestyle_jobs.add(avalanche_maintenance_job)
            freestyle_jobs.add(avalanche_maintenance_azure_job)
            LibMaintenance.avalanche_maintenance_job(avalanche_maintenance_job, branchfile, project, masterSettings, current_branch as String)
            LibMaintenance.avalanche_maintenance_job(avalanche_maintenance_azure_job, branchfile, project, masterSettings, current_branch as String, true)
            [avalanche_maintenance_job, avalanche_maintenance_azure_job].each { job ->
                LibJobDsl.addVaultSecrets(job, branch_info)
                LibJobDsl.archive_non_build_logs(job, branch_info)
                LibJobDsl.postclean_silverback(job, project, branch_info)
            }
        }

        def p4_delete_workspace = job('workspace-deletion-' + project.name) {}
        freestyle_jobs.add(p4_delete_workspace)
        LibMaintenance.p4_delete_workspace(p4_delete_workspace, project, branchfile, masterSettings, current_branch as String)
        LibJobDsl.addVaultSecrets(p4_delete_workspace, branch_info)
        LibJobDsl.postclean_silverback(p4_delete_workspace, project, branch_info)

        def avalanchecli_nuke_start = job('avalanchecli_nuke.start') {}
        freestyle_jobs.add(avalanchecli_nuke_start)
        LibMaintenance.avalanchecli_nuke_start(avalanchecli_nuke_start, branchfile, project, masterSettings, current_branch as String)
        LibJobDsl.addVaultSecrets(avalanchecli_nuke_start, branch_info)
        LibJobDsl.postclean_silverback(avalanchecli_nuke_start, project, branch_info)

        def avalanchecli_nuke_all = job('avalanchecli_nuke_all') {}
        freestyle_jobs.add(avalanchecli_nuke_all)
        LibMaintenance.avalanchecli_nuke_all(avalanchecli_nuke_all, branchfile, project, masterSettings, current_branch as String)
        LibJobDsl.addVaultSecrets(avalanchecli_nuke_all, branch_info)
        LibJobDsl.postclean_silverback(avalanchecli_nuke_all, project, branch_info)

        def avalanchecli_nuke_job = job('avalanchecli_nuke') {}
        freestyle_jobs.add(avalanchecli_nuke_job)
        LibMaintenance.avalanchecli_nuke_job(avalanchecli_nuke_job, branchfile, project, masterSettings, current_branch as String)
        LibJobDsl.addVaultSecrets(avalanchecli_nuke_job, branch_info)
        LibJobDsl.postclean_silverback(avalanchecli_nuke_job, project, branch_info)

        def avalanchecli_drop_all_dbs_start = job('avalanchecli_drop_all_dbs.start') {}
        freestyle_jobs.add(avalanchecli_drop_all_dbs_start)
        LibMaintenance.avalanchecli_drop_all_dbs_start(avalanchecli_drop_all_dbs_start, branchfile, project, masterSettings, current_branch as String)
        LibJobDsl.addVaultSecrets(avalanchecli_drop_all_dbs_start, branch_info)
        LibJobDsl.postclean_silverback(avalanchecli_drop_all_dbs_start, project, branch_info)

        def avalanchecli_drop_all_dbs_job = job('avalanchecli_drop_all_dbs') {}
        freestyle_jobs.add(avalanchecli_drop_all_dbs_job)
        LibMaintenance.avalanchecli_drop_all_dbs_job(avalanchecli_drop_all_dbs_job, branchfile, project, masterSettings, current_branch as String)
        LibJobDsl.addVaultSecrets(avalanchecli_drop_all_dbs_job, branch_info)
        LibJobDsl.postclean_silverback(avalanchecli_drop_all_dbs_job, project, branch_info)

        def clean_agent_folder = job('clean_agent_folder') {}
        freestyle_jobs.add(clean_agent_folder)
        LibMaintenance.clean_agent_folder(clean_agent_folder, branchfile, project, masterSettings, current_branch as String)
        LibJobDsl.addVaultSecrets(clean_agent_folder, branch_info)

        def code_warm = job('code.warm.cobra') {}
        freestyle_jobs.add(code_warm)
        LibMaintenance.code_warm_job(code_warm, project, branchfile, masterSettings, current_branch as String)
        LibJobDsl.addVaultSecrets(code_warm, branch_info)
        LibJobDsl.initialP4revert(code_warm, project, branch_info, true, false)
        LibJobDsl.postclean_silverback(code_warm, project, branch_info)

        def data_warm = job('data.warm.cobra') {}
        freestyle_jobs.add(data_warm)
        LibMaintenance.data_warm_job(data_warm, project, branchfile, masterSettings, current_branch as String)
        LibJobDsl.addVaultSecrets(data_warm, branch_info)
        LibJobDsl.initialP4revert(data_warm, project, branch_info)
        LibJobDsl.postclean_silverback(data_warm, project, branch_info)

        LibMaintenance.jenkinsShutdownJob(this, branch_info, !LibCommonNonCps.get_setting_value(branch_info, [], 'is_cloud', false, project))

        if (branch_info.include_vault) {
            LibMaintenance.vault_start(
                LibJobDsl.standardPipelineJob(
                    this,
                    'vault.' + project.name + '.' + project.short_name,
                    'src/scripts/schedulers/maintenance/vault_pipeline.groovy'
                ),
                project, branchfile, masterSettings, current_branch as String
            )

            ['build'].each { job_type ->
                def vault_job = job('vault.' + project.name + '.' + project.short_name + '.' + job_type) {}
                freestyle_jobs.add(vault_job)
                LibMaintenance.vault_job(vault_job, project, branchfile, masterSettings, current_branch as String, job_type)
                LibSlack.slack_default(vault_job, '#cobra-outage-vault', project.short_name, true)
                LibJobDsl.addVaultSecrets(vault_job, branch_info)
                LibJobDsl.initialP4revert(vault_job, project, branch_info, true, false)
                LibJobDsl.archive_non_build_logs(vault_job, branch_info)
            }

            def baseline_job = job('store.baseline.' + project.short_name + '.build') {}
            freestyle_jobs.add(baseline_job)
            LibMaintenance.baseline_job(baseline_job, project, branchfile, masterSettings, current_branch as String)
            LibSlack.slack_default(baseline_job, '#cobra-outage-vault', project.short_name, true)
            LibJobDsl.addVaultSecrets(baseline_job, branch_info)
            LibJobDsl.initialP4revert(baseline_job, project, branch_info, true, false)
            LibJobDsl.archive_non_build_logs(baseline_job, branch_info)
        }

        if (branch_info.include_register_release_candidate) {
            def bilbo_reg_release_candidate_job = job('register_release_candidate.' + project.name + '.' + project.short_name) {}
            freestyle_jobs.add(bilbo_reg_release_candidate_job)
            LibBilbo.bilbo_register_release_candidate_job(bilbo_reg_release_candidate_job, project, branchfile, masterSettings, current_branch as String)
            LibSlack.slack_default(bilbo_reg_release_candidate_job, '#cobra-outage-register-release-candidate', project.short_name, true)
            LibJobDsl.addVaultSecrets(bilbo_reg_release_candidate_job, branch_info)
            LibJobDsl.initialP4revert(bilbo_reg_release_candidate_job, project, branch_info, true, false)
            LibJobDsl.archive_non_build_logs(bilbo_reg_release_candidate_job, branch_info)
        }
        LibCommonCps.add_downstream_freestyle_job_triggers(freestyle_jobs, current_branch, branchfile.freestyle_job_trigger_matrix)
    }

    if (project.presync_machines == true) {
        def branches_to_sync = masterSettings.branches
        branches_to_sync.each { current_branch, info ->
            def branchfile = GetBranchFile.get_branchfile(project.name, current_branch)
            def general_settings = branchfile.general_settings
            def branch_info = info + general_settings + [branch_name: current_branch]
            LibMaintenance.sync_start(
                LibJobDsl.standardPipelineJob(
                    this,
                    'sync.' + current_branch + '.start',
                    'src/scripts/schedulers/all/sync_pipeline.groovy'
                ),
                branchfile,
                project,
                masterSettings,
                current_branch as String
            )
            def sync_job = job('sync.' + current_branch + '.job') {}
            LibMaintenance.sync_job(sync_job, project, branchfile, masterSettings, current_branch as String)
            LibScm.sync_code_and_data(sync_job, project, branch_info)
            LibJobDsl.addVaultSecrets(sync_job, branch_info)
            LibJobDsl.initialP4revert(sync_job, project, branch_info)
            LibJobDsl.postclean_silverback(sync_job, project, branch_info)

            LibCommonCps.add_downstream_freestyle_job_triggers([sync_job], current_branch, branchfile.freestyle_job_trigger_matrix)
        }
    }

    def sdelete_jobs = [
//        'standard',
//        'statebuild',
    ]

    if (project.is_cloud) {
        masterSettings.preflight_branches.each { current_branch, branch_info ->
            def branchfile = GetBranchFile.get_branchfile(project.name, current_branch)
            def standardJobsSettings = branchfile.standard_jobs_settings
            LibAWS.downsize_fleet(
                LibJobDsl.standardPipelineJob(
                    this,
                    'aws.' + current_branch + '.downsize_fleet.start',
                    'src/scripts/schedulers/aws/update-fleet.groovy'
                ),
                branch_info
            )
            LibAWS.upsize_fleet(
                LibJobDsl.standardPipelineJob(
                    this,
                    'aws.' + current_branch + '.upsize_fleet.start',
                    'src/scripts/schedulers/aws/update-fleet.groovy'
                ),
                branch_info
            )
            LibAWS.warm_agent(this, current_branch, standardJobsSettings.schedule)
        }
    } else {
        if (All.isAssignableFrom(project)) {
            project = Cobra
        }
        for (job_type in sdelete_jobs) {
            LibJobDsl.standardPipelineJob(this, "sdelete.${job_type}.${project.name}.start", 'src/scripts/schedulers/sdelete.' + job_type + '.groovy').with {
                environmentVariables {
                    env('project_name', project.name)
                }
                properties {
                    pipelineTriggers {
                        triggers {
                            switch (job_type) {
                                case 'standard':
                                    cron {
                                        spec('H 5 * * 3,6')
                                    }
                                    break
                                case 'statebuild':
                                    cron {
                                        spec('H * * * 1-6\nH 6-23 * * 7')
                                    }
                                    break
                            }
                        }
                    }
                }
            }

            // !NB! sdelete flags sucks. Before making changes please read up on the changes between versioning:
            // https://www.kennethghartman.com/securely-delete-files-with-sdelete/
            // https://superuser.com/questions/747820/what-is-the-different-between-sdelete-c-and-z

            def sdelete_job = job('sdelete.' + job_type) {
                logRotator(daysToKeep = 7, numToKeep = 150)
                concurrentBuild()
                if (job_type == 'statebuild') {
                    throttleConcurrentBuilds {
                        maxTotal(5)
                    }
                }
                description('Runs zero-out-free-space.ps1 on the machine to make sure we empty out free space (write 0 to all free bits).')
                parameters {
                    stringParam {
                        name('Node')
                        defaultValue('')
                        description('Name of the node to run the script on.')
                        trim(true)
                    }
                }
                properties {
                    groovyLabelAssignmentProperty {
                        secureGroovyScript {
                            script('return binding.getVariables().get("Node")')
                            sandbox(true)
                        }
                    }
                }
                wrappers {
                    timestamps()
                }
                steps {
                    batchFile('powershell.exe -NonInteractive -ExecutionPolicy ByPass -File ci\\resources\\zero-out-free-space.ps1')
                }
            }
            LibScm.git_ci(sdelete_job)
        }
    }

    job('jenkins.cleanup') {
        logRotator(daysToKeep = 7, numToKeep = 50)
        description('Cleanup $JENKINS_HOME/monitoring and $JENKINS_HOME/fingerprints; approve all scripts; remove secrets from environment')
        label('master')
        wrappers {
            timestamps()
            timeout {
                absolute(240)
                failBuild()
                writeDescription('Build failed due to timeout after {0} minutes')
            }
        }
        triggers {
            cron('H/5 * * * 1-6\nH/5 6-23 * * 7')
            upstream('seed', 'UNSTABLE')
        }
        steps {
            // Clean garbage files on disk
            shell('''
set +x

FINGERPRINTS=$JENKINS_HOME/fingerprints
if [ ! -d "$FINGERPRINTS" ]; then
    echo Error: $FINGERPRINTS does not exist.
    exit 1
fi
echo Cleaning up $FINGERPRINTS contents
set +e; rm -rf $FINGERPRINTS/*; set -e;
echo Done

MONITORING=$JENKINS_HOME/monitoring
if [ ! -d "$MONITORING" ]; then
    echo Error: $MONITORING does not exist.
    exit 1
fi
echo Cleaning up old files from $MONITORING
set +e; find $MONITORING -mindepth 1 -mtime +30 -delete; set -e;
echo Done
''')
        }
    }

    job('jenkins.gc') {
        logRotator(daysToKeep = 10, numToKeep = 50)
        description('Trigger a manual full garbage collection on the Jenkins master to clear up metaspace, unused classes and heap space.')
        label('master')
        wrappers {
            timestamps()
            timeout {
                absolute(1) // in minutes
                abortBuild()
                writeDescription('Build aborted due to timeout after {0} minutes')
            }
        }
        triggers {
            cron('H * * * 1-6\nH 6-23 * * 7')
        }
        steps {
            systemGroovyCommand('System.gc()') {
                sandbox(true)
            }
        }
    }

    def reboot = job('agent.reboot') { // old name: maintenance.reboot
        logRotator(daysToKeep = 7, numToKeep = 100)
        description('Runs shutdown /r /f /t 0 on the machine. Restarting the OS.')
        concurrentBuild()
        parameters {
            nodeParam('machine') {
                description('select which machine to reboot')
                trigger('allowMultiSelectionForConcurrentBuilds')
                eligibility('IgnoreOfflineNodeEligibility')
            }
        }
        throttleConcurrentBuilds {
            maxPerNode(1)
        }
        wrappers {
            timestamps()
            buildName('${JOB_NAME}.${machine}')
        }
        steps {
            // ping is used as a hacky workaround wait() function, since sleep isn't a windows cmd and timeout doesn't work in a non-interactive environment
            batchFile('ping localhost -n 5 && shutdown /r /t 0')
            systemGroovyCommand('Thread.sleep(120000)') // Sleep for 2 minutes after reboot command
        }
    }
    LibScm.git_ci(reboot)

    def runCommandOnAgent = job('runCommandOnAgent') {
        logRotator(daysToKeep = 7, numToKeep = 100)
        description('Runs arbitrary command on the specified machine via BFA automation.')
        concurrentBuild()
        parameters {
            nodeParam('machine') {
                description('select which machine to run command on')
                trigger('allowMultiSelectionForConcurrentBuilds')
                eligibility('IgnoreOfflineNodeEligibility')
            }
            stringParam {
                name('command')
                defaultValue('')
                description('Command to execute on the target machine')
                trim(true)
            }
        }
        throttleConcurrentBuilds {
            maxPerNode(1)
        }
        wrappers {
            timestamps()
            buildName('${JOB_NAME}.${machine}')
        }
        steps {
            batchFile('${command}')
        }
    }
    LibScm.git_ci(runCommandOnAgent)

    pipelineJob('offline.agent') {
        logRotator(daysToKeep = 21, numToKeep = 20)
        properties {
            disableResume()
        }
        parameters {
            stringParam {
                name('node')
                defaultValue('')
                description('Input Jenkins agent name which need to be put offline. e.g cu2-66ae33')
                trim(true)
            }
            stringParam {
                name('reason')
                defaultValue('')
                description('Describe why this node should be offline. e.g troubleshooting issue; test new hardware, [Taint] NeedsTainting ')
                trim(true)
            }
        }
        definition {
            cps {
                script(readFileFromWorkspace('src/scripts/schedulers/offlineAgent.groovy'))
                sandbox(true)
            }
        }
    }

    pipelineJob('autoMaintenance.agent') {
        logRotator(daysToKeep = 21, numToKeep = 20)
        properties {
            disableResume()
            pipelineTriggers {
                triggers {
                    cron {
                        spec('*/10 * * * *')
                    }
                }
            }
        }
        definition {
            cps {
                script(readFileFromWorkspace('src/scripts/schedulers/autoMaintenanceAgent.groovy'))
                sandbox(true)
            }
        }
    }

    pipelineJob('delete.agent') {
        logRotator(daysToKeep = 21, numToKeep = 20)
        properties {
            disableResume()
            pipelineTriggers {
                triggers {
                    cron {
                        spec('H H/12 * * 1-5')  //only run this job on working days' 12P.M and 12A.M
                    }
                }
            }
        }
        definition {
            cps {
                script(readFileFromWorkspace('src/scripts/schedulers/deleteAgent.groovy'))
                sandbox(true)
            }
        }
    }

    pipelineJob('delete.cloud.agent') {
        logRotator(daysToKeep = 21, numToKeep = 20)
        properties {
            disableResume()
            pipelineTriggers {
                triggers {
                    cron {
                        spec('0  22  *  *  0')  // run every sunday at 22:00
                    }
                }
            }
        }
        definition {
            cps {
                script(readFileFromWorkspace('src/scripts/schedulers/recycleCloudAgents.groovy'))
                sandbox(true)
            }
        }
    }

    pipelineJob('azure.testagent.create') {
        logRotator(daysToKeep = 7, numToKeep = 20)
        properties {
            disableResume()
        }
        definition {
            cps {
                script(readFileFromWorkspace('src/scripts/schedulers/createTestCloudAgents.groovy'))
                sandbox(true)
            }
        }
    }

    pipelineJob('sync.secrets.configurations') {
        logRotator(daysToKeep = 2, numToKeep = 20)
        properties {
            disableResume()
            disableConcurrentBuilds()
            pipelineTriggers {
                triggers {
                    cron {
                        spec('H/10 * * * 1-5')  // run every 10 minutes on weekdays
                    }
                }
            }
        }
        definition {
            cps {
                script(readFileFromWorkspace('src/scripts/schedulers/syncSecretsAndConfigurations.groovy'))
                sandbox(true)
            }
        }
    }
}
