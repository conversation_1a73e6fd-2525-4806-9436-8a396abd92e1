package com.ea.lib.model.autotest.jobs

import com.ea.lib.model.autotest.TestSuite

/**
 * Model for an Autotest job
 */
class AutotestModel {
    /**
     * The job's name
     */
    String name
    /**
     * The list of tests that run in the job
     */
    List<TestSuite> tests = []
    /**
     * The platform the tests run on
     */
    String platform
    /**
     * Optional extra arguments to pass to Icepick
     */
    String extraArguments
    /**
     * Optional region
     */
    String region

    /**
     * Adds a test to the job
     * @param testSuite the test suite to add
     * @return The added testSuite
     */
    TestSuite addTest(TestSuite testSuite) {
        tests.add(testSuite)
        return testSuite
    }

    /**
     * Returns a String representation of the model
     * @return A description
     */
    String getJobDescription() {
        StringBuilder sb = new StringBuilder()
        sb.append("Optional region: ${region}\n")
        sb.append("Optional extra_args: ${extraArguments}\n")
        sb.append("This job triggers the following ${platform} test suites: \n")
        tests.each {
            sb.append("- ${it.name}\n")
        }
        return sb.toString()
    }
}
