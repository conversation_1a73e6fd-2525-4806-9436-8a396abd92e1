package scripts.schedulers

import hudson.model.Queue
import hudson.model.labels.Label<PERSON>tom
import jenkins.model.<PERSON>
import jenkins.model.JenkinsLocationConfiguration

/**
 * node-status-influxdb.groovy
 */
pipeline {
    options {
        allowBrokenBuildClaiming()
    }
    agent { label 'master' }
    stages {
        stage('Run') {
            steps {
                script {
                    Set<LabelAtom> allLabels = []

                    if (Jenkins.get().nodes.isEmpty()) {
                        echo 'No nodes. Exiting.'
                        return 0
                    }

                    for (def node : Jenkins.get().nodes) {
                        def labels = node.assignedLabels
                        allLabels.addAll(labels)
                    }

                    echo 'All labels: ' + allLabels
                    StringBuilder builder = new StringBuilder()
                    def jenkinsUrl = JenkinsLocationConfiguration.get().url

                    for (def label : allLabels) {
                        builder.append('executors_busy,')
                        builder.append('server=').append(jenkinsUrl).append(',')
                        builder.append('label=').append(label.name)
                        builder.append(' value=').append(label.busyExecutors).append('\n')

                        builder.append('executors_idle,')
                        builder.append('server=').append(jenkinsUrl).append(',')
                        builder.append('label=').append(label.name)
                        builder.append(' value=').append(label.idleExecutors).append('\n')

                        builder.append('executors_total,')
                        builder.append('server=').append(jenkinsUrl).append(',')
                        builder.append('label=').append(label.name)
                        builder.append(' value=').append(label.totalExecutors).append('\n')

                        def queued = Queue.instance.countBuildableItemsFor(label)
                        builder.append('queue_length,')
                        builder.append('server=').append(jenkinsUrl).append(',')
                        builder.append('label=').append(label.name)
                        builder.append(' value=').append(queued).append('\n')
                    }

                    echo builder.toString()
                    def baseUrl = new URL('https://influxdb.cobra.dre.ea.com/write?db=jenkins-nodes')
                    def connection = baseUrl.openConnection()
                    connection.setRequestProperty('Content-Type', 'application/x-www-form-urlencoded')
                    connection.requestMethod = 'POST'
                    connection.doOutput = true
                    connection.outputStream.write(builder.toString().getBytes('UTF-8'))
                    def responseCode = connection.responseCode

                    if (responseCode == 204) {
                        echo 'POST successful'
                        return 0
                    }

                    echo "Error while posting data. HTTP return code: $responseCode"
                    echo connection.inputStream.text
                    return 1
                }
            }
        }
    }
}

