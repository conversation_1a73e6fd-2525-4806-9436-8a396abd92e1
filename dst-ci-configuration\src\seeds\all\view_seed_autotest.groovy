package all

import com.ea.lib.model.autotest.AutotestCategory
import com.ea.matrixfiles.AutotestMatrix
import com.ea.matrixfiles.AutotestMatrixFactory
import com.ea.project.GetMasterFile
import jenkins.model.Jenkins

GetMasterFile.get_masterfile(BUILD_URL).each { masterSettings ->
    String autotestMatrixName = masterSettings.project.autotest_matrix
    Map branches = masterSettings.autotest_branches

    branches.each { branchName, branchInfo ->
        if (branchInfo.project) {
            autotestMatrixName = branchInfo.project.autotest_matrix
        }
        if (autotestMatrixName) {
            String autotestRegex = "(${branchName})\\.autotest.*"
            AutotestMatrix autotestMatrix = AutotestMatrixFactory.getInstance(autotestMatrixName)
            List<AutotestCategory> testCategories = autotestMatrix.getTestCategories(branchName as String)

            sectionedView("${branchName} Autotests") {
                sections {
                    listView {
                        name('Orchestration')
                        width('FULL')
                        alignment('CENTER')
                        jobs {
                            regex("${autotestRegex}\\.start")
                        }
                        columns {
                            status()
                            weather()
                            name()
                            lastSuccess()
                            lastFailure()
                            lastDuration()
                            buildButton()
                        }
                    }
                    listView {
                        name('Utility jobs')
                        width('FULL')
                        alignment('CENTER')
                        jobs {
                            regex("${branchName}.(build.selector|bilbo.register-.*-autotestutils|autotest-to-integration.code)")
                        }
                        columns {
                            status()
                            weather()
                            name()
                            lastSuccess()
                            lastFailure()
                            lastDuration()
                        }
                    }
                    if (autotestMatrix.hasManualTestJobs(branchName)) {
                        listView {
                            name('Manual Tests')
                            width('FULL')
                            alignment('CENTER')
                            jobs {
                                regex("${autotestRegex}.*\\.manual.*")
                            }
                            columns {
                                status()
                                weather()
                                name()
                                lastSuccess()
                                lastFailure()
                                lastDuration()
                            }
                        }
                    }
                    testCategories.each {
                        String testCategoryName = it.name
                        listView {
                            name(testCategoryName)
                            width('FULL')
                            alignment('CENTER')
                            jobs {
                                regex("(${autotestRegex}.*\\.${testCategoryName}.*(job).*)|(${branchName}.${testCategoryName}.p4counterupdater)")
                            }
                            columns {
                                status()
                                weather()
                                name()
                                lastSuccess()
                                lastFailure()
                                lastDuration()
                            }
                        }
                    }
                }
            }
            if (branchInfo.koala_autotest == true) {
                sectionedView('koala-autotest') {
                    sections {
                        listView {
                            name('autotest jobs')
                            width('FULL')
                            alignment('CENTER')
                            jobs {
                                regex("${branchName}.autotest.(lkg_auto|lkgtests).*")
                            }
                            columns {
                                status()
                                weather()
                                name()
                                lastSuccess()
                                lastFailure()
                                lastDuration()
                            }
                        }
                    }
                }
            }
        }
    }

    // Set the default view. NOTE: the views are created after JobDSL is finished. This means that the second run will do the actual set.
    if (branches.size() > 0 && Jenkins.get().getView(branches[0]) != null) {
        Jenkins.get().primaryView = Jenkins.get().getView(branches[0])
    }
}
