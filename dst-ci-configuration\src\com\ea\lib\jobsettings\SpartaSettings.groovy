package com.ea.lib.jobsettings

import com.ea.lib.LibCommonNonCps

class SpartaSettings extends JobSetting {

    void initializeSpartaStart(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        List modifiers = ['sparta']
        description = "Scheduler to start a Sparta job on ${this.branchName}, using the latest verified binary build."
        isDisabled = LibCommonNonCps.get_setting_value(branchInfo, modifiers, 'disable_build', false)
        projectName = this.projectFile.name
    }

    void initializeSpartaBundleJob(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        jobLabel = branchInfo.job_label_statebuild ?: 'statebuild'
        def timeoutHours = branchInfo.timeout_hours_sparta ?: 24
        timeoutMinutes = timeoutHours * 60
        description = "Builds ${branchInfo.dataset} for win64 with code from ${branchInfo.code_branch}."
        buildName = '${JOB_NAME}.${ENV, var="bundle_changelist"}'
        elipyCmd = "${this.elipyCall} sparta_bundle ${branchInfo.dataset}" +
            " --code-branch ${branchInfo.code_branch} --code-changelist %code_changelist%" +
            ' --source-bundle-path %source_bundle_path% --bundle-changelist %bundle_changelist% --description %description% --no-submit %no_submit%'
    }

}
