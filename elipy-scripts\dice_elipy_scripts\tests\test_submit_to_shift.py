"""
test_submit_to_shift.py

Unit testing for submit_to_shift
"""

import unittest
from copy import deepcopy
import sys


import pytest
from click.testing import C<PERSON><PERSON>unner
from mock import MagicMock, patch

sys.modules["elipy2.shifters"] = MagicMock()
from dice_elipy_scripts.submit_to_shift import cli, validate_data_changelist, check_shift_pc_agent

DEFAULT_ARGS = [
    "some.user",
    "some-password",
]

DEFAULT_VALUES = {
    "submission_path": "some-submission-path",
    "use_bilbo": False,
    "shift_url": "https://shift.ea.com",
    "submission_tool": "submission-tool-path",
    "code_branch": "code_branch",
    "code_changelist": "code_changelist",
    "compression": False,
    "data_branch": "data_branch",
    "data_changelist": "data_changelist",
    "re_shift": False,
    "use_elipy_config": False,
    "artifactory_user": "artifactory_user",
    "artifactory_api_key": "artifactory_apikey",
    "build_id": "build_12345",
}


def get_submit_to_shift_test_args(kwargs_dict):
    test_args = [
        "--user",
        "some.user",
        "--password",
        "some-password",
        "--code-branch",
        kwargs_dict["code_branch"],
        "--code-changelist",
        kwargs_dict["code_changelist"],
        "--data-branch",
        kwargs_dict["data_branch"],
        "--data-changelist",
        kwargs_dict["data_changelist"],
        "--submission-path",
        kwargs_dict["submission_path"],
        "--shifter-type",
        kwargs_dict["shift_url"],
        "--submission-tool",
        kwargs_dict["submission_tool"],
        "--artifactory-user",
        kwargs_dict["artifactory_user"],
        "--artifactory-apikey",
        kwargs_dict["artifactory_api_key"],
        "--build-id",
        kwargs_dict["build_id"],
    ]

    if kwargs_dict["re_shift"]:
        test_args.append("--force-reshift")
    if kwargs_dict["use_elipy_config"]:
        test_args.append("--use-elipy-config")
    if kwargs_dict["use_bilbo"]:
        test_args.append("--use-bilbo")
    else:
        test_args.append(
            "--no-bilbo",
        )

    return test_args


class TestShifterFactory:
    @pytest.mark.parametrize(
        "shifter_type", ["offsite_basic_drone_shifter", "frosty_shifter", "offsite_drone_shifter"]
    )
    @patch("dice_elipy_scripts.submit_to_shift.ShifterFactory")
    @patch("dice_elipy_scripts.submit_to_shift.check_shift_pc_agent")
    def test_get_shifter_instance_offsite_basic_drone_shifter(
        self, mock_check, mock_shifter_factory, shifter_type
    ):
        test_kwargs = deepcopy(DEFAULT_VALUES)
        test_args = get_submit_to_shift_test_args(test_kwargs)
        test_args.extend(
            [
                "--shifter-type",
                shifter_type,
            ]
        )

        runner = CliRunner()
        runner.invoke(cli, test_args)

        assert mock_shifter_factory.get_shifter_instance.call_count == 1


class TestSubmitToShift:
    @pytest.mark.parametrize("shifter_type", ["frosty_shifter"])
    @patch("dice_elipy_scripts.submit_to_shift.ShifterFactory")
    @patch("dice_elipy_scripts.submit_to_shift.check_shift_pc_agent")
    def test_shifter_process_shift_upload_called(
        self,
        mock_check,
        mock_shifter_factory,
        shifter_type,
    ):
        # Prepare test args
        default_args = deepcopy(DEFAULT_VALUES)
        test_args = [
            "--user",
            "some.user",
            "--password",
            "some-password",
            "--code-branch",
            default_args["code_branch"],
            "--code-changelist",
            default_args["code_changelist"],
            "--data-branch",
            default_args["data_branch"],
            "--data-changelist",
            default_args["data_changelist"],
            "--submission-path",
            default_args["submission_path"],
            "--shifter-type",
            shifter_type,
            "--shift-url",
            default_args["shift_url"],
            "--submission-tool",
            default_args["submission_tool"],
            "--artifactory-user",
            default_args["artifactory_user"],
            "--artifactory-apikey",
            default_args["artifactory_api_key"],
            "--build-id",
            default_args["build_id"],
        ]

        if default_args["re_shift"]:
            test_args.append("--force-reshift")
        if default_args["use_elipy_config"]:
            test_args.append("--use-elipy-config")
        if default_args["use_bilbo"]:
            test_args.append("--use-bilbo")
        else:
            test_args.append(
                "--no-bilbo",
            )

        # Prepare mocks
        mock_shifter_get_shifter_instance = mock_shifter_factory.get_shifter_instance = MagicMock()

        # Run tests
        runner = CliRunner()
        runner.invoke(cli, test_args)

        assert mock_shifter_get_shifter_instance.call_count == 1


class TestValidateDataChangelist(unittest.TestCase):
    @patch("dice_elipy_scripts.submit_to_shift.SETTINGS")
    @patch("dice_elipy_scripts.submit_to_shift.LOGGER")
    def test_data_changelist_not_set_in_unified_project(self, mock_logger, mock_settings):
        mock_settings.get.return_value = True
        kwargs = {"data_changelist": None, "code_changelist": "1234"}
        result = validate_data_changelist(kwargs)
        self.assertEqual(result, "1234")
        mock_logger.info.assert_called_once()

    @patch("dice_elipy_scripts.submit_to_shift.SETTINGS")
    def test_data_changelist_set_in_unified_project(self, mock_settings):
        mock_settings.get.return_value = True
        kwargs = {"data_changelist": "5678", "code_changelist": "1234"}
        result = validate_data_changelist(kwargs)
        self.assertEqual(result, "5678")

    @patch("dice_elipy_scripts.submit_to_shift.SETTINGS")
    def test_data_changelist_not_set_in_non_unified_project(self, mock_settings):
        mock_settings.get.return_value = False
        kwargs = {"data_changelist": None, "code_changelist": "1234"}
        result = validate_data_changelist(kwargs)
        self.assertIsNone(result)

    @patch("dice_elipy_scripts.submit_to_shift.SETTINGS")
    def test_data_changelist_set_in_non_unified_project(self, mock_settings):
        mock_settings.get.return_value = False
        kwargs = {"data_changelist": "5678", "code_changelist": "1234"}
        result = validate_data_changelist(kwargs)
        self.assertEqual(result, "5678")
