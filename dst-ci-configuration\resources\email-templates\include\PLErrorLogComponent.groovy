import com.sonyericsson.jenkins.plugins.bfa.model.FailureCauseBuildAction
import groovy.xml.MarkupBuilder
import hudson.model.Result
import hudson.model.Run

public class PLErrorLogComponent implements IEmailComponent {

    private List gatherDownstreamErrorLogData(Run run) {
        def downstreamRuns = JobUtil.getDownstreamRuns(run)
        return (downstreamRuns.collect { r ->
            return gatherErrorLogData(r)
        }).flatten()
    }


    private List<Map> gatherErrorLogData(Run run) {
        def action = run.getAction(FailureCauseBuildAction.class)
        if (!action) {
            return [[:]]
        }

        if (action.getFoundFailureCauses().isEmpty()) {
            return [[:]]
        }

        def result = []

        for (def cause : action.getFoundFailureCauses()) {
            def errorName = cause.getName()
            def causeId = cause.getId()

            def indications = cause.getIndications()
            if (!indications) {
                continue
            }

            for (def indication : indications) {
                def indicationHash = indication.getMatchingHash()
                def runUrl = JobUtil.getClassicDisplayURL(run)
                def errorUrl = "${runUrl}consoleFull#${indicationHash}${causeId}"

                result.add([
                    ErrorName         : errorName,
                    ErrorCause        : indication.getMatchingString(),
                    ErrorContextBefore: '',
                    ErrorContextAfter : '',
                    ErrorUrl          : errorUrl,
                    JobName           : run.getParent().getName(),
                ])
            }
        }
        return result
    }


    private Map gather(Run run) {
        def errorLogDataList = []
        def errorLogData = gatherErrorLogData(run)
        if (errorLogData) {
            errorLogDataList.addAll(errorLogData)
        }

        def downstreamErrorLogDataList = gatherDownstreamErrorLogData(run)
        if (downstreamErrorLogDataList) {
            errorLogDataList.addAll(downstreamErrorLogDataList)
        }

        def data = [:]
        if (errorLogDataList) {
            data.ErrorLogDataList = errorLogDataList
        }
        return data
    }


    private String getSectionClass(Map data) {
        return "section"
    }


    private void renderErrorLogData(Map data, MarkupBuilder builder) {
        if (data) {
            builder.table(border: "0", cellpadding: "0", cellspacing: "0", width: "100%", style: "max-width: 500px; table-layout: fixed;", class: "responsive-table") {
                tr {
                    td(class: "error-log-header") {
                        def errorUrl = data.ErrorUrl
                        def errorName = data.ErrorName
                        def jobName = data.JobName
                        if (errorUrl) {
                            a(href: errorUrl, class: "error-log-header") {
                                mkp.yield(errorName + ' while building ' + jobName)
                            }
                        } else {
                            mkp.yield(errorName + ' while building ' + jobName)
                        }
                    }
                }

                tr(class: "error-log-content") {
                    td(class: "error-log-content") {
                        def errorContextBefore = data.ErrorContextBefore
                        if (errorContextBefore) {
                            pre(class: "error-log-content") {
                                mkp.yield(errorContextBefore)
                            }
                        }

                        def errorCause = data.ErrorCause
                        if (errorCause) {
                            pre(class: "error-log-cause") {
                                mkp.yield(errorCause)
                            }
                        }

                        def errorContextAfter = data.ErrorContextAfter
                        if (errorContextAfter) {
                            pre(class: "error-log-content") {
                                mkp.yield(errorContextAfter)
                            }
                        }
                    }
                }
            }
        }
    }


    public void render(Run run, MarkupBuilder builder) {
        def data = gather(run)
        if (data) {
            builder.tr {
                td(class: getSectionClass(data), align: "center") {
                    mkp.yieldUnescaped("<!-- ERROR LOG -->")
                    table(border: "0", cellpadding: "0", cellspacing: "0", width: "100%", style: "max-width: 500px; table-layout: fixed;", class: "responsive-table") {
                        tr {
                            td(class: "section-title") {
                                mkp.yield("Error Output")
                            }
                        }
                    }
                    def errorLogDataList = data.ErrorLogDataList
                    if (errorLogDataList) {
                        def iterator = errorLogDataList.iterator()
                        while (iterator.hasNext()) {
                            renderErrorLogData(iterator.next(), builder)
                            if (iterator.hasNext()) {
                                mkp.yieldUnescaped("<br/>")
                            }
                        }
                    }
                }
            }
        }
    }


    private boolean isRunApplicable(Run run) {
        def applicable = false
        def result = run.getResult()
        if (result && result.isWorseThan(Result.SUCCESS)) {
            def primaryCauseActions = run.getActions(FailureCauseBuildAction.class)
            applicable = primaryCauseActions.any { primaryCauseAction ->
                return !primaryCauseAction?.getFoundFailureCauses()?.isEmpty()
            }
        }
        return applicable
    }


    public boolean isApplicable(Run run) {
        def applicable = isRunApplicable(run)
        if (!applicable) {
            def downstreamRuns = JobUtil.getDownstreamRuns(run)
            applicable = downstreamRuns.any { r ->
                return isRunApplicable(r)
            }
        }
        return applicable
    }


    public String getEmbeddedStyle(Run run) {
        return """
                    /* ERROR LOG SPECIFIC STYLES */

                    td.error-log-header {
                        border: 1px solid #C8C8C8;
                        color: #9A9fA8;
                        font-family: "LatoLatinWeb", "Lato", "Helvetica Neue", Helvetica, Arial, sans-serif;
                        font-size: 12px;
                        font-weight: bold;
                        padding: 10px;
                        text-align: left;
                    }

                    a.error-log-header {
                        color: #9A9fA8;
                        font-family: "LatoLatinWeb", "Lato", "Helvetica Neue", Helvetica, Arial, sans-serif;
                        font-size: 12px;
                        font-weight: bold;
                        text-decoration: none;
                    }

                    td.error-log-content {
                        background-color: #333333;
                        border: 1px solid #C8C8C8;
                        padding: 10px;
                    }

                    pre.error-log-cause {
                        color: #F5F5F5;
                        background-color: #C0424A;
                        font-family: "Source Code Pro", Menlo, Monaco, Consolas, "Courier New", monospace;
                        font-size: 10px;
                        margin: 0px 0px 0px 0px;
                        overflow-wrap: break-word;
                        text-decoration: none;
                        word-break: break-all;
                        word-wrap: break-word;
                    }

                    pre.error-log-content {
                        color: #F5F5F5;
                        font-family: "Source Code Pro", Menlo, Monaco, Consolas, "Courier New", monospace;
                        font-size: 10px;
                        margin: 0px 0px 0px 0px;
                        overflow-wrap: break-word;
                        text-decoration: none;
                        word-break: break-all;
                        word-wrap: break-word;
                    }
        """
    }
}
