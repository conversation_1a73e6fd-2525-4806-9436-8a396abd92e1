package all

sectionedView('Maintenance') {
    sections {
        listView {
            name('Seed job')
            width('FULL')
            alignment('CENTER')
            jobs {
                regex('seed')
            }
            columns {
                status()
                weather()
                name()
                lastSuccess()
                lastFailure()
                lastDuration()
                buildButton()
            }
        }
        listView {
            name('VM maintenance jobs')
            width('FULL')
            alignment('CENTER')
            jobs {
                regex('.*(avalanche|p4_clean|reboot|clean_agent).*')
            }
            columns {
                status()
                weather()
                name()
                lastSuccess()
                lastFailure()
                lastDuration()
            }
        }
        listView {
            name('Sync jobs')
            width('FULL')
            alignment('CENTER')
            jobs {
                regex('.*(sync|warm|polling).*')
            }
            columns {
                status()
                weather()
                name()
                lastSuccess()
                lastFailure()
                lastDuration()
            }
        }
        listView {
            name('Build deleter job')
            width('FULL')
            alignment('CENTER')
            jobs {
                regex('.*(utility).*')
            }
            columns {
                status()
                weather()
                name()
                lastSuccess()
                lastFailure()
                lastDuration()
            }
        }
        listView {
            name('Build register job')
            width('FULL')
            alignment('CENTER')
            jobs {
                regex('.*(register_release_candidate).*')
            }
            columns {
                status()
                weather()
                name()
                lastSuccess()
                lastFailure()
                lastDuration()
            }
        }
        listView {
            name('Master maintenance jobs')
            width('FULL')
            alignment('CENTER')
            jobs {
                regex('.*(cleanup|shutdown|delete|offline|upsize|downsize|orphaned|controller).*')
            }
            columns {
                status()
                weather()
                name()
                lastSuccess()
                lastFailure()
                lastDuration()
            }
        }
        listView {
            name('ETL jobs')
            width('FULL')
            alignment('CENTER')
            jobs {
                regex('.*cobra-etl')
            }
            columns {
                status()
                weather()
                name()
                lastSuccess()
                lastFailure()
                lastDuration()
            }
        }
        listView {
            name('Vault jobs')
            width('FULL')
            alignment('CENTER')
            jobs {
                regex('.*(vault|store.baseline).*')
            }
            columns {
                status()
                weather()
                name()
                lastSuccess()
                lastFailure()
                lastDuration()
            }
        }
    }
}
