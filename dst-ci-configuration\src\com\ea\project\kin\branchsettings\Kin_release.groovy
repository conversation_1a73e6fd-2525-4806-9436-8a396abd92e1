package com.ea.project.kin.branchsettings

import com.ea.project.kin.Kingston

class Kin_release {
    // Settings for jobs
    static Class project = Kingston
    static Map general_settings = [
        dataset           : project.dataset,
        frostbite_licensee: project.frostbite_licensee,
        elipy_install_call: project.elipy_install_call,
        elipy_call        : project.elipy_call,
        workspace_root    : project.workspace_root,
    ]
    static Map standard_jobs_settings = [
        asset                                 : 'ShippingLevels',
        clean_local                           : true,
        denuvo_exclusion_path                 : 'TnT\\Build\\DenuvoExclusionList',
        denuvo_wrapping                       : true,
        enable_eac                            : true,
        enable_eac_frosty_win64_digital_retail: true,
        enable_lkg_p4_counters                : true,
        extra_data_args                       : ['--pipeline-args -Pipeline.AbortOnPipelineError --pipeline-args true '],
        extra_datapreflight_args              : ' --pipeline-args -Pipeline.AbortOnPipelineError --pipeline-args true ',
        extra_patchdata_args                  : ' --pipeline-args -Pipeline.AbortOnPipelineError --pipeline-args true ',
        keep_intermediate_data                : true,
        fake_ooa_wrapped_symbol               : true,
        frosty_asset                          : 'Game/Setup/Build/ReleaseLooseFileLevels',
        frosty_reference_job                  : 'kin-release.patchdata.start',
        linux_docker_images                   : false,
        marvin_trigger_upload_and_test        : false,
        offsite_code_token                    : '<EMAIL>:1180acfb166f9612424e10e603d75acc0e',
        offsite_drone_basic_builds            : true,
        offsite_drone_builds                  : true,
        offsite_job_remote                    : 'http://dice-la-jenkins-tools.la.ad.ea.com:8080/job/GetNewDrone_kin-release/buildWithParameters?token=2Zm67RaPGVd6^&code_changelist=%code_changelist%^&cause=%BUILD_URL%^&share_root=\\\\filer.dice.ad.ea.com\\Builds\\Kingston\\code\\kin-release',
        poolbuild_data                        : true,
        statebuild_patchdata                  : false,
        remote_masters_to_receive_code        : [
            [name: 'kin-preflight-jenkins.cobra.dre.ea.com', allow_failure: false],
            [name: 'aws-kin-staging.dre.dice.se', allow_failure: true],
        ],
        // autotest_remote_settings               : [
        //     p4_code_server            : 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
        //     p4_code_creds             : 'dice-p4buildedge03-fb',
        // ],
        remote_masters_to_receive_data        : [[name: 'kin-preflight-jenkins.cobra.dre.ea.com', allow_failure: false]],
        retry_limit_patchdata                 : 1,
        server_asset                          : 'ShippingLevels',
        shift_branch                          : true,
        shift_every_build                     : true,
        shift_reference_job                   : 'kin-release.patchfrosty.start',
        skip_frosty_trigger                   : true,
        skip_icepick_settings_file            : true,
        slack_channel_code                    : [
            channels                  : ['#kin-build-notify'],
            skip_for_multiple_failures: true,
        ],
        slack_channel_data                    : [
            channels                  : ['#kin-build-notify'],
            skip_for_multiple_failures: true,
        ],
        slack_channel_patchdata               : [
            channels                  : ['#kin-build-notify'],
            skip_for_multiple_failures: true,
        ],
        slack_channel_patchfrosty             : [
            channels                  : ['#kin-build-notify', '#earo-frosty-notify'],
            skip_for_multiple_failures: true,
        ],
        timeout_hours_data                    : 5,
        timeout_hours_patchdata               : 11,
        timeout_hours_frosty                  : 12,
        use_win64trial                        : true,
        webexport_branch                      : true,
    ]
    static Map preflight_settings = [
        concurrent_code                  : 1,
        concurrent_data                  : 1,
        use_icepick_test                 : true,
        pre_preflight                    : true,
        statebuild_datapreflight         : false,
        statebuild_codepreflight         : false,
        slack_channel_preflight          : [channels: ['#cobra-build-preflight']],
        datapreflight_reference_job      : 'kin-release.data.lastknowngood',
        trigger_type                     : 'cron',
        trigger_string_pre_preflight_data: 'H */12 * * 1-5\nH 6-23 * * 6-7',
        prepreflight_idle_length         : '60', // minutes idling before to run pre-preflight
        p4_code_server                   : 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
        p4_code_creds                    : 'dice-p4buildedge03-fb',
    ]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'win64game', configs: ['final', 'retail', 'performance']],
        [name: 'win64trial', configs: ['final', 'retail']],
        [name: 'ps4', configs: ['final', 'retail', 'performance']],
        [name: 'xb1', configs: ['final', 'retail', 'performance']],
        [name: 'win64server', configs: ['final']],
        [name: 'linux64server', configs: ['final']],
        [name: 'tool', configs: ['release']],
        [name: 'ps5', configs: ['final', 'retail', 'performance']],
        [name: 'xbsx', configs: ['final', 'retail', 'performance']],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = [
        [name: '.patchdata.start', args: []],
        [name: '.data.start', args: []],
    ]
    static List data_matrix = [
        'server',
        'win64',
        'ps4',
        'xb1',
        'ps5',
        'xbsx',
    ]
    static List data_downstream_matrix = [
        [name: '.data.lastknowngood', args: ['code_changelist', 'data_changelist']],
    ]
    static List patchdata_matrix = [
        'win64',
        'ps4',
        'xb1',
        'ps5',
        'xbsx',
    ]
    static List patchdata_downstream_matrix = [
        [name: '.patchfrosty.start', args: []],
    ]
    static List frosty_matrix = []
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = [
        [name: 'win64', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs performance']]],
        [name: 'ps4', variants: [[format: 'files', config: 'final', region: 'eu', args: ' --additional-configs performance'],
                                 [format: 'files', config: 'final', region: 'na', args: ' --additional-configs performance']]],
        [name: 'xb1', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs performance']]],
        [name: 'ps5', variants: [[format: 'files', config: 'final', region: 'eu', args: ' --additional-configs performance'],
                                 [format: 'files', config: 'final', region: 'na', args: ' --additional-configs performance']]],
        [name: 'xbsx', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs performance']]],
        [name: 'server', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww', args: '']]],
    ]
    static List patchfrosty_matrix = [
        [name: 'win64', variants: [[format: 'digital', config: 'final', region: 'ww', args: ''],
                                   [format: 'digital', config: 'retail', region: 'ww', args: '']]],
        [name: 'ps4', variants: [[format: 'digital', config: 'final', region: 'eu', args: ''],
                                [format: 'digital', config: 'retail', region: 'eu', args: ''],
                                 [format: 'digital', config: 'final', region: 'na', args: ''],
                                 [format: 'digital', config: 'retail', region: 'na', args: '']]],
        [name: 'xb1', variants: [[format: 'digital', config: 'final', region: 'ww', args: ''],
                                 [format: 'digital', config: 'retail', region: 'ww', args: '']]],
        [name: 'ps5', variants: [[format: 'digital', config: 'final', region: 'eu', args: ''],
                                 [format: 'digital', config: 'retail', region: 'eu', args: ''],
                                 [format: 'digital', config: 'final', region: 'na', args: ''],
                                 [format: 'digital', config: 'retail', region: 'na', args: '']]],
        [name: 'xbsx', variants: [[format: 'digital', config: 'final', region: 'ww', args: ''],
                                  [format: 'digital', config: 'retail', region: 'ww', args: '']]],
    ]
    static List patchfrosty_downstream_matrix = [
        [name: '.spin.linuxserver.digital.final.ww', args: ['code_changelist', 'data_changelist']],
    ]
    static List code_preflight_matrix = [
        [name: 'win64game', configs: ['retail']],
        [name: 'ps4', configs: ['release']],
        [name: 'xb1', configs: ['final']],
        [name: 'xbsx', configs: ['retail']],
        [name: 'ps5', configs: ['retail']],
        [name: 'win64server', configs: ['final']],
        [name: 'linux64server', configs: ['release']],
        [name: 'tool', configs: ['release'], sync_code_and_data: true],
    ]
    static List data_preflight_matrix = [
        [name: 'server', platform: 'server', assets: ['PreflightLevels'], extra_label: ''],
        [name: 'xb1', platform: 'xb1', assets: ['PreflightLevels'], extra_label: ''],
        [
            name          : 'win64',
            platform      : 'win64',
            p4_code_server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',  // for LibCommon to use different server
            p4_data_server: 'p4-tunguska-build01.dice.ad.ea.com:2001', // for LibCommon to use different server
            p4_code_creds : 'dice-p4buildedge03-fb', // for LibSCM to use different server
            p4_data_creds : 'perforce-tunguska-kingston01', // for LibSCM to use different server
            assets        : ['PreflightLevels'], extra_label: '',
        ],
        [
            name          : 'ps5',
            platform      : 'ps5',
            p4_code_server: 'dice-p4buildedge03-fb.dice.ad.ea.com:2001', // for LibCommon to use different server
            p4_data_server: 'p4-tunguska-build01.dice.ad.ea.com:2001', // for LibCommon to use different server
            p4_code_creds : 'dice-p4buildedge03-fb', // for LibSCM to use different server
            p4_data_creds : 'perforce-tunguska-kingston01', // for LibSCM to use different server
            assets        : ['PreflightLevels'], extra_label: '',
        ],
    ]
    static List spin_upload_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww']]],
    ]
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
