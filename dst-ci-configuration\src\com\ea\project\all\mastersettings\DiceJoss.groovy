package com.ea.project.all.mastersettings

import com.ea.project.all.All
import com.ea.project.kin.Kingston

class DiceJoss {
    static Class project = All
    static Map branches = [:]
    static Map preflight_branches = [
        'lab.kin-dev': [project: Kingston, code_folder: 'dev', code_branch: 'kin-dev', data_folder: 'dev', data_branch: 'kin-dev'],
    ]
    static Map autotest_branches = [:]
    static Map integrate_branches = [:]
    static Map copy_branches = [:]
    static Map feature_integrations = [:]
    static Map maintenance_branch = [:]
    static List dashboard_list = []
    static Map dvcs_configs = [:]
    static Map MAINTENANCE_SETTINGS = [
        IS_PRODUCTION: false,
    ]
}
