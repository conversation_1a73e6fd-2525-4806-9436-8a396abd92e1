package com.ea.project.fb1.mastersettings

class Fb1 {
    static Class project = com.ea.project.fb1.Fb1Battlefieldgame
    static Map branches = [
        'dev-na-battlefieldgame'            : [
            project                : project,
            code_folder            : 'fbstream',
            code_branch            : 'dev-na-dice-next-build',
            data_folder            : 'fbstream',
            data_branch            : 'dev-na-dice-next-build-data',
            non_virtual_code_branch: 'dev-na',
            non_virtual_data_branch: '//fblicensee/battlefield/dev-na-bf-data',
            koala_branch           : true
        ],
        'dev-na-battlefieldgame-asan'       : [
            project                : project,
            code_folder            : 'fbstream',
            code_branch            : 'dev-na-dice-next-build-asan',
            data_folder            : 'fbstream',
            data_branch            : 'dev-na-dice-next-build-data',
            non_virtual_code_branch: 'dev-na',
            non_virtual_data_branch: '//fblicensee/battlefield/dev-na-bf-data',
            koala_branch           : false
        ],
        'dev-na-battlefieldgame-first-patch': [
            project                : project,
            code_folder            : 'fbstream',
            code_branch            : 'dev-na-dice-next-build',
            data_folder            : 'fbstream',
            data_branch            : 'dev-na-dice-next-build-data-first-patch',
            non_virtual_code_branch: 'dev-na',
            non_virtual_data_branch: '//fblicensee/battlefield/dev-na-bf-data',
            koala_branch           : true
        ],
        '2024_1_dev-bf'                     : [
            project                : project,
            code_folder            : 'fbstream',
            code_branch            : '2024_1_dev-bf_code-only',
            data_folder            : 'fbstream',
            data_branch            : '2024_1_dev-bf_bfdata-only',
            non_virtual_code_branch: '2024_1_dev-bf',
            non_virtual_data_branch: '2024_1_dev-bf',
            koala_branch           : false
        ],
    ]
    static Map preflight_branches = [
        '2024_1_dev-bf': [
            code_folder: 'fbstream',
            code_branch: '2024_1_dev-bf_code-only',
            data_folder: 'fbstream',
            data_branch: '2024_1_dev-bf_bfdata-only'
        ],
    ]
    static Map autotest_branches = [
        'dev-na-battlefieldgame'     : [
            code_branch         : 'dev-na-dice-next-build',
            code_folder         : 'fbstream',
            data_branch         : 'dev-na-dice-next-build-data',
            data_folder         : 'fbstream',
            project             : project,
            set_integration_info: [
                remote_jenkins: 'bct-dev-jenkins.cobra.dre.ea.com',
                remote_job    : 'dev-na.autotest-to-integration.code',
                test_category : 'lkg_auto',
            ],
            statebuild_autotest : true,
            poolbuild_autotest  : true
        ],
        'dev-na-battlefieldgame-asan': [
            code_branch        : 'dev-na-dice-next-build-asan',
            code_folder        : 'fbstream',
            data_branch        : 'dev-na-dice-next-build-data',
            data_folder        : 'fbstream',
            project            : project,
            statebuild_autotest: true,
            poolbuild_autotest : true
        ],
        '2024_1_dev-bf'              : [
            code_branch        : '2024_1_dev-bf_code-only',
            code_folder        : 'fbstream',
            data_branch        : '2024_1_dev-bf_bfdata-only',
            data_folder        : 'fbstream',
            project            : project,
            statebuild_autotest: true,
            poolbuild_autotest : true
        ],
    ]
    static Map integrate_branches = [:]
    static Map copy_branches = [:]
    static Map feature_integrations = [:]
    static Map maintenance_branch = [
        'dev-na-battlefieldgame': [
            project    : project,
            code_folder: 'fbstream',
            code_branch: 'dev-na-dice-next-build',
            data_folder: 'fbstream',
            data_branch: 'dev-na-dice-next-build-data',
        ],
    ]
    static List dashboard_list = [
        'dev-na-battlefieldgame',
        '2024_1_dev-bf',
    ]
    static Map dvcs_configs = [:]
    static List code_downstream_matrix = []
    static Map MAINTENANCE_SETTINGS = [:]
}
