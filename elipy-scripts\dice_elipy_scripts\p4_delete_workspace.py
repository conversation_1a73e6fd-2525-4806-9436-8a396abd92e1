"""
p4_delete_workspace.py
"""
import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import LOGGER, p4
from elipy2.exceptions import ELIPYException
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command("p4_delete_workspace", short_help="Performs a deletion of perforce workspace.")
@click.option("--codeport", required=True, help="Perforce code server specification")
@click.option("--codeclient", required=True)
@click.option("--dataport", required=True, help="Perforce data server specification")
@click.option("--dataclient", required=True)
@click.option("--user", required=True)
@click.option("--codeworkspace", required=True, help="code workspace name")
@click.option("--dataworkspace", required=True, help="data workspace name")
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(_, codeport, codeclient, dataport, dataclient, user, codeworkspace, dataworkspace):
    """
    Performs a deletion of perforce workspace
    """
    # adding sentry tags
    add_sentry_tags(__file__)
    try:
        if codeport != dataport or codeworkspace != dataworkspace:
            LOGGER.info("Perform deletion {0} on data perforce server!".format(dataworkspace))
            perforce = p4.P4Utils(port=dataport, client=dataclient, user=user)
            perforce.delete_workspace(workspace_name=dataworkspace)
        LOGGER.info("Perform deletion {0} on code perforce server!".format(codeworkspace))
        perforce = p4.P4Utils(port=codeport, client=codeclient, user=user)
        perforce.delete_workspace(workspace_name=codeworkspace)
    except ELIPYException:
        raise ELIPYException("Failed to delete workspace")
    LOGGER.info("Done!")
