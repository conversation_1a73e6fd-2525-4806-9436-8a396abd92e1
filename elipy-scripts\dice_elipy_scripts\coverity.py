"""
coverity.py
"""
import os

import click

from elipy2.artifactory_client import ArtifactoryClient
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics
from elipy2 import LOGGER, running_processes, code, frostbite_core, core, secrets
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from dice_elipy_scripts.utils.frosty_build_utils import authenticate_eapm_credstore


@click.command(
    "coverity",
    short_help="Run coverity tool.",
    context_settings=dict(ignore_unknown_options=True),
)
@click.option("--code-branch", required=True, help="Perforce branch/stream name.")
@click.option("--code-changelist", required=True, help="Which code changelist to use.")
@click.option("--config", default="release", help="Config")
@click.option("--clean", type=click.BOOL, default=False, help="Delete TnT/Local")
@click.option(
    "--artifactory-user",
    required=True,
    help="Artifactory user for fetching the Coverity binaries.",
)
@click.option(
    "--artifactory-apikey",
    required=True,
    help="Artifactory API key for fetching the Coverity binaries.",
)
@click.option(
    "--artifactory-coverity-source-path",
    required=True,
    help="The source path to the Coverity binaries on Artifactory.",
)
@click.option(
    "--coverity-user",
    required=True,
    help="Coverity server username.",
)
@click.option(
    "--coverity-password",
    required=True,
    help="Coverity server password.",
)
@click.option(
    "--clean-coverity-client",
    default=False,
    type=click.BOOL,
    help="Force re-download the Coverity client",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    code_branch,
    code_changelist,
    config,
    clean,
    artifactory_user,
    artifactory_apikey,
    artifactory_coverity_source_path,
    coverity_user,
    coverity_password,
    clean_coverity_client,
):
    """
    Run Coverity tool.
    :return:
    """
    LOGGER.info("Running Coverity...")

    # adding sentry tags
    add_sentry_tags(__file__, "coverity")

    # Create builder
    tool_builder = code.CodeUtils(
        platform="tool",
        config=config,
        monkey_build_label=code_changelist,
    )
    builder = code.CodeUtils(
        platform="win64game",
        config=config,
        monkey_build_label=code_changelist,
    )

    # Delete previous TnT\Local
    if clean:
        builder.clean_local(close_handles=True)

    if coverity_password and coverity_user:
        authenticate_eapm_credstore(coverity_password, coverity_user)

    # gensln tool,win64game -NoMaster
    kwargs = {"framework_args": ["-D:nant.p4protocol.timeout.ms=240000"], "nomaster": False}
    tool_builder.gensln(**kwargs)
    builder.gensln(**kwargs)

    target_directory = os.path.join(frostbite_core.get_game_root(), "Coverity")
    artifactory_client = ArtifactoryClient(artifactory_user, artifactory_apikey)
    download_directory = artifactory_client.download_artifact(
        artifactory_coverity_source_path, target_directory, force_download=clean_coverity_client
    )

    coverity_bin_path = os.path.join(download_directory, "bin")
    coverity_config_path = os.path.join(download_directory, "config", "coverity_config.xml")
    env_patch = {"PATH": os.environ.get("PATH") + f";{coverity_bin_path}"}

    secrets.get_secrets({"build_type": "coverity"})
    core.robocopy(frostbite_core.get_game_data_dir(), coverity_bin_path, extra_args=["license.dat"])

    LOGGER.info("Generating Coverity config to %s.", coverity_config_path)
    generate_config_cmd = [
        "cov-configure",
        "--config",
        coverity_config_path,
        "--msvc",
    ]
    core.run(cmd=generate_config_cmd, print_std_out=True, env_patch=env_patch)

    LOGGER.info("Building solution")
    build_cmd = [
        "cov-build",
        "--disable-ms-pch",
        "--no-log-server",
        "--dir",
        os.path.join(frostbite_core.get_tnt_root(), "Local", "coverity-out"),
        "fb",
        "buildsln",
        "win64",
        config,
        # "rebuild",
    ]
    core.run(cmd=build_cmd, print_std_out=True, env_patch=env_patch)

    LOGGER.info("Importing SCM data")
    import_scm_cmd = [
        "cov-import-scm",
        "--dir",
        os.path.join(frostbite_core.get_tnt_root(), "Local", "coverity-out"),
        "--scm",
        "perforce",
        "--filename-regex",
        "TnT",
        "--verbose",
        "0",
    ]
    core.run(cmd=import_scm_cmd, print_std_out=True, env_patch=env_patch)

    LOGGER.info("Starting to analyze data")
    analyze_cmd = [
        "cov-analyze",
        "-t",
        os.path.join(".", "temp"),
        "-j",
        "auto",
        "--dir",
        os.path.join(frostbite_core.get_tnt_root(), "Local", "coverity-out"),
        "--security",
        "--concurrency",
        "--enable-callgraph-metrics",
        "--paths",
        "100000",
        "--all",
        "--enable-constraint-fpp",
        "--strip-path",
        "/dev/na",
        "--disable-parse-warnings",
    ]
    core.run(cmd=analyze_cmd, print_std_out=True, env_patch=env_patch)

    LOGGER.info("Committing defects to Coverity Server")
    defects_env_patch = dict(env_patch)
    defects_env_patch["COVERITY_USER"] = coverity_user
    defects_env_patch["COVERITY_PASSWORD"] = coverity_password
    commit_defects_cmd = [
        "cov-commit-defects",
        "--dir",
        os.path.join(frostbite_core.get_tnt_root(), "Local", "coverity-out"),
        "--description",
        f"FB-eac-freeze-{code_branch}",
        "--scm",
        "perforce",
        "--url",
        "https://dice-coverity.dice.ad.ea.com",
        "--user",
        "%COVERITY_USER%",
        "--password",
        "%COVERITY_PASSWORD%",
        "--stream",
        f"Frostbite.{code_branch}.win64.nightly",
    ]
    core.run(cmd=commit_defects_cmd, print_std_out=True, env_patch=defects_env_patch)

    running_processes.kill()
    LOGGER.info("Done running Coverity.")
