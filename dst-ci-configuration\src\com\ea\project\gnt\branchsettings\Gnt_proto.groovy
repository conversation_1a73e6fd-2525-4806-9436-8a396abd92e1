package com.ea.project.gnt.branchsettings

import com.ea.lib.jobsettings.ShiftSettings

class Gnt_proto {
    // Settings for jobs
    static Class project = com.ea.project.gnt.Granite
    static Map general_settings = [
        dataset           : project.dataset,
        frostbite_licensee: project.frostbite_licensee,
        elipy_install_call: project.elipy_install_call,
        elipy_call        : project.elipy_call,
        workspace_root    : project.workspace_root,
    ]
    static Map code_settings = [
        deploy_frostedtests    : true,
        deploy_tests           : true,
        fake_ooa_wrapped_symbol: false,
        slack_channel_code     : [
            channels                  : ['#gnt-proto-integrations'],
            skip_for_multiple_failures: true,
        ],
        use_snowcache          : false,
        statebuild_code_list   : ['tool'],
    ]
    static Map data_settings = [
        slack_channel_data     : [
            channels                  : ['#gnt-proto-integrations'],
            skip_for_multiple_failures: true,
        ],
        enable_lkg_cleaning    : true,
        poolbuild_data         : true,
        webexport_branch       : false,
        webexport_allow_failure: true,
    ]
    static Map frosty_settings = [
        slack_channel_data: [
            channels                  : ['#gnt-proto-integrations'],
            skip_for_multiple_failures: true,
        ],
        poolbuild_frosty  : true,
    ]
    static Map standard_jobs_settings = code_settings + data_settings + frosty_settings + [
        asset                     : 'Granite/GraniteLevels',
        baseline_set              : false,
        enable_lkg_p4_counters    : true,
        linux_docker_images       : false,
        server_asset              : 'Granite/GraniteLevels',
        shift_branch              : true,
        shift_reference_job       : 'gnt-proto.frosty.start',
        skip_icepick_settings_file: true,
        trigger_string_shift      : 'TZ=America/Los_Angeles \n H 1,14 * * 1-6\nH 6,13 * * 7',
        trigger_type_shift        : 'cron',
        timeout_hours_data        : 6,
        //remote_masters_to_receive_code      : [[name: 'res-gnt-dev.dre.dice.se', allow_failure: false]],
        //remote_masters_to_receive_data      : [[name: 'res-gnt-dev.dre.dice.se', allow_failure: false]],
    ]
    static Map preflight_settings = [:]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'win64game', configs: ['final', 'retail']],
        [name: 'tool', configs: [[name: 'release', compile_unit_tests: true, run_unit_tests: true],]],
        [name: 'win64server', configs: ['final']],
        [name: 'xbsx', configs: ['final', 'retail']],
        [name: 'ps5', configs: ['final', 'retail']],
        [name: 'linux64server', configs: ['final']],
    ]
    static List code_nomaster_matrix = [
        [name: 'win64game', configs: ['retail']],
        [name: 'win64server', configs: ['final']],
        [name: 'ps5', configs: ['final']],
        [name: 'xbsx', configs: ['retail']],
        [name: 'linux64server', configs: ['final']],
        [name: 'tool', configs: ['release']],
    ]
    static List code_downstream_matrix = [
        [name: '.data.start', args: ['code_changelist']],
        [name: '.code.lastknowngood', args: ['code_changelist']],
        [name: '.code.p4counterupdater', args: ['code_changelist', 'code_countername']],
    ]
    static List data_matrix = [
        [name: 'win64'],
        [name: 'server'],
        [name: 'xbsx'],
        [name: 'ps5'],
    ]
    static List data_downstream_matrix = [
        [name: '.frosty.start', args: []],
        [name: '.data.lastknowngood', args: ['code_changelist', 'data_changelist']],
        [name: '.data.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
    ]
    static List patchdata_matrix = []
    static List frosty_matrix = [
        [name: 'win64', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'ps5', variants: [[format: 'files', config: 'final', region: 'dev', args: '']]],
        [name: 'xbsx', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'server', variants: [[format: 'digital', config: 'final', region: 'ww', args: '']]],
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww', args: '']]],
    ]
    static List frosty_downstream_matrix = [
        [name: '.frosty.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
    ]
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = []
    static List shift_upload_matrix = [
        [shifter_type: ShiftSettings.SHIFTER_TYPE_OFFSITE_BASIC_DRONE, args: ['code_changelist']],
    ]
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = [
        [upstream_job: '.bilbo.register-Data-dronebuild', downstream_job: ".shift.${ShiftSettings.SHIFTER_TYPE_OFFSITE_BASIC_DRONE}.start", args: ['code_changelist']],
    ]
    static List azure_uploads_matrix = []
}
