package all

import com.ea.lib.LibCommonCps
import com.ea.lib.LibCommonNonCps
import com.ea.lib.LibJobDsl
import com.ea.lib.LibScm
import com.ea.lib.jobs.LibCode
import com.ea.lib.jobs.LibData
import com.ea.lib.jobs.LibMaintenance
import com.ea.project.GetBranchFile
import com.ea.project.GetMasterFile
import com.ea.project.all.mastersettings.DiceUpgradeValidator
import com.ea.project.kin.mastersettings.DiceKinDev

GetMasterFile.get_masterfile(BUILD_URL).each { masterSettings ->
    def branches = masterSettings.branches
    def project = masterSettings.project

    if (masterSettings == DiceKinDev) {
        def p4_code_creds = LibCommonNonCps.get_setting_value([:], [], 'p4_code_creds', '', project)

        job('trigger-on.upgrade-validator') {
            description('Test job to trigger a job remotely on dice-upgrade-validator.')
            label('master')
            logRotator(7, 100)
            quietPeriod(0)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies which code changelist that will be sent to dice-upgrade-validator.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies which data changelist that will be sent to dice-upgrade-validator.')
                    trim(true)
                }
            }
            wrappers {
                timestamps()
                buildName('${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}')
                timeout {
                    absolute(60)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                remoteTrigger('dice-upgrade-validator.dre.dice.se', 'trigger-from.dice-kin-dev') {
                    parameter('code_changelist', '${code_changelist}')
                    parameter('data_changelist', '${data_changelist}')
                    overrideCredentials(p4_code_creds) // Any valid credential should work.
                    shouldNotFailBuild(true)
                    preventRemoteBuildQueue(false)
                    pollInterval(10)
                    blockBuildUntilComplete(true)
                }
            }
        }

        job('trigger-from.upgrade-validator') {
            description('Test job triggered remotely from dice-upgrade-validator.')
            label('master')
            logRotator(7, 100)
            quietPeriod(0)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies which code changelist that will be sent to dice-upgrade-validator.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies which data changelist that will be sent to dice-upgrade-validator.')
                    trim(true)
                }
            }
            wrappers {
                timestamps()
                buildName('${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}')
                timeout {
                    absolute(60)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
        }
    }

    if (masterSettings == DiceUpgradeValidator) {
        branches.each { current_branch, info ->
            project = info.project

            def branchfile = GetBranchFile.get_branchfile(project.name, current_branch)
            def general_settings = branchfile.general_settings
            def standard_jobs_settings = branchfile.standard_jobs_settings
            def branch_info = info + general_settings + standard_jobs_settings + [branch_name: current_branch, project: project]
            def p4_code_creds = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_code_creds', '', project)
            def freestyle_jobs = []

            // Jobs to test if triggering on Perforce checkins work.
            def code_trigger = pipelineJob(current_branch + '.code-trigger') {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/testjobs/code_trigger_scheduler.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibCode.code_start(code_trigger, project, branch_info)

            def data_trigger = pipelineJob(current_branch + '.data-trigger') {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/testjobs/data_trigger_scheduler.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibData.data_start(data_trigger, project, branch_info)

            // Jobs to test remote triggering.
            job('trigger-on.dice-kin-dev') {
                description('Test job to trigger a job remotely on dice-cas-dev.')
                label('master')
                logRotator(7, 100)
                quietPeriod(0)
                parameters {
                    stringParam {
                        name('code_changelist')
                        defaultValue('')
                        description('Specifies which code changelist that will be sent to dice-cas-dev.')
                        trim(true)
                    }
                    stringParam {
                        name('data_changelist')
                        defaultValue('')
                        description('Specifies which data changelist that will be sent to dice-cas-dev.')
                        trim(true)
                    }
                }
                wrappers {
                    timestamps()
                    buildName('${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}')
                    timeout {
                        absolute(60)
                        failBuild()
                        writeDescription('Build failed due to timeout after {0} minutes')
                    }
                }
                steps {
                    remoteTrigger('kin-dev-jenkins.cobra.dre.ea.com', 'trigger-from.upgrade-validator') {
                        parameter('code_changelist', '${code_changelist}')
                        parameter('data_changelist', '${data_changelist}')
                        overrideCredentials(p4_code_creds) // Any valid credential should work.
                        shouldNotFailBuild(true)
                        preventRemoteBuildQueue(false)
                        pollInterval(10)
                        blockBuildUntilComplete(true)
                    }
                }
            }

            job('trigger-from.dice-kin-dev') {
                description('Test job triggered remotely from dice-cas-dev. Also used to get changelists for the integration job on dre-triggering and the cas-upgrade jobs.')
                label('master')
                logRotator(7, 100)
                quietPeriod(0)
                parameters {
                    stringParam {
                        name('code_changelist')
                        defaultValue('')
                        description('Specifies which code changelist that will be sent to dice-cas-dev.')
                        trim(true)
                    }
                    stringParam {
                        name('data_changelist')
                        defaultValue('')
                        description('Specifies which data changelist that will be sent to dice-cas-dev.')
                        trim(true)
                    }
                }
                wrappers {
                    timestamps()
                    buildName('${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}')
                    timeout {
                        absolute(60)
                        failBuild()
                        writeDescription('Build failed due to timeout after {0} minutes')
                    }
                }
            }

            job('trigger-from.dice-jenkins') {
                description('Test job triggered remotely from dice-jenkins. Also used to get changelists for the wa-upgrade jobs.')
                label('master')
                logRotator(7, 100)
                quietPeriod(0)
                parameters {
                    stringParam {
                        name('code_changelist')
                        defaultValue('')
                        description('Specifies which code changelist that will be sent to dice-cas-dev.')
                        trim(true)
                    }
                    stringParam {
                        name('data_changelist')
                        defaultValue('')
                        description('Specifies which data changelist that will be sent to dice-cas-dev.')
                        trim(true)
                    }
                }
                wrappers {
                    timestamps()
                    buildName('${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}')
                    timeout {
                        absolute(60)
                        failBuild()
                        writeDescription('Build failed due to timeout after {0} minutes')
                    }
                }
            }

            // Jobs to test sending emails after a finished job.
            pipelineJob('email-testing.start') {
                description('Test sending an email after a finished job.')
                logRotator(7, 100)
                quietPeriod(0)
                properties {
                    disableConcurrentBuilds()
                    disableResume()
                }
                parameters {
                    stringParam {
                        name('unshelve_changelist')
                        defaultValue('1234')
                        description('Changelist to unshelve.')
                        trim(true)
                    }
                    stringParam {
                        name('preflighter')
                        defaultValue('DICE\\bvaksdal')
                        description('Name of the user triggering the job.')
                        trim(true)
                    }
                    choiceParam('success_report', ['true', 'false'], 'If true, send an email with success report. If false, send an email with failure report.')
                }
                environmentVariables {
                    env('branch_name', current_branch)
                    env('project_name', project.name)
                }
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/testjobs/email_testing_scheduler.groovy'))
                        sandbox(true)
                    }
                }
            }

            def jobs_to_report = [
                'job1',
                'job2',
                'job3',
                'job4',
            ]

            for (job_to_report in jobs_to_report) {
                job('email-testing.' + job_to_report) {
                    description('Job that runs so the email-testing.start has results to report.')
                    label('master')
                    logRotator(7, 100)
                    quietPeriod(0)
                    wrappers {
                        timestamps()
                        buildName('${JOB_NAME}.${BUILD_NUMBER}')
                    }
                    if (job_to_report == 'job3' || job_to_report == 'job4') {
                        steps {
                            batchFile('ECHO testjob finished')
                        }
                    }
                }
            }

            project.p4_code_servers.each { name, p4_port ->
                def clean_codestream = job('p4_clean_codestream.' + name + '.' + project.name + '.' + branch_info.code_branch) {}
                freestyle_jobs.add(clean_codestream)
                LibMaintenance.p4_clean_codestream_job(clean_codestream, project, branch_info, p4_port)
                LibScm.sync_code(clean_codestream, project, branch_info)
                LibJobDsl.postclean_silverback(clean_codestream, project, branch_info)
                LibJobDsl.addVaultSecrets(clean_codestream, branch_info)
            }

            for (data_clean_job in project.p4_data_servers) {
                String dataset = data_clean_job.dataset ?: project.dataset
                def clean_datastream = job('p4_clean_datastream.' + data_clean_job.name + '.' + project.name + '.' + branch_info.data_branch) {}
                freestyle_jobs.add(clean_datastream)
                LibMaintenance.p4_clean_datastream_job(clean_datastream, project, branch_info as Map, data_clean_job.p4_port, dataset)
                LibScm.sync_code(clean_datastream, project, branch_info)
                LibJobDsl.postclean_silverback(clean_datastream, project, branch_info)
                LibJobDsl.addVaultSecrets(clean_datastream, branch_info)
            }

            LibCommonCps.add_downstream_freestyle_job_triggers(freestyle_jobs, current_branch, branchfile.freestyle_job_trigger_matrix)
        }

        def maintenanceBranches = masterSettings.maintenance_branch
        maintenanceBranches.each { String currentBranch, info ->
            project = info.project
            def branchFile = GetBranchFile.get_branchfile(project.name, currentBranch)
            def generalSettings = branchFile.general_settings
            def standardJobsSettings = branchFile.standard_jobs_settings
            def branchInfo = info + generalSettings + standardJobsSettings + [branch_name: currentBranch]
            def freestyle_jobs = []
            def avalancheMaintenanceStart = pipelineJob('avalanche_maintenance.start') {
                environmentVariables {
                    env('project_name', project.name)
                }
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/avalanche_maintenance_upgrade.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibMaintenance.avalanche_maintenance_start(avalancheMaintenanceStart, project, branchFile, masterSettings, currentBranch)

            def avalancheMaintenanceJob = job('avalanche_maintenance_' + project.short_name) {}
            freestyle_jobs.add(avalancheMaintenanceJob)
            LibMaintenance.avalanche_maintenance_job(avalancheMaintenanceJob, branchFile, project, masterSettings, currentBranch)
            LibScm.sync_code(avalancheMaintenanceJob, project, branchInfo)
            LibJobDsl.initialP4revert(avalancheMaintenanceJob, project, branchInfo, true, false)
            LibJobDsl.archive_non_build_logs(avalancheMaintenanceJob, branchInfo)
            LibJobDsl.postclean_silverback(avalancheMaintenanceJob, project, branchInfo)
            LibJobDsl.addVaultSecrets(avalancheMaintenanceJob, branchInfo)

            LibCommonCps.add_downstream_freestyle_job_triggers(freestyle_jobs, currentBranch, branchFile.freestyle_job_trigger_matrix)
        }
    }
}
