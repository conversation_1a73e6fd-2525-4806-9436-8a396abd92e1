{#
    Command:
        vault
            short_help: Vaults a given build.

    Arguments:

    Required variables:
        vault_type
            help: build or symbols
            required: True
        platform
            help: Platform for vaulting.
            required: True
            type: click.Choice(SUPPORTED_PLATFORMS + ['all'])

    Optional variables:
        source
            default: None
            help: Fully qualified source path.
        destination
            default: None
            help: Fully qualified destination path.
        build_url
            required: False
            help: Used to store vaulting logs. the URL of the currently running build.
        build_id
            required: False
            help: Used to store vaulting logs. the UID of the currently running build.
        code_branch
            help: Branch/stream to fetch the code/binary build from.
        code_changelist
            help: Changelist of binaries to fetch.
        data_branch
            help: Branch/stream that data is coming from.
        data_changelist
            help: Changelist of data being used.
        expression_debug_data
            is_flag: True
            help: Export expression debug data for vault.
        version
            help: Version to store under.
        shift_user
            help: User for Shift authentication
            default: None
        shift_password
            help: Password for Shift authentication.
            default: None
        use_elipy_shift_config
            is_flag: True
            help: Use shift elipy config instead of shift template.
        archive_builds_in_shift
            is_flag: True
            help: Archive builds in shift.
        md5_validation
            is_flag: True
            help: Validate vault with md5.
        win64_trial/__no_win64_trial
            default: True
            help: Include by default win64-trial platform on symbol.
        include_servers
            default: true
            help: DEPRECATED
        verify_post_vault
            is_flag: True
            help: Verifies that files have been properly vaulted in                   accordance with the vault_validation_config yaml file.
        vault_layout
            default: default
            help: Targets a specific vault_validation_config in the case where multiple configurations               are defined in the vault_validation_config yaml file
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    vault
    --vault-type {{ vault_type }}
    --platform {{ platform }}
    {%- if source %}
    --source {{ source }}
    {%- endif %}
    {%- if destination %}
    --destination {{ destination }}
    {%- endif %}
    {%- if build_url %}
    --build-url {{ build_url }}
    {%- endif %}
    {%- if build_id %}
    --build-id {{ build_id }}
    {%- endif %}
    {%- if code_branch %}
    --code-branch {{ code_branch }}
    {%- endif %}
    {%- if code_changelist %}
    --code-changelist {{ code_changelist }}
    {%- endif %}
    {%- if data_branch %}
    --data-branch {{ data_branch }}
    {%- endif %}
    {%- if data_changelist %}
    --data-changelist {{ data_changelist }}
    {%- endif %}
    {%- if expression_debug_data %}
    --expression-debug-data {{ expression_debug_data }}
    {%- endif %}
    {%- if version %}
    --version {{ version }}
    {%- endif %}
    {%- if shift_user %}
    --shift-user {{ shift_user }}
    {%- endif %}
    {%- if shift_password %}
    --shift-password {{ shift_password }}
    {%- endif %}
    {%- if use_elipy_shift_config %}
    --use-elipy-shift-config {{ use_elipy_shift_config }}
    {%- endif %}
    {%- if archive_builds_in_shift %}
    --archive-builds-in-shift {{ archive_builds_in_shift }}
    {%- endif %}
    {%- if md5_validation %}
    --md5-validation {{ md5_validation }}
    {%- endif %}
    {%- if win64_trial/__no_win64_trial %}
    --win64-trial/--no-win64-trial {{ win64_trial/__no_win64_trial }}
    {%- endif %}
    {%- if include_servers %}
    --include-servers {{ include_servers }}
    {%- endif %}
    {%- if verify_post_vault %}
    --verify-post-vault {{ verify_post_vault }}
    {%- endif %}
    {%- if vault_layout %}
    --vault-layout {{ vault_layout }}
    {%- endif %}
  displayName: elipy vault
