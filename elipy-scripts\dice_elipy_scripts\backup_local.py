"""
backup_local.py
"""
import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import core, LOGGER, local_paths
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics
from elipy2.exceptions import ELIPYException


@click.command(
    "backup_local",
    short_help="Backup tnt/local that will be used to troubleshoot failed build later.",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(_):
    """
    Backup tnt/local that will be used to troubleshoot failed build later,

    The copied build will be deleted/overwritten for the next failed build.
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    source = local_paths.get_tnt_local_path()
    if source is None:
        raise ELIPYException("TnT folder cannot be found!!")
    dest = r"D:\backup_local"
    LOGGER.info("Copying local from {0} to {1} ...".format(source, dest))
    core.robocopy(source, dest, purge=True)
