package com.ea.project.kin.branchsettings

class Kin_live {
    // Settings for jobs
    static Class project = com.ea.project.kin.Kingston
    static Map general_settings = [
        dataset           : project.dataset,
        frostbite_licensee: project.frostbite_licensee,
        elipy_install_call: project.elipy_install_call,
        elipy_call        : project.elipy_call,
        workspace_root    : project.workspace_root,
    ]
    static Map standard_jobs_settings = [
        asset                     : 'ShippingLevels',
        clean_local               : true,
        denuvo_wrapping           : true,
        enable_eac                : true,
        fake_ooa_wrapped_symbol   : true,
        frosty_asset              : 'Game/Setup/Build/ReleaseLooseFileLevels',
        frosty_reference_job      : 'kin-live.patchdata.start',
        linux_docker_images       : false,
        poolbuild_data            : true,
        poolbuild_patchdata       : true,
        retry_limit_patchdata     : 1,
        server_asset              : 'ShippingLevels',
        shift_branch              : true,
        shift_every_build         : true,
        shift_reference_job       : 'kin-live.patchfrosty.start',
        skip_frosty_trigger       : true,
        slack_channel_code        : [
            channels                  : ['#kin-build-notify'],
            skip_for_multiple_failures: true,
        ],
        slack_channel_data        : [
            channels                  : ['#kin-build-notify'],
            skip_for_multiple_failures: true,
        ],
        slack_channel_patchdata   : [
            channels                  : ['#kin-build-notify'],
            skip_for_multiple_failures: true,
        ],
        slack_channel_patchfrosty : [
            channels                  : ['#kin-build-notify'],
            skip_for_multiple_failures: true,
        ],
        timeout_hours_patchdata   : 11,
        use_win64trial            : true,
        webexport_branch          : true,
    ]
    static Map preflight_settings = [
        p4_code_server             : 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
        p4_code_creds              : 'dice-p4buildedge03-fb',
    ]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'win64game', configs: ['final', 'retail']],
        [name: 'win64trial', configs: ['final', 'retail']],
        [name: 'ps4', configs: ['final', 'retail']],
        [name: 'xb1', configs: ['final', 'retail']],
        [name: 'win64server', configs: ['final']],
        [name: 'linux64server', configs: ['final']],
        [name: 'tool', configs: ['release']],
        [name: 'ps5', configs: ['final', 'retail']],
        [name: 'xbsx', configs: ['final', 'retail']],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = [
        [name: '.data.start', args: []],
        [name: '.patchdata.start', args: []],
    ]
    static List data_matrix = [
        'server',
        'win64',
        'ps4',
        'xb1',
        'ps5',
        'xbsx',
    ]
    static List data_downstream_matrix = []
    static List patchdata_matrix = [
        'win64',
        'ps4',
        'xb1',
        'ps5',
        'xbsx',
    ]
    static List patchdata_downstream_matrix = [
        [name: '.patchfrosty.start', args: []],
    ]
    static List frosty_matrix = []
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = [
        [name: 'win64', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'ps4', variants: [[format: 'files', config: 'final', region: 'eu', args: ''],
                                 [format: 'files', config: 'final', region: 'na', args: '']]],
        [name: 'xb1', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'ps5', variants: [[format: 'files', config: 'final', region: 'eu', args: ''],
                                 [format: 'files', config: 'final', region: 'na', args: '']]],
        [name: 'xbsx', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'server', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww', args: '']]],
    ]
    static List patchfrosty_matrix = [
        [name: 'win64', variants: [[format: 'digital', config: 'final', region: 'ww', args: ''],
                                   [format: 'digital', config: 'retail', region: 'ww', args: '']]],
        [name: 'ps4', variants: [[format: 'digital', config: 'final', region: 'eu', args: ''],
                                 [format: 'digital', config: 'retail', region: 'eu', args: ''],
                                 [format: 'digital', config: 'final', region: 'na', args: ''],
                                 [format: 'digital', config: 'retail', region: 'na', args: '']]],
        [name: 'xb1', variants: [[format: 'digital', config: 'final', region: 'ww', args: ''],
                                 [format: 'digital', config: 'retail', region: 'ww', args: '']]],
        [name: 'ps5', variants: [[format: 'digital', config: 'final', region: 'eu', args: ''],
                                 [format: 'digital', config: 'retail', region: 'eu', args: ''],
                                 [format: 'digital', config: 'final', region: 'na', args: ''],
                                 [format: 'digital', config: 'retail', region: 'na', args: '']]],
        [name: 'xbsx', variants: [[format: 'digital', config: 'final', region: 'ww', args: ''],
                                  [format: 'digital', config: 'retail', region: 'ww', args: '']]],
    ]
    static List patchfrosty_downstream_matrix = [
        [name: '.spin.linuxserver.digital.final.ww', args: ['code_changelist', 'data_changelist']],
    ]
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww']]],
    ]
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
