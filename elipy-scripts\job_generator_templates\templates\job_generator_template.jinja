{#- <PERSON>ja Template that generates Job Generator ready step templates based on Elipy scripts click info -#}

{#- Macro to generate doc based on the info gathered from the elipy script click decorators -#}
{% macro generate_doc(click_elements) -%}
    {%- for click_element in click_elements %}
        {{ click_element.args[0].removeprefix("--").replace("-","_") }}
        {%- for key, value in click_element.kwargs.items() %}
            {%- if value is string %}
                {%- set value = value if value != "" else "''" -%}
                {%- set value = value.strip() -%}
            {% endif %}
            {{ key }}: {{ value if value != "" else "''" }}
        {%- endfor -%}
    {%- endfor %}
{%- endmacro -%}

{%- set click_command = click_elements | selectattr('function','eq','command') | list -%}
{%- set click_arguments = click_elements | selectattr('function','eq','argument') | list -%}
{%- set click_options = click_elements | selectattr('function','eq','option') | list -%}
{%- set click_options_required = click_options | selectattr('kwargs.required') | list -%}
{%- set click_options_optional = click_options | rejectattr('kwargs.required','equalto',True) | list -%}

{#- Generate template docstring based on the elipy script click decorators -#}
{{ "{#" }}
    Command: {{- generate_doc(click_command) }}

    Arguments: {{- generate_doc(click_arguments) }}

    Required variables: {{- generate_doc(click_options_required) }}

    Optional variables: {{- generate_doc(click_options_optional) }}
{{ "#}" }}

{% raw -%}
- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
{%- endraw %}

{#- TODO: get cli args dynamically -#}
{% raw %}
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
{%- endraw %}
    {{ click_command[0].args[0] -}}

{% for click_argument in click_arguments %}
    {{ click_argument.args[0] + " {{ " + click_argument.args[0].replace("-","_") + " }}" -}}
{% endfor -%}

{% for click_option in click_options_required %}
    {{ click_option.args[0] + " {{ " + click_option.args[0].removeprefix("--").replace("-","_") + " }}" -}}
{% endfor -%}

{% for click_option in click_options_optional %}
    {{ "{%- if " + click_option.args[0].removeprefix("--").replace("-","_") + " %}" }}
    {{ click_option.args[0] + " {{ " + click_option.args[0].removeprefix("--").replace("-","_") + " }}" }}
    {{ "{%- endif %}" }}
{%- endfor %}
  displayName: elipy {{ click_command[0].args[0] }}

