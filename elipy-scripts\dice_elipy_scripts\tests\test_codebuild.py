"""
test_codebuild.py

Unit testing for codebuild
"""
import os
import pytest
import unittest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import ANY, call, MagicMock, patch
from dice_elipy_scripts.codebuild import cli, code_build
from elipy2.config import ConfigManager
from elipy2.exceptions import BrokenSymbol, ELIPYException

config_path = os.path.join(os.path.dirname(__file__), "data", "elipy_test.yml")
config_manager = ConfigManager(path=config_path)

FAKE_PATH = "\\\\mytest.ea.com\\some\\path"


@patch("dice_elipy_scripts.codebuild.add_sentry_tags", MagicMock())
@patch("dice_elipy_scripts.codebuild.download_outsource_dependencies", MagicMock())
@patch("elipy2.build_metadata.BuildMetadataManager", MagicMock())
@patch("dice_elipy_scripts.codebuild.prepare_outsource_dependencies", MagicMock())
@patch("dice_elipy_scripts.codebuild.throw_if_files_found", MagicMock())
@patch("elipy2.core.clean_temp", MagicMock())
@patch("elipy2.core.close_file_handles", MagicMock())
@patch("elipy2.filer_paths.get_code_build_path", MagicMock(return_value=FAKE_PATH))
@patch(
    "elipy2.local_paths.get_local_build_path",
    MagicMock(return_value="C:\\local\\build\\path"),
)
@patch(
    "elipy2.frostbite_core.minimum_fb_version",
    MagicMock(return_value=False),
)
@patch("elipy2.running_processes.kill", MagicMock())
@patch("elipy2.telemetry.collect_metrics", MagicMock())
@patch("elipy2.telemetry.upload_metrics", MagicMock())
class TestCodeBuildCli(unittest.TestCase):
    ARGUMENT_PLATFORM_1 = "win64"
    ARGUMENT_PLATFORM_2 = "tool"
    ARGUMENT_PLATFORM_3 = "ps5"
    ARGUMENT_CONFIG_1 = "retail"
    ARGUMENT_CONFIG_2 = "deprecation-test"
    ARGUMENT_CONFIG_3 = "final"
    ARGUMENT_CONFIG_4 = "steam"

    OPTION_CODE_BRANCH = "--code-branch"
    OPTION_CODE_CHANGELIST = "--code-changelist"
    OPTION_P4_PORT = "--p4-port"
    OPTION_P4_CLIENT = "--p4-client"
    OPTION_P4_USER = "--p4-user"
    OPTION_FRAMEWORK_ARGS = "--framework-args"
    OPTION_MSBUILD_ARGS = "--msbuild-args"
    OPTION_CLEAN = "--clean"
    OPTION_OREANS_PROTECTION = "--oreans-protection"
    OPTION_OREANS_CONFIG = "--oreans-config"
    OPTION_DENUVO_WRAPPING = "--denuvo-wrapping"
    OPTION_DENUVO_EXCLUSION = "--denuvo-exclusion"
    OPTION_ARTIFACTORY_USER = "--artifactory-user"
    OPTION_ARTIFACTORY_APIKEY = "--artifactory-apikey"
    OPTION_NOMASTER = "--nomaster"
    OPTION_WSL = "--wsl"
    OPTION_DRY_RUN = "--dry-run"
    OPTION_DEPLOY_LOCAL = "--deploy-local"
    OPTION_IMPORT_LOCAL = "--import-local"
    OPTION_SKIP_DEPLOY_TNT = "--skip-deploy-tnt"
    OPTION_DEPLOY_HOUDINI = "--deploy-houdini"
    OPTION_ALL_TESTS = "--alltests"
    OPTION_DEPLOY_TESTS = "--deploy-tests"
    OPTION_DEPLOY_FROSTEDTESTS = "--deploy-frostedtests"
    OPTION_DATA_DIRECTORY = "--data-directory"
    OPTION_SKIP_SYMBOLS_BACKUP = "--skip-symbols-backup"
    OPTION_SKIP_SYMBOLS_TO_SYMSTORE = "--skip-symbols-to-symstore"
    OPTION_COMPRESS_SYMBOLS = "--compress-symbols"
    OPTION_STRIP_SYMBOLS = "--strip-symbols"
    OPTION_ICEPICK_TEST = "--icepick-test"
    OPTION_OVERRIDE_DO_NOT_RUN_CODE_UNITTESTS = "--override-do-not-run-code-unittests"
    OPTION_IGNORE_ICEPICK_EXIT_CODE = "--ignore-icepick-exit-code"
    OPTION_SETTINGS_FILES = "--settings-files"
    OPTION_LICENSEE = "--licensee"
    OPTION_PASSWORD = "--password"
    OPTION_EMAIL = "--email"
    OPTION_FAKE_OOA_WRAPPED_SYMBOL = "--fake-ooa-wrapped-symbol"
    OPTION_USE_STATE_ZIP = "--use-state-zip"
    OPTION_USE_SNOWCACHE = "--use-snowcache"
    OPTION_SNOWCACHE_MODE_OVERRIDE = "--snowcache-mode-override"
    OPTION_GENSLN_CONFIG = "--gensln-config"
    OPTION_BUILDLAYOUT_CONFIG = "--buildlayout-config"
    OPTION_ICEPICK_EXTRA_FRAMEWORK_ARGS = "--icepick-extra-framework-args"
    OPTION_ICEPICK_RUN_ARGS = "--icepick-run-args"
    OPTION_CLEAN_PACKAGES = "--clean-packages"
    OPTION_IS_OUTSOURCE_BUILD = "--is-outsource-build"
    OPTION_ONLY_GENSLN = "--only-gensln"
    OPTION_TOOL_TARGETS = "--tool-targets"
    OPTION_custom_tag = "--custom-tag"
    OPTION_COPY_TO_KOBOLD_INBOX = "--copy-symbols-to-kobold-inbox"
    OPTION_FB_ENV_VALUES = "--fb-env-values"
    OPTION_VIRTUAL_BRANCH_OVERRIDE = "--virtual-branch-override"

    VALUE_ALL_TESTS = "true"
    VALUE_CODE_BRANCH = "code_branch"
    VALUE_CODE_CHANGELIST = "1234"
    VALUE_P4_PORT = "p4_port"
    VALUE_P4_CLIENT = "p4_client"
    VALUE_P4_USER = "p4_user"
    VALUE_FRAMEWORK_ARGS = "arg1"
    VALUE_MSBUILD_ARGS = "arg2"
    VALUE_CLEAN = "true"
    VALUE_OREANS_PROTECTION = "false"
    VALUE_OREANS_CONFIG = ""
    VALUE_DENUVO_WRAPPING = "false"
    VALUE_DENUVO_EXCLUSION = "denuvo_exclusion_path"
    VALUE_ARTIFACTORY_USER = "artifactory_user"
    VALUE_ARTIFACTORY_APIKEY = "artifactory_apikey"
    VALUE_NOMASTER = "true"
    VALUE_WSL = "true"
    VALUE_DRY_RUN = "true"
    VALUE_IMPORT_LOCAL = "true"
    VALUE_SKIP_DEPLOY_TNT = "true"
    VALUE_DEPLOY_TESTS = "true"
    VALUE_DEPLOY_FROSTEDTESTS = "true"
    VALUE_DATA_DIRECTORY = "data_directory"
    VALUE_ICEPICK_TEST = "icepick_test"
    VALUE_OVERRIDE_DO_NOT_RUN_CODE_UNITTESTS = False
    VALUE_SKIP_SYMBOLS_BACKUP = "true"
    VALUE_SKIP_SYMBOLS_TO_SYMSTORE = "true"
    VALUE_COMPRESS_SYMBOLS = "false"
    VALUE_STRIP_SYMBOLS = "false"
    VALUE_IGNORE_ICEPICK_EXIT_CODE = "false"
    VALUE_SETTINGS_FILES = "settings_file"
    VALUE_LICENSEE = "licensee_name"
    VALUE_PASSWORD = "pass_word"
    VALUE_EMAIL = "e_mail"
    VALUE_FAKE_OOA_WRAPPED_SYMBOL = "true"
    VALUE_USE_STATE_ZIP = "true"
    VALUE_USE_SNOWCACHE = "true"
    VALUE_GENSLN_CONFIG = "final"
    VALUE_BUILDLAYOUT_CONFIG = "final"
    VALUE_ICEPICK_EXTRA_FRAMEWORK_ARGS = "icepick_extra_args"
    VALUE_ONLY_GENSLN = "false"
    VALUE_CLEAN_PACKAGES = "true"
    VALUE_IS_OUTSOURCE_BUILD = "true"
    VALUE_DEFAULT_TOOL_TARGETS = ["pipeline", "frosted", "win64-dll"]
    VALUE_ARTIFACT_TEST = "Tests"
    VALUE_custom_tag = "test_folder"
    VALUE_COPY_TO_KOBOLD_INBOX = "True"
    VALUE_FB_ENV_VALUES_1 = "key1=gensln:value1"
    VALUE_FB_ENV_VALUES_2 = "key1=buildsln:value2"
    VALUE_VIRTUAL_BRANCH_OVERRIDE = "true"

    FIXED_ARGS = [
        OPTION_CODE_BRANCH,
        VALUE_CODE_BRANCH,
        OPTION_CODE_CHANGELIST,
        VALUE_CODE_CHANGELIST,
        OPTION_P4_PORT,
        VALUE_P4_PORT,
        OPTION_P4_CLIENT,
        VALUE_P4_CLIENT,
        OPTION_P4_USER,
        VALUE_P4_USER,
        OPTION_PASSWORD,
        VALUE_PASSWORD,
        OPTION_EMAIL,
        VALUE_EMAIL,
        OPTION_OVERRIDE_DO_NOT_RUN_CODE_UNITTESTS,
        VALUE_OVERRIDE_DO_NOT_RUN_CODE_UNITTESTS,
    ]

    BASIC_ARGS = [
        ARGUMENT_PLATFORM_1,
        ARGUMENT_CONFIG_1,
    ] + FIXED_ARGS

    DEFAULT_TOOL_TARGETS = ["pipeline", "frosted", "win64-dll"]

    def setUp(self):
        def settings_get_side_effect(key):
            if key == "snowcache_host":
                return {"win64": "host_name"}
            return key  # Hack to not care about keys that are irrelevant for the test.

        self.patcher_settings_get = patch("dice_elipy_scripts.codebuild.SETTINGS", config_manager)
        self.mock_settings_get = self.patcher_settings_get.start()

        self.patcher_import_local_code_state = patch(
            "dice_elipy_scripts.codebuild.import_local_code_state", autospec=True
        )
        self.mock_import_local_code_state = self.patcher_import_local_code_state.start()

        self.patcher_add_metadata_files = patch(
            "dice_elipy_scripts.codebuild.add_metadata_files", autospec=True
        )
        self.mock_add_metadata_files = self.patcher_add_metadata_files.start()

        self.patcher_modify_buildlayout = patch(
            "dice_elipy_scripts.codebuild.modify_buildlayout", autospec=True
        )
        self.mock_modify_buildlayout = self.patcher_modify_buildlayout.start()

        self.patcher_run_gensln = patch("dice_elipy_scripts.codebuild.run_gensln", autospec=True)
        self.mock_run_gensln = self.patcher_run_gensln.start()

        self.patcher_set_licensee = patch(
            "dice_elipy_scripts.codebuild.set_licensee", autospec=True
        )
        self.mock_set_licensee = self.patcher_set_licensee.start()
        self.mock_set_licensee.return_value = ["licensee_arg"]

        self.patcher_codeutils = patch("elipy2.code.CodeUtils", autospec=True)
        self.mock_codeutils = self.patcher_codeutils.start()

        self.patcher_delete_folder = patch("elipy2.core.delete_folder", autospec=True)
        self.mock_delete_folder = self.patcher_delete_folder.start()

        self.patcher_set_datadir = patch("elipy2.data.DataUtils.set_datadir")
        self.mock_set_datadir = self.patcher_set_datadir.start()

        self.patcher_oreans_protect = patch("elipy2.oreans.protect", autospec=True)
        self.mock_oreans_protect = self.patcher_oreans_protect.start()

        self.patcher_denuvo_wrap = patch("elipy2.denuvo.wrap", autospec=True)
        self.mock_denuvo_wrap = self.patcher_denuvo_wrap.start()

        self.patcher_filerutils = patch("elipy2.filer.FilerUtils", autospec=True)
        self.mock_filerutils = self.patcher_filerutils.start()

        self.patcher_icepickutils = patch("elipy2.frostbite.icepick.IcepickUtils", autospec=True)
        self.mock_icepickutils = self.patcher_icepickutils.start()

        self.patcher_symbolsutils = patch("elipy2.symbols.SymbolsUtils", autospec=True)
        self.mock_symbolsutils = self.patcher_symbolsutils.start()

        self.patcher_exists = patch("os.path.exists", autospec=True)
        self.mock_exists = self.patcher_exists.start()
        self.mock_exists.return_value = False

        self.patcher_fbcli_pushbuild = patch("elipy2.frostbite.fbcli.pushbuild", autospec=True)
        self.mock_fbcli_pushbuild = self.patcher_fbcli_pushbuild.start()

        # self.patcher_fbcli_run = patch("elipy2.fbcli.run")
        # self.mock_fbcli_run = self.patcher_fbcli_run.start()
        self.patcher_avalanche_upstream_server_healthy = patch(
            "dice_elipy_scripts.utils.snowcache_utils.avalanche_upstream_server_healthy",
            autospec=True,
        )
        self.mock_avalanche_upstream_server_healthy = (
            self.patcher_avalanche_upstream_server_healthy.start()
        )
        self.mock_avalanche_upstream_server_healthy.return_value = True

    def tearDown(self):
        os.environ["use_fbcli"] = "False"
        patch.stopall()

    def _helper_validate_filder_deploy_code_not_called(self):
        # check filer deploy_code is not called
        with patch("elipy2.filer.FilerUtils", self.mock_filerutils):
            self.mock_filerutils.return_value.deploy_code.assert_not_called()

    def test_basic_args(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0

    def test_clean_packages(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_CLEAN_PACKAGES, self.VALUE_CLEAN_PACKAGES]
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0

    def test_is_outsource_build(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_IS_OUTSOURCE_BUILD, self.VALUE_IS_OUTSOURCE_BUILD]
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0

    def test_rungensln_basic(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert result.exit_code == 0
        self.mock_run_gensln.assert_called_once_with(
            password=self.VALUE_PASSWORD,
            user=self.VALUE_EMAIL,
            framework_args=["licensee_arg"],
            builder=self.mock_codeutils.return_value,
            alltests=False,
            nomaster=False,
            wsl=False,
            gensln_config=None,
            domain_user=None,
            stressbulkbuild=False,
        )

    def test_rungensln_only(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_ONLY_GENSLN, True])
        assert result.exit_code == 0
        self.mock_run_gensln.assert_called_once_with(
            password=self.VALUE_PASSWORD,
            user=self.VALUE_EMAIL,
            framework_args=["licensee_arg"],
            builder=self.mock_codeutils.return_value,
            alltests=False,
            nomaster=False,
            wsl=False,
            gensln_config=None,
            domain_user=None,
            stressbulkbuild=False,
        )
        self.mock_codeutils.return_value.buildsln.assert_not_called()

    def test_rungensln_only_wsl(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS + [self.OPTION_ONLY_GENSLN, True] + [self.OPTION_WSL, self.VALUE_WSL],
        )
        assert result.exit_code == 0
        self.mock_run_gensln.assert_called_once_with(
            password=self.VALUE_PASSWORD,
            user=self.VALUE_EMAIL,
            framework_args=["licensee_arg"],
            builder=self.mock_codeutils.return_value,
            alltests=False,
            nomaster=False,
            wsl=True,
            gensln_config=None,
            domain_user=None,
            stressbulkbuild=False,
        )
        self.mock_codeutils.return_value.buildsln.assert_not_called()

    def test_rungensln_broken_symbols(self):
        self.mock_symbolsutils.return_value.verify_symbol_integrity.side_effect = BrokenSymbol()
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        self.mock_run_gensln.assert_called_with(
            password=self.VALUE_PASSWORD,
            user=self.VALUE_EMAIL,
            framework_args=["licensee_arg"],
            builder=self.mock_codeutils.return_value,
            alltests=False,
            nomaster=False,
            wsl=False,
            gensln_config=None,
            domain_user=None,
            stressbulkbuild=False,
        )
        self.mock_run_gensln.call_count == 2

    def test_snowcache_not_used_if_avalanche_server_not_healthy(self):
        runner = CliRunner()

        self.mock_avalanche_upstream_server_healthy.return_value = False

        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_USE_SNOWCACHE, self.VALUE_USE_SNOWCACHE]
        )
        framework_args = self.mock_run_gensln.call_args[1].get("framework_args")

        assert result.exit_code == 0
        assert not any(["snowcache" in arg.lower() for arg in framework_args])

    def test_rungensln_snowcache(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_USE_SNOWCACHE, self.VALUE_USE_SNOWCACHE]
        )
        assert result.exit_code == 0
        expected_snowcache_host = config_manager.get("snowcache_host")[
            TestCodeBuildCli.ARGUMENT_PLATFORM_1
        ]
        self.mock_run_gensln.assert_called_once_with(
            password=self.VALUE_PASSWORD,
            user=self.VALUE_EMAIL,
            framework_args=[
                "licensee_arg",
                f"-G:package.SnowCache.avalancheHost={expected_snowcache_host}",
                "-D:eaconfig.optimization.ltcg=off",
                "-G:package.SnowCache.mode=uploadanddownload",
            ],
            builder=self.mock_codeutils.return_value,
            alltests=False,
            nomaster=False,
            wsl=False,
            gensln_config=None,
            domain_user=None,
            stressbulkbuild=False,
        )

    def test_rungensln_broken_symbols_snowcache(self):
        self.mock_symbolsutils.return_value.verify_symbol_integrity.side_effect = BrokenSymbol()
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_USE_SNOWCACHE, self.VALUE_USE_SNOWCACHE]
        )

        expected_snowcache_host = config_manager.get("snowcache_host")[
            TestCodeBuildCli.ARGUMENT_PLATFORM_1
        ]
        self.mock_run_gensln.assert_called_with(
            password=self.VALUE_PASSWORD,
            user=self.VALUE_EMAIL,
            framework_args=[
                "licensee_arg",
                f"-G:package.SnowCache.avalancheHost={expected_snowcache_host}",
                "-D:eaconfig.optimization.ltcg=off",
                "-G:package.SnowCache.mode=forceupload",
            ],
            builder=self.mock_codeutils.return_value,
            alltests=False,
            nomaster=False,
            wsl=False,
            gensln_config=None,
            domain_user=None,
            stressbulkbuild=False,
        )
        self.mock_run_gensln.call_count == 2

    def test_rungensln_with_snowcache_mode_override(self):
        for clean, snowcache_mode_override, expected_snowcache_mode in [
            ("true", "upload", "upload"),
            ("true", "forceupload", "forceupload"),
            ("true", "uploadanddownload", "uploadanddownload"),
            ("true", "", "forceupload"),
            ("false", "", "uploadanddownload"),
        ]:
            with self.subTest(
                msg="Assert snowcache mode '{}' used if snowcache_mode_override of {} passed".format(
                    snowcache_mode_override, snowcache_mode_override
                )
            ):
                runner = CliRunner()

                SNOWCACHE_ARGS = [
                    "--clean",
                    clean,
                    self.OPTION_USE_SNOWCACHE,
                    self.VALUE_USE_SNOWCACHE,
                ]

                if snowcache_mode_override:
                    SNOWCACHE_ARGS = SNOWCACHE_ARGS + [
                        "--snowcache-mode-override",
                        snowcache_mode_override,
                    ]

                result = runner.invoke(cli, self.BASIC_ARGS + SNOWCACHE_ARGS)

                expected_snowcache_mode_arg = "-G:package.SnowCache.mode={}".format(
                    expected_snowcache_mode
                )
                assert result.exit_code == 0
                assert (
                    expected_snowcache_mode_arg
                    in self.mock_run_gensln.call_args[-1]["framework_args"]
                )

    def test_snowcache_arg_cleans_appropriately(self):
        for use_snowcache, clean_value, snowcache_mode, clean_local_call_count in [
            (True, "true", "upload", 1),
            (True, "false", "upload", 0),
            (False, "true", "upload", 1),
            (False, "false", "upload", 0),
            (None, "true", "upload", 1),
            (None, "false", "upload", 0),
            (True, "true", "uploadanddownload", 1),
            (True, "false", "uploadanddownload", 1),
            (False, "true", "uploadanddownload", 1),
            (False, "false", "uploadanddownload", 0),
            (None, "true", "uploadanddownload", 1),
            (None, "false", "uploadanddownload", 0),
            (True, "true", "forceupload", 1),
            (True, "false", "forceupload", 1),
            (False, "true", "forceupload", 1),
            (False, "false", "forceupload", 0),
            (None, "true", "forceupload", 1),
            (None, "false", "forceupload", 0),
        ]:
            with self.subTest(
                msg="Assert builder.clean_local called {} times".format(clean_local_call_count)
            ):
                mock_clean_local = self.mock_codeutils.return_value.clean_local = MagicMock()
                runner = CliRunner()

                SNOWCACHE_ARGS = [
                    "--clean",
                    clean_value,
                ]
                if use_snowcache:
                    SNOWCACHE_ARGS += [self.OPTION_USE_SNOWCACHE, self.VALUE_USE_SNOWCACHE]
                if snowcache_mode:
                    SNOWCACHE_ARGS += [self.OPTION_SNOWCACHE_MODE_OVERRIDE, snowcache_mode]

                result = runner.invoke(cli, self.BASIC_ARGS + SNOWCACHE_ARGS)

                assert result.exit_code == 0
                assert mock_clean_local.call_count == clean_local_call_count

    def test_rungensln_is_outsource_build(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_IS_OUTSOURCE_BUILD, self.VALUE_IS_OUTSOURCE_BUILD]
        )
        assert result.exit_code == 0
        self.mock_run_gensln.assert_called_once_with(
            password=self.VALUE_PASSWORD,
            user=self.VALUE_EMAIL,
            framework_args=[
                "licensee_arg",
                "-G:frostbite.pipeline.disable-platform-sdks=true",
                "-G:useProxyPackages=true",
                "-G:frostbite.usePrebuiltPackages=true",
                "-G:frostbite.is-outsource-build=true",
                "-G:disablePackageServer=true",
            ],
            builder=self.mock_codeutils.return_value,
            alltests=False,
            nomaster=False,
            wsl=False,
            gensln_config=None,
            domain_user=None,
            stressbulkbuild=False,
        )

    def test_rungensln_broken_symbols_is_outsource_build(self):
        self.mock_symbolsutils.return_value.verify_symbol_integrity.side_effect = BrokenSymbol()
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_IS_OUTSOURCE_BUILD, self.VALUE_IS_OUTSOURCE_BUILD]
        )
        self.mock_run_gensln.assert_called_with(
            password=self.VALUE_PASSWORD,
            user=self.VALUE_EMAIL,
            framework_args=[
                "licensee_arg",
                "-G:frostbite.pipeline.disable-platform-sdks=true",
                "-G:useProxyPackages=true",
                "-G:frostbite.usePrebuiltPackages=true",
                "-G:frostbite.is-outsource-build=true",
                "-G:disablePackageServer=true",
            ],
            builder=self.mock_codeutils.return_value,
            alltests=False,
            nomaster=False,
            wsl=False,
            gensln_config=None,
            domain_user=None,
            stressbulkbuild=False,
        )
        self.mock_run_gensln.call_count == 2

    def test_rungensln_deprecation_test(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, [self.ARGUMENT_PLATFORM_1, self.ARGUMENT_CONFIG_2] + self.FIXED_ARGS
        )
        assert result.exit_code == 0
        self.mock_run_gensln.assert_called_with(
            password=self.VALUE_PASSWORD,
            user=self.VALUE_EMAIL,
            framework_args=[
                "licensee_arg",
                "-G:frostbite.ignoreDeprecation=false",
                "-G:frostbite.disableForwardHeader=true",
            ],
            builder=self.mock_codeutils.return_value,
            alltests=False,
            nomaster=False,
            wsl=False,
            gensln_config=None,
            domain_user=None,
            stressbulkbuild=False,
        )

    def test_rungensln_steam(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, [self.ARGUMENT_PLATFORM_1, self.ARGUMENT_CONFIG_4] + self.FIXED_ARGS
        )
        assert result.exit_code == 0
        self.mock_run_gensln.assert_called_with(
            password=self.VALUE_PASSWORD,
            user=self.VALUE_EMAIL,
            framework_args=[
                "licensee_arg",
                "-G:steam=true",
            ],
            builder=self.mock_codeutils.return_value,
            alltests=False,
            nomaster=False,
            wsl=False,
            gensln_config=None,
            domain_user=None,
            stressbulkbuild=False,
        )

    def test_rungensln_separate_genlsn_config(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_GENSLN_CONFIG, self.VALUE_GENSLN_CONFIG]
        )
        assert result.exit_code == 0
        self.mock_run_gensln.assert_called_once_with(
            password=self.VALUE_PASSWORD,
            user=self.VALUE_EMAIL,
            framework_args=["licensee_arg"],
            builder=self.mock_codeutils.return_value,
            alltests=False,
            nomaster=False,
            wsl=False,
            gensln_config=self.VALUE_GENSLN_CONFIG,
            domain_user=None,
            stressbulkbuild=False,
        )

    def test_data_directory(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_DATA_DIRECTORY, self.VALUE_DATA_DIRECTORY]
        )
        assert result.exit_code == 0
        self.mock_set_datadir.assert_called_once_with("data_directory")

    def test_modify_buildlayout(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_BUILDLAYOUT_CONFIG, self.VALUE_BUILDLAYOUT_CONFIG]
        )
        assert result.exit_code == 0
        self.mock_modify_buildlayout.assert_called_once_with(
            self.VALUE_BUILDLAYOUT_CONFIG, [], self.ARGUMENT_PLATFORM_1, self.ARGUMENT_CONFIG_1
        )

    def test_modify_buildlayout_licensee(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_BUILDLAYOUT_CONFIG,
                self.VALUE_BUILDLAYOUT_CONFIG,
                self.OPTION_LICENSEE,
                self.VALUE_LICENSEE,
            ],
        )
        assert result.exit_code == 0
        self.mock_modify_buildlayout.assert_called_once_with(
            self.VALUE_BUILDLAYOUT_CONFIG,
            [self.VALUE_LICENSEE],
            self.ARGUMENT_PLATFORM_1,
            self.ARGUMENT_CONFIG_1,
        )

    def test_add_metadata_files(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert result.exit_code == 0
        self.mock_add_metadata_files.assert_called_once_with(
            self.mock_filerutils.return_value,
            self.VALUE_CODE_BRANCH,
            self.VALUE_CODE_CHANGELIST,
            nomaster=False,
        )

    def test_add_metadata_files_nomaster(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_NOMASTER, self.VALUE_NOMASTER])
        assert result.exit_code == 0
        assert not self.mock_add_metadata_files.called

    def test_backup_symbols(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert result.exit_code == 0
        self.mock_symbolsutils.return_value.backup_symbols.assert_called_once_with(
            branch=self.VALUE_CODE_BRANCH,
            changelist=self.VALUE_CODE_CHANGELIST,
            platform=self.ARGUMENT_PLATFORM_1,
            config=self.ARGUMENT_CONFIG_1,
        )

    def test_skip_backup_symbols(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_SKIP_SYMBOLS_BACKUP, self.VALUE_SKIP_SYMBOLS_BACKUP]
        )
        assert result.exit_code == 0
        assert not self.mock_symbolsutils.return_value.backup_symbols.called

    def test_upload_symbols_to_sym_store(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert result.exit_code == 0
        self.mock_symbolsutils.return_value.upload_symbols_to_sym_store.assert_called_once_with(
            platform=self.ARGUMENT_PLATFORM_1,
            changelist=self.VALUE_CODE_CHANGELIST,
            product_name=self.VALUE_CODE_BRANCH + "." + self.ARGUMENT_PLATFORM_1,
            config=self.ARGUMENT_CONFIG_1,
            compress=True,
        )

    def test_upload_symbols_to_sym_store_DONT_compress(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_COMPRESS_SYMBOLS, self.VALUE_COMPRESS_SYMBOLS]
        )
        assert result.exit_code == 0
        self.mock_symbolsutils.return_value.upload_symbols_to_sym_store.assert_called_once_with(
            platform=self.ARGUMENT_PLATFORM_1,
            changelist=self.VALUE_CODE_CHANGELIST,
            product_name=self.VALUE_CODE_BRANCH + "." + self.ARGUMENT_PLATFORM_1,
            config=self.ARGUMENT_CONFIG_1,
            compress=False,
        )

    def test_skip_upload_symbols_to_sym_store(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [self.OPTION_SKIP_SYMBOLS_TO_SYMSTORE, self.VALUE_SKIP_SYMBOLS_TO_SYMSTORE],
        )
        assert result.exit_code == 0
        assert not self.mock_symbolsutils.return_value.upload_symbols_to_sym_store.called

    def test_skip_deploy_tnt_local_build(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert result.exit_code == 0
        assert not self.mock_filerutils.return_value.deploy_tnt_local_build.called

    def test_import_local(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_IMPORT_LOCAL, self.VALUE_IMPORT_LOCAL]
        )
        assert result.exit_code == 0
        self.mock_import_local_code_state.assert_called_once_with(
            self.mock_codeutils.return_value,
            self.mock_filerutils.return_value,
            self.VALUE_CODE_BRANCH,
            self.ARGUMENT_PLATFORM_1,
            self.ARGUMENT_CONFIG_1,
            False,
        )

    def test_skip_import_local(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert result.exit_code == 0
        assert not self.mock_import_local_code_state.called

    def test_icepick(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_2, self.ARGUMENT_CONFIG_1]
            + self.FIXED_ARGS
            + [
                self.OPTION_ICEPICK_TEST,
                self.VALUE_ICEPICK_TEST,
                self.OPTION_SETTINGS_FILES,
                self.VALUE_SETTINGS_FILES,
                self.OPTION_ICEPICK_EXTRA_FRAMEWORK_ARGS,
                self.VALUE_ICEPICK_EXTRA_FRAMEWORK_ARGS,
                self.OPTION_IGNORE_ICEPICK_EXIT_CODE,
                "true",
                self.OPTION_ICEPICK_RUN_ARGS,
                "test-run-arg",
            ],
        )
        assert result.exit_code == 0
        self.mock_icepickutils.return_value.run_icepick.assert_called_once_with(
            platform="win64",
            test_suite=self.VALUE_ICEPICK_TEST,
            settings_file_list=[self.VALUE_SETTINGS_FILES],
            config=self.ARGUMENT_CONFIG_1,
            ignore_icepick_exit_code=True,
            build_type="dll",
            run_args=[
                "--reporting-build-version-id",
                self.VALUE_CODE_CHANGELIST,
                "--reporting-is-monkey",
                "true",
                "--reporting-public-suite",
                "true",
                "--reporting-branch",
                self.VALUE_CODE_BRANCH,
                "--disable-ensemble-plugin",
                "true",
                "test-run-arg",
            ],
            lease=None,
            extra_framework_args=self.VALUE_ICEPICK_EXTRA_FRAMEWORK_ARGS,
        )

    def test_icepick_dont_ignore_exit_code(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_2, self.ARGUMENT_CONFIG_1]
            + self.FIXED_ARGS
            + [
                self.OPTION_ICEPICK_TEST,
                self.VALUE_ICEPICK_TEST,
                self.OPTION_SETTINGS_FILES,
                self.VALUE_SETTINGS_FILES,
                self.OPTION_ICEPICK_EXTRA_FRAMEWORK_ARGS,
                self.VALUE_ICEPICK_EXTRA_FRAMEWORK_ARGS,
                self.OPTION_IGNORE_ICEPICK_EXIT_CODE,
                self.VALUE_IGNORE_ICEPICK_EXIT_CODE,
            ],
        )
        assert result.exit_code == 0
        self.mock_icepickutils.return_value.run_icepick.assert_called_once_with(
            platform="win64",
            test_suite=self.VALUE_ICEPICK_TEST,
            settings_file_list=[self.VALUE_SETTINGS_FILES],
            config=self.ARGUMENT_CONFIG_1,
            ignore_icepick_exit_code=False,
            build_type="dll",
            run_args=[
                "--reporting-build-version-id",
                self.VALUE_CODE_CHANGELIST,
                "--reporting-is-monkey",
                "true",
                "--reporting-public-suite",
                "true",
                "--reporting-branch",
                self.VALUE_CODE_BRANCH,
                "--disable-ensemble-plugin",
                "true",
            ],
            lease=None,
            extra_framework_args=self.VALUE_ICEPICK_EXTRA_FRAMEWORK_ARGS,
        )

    def test_verify_symbol_integrity(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert result.exit_code == 0
        self.mock_symbolsutils.return_value.verify_symbol_integrity.assert_called_once_with(
            path_to_binaries="C:\\local\\build\\path"
        )

    def test_skip_verify_symbol_integrity_ps5(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, [self.ARGUMENT_PLATFORM_3, self.ARGUMENT_CONFIG_1] + self.FIXED_ARGS
        )
        assert result.exit_code == 0
        assert not self.mock_symbolsutils.return_value.verify_symbol_integrity.called

    def test_oreans_protect(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_OREANS_PROTECTION,
                True,
                self.OPTION_OREANS_CONFIG,
                self.VALUE_OREANS_CONFIG,
                self.OPTION_ARTIFACTORY_USER,
                self.VALUE_ARTIFACTORY_USER,
                self.OPTION_ARTIFACTORY_APIKEY,
                self.VALUE_ARTIFACTORY_APIKEY,
            ],
        )
        assert result.exit_code == 0
        self.mock_oreans_protect.assert_called_once_with(
            platform=self.ARGUMENT_PLATFORM_1,
            artifactory_user=self.VALUE_ARTIFACTORY_USER,
            artifactory_api_key=self.VALUE_ARTIFACTORY_APIKEY,
            oreans_config=self.VALUE_OREANS_CONFIG,
        )

    def test_denuvo_wrap(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_DENUVO_WRAPPING,
                True,
                self.OPTION_DENUVO_EXCLUSION,
                self.VALUE_DENUVO_EXCLUSION,
                self.OPTION_ARTIFACTORY_USER,
                self.VALUE_ARTIFACTORY_USER,
                self.OPTION_ARTIFACTORY_APIKEY,
                self.VALUE_ARTIFACTORY_APIKEY,
            ],
        )
        assert result.exit_code == 0
        self.mock_denuvo_wrap.assert_called_once_with(
            platform=self.ARGUMENT_PLATFORM_1,
            artifactory_user=self.VALUE_ARTIFACTORY_USER,
            artifactory_api_key=self.VALUE_ARTIFACTORY_APIKEY,
            exclusion_path=self.VALUE_DENUVO_EXCLUSION,
        )

    def test_skip_denuvo_wrap_final(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_1, self.ARGUMENT_CONFIG_3]
            + self.FIXED_ARGS
            + [self.OPTION_DENUVO_WRAPPING, self.VALUE_DENUVO_WRAPPING],
        )
        assert result.exit_code == 0
        assert not self.mock_denuvo_wrap.called

    def test_skip_denuvo_wrap_ps5(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_3, self.ARGUMENT_CONFIG_1]
            + self.FIXED_ARGS
            + [self.OPTION_DENUVO_WRAPPING, self.VALUE_DENUVO_WRAPPING],
        )
        assert result.exit_code == 0
        assert not self.mock_denuvo_wrap.called

    def test_fake_ooa_wrapped(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [self.OPTION_FAKE_OOA_WRAPPED_SYMBOL, self.VALUE_FAKE_OOA_WRAPPED_SYMBOL],
        )
        assert result.exit_code == 0
        self.mock_symbolsutils.return_value.fake_ooawrap_bin.assert_called_once_with(
            platform=self.ARGUMENT_PLATFORM_1,
            config=self.ARGUMENT_CONFIG_1,
            changelist=self.VALUE_CODE_CHANGELIST,
            branch=self.VALUE_CODE_BRANCH,
            denuvo_wrapping=False,
        )

    def test_fake_ooa_wrapped_with_denuvo(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_FAKE_OOA_WRAPPED_SYMBOL,
                self.VALUE_FAKE_OOA_WRAPPED_SYMBOL,
                self.OPTION_DENUVO_WRAPPING,
                "true",
            ],
        )
        assert result.exit_code == 0
        self.mock_symbolsutils.return_value.fake_ooawrap_bin.assert_called_once_with(
            platform=self.ARGUMENT_PLATFORM_1,
            config=self.ARGUMENT_CONFIG_1,
            changelist=self.VALUE_CODE_CHANGELIST,
            branch=self.VALUE_CODE_BRANCH,
            denuvo_wrapping=True,
        )

    def test_fake_ooa_wrapped_skip_denuvo_final(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_1, self.ARGUMENT_CONFIG_3]
            + self.FIXED_ARGS
            + [
                self.OPTION_FAKE_OOA_WRAPPED_SYMBOL,
                self.VALUE_FAKE_OOA_WRAPPED_SYMBOL,
                self.OPTION_DENUVO_WRAPPING,
                self.VALUE_DENUVO_WRAPPING,
            ],
        )
        assert result.exit_code == 0
        self.mock_symbolsutils.return_value.fake_ooawrap_bin.assert_called_once_with(
            platform=self.ARGUMENT_PLATFORM_1,
            config=self.ARGUMENT_CONFIG_3,
            changelist=self.VALUE_CODE_CHANGELIST,
            branch=self.VALUE_CODE_BRANCH,
            denuvo_wrapping=False,
        )

    def test_skip_fake_ooa_wrapped(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_3, self.ARGUMENT_CONFIG_1]
            + self.FIXED_ARGS
            + [self.OPTION_FAKE_OOA_WRAPPED_SYMBOL, self.VALUE_FAKE_OOA_WRAPPED_SYMBOL],
        )
        assert result.exit_code == 0
        assert not self.mock_symbolsutils.return_value.fake_ooawrap_bin.called

    def test_deploy_code_dry_run(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_DRY_RUN, self.VALUE_DRY_RUN])
        assert result.exit_code == 0
        assert not self.mock_filerutils.return_value.deploy_code.called

    def test_deploy_code_basic(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert result.exit_code == 0
        self.mock_filerutils.return_value.deploy_code.assert_called_once_with(
            self.VALUE_CODE_BRANCH,
            self.VALUE_CODE_CHANGELIST,
            self.ARGUMENT_PLATFORM_1,
            self.ARGUMENT_CONFIG_1,
            deploy_tnt=False,
            nomaster=False,
            fbenv_copy_args=["/XF", "flag_to_include_incremental_state"],
            use_state_zip=False,
            deploy_tests=False,
            custom_tag=None,
            deploy_frostedtests=False,
            tool_targets=ANY,
            mirror=True,
        )

    def test_deploy_code_no_mirror(self):
        runner = CliRunner()
        _args = self.BASIC_ARGS.copy()
        _args += ["--mirror", "false"]
        result = runner.invoke(cli, _args)
        assert result.exit_code == 0
        self.mock_filerutils.return_value.deploy_code.assert_called_once_with(
            self.VALUE_CODE_BRANCH,
            self.VALUE_CODE_CHANGELIST,
            self.ARGUMENT_PLATFORM_1,
            self.ARGUMENT_CONFIG_1,
            deploy_tnt=False,
            nomaster=False,
            fbenv_copy_args=["/XF", "flag_to_include_incremental_state"],
            use_state_zip=False,
            deploy_tests=False,
            custom_tag=None,
            deploy_frostedtests=False,
            tool_targets=ANY,
            mirror=False,
        )

    def test_deploy_code_custom_tag(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_custom_tag, self.VALUE_custom_tag]
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.deploy_code.assert_called_once_with(
            self.VALUE_CODE_BRANCH,
            self.VALUE_CODE_CHANGELIST,
            self.ARGUMENT_PLATFORM_1,
            self.ARGUMENT_CONFIG_1,
            deploy_tnt=False,
            nomaster=False,
            fbenv_copy_args=["/XF", "flag_to_include_incremental_state"],
            use_state_zip=False,
            deploy_tests=False,
            custom_tag=self.VALUE_custom_tag,
            deploy_frostedtests=False,
            tool_targets=ANY,
            mirror=True,
        )

    def test_deploy_code_basic_use_state_zip(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_USE_STATE_ZIP, self.VALUE_USE_STATE_ZIP]
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.deploy_code.assert_called_once_with(
            self.VALUE_CODE_BRANCH,
            self.VALUE_CODE_CHANGELIST,
            self.ARGUMENT_PLATFORM_1,
            self.ARGUMENT_CONFIG_1,
            deploy_tnt=False,
            nomaster=False,
            fbenv_copy_args=["/XF", "flag_to_include_incremental_state"],
            use_state_zip=True,
            deploy_tests=False,
            custom_tag=None,
            deploy_frostedtests=False,
            tool_targets=ANY,
            mirror=True,
        )

    def test_deploy_code_tool_basic(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, [self.ARGUMENT_PLATFORM_2, self.ARGUMENT_CONFIG_1] + self.FIXED_ARGS
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.deploy_code.assert_called_once_with(
            self.VALUE_CODE_BRANCH,
            self.VALUE_CODE_CHANGELIST,
            self.ARGUMENT_PLATFORM_2,
            self.ARGUMENT_CONFIG_1,
            deploy_tnt=True,
            nomaster=False,
            fbenv_copy_args=["/XF", "flag_to_include_incremental_state"],
            use_state_zip=False,
            deploy_tests=False,
            custom_tag=None,
            deploy_frostedtests=False,
            tool_targets=ANY,
            mirror=True,
        )

    def test_deploy_code_tool_flags(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_2, self.ARGUMENT_CONFIG_1]
            + self.FIXED_ARGS
            + [
                self.OPTION_SKIP_DEPLOY_TNT,
                self.VALUE_SKIP_DEPLOY_TNT,
                self.OPTION_ALL_TESTS,
                self.VALUE_ALL_TESTS,
                self.OPTION_DEPLOY_TESTS,
                self.VALUE_DEPLOY_TESTS,
                self.OPTION_DEPLOY_FROSTEDTESTS,
                self.VALUE_DEPLOY_FROSTEDTESTS,
            ],
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.deploy_code.assert_called_once_with(
            self.VALUE_CODE_BRANCH,
            self.VALUE_CODE_CHANGELIST,
            self.ARGUMENT_PLATFORM_2,
            self.ARGUMENT_CONFIG_1,
            deploy_tnt=False,
            nomaster=False,
            fbenv_copy_args=["/XF", "flag_to_include_incremental_state"],
            use_state_zip=False,
            deploy_tests=True,
            deploy_frostedtests=True,
            custom_tag=None,
            tool_targets=ANY,
            mirror=True,
        )

    def test_nodeploy_missingalltestsflag_code_tool_flags(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_2, self.ARGUMENT_CONFIG_1]
            + self.FIXED_ARGS
            + [
                self.OPTION_SKIP_DEPLOY_TNT,
                self.VALUE_SKIP_DEPLOY_TNT,
                self.OPTION_DEPLOY_TESTS,
                self.VALUE_DEPLOY_TESTS,
                self.OPTION_DEPLOY_FROSTEDTESTS,
                self.VALUE_DEPLOY_FROSTEDTESTS,
            ],
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.deploy_code.assert_called_once_with(
            self.VALUE_CODE_BRANCH,
            self.VALUE_CODE_CHANGELIST,
            self.ARGUMENT_PLATFORM_2,
            self.ARGUMENT_CONFIG_1,
            deploy_tnt=False,
            nomaster=False,
            fbenv_copy_args=["/XF", "flag_to_include_incremental_state"],
            use_state_zip=False,
            deploy_tests=False,
            deploy_frostedtests=False,
            custom_tag=None,
            tool_targets=ANY,
            mirror=True,
        )

    def test_deploy_code_ps5_flags(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_3, self.ARGUMENT_CONFIG_1]
            + self.FIXED_ARGS
            + [
                self.OPTION_SKIP_DEPLOY_TNT,
                self.VALUE_SKIP_DEPLOY_TNT,
                self.OPTION_ALL_TESTS,
                self.VALUE_ALL_TESTS,
                self.OPTION_DEPLOY_TESTS,
                self.VALUE_DEPLOY_TESTS,
                self.OPTION_DEPLOY_FROSTEDTESTS,
                self.VALUE_DEPLOY_FROSTEDTESTS,
            ],
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.deploy_code.assert_called_once_with(
            self.VALUE_CODE_BRANCH,
            self.VALUE_CODE_CHANGELIST,
            self.ARGUMENT_PLATFORM_3,
            self.ARGUMENT_CONFIG_1,
            deploy_tnt=False,
            nomaster=False,
            fbenv_copy_args=["/XF", "flag_to_include_incremental_state"],
            use_state_zip=False,
            deploy_tests=True,
            deploy_frostedtests=False,
            custom_tag=None,
            tool_targets=ANY,
            mirror=True,
        )

    def test_no_deploy_frostedtest_if_not_build_frosted(self):
        # no deploy frostedtest if we are not building frosted in the tool targets
        runner = CliRunner()
        extra_args = [self.OPTION_LICENSEE, self.VALUE_LICENSEE] + [
            self.OPTION_TOOL_TARGETS,
            "pipeline",
            self.OPTION_TOOL_TARGETS,
            "win64-dll",
        ]
        result = runner.invoke(
            cli,
            ["tool", "release"]
            + self.FIXED_ARGS
            + extra_args
            + [
                self.OPTION_DEPLOY_FROSTEDTESTS,
                self.VALUE_DEPLOY_FROSTEDTESTS,
            ],
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.deploy_code.assert_called_once_with(
            self.VALUE_CODE_BRANCH,
            self.VALUE_CODE_CHANGELIST,
            "tool",
            "release",
            deploy_tnt=False,
            nomaster=False,
            fbenv_copy_args=["/XF", "flag_to_include_incremental_state"],
            use_state_zip=False,
            deploy_tests=False,
            deploy_frostedtests=False,
            custom_tag=None,
            tool_targets=["pipeline", "win64-dll"],
            mirror=True,
        )

    def _helper_invoke_codebuild_cli(
        self, platform, config, extra_args=[], use_fbcli=True, catch_exceptions=True
    ):
        if use_fbcli:
            os.environ["use_fbcli"] = "True"
        runner = CliRunner()
        return runner.invoke(
            cli,
            [platform, config] + extra_args + self.FIXED_ARGS,
            catch_exceptions=catch_exceptions,
        )

    def test_build_tool_msbuild_args(self):
        extra_args = [self.OPTION_MSBUILD_ARGS, self.VALUE_MSBUILD_ARGS]
        result = self._helper_invoke_codebuild_cli(
            "tool",
            "release",
            extra_args,
            use_fbcli=False,
            catch_exceptions=False,
        )
        assert result.exit_code == 0
        # verify default build frosted = true
        self.mock_codeutils.return_value.buildsln.assert_called_once_with(
            build_frosted=True, fail_on_first_error=True, msbuild_args=[self.VALUE_MSBUILD_ARGS]
        )

    def test_build_tool_non_frosted(self):
        extra_args = [self.OPTION_LICENSEE, self.VALUE_LICENSEE] + [
            self.OPTION_TOOL_TARGETS,
            "pipeline",
            self.OPTION_TOOL_TARGETS,
            "win64-dll",
        ]
        result = self._helper_invoke_codebuild_cli("tool", "final", extra_args)
        assert result.exit_code == 0
        self.mock_codeutils.return_value.buildsln.assert_called_once_with(
            build_frosted=False, fail_on_first_error=True, msbuild_args=[]
        )

    def test_build_tool_frosted(self):
        extra_args = [self.OPTION_LICENSEE, self.VALUE_LICENSEE] + [
            self.OPTION_TOOL_TARGETS,
            "pipeline",
            self.OPTION_TOOL_TARGETS,
            "frosted",
        ]
        result = self._helper_invoke_codebuild_cli("tool", "release", extra_args)
        assert result.exit_code == 0
        self.mock_codeutils.return_value.buildsln.assert_called_once_with(
            build_frosted=True, fail_on_first_error=True, msbuild_args=[]
        )

    def test_build_tool_default(self):
        result = self._helper_invoke_codebuild_cli(
            "tool", "release", use_fbcli=False, catch_exceptions=False
        )
        assert result.exit_code == 0
        # verify default build frosted = true
        self.mock_codeutils.return_value.buildsln.assert_called_once_with(
            build_frosted=True, fail_on_first_error=True, msbuild_args=[]
        )

    def test_deploy_code_nomaster(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_NOMASTER, self.VALUE_NOMASTER])
        assert result.exit_code == 0
        assert not self.mock_filerutils.return_value.deploy_code.called

    def test_source_index(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert result.exit_code == 0
        self.mock_symbolsutils.return_value.source_index.assert_called_once_with(
            platform=self.ARGUMENT_PLATFORM_1,
            config=self.ARGUMENT_CONFIG_1,
            p4_port=self.VALUE_P4_PORT,
            p4_client=self.VALUE_P4_CLIENT,
            p4_user=self.VALUE_P4_USER,
            strip_symbols=True,
        )

    def test_source_index_dont_strip_symbols(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_STRIP_SYMBOLS, self.VALUE_STRIP_SYMBOLS]
        )
        assert result.exit_code == 0
        self.mock_symbolsutils.return_value.source_index.assert_called_once_with(
            platform=self.ARGUMENT_PLATFORM_1,
            config=self.ARGUMENT_CONFIG_1,
            p4_port=self.VALUE_P4_PORT,
            p4_client=self.VALUE_P4_CLIENT,
            p4_user=self.VALUE_P4_USER,
            strip_symbols=False,
        )

    def test_skip_source_index_nomaster(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_NOMASTER, self.VALUE_NOMASTER])
        assert result.exit_code == 0
        assert not self.mock_symbolsutils.return_value.source_index.called

    def test_delete_folder(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert result.exit_code == 0
        assert not self.mock_delete_folder.called

    def test_delete_folder_clean_packages(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_CLEAN_PACKAGES, self.VALUE_CLEAN_PACKAGES]
        )
        assert result.exit_code == 0
        assert self.mock_delete_folder.call_count == 2

    @patch(
        "dice_elipy_scripts.codebuild.SETTINGS",
        ConfigManager(path=config_path, default_location="location_threshold_clean"),
    )
    @patch("elipy2.frostbite.build_agent_utils.generic_threshold_clean")
    def test_threshold_cleanup_called(self, mock_generic_threshold_clean):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert result.exit_code == 0
        assert mock_generic_threshold_clean.call_count == 1

    def test_delete_folder_is_outsource_build(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_IS_OUTSOURCE_BUILD, self.VALUE_IS_OUTSOURCE_BUILD]
        )
        assert result.exit_code == 0
        assert self.mock_delete_folder.call_count == 2

    @patch("elipy2.SETTINGS.get", MagicMock(return_value="\\fake_path\\here"))
    @patch("elipy2.core.is_buildsystem_run", MagicMock(return_value=True))
    def test_filer_authentication(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + ["--filer-user", "user", "--filer-password", "password"]
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.delete_network_connection.assert_called_once()
        self.mock_filerutils.return_value.auth_network_connection.assert_called_once_with(
            network_path="\\fake_path\\here",
            username="user",
            password="password",
        )

    @patch("elipy2.symbols.SymbolsUtils")
    def test_copy_symbols_to_kobold(self, mock_symbolsutils):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS + [self.OPTION_COPY_TO_KOBOLD_INBOX, self.VALUE_COPY_TO_KOBOLD_INBOX],
        )
        assert result.exit_code == 0
        assert not mock_symbolsutils.copy_symbols_to_kobold_inbox.called

    def test_rungensln_adds_stressbulkbuild_param_with_stressbulkbuild_flag(self):
        runner = CliRunner()

        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + ["--stressbulkbuild", True])
        self.assertEqual(result.exit_code, 0)
        self.mock_run_gensln.assert_called_with(
            password=self.VALUE_PASSWORD,
            user=self.VALUE_EMAIL,
            framework_args=["licensee_arg"],
            builder=self.mock_codeutils.return_value,
            alltests=False,
            nomaster=False,
            wsl=False,
            gensln_config=None,
            domain_user=None,
            stressbulkbuild=True,
        )

    def test_rungensln_doesnt_add_stressbulkbuild_param_without_flag(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        self.assertEqual(result.exit_code, 0)
        self.mock_run_gensln.assert_called_with(
            password=self.VALUE_PASSWORD,
            user=self.VALUE_EMAIL,
            framework_args=["licensee_arg"],
            builder=self.mock_codeutils.return_value,
            alltests=False,
            nomaster=False,
            wsl=False,
            gensln_config=None,
            domain_user=None,
            stressbulkbuild=False,
        )

    def test_rungensln_virtual_branch_override(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [self.OPTION_VIRTUAL_BRANCH_OVERRIDE, self.VALUE_VIRTUAL_BRANCH_OVERRIDE],
        )
        assert result.exit_code == 0
        self.mock_run_gensln.assert_called_with(
            password=self.VALUE_PASSWORD,
            user=self.VALUE_EMAIL,
            framework_args=["licensee_arg", "-G:frostbite.Engine.Communication.ForceBranch=true"],
            builder=self.mock_codeutils.return_value,
            alltests=False,
            nomaster=False,
            wsl=False,
            gensln_config=None,
            domain_user=None,
            stressbulkbuild=False,
        )

    @patch("elipy2.frostbite.fbenv_layer.set_environment_values")
    def test_set_environment_values(self, mock_set_environment_values):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_FB_ENV_VALUES,
                self.VALUE_FB_ENV_VALUES_1,
                self.OPTION_FB_ENV_VALUES,
                self.VALUE_FB_ENV_VALUES_2,
            ],
        )
        assert result.exit_code == 0
        mock_set_environment_values.assert_has_calls(
            [
                call({"key1": "gensln:value1"}),
                call({"key1": "buildsln:value2"}),
            ]
        )


@patch("dice_elipy_scripts.codebuild.set_licensee", MagicMock())
@patch("elipy2.filer_paths.get_code_build_path", MagicMock(return_value="\\fake_path\\here"))
class TestCodeBuildCodeBuild:
    def test_fail_on_existing_dest(self):
        with patch("os.path.exists", return_value=True):
            with pytest.raises(ELIPYException):
                code_build(
                    "win64",
                    "retail",
                    code_branch="code_branch",
                    code_changelist="code_changelist",
                    p4_port="p4_port",
                    p4_client="p4_client",
                )

    @patch("dice_elipy_scripts.codebuild._code_build", MagicMock())
    @patch("elipy2.code.CodeUtils", MagicMock())
    def test_existing_dest_dry_run(self):
        with patch("os.path.exists", MagicMock(return_value=True)):
            code_build(
                "win64",
                "retail",
                code_branch="code_branch",
                code_changelist="code_changelist",
                p4_port="p4_port",
                p4_client="p4_client",
                dry_run=True,
            )
