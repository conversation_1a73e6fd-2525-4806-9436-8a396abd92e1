"""
licensee_helper.py

Helper functions for working with the licensee.
"""

import os
import json

from elipy2 import core, LOGGER, exceptions, frostbite_core
from elipy2.frostbite import licensee_utils


# pylint: disable=protected-access
def set_licensee(licensees, framework_args):
    """
    Set the licensee name where this needs to be set in FB.

    2020_PR4+ requires that we set it using the licensee script/module

    If before PR4, add it to framework_args
    """
    # we need to make sure that we are in FB2020PR4 and that this
    # file exists since there is a 2 month gap.
    _licensees = licensees

    if isinstance(licensees, tuple):
        _licensees = list(licensees)

    if _licensees is not None:
        (already_enabled, _) = _licensee_enabled(_licensees)
        if already_enabled is True:
            LOGGER.info("Skipping setting licensee as {} already enabled".format(str(_licensees)))
            core.update_shell()
            return framework_args

    licensee_py_path = os.path.join(
        frostbite_core.get_tnt_root(), "bin", "fbcli", "core", "licensee.py"
    )
    licensee_py_exists = os.path.exists(licensee_py_path)

    if (
        frostbite_core.minimum_fb_version(year=2020, version_nr=4)
        and licensee_py_exists
        and licensees
    ):
        if not frostbite_core.minimum_fb_version(year=2022, season=1, version_nr=2):
            # This variable was used in earlier Frostbite versions, but has been removed.
            licensee_utils.set_nant_cached_licensee_dict(None)

        user_settings_key = "FBENV.LICENSEE_CODE_CONFIG"
        LOGGER.info("Enabling licensee via set_licensee_code_config: {}".format(str(_licensees)))

        if frostbite_core.minimum_fb_version(year=2022, season=1, version_nr=2):
            licensee_dict = licensee_utils.licensee_update_enables(
                licensees=_licensees, enable=True, disableallothers=True
            )
        else:
            licensee_dict = licensee_utils.nant_generate_licensee_fragment_info(licensees)

        licensee_utils.set_licensee_code_config(json.dumps(licensee_dict, separators=(",", ":")))

        core.update_shell()
        LOGGER.info(os.environ.get(user_settings_key, "Couldn't find {}".format(user_settings_key)))
        check_enabled_licensee(_licensees)

    elif _licensees:
        framework_args = list(framework_args)
        for licensee in _licensees:
            LOGGER.info(
                "Skipping fb licensee since this version of FB doesn't support it, \
                adding to framework_args instead"
            )
            framework_args.append("-D:frostbite.licensees=" + licensee)
    else:
        LOGGER.warning("No licensee specified, nothing to be set")

    return framework_args


def _licensee_enabled(licensees):
    """
    Confirms that enabled licensee(s) matched with licensee parameter(s).
    """
    set_licensees = []

    for licensee in licensees:
        if frostbite_core.minimum_fb_version(year=2022, season=1, version_nr=2):
            set_licensees = licensee_utils.get_enabled_licensee_names()
        else:
            set_licensees = licensee_utils.nant_get_enabled_licensee_names()

        if set_licensees is None or len(set_licensees) == 0:
            return (False, set_licensees)

        if licensee not in set_licensees:
            return (False, set_licensees)
        LOGGER.info("Confirmed enabled licensee is: %s", licensee)
    return (True, set_licensees)


def check_enabled_licensee(licensees):
    """
    Confirms that enabled licensee(s) matched with licensee parameter(s).
    """
    (licensee_enabled, current_licensees) = _licensee_enabled(licensees)
    if licensee_enabled is False:
        raise exceptions.ELIPYException(
            "Wrong licensee set. Enabled licensee is {}, should be {}".format(
                current_licensees, licensees
            )
        )
