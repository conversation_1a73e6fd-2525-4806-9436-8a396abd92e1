package com.ea.project.bct.branchsettings

class Bf_playtest_sp {
    // Settings for jobs
    static Class project = com.ea.project.bct.Bct
    static Map general_settings = [
        dataset           : project.dataset,
        elipy_call        : project.elipy_call + ' --use-fbenv-core',
        elipy_install_call: project.elipy_install_call,
        frostbite_licensee: project.frostbite_licensee,
        workspace_root    : project.workspace_root,
    ]
    static Map standard_jobs_settings = [
        asset                        : 'CH3/Build/PlaytestLevelsCh3Sp',
        denuvo_wrapping              : false,
        frosty_reference_job         : 'trunk-code-dev.data.start',
        extra_data_args              : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        extra_frosty_args            : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        import_avalanche_autotest    : false,
        linux_docker_images          : false,
        poolbuild_data               : true,
        poolbuild_frosty             : true,
        server_asset                 : 'Game/Setup/Build/PlaytestLevelsSp',
        shift_branch                 : true,
        slack_channel_code           : [channels: ['#san-build-notify'], skip_for_multiple_failures: true],
        slack_channel_data           : [channels: ['#san-build-notify'], skip_for_multiple_failures: true],
        slack_channel_frosty         : [channels: ['#san-build-notify'], skip_for_multiple_failures: true],
        timeout_hours_data           : 5,
        trigger_type_frosty          : 'none',
        trigger_type_shift           : 'none',
        skip_code_build_if_no_changes: false,
        strip_symbols                : false,
    ]
    static Map preflight_settings = [:]

    // Matrix definitions for jobs
    static List code_matrix = []
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = [
        // [name: '.frosty.start', args: []],
    ]
    static List data_matrix = []
    static List data_downstream_matrix = [
        // [name: '.frosty.start', args: []],
    ]
    static List patchdata_matrix = []
    static List frosty_matrix = [
        [name: 'win64', variants: [[format: 'files', config: 'final', region: 'playtest', args: ' --additional-configs performance ']]],
    ]
    static List frosty_downstream_matrix = [
        [name: '.shift.upload', args: ['code_changelist', 'data_changelist']],
    ]
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = []
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
