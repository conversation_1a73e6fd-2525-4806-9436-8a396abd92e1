package com.ea.project.bctch1.branchsettings

import com.ea.project.bctch1.BctCh1

class CH1_SP_content_dev_first_patch {
    // Settings for jobs
    static Class project = BctCh1
    static Map general_settings = [
        dataset             : project.dataset,
        elipy_call          : project.elipy_call + ' --use-fbenv-core',
        elipy_install_call  : project.elipy_install_call,
        frostbite_licensee  : project.frostbite_licensee,
        workspace_root      : project.workspace_root,
        job_label_statebuild: 'statebuild',
    ]
    static Map code_settings = [:]
    static Map data_settings = [
        poolbuild_data           : true,
        skip_standalone_patchdata: true,
        slack_channel_patchdata  : [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
    ]
    static Map frosty_settings = [:]
    static Map standard_jobs_settings = code_settings + data_settings + frosty_settings + [
        asset                       : 'CombinedShippingSPLevels',
        combine_bundles             : [
            is_target_branch: false,
        ],
        extra_data_args             : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        fetch_baseline_reference_job: 'CH1-SP-content-dev-disc-build.store_regular_baseline.start',
        first_patch                 : true,
        server_asset                : 'Game/Setup/Build/DevMPLevels',
        trigger_string_patchdata    : 'H 0 * * 1-5',
        trigger_type_patchdata      : 'cron',
        use_dynamic_disc_baselines  : true,
    ]
    static Map icepick_settings = [:]
    static Map preflight_settings = [:]

    // Matrix definitions for jobs
    static List code_matrix = []
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = []
    static List data_matrix = []
    static List data_downstream_matrix = []
    static List patchdata_matrix = [
        [name: 'win64'],
        [name: 'ps5'],
        [name: 'xbsx'],
    ]
    static List patchdata_downstream_matrix = []
    static List frosty_matrix = []
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = []
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
