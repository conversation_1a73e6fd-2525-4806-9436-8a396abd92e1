package support

import hudson.model.Result

class TestBuild {
    TestRawBuild rawBuild
    Integer number
    String description

    TestBuild(TestRawBuild rawBuild, int number, String description) {
        this.rawBuild = rawBuild
        this.number = number
        this.description = description
    }

    String getAbsoluteUrl() {
        return "https://controller/job/name/${this.number}/"
    }

    String getDisplayName() {
        return number.toString()
    }

    TestBuild getPreviousCompletedBuild() {
        return this.number > 1 ? rawBuild.parent.builds[this.number - 2] : null
    }

    String getResult() {
        return this.rawBuild.result.toString()
    }

    String getCurrentResult() {
        return result
    }
}

class TestRawBuild {
    TestParent parent
    TestBuild build
    Result result
}

class TestParent {
    List<TestBuild> builds = []

    TestParent(List<String> descriptions, List<String> results = []) {
        int number = 1
        def missingResults = descriptions.size() - results.size()
        def paddedResults = missingResults < 1 ? results : results + ['SUCCESS'] * missingResults

        [descriptions, paddedResults].transpose().each { String description, String result ->
            TestRawBuild rawBuild = new TestRawBuild()
            rawBuild.build = new TestBuild(rawBuild, number, description)
            rawBuild.result = Result.fromString(result)
            this.builds.add(rawBuild.build)
            number += 1
        }
    }

    TestParent init() {
        builds.each {
            it.rawBuild.parent = this
        }
        return this
    }

    TestBuild getCurrentBuild() {
        return this.builds.last()
    }
}

class TestPipeLineContext {
    TestBuild currentBuild
    Steps steps
    Map env

    TestPipeLineContext(currentBuild, steps) {
        this.currentBuild = currentBuild
        this.steps = steps
    }

    TestPipeLineContext(currentBuild, steps, env) {
        this.currentBuild = currentBuild
        this.steps = steps
        this.env = env
    }
}

// Jenkins pipeline steps context to fill with methods for what you need to test
class Steps {
    Map getEnv() { return [:] }
}
