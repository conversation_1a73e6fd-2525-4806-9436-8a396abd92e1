pre-push:
  commands:
    # ec generates a lot of redundant output and I haven't been able to suppress it
    #ec:
    #  run: ec
    black:
      glob: "*.py"
      run: python -m black -l 100 --check . -q
    pylint:
      glob: "*.py"
      run: python -m pylint dice_elipy_scripts -r y
    tests:
      glob: "*.py"
      run: python setup.py test

pre-commit:
  scripts:
    "not_default_branch.sh":
      runner: bash
