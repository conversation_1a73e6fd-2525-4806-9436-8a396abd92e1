root:
  base: ['$DESTINATION_BASEPATH$', '$VERSION$']
  paths:
  - path_location: []
    min_items: 1

server:
  base: ['$DESTINATION_BASEPATH$', '$VERSION$', 'server', '$DATACHANGELIST$_$CODECHANGELIST$']
  paths:
  - path_location: []
    min_items: 5
  - path_location: ['symbol']

linuxserver:
  base: ['$DESTINATION_BASEPATH$', '$VERSION$', 'linuxserver', '$DATACHANGELIST$_$CODECHANGELIST$']
  paths:
  - path_location: []
    min_items: 5
  - path_location: ['symbol']
  - path_location: ['digital', 'ww', 'final', 'FrostyLogFile.txt']
  - path_location: ['digital', 'ww', 'final', 'build.json']
  - path_location: ['digital', 'ww', 'final', 'builtLevels.json']
  - path_location: ['digital', 'ww', 'final', 'NFS_COMMON_Server_$DATACHANGELIST$_Binaries.zip']
  - path_location: ['digital', 'ww', 'final', 'NFS_COMMON_Server_$DATACHANGELIST$_Symbols.zip']

ps5:
  base: ['$DESTINATION_BASEPATH$', '$VERSION$', 'ps5', '$DATACHANGELIST$_$CODECHANGELIST$']
  paths:
  - path_location: ['symbol']
  - path_location: ['bundles', 'head', 'data', 'ops_chain.zip']
  - path_location: ['bundles', 'state', 'cas.cat']
  - path_location: ['bundles', 'state', 'meta', 'CURRENT']
  - path_location: ['patch', 'ww', 'final', 'publish.log']
  - path_location: ['patch', 'ww', 'final', 'streaminstall_chunk.log']
  - path_location: []
  - path_location: []
    min_items: 6
  - path_location: ['bundles']
    min_items: 3
  - path_location: ['bundles', 'head', 'data']
    min_items: 10
  - path_location: ['bundles', 'state']
    min_items: 25
  - path_location: ['bundles', 'state', 'meta']
    min_items: 450

xbsx:
  base: ['$DESTINATION_BASEPATH$', '$VERSION$', 'xbsx', '$DATACHANGELIST$_$CODECHANGELIST$']
  paths:
  - path_location: ['symbol']
  - path_location: ['bundles', 'head', 'data', 'ops_chain.zip']
  - path_location: ['bundles', 'state', 'cas.cat']
  - path_location: ['bundles', 'state', 'meta', 'CURRENT']
  - path_location: ['patch', 'ww', 'final', 'streaminstall_chunk.log']
  - path_location: ['patch', 'ww', 'retail', 'streaminstall_chunk.log']
  - path_location: ['patch', 'ww', 'retail', 'NFS.Main_Xbsx_retail.exe']
  - path_location: ['patch', 'ww', 'retail', 'NFS.Main_Xbsx_retail.pdb']
  - path_location: []
    min_items: 6
  - path_location: ['bundles']
    min_items: 3
  - path_location: ['bundles', 'head', 'data']
    min_items: 10
  - path_location: ['bundles', 'state']
    min_items: 25
  - path_location: ['bundles', 'state', 'meta']
    min_items: 450

win64:
  base: ['$DESTINATION_BASEPATH$', '$VERSION$', 'win64', '$DATACHANGELIST$_$CODECHANGELIST$']
  paths:
  - path_location: ['symbol']
  - path_location: ['bundles', 'head', 'data', 'ops_chain.zip']
  - path_location: ['bundles', 'state', 'cas.cat']
  - path_location: ['bundles', 'state', 'meta', 'CURRENT']
  - path_location: ['patch', 'ww', 'final', 'streaminstall_chunk.log']
  - path_location: ['patch', 'ww', 'retail', 'streaminstall_chunk.log']
  - path_location: ['patch', 'ww', 'retail', 'NFS.Main_Win64_retail.pdb']
  - path_location: ['patch', 'ww', 'retail', 'NFS.Main_Win64_retail_prot.pdb']
  - path_location: ['patch', 'ww', 'retail', 'NFSUpgrade.zip']
  - path_location: ['patch', 'ww', 'retail', 'NFSUpgrade.exe']
  - path_location: []
    min_items: 7
  - path_location: [ 'bundles']
    min_items: 3
  - path_location: [ 'bundles', 'head', 'data']
    min_items: 10
  - path_location: [ 'bundles', 'state']
    min_items: 23
  - path_location: [ 'bundles', 'state', 'meta']
    min_items: 453
