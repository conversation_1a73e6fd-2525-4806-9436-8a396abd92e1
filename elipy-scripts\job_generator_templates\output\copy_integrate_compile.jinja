{#
    Command:
        copy_integrate_build
            short_help: Performs copy+integration+compile and submits the result.

    Arguments:

    Required variables:
        changelist
            required: True
        copy_mapping
            required: True
        integrate_mapping
            required: True
        p4_client
            required: True
        p4_port
            required: True
        source_branch
            required: True
            help: Source branch in Perforce.

    Optional variables:
        clean
            default: false
            help: Delete TnT/Local if --clean true is passed.
        data_directory
            default: None
            help: Specify which data directory to use (relative to GAME_ROOT).
        email
            default: None
            help: User email to authenticate to package server.
        domain_user
            default: None
            help: The user to authenticate to package server as DOMAIN\user
        framework_args
            multiple: True
            help: Framework arguments for gensln.
        ignore_source_history
            is_flag: True
            help: Ignore source file history (sets the Perforce integrate flag -Di).
        licensee
            multiple: True
            default: None
            help: Licensee to use
        p4_user
            default: None
            help: Perforce user name.
        password
            default: None
            help: User credentials to authenticate to package server.
        shelve_cl/__no_shelve_cl
            default: False
            help: Shelve changelist for failed integration.
        submit/__no_submit
            default: True
        submit_message
            default: ''
            help: Message to include in the submit message.
        use_snowcache
            is_flag: True
            help: Whether to enable Snowcache or not
        snowcache_mode_override
            type: click.Choice(SNOWCACHE_MODES, case_sensitive=False)
            default: ''
            help: Override the logically evaluated snowcache mode with this
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    copy_integrate_build
    --changelist {{ changelist }}
    --copy-mapping {{ copy_mapping }}
    --integrate-mapping {{ integrate_mapping }}
    --p4-client {{ p4_client }}
    --p4-port {{ p4_port }}
    --source-branch {{ source_branch }}
    {%- if clean %}
    --clean {{ clean }}
    {%- endif %}
    {%- if data_directory %}
    --data-directory {{ data_directory }}
    {%- endif %}
    {%- if email %}
    --email {{ email }}
    {%- endif %}
    {%- if domain_user %}
    --domain-user {{ domain_user }}
    {%- endif %}
    {%- if framework_args %}
    --framework-args {{ framework_args }}
    {%- endif %}
    {%- if ignore_source_history %}
    --ignore-source-history {{ ignore_source_history }}
    {%- endif %}
    {%- if licensee %}
    --licensee {{ licensee }}
    {%- endif %}
    {%- if p4_user %}
    --p4-user {{ p4_user }}
    {%- endif %}
    {%- if password %}
    --password {{ password }}
    {%- endif %}
    {%- if shelve_cl/__no_shelve_cl %}
    --shelve-cl/--no-shelve-cl {{ shelve_cl/__no_shelve_cl }}
    {%- endif %}
    {%- if submit/__no_submit %}
    --submit/--no-submit {{ submit/__no_submit }}
    {%- endif %}
    {%- if submit_message %}
    --submit-message {{ submit_message }}
    {%- endif %}
    {%- if use_snowcache %}
    --use-snowcache {{ use_snowcache }}
    {%- endif %}
    {%- if snowcache_mode_override %}
    --snowcache-mode-override {{ snowcache_mode_override }}
    {%- endif %}
  displayName: elipy copy_integrate_build
