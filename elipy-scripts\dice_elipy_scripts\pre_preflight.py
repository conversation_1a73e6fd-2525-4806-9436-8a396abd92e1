"""
pre_preflight.py
"""

import click
import os
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import LOGGER, data, p4, filer, avalanche, running_processes
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command("pre_preflight", short_help="Performs a pre preflight maintenance run.")
@click.option("--code-changelist", default=None, help="Code changelist to run pre preflight on.")
@click.option("--code-branch", default="game-dev", help="Perforce branch for code.")
@click.option("--platform", help="Platform to run pre preflight on.", required=True)
@click.option("--datadir", help="Which datadir to build from.", required=True)
@click.option("--p4-client", default=None, help="Which p4 client to use.", required=True)
@click.option("--p4-user", default=None, help="Which p4 user is being used.", required=True)
@click.option("--p4-port", default=None, help="Which p4 port to be used.", required=True)
@click.option("--asset", help="Asset to be cooked.", default=["preflightlevels"], multiple=True)
@click.option(
    "--server-asset",
    help="Server asset to be cooked.",
    default=["PreflightServerAssets.dbx"],
    multiple=False,
)
@click.option("--data-changelist", default=None, help="Data changelist to run pre preflight on.")
@click.option("--data-branch", default=None, help="Perforce branch for data.")
@click.option(
    "--clean-master-version-check",
    is_flag=True,
    help="Run clean on master version update.",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    code_changelist,
    code_branch,
    platform,
    datadir,
    p4_client,
    p4_user,
    p4_port,
    asset,
    server_asset,
    data_changelist,
    data_branch,
    clean_master_version_check,
):
    """
    Performs a pre preflight maintenance operation on content only preflights.
    """
    # adding sentry tags
    add_sentry_tags(__file__, "preflight")

    # kill any unexpected running processes
    running_processes.kill()

    # Post preflight for content preflight
    if platform.lower() in ["server", "linuxserver"]:
        asset = [server_asset]

    perforce = p4.P4Utils(p4_port, p4_user, p4_client)
    perforce.revert()

    # Fetch pipeline build from filer.
    filer.FilerUtils().fetch_code(code_branch, code_changelist, "pipeline", "release")

    builder = data.DataUtils(platform, list(asset))
    builder.set_datadir(datadir)

    builder.cook(trim=False, clean_master_version_check=clean_master_version_check)
    avalanche.set_avalanche_build_status(
        code_changelist=code_changelist,
        data_changelist=data_changelist,
        data_branch=data_branch,
        platform=platform,
    )
    LOGGER.info("Cook completed")
