package scripts.schedulers

import hudson.model.BuildableItem
import hudson.model.Cause
import hudson.model.Job
import hudson.model.Result
import jenkins.model.Jenkins
import jenkins.triggers.SCMTriggerItem

/**
 * retriggerJobWithScmOnFailure.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Run job to detect stuck jobs failed due to the P4 Polling error') {
            steps {
                script {
                    currentBuild.displayName = env.JOB_NAME + '.' + env.BUILD_NUMBER
                    List<Job> failedJobs = []
                    List<String> failureStrings = [
                        'Could not read from remote repository',
                        'ERROR: Maximum checkout retry attempts reached, aborting',
                        '*** ERROR: Unable to connect to ldap_host: IO::Socket::INET: Bad hostname \'\' ***',
                        'Unable to resolve Perforce server host name',
                        'Unable to connect to Perforce server',
                    ]
                    echo('Ignoring running jobs...')
                    List<Job> jobs = Jenkins.get().getItems(Job).findAll { job ->
                        job instanceof SCMTriggerItem && !job.building
                    }

                    echo('Processing jobs...')
                    for (def job : jobs) {
                        try {
                            echo("\tProcessing ${job.name}")
                            def build = job.lastCompletedBuild
                            def buildLog = build?.result == Result.FAILURE ? build.getLog(200) : ['']
                            def failureStringFound = buildLog.any { line ->
                                failureStrings.any { failureString ->
                                    line.contains(failureString)
                                }
                            }

                            if (failureStringFound && ((SCMTriggerItem) job).SCMTrigger) {
                                failedJobs.add(job)
                            }
                        } catch (FileNotFoundException exc) {
                            echo("No log found for ${job.name}, skipping")
                        }
                    }

                    echo("Found ${failedJobs.size()} failed builds that need retriggering.")
                    failedJobs.each {
                        def triggered = ((BuildableItem) it).scheduleBuild(
                            new Cause.RemoteCause(
                                currentBuild.absoluteUrl,
                                'Triggered jobs after detected failed (usually caused by syncing issues).'
                            )
                        )
                        echo "\t${it.name}: trigger ${triggered ? 'succeeded' : 'failed'}"
                    }

                    if (failedJobs) {
                        currentBuild.result = 'UNSTABLE'
                    }
                }
                SlackMessageNew(currentBuild, '#cobra-support-alerts', 'cob')
            }
        }
    }
}
