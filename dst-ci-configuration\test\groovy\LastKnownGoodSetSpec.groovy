import com.ea.lib.LastKnownGood
import spock.lang.Specification

class LastKnownGoodSetSpec extends Specification {
    Map branchInfo = [
        remote_masters_to_receive_code: [
            [name: 'kin-preflight-jenkins.cobra.dre.ea.com', allow_failure: false],
            [name: 'aws-kin-staging.dre.dice.se', allow_failure: true],
        ],
        remote_masters_to_receive_data: [
            [name: 'kin-preflight-jenkins.cobra.dre.ea.com', allow_failure: false],
            [name: 'aws-kin-staging.dre.dice.se', allow_failure: true],
        ],
        'code_branch'                 : 'code_branch',
        'data_branch'                 : 'data_branch',
        'branch_name'                 : 'current_branch',
    ]

    void "prePreflight code: test that we get the expected job settings"() {
        when:
        def result = LastKnownGood.codeRemoteTriggers(branchInfo)

        then:
        result == [
            '/usr/bin/curl -s -u ${jenkinsUser_kin_preflight_jenkins}:${jenkinsAPIToken_kin_preflight_jen<PERSON>} -XPOST "https://kin-preflight-jenkins.cobra.dre.ea.com/job/current_branch.code.lastknowngood/buildWithParameters?token=remotebuild&code_changelist=${code_changelist}&cause=${BUILD_URL}"',
            '/usr/bin/curl -s -u ${jenkinsUser_aws_kin_staging}:${jenkinsAPIToken_aws_kin_staging} -XPOST "https://aws-kin-staging.dre.dice.se/job/stage-current_branch.code.lastknowngood/buildWithParameters?token=remotebuild&code_changelist=${code_changelist}&cause=${BUILD_URL}" || true',
        ]
    }

    void "prePreflight data: test that we get the expected job settings"() {
        when:
        def result = LastKnownGood.dataRemoteTriggers(branchInfo)

        then:
        result == [
            '/usr/bin/curl -s -u ${jenkinsUser_kin_preflight_jenkins}:${jenkinsAPIToken_kin_preflight_jenkins} -XPOST "https://kin-preflight-jenkins.cobra.dre.ea.com/job/current_branch.data.lastknowngood/buildWithParameters?token=remotebuild&code_changelist=${code_changelist}&data_changelist=${data_changelist}&cause=${BUILD_URL}"',
            '/usr/bin/curl -s -u ${jenkinsUser_aws_kin_staging}:${jenkinsAPIToken_aws_kin_staging} -XPOST "https://aws-kin-staging.dre.dice.se/job/stage-current_branch.data.lastknowngood/buildWithParameters?token=remotebuild&code_changelist=${code_changelist}&data_changelist=${data_changelist}&cause=${BUILD_URL}" || true',
        ]
    }
}
