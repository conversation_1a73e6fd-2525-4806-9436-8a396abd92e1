package com.ea.lib.model.autotest

/**
 * A model representing an Autotest Platform. Contains properties that are specific to the given platform
 * when running an Autotest job.
 */
class Platform implements Comparable<Platform> {
    /**
     * The name of the Platform
     */
    Name name
    /**
     * Optional region that is specific for this platform's configuration
     */
    Region region
    /**
     * Not currently supported
     */
    String extraArgs

    @Override
    String toString() {
        return name.value
    }

    @Override
    int compareTo(Platform o) {
        return this.name.value <=> o.name.value
    }
}
