import hudson.model.Run
import hudson.model.Run.Artifact


public class PLConsoleOutputArtifact {

    private final String name = "ConsoleOutput"
    private String url

    PLConsoleOutputArtifact(String url) {
        this.url = url
    }


    public String getName() {
        return this.@name
    }


    public String getUrl() {
        return this.@url
    }


    public static List<PLConsoleOutputArtifact> gatherArtifacts(Run run) {
        def consoleOutputArtifactList = []
        def runUrl = JobUtil.getClassicDisplayURL(run)
        if (runUrl) {
            def consoleUrl = "${runUrl}console"
            consoleOutputArtifactList.add(new PLConsoleOutputArtifact(consoleUrl))
        }
        return consoleOutputArtifactList
    }


    public Artifact getArtifact() {
        return null
    }
}
