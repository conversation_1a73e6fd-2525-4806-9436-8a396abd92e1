"""
gametool/frostbite_database_upgrader.py
"""
import os
import click

from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics
from elipy2 import LOGGER, running_processes, code, p4, frostbite_core
from elipy2.frostbite import fbcli


@click.command(
    "gametool_frostbite_database_upgrader",
    short_help="Build the FDU gametool.",
    context_settings=dict(ignore_unknown_options=True),
)
@click.option("--code-changelist", required=True, help="Which code changelist to use.")
@click.option(
    "--clean",
    type=click.BOOL,
    default=False,
    help="Delete TnT/Local if --clean true is passed, otherwise no cleanup is performed.",
)
@click.option("--config", default="release")
@click.option("--p4-port", required=True)
@click.option("--p4-client", required=True)
@click.option("--p4-user", default=None, help="Perforce user name")
@click.option("--submit", type=click.BOOL, default=True, help="Set this to false for dry-run")
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    code_changelist,
    clean,
    config,
    p4_port,
    p4_client,
    p4_user,
    submit,
):
    """
    Build Frostbite Database Upgrader Gametool
    """
    # adding sentry tags
    add_sentry_tags(__file__, "gametool_frostbite_database_upgrader")

    running_processes.kill()

    perforce = p4.P4Utils(port=p4_port, user=p4_user, client=p4_client)
    perforce.set_environment()
    fdu_bin_p4_path = (
        os.path.join(
            frostbite_core.get_tnt_root(), "Code", "Utils", "FrostbiteDatabaseUpgrader", "bin"
        )
        + os.sep
        + "..."
    )
    perforce.sync(fdu_bin_p4_path)
    perforce.edit(fdu_bin_p4_path)

    try:
        build_fdu(code_changelist, config, clean)
    except Exception as ex:
        LOGGER.info("An exception occurred while building FDU. Reverting and cleaning the P4 path.")
        perforce.revert(path=fdu_bin_p4_path)
        perforce.clean(folder=fdu_bin_p4_path)
        raise ex

    LOGGER.info("FDU has successfully built.")

    if submit:
        try:
            LOGGER.info("Submitting FDU binaries...")
            perforce.reconcile(path=fdu_bin_p4_path, options=["e"])
            perforce.revert(path=fdu_bin_p4_path, wipe=False, only_unchanged=True)
            submit_message = "[Automated] Submitting rebuilt FDU binaries from cl {}".format(
                code_changelist
            )
            perforce.submit(message=submit_message)
        except Exception as ex:
            LOGGER.warning("An exception occurred while submitting: {}".format(ex))
        finally:
            perforce.revert(path=fdu_bin_p4_path)
            perforce.clean(folder=fdu_bin_p4_path)


def build_fdu(code_changelist, config, clean):
    """
    Runs fbcli buildsln dbupgrade
    """
    builder = code.CodeUtils(
        platform="dbupgrade",
        config=config,
        monkey_build_label=code_changelist,
    )

    if clean:
        builder.clean_local(close_handles=True)

    LOGGER.info("Run buildsln...")
    builder.gensln()
    builder.buildsln(fail_on_first_error=False)
    fbcli.run(
        "nant",
        method_args=[
            "Code/Utils/FrostbiteDatabaseUpgrader/FrostbiteDatabaseUpgrader.build",
            "win64-dll",
            "FDUTestAndPublish",
        ],
    )
