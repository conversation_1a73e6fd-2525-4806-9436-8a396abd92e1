/**
 * HasRecentJob.groovy
 * Checks if there are any recent jobs on a certain machine, used for the prepreflight and sdelete-statebuild jobs.
 */
boolean call(String node_name, String job_name, int max_recent_time) {
    def job = Jenkins.get().getItem(job_name)

    def hasRecentJob = false
    def hasJobInQueue = false
    def hasRecentlyBuiltJob = false

    def queueUrl = 'No-URL'
    hasJobInQueue = Jenkins.get().queue.items.any {
        def inQueue = it.assignedLabel.name == node_name && it.task.name == job_name
        if (inQueue) {
            queueUrl = it.url
        }
    }

    if (hasJobInQueue) {
        echo 'Node: ' + node_name + ', found ' + job_name + ' in queue ' + queueUrl
    }

    if (hasJobInQueue == false) {
        hasRecentlyBuiltJob = job.builds.any { build ->
            if (node_name == build.builtOnStr) {
                if (build.inProgress) {
                    return true
                }
                build_end_time = build.startTimeInMillis + build.duration
                time_since_build = new Date().time - build_end_time
                if (time_since_build < max_recent_time) {
                    echo 'Node: ' + node_name + ', found recent ' + job_name + ' run ' + time_since_build + ' milliseconds ago.'
                    return true
                }
            }
            return false
        }
    }

    hasRecentJob = hasJobInQueue || hasRecentlyBuiltJob
    return hasRecentJob
}
