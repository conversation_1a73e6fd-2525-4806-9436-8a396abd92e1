package scripts.schedulers.all

import com.ea.lib.model.JobReference
import com.ea.project.GetBranchFile

def branchFile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
def branchInfo = branchFile.general_settings + branchFile.standard_jobs_settings
def project = ProjectClass(env.project_name)

/**
 * CoverityScheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Get changelist from Perforce') {
            steps {
                script {
                    def ignore_paths = branchFile.general_settings?.ignore_paths_code_preview ?: []
                    P4PreviewCode(project, 'stream', env.CODE_FOLDER, env.CODE_BRANCH, env.NON_VIRTUAL_CODE_FOLDER, env.NON_VIRTUAL_CODE_BRANCH, ignore_paths, [], [:])
                }
            }
        }
        stage('Trigger Coverity job') {
            steps {
                script {
                    List<JobReference> jobReferences = []
                    retryOnFailureCause(3, jobReferences) {
                        def codeChangelist = params.CODE_CHANGELIST ?: env.P4_CHANGELIST
                        def cleanLocal = params.CLEAN_LOCAL
                        def cleanCoverityClient = params.CLEAN_COVERITY_CLIENT

                        def parameters = [
                            string(name: 'CODE_CHANGELIST', value: codeChangelist),
                            booleanParam(name: 'CLEAN_LOCAL', value: cleanLocal),
                            booleanParam(name: 'CLEAN_COVERITY_CLIENT', value: cleanCoverityClient),
                        ]

                        def injectMap = [
                            'CODE_CHANGELIST': codeChangelist,
                        ]
                        EnvInject(currentBuild, injectMap)
                        currentBuild.displayName = "${env.JOB_NAME}.${codeChangelist}"
                        String jobName = "${env.BRANCH_NAME}.coverity"
                        def downstreamJob = build(job: jobName, parameters: parameters, propagate: false)
                        jobReferences << new JobReference(downstreamJob: downstreamJob, jobName: jobName, parameters: parameters, propagate: false)

                        def slackSettings = branchInfo.coverity_settings.slack_channel
                        SlackMessageNew(currentBuild, slackSettings, project.short_name)
                    }
                }
            }
        }
    }
}
