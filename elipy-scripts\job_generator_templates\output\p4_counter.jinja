{#
    Command:
        p4_counter
            short_help: Performs a Perforce set single counter value operation.

    Arguments:

    Required variables:
        port
            required: True
            help: Perforce server specification
        client
            required: True
        user
            required: True
        countername
            required: True
        value
            required: True

    Optional variables:
        extra_port
            required: False
            help: When enable 2nd counter on a different Perforce server
        extra_client
            required: False
            help: When enable 2nd counter from a different client
        extra_countername
            required: False
            help: Set for 2nd counter's name, usually for data-counter
        extra_value
            required: False
            help: Set for 2nd counter's value, usually for data-counter
        force
            is_flag: True
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    p4_counter
    --port {{ port }}
    --client {{ client }}
    --user {{ user }}
    --countername {{ countername }}
    --value {{ value }}
    {%- if extra_port %}
    --extra-port {{ extra_port }}
    {%- endif %}
    {%- if extra_client %}
    --extra-client {{ extra_client }}
    {%- endif %}
    {%- if extra_countername %}
    --extra-countername {{ extra_countername }}
    {%- endif %}
    {%- if extra_value %}
    --extra-value {{ extra_value }}
    {%- endif %}
    {%- if force %}
    --force {{ force }}
    {%- endif %}
  displayName: elipy p4_counter
