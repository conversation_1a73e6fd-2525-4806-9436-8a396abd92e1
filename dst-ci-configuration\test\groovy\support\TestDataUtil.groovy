package support

import com.ea.lib.model.autotest.AutotestCategory
import com.ea.matrixfiles.AutotestMatrix
import com.ea.matrixfiles.AutotestMatrixFactory
import com.ea.project.GetBranchFile

/**
 * Utility class for preparing and parsing test data for autotests and branches.
 * This class provides methods to process master settings files and extract
 * relevant information such as test cases, branch details, and test categories.
 */
class TestDataUtil {

    /**
     * Prepares test data for autotests by parsing master settings files.
     * Adds test categories for branches by processing the autotest matrix and branches.
     * @return A list of maps containing test data for autotests.
     */
    static List<Map<String, ?>> prepareTestDataForAutotests() {
        List<Map<String, ?>> testCases = parseMasterSettingsForAutotests(TestUtil.masterSettingsFiles)
        return testCases.collect { Map testCase ->
            testCase + ['testCategoriesForBranches': parseCategoriesForAutotests(testCase.autotestMatrix as AutotestMatrix,
                testCase.branches as List)]
        }
    }

    /**
     * Prepares test data for all branches by parsing master settings files.
     * Collects branch settings for all branches.
     * @return A list of branch settings.
     */
    static List<Map> prepareTestDataForAllBranches() {
        List<Map<String, ?>> testCases = parseMasterSettingsForAllBranches(TestUtil.masterSettingsFiles)
        return testCases.collectMany { Map testCase ->
            testCase.branches*.branchSettings
        }
    }

    /**
     * Parses master settings files to extract test cases for autotests.
     * Processes `autotest_branches` from the master settings files.
     * @param masterSettingsFiles A list of master settings files.
     * @return A list of maps containing test cases for autotests.
     */
    private static List<Map<String, ?>> parseMasterSettingsForAutotests(List<File> masterSettingsFiles) {
        Map<String, Map<String, ?>> testCases = [:]
        GroovyClassLoader groovyClassLoader = new GroovyClassLoader()
        masterSettingsFiles.each {
            Class masterSettings = groovyClassLoader.parseClass(it)
            masterSettings.autotest_branches.each { String branchName, Map autotestConfiguration ->
                def project = autotestConfiguration.project ?: masterSettings.project
                AutotestMatrix autotestMatrix = AutotestMatrixFactory.getInstance(project.autotest_matrix)
                String autotestMatrixName = autotestMatrix.class
                Map<String, ?> test = testCases.get(autotestMatrixName) ?: [autotestMatrix: autotestMatrix, branches: []]
                def branch = test.branches.find { it.name == branchName }
                if (!branch) {
                    test.branches << [name: branchName, branchSettings: GetBranchFile.get_branchfile(project.name, branchName)]
                    testCases.put(autotestMatrixName, test)
                }
            }
        }
        return testCases*.value
    }

    /**
     * Parses master settings files to extract test cases for all branches.
     * Processes `branches` from the master settings files.
     * @param masterSettingsFiles A list of master settings files.
     * @return A list of maps containing test cases for all branches.
     */
    private static List<Map<String, ?>> parseMasterSettingsForAllBranches(List<File> masterSettingsFiles) {
        Map<String, Map<String, ?>> testCases = [:]
        GroovyClassLoader groovyClassLoader = new GroovyClassLoader()
        masterSettingsFiles.each {
            Class masterSettings = groovyClassLoader.parseClass(it)
            masterSettings.branches.each { String branchName, Map branchConfiguration ->
                def project = branchConfiguration.project ?: masterSettings.project
                String projectName = project.name
                Map<String, ?> test = testCases.get(projectName) ?: [project: project, branches: []]
                def branch = test.branches.find { it.name == branchName }
                if (!branch) {
                    def branchSettings = GetBranchFile.get_branchfile(projectName, branchName)
                    test.branches << [name: branchName, branchSettings: branchSettings]
                    testCases.put(projectName, test)
                }
            }
        }
        return testCases*.value
    }

    /**
     * Parses autotest matrix and branches to extract test categories for autotests.
     * @param autotestMatrix The autotest matrix object.
     * @param branches A list of branch maps.
     * @return A list of maps containing test categories for each branch.
     */
    private static List<Map> parseCategoriesForAutotests(AutotestMatrix autotestMatrix, List<Map> branches) {
        List<Map> testCategories = []
        branches.each {
            String branchName = it.name
            List<AutotestCategory> testCategoriesWithBranch = []
            testCategoriesWithBranch += autotestMatrix.getTestCategories(branchName)
            testCategoriesWithBranch += autotestMatrix.getManualTestCategories(branchName)
            testCategories << [name: branchName, testCategories: testCategoriesWithBranch]
        }
        return testCategories
    }
}
