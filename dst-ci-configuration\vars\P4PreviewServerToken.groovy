import com.ea.exceptions.CobraException

/**
 * P4PreviewServerToken.groovy
 * Preview a Perforce server to create a token there.
 */
void call(Class project) {
    for (p4_server in project.p4_extra_servers) {
        String workspace_type = p4_server.workspace_type ?: 'streams'
        String workspace_name_postfix = p4_server.workspace_name_postfix ?: ''
        String workspace_name = p4_server.p4_creds + workspace_name_postfix + '-previewonly'

        switch (workspace_type) {
            case 'streams': p4_checkout_stream(p4_server, workspace_name); break
            case 'manual': p4_checkout_manual(p4_server, workspace_name); break
            default: throw new CobraException('Unknown workspace type: ' + workspace_type + ', aborting.')
        }
    }
}

void p4_checkout_stream(Map p4_server, String workspace_name) {
    checkout perforce(
        credential: p4_server.p4_creds,
        workspace: streamSpec(
            charset: 'none',
            pinHost: true,
            streamName: p4_server.p4_stream,
            format: workspace_name,
        ),
        populate: previewOnly(
            quiet: true
        )
    )
}

void p4_checkout_manual(Map p4_server, String workspace_name) {
    checkout perforce(
        credential: p4_server.p4_creds,
        workspace: manualSpec(
            charset: 'none',
            pinHost: true,
            name: workspace_name,
            spec: clientSpec(
                allwrite: false,
                clobber: true,
                compress: false,
                locked: false,
                modtime: false,
                rmdir: false,
                streamName: '',
                line: 'LOCAL',
                view: p4_server.view,
                changeView: '',
                type: 'WRITABLE',
                serverID: '',
                backup: true
            ),
            cleanup: false,
            syncID: ''
        ),
        populate: previewOnly(
            quiet: true
        )
    )
}
