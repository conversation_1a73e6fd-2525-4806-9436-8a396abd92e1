"""
test_nant.py

Used to call fb test_nant. Runs the NAnt Regression tests using the frostbite masterconfig.
"""
import click
from elipy2.cli import pass_context
from elipy2.frostbite import fbcli


@click.command(
    "test_nant",
    short_help="Used to call fb test_nant. Runs the NAnt Regression\
                tests using the frostbite masterconfig.",
)
@click.argument("test")
@click.option(
    "-show_output",
    type=bool,
    default=False,
    help="Print the output of the test as the test is\
               running, normally the output is written\
               to a log and only printed when the test fails.",
)
@click.option(
    "-retest_failures",
    type=bool,
    default=False,
    help="Only tests that failed or were not\
               previously reached in the last run should be tested.",
)
@click.option("-gensln", type=bool, default=False, help="Generate a solution for a specific test.")
@click.option(
    "-linux",
    type=bool,
    default=False,
    help="Runs test_nant in Windows Subsystem for Linux (WSL).\
               You need wsl installed and mono installed into it for this to work.",
)
@pass_context
def cli(_, test, show_output, retest_failures, gensln, linux):
    """
    Used to call fb test_nant. Runs the NAnt Regression tests using the frostbite masterconfig.
    """
    potential_args = {
        "-show_output": show_output,
        "-retest_failures": retest_failures,
        "-gensln": gensln,
        "-linux": linux,
    }
    args = [test]
    for arg_name, arg_value in potential_args.items():
        if arg_value:
            args.append(arg_name)

    fbcli.run("test_nant", method_args=args)
