"""
test_bilbo_register_drone.py

Unit testing for bilbo_register_drone
"""

import os
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import call, patch, MagicMock
from dice_elipy_scripts.bilbo_register_drone import cli
from elipy2.config import ConfigManager

config_path = os.path.join(os.path.dirname(__file__), "data", "elipy_test.yml")


@patch("dice_elipy_scripts.bilbo_register_drone.SETTINGS", ConfigManager(path=config_path))
@patch("dice_elipy_scripts.bilbo_register_drone.build_metadata_utils.setup_metadata_manager")
@patch("elipy2.filer_paths.get_code_build_root_path", MagicMock(return_value="PATH"))
class TestBilboRegisterDrone:
    """
    Test class
    """

    DEFAULT_ARGS = [
        "--dataset",
        "a_data_set",
        "--code-branch",
        "code_branch",
        "--code-changelist",
        "11154536",
        "--data-branch",
        "data_branch",
        "--data-changelist",
        "4381933",
    ]

    DEFAULT_ARGS_MULTI_LOC = DEFAULT_ARGS + ["--extra-location", "different_location"]

    # Add args with skip-code-smoke-tag flag for OneTrunk workflow
    DEFAULT_ARGS_SKIP_CODE_SMOKE = DEFAULT_ARGS_MULTI_LOC + ["--skip-code-smoke-tag"]

    # Add args without skip-code-smoke-tag flag for normal workflow
    DEFAULT_ARGS_WITH_CODE_SMOKE = DEFAULT_ARGS_MULTI_LOC

    def test_register_called_once(self, mock_setup_metadata_manager):
        mock_manager_instance = mock_setup_metadata_manager.return_value

        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)
        assert result.exit_code == 0
        assert mock_manager_instance.register_drone_build.call_count == 1

    def test_register_called_multiple_times(self, mock_setup_metadata_manager):
        mock_manager_instance = mock_setup_metadata_manager.return_value

        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS_MULTI_LOC)
        assert result.exit_code == 0
        assert mock_manager_instance.register_drone_build.call_count == 2

    def test_register_call_multiple_times_with(self, mock_setup_metadata_manager):
        mock_manager_instance = mock_setup_metadata_manager.return_value

        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS_MULTI_LOC)
        assert result.exit_code == 0

        calls = [
            call(
                path="PATH",
                data_changelist="4381933",
                code_changelist="11154536",
                code_branch="code_branch",
                data_branch="data_branch",
                dataset="a_data_set",
                write_attributes_file=True,
            ),
            call(
                path="PATH",
                data_changelist="4381933",
                code_changelist="11154536",
                code_branch="code_branch",
                data_branch="data_branch",
                dataset="a_data_set",
                write_attributes_file=False,
            ),
        ]

        mock_manager_instance.register_drone_build.assert_has_calls(calls)

    @patch("dice_elipy_scripts.bilbo_register_drone.LOGGER", MagicMock())
    def test_copy_smoke_tag_to_remote_locations(self, mock_setup_metadata_manager):
        """Test copying smoke tags to remote locations with default behavior (should copy code smoke tag if original has it)."""
        # Setup mock for original location and remote location
        mock_original_manager = MagicMock()
        mock_remote_manager = MagicMock()

        # Configure mock to return different instances for different locations
        # We need to return the same manager for the first two calls (original location check and first location in the loop)
        # and then the remote manager for the second location
        mock_setup_metadata_manager.side_effect = [
            mock_original_manager,
            mock_original_manager,
            mock_remote_manager,
        ]

        # Setup mock build with smoke tag
        mock_build = MagicMock()
        mock_build.source = {
            "build_promotion_level": "qa_verified",
            "location": "DiceStockholm",
            "changelist": "4381933",
            "verified_data": [
                {
                    "build_promotion_level": "qa_verified",
                    "changelist": "4381933",
                    "branch": "data_branch",
                    "dataset": "a_data_set",
                    "timestamp": "2025-04-24T19:40:35.217590",
                }
            ],
        }

        # Setup mock for remote build
        mock_remote_build = MagicMock()
        mock_remote_build.source = {}

        # Configure get_all_builds_query_string to return our mock builds
        mock_original_manager.get_all_builds_query_string.return_value = [mock_build]

        # Run the command with explicit code smoke tag
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS_WITH_CODE_SMOKE)

        # Verify the result
        assert result.exit_code == 0
        assert mock_setup_metadata_manager.call_count == 3
        assert mock_remote_manager.register_drone_build.call_count == 1
        # Check that tag_data_only_as_smoked is called
        assert mock_remote_manager.tag_data_only_as_smoked.call_count >= 1
        # Verify that tag_code_build_as_smoked IS called when original has code smoke tag
        assert mock_remote_manager.tag_code_build_as_smoked.call_count >= 1

    @patch("dice_elipy_scripts.bilbo_register_drone.LOGGER", MagicMock())
    def test_copy_smoke_tag_to_remote_locations_skip_code_smoke(self, mock_setup_metadata_manager):
        """Test copying smoke tags to remote locations with skip-code-smoke-tag flag (OneTrunk workflow)."""
        # Setup mock for original location and remote location
        mock_original_manager = MagicMock()
        mock_remote_manager = MagicMock()

        # Configure mock to return different instances for different locations
        mock_setup_metadata_manager.side_effect = [
            mock_original_manager,
            mock_original_manager,
            mock_remote_manager,
        ]

        # Setup mock build with smoke tag
        mock_build = MagicMock()
        mock_build.source = {
            "build_promotion_level": "qa_verified",
            "location": "DiceStockholm",
            "changelist": "4381933",
            "verified_data": [
                {
                    "build_promotion_level": "qa_verified",
                    "changelist": "4381933",
                    "branch": "data_branch",
                    "dataset": "a_data_set",
                    "timestamp": "2025-04-24T19:40:35.217590",
                }
            ],
        }

        # Configure get_all_builds_query_string to return our mock builds
        mock_original_manager.get_all_builds_query_string.return_value = [mock_build]

        # Run the command with skip-code-smoke-tag flag
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS_SKIP_CODE_SMOKE)

        # Verify the result
        assert result.exit_code == 0
        assert mock_setup_metadata_manager.call_count == 3
        assert mock_remote_manager.register_drone_build.call_count == 1
        # Check that tag_data_only_as_smoked is called
        assert mock_remote_manager.tag_data_only_as_smoked.call_count >= 1
        # Verify that tag_code_build_as_smoked is NOT called (OneTrunk workflow)
        assert mock_remote_manager.tag_code_build_as_smoked.call_count == 0

    @patch("dice_elipy_scripts.bilbo_register_drone.LOGGER", MagicMock())
    def test_copy_smoke_tag_to_remote_locations_when_original_not_code_smoked(
        self, mock_setup_metadata_manager
    ):
        """Test copying smoke tags to remote locations when original doesn't have code smoke tag."""
        # Setup mock for original location and remote location
        mock_original_manager = MagicMock()
        mock_remote_manager = MagicMock()

        # Configure mock to return different instances for different locations
        mock_setup_metadata_manager.side_effect = [
            mock_original_manager,
            mock_original_manager,
            mock_remote_manager,
        ]

        # Setup mock build WITHOUT code smoke tag
        mock_build = MagicMock()
        mock_build.source = {
            "build_promotion_level": None,  # No code smoke tag
            "location": "DiceStockholm",
            "changelist": "4381933",
            "verified_data": [
                {
                    "build_promotion_level": "qa_verified",
                    "changelist": "4381933",
                    "branch": "data_branch",
                    "dataset": "a_data_set",
                    "timestamp": "2025-04-24T19:40:35.217590",
                }
            ],
        }

        # Configure get_all_builds_query_string to return our mock builds
        mock_original_manager.get_all_builds_query_string.return_value = [mock_build]

        # Run the command
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS_WITH_CODE_SMOKE)

        # Verify the result
        assert result.exit_code == 0
        assert mock_setup_metadata_manager.call_count == 3
        assert mock_remote_manager.register_drone_build.call_count == 1
        # Check that tag_data_only_as_smoked is called
        assert mock_remote_manager.tag_data_only_as_smoked.call_count >= 1
        # Verify that tag_code_build_as_smoked is NOT called when original doesn't have code smoke tag
        assert mock_remote_manager.tag_code_build_as_smoked.call_count == 0
