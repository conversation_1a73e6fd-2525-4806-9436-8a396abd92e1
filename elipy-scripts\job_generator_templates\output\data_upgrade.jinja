{#
    Command:
        data_upgrade
            short_help: Upgrade data locally on a branch.

    Arguments:

    Required variables:
        code_branch
            required: True
            help: Perforce branch/stream name.
        code_changelist
            required: True
            help: Perforce code changelist.
        data_changelist
            required: True
            help: Perforce data changelist.
        p4_port
            required: True
            help: Perforce port/server.
        p4_client
            required: True
            help: Perforce client/workspace.

    Optional variables:
        submit/__no_submit
            default: True
        p4_user
            default: None
            help: Perforce user name.
        clean
            default: false
            help: Delete TnT/Local if --clean true is passed.
        data_directory
            default: None
            help: Which data directory to use for fetching licensee settings.
        licensee
            multiple: True
            default: None
            help: What licensee should gensln be ran against.
        password
            default: None
            help: User credentials to authenticate to package server.
        email
            default: None
            help: User email to authenticate to package server.
        domain_user
            default: None
            help: The user to authenticate to package server as DOMA<PERSON>\user.
        submit_folder
            default: None
            help: Restrict the submit to one folder.
        batch_file
            default: UpgradeLocal.bat
            help: Upgrade batch file to run.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    data_upgrade
    --code-branch {{ code_branch }}
    --code-changelist {{ code_changelist }}
    --data-changelist {{ data_changelist }}
    --p4-port {{ p4_port }}
    --p4-client {{ p4_client }}
    {%- if submit/__no_submit %}
    --submit/--no-submit {{ submit/__no_submit }}
    {%- endif %}
    {%- if p4_user %}
    --p4-user {{ p4_user }}
    {%- endif %}
    {%- if clean %}
    --clean {{ clean }}
    {%- endif %}
    {%- if data_directory %}
    --data-directory {{ data_directory }}
    {%- endif %}
    {%- if licensee %}
    --licensee {{ licensee }}
    {%- endif %}
    {%- if password %}
    --password {{ password }}
    {%- endif %}
    {%- if email %}
    --email {{ email }}
    {%- endif %}
    {%- if domain_user %}
    --domain-user {{ domain_user }}
    {%- endif %}
    {%- if submit_folder %}
    --submit-folder {{ submit_folder }}
    {%- endif %}
    {%- if batch_file %}
    --batch-file {{ batch_file }}
    {%- endif %}
  displayName: elipy data_upgrade
