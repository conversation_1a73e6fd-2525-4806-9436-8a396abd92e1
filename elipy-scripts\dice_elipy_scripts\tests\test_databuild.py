"""
test_databuild.py

Unit testing for databuild
"""
import os
import copy
import unittest
import elipy2.frostbite.icepick
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import MagicMock, mock_open, patch, call, ANY
from dice_elipy_scripts.databuild import cli, deploy_state, remote_clone_state
from elipy2.config import ConfigManager

config_path = os.path.join(os.path.dirname(__file__), "data", "elipy_test.yml")


class BaseTestClass:
    ARGUMENT_DATA_DIR = "data_dir"
    ARGUMENT_PLATFORM = "win64"
    ARGUMENT_ASSETS = "test_assets"

    OPTION_CODE_BRANCH = "--code-branch"
    OPTION_CODE_CHANGELIST = "--code-changelist"
    OPTION_DATA_BRANCH = "--data-branch"
    OPTION_DATA_CHANGELIST = "--data-changelist"
    OPTION_PIPELINE_ARGS_HYPHEN = "--pipeline-args"
    OPTION_EXPORT_AVALANCHE_STATE = "--export-avalanche-state"
    OPTION_IMPORT_AVALANCHE_STATE = "--import-avalanche-state"
    OPTION_DATA_CLEAN = "--data-clean"
    OPTION_USE_RECOMPRESSION_CACHE = "--use-recompression-cache"
    OPTION_EXPORT_COMBINE_BUNDLES = "--export-combine-bundles"
    OPTION_EXPORT_SUPER_BUNDLES = "--export-super-bundles"
    OPTION_CLONE_HOST = "--clone-host"
    OPTION_EXPORT_SVEN = "--export-sven"
    OPTION_ENABLE_COMPRESSION = "--enable-compression"
    OPTION_CLEAN_MASTER_VERSION_CHECK = "--clean-master-version-check"
    OPTION_EXPRESSION_DEBUG_DATA = "--expression-debug-data"
    OPTION_ENLIGHTEN_MODE = "--enlighten-mode"
    OPTION_ENLIGHTEN_ASSET_FILTER = "--enlighten-asset-filter"
    OPTION_ENLIGHTEN_TYPE = "--enlighten-type"
    OPTION_P4_PORT = "--p4-port"
    OPTION_P4_CLIENT = "--p4-client"
    OPTION_PROPERTIES_FILE = "--properties-file"
    OPTION_DB_NAME_PREFIX = "--db-name-prefix"
    OPTION_PULLBUILD_ARGS = "--pullbuild-args"
    OPTION_LICENSEE = "--licensee"
    OPTION_custom_tag = "--custom-tag"
    OPTION_ICEPICK_SUITES = "--icepick-suites"
    OPTION_FETCH_PIPELINE = "--fetch-pipeline"
    OPTION_ENABLE_HAILSTORM = "--enable-hailstorm"
    OPTION_DATABASE_ID = "--database-id"
    OPTION_DEPLOY_BUNDLES_LOCAL = "--deploy-super-bundles-local"
    OPTION_USE_SUPER_BUNDLES_PATH = "--super-bundles-deploy-path"
    OPTION_FB_ENV_VALUES = "--fb-env-values"
    OPTION_VIRTUAL_BRANCH_OVERRIDE = "--virtual-branch-override"
    OPTION_CONTENT_LAYERS = "--content-layers"
    OPTION_INCLUDE_DEFAULT_LAYER = "--include-default-layer"

    VALUE_CODE_BRANCH = "code_branch"
    VALUE_CODE_CHANGELIST = "1234"
    VALUE_DATA_BRANCH = "data_branch"
    VALUE_DATA_CHANGELIST = "5678"
    VALUE_PIPELINE_ARGS = "arg1"
    VALUE_EXPORT_AVALANCHE_STATE = "true"
    VALUE_IMPORT_AVALANCHE_STATE = "true"
    VALUE_DATA_CLEAN = "true"
    VALUE_USE_RECOMPRESSION_CACHE = "true"
    VALUE_EXPORT_COMBINE_BUNDLES = "true"
    VALUE_EXPORT_SUPER_BUNDLES = "true"
    VALUE_CLONE_HOST = "clone.host.address"
    VALUE_ENABLE_COMPRESSION = "true"
    VALUE_CLEAN_MASTER_VERSION_CHECK = "true"
    VALUE_EXPRESSION_DEBUG_DATA = "true"
    VALUE_ENLIGHTEN_MODE_1 = "shelve"
    VALUE_ENLIGHTEN_MODE_2 = "submit"
    VALUE_ENLIGHTEN_ASSET_FILTER = "enlighten_filter"
    VALUE_ENLIGHTEN_TYPE_ASSET = "asset"
    VALUE_ENLIGHTEN_TYPE_ZONESTREAMER = "ZoneStreamer"
    VALUE_P4_PORT = "p4_port"
    VALUE_P4_CLIENT = "p4_client"
    VALUE_PROPERTIES_FILE = "properties_file"
    VALUE_DB_NAME_PREFIX = "test_db_prefix"
    VALUE_custom_tag = "test_folder"
    VALUE_DATABASE_ID = "Data.Branch.Platform"
    VALUE_USE_SUPER_BUNDLES_PATH = "superbundles\\path"
    VALUE_FB_ENV_VALUES_1 = "key1=value1"
    VALUE_FB_ENV_VALUES_2 = "key2=value2"
    VALUE_VIRTUAL_BRANCH_OVERRIDE = "true"
    VALUE_CONTENT_LAYERS_1 = "layer1"
    VALUE_CONTENT_LAYERS_2 = "layer2"

    BASIC_ARGS = [
        ARGUMENT_DATA_DIR,
        ARGUMENT_PLATFORM,
        ARGUMENT_ASSETS,
        OPTION_CODE_BRANCH,
        VALUE_CODE_BRANCH,
        OPTION_CODE_CHANGELIST,
        VALUE_CODE_CHANGELIST,
        OPTION_DATA_BRANCH,
        VALUE_DATA_BRANCH,
        OPTION_DATA_CHANGELIST,
        VALUE_DATA_CHANGELIST,
    ]

    P4_ARGS_HYPHEN = [
        OPTION_P4_PORT,
        VALUE_P4_PORT,
        OPTION_P4_CLIENT,
        VALUE_P4_CLIENT,
    ]


@patch("os.path.join", MagicMock(side_effect=lambda *args: "\\".join(args)))
@patch("elipy2.avalanche.drop", MagicMock())
@patch("elipy2.avalanche.restart_avalanche", MagicMock())
@patch("elipy2.avalanche.set_avalanche_build_status", MagicMock())
@patch("elipy2.running_processes.kill", MagicMock())
@patch("elipy2.telemetry.upload_metrics", MagicMock())
@patch("dice_elipy_scripts.databuild.add_sentry_tags", MagicMock())
class TestDatabuild(unittest.TestCase, BaseTestClass):
    def setUp(self):
        config_manager = ConfigManager(path=config_path)
        self.patcher_settings_get = patch("dice_elipy_scripts.databuild.SETTINGS", config_manager)
        self.mock_settings_get = self.patcher_settings_get.start()

        self.patcher_datautils = patch("elipy2.data.DataUtils", autospec=True)
        self.mock_datautils = self.patcher_datautils.start()

        self.patcher_filerutils = patch("elipy2.filer.FilerUtils", autospec=True)
        self.mock_filerutils = self.patcher_filerutils.start()

        self.patcher_p4utils = patch("elipy2.p4.P4Utils", autospec=True)
        self.mock_p4utils = self.patcher_p4utils.start()
        self.mock_p4utils.return_value.latest_pending_changelist.return_value = "1122"

        self.patcher_get_temp_db_name = patch("elipy2.avalanche.get_temp_db_name", autospec=True)
        self.mock_get_temp_db_name = self.patcher_get_temp_db_name.start()
        self.mock_get_temp_db_name.return_value = "avalanche_temp_db"

        self.patcher_minimum_fb_version = patch(
            "elipy2.frostbite_core.minimum_fb_version", autospec=True
        )
        self.mock_minimum_fb_version = self.patcher_minimum_fb_version.start()

        self.patcher_fbenv_layer_cook = patch("elipy2.frostbite.fbenv_layer.cook", autospec=True)
        self.mock_fbenv_layer_cook = self.patcher_fbenv_layer_cook.start()

        self.patcher_set_licensee = patch(
            "dice_elipy_scripts.databuild.set_licensee", autospec=True
        )
        self.mock_set_licensee = self.patcher_set_licensee.start()
        self.mock_set_licensee.return_value = ["licensee_arg"]

        self.patcher_fbcli_pullbuild = patch("elipy2.frostbite.fbcli.pullbuild", autospec=True)
        self.mock_fbcli_pullbuild = self.patcher_fbcli_pullbuild.start()

    def tearDown(self):
        os.environ["use_fbcli"] = "False"
        patch.stopall()

    def test_basic_args(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0

    def test_basic_args_no_asset(self):
        runner = CliRunner()
        _new_args = self.BASIC_ARGS.copy()
        del _new_args[2]
        result = runner.invoke(cli, _new_args)
        assert "Usage:" not in result.output
        assert result.exit_code == 0

    def test_pipeline_args_hyphen(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_PIPELINE_ARGS_HYPHEN, self.VALUE_PIPELINE_ARGS]
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_datautils.return_value.cook.assert_called_once_with(
            pipeline_args=["arg1"],
            indexing_args=[],
            collect_mdmps=True,
            trim=True,
            clean_master_version_check=False,
            disable_caches=False,
        )

    def test_pipeline_args_with_database_id(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_DATABASE_ID, self.VALUE_DATABASE_ID]
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_datautils.return_value.cook.assert_called_once_with(
            pipeline_args=["-databaseId Data.Branch.Platform"],
            indexing_args=[],
            collect_mdmps=True,
            trim=True,
            clean_master_version_check=False,
            disable_caches=False,
        )

    @patch(
        "dice_elipy_scripts.databuild.icepick.IcepickUtils.run_icepick_cook",
        spec=elipy2.frostbite.icepick.IcepickUtils.run_icepick_cook,
    )
    def test_basic_args_disable_hailstorm(self, patch_run_icepick_cook: MagicMock):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [self.OPTION_ICEPICK_SUITES, "test-suite", self.OPTION_ENABLE_HAILSTORM, "False"],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0

        patch_run_icepick_cook.assert_called_once_with(
            platform="win64", test_suites=("test-suite",), pipeline_args=[], enable_hailstorm=False
        )

    @patch("dice_elipy_scripts.databuild.remote_clone_state", MagicMock())
    def test_avalanche_db_name(self):
        self.mock_minimum_fb_version.return_value = True
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [self.OPTION_EXPORT_AVALANCHE_STATE, self.VALUE_EXPORT_AVALANCHE_STATE],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_datautils.return_value.cook.assert_called_once_with(
            pipeline_args=["-exportState", "avalanche_temp_db_export"],
            indexing_args=[],
            collect_mdmps=True,
            trim=True,
            clean_master_version_check=False,
            disable_caches=False,
        )

    def test_clean(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_DATA_CLEAN, self.VALUE_DATA_CLEAN]
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_datautils.return_value.clean.assert_called_once_with(extra_pipeline_args=[])

    def test_clean_virtual_branch_override(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_DATA_CLEAN,
                self.VALUE_DATA_CLEAN,
                self.OPTION_VIRTUAL_BRANCH_OVERRIDE,
                "true",
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_datautils.return_value.clean.assert_called_once_with(
            extra_pipeline_args=["-stateId", self.VALUE_DATA_BRANCH]
        )

    @patch("dice_elipy_scripts.databuild.import_avalanche_data_state")
    def test_import_avalanche_state(self, mock_import_avalanche):
        mock_import_avalanche.return_value = ["avalanche", "import", "args"]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [self.OPTION_IMPORT_AVALANCHE_STATE, self.VALUE_IMPORT_AVALANCHE_STATE],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        mock_import_avalanche.assert_called_once_with(
            self.VALUE_DATA_BRANCH,
            self.VALUE_CODE_BRANCH,
            self.ARGUMENT_PLATFORM,
            self.mock_filerutils.return_value,
            self.VALUE_DATA_CHANGELIST,
        )
        self.mock_datautils.return_value.cook.assert_called_once_with(
            pipeline_args=["avalanche", "import", "args"],
            indexing_args=[],
            collect_mdmps=True,
            trim=True,
            clean_master_version_check=False,
            disable_caches=False,
        )

    def test_enlighten_basic_args(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_ENLIGHTEN_MODE,
                self.VALUE_ENLIGHTEN_MODE_1,
                self.OPTION_ENLIGHTEN_TYPE,
                self.VALUE_ENLIGHTEN_TYPE_ASSET,
            ]
            + self.P4_ARGS_HYPHEN,
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_datautils.return_value.cook.assert_called_once_with(
            pipeline_args=[
                "-f",
                "generateSceneDesc",
                "-functionParameters",
                "(false,GiBakeMode_SNDBS,false,false,false,false,false,false,false)",
            ],
            indexing_args=[],
            collect_mdmps=True,
            trim=True,
            clean_master_version_check=False,
            disable_caches=False,
        )
        self.mock_fbenv_layer_cook.assert_called_once_with(
            pipeline_args=[
                "-hailstorm",
                "res-hailstorm.la.ad.ea.com",
                "-f",
                "uploadSandboxes",
                "-functionParameters",
                "{['Changelist']='1122', ['ChangeDescriptionInP4']=true}",
            ]
        )

    def test_enlighten_no_p4_args(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS + [self.OPTION_ENLIGHTEN_MODE, self.VALUE_ENLIGHTEN_MODE_1],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 1

    def test_enlighten_asset_filter(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_ENLIGHTEN_MODE,
                self.VALUE_ENLIGHTEN_MODE_1,
                self.OPTION_ENLIGHTEN_ASSET_FILTER,
                self.VALUE_ENLIGHTEN_ASSET_FILTER,
                self.OPTION_ENLIGHTEN_TYPE,
                self.VALUE_ENLIGHTEN_TYPE_ASSET,
            ]
            + self.P4_ARGS_HYPHEN,
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_datautils.return_value.cook.assert_called_once_with(
            pipeline_args=[
                "-f",
                "generateSceneDesc",
                "-functionParameters",
                "(false,GiBakeMode_SNDBS,false,false,false,false,false,false,false)",
                "-gi.EnlightenAssetFilter",
                self.VALUE_ENLIGHTEN_ASSET_FILTER,
            ],
            indexing_args=[],
            collect_mdmps=True,
            trim=True,
            clean_master_version_check=False,
            disable_caches=False,
        )

    def test_enlighten_zone_streamer(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_ENLIGHTEN_MODE,
                self.VALUE_ENLIGHTEN_MODE_1,
                self.OPTION_ENLIGHTEN_ASSET_FILTER,
                self.VALUE_ENLIGHTEN_ASSET_FILTER,
                self.OPTION_ENLIGHTEN_TYPE,
                self.VALUE_ENLIGHTEN_TYPE_ZONESTREAMER,
            ]
            + self.P4_ARGS_HYPHEN,
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_datautils.return_value.cook.assert_called_once_with(
            pipeline_args=[
                "-f",
                "bakeGlobalRadiosity",
                "-zsbake.GlobalSolution",
                "true",
                "-zsbake.BakeMode",
                "GiBakeMode_SNDBS",
            ],
            indexing_args=[],
            collect_mdmps=True,
            trim=True,
            clean_master_version_check=False,
            disable_caches=False,
        )

    def test_enlighten_p4_hyphen(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [self.OPTION_ENLIGHTEN_MODE, self.VALUE_ENLIGHTEN_MODE_1]
            + self.P4_ARGS_HYPHEN,
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0

    @patch.dict(os.environ, {"BUILD_URL": "jenkins-url.fake"}, clear=True)
    def test_enlighten_shelve(self):
        changelist_description = "Enlighten bake of {} with:\ncode {}\ndata {}".format(
            [self.ARGUMENT_ASSETS],
            self.VALUE_CODE_CHANGELIST,
            self.VALUE_DATA_CHANGELIST,
        )
        changelist_description += "\nJenkins URL: jenkins-url.fake"
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [self.OPTION_ENLIGHTEN_MODE, self.VALUE_ENLIGHTEN_MODE_1]
            + self.P4_ARGS_HYPHEN,
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_p4utils.return_value.set_description.assert_called_once_with(
            "1122", changelist_description
        )
        self.mock_p4utils.return_value.shelve.assert_called_once_with("1122", discard=False)

    @patch.dict(os.environ, {"BUILD_URL": "jenkins-url.fake"}, clear=True)
    def test_enlighten_submit(self):
        changelist_description = "Enlighten bake of {} with:\ncode {}\ndata {}".format(
            [self.ARGUMENT_ASSETS], self.VALUE_CODE_CHANGELIST, self.VALUE_DATA_CHANGELIST
        )
        changelist_description += "\nJenkins URL: jenkins-url.fake"
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [self.OPTION_ENLIGHTEN_MODE, self.VALUE_ENLIGHTEN_MODE_2]
            + self.P4_ARGS_HYPHEN,
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_p4utils.return_value.reopen.assert_called_once_with()
        self.mock_p4utils.return_value.submit.assert_called_once_with(changelist_description)

    @patch.dict(os.environ, {"TEMP": "temp_path"}, clear=True)
    @patch("dice_elipy_scripts.databuild.open", new_callable=mock_open())
    def test_enlighten_write_file_shelve(self, mock_open_file):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_ENLIGHTEN_MODE,
                self.VALUE_ENLIGHTEN_MODE_1,
                self.OPTION_PROPERTIES_FILE,
                self.VALUE_PROPERTIES_FILE,
            ]
            + self.P4_ARGS_HYPHEN,
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        mock_open_file.assert_called_once_with(
            os.path.join("temp_path", self.VALUE_PROPERTIES_FILE), "a+"
        )
        mock_open_file.return_value.__enter__().write.assert_called_once_with(
            "shelve_changelist = 1122\n"
        )

    @patch.dict(os.environ, {"TEMP": "temp_path"}, clear=True)
    @patch("dice_elipy_scripts.databuild.open", new_callable=mock_open())
    def test_enlighten_write_file_submit(self, mock_open_file):
        self.mock_p4utils.return_value.submit.return_value = [
            {b"submittedChange": b"5678"},
        ]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_ENLIGHTEN_MODE,
                self.VALUE_ENLIGHTEN_MODE_2,
                self.OPTION_PROPERTIES_FILE,
                self.VALUE_PROPERTIES_FILE,
            ]
            + self.P4_ARGS_HYPHEN,
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        mock_open_file.assert_called_once_with(
            os.path.join("temp_path", self.VALUE_PROPERTIES_FILE), "a+"
        )
        mock_open_file.return_value.__enter__().write.assert_called_once_with(
            "submit_changelist = 5678\n"
        )

    def test_enlighten_does_not_cook_content_layers(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_ENLIGHTEN_MODE,
                self.VALUE_ENLIGHTEN_MODE_1,
                self.OPTION_ENLIGHTEN_TYPE,
                self.VALUE_ENLIGHTEN_TYPE_ASSET,
                self.OPTION_CONTENT_LAYERS,
                self.VALUE_CONTENT_LAYERS_1,
            ]
            + self.P4_ARGS_HYPHEN,
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_datautils.return_value.cook.assert_called_once_with(
            pipeline_args=[
                "-f",
                "generateSceneDesc",
                "-functionParameters",
                "(false,GiBakeMode_SNDBS,false,false,false,false,false,false,false)",
            ],
            indexing_args=[],
            collect_mdmps=True,
            trim=True,
            clean_master_version_check=False,
            disable_caches=False,
        )
        self.mock_fbenv_layer_cook.assert_called_once_with(
            pipeline_args=[
                "-hailstorm",
                "res-hailstorm.la.ad.ea.com",
                "-f",
                "uploadSandboxes",
                "-functionParameters",
                "{['Changelist']='1122', ['ChangeDescriptionInP4']=true}",
            ]
        )

    @patch("dice_elipy_scripts.databuild.export_head_bundles")
    def test_export_head_bundles_no_extra_arg(self, mock_export_head_bundles):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS + [self.OPTION_EXPORT_SUPER_BUNDLES, self.VALUE_EXPORT_SUPER_BUNDLES],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        mock_export_head_bundles.assert_called_once_with(
            self.ARGUMENT_PLATFORM,
            os.path.join("tnt_root", "local", "bundles", "head"),
            deploy_extra_args=[],
            db_name=None,
        )

    @patch("dice_elipy_scripts.databuild.export_head_bundles")
    @patch("elipy2.filer.FilerUtils.deploy_head_bundles")
    def test_deploy_bundles_only(self, mock_deploy_head_bundles, mock_export_head_bundles):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS + [self.OPTION_DEPLOY_BUNDLES_LOCAL, True],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        mock_export_head_bundles.assert_called_once_with(
            self.ARGUMENT_PLATFORM,
            os.path.join("tnt_root", "local", "bundles", "head"),
            deploy_extra_args=[],
            db_name=None,
        )
        assert mock_deploy_head_bundles.called is False

    @patch("dice_elipy_scripts.databuild.export_head_bundles")
    def test_deploy_bundles_only_with_path_name(self, mock_export_head_bundles):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_DEPLOY_BUNDLES_LOCAL,
                True,
                self.OPTION_USE_SUPER_BUNDLES_PATH,
                self.VALUE_USE_SUPER_BUNDLES_PATH,
                self.OPTION_DATABASE_ID,
                self.VALUE_DATABASE_ID,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        mock_export_head_bundles.assert_called_once_with(
            self.ARGUMENT_PLATFORM,
            os.path.join("tnt_root", "local", "superbundles", "path"),
            deploy_extra_args=[],
            db_name="Data.Branch.Platform",
        )

    @patch("dice_elipy_scripts.databuild.get_export_compression_args")
    @patch("dice_elipy_scripts.databuild.export_head_bundles")
    def test_export_head_bundles_enable_compression(
        self, mock_export_head_bundles, mock_get_export_compression_args
    ):
        mock_get_export_compression_args.return_value = ["export", "compression", "args"]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_EXPORT_SUPER_BUNDLES,
                self.VALUE_EXPORT_SUPER_BUNDLES,
                self.OPTION_ENABLE_COMPRESSION,
                self.VALUE_ENABLE_COMPRESSION,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        mock_export_head_bundles.assert_called_once_with(
            self.ARGUMENT_PLATFORM,
            os.path.join("tnt_root", "local", "bundles", "head"),
            deploy_extra_args=["export", "compression", "args"],
            db_name=None,
        )

    @patch("dice_elipy_scripts.bilbo_register_drone.SETTINGS", ConfigManager(path=config_path))
    @patch("dice_elipy_scripts.databuild.export_head_bundles")
    def test_export_head_bundles_recompression_cache(self, mock_export_head_bundles):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_EXPORT_SUPER_BUNDLES,
                self.VALUE_EXPORT_SUPER_BUNDLES,
                self.OPTION_USE_RECOMPRESSION_CACHE,
                self.VALUE_USE_RECOMPRESSION_CACHE,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        mock_export_head_bundles.assert_called_once_with(
            self.ARGUMENT_PLATFORM,
            os.path.join("tnt_root", "local", "bundles", "head"),
            deploy_extra_args=["--recompressionCache", "server.address"],
            db_name=None,
        )

    @patch("dice_elipy_scripts.databuild.export_head_bundles")
    def test_export_combine_bundles_export(self, mock_export_head_bundles):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [self.OPTION_EXPORT_COMBINE_BUNDLES, self.VALUE_EXPORT_COMBINE_BUNDLES],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        mock_export_head_bundles.assert_called_once_with(
            self.ARGUMENT_PLATFORM,
            os.path.join("tnt_root", "local", "combine_bundles", "head"),
            deploy_extra_args=["-s", "pre-combine.yaml"],
            include_platform=False,
            ordering_algorithm=None,
        )

    @patch("dice_elipy_scripts.databuild.export_head_bundles", MagicMock())
    def test_export_combine_bundles_deploy(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [self.OPTION_EXPORT_COMBINE_BUNDLES, self.VALUE_EXPORT_COMBINE_BUNDLES],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_filerutils.return_value.deploy_head_bundles.assert_called_once_with(
            os.path.join("tnt_root", "local", "combine_bundles", "head"),
            data_branch=self.VALUE_DATA_BRANCH,
            data_changelist=self.VALUE_DATA_CHANGELIST,
            code_branch=self.VALUE_CODE_BRANCH,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            platform=self.ARGUMENT_PLATFORM,
            bundles_dir_name="combine_bundles",
        )

    @patch("dice_elipy_scripts.databuild.export_head_bundles", MagicMock())
    def test_content_layer_bundles_deploy(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_EXPORT_SUPER_BUNDLES,
                self.VALUE_EXPORT_SUPER_BUNDLES,
                self.OPTION_INCLUDE_DEFAULT_LAYER,
                "False",
                self.OPTION_CONTENT_LAYERS,
                self.VALUE_CONTENT_LAYERS_1,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_filerutils.return_value.deploy_head_bundles.assert_called_once_with(
            os.path.join("tnt_root", "local", "bundles", "head"),
            data_branch=self.VALUE_DATA_BRANCH,
            data_changelist=f"{self.VALUE_DATA_CHANGELIST}_{self.VALUE_CONTENT_LAYERS_1}",
            code_branch=self.VALUE_CODE_BRANCH,
            code_changelist=f"{self.VALUE_CODE_CHANGELIST}_{self.VALUE_CONTENT_LAYERS_1}",
            platform=self.ARGUMENT_PLATFORM,
        )

    @patch("dice_elipy_scripts.databuild.deploy_state")
    @patch("elipy2.frostbite_core.minimum_fb_version")
    def test_export_avalanche_state_old(self, mock_minimum_fb_version, mock_deploy_state):
        mock_minimum_fb_version.return_value = False
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [self.OPTION_EXPORT_AVALANCHE_STATE, self.VALUE_EXPORT_AVALANCHE_STATE],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        mock_deploy_state.assert_called_once_with(
            platform=self.ARGUMENT_PLATFORM,
            db_name="avalanche_temp_db",
            data_changelist=self.VALUE_DATA_CHANGELIST,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            data_branch=self.VALUE_DATA_BRANCH,
            _filer=self.mock_filerutils.return_value,
        )

    @patch("dice_elipy_scripts.databuild.run_expression_debug_data")
    def test_expression_debug_data(self, mock_run_expression_debug_data):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS + [self.OPTION_EXPRESSION_DEBUG_DATA, self.VALUE_EXPRESSION_DEBUG_DATA],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        mock_run_expression_debug_data.assert_called_once_with(
            self.VALUE_CODE_CHANGELIST,
            self.VALUE_DATA_CHANGELIST,
            self.VALUE_CODE_BRANCH,
            self.VALUE_DATA_BRANCH,
            self.ARGUMENT_PLATFORM,
            builder_instance=self.mock_datautils.return_value,
            pipeline_args=[],
            clean_master_version_check=False,
        )

    @patch("dice_elipy_scripts.databuild.run_expression_debug_data")
    def test_expression_debug_data_pipeline_args(self, mock_run_expression_debug_data):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_EXPRESSION_DEBUG_DATA,
                self.VALUE_EXPRESSION_DEBUG_DATA,
                self.OPTION_PIPELINE_ARGS_HYPHEN,
                self.VALUE_PIPELINE_ARGS,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        mock_run_expression_debug_data.assert_called_once_with(
            self.VALUE_CODE_CHANGELIST,
            self.VALUE_DATA_CHANGELIST,
            self.VALUE_CODE_BRANCH,
            self.VALUE_DATA_BRANCH,
            self.ARGUMENT_PLATFORM,
            builder_instance=self.mock_datautils.return_value,
            pipeline_args=["arg1"],
            clean_master_version_check=False,
        )

    @patch("dice_elipy_scripts.databuild.run_expression_debug_data")
    def test_expression_debug_data_clean_master_version_check(self, mock_run_expression_debug_data):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_EXPRESSION_DEBUG_DATA,
                True,
                self.OPTION_CLEAN_MASTER_VERSION_CHECK,
                self.VALUE_CLEAN_MASTER_VERSION_CHECK,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        mock_run_expression_debug_data.assert_called_once_with(
            self.VALUE_CODE_CHANGELIST,
            self.VALUE_DATA_CHANGELIST,
            self.VALUE_CODE_BRANCH,
            self.VALUE_DATA_BRANCH,
            self.ARGUMENT_PLATFORM,
            builder_instance=self.mock_datautils.return_value,
            pipeline_args=[],
            clean_master_version_check=True,
        )

    @patch("elipy2.avalanche.clean_temp_state_folder")
    @patch("elipy2.avalanche.export")
    @patch("elipy2.local_paths.get_local_avalanche_export_path")
    def test_deploy_state(
        self, mock_get_local_avalanche_export_path, mock_export, mock_clean_temp_state_folder
    ):
        mock_get_local_avalanche_export_path.return_value = "local_path"
        deploy_state(
            self.ARGUMENT_PLATFORM,
            "db_name",
            self.VALUE_DATA_CHANGELIST,
            self.VALUE_CODE_CHANGELIST,
            self.VALUE_DATA_BRANCH,
            self.mock_filerutils.return_value,
        )
        mock_export.assert_called_once_with("db_name", "local_path")
        self.mock_filerutils.return_value.deploy_avalanche_state.assert_called_once_with(
            self.ARGUMENT_PLATFORM,
            self.VALUE_DATA_BRANCH,
            self.VALUE_DATA_CHANGELIST,
            self.VALUE_CODE_CHANGELIST,
        )
        mock_clean_temp_state_folder.assert_called_once_with("local_path")

    @patch("elipy2.avalanche.get_temp_db_name")
    def test_db_name_prefix(self, mock_get_temp_db_name: MagicMock):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS + [self.OPTION_DB_NAME_PREFIX, self.VALUE_DB_NAME_PREFIX],
        )

        mock_get_temp_db_name.assert_called_once_with(ANY, ANY, ANY, ANY, self.VALUE_DB_NAME_PREFIX)

    def test_custom_tag(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_custom_tag, self.VALUE_custom_tag]
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_code.assert_called_once_with(
            self.VALUE_CODE_BRANCH,
            self.VALUE_CODE_CHANGELIST,
            "pipeline",
            "release",
            custom_tag=self.VALUE_custom_tag,
        )

    def test_no_fetch_pipeline(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_FETCH_PIPELINE, False])
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_code.assert_not_called()

    def test_validate_frosted_platform_doesnt_fail(self):
        runner = CliRunner()
        test_args = list(self.BASIC_ARGS)
        # Set platform
        test_args[1] = "validate-frosted"
        result = runner.invoke(cli, test_args)
        assert result.exit_code == 0

    def test_validate_frosted_platform_doesnt_deploy(self):
        runner = CliRunner()
        test_args = list(self.BASIC_ARGS)
        # Set platform
        test_args[1] = "validate-frosted"
        mock_filer_deploy_head_bundles = self.mock_filerutils.return_value.deploy_head_bundles
        result = runner.invoke(cli, test_args)
        assert result.exit_code == 0
        mock_filer_deploy_head_bundles.assert_not_called()

    def test_validate_frosted_platform_passes_forceDebugTarget(self):
        runner = CliRunner()
        test_args = list(self.BASIC_ARGS)
        # Set platform
        test_args[1] = "validate-frosted"
        mock_cook: MagicMock = self.mock_datautils.return_value.cook
        result = runner.invoke(cli, test_args)
        assert result.exit_code == 0
        assert mock_cook.call_args_list[0][1]["pipeline_args"] == ["-forceDebugTarget"]

    def test_validate_frosted_platform_doesnt_pass_export_state(self):
        runner = CliRunner()
        test_args = list(self.BASIC_ARGS)
        # Set platform
        test_args[1] = "validate-frosted"
        mock_cook: MagicMock = self.mock_datautils.return_value.cook
        result = runner.invoke(cli, test_args)
        assert result.exit_code == 0
        assert "-exportState" not in mock_cook.call_args_list[0][1]["pipeline_args"]

    def test_validate_frosted_platform_not_used_to_create_builder(self):
        runner = CliRunner()
        test_args = list(self.BASIC_ARGS)
        # Set platform
        test_args[1] = "validate-frosted"
        result = runner.invoke(cli, test_args)
        assert result.exit_code == 0
        self.mock_datautils.assert_called_with("win64", ["test_assets"], monkey_build_label="5678")

    @patch("elipy2.SETTINGS.get", MagicMock(return_value="\\fake_path\\here"))
    @patch("elipy2.core.is_buildsystem_run", MagicMock(return_value=True))
    def test_filer_authentication(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + ["--filer-user", "user", "--filer-password", "password"]
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.delete_network_connection.assert_called_once()
        self.mock_filerutils.return_value.auth_network_connection.assert_called_once_with(
            network_path="\\fake_path\\here",
            username="user",
            password="password",
        )

    def test_virtual_branch_override(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [self.OPTION_VIRTUAL_BRANCH_OVERRIDE, self.VALUE_VIRTUAL_BRANCH_OVERRIDE],
        )
        assert result.exit_code == 0
        self.mock_datautils.return_value.cook.assert_called_once_with(
            pipeline_args=[
                "-BuildSettings.ForceBranch",
                self.VALUE_DATA_BRANCH,
                "-stateId",
                self.VALUE_DATA_BRANCH,
            ],
            indexing_args=["-stateId", self.VALUE_DATA_BRANCH],
            collect_mdmps=True,
            trim=True,
            clean_master_version_check=False,
            disable_caches=False,
        )

    @patch("elipy2.frostbite.fbenv_layer.set_environment_values")
    def test_set_environment_values(self, mock_set_environment_values):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_FB_ENV_VALUES,
                self.VALUE_FB_ENV_VALUES_1,
                self.OPTION_FB_ENV_VALUES,
                self.VALUE_FB_ENV_VALUES_2,
            ],
        )
        assert result.exit_code == 0
        mock_set_environment_values.assert_called_once_with({"key1": "value1", "key2": "value2"})

    def test_content_layers(self):
        cook_args = {
            "pipeline_args": [self.VALUE_PIPELINE_ARGS],
            "indexing_args": [],
            "collect_mdmps": True,
            "trim": True,
            "clean_master_version_check": False,
            "disable_caches": False,
        }
        content_layer1_cook_args = copy.deepcopy(cook_args)
        content_layer1_cook_args["pipeline_args"] += [
            "-activeContentLayer",
            self.VALUE_CONTENT_LAYERS_1,
        ]
        content_layer2_cook_args = copy.deepcopy(cook_args)
        content_layer2_cook_args["pipeline_args"] += [
            "-activeContentLayer",
            self.VALUE_CONTENT_LAYERS_2,
        ]

        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_PIPELINE_ARGS_HYPHEN,
                self.VALUE_PIPELINE_ARGS,
                self.OPTION_CONTENT_LAYERS,
                self.VALUE_CONTENT_LAYERS_1,
                self.OPTION_CONTENT_LAYERS,
                self.VALUE_CONTENT_LAYERS_2,
            ],
        )
        assert "Usage:" not in result.output
        assert self.mock_datautils.return_value.cook.call_count == 3
        self.mock_datautils.return_value.cook.assert_has_calls(
            [call(**cook_args), call(**content_layer1_cook_args), call(**content_layer2_cook_args)],
            any_order=False,
        )
        assert result.exit_code == 0

    def test_content_layers_without_default_layer(self):
        cook_args = {
            "pipeline_args": [self.VALUE_PIPELINE_ARGS],
            "indexing_args": [],
            "collect_mdmps": True,
            "trim": True,
            "clean_master_version_check": False,
            "disable_caches": False,
        }
        content_layer1_cook_args = copy.deepcopy(cook_args)
        content_layer1_cook_args["pipeline_args"] += [
            "-activeContentLayer",
            self.VALUE_CONTENT_LAYERS_1,
        ]
        content_layer2_cook_args = copy.deepcopy(cook_args)
        content_layer2_cook_args["pipeline_args"] += [
            "-activeContentLayer",
            self.VALUE_CONTENT_LAYERS_2,
        ]

        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_PIPELINE_ARGS_HYPHEN,
                self.VALUE_PIPELINE_ARGS,
                self.OPTION_INCLUDE_DEFAULT_LAYER,
                "False",
                self.OPTION_CONTENT_LAYERS,
                self.VALUE_CONTENT_LAYERS_1,
                self.OPTION_CONTENT_LAYERS,
                self.VALUE_CONTENT_LAYERS_2,
            ],
        )
        assert "Usage:" not in result.output
        assert self.mock_datautils.return_value.cook.call_count == 2
        self.mock_datautils.return_value.cook.assert_has_calls(
            [call(**content_layer1_cook_args), call(**content_layer2_cook_args)], any_order=False
        )
        assert result.exit_code == 0


@patch("os.path.join", MagicMock(side_effect=lambda *args: "\\".join(args)))
@patch("elipy2.avalanche.restart_avalanche", MagicMock())
@patch("elipy2.avalanche.set_avalanche_build_status", MagicMock())
@patch("elipy2.data.DataUtils.cook", MagicMock())
@patch("elipy2.data.DataUtils.set_datadir", MagicMock())
@patch("elipy2.filer.FilerUtils.fetch_code", MagicMock())
@patch("elipy2.running_processes.kill", MagicMock())
@patch("elipy2.telemetry.upload_metrics", MagicMock())
@patch("dice_elipy_scripts.databuild.add_sentry_tags", MagicMock())
@patch("dice_elipy_scripts.databuild.set_licensee", MagicMock())
class TestDatabuildWithMetadata(BaseTestClass):
    @patch("elipy2.avalanche.get_temp_db_name", MagicMock())
    @patch("dice_elipy_scripts.databuild.export_head_bundles", MagicMock())
    @patch("elipy2.filer.FilerUtils.deploy_head_bundles")
    def test_export_super_bundles(self, mock_deploy_head_bundles, fixture_metadata_manager):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS + [self.OPTION_EXPORT_SUPER_BUNDLES, self.VALUE_EXPORT_SUPER_BUNDLES],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        mock_deploy_head_bundles.assert_called_once_with(
            os.path.join("tnt_root", "local", "bundles", "head"),
            data_branch=self.VALUE_DATA_BRANCH,
            data_changelist=self.VALUE_DATA_CHANGELIST,
            code_branch=self.VALUE_CODE_BRANCH,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            platform=self.ARGUMENT_PLATFORM,
        )
        fixture_metadata_manager.register_bundles.assert_called_once_with(
            data_branch=self.VALUE_DATA_BRANCH,
            data_changelist=self.VALUE_DATA_CHANGELIST,
            code_branch=self.VALUE_CODE_BRANCH,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            platform=self.ARGUMENT_PLATFORM,
        )

    @patch("elipy2.avalanche.get_temp_db_name", MagicMock())
    @patch("dice_elipy_scripts.databuild.export_head_bundles", MagicMock())
    @patch("elipy2.filer.FilerUtils.deploy_head_bundles", MagicMock())
    def test_export_combine_bundles(self, fixture_metadata_manager):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [self.OPTION_EXPORT_COMBINE_BUNDLES, self.VALUE_EXPORT_COMBINE_BUNDLES],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        fixture_metadata_manager.register_bundles.assert_called_once_with(
            data_branch=self.VALUE_DATA_BRANCH,
            data_changelist=self.VALUE_DATA_CHANGELIST,
            code_branch=self.VALUE_CODE_BRANCH,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            platform=self.ARGUMENT_PLATFORM,
            bundles_type="combine_bundles",
        )

    @patch("elipy2.avalanche.get_temp_db_name", MagicMock())
    @patch("elipy2.avalanche.remote_clone_db")
    @patch("elipy2.avalanche.get_full_database_name")
    def test_clone_host(
        self, mock_get_full_database_name, mock_remote_clone_db, fixture_metadata_manager
    ):
        mock_get_full_database_name.return_value = "source_db_name"
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_CLONE_HOST, self.VALUE_CLONE_HOST]
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        mock_get_full_database_name.assert_called_once_with(self.ARGUMENT_PLATFORM)
        mock_remote_clone_db.assert_called_once_with(
            "source_db_name",
            "source_db_name.{}.{}".format(self.VALUE_CODE_CHANGELIST, self.VALUE_DATA_CHANGELIST),
            "clone.host.address",
        )
        fixture_metadata_manager.register_clone_db.assert_called_once_with(
            data_branch=self.VALUE_DATA_BRANCH,
            data_changelist=self.VALUE_DATA_CHANGELIST,
            code_branch=self.VALUE_CODE_BRANCH,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            platform=self.ARGUMENT_PLATFORM,
            clone_host=self.VALUE_CLONE_HOST,
            destination_db="source_db_name.{}.{}".format(
                self.VALUE_CODE_CHANGELIST, self.VALUE_DATA_CHANGELIST
            ),
        )

    @patch("elipy2.avalanche.drop", MagicMock())
    @patch("elipy2.filer.FilerUtils")
    @patch("dice_elipy_scripts.databuild.remote_clone_state")
    @patch("elipy2.frostbite_core.minimum_fb_version")
    @patch("dice_elipy_scripts.databuild.SETTINGS", ConfigManager(path=config_path))
    @patch("elipy2.avalanche.get_temp_db_name")
    def test_export_avalanche_state_new(
        self,
        mock_get_temp_db_name,
        mock_minimum_fb_version,
        mock_remote_clone_state,
        mock_filerutils,
        fixture_metadata_manager,
    ):
        mock_get_temp_db_name.return_value = "avalanche_temp_db"
        mock_minimum_fb_version.return_value = True
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_EXPORT_AVALANCHE_STATE, True])
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        mock_remote_clone_state.assert_called_once_with(
            platform=self.ARGUMENT_PLATFORM,
            db_name="avalanche_temp_db",
            db_name_export="avalanche_temp_db_export",
            data_changelist=self.VALUE_DATA_CHANGELIST,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            data_branch=self.VALUE_DATA_BRANCH,
            _filer=mock_filerutils.return_value,
            _bilbo=fixture_metadata_manager,
        )

    @patch("elipy2.core.use_bilbo")
    @patch("elipy2.windows_tools.get_computer_name")
    @patch("elipy2.avalanche.remote_clone_db")
    @patch("dice_elipy_scripts.databuild.SETTINGS", ConfigManager(path=config_path))
    @patch("elipy2.filer.FilerUtils")
    def test_remote_clone_state_no_bilbo(
        self,
        mock_filerutils,
        mock_remote_clone_db,
        mock_get_computer_name,
        mock_use_bilbo,
        fixture_metadata_manager,
    ):
        mock_get_computer_name.return_value = "local_computer"
        mock_use_bilbo.return_value = False
        remote_clone_state(
            self.ARGUMENT_PLATFORM,
            "db_name",
            "db_name_export",
            self.VALUE_DATA_CHANGELIST,
            self.VALUE_CODE_CHANGELIST,
            self.VALUE_DATA_BRANCH,
            mock_filerutils.return_value,
            fixture_metadata_manager,
        )
        mock_remote_clone_db.assert_called_once_with(
            source_db="db_name_export",
            dest_db="db_name",
            dest_host="avalanche.state.host",
            source_host="local_computer",
            branch=self.VALUE_DATA_BRANCH,
            limited_lifetime=True,
            push_built_levels=False,
            complete_clone=True,
        )

    @patch("elipy2.core.use_bilbo")
    @patch("elipy2.windows_tools.get_computer_name")
    @patch("elipy2.avalanche.remote_clone_db")
    @patch("dice_elipy_scripts.databuild.SETTINGS", ConfigManager(path=config_path))
    @patch("elipy2.filer.FilerUtils")
    def test_remote_clone_state_use_bilbo(
        self,
        mock_filerutils,
        mock_remote_clone_db,
        mock_get_computer_name,
        mock_use_bilbo,
        fixture_metadata_manager,
    ):
        mock_get_computer_name.return_value = "local_computer"
        mock_use_bilbo.return_value = True
        remote_clone_state(
            self.ARGUMENT_PLATFORM,
            "db_name",
            "db_name_export",
            self.VALUE_DATA_CHANGELIST,
            self.VALUE_CODE_CHANGELIST,
            self.VALUE_DATA_BRANCH,
            mock_filerutils.return_value,
            fixture_metadata_manager,
        )
        mock_remote_clone_db.assert_called_once_with(
            source_db="db_name_export",
            dest_db="db_name",
            dest_host="avalanche.state.host",
            source_host="local_computer",
            branch=self.VALUE_DATA_BRANCH,
            limited_lifetime=True,
            push_built_levels=False,
            complete_clone=True,
        )
        fixture_metadata_manager.register_avalanche_state.assert_called_once_with(
            path="db_name",
            data_changelist=self.VALUE_DATA_CHANGELIST,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            branch=self.VALUE_DATA_BRANCH,
            platform=self.ARGUMENT_PLATFORM,
            remote_host=["avalanche.state.host"],
        )

    @patch("dice_elipy_scripts.databuild.deploy_state")
    @patch("elipy2.filer.FilerUtils")
    def test_remote_clone_state_no_state_host(
        self,
        mock_filerutils,
        mock_deploy_state,
        fixture_metadata_manager,
    ):
        remote_clone_state(
            self.ARGUMENT_PLATFORM,
            "db_name",
            "db_name_export",
            self.VALUE_DATA_CHANGELIST,
            self.VALUE_CODE_CHANGELIST,
            self.VALUE_DATA_BRANCH,
            mock_filerutils.return_value,
            fixture_metadata_manager,
        )
        mock_deploy_state.assert_called_once_with(
            platform=self.ARGUMENT_PLATFORM,
            db_name="db_name",
            data_changelist=self.VALUE_DATA_CHANGELIST,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            data_branch=self.VALUE_DATA_BRANCH,
            _filer=mock_filerutils.return_value,
        )

    @patch(
        "dice_elipy_scripts.databuild.SETTINGS",
        ConfigManager(path=config_path, default_location="location_threshold_clean"),
    )
    @patch("elipy2.frostbite.build_agent_utils.generic_threshold_clean")
    def test_threshold_cleanup_called(self, mock_generic_threshold_clean):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert result.exit_code == 0
        assert mock_generic_threshold_clean.call_count == 1
