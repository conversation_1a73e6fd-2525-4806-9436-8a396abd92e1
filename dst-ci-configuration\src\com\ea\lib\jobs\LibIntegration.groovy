package com.ea.lib.jobs

import com.ea.lib.LastKnownGood
import com.ea.lib.LibCommonNonCps
import com.ea.lib.LibJobDsl
import com.ea.lib.LibSlack
import javaposse.jobdsl.dsl.jobs.FreeStyleJob

class LibIntegration {
    /**
     * Adds generic job parameters for integration start jobs, using verified changelists.
     */
    static void verified_integration_start(def job, def branch_info, def project) {
        // Set values for variables.
        def modifiers = ['integration']
        def integration_reference_job_code = LibCommonNonCps.get_setting_value(branch_info, [], 'integration_reference_job_code', '')
        def integration_reference_job = branch_info.integration_reference_job ?: ''
        def retry_limit = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'retry_limit', 0, project)
        def disable_build = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'disable_build', false)
        def data_upgrade = branch_info.data_upgrade ?: false
        def preview_folder = branch_info.preview_folder ?: ''
        def preview_project_name = branch_info.preview_project?.name ?: ''
        def preview_branch = branch_info.preview_branch ?: ''
        def source_folder = branch_info.source_folder ?: ''
        def source_project_name = branch_info.source_project?.name ?: ''
        def source_branch = branch_info.source_branch ?: ''
        def target_branch = branch_info.target_branch ?: ''
        def dataset = branch_info.source_project?.dataset ?: ''
        def manual_trigger = branch_info.manual_trigger ?: false
        def p4_code_creds = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'p4_code_creds', '', branch_info.preview_project ?: branch_info.target_project)
        def p4_code_root = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'p4_code_root', '', branch_info.preview_project ?: branch_info.target_project)

        def trigger_string = branch_info.trigger_string_integrate ?: 'H/5 * * * 1-6\nH/5 6-23 * * 7'
        def trigger_type = branch_info.trigger_type_integrate ?: 'none'
        if (data_upgrade == true && manual_trigger == false) {
            trigger_type = 'scm'
        }
        def perforce_trigger = false
        if (trigger_type == 'scm' || trigger_type == 'cron') {
            perforce_trigger = true
        }

        // Add sections to the Jenkins job.
        job.with {
            description('Trigger integrations to task branches using verified changelists.')
            disabled(disable_build)
            logRotator(7, 100)
            quietPeriod(0)
            properties {
                disableConcurrentBuilds()
                disableResume()
                pipelineTriggers {
                    triggers {
                        if (trigger_type == 'scm') {
                            pollSCM {
                                scmpoll_spec(trigger_string)
                            }
                        } else if (trigger_type == 'cron') {
                            cron {
                                spec(trigger_string)
                            }
                        }
                    }
                }
            }
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to integrate.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to integrate.')
                    trim(true)
                }
                if (data_upgrade == true) {
                    choiceParam('clean', ['False', 'True'], 'If True, TnT/Local will be cleaned before run.')
                }
            }
            environmentVariables {
                env('integration_reference_job_code', integration_reference_job_code)
                env('integration_reference_job', integration_reference_job)
                env('retry_limit', retry_limit)
                env('data_upgrade', data_upgrade)
                env('perforce_trigger', perforce_trigger)
                env('preview_folder', preview_folder)
                env('preview_project_name', preview_project_name)
                env('preview_branch', preview_branch)
                env('source_folder', source_folder)
                env('source_project_name', source_project_name)
                env('source_branch', source_branch)
                env('target_branch', target_branch)
                env('dataset', dataset)
                env('p4_code_creds', p4_code_creds)
                env('p4_code_root', p4_code_root)
            }
        }
    }

    /**
     * Adds generic job parameters for data-upgrade validation start jobs.
     */
    static void data_upgrader_validator_start(def job, def branch_info) {
        // Add sections to the Jenkins job.
        job.with {
            description('Triggers a validator for FrostbiteDatabaseUpgrader, for upgrades from ' + branch_info.source_branch + ' to ' + branch_info.target_project + '.')
            logRotator(7, 100)
            quietPeriod(0)
            properties {
                disableConcurrentBuilds()
                disableResume()
                pipelineTriggers {
                    triggers {
                        pollSCM {
                            scmpoll_spec('H/5 * * * 1-6\nH/5 6-23 * * 7')
                        }
                    }
                }
            }
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to validate.')
                    trim(true)
                }
            }
            environmentVariables {
                env('preview_branch', branch_info.preview_branch)
                env('preview_folder', branch_info.preview_folder)
                env('preview_project_name', branch_info.preview_project.name)
                env('slack_channel', branch_info.slack_channel)
                env('slack_notify_bot', branch_info.slack_notify_bot)
                env('source_branch', branch_info.source_branch)
                env('target_branch', branch_info.target_branch)
            }
        }
    }

    /**
     * Adds generic job parameters for cherrypicking start jobs.
     */
    static void cherrypick_start(def job, def target_project, def branch_info) {
        // Set values for variables.
        def non_virtual_code_branch = branch_info.non_virtual_code_branch ?: ''
        def non_virtual_code_folder = branch_info.non_virtual_code_folder ?: ''
        def non_virtual_data_branch = branch_info.non_virtual_data_branch ?: ''
        def non_virtual_data_folder = branch_info.non_virtual_data_folder ?: ''
        def data_stream = branch_info.data_stream ?: false

        // Add sections to the Jenkins job.
        job.with {
            description('Triggers a cherrypick integration from ' + branch_info.source_branch + ' to ' + branch_info.target_project + '.')
            logRotator(7, 100)
            quietPeriod(0)
            properties {
                disableConcurrentBuilds()
                disableResume()
                pipelineTriggers {
                    triggers {
                        pollSCM {
                            scmpoll_spec('H/5 * * * 1-6\nH/5 6-23 * * 7')
                        }
                    }
                }
            }
            environmentVariables {
                env('project_name', target_project.name)
                env('slack_channel', branch_info.slack_channel)
                env('source_branch', branch_info.source_branch)
                env('target_branch', branch_info.target_branch)
                env('target_folder', branch_info.target_folder)
                env('non_virtual_code_branch', non_virtual_code_branch)
                env('non_virtual_code_folder', non_virtual_code_folder)
                env('non_virtual_data_branch', non_virtual_data_branch)
                env('non_virtual_data_folder', non_virtual_data_folder)
                env('data_stream', data_stream)
            }
        }
    }

    /**
     * Adds generic job parameters for code cherrypick jobs.
     */
    static void cherrypick(def job, def target_project, def branch_info) {
        String p4_code_server = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_code_server', '', target_project)
        String p4_data_server = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_data_server', '', target_project)

        // Set values for variables.
        def extra_args = branch_info.extra_args ?: ''
        def job_label = branch_info.job_label ?: 'statebuild'
        def post_trigger = branch_info.post_trigger ?: ''
        def data_stream = branch_info.data_stream ?: false

        def description_string = 'Performs a cherrypick integration from ' + branch_info.source_branch + ' to ' + branch_info.target_branch + '.'
        def code_changelist = 'code_changelist'

        def p4_server = p4_code_server
        def p4_client = target_project.p4_code_client_env
        if (data_stream == true) {
            p4_server = p4_data_server
            p4_client = target_project.p4_data_client_env
        }

        def timeout_hours = branch_info.timeout_hours_cherrypick ?: 4
        def timeout_minutes = timeout_hours * 60

        // Add sections to the Jenkins job.
        job.with {
            description(description_string)
            label(job_label)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(branch_info.workspace_root)
            wrappers {
                colorizeOutput()
                timestamps()
                buildName('${JOB_NAME}.${ENV, var="' + code_changelist + '"}')
                timeout {
                    absolute(timeout_minutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, branch_info.elipy_install_call, target_project)
                batchFile(branch_info.elipy_call + ' p4_copy_cherrypick --port ' + p4_server + ' --client ' + p4_client +
                    ' --user ' + target_project.p4_user_single_slash + ' --source-branch ' + branch_info.source_branch +
                    ' --json-file ' + branch_info.json_file + extra_args)
            }
            if (post_trigger != '') {
                publishers {
                    downstreamParameterized {
                        trigger(post_trigger) {
                            triggerWithNoParameters(true)
                        }
                    }
                }
            }
        }
    }

    /**
     * Adds generic job parameters for code merge-down jobs.
     */
    static void integrate_code(def job, def sourceProject, def targetProject, def branchInfo) {
        String p4_code_root_source = LibCommonNonCps.get_setting_value(branchInfo, ['source'], 'p4_code_root', '', sourceProject)
        String p4_code_root_target = LibCommonNonCps.get_setting_value(branchInfo, ['target'], 'p4_code_root', '', targetProject)

        final EXCLUDE_DRONE = branchInfo.exclude_drone ?: false
        def extraArgs = []
        final STREAM_INTEGRATION = branchInfo.stream_integration != null ? branchInfo.stream_integration : true
        final USE_FILE_PATH = branchInfo.use_file_path ?: false
        def direction = 'an integration'
        final String MAPPING = branchInfo.branch_mapping
        final MERGE_VERIFICATION = branchInfo.merge_verification ?: false
        def mappings = [
            MAPPING,
            [p4_code_root_target, branchInfo.target_folder, branchInfo.target_branch].join('/'),
        ]
        final SOURCE_FILE_PATH = [p4_code_root_source, branchInfo.source_folder, branchInfo.source_branch].join('/')

        if (STREAM_INTEGRATION) {
            direction = 'an integrate-up'
            mappings[0] = SOURCE_FILE_PATH

            if (branchInfo.parent_to_child) {
                direction = 'a merge-down'
                if (MERGE_VERIFICATION) {
                    mappings[0] = MAPPING
                } else {
                    extraArgs << '--reverse'
                    mappings = mappings.reverse()
                }
            }
            if (!MERGE_VERIFICATION) {
                extraArgs << '--reverse-mapping' << mappings[1]
            }
        }
        if (!MERGE_VERIFICATION) {
            extraArgs << '--source-file-path' << SOURCE_FILE_PATH
        }

        if (EXCLUDE_DRONE) {
            extraArgs << '--exclude-path' << "//${targetProject.p4_code_client_env}/${targetProject.drone_exclude_path}"
            // We need to look at this in the future
            //does not seem to work and blocks EXC - https://gitlab.ea.com/dre-cobra/team/-/issues/1051
            //extra_args += ' --exclude-accept-yours'
        }
        if (!MERGE_VERIFICATION) {
            extraArgs << (STREAM_INTEGRATION ? '--stream' : '--no-stream')
        }
        if (MERGE_VERIFICATION) {
            extraArgs << '--merge-verification'
            extraArgs << '--email %monkey_email% --password "%monkey_passwd%"'
        }
        if (USE_FILE_PATH) {
            extraArgs << '--use-file-path'
        }

        integrate(job, targetProject, branchInfo, 'code', direction, mappings[0], extraArgs)
    }

    /**
     * Adds generic job parameters for data merge-down jobs.
     */
    static void integrate_data(def job, def sourceProject, def targetProject, def branchInfo) {
        final EXCLUDE_SPARTA = branchInfo.exclude_sparta ?: false
        def extraArgs = ['--stream']
        final SHELVE_CHANGELIST = branchInfo.shelve_changelist ?: false
        def direction = 'an integrate-up'
        def mappings = [
            [sourceProject.p4_data_root, branchInfo.source_folder, branchInfo.source_branch].join('/'),
            [targetProject.p4_data_root, branchInfo.target_folder, branchInfo.target_branch].join('/'),
        ]

        if (branchInfo.parent_to_child) {
            direction = 'a merge-down'
            extraArgs << '--reverse'
            mappings = mappings.reverse()
        }

        extraArgs << '--reverse-mapping' << mappings[1]

        if (SHELVE_CHANGELIST) {
            extraArgs << '--shelve-cl'
        }
        if (EXCLUDE_SPARTA) {
            extraArgs << '--exclude-path' << "//${targetProject.p4_data_client_env}/${branchInfo.dataset}/Source/Sparta/Offline/BundledTwinklePackages/package_all.*"
        }
        if (branchInfo.cook_before_submit) {
            extraArgs << ' --cook'
        }

        integrate(job, targetProject, branchInfo, 'data', direction, mappings[0], extraArgs)
    }
    /**
     * Adds generic job parameters for data merge-down jobs with upgrading of data.
     */
    static void data_upgrade(def job, def target_project, def branch_info) {
        // Set values for variables.
        def modifiers = []
        def extra_args = branch_info.extra_args ?: ''
        def job_label = branch_info.job_label ?: 'statebuild'
        def no_submit = branch_info.no_submit ?: false
        def revert_branchid_file = branch_info.revert_branchid_file != null ? branch_info.revert_branchid_file : true

        def description_string = 'Performs copy from ' + branch_info.source_branch + ' to ' + branch_info.target_branch + '.'
        def fb_login_details = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'p4_fb_settings', [:], target_project)
        def user_credentials = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'user_credentials', '', target_project)

        String p4_data_server = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'p4_data_server', '', target_project)

        def timeout_hours = branch_info.timeout_hours_data_upgrade ?: 4
        def timeout_minutes = timeout_hours * 60

        if (user_credentials != '') {
            extra_args += ' --email %monkey_email% --password "%monkey_passwd%"'
        }
        if (no_submit == true) {
            extra_args += ' --no-submit'
        }
        if (revert_branchid_file == true) {
            extra_args += ' --revert-branchid-file'
        }

        // Add sections to the Jenkins job.
        job.with {
            description(description_string)
            label(job_label)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(branch_info.workspace_root)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to integrate.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to integrate.')
                    trim(true)
                }
                stringParam {
                    name('last_data_changelist')
                    defaultValue('')
                    description('Data changelist for the previous job.')
                    trim(true)
                }
                choiceParam('clean', ['False', 'True'], 'If True, TnT/Local will be cleaned before run.')
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName('${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}')
                timeout {
                    absolute(timeout_minutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
                credentialsBinding {
                    if (user_credentials != '') {
                        usernamePassword('monkey_email', 'monkey_passwd', user_credentials)
                    }
                    if (fb_login_details.p4_creds) {
                        usernamePassword('fb_p4_user', 'fb_p4_passwd', fb_login_details.p4_creds)
                    }
                }
            }
            steps {
                if (fb_login_details) {
                    batchFile("echo %fb_p4_passwd%|p4 -p ${fb_login_details.p4_port} -u %fb_p4_user% login & exit 0")
                }
                LibJobDsl.installElipy(delegate, branch_info.elipy_install_call, target_project)
                batchFile(branch_info.elipy_call + ' data_upgrade_integration --port ' + p4_data_server + ' --client ' + target_project.p4_data_client_env +
                    ' --user ' + target_project.p4_user_single_slash +
                    ' --code-changelist %code_changelist% ' + ' --data-changelist %data_changelist% ' +
                    ' --data-directory ' + branch_info.dataset + ' --script-path ' + branch_info.script_path +
                    ' --p4-path-source ' + target_project.p4_data_root + '/' + branch_info.source_folder + '/' + branch_info.source_branch +
                    ' --last-data-changelist %last_data_changelist% --clean %clean% ' + extra_args)
            }
        }
    }

    /**
     * Adds generic job parameters for data-upgrade validation build jobs.
     */
    static void data_upgrader_validator_job(def job, def branch_info) {
        // Set values for variables.
        def timeout_hours = branch_info.timeout_hours_data_upgrader_validator ?: 40
        def timeout_minutes = timeout_hours * 60

        // Add sections to the Jenkins job.
        job.with {
            description('Validation for FrostbiteDatabaseUpgrader, for upgrades from ' + branch_info.source_branch + ' to ' + branch_info.target_branch + '.')
            label('data-upgrader-validator')
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(branch_info.workspace_root)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to validate.')
                    trim(true)
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName('${JOB_NAME}.${ENV, var="code_changelist"}')
                timeout {
                    absolute(timeout_minutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, branch_info.install_elipy_call, branch_info.target_project)
                batchFile(branch_info.elipy_call + ' data_upgrader_validator --code-changelist %code_changelist% ' +
                    ' --data-branch-dest ' + branch_info.target_branch + ' --data-branch-source ' + branch_info.target_branch +
                    ' --data-dir-dest ' + branch_info.target_project.dataset + ' --data-dir-source ' + branch_info.source_project.dataset +
                    ' --p4-client-code ' + branch_info.target_project.p4_code_client_env + ' --p4-port-code ' + branch_info.target_project.p4_code_server +
                    ' --p4-client-dest ' + branch_info.target_project.p4_data_client_env + ' --p4-port-dest ' + branch_info.target_project.p4_data_server +
                    ' --p4-client-source ' + branch_info.source_project.p4_data_client_env + ' --p4-port-source ' + branch_info.source_project.p4_data_server +
                    ' --p4-user ' + branch_info.target_project.p4_user_single_slash + ' --licensee ' + branch_info.licensee)
            }
        }
    }

    /**
     * Adds generic job parameters for code copy-to jobs.
     */
    static void copy_to_code(def job, def project, def branch_info) {
        // Set values for variables.
        def extra_args_list = branch_info.extra_code_args ?: []
        def extra_args = extra_args_list.join(' ')
        def p4_code_force_copy = branch_info.p4_code_force_copy ?: '--no-force'
        def job_label = branch_info.job_label_statebuild ?: 'statebuild'
        def no_submit = branch_info.no_submit ?: false
        def exclude_drone = branch_info.exclude_drone ?: false
        def trigger_string = branch_info.trigger_string_copy ?: 'H/5 * * * 1-6\nH/5 6-23 * * 7'
        def trigger_type = branch_info.trigger_type_copy ?: 'none'
        String p4_code_root = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_code_root', '', project)
        String p4_code_server = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_code_server', '', project)

        def mapping = p4_code_root + '/' + branch_info.source_folder + '/' + branch_info.source_branch
        if (branch_info.parent_to_child == true) {
            extra_args += ' --reverse'
            mapping = p4_code_root + '/' + branch_info.target_folder + '/' + branch_info.target_branch
        }

        def timeout_hours = branch_info.timeout_hours_code_copy ?: 4
        def timeout_minutes = timeout_hours * 60

        if (no_submit == true) {
            extra_args += ' --no-submit'
        }
        if (exclude_drone == true) {
            extra_args += ' --exclude-path //' + project.p4_code_client_env + '/' + project.drone_exclude_path
        }

        // Add sections to the Jenkins job.
        job.with {
            description('Performs a copy from ' + branch_info.source_branch + ' to ' + branch_info.target_branch + '.')
            label(job_label)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(branch_info.workspace_root)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to copy.')
                    trim(true)
                }
                stringParam {
                    name('submit_message')
                    defaultValue('')
                    description('Message to use with P4 submite.')
                    trim(true)
                }
                stringParam {
                    name('force_copy')
                    defaultValue(p4_code_force_copy)
                    description("'--no-force'/'--force', `--force` will force copy and override target branch")
                    trim(true)
                }
                choiceParam('user_no_submit', ['', '--no-submit'], 'If "--no-submit" is selected, elipy is triggered as a dry run.')
            }
            if (trigger_type == 'scm' || trigger_type == 'cron') {
                triggers {
                    if (trigger_type == 'scm') {
                        pollSCM {
                            scmpoll_spec(trigger_string)
                        }
                    } else if (trigger_type == 'cron') {
                        cron {
                            spec(trigger_string)
                        }
                    }
                }
            }
            wrappers {
                timestamps()
                buildName('${JOB_NAME}.${ENV, var="P4_CHANGELIST"}')
                timeout {
                    absolute(timeout_minutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, branch_info.elipy_install_call, project)
                batchFile(branch_info.elipy_call + ' p4_copy ' + p4_code_server + ' ' + project.p4_code_client_env + ' ' + mapping +
                    ' %P4_CHANGELIST% --user ' + project.p4_user_single_slash + ' --stream' +
                    ' --submit-message "%submit_message%" --source-branch ' + branch_info.source_branch +
                    ' --target-branch ' + branch_info.target_branch + ' %force_copy% ' + ' %user_no_submit% ' + extra_args)
            }
        }
    }

    /**
     * Adds generic job parameters for data copy-to jobs.
     */
    static void copy_to_data(def job, def project, def branch_info) {
        // Set values for variables.
        def extra_args_list = branch_info.extra_data_args ?: []
        def extra_args = extra_args_list.join(' ')
        def job_label = branch_info.job_label_statebuild ?: 'statebuild'
        def no_submit = branch_info.no_submit ?: false
        def p4_data_force_copy = branch_info.p4_data_force_copy ?: '--no-force'
        def trigger_string = branch_info.trigger_string_copy ?: 'H/5 * * * 1-6\nH/5 6-23 * * 7'
        def trigger_type = branch_info.trigger_type_copy ?: 'none'

        String p4_data_server = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_data_server', '', project)

        def mapping = project.p4_data_root + '/' + branch_info.source_folder + '/' + branch_info.source_branch
        if (branch_info.parent_to_child == true) {
            extra_args += ' --reverse'
            mapping = project.p4_data_root + '/' + branch_info.target_folder + '/' + branch_info.target_branch
        }

        def timeout_hours = branch_info.timeout_hours_data_copy ?: 4
        def timeout_minutes = timeout_hours * 60

        if (no_submit == true) {
            extra_args += ' --no-submit'
        }

        // Add sections to the Jenkins job.
        job.with {
            description('Performs a copy from ' + branch_info.source_branch + ' to ' + branch_info.target_branch + '.')
            label(job_label)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(branch_info.workspace_root)
            parameters {
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to copy.')
                    trim(true)
                }
                stringParam {
                    name('submit_message')
                    defaultValue('')
                    description('Message to use with P4 submite.')
                    trim(true)
                }
                stringParam {
                    name('force_copy')
                    defaultValue(p4_data_force_copy)
                    description("'--no-force'/'--force', `--force` will force copy and override target branch")
                    trim(true)
                }
                choiceParam('user_no_submit', ['', '--no-submit'], 'If "--no-submit" is selected, elipy is triggered as a dry run.')
            }
            if (trigger_type == 'scm' || trigger_type == 'cron') {
                triggers {
                    if (trigger_type == 'scm') {
                        pollSCM {
                            scmpoll_spec(trigger_string)
                        }
                    } else if (trigger_type == 'cron') {
                        cron {
                            spec(trigger_string)
                        }
                    }
                }
            }
            wrappers {
                timestamps()
                buildName('${JOB_NAME}.${ENV, var="P4_CHANGELIST"}')
                timeout {
                    absolute(timeout_minutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, branch_info.elipy_install_call, project)
                batchFile(branch_info.elipy_call + ' p4_copy ' + p4_data_server + ' ' + project.p4_data_client_env + ' ' + mapping +
                    ' %P4_CHANGELIST% --user ' + project.p4_user_single_slash + ' --stream' +
                    ' --submit-message "%submit_message%" --source-branch ' + branch_info.source_branch +
                    ' --target-branch ' + branch_info.target_branch + ' %force_copy% ' + ' %user_no_submit% ' + extra_args)
            }
        }
    }

    /**
     * Adds generic job parameters for future-smoke copy-up jobs.
     */
    static void future_smoke_copy_up(def job, def project, def branch_info) {
        // Set values for variables.
        def extra_args = branch_info.extra_args ?: ''
        def job_label = branch_info.job_label_statebuild ?: 'statebuild'
        def job_to_trigger = branch_info.target_branch + '.verified-data.start'

        def timeout_hours = branch_info.timeout_hours_future_smoke ?: 4
        def timeout_minutes = timeout_hours * 60

        def p4_data_root = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_data_root', '', project)
        def p4_data_server = LibCommonNonCps.get_setting_value(branch_info, [], 'p4_data_server', '', project)

        // Add sections to the Jenkins job.
        job.with {
            description('Performs a copy-up from ' + branch_info.source_branch + ' to ' + branch_info.target_branch + '. Also registers build as smoked in Bilbo.')
            label(job_label)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(branch_info.workspace_root)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('What code changelist got smoked.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('What data changelist got smoked.')
                    trim(true)
                }
            }
            wrappers {
                timestamps()
                buildName('${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}')
                timeout {
                    absolute(timeout_minutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, branch_info.elipy_install_call, project)
                batchFile(branch_info.elipy_call + ' smoke_integrate --perforce-server ' + p4_data_server + ' --perforce-client ' + project.p4_data_client_env +
                    ' --from-stream ' + p4_data_root + '/' + branch_info.source_folder + '/' + branch_info.source_branch + ' --code-branch ' + branch_info.target_branch +
                    ' --data-changelist %data_changelist% --code-changelist %code_changelist%' +
                    ' --perforce-user ' + project.p4_user_single_slash + ' ' + extra_args)
            }
            publishers {
                downstreamParameterized {
                    trigger(job_to_trigger) {
                        triggerWithNoParameters(true)
                    }
                }
            }
        }
    }

    /**
     * Adds job parameters for a job sending code and data changelists to be used for integrations.
     */
    static void autotest_to_integration_code_set(def job, def branch_info) {
        // Set values for variables.
        def remote_jenkins = branch_info.set_integration_info.remote_jenkins
        def remote_job = branch_info.set_integration_info.remote_job ?: branch_info.code_branch + '.autotest-to-integration.code'
        // Add sections to the Jenkins job.
        job.with {
            description('Set changelist to use for integration.')
            label('master')
            logRotator(7, 100)
            quietPeriod(0)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Code changelist used for latest successful autotest on ' + branch_info.code_branch + '.')
                    trim(true)
                }
            }
            wrappers {
                timestamps()
                buildName('${JOB_NAME}.${code_changelist}')
                credentialsBinding {
                    String remoteJenkinsName = remote_jenkins.split('\\.')[0]
                    usernamePassword(
                        "jenkinsUser_${remoteJenkinsName.replace('-', '_')}",
                        "jenkinsAPIToken_${remoteJenkinsName.replace('-', '_')}",
                        "jenkins-api-token-${remoteJenkinsName}")
                }
            }
            steps {
                for (curlCall in LastKnownGood.autotestToCodeRemoteTriggers(remote_job, remote_jenkins)) {
                    shell(curlCall)
                }
            }
        }
    }

    /**
     * Adds job parameters for a job receiving code and data changelists to be used for integrations.
     */
    static void autotest_to_integration_code_get(def job, def branch_info) {
        // Add sections to the Jenkins job.
        job.with {
            description('Get changelist to use for integration.')
            label('master')
            logRotator(30, 200)
            quietPeriod(0)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Code changelist used for latest successful autotest on ' + branch_info.source_branch + '.')
                    trim(true)
                }
            }
            authenticationToken('remotebuild') // Config token which is used in the 'set' job.
            wrappers {
                timestamps()
                buildName('${JOB_NAME}.${code_changelist}')
            }
            for (integration_job in branch_info.integration_jobs) {
                publishers {
                    downstreamParameterized {
                        trigger(integration_job) {
                            triggerWithNoParameters(true)
                        }
                    }
                }
            }
        }
    }

    static void set_integration_changelist_job(def job, def branch_info, upgrade_job_name = '') {
        job.with {
            def smoke_downstream_jobs = branch_info.smoke_downstream_jobs ?: ''
            def smoke_integrate_at_latest = branch_info.smoke_integrate_at_latest ?: false

            if (branch_info.data_upgrade == true) {
                smoke_downstream_jobs = upgrade_job_name
            }

            description('Set integration changelist.')
            environmentVariables {
                env('smoke_downstream_jobs', smoke_downstream_jobs)
                env('smoke_integrate_at_latest', smoke_integrate_at_latest)
            }
            logRotator(7, 50)
            parameters {
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to sync')
                    trim(true)
                }
            }
            properties {
                disableConcurrentBuilds()
                disableResume()
            }
        }
    }

    /**
     * Adds generic job parameters for jobs that run a combined copy-integrate-compile process.
     */
    static void copyIntegrateCompile(FreeStyleJob job, def targetProject, Map branchInfo) {
        final Boolean CODE_CLEAN_BOOL = branchInfo.clean_code_always ?: false
        final String DESCRIPTION_STRING = "Performs a copy+integration from ${branchInfo.source_branch} to ${branchInfo.target_branch}."
        final String FROSTBITE_LICENSEE = branchInfo.frostbite_licensee ?: ''
        final Boolean IGNORE_SOURCE_HISTORY = branchInfo.ignore_source_history ?: false
        final String JOB_LABEL = branchInfo.job_label_statebuild ?: 'statebuild'
        final Boolean OVERRIDE_BRANCH_GUARDIAN = branchInfo.override_branch_guardian ?: false
        final Boolean NO_SUBMIT = branchInfo.no_submit ?: false
        final Boolean SHELVE_CHANGELIST = branchInfo.shelve_changelist ?: false
        final int TIMEOUT_MINUTES = (branchInfo.timeout_hours ?: 4) * 60
        final Boolean USE_SNOWCACHE = branchInfo.use_snowcache ?: false
        final String OVERRIDE_SNOWCACHE = branchInfo.override_snowcache != null ? branchInfo.override_snowcache : ''
        final String USER_CREDENTIALS = LibCommonNonCps.get_setting_value(branchInfo, ['integration'], 'user_credentials', '', targetProject)
        final NO_PARALLEL_GROUP = branchInfo.no_parallel_group ?: 'None'
        final String P4_CODE_SERVER_TARGET = LibCommonNonCps.get_setting_value(branchInfo, ['target'], 'p4_code_server', '', targetProject)

        String extraArgs = branchInfo.extra_args ?: ''
        if (FROSTBITE_LICENSEE != '') {
            extraArgs += ' --licensee ' + FROSTBITE_LICENSEE
        }
        if (IGNORE_SOURCE_HISTORY) {
            extraArgs += ' --ignore-source-history'
        }
        if (OVERRIDE_BRANCH_GUARDIAN) {
            extraArgs += ' --override-branch-guardian'
        }
        if (NO_SUBMIT) {
            extraArgs += ' --no-submit'
        }
        if (SHELVE_CHANGELIST) {
            extraArgs += ' --shelve-cl'
        }
        if (USE_SNOWCACHE) {
            extraArgs += ' --use-snowcache'
        }
        if (OVERRIDE_SNOWCACHE != '') {
            extraArgs += ' --snowcache-mode-override ' + OVERRIDE_SNOWCACHE
        }
        if (USER_CREDENTIALS != '') {
            extraArgs += ' --email %monkey_email% --password "%monkey_passwd%"'
        }

        job.with {
            description(DESCRIPTION_STRING)
            label(JOB_LABEL)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(branchInfo.workspace_root as String)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies changelist to use from source branch.')
                    trim(true)
                }
                stringParam {
                    name('submit_message')
                    defaultValue('')
                    description('Message to use with P4 submit.')
                    trim(true)
                }
                stringParam {
                    name('code_clean')
                    defaultValue(CODE_CLEAN_BOOL as String)
                    description('If true, TnT/Local will be deleted before compiling the code.')
                    trim(true)
                }
                choiceParam('user_no_submit', ['', '--no-submit'], 'If "--no-submit" is selected, elipy is triggered as a dry run.')
            }
            if (NO_PARALLEL_GROUP != 'None') {
                throttleConcurrentBuilds {
                    categories([NO_PARALLEL_GROUP])
                }
            }
            wrappers {
                buildName('${JOB_NAME}.${ENV, var="code_changelist"}')
                credentialsBinding {
                    if (USER_CREDENTIALS != '') {
                        usernamePassword('monkey_email', 'monkey_passwd', USER_CREDENTIALS)
                    }
                }
                colorizeOutput()
                timeout {
                    absolute(TIMEOUT_MINUTES)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
                timestamps()
            }
            steps {
                LibJobDsl.installElipy(delegate, branchInfo.elipy_install_call as String, targetProject)
                batchFile(branchInfo.elipy_call + ' copy_integrate_compile ' +
                    ' --changelist %code_changelist%' + ' --clean %code_clean%' +
                    ' --copy-mapping ' + branchInfo.copy_mapping +
                    ' --data-directory ' + branchInfo.dataset +
                    ' --integrate-mapping ' + branchInfo.integrate_mapping +
                    ' --p4-client ' + targetProject.p4_code_client_env +
                    ' --p4-port ' + P4_CODE_SERVER_TARGET +
                    ' --p4-user ' + targetProject.p4_user_single_slash +
                    ' --source-branch ' + branchInfo.source_branch +
                    ' --submit-message ' + '"%submit_message%"' +
                    ' %user_no_submit% ' + extraArgs as String)
            }
        }
    }

    /**
     * Adds generic job parameters for jobs that run a combined integrate-compile-upgrade-cook process.
     */
    static void integrateCompileUpgradeCook(FreeStyleJob job, def targetProject, def dataProject, Map branchInfo) {
        final Boolean CODE_CLEAN_BOOL = branchInfo.clean_code_always ?: false
        final Boolean DATA_CLEAN_BOOL = branchInfo.clean_data_always ?: false
        final String DATA_PLATFORM = branchInfo.data_platform ?: ''
        final String DESCRIPTION_STRING = "Performs a integration from ${branchInfo.source_branch} to ${branchInfo.target_branch}, including verifications: compiling code followed by upgrading and cooking data."
        final String FROSTBITE_LICENSEE = branchInfo.frostbite_licensee ?: ''
        final Boolean IGNORE_SOURCE_HISTORY = branchInfo.ignore_source_history ?: false
        final String JOB_LABEL = branchInfo.job_label_statebuild ?: 'statebuild'
        final Boolean NO_SUBMIT = branchInfo.no_submit ?: false
        final int TIMEOUT_MINUTES = (branchInfo.timeout_hours ?: 4) * 60
        final Boolean USE_SNOWCACHE = branchInfo.use_snowcache ?: false
        final String OVERRIDE_SNOWCACHE = branchInfo.override_snowcache != null ? branchInfo.override_snowcache : ''
        final String USER_CREDENTIALS = LibCommonNonCps.get_setting_value(branchInfo, ['integration'], 'user_credentials', '', targetProject)
        final String P4_CODE_SERVER = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_code_server', '', targetProject)
        final String P4_DATA_SERVER = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_data_server', '', dataProject)

        String extraArgs = branchInfo.extra_args ?: ''
        if (DATA_PLATFORM != '') {
            extraArgs += ' --data-platform ' + DATA_PLATFORM
        }
        if (FROSTBITE_LICENSEE != '') {
            extraArgs += ' --licensee ' + FROSTBITE_LICENSEE
        }
        if (IGNORE_SOURCE_HISTORY) {
            extraArgs += ' --ignore-source-history'
        }
        if (NO_SUBMIT) {
            extraArgs += ' --no-submit'
        }
        if (USE_SNOWCACHE) {
            extraArgs += ' --use-snowcache'
        }
        if (OVERRIDE_SNOWCACHE != '') {
            extraArgs += ' --snowcache-mode-override ' + OVERRIDE_SNOWCACHE
        }
        if (USER_CREDENTIALS != '') {
            extraArgs += ' --email %monkey_email% --password "%monkey_passwd%"'
        }

        job.with {
            description(DESCRIPTION_STRING)
            label(JOB_LABEL)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(branchInfo.workspace_root as String)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies changelist to use from source branch.')
                    trim(true)
                }
                stringParam {
                    name('submit_message')
                    defaultValue('')
                    description('Message to use with P4 submit.')
                    trim(true)
                }
                stringParam {
                    name('code_clean')
                    defaultValue(CODE_CLEAN_BOOL as String)
                    description('If true, TnT/Local will be deleted before compiling the code.')
                    trim(true)
                }
                stringParam {
                    name('data_clean')
                    defaultValue(DATA_CLEAN_BOOL as String)
                    description('If True, Avalanche will be cleaned before cooking the data.')
                    trim(true)
                }
                choiceParam('user_no_submit', ['', '--no-submit'], 'If "--no-submit" is selected, elipy is triggered as a dry run.')
            }
            wrappers {
                buildName('${JOB_NAME}.${ENV, var="code_changelist"}')
                credentialsBinding {
                    if (USER_CREDENTIALS != '') {
                        usernamePassword('monkey_email', 'monkey_passwd', USER_CREDENTIALS)
                    }
                }
                colorizeOutput()
                timeout {
                    absolute(TIMEOUT_MINUTES)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
                timestamps()
            }
            steps {
                LibJobDsl.installElipy(delegate, branchInfo.elipy_install_call as String, targetProject)
                batchFile(branchInfo.elipy_call + ' integrate_compile_upgrade_cook ' +
                    ' --assets ' + branchInfo.assets +
                    ' --code-changelist %code_changelist%' +
                    ' --code-clean %code_clean%' +
                    ' --data-clean %data_clean%' +
                    ' --data-directory ' + branchInfo.dataset +
                    ' --mapping ' + branchInfo.mapping +
                    ' --p4-client-code ' + targetProject.p4_code_client_env +
                    ' --p4-client-data ' + dataProject.p4_data_client_env +
                    ' --p4-port-code ' + P4_CODE_SERVER +
                    ' --p4-port-data ' + P4_DATA_SERVER +
                    ' --p4-user-code ' + targetProject.p4_user_single_slash +
                    ' --p4-user-data ' + dataProject.p4_user_single_slash +
                    ' --source-branch ' + branchInfo.source_branch +
                    ' --submit-message ' + '"%submit_message%"' +
                    ' %user_no_submit% ' + extraArgs as String)
            }
        }
    }

    /**
     * Adds job parameters for code cherrypick jobs.
     * @param job The job object we add parameters to
     * @param targetProject Project settings
     * @param branchInfo Map with most of the information we need
     */
    static void cherrypickCode(FreeStyleJob job, def targetProject, Map branchInfo) {
        String descriptionString = "Performs a code cherrypick integration to ${branchInfo.code_branch}."
        String p4CodeRootTarget = LibCommonNonCps.get_setting_value(branchInfo, ['target'], 'p4_code_root', '', targetProject)
        List<String> extraArgs = []
        String targetMapping = [p4CodeRootTarget, branchInfo.code_folder, branchInfo.code_branch].join('/')
        extraArgs << '--reverse-mapping' << targetMapping

        integrateCherrypick(job, targetProject, branchInfo, 'code', descriptionString, extraArgs)
    }

    /**
     * Adds job parameters for data cherrypick jobs.
     * @param job The job object we add parameters to
     * @param targetProject Project settings
     * @param branchInfo Map with most of the information we need
     */
    static void cherrypickData(FreeStyleJob job, def targetProject, Map branchInfo) {
        String descriptionString = "Performs a data cherrypick integration to ${branchInfo.data_branch}."
        String p4DataRootTarget = LibCommonNonCps.get_setting_value(branchInfo, ['target'], 'p4_data_root', '', targetProject)
        List<String> extraArgs = []
        String targetMapping = [p4DataRootTarget, branchInfo.data_folder, branchInfo.data_branch].join('/')
        extraArgs << '--reverse-mapping' << targetMapping

        integrateCherrypick(job, targetProject, branchInfo, 'data', descriptionString, extraArgs)
    }

    /**
     * Adds job parameters for jobs that run a combined p4_copy + data_upgrade process.
     * This job only works for a one stream setup, i.e. where we have code and data in the same stream.
     */
    static void p4CopyDataUpgrade(FreeStyleJob job, def targetProject, Map branchInfo) {
        // Set values for variables.
        final Boolean CODE_CLEAN_BOOL = branchInfo.clean_code_always ?: false
        final String DESCRIPTION_STRING = "Performs a copy-up of code from ${branchInfo.source_branch} to ${branchInfo.target_branch}, followed by a data upgrade on ${branchInfo.target_branch}."
        final FB_LOGIN_DETAILS = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_fb_settings', [:], targetProject)
        final String JOB_LABEL = branchInfo.job_label_statebuild ?: 'statebuild'
        final Boolean NO_SUBMIT = branchInfo.no_submit ?: false
        final String P4_CODE_FORCE_COPY = branchInfo.p4_code_force_copy ? '--force' : '--no-force'
        final String P4_SERVER = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_code_server', '', targetProject)
        final int TIMEOUT_MINUTES = (branchInfo.timeout_hours ?: 4) * 60
        final String USER_CREDENTIALS = LibCommonNonCps.get_setting_value(branchInfo, ['integration'], 'user_credentials', '', targetProject)

        String extraArgs = branchInfo.extra_args ?: ''
        if (branchInfo.frostbite_licensee) {
            extraArgs += ' --licensee ' + branchInfo.frostbite_licensee
        }
        if (NO_SUBMIT) {
            extraArgs += ' --no-submit'
        }
        if (USER_CREDENTIALS != '') {
            extraArgs += ' --email %monkey_email% --password "%monkey_passwd%"'
        }

        // Add sections to the Jenkins job.
        job.with {
            description(DESCRIPTION_STRING)
            label(JOB_LABEL)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(branchInfo.workspace_root as String)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies changelist to use from source branch. The name code changelist is used for legacy reasons')
                    trim(true)
                }
                stringParam {
                    name('submit_message')
                    defaultValue('')
                    description('Message to use with P4 submit.')
                    trim(true)
                }
                stringParam {
                    name('code_clean')
                    defaultValue(CODE_CLEAN_BOOL as String)
                    description('If true, TnT/Local will be deleted before compiling the code.')
                    trim(true)
                }
                stringParam {
                    name('force_copy')
                    defaultValue(P4_CODE_FORCE_COPY)
                    description("'--no-force'/'--force', `--force` will force copy and override target branch.")
                    trim(true)
                }
                choiceParam('user_no_submit', ['', '--no-submit'], 'If "--no-submit" is selected, elipy is triggered as a dry run.')
            }
            wrappers {
                buildName('${JOB_NAME}.${ENV, var="code_changelist"}')
                credentialsBinding {
                    if (USER_CREDENTIALS != '') {
                        usernamePassword('monkey_email', 'monkey_passwd', USER_CREDENTIALS)
                    }
                    if (FB_LOGIN_DETAILS.p4_creds) {
                        usernamePassword('fb_p4_user', 'fb_p4_passwd', FB_LOGIN_DETAILS.p4_creds as String)
                    }
                }
                colorizeOutput()
                timeout {
                    absolute(TIMEOUT_MINUTES)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
                timestamps()
            }
            steps {
                if (FB_LOGIN_DETAILS) {
                    batchFile("echo %fb_p4_passwd%|p4 -p ${FB_LOGIN_DETAILS.p4_port} -u %fb_p4_user% login & exit 0")
                }
                LibJobDsl.installElipy(delegate, branchInfo.elipy_install_call as String, targetProject)
                batchFile(branchInfo.elipy_call + ' p4_copy_data_upgrade ' +
                    ' --clean %code_clean%' +
                    ' --data-directory ' + branchInfo.dataset +
                    ' --copy-mapping ' + branchInfo.copy_mapping +
                    ' --p4-client ' + targetProject.p4_code_client_env +
                    ' --p4-port ' + P4_SERVER +
                    ' --p4-user ' + targetProject.p4_user_single_slash +
                    ' --source-branch ' + branchInfo.source_branch +
                    ' --source-changelist %code_changelist%' +
                    ' --submit-message ' + '"%submit_message%"' +
                    ' --target-changelist %P4_CHANGELIST%' +
                    ' %user_no_submit% ' + extraArgs as String)
            }
        }
    }

    /**
     * Adds job parameters for jobs that run a combined process with code copy/integration and data upgrade (locally or with integration included).
     * This job type only works for a one stream setup, i.e. where we have code and data in the same stream.
     */
    static void integrateUpgradeOneStream(FreeStyleJob job, def sourceProject, def targetProject, Map branchInfo) {
        // Set values for variables.
        final Boolean CODE_CLEAN_BOOL = branchInfo.clean_code_always ?: false
        final Boolean DATA_CLEAN_BOOL = branchInfo.clean_data_always ?: false
        final String DATASET = LibCommonNonCps.get_setting_value(branchInfo, ['integration'], 'dataset', 'Data', targetProject)
        final String DESCRIPTION_STRING = "Performs a copy and/or integration of code from ${branchInfo.source_branch} to ${branchInfo.target_branch}, " +
            "followed by a data upgrade either locally on ${branchInfo.target_branch} or as a data upgrade integration."
        final FB_LOGIN_DETAILS = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_fb_settings', [:], targetProject)
        final String JOB_LABEL = branchInfo.job_label ?: 'statebuild'
        Boolean localUpgrade = branchInfo.local_upgrade ?: false
        final String P4_SERVER = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_code_server', '', targetProject)
        final Boolean REVERT_BRANCHID_FILE = LibCommonNonCps.get_setting_value(branchInfo, [], 'revert_branchid_file', true, targetProject)
        Boolean skipUpgrade = branchInfo.skip_upgrade ?: false
        final int TIMEOUT_MINUTES = (branchInfo.timeout_hours ?: 4) * 60
        final String USER_CREDENTIALS = LibCommonNonCps.get_setting_value(branchInfo, ['integration'], 'user_credentials', '', targetProject)

        String extraArgs = branchInfo.extra_args ?: ''
        if (branchInfo.branch_guardian) {
            localUpgrade = false
            skipUpgrade = true
            extraArgs += ' --run-branch-guardian true'
            extraArgs += ' --p4-client-branch-guardian-rules-cleanup ' + targetProject.p4_code_client_env + '-branchguardianrules'
        }
        if (branchInfo.branch_guardian_rules_file) {
            extraArgs += ' --branch-guardian-rules-file ' + branchInfo.branch_guardian_rules_file
        }
        if (branchInfo.copy_mapping) {
            extraArgs += ' --copy-mapping ' + branchInfo.copy_mapping
        }
        if (branchInfo.copy_reverse) {
            extraArgs += ' --copy-reverse'
        }
        if (branchInfo.frostbite_licensee) {
            extraArgs += ' --licensee ' + branchInfo.frostbite_licensee
        }
        if (branchInfo.ignore_source_history) {
            extraArgs += ' --ignore-source-history'
        }
        if (branchInfo.integrate_mapping) {
            extraArgs += ' --integrate-mapping ' + branchInfo.integrate_mapping
        }
        if (branchInfo.integrate_reverse) {
            extraArgs += ' --integrate-reverse'
        }
        if (branchInfo.no_submit) {
            extraArgs += ' --no-submit'
        }
        if (branchInfo.shelve_cl) {
            extraArgs += ' --shelve-cl true'
        }
        if (branchInfo.skip_cook) {
            extraArgs += ' --run-cook false'
        }
        if (branchInfo.use_preview_dotnet_version) {
            extraArgs += ' --use-preview-dotnet-version true'
        }
        if (localUpgrade) {
            extraArgs += ' --local-upgrade'
        }
        if (REVERT_BRANCHID_FILE) {
            extraArgs += ' --revert-branchid-file'
        }
        if (skipUpgrade) {
            extraArgs += ' --run-upgrade false'
        }
        if (USER_CREDENTIALS != '') {
            extraArgs += ' --email %monkey_email% --password "%monkey_passwd%"'
        }

        // Add sections to the Jenkins job.
        job.with {
            description(DESCRIPTION_STRING)
            label(JOB_LABEL)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(branchInfo.workspace_root as String)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies changelist to use from source branch. The name code changelist is used for legacy reasons.')
                    trim(true)
                }
                stringParam {
                    name('code_clean')
                    defaultValue(CODE_CLEAN_BOOL as String)
                    description('If true, TnT/Local will be deleted before compiling the code.')
                    trim(true)
                }
                stringParam {
                    name('data_clean')
                    defaultValue(DATA_CLEAN_BOOL as String)
                    description('If true, Avalanche will be cleaned before cooking the data.')
                    trim(true)
                }
                stringParam {
                    name('last_changelist')
                    defaultValue('')
                    description('Changelist for the previous successful build.')
                    trim(true)
                }
                choiceParam('user_no_submit', ['', '--no-submit'], 'If "--no-submit" is selected, elipy is triggered as a dry run.')
            }
            wrappers {
                buildName('${JOB_NAME}.${ENV, var="code_changelist"}')
                credentialsBinding {
                    if (USER_CREDENTIALS != '') {
                        usernamePassword('monkey_email', 'monkey_passwd', USER_CREDENTIALS)
                    }
                    if (FB_LOGIN_DETAILS.p4_creds) {
                        usernamePassword('fb_p4_user', 'fb_p4_passwd', FB_LOGIN_DETAILS.p4_creds as String)
                    }
                }
                colorizeOutput()
                timeout {
                    absolute(TIMEOUT_MINUTES)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
                timestamps()
            }
            steps {
                if (FB_LOGIN_DETAILS) {
                    batchFile("echo %fb_p4_passwd%|p4 -p ${FB_LOGIN_DETAILS.p4_port} -u %fb_p4_user% login & exit 0")
                }
                LibJobDsl.installElipy(delegate, branchInfo.elipy_install_call as String, targetProject)
                batchFile(branchInfo.elipy_call + ' integrate_upgrade_one_stream ' +
                    ' --assets ' + branchInfo.asset +
                    ' --changelist %code_changelist%' +
                    ' --code-clean %code_clean%' +
                    ' --data-clean %data_clean%' +
                    ' --data-directory ' + DATASET +
                    ' --last-changelist "%last_changelist%"' +
                    ' --p4-client ' + targetProject.p4_code_client_env +
                    ' --p4-path-source ' + sourceProject.p4_data_root + '/' + branchInfo.source_folder + '/' + branchInfo.source_branch +
                    ' --p4-path-target ' + targetProject.p4_data_root + '/' + branchInfo.target_folder + '/' + branchInfo.target_branch +
                    ' --p4-port ' + P4_SERVER +
                    ' --p4-user ' + targetProject.p4_user_single_slash +
                    ' %user_no_submit% ' + extraArgs as String)
            }
            if (branchInfo.integration_downstream_jobs != null) {
                publishers {
                    for (downstreamJobName in branchInfo.integration_downstream_jobs.split(',')) {
                        downstreamParameterized {
                            trigger(downstreamJobName) {
                                condition('SUCCESS')
                                parameters {
                                    currentBuild()
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    static void cleanUp(job_name, target_project, branch_info) {
        LibJobDsl.initialP4revert(job_name, target_project, branch_info, true, false)
        LibJobDsl.postclean_silverback(job_name, target_project, branch_info)
    }

    static void slackSend(job, branch_info, target_project) {
        if (branch_info.slack_channel != null) {
            Boolean slackAlwaysNotify = branch_info.slack_always_notify ?: false
            def slackProject = branch_info.slack_project ?: target_project
            LibSlack.slack_default(job, branch_info.slack_channel as String, slackProject.short_name as String, slackAlwaysNotify)
        }
    }

    private static void integrate(FreeStyleJob job, def targetProject, Map branchInfo, String type, String direction, String mapping, List<String> extraArgs) {
        assert type in ['code', 'data']
        final ACCEPT_THEIRS = branchInfo.accept_theirs ?: false
        final CL_BY_CL = branchInfo.cl_by_cl ?: false
        extraArgs.addAll(branchInfo."extra_${type}_args" ?: [])
        final IGNORE_SOURCE_HISTORY = branchInfo.ignore_source_history ?: false
        final String JOB_LABEL = branchInfo.job_label_statebuild ?: 'statebuild'
        final NO_SAFE_RESOLVE = branchInfo.no_safe_resolve ?: false
        final NO_SUBMIT = branchInfo.no_submit ?: false
        final TRIGGER_STRING = branchInfo.trigger_string_integrate ?: 'H/5 * * * 1-6\nH/5 6-23 * * 7'
        final TRIGGER_TYPE = branchInfo.trigger_type_integrate ?: 'scm'
        final VERIFIED_INTEGRATION = branchInfo.verified_integration ?: false
        final P4_REMOTE_SERVER = branchInfo.p4_remote_server ?: ''
        final String USER_CREDENTIALS = LibCommonNonCps.get_setting_value(branchInfo, ['integration'], 'user_credentials', '', targetProject)
        final NO_PARALLEL_GROUP = branchInfo.no_parallel_group ?: false
        def descriptionString = "Performs $direction from ${branchInfo.source_branch} to ${branchInfo.target_branch}"

        if (VERIFIED_INTEGRATION) {
            descriptionString += ' using verified changelists'
        }

        descriptionString += '.'
        final int TIMEOUT_MINUTES = (branchInfo.timeout_hours_code_integration ?: 4) * 60

        if (ACCEPT_THEIRS) {
            extraArgs << '--accept-theirs'
        }
        if (CL_BY_CL) {
            extraArgs << '--cl-by-cl'
        }
        if (IGNORE_SOURCE_HISTORY) {
            extraArgs << '--ignore-source-history'
        }
        if (NO_SAFE_RESOLVE) {
            extraArgs << '--no-safe-resolve'
        }
        if (NO_SUBMIT) {
            extraArgs << '--no-submit'
        }
        if (P4_REMOTE_SERVER != '') {
            extraArgs << '--remote-p4server' << P4_REMOTE_SERVER
        }

        job.with {
            description(descriptionString)
            label(JOB_LABEL)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(branchInfo.workspace_root as String)
            parameters {
                stringParam {
                    name("${type}_changelist")
                    defaultValue('')
                    description("Specifies ${type} changelist to integrate.")
                    trim(true)
                }
                stringParam {
                    name('submit_message')
                    defaultValue('')
                    description('Message to use with P4 submit.')
                    trim(true)
                }
                choiceParam('user_no_submit', ['', '--no-submit'], 'If "--no-submit" is selected, elipy is triggered as a dry run.')
            }
            if (VERIFIED_INTEGRATION == false) {
                triggers {
                    if (TRIGGER_TYPE == 'scm') {
                        pollSCM {
                            scmpoll_spec(TRIGGER_STRING)
                        }
                    } else if (TRIGGER_TYPE == 'cron') {
                        cron {
                            spec(TRIGGER_STRING)
                        }
                    }
                }
            }
            if (NO_PARALLEL_GROUP != 'None') {
                throttleConcurrentBuilds {
                    categories([NO_PARALLEL_GROUP])
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName('${JOB_NAME}.${ENV, var="P4_CHANGELIST"}')
                credentialsBinding {
                    if (USER_CREDENTIALS != '') {
                        usernamePassword('monkey_email', 'monkey_passwd', USER_CREDENTIALS)
                    }
                }
                timeout {
                    absolute(TIMEOUT_MINUTES)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, branchInfo.elipy_install_call as String, targetProject)
                batchFile(([
                    branchInfo.elipy_call,
                    'integrate',
                    targetProject."p4_${type}_server",
                    targetProject."p4_${type}_client_env",
                    mapping,
                    '%P4_CHANGELIST%',
                    '--user',
                    targetProject.p4_user_single_slash,
                    '--no-stream-merge',
                    '--submit-message',
                    '"%submit_message%"',
                    '%user_no_submit%',
                ] + extraArgs).join(' '))
            }
        }
    }

    /**
     * Adds job parameters for cherrypick jobs (not specific for code or data cherrypicks)
     * @param job The job object we add parameters to
     * @param targetProject Project settings
     * @param branchInfo Map with most of the information we need
     * @param type Integration type, code or data
     * @param extraArgs List with args
     */
    private static void integrateCherrypick(FreeStyleJob job, def targetProject, Map branchInfo, String type, String descriptionString, List<String> extraArgs) {
        assert type in ['code', 'data']
        extraArgs.addAll(branchInfo."extra_${type}_args" ?: [])

        final boolean ACCEPT_THEIRS = branchInfo.accept_theirs ?: false
        final String JOB_LABEL = branchInfo.job_label_cherrypick ?: 'statebuild'
        final boolean IGNORE_SOURCE_HISTORY = branchInfo.ignore_source_history ?: false
        final boolean NO_SAFE_RESOLVE = branchInfo.no_safe_resolve ?: false
        final boolean NO_SUBMIT = branchInfo.no_submit ?: false
        final int TIMEOUT_MINUTES = (branchInfo.timeout_hours_code_integration ?: 4) * 60

        if (ACCEPT_THEIRS) {
            extraArgs << '--accept-theirs'
        }
        if (IGNORE_SOURCE_HISTORY) {
            extraArgs << '--ignore-source-history'
        }
        if (NO_SAFE_RESOLVE) {
            extraArgs << '--no-safe-resolve'
        }
        if (NO_SUBMIT) {
            extraArgs << '--no-submit'
        }

        job.with {
            description(descriptionString)
            label(JOB_LABEL)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(branchInfo.workspace_root as String)
            parameters {
                stringParam {
                    name("${type}_changelist")
                    defaultValue('')
                    description("Specifies ${type} changelist to cherrypick.")
                    trim(true)
                }
                stringParam {
                    name('source_path')
                    defaultValue('')
                    description('Full path in Perforce to the source branch (e.g. //fbstream/dev-na).')
                    trim(true)
                }
                stringParam {
                    name('submit_message')
                    defaultValue('')
                    description('Message to use with P4 submit.')
                    trim(true)
                }
                choiceParam('user_no_submit', ['', '--no-submit'], 'If "--no-submit" is selected, elipy is triggered as a dry run.')
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName('${JOB_NAME}.${ENV, var="P4_CHANGELIST"}')
                timeout {
                    absolute(TIMEOUT_MINUTES)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, branchInfo.elipy_install_call as String, targetProject)
                batchFile(([
                    branchInfo.elipy_call,
                    'integrate',
                    targetProject."p4_${type}_server",
                    targetProject."p4_${type}_client_env",
                    '%source_path%',
                    "%${type}_changelist%",
                    '--user',
                    targetProject.p4_user_single_slash,
                    '--no-stream-merge',
                    '--stream',
                    '--cherrypick',
                    '--submit-message',
                    '"%submit_message%"',
                    '--use-file-path',
                    '%user_no_submit%',
                ] + extraArgs).join(' '))
            }
        }
    }
}
