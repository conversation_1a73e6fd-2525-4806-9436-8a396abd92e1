package scripts.schedulers

/**
 * recycleCloudAgents.groovy
 * This will attempt to recycle cloud agents labeled with `managed_cloud_recycled`
 * on a weekly basis, as per EA Security's recommendation.
 */

final String LABEL = 'managed_cloud_recycled'

pipeline {
    agent { label 'master' }
    options {
        timeout(time: 8, unit: 'HOURS')
    }
    stages {
        stage('Recycle Cloud Agents') {
            steps {
                script {
                    List<String> nodesWithLabel = jenkins.model.Jenkins.get().computers
                        .findAll { it.node.labelString.contains(LABEL) }
                        .collect { it.node.nodeName }

                    Map parallelStages = nodesWithLabel.collectEntries {
                        [(it): generateStage(it)]
                    }

                    parallel parallelStages
                }
            }
        }
    }
}

Closure generateStage(nodeName) {
    return {
        stage("Tear down: ${nodeName}") {
            echo "Trying to remove node ${nodeName}"
            def nodeInfo = Jenkins.get().getComputer(nodeName)

            if (nodeInfo.offline) {
                echo 'Node is offline, proceeding to remove'
                nodeInfo.doDoDelete()
            } else {
                node(nodeName) {
                    echo "Removing ${nodeName}"
                    nodeInfo.doDoDelete()
                }
            }
        }
    }
}
