package com.ea.project.gnt.mastersettings

import com.ea.project.gnt.Granite

class ResGntDev {
    static Class project = Granite
    static Map branches = [
        'gnt-proto': [code_folder: 'gnt', code_branch: 'gnt-proto', data_folder: 'gnt', data_branch: 'gnt-proto'],
    ]
    static Map preflight_branches = [
        'gnt-proto': [code_folder: 'gnt', code_branch: 'gnt-proto', data_folder: 'gnt', data_branch: 'gnt-proto'],
    ]
    static Map autotest_branches = [:]
    static Map integrate_branches = [
        'bf-trunk_to_gnt-proto': [
            source_project              : project, source_folder: 'mainline', source_branch: 'bf-trunk',
            target_project              : project, target_folder: 'gnt', target_branch: 'gnt-proto',
            preview_project             : project, preview_folder: 'gnt', preview_branch: 'gnt-proto',
            code                        : true, data: true, data_upgrade: false, script_path: project.workspace_root + '\\TnT\\Code\\DICE\\UpgradeScripts\\UpgradeGraniteScripts.txt',
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call,
            workspace_root              : project.workspace_root, slack_channel: '#gnt-proto-integrations', job_label: 'bf-trunk-to-gnt-proto',
            verified_integration        : true, manual_trigger: true,
            create_ref_job              : true, integration_reference_job: 'bf-trunk.set_integration_changelist',
            no_submit                   : true, extra_args: ' --licensee BattlefieldGame',
            freestyle_job_trigger_matrix: [],
        ],
    ]
    static Map copy_branches = [
        'gnt-proto_to_bf-trunk': [
            source_project              : project, source_folder: 'gnt', source_branch: 'gnt-proto',
            target_project              : project, target_folder: 'mainline', target_branch: 'bf-trunk',
            code                        : true, data: true, data_upgrade: false, parent_to_child: true,
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call,
            workspace_root              : project.workspace_root, slack_channel: '#gnt-proto-integrations',
            trigger_type_copy           : 'none', no_submit: true,
            freestyle_job_trigger_matrix: [],
        ],
    ]
    static Map feature_integrations = [:]
    static Map maintenance_branch = [
        'gnt-proto': [code_folder: 'gnt', code_branch: 'gnt-proto', data_folder: 'gnt', data_branch: 'gnt-proto'],
    ]
    static List dashboard_list = []
    static Map dvcs_configs = [:]
    static List code_downstream_matrix = []
    static Map MAINTENANCE_SETTINGS = [:]
}
