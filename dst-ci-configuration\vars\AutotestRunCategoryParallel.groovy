import com.ea.lib.model.JobReference
import com.ea.lib.model.autotest.AutotestCategory

/**
 * AutotestRunCategoryParallel.groovy
 * Run all the categories for a given branch in parallel
 * @param categoriesSettings categories settings
 * @param branchName which branch the tests reside on
 * @param autotestMatrixName which AutotestMatrix configuration to use
 * @param isManual Whether or not the category is a manual test or not
 * @param buildSelectorResult changelists split by platform determined by the build-selector
 * @param jobReferences jobReferences to retry on failure
 */
void call(Map categoriesSettings, String branchName, String autotestMatrixName, Map<String, Map> buildSelectorResult,
          List<JobReference> jobReferences) {
    Map tests = [:]
    Map results = [:]
    int parallelLimit = (Integer) categoriesSettings.parallel_limit ?: 1
    List<AutotestCategory> testCategories = categoriesSettings.categories as List<AutotestCategory> ?: []
    int categoriesSize = testCategories.size()

    if (parallelLimit == 0) {
        parallelLimit = categoriesSize
    }

    int bucketSize = (int) Math.ceil(categoriesSize / parallelLimit)
    int bucketStart = 0
    int bucketEnd = bucketSize
    int bucketId = 0

    echo 'Parallel limit is: ' + parallelLimit
    echo 'Categories size is: ' + categoriesSize
    echo 'Bucket size is: ' + bucketSize

    while (bucketStart < categoriesSize) {
        def subTestCategories = testCategories[bucketStart..<bucketEnd]
        tests[bucketId] = {
            subTestCategories.each { testCategory ->
                results[testCategory.name] = AutotestRunCategory(
                        testCategory,
                        branchName,
                        autotestMatrixName,
                        buildSelectorResult,
                        jobReferences
                )
            }
        }

        bucketStart += bucketSize
        bucketEnd += bucketSize
        bucketEnd = Math.min(bucketEnd, categoriesSize)
        bucketId++
    }
    parallel(tests)
}
