package com.ea.project.kin

import com.ea.project.Cobra

class KingstonAWS {
    static String name = 'kingston_aws'
    static String short_name = 'kin'
    static Boolean frostbite_syncer_setup = false
    static Boolean single_perforce_server = false
    static Boolean presync_machines = false
    static String user_credentials = 'svc_kin01'

    static String dataset = 'kindata'
    static String frostbite_licensee = 'BattlefieldGame'

    static String fbcli_call = 'tnt\\bin\\fbcli\\cli.bat x64'
    static String location = 'dice'
    static String elipy_scripts_config_file = 'elipy_kingston.yml'

    static String elipy_install_call = "${fbcli_call} && ${workspace_root}\\ci\\install-elipy.bat $elipy_scripts_config_file >> ${workspace_root}\\logs\\install-elipy.log 2>&1"
    static String elipy_setup_call = "${fbcli_call} && ${workspace_root}\\ci\\setup-elipy-env.bat $elipy_scripts_config_file >> ${workspace_root}\\logs\\setup-elipy-env.log 2>&1"
    static String elipy_call = "${elipy_setup_call} && elipy --location $location"
    static String elipy_call_criterion = "${elipy_setup_call} && elipy --location criterion"

    static String azure_workspace_root = 'E:\\dev'
    static String azure_elipy_install_root = 'C:\\dev'
    static String azure_elipy_setup_call = "$fbcli_call && $azure_elipy_install_root\\ci\\setup-elipy-env.bat $elipy_scripts_config_file >> $azure_workspace_root\\logs\\setup-elipy-env.log 2>&1"
    static String azure_elipy_install_call = "$fbcli_call && $azure_elipy_install_root\\ci\\install-elipy.bat $elipy_scripts_config_file >> $azure_workspace_root\\logs\\install-elipy.log 2>&1"
    static String azure_elipy_call = "$azure_elipy_setup_call && elipy --location $location"

    static String p4_browser_url = 'https://swarm.frostbite.com/'
    static String p4_user_single_slash = 'DICE\\svc_auto_kingston'
    static Map p4_extra_servers = [:]

    static String p4_code_root = '//dicestudio/kin'
    static String p4_code_client = 'jenkins-${NODE_NAME}-codestream'
    static String p4_code_client_env = 'jenkins-%NODE_NAME%-codestream'
    static String p4_code_creds = 'perforce-p4buildedge02-fb-kingston01'
    static String p4_code_server = 'dice-p4buildedge02-fb.dice.ad.ea.com:2001'

    static String p4_data_root = '//data/kin'
    static String p4_data_creds = 'perforce-tunguska-kingston01'
    static String p4_data_server = 'p4-tunguska-build01.dice.ad.ea.com:2001'
    static String p4_data_client = 'jenkins-${NODE_NAME}-' + dataset + 'stream'
    static String p4_data_client_env = 'jenkins-%NODE_NAME%-' + dataset + 'stream'

    static Map p4_code_servers = [
        'frostbite_build_dice': p4_code_server
    ]

    static List<Map> p4_data_servers = [
        [name: 'tunguska_build_dice', p4_port: p4_data_server],
    ]

    static Map icepick_settings = [
        icepick_test          : 'KingstonUnitTests',
        icepick_preflight_test: 'PreflightUnitTests',
        settings_files        : 'Config/Icepick/IcepickSettings.ini',
    ]
    static String webexport_script_path = 'Scripts\\DICE\\webexport.py'
    static String drone_exclude_path = 'TnT/Setup/Drone/...'
    static Boolean fake_ooa_wrapped_symbol = false
    static Boolean commerce_debug_disable = true
    static Boolean use_recompression_cache = false
    static Boolean clean_master_version_check = true

    static String autotest_matrix = 'KinAutotestMatrix'

    /* Below are switch part from AWS and VM */
    static String workspace_root = 'D:\\dev'
    static Boolean is_cloud = false
    static List<Map> vault_secrets_project = Cobra.af2_vault_credentials
}
