/**
 * NeedsRebuildCode.groovy
 * Checks for a certain job if there is a previous one on the same code cl that built successfully.
 */
boolean call(def job_name, def current_changelist) {
    def methodName = '[NeedsRebuildCode]'
    echo "$methodName Start"
    def endMessage = "$methodName End"
    def jenkinsItem = Jenkins.get()?.getItem(job_name)

    if (jenkinsItem.builds) {
        last_jobs = jenkinsItem.getLastBuildsOverThreshold(10, Result.UNSTABLE)
        for (last_job in last_jobs) {
            if (last_job != null && last_job.getEnvironment(TaskListener.NULL)?.code_changelist != null && last_job.getEnvironment(TaskListener.NULL)?.code_changelist == current_changelist) {
                if (last_job.result != null) {
                    echo "Results of last $job_name run on code CL $current_changelist was ${last_job.result}."

                    if (last_job.result == Result.SUCCESS) {
                        echo 'Will not rebuild.'
                        echo endMessage
                        return false
                    }

                    echo 'Rebuilding'
                }
            }
        }
    }
    echo endMessage
    return true
}
