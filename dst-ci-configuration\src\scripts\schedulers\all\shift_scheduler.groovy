package scripts.schedulers.all

import com.ea.lib.LibCommonCps
import com.ea.lib.LibJenkins
import com.ea.lib.model.JobReference
import com.ea.project.GetBranchFile

def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
def project = ProjectClass(env.project_name)
/**
 * shift_scheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Get changelists') {
            steps {
                script {
                    String lastCodeBuild = LibJenkins.getLastStableCodeChangelist(env.shift_reference_job as String)
                    String lastDataBuild = LibJenkins.getLastStableDataChangelist(env.shift_reference_job as String)

                    def codeChangelist = params.code_changelist ?: lastCodeBuild
                    def dataChangelist = params.data_changelist ?: lastDataBuild

                    if (codeChangelist == null || dataChangelist == null) {
                        error('Missing changelist, aborting build!')
                    }

                    def lastCodeBuilt = LibJenkins.getLastStableCodeChangelist(env.JOB_NAME as String) ?: '1234'
                    def lastDataBuilt = LibJenkins.getLastStableDataChangelist(env.JOB_NAME as String) ?: '1234'

                    def injectMap = [
                        'code_changelist': codeChangelist,
                        'data_changelist': dataChangelist,
                        'last_code_built': lastCodeBuilt,
                        'last_data_built': lastDataBuilt,
                    ]
                    EnvInject(currentBuild, injectMap)

                    currentBuild.displayName = env.JOB_NAME + '.' + dataChangelist + '.' + codeChangelist
                }
            }
        }
        stage('Trigger shift upload') {
            steps {
                script {
                    def args = [
                        string(name: 'code_changelist', value: env.code_changelist),
                        string(name: 'data_changelist', value: env.data_changelist),
                    ]

                    if (env.code_changelist == env.last_code_built && env.data_changelist == env.last_data_built) {
                        echo('Last code build was on CL ' + env.last_data_built + '.' + env.last_code_built +
                            ' and current is CL ' + env.data_changelist + '.' + env.code_changelist + ', aborting build.')
                        currentBuild.result = Result.UNSTABLE.toString()
                    } else {
                        echo('Last build was on CL ' + env.last_data_built + '.' + env.last_code_built +
                            ' but current is ' + env.data_changelist + '.' + env.code_changelist + ', proceeding.')
                        List<JobReference> jobReferences = []
                        retryOnFailureCause(3, jobReferences) {
                            String jobName = env.branch_name + '.shift.upload'
                            def shiftJob = build(job: jobName, parameters: args, propagate: false)
                            jobReferences << new JobReference(downstreamJob: shiftJob, jobName: jobName, parameters: args, propagate: false)
                        }
                    }

                    if (currentBuild.result.toString() == 'SUCCESS') {
                        def shift_downstream_matrix = branchfile.shift_downstream_matrix
                        LibCommonCps.triggerDownstreamJobs(this, shift_downstream_matrix, 'shift', env.branch_name, branchfile, code_changelist, data_changelist)
                    }
                }
            }
        }
    }
    post {
        cleanup {
            script {
                def slackSettings = branchfile.standard_jobs_settings?.slack_channel_shift ?: [channels: ['#cobra-outage-shift']]
                SlackMessageNew(currentBuild, slackSettings, project.short_name)
            }
        }
    }
}
