{#
    Command:
        data_upgrader_validator
            short_help: Runs a data upgrader validation.

    Arguments:

    Required variables:
        p4_client_code
            required: True
            help: Perforce workspace for code.
        p4_client_dest
            required: True
            help: Perforce workspace for destination data.
        p4_client_source
            required: True
            help: Perforce workspace for source data.
        p4_port_code
            required: True
            help: Perforce server for code.
        p4_port_dest
            required: True
            help: Perforce server for destination data.
        p4_port_source
            required: True
            help: Perforce server for source data.

    Optional variables:
        code_changelist
            default: None
            help: Code changelist for the branch where the upgrader is located.
        data_branch_dest
            default: None
            help: Perforce dest branch/stream name.
        data_branch_source
            default: None
            help: Perforce source branch/stream name.
        data_dir_dest
            default: None
            help: Data directory for destination.
        data_dir_source
            default: None
            help: Data directory for source.
        licensee
            multiple: True
            default: None
            help: Game licensee used for FrostbiteDatabaseUpgrader.
        p4_user
            default: None
            help: Perforce user name.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    data_upgrader_validator
    --p4-client-code {{ p4_client_code }}
    --p4-client-dest {{ p4_client_dest }}
    --p4-client-source {{ p4_client_source }}
    --p4-port-code {{ p4_port_code }}
    --p4-port-dest {{ p4_port_dest }}
    --p4-port-source {{ p4_port_source }}
    {%- if code_changelist %}
    --code-changelist {{ code_changelist }}
    {%- endif %}
    {%- if data_branch_dest %}
    --data-branch-dest {{ data_branch_dest }}
    {%- endif %}
    {%- if data_branch_source %}
    --data-branch-source {{ data_branch_source }}
    {%- endif %}
    {%- if data_dir_dest %}
    --data-dir-dest {{ data_dir_dest }}
    {%- endif %}
    {%- if data_dir_source %}
    --data-dir-source {{ data_dir_source }}
    {%- endif %}
    {%- if licensee %}
    --licensee {{ licensee }}
    {%- endif %}
    {%- if p4_user %}
    --p4-user {{ p4_user }}
    {%- endif %}
  displayName: elipy data_upgrader_validator
