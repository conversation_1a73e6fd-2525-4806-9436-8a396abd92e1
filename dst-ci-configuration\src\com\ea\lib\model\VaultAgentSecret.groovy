package com.ea.lib.model

class VaultAgentSecret {

    List<Map> secretEnvVarMapping
    Map configuration
    Map hashicorpVaultSecretBindingConfig
    List<Map> vaultSecrets
    String path
    String vaultCredentialId
    String vaultUrl
    int engineVersion

    VaultAgentSecret(String path = "", String vaultUrl = 'http://127.0.0.1:8200', String vaultCredentialId = 'vault-auth-dummy', int engineVersion = 2, List<Map> secretEnvVarMapping = []) {
        this.engineVersion = engineVersion
        this.path = path
        this.secretEnvVarMapping = secretEnvVarMapping
        this.vaultCredentialId = vaultCredentialId
        this.vaultUrl = vaultUrl
        this.vaultSecrets = [[path: path, engineVersion: engineVersion, secretValues: []]]

        this.configuration = [vaultUrl: vaultUrl, vaultCredentialId: vaultCredentialId, engineVersion: engineVersion]

        for (secret in this.secretEnvVarMapping) {
            if (secret.vaultKey && secret.envVar) {
                this.vaultSecrets[0].secretValues.add([vaultKey: secret.vaultKey, envVar: secret.envVar])
            }
        }

        this.hashicorpVaultSecretBindingConfig = [configuration: this.configuration, vaultSecrets: this.vaultSecrets]
    }

    String getDslBindingScript() {
        return """
        withVault(configuration: ${this.configuration}, vaultSecrets: ${this.vaultSecrets}) {
            sh '''
                Vault secrets successfully bound to environment variables...
            '''
        }
        """
    }

    boolean isValid() {
        return this.path && this.vaultUrl && this.vaultCredentialId && this.vaultSecrets
    }
}
