{#
    Command:
        smoke_integrate
            short_help: Performs a Perforce integration.

    Arguments:

    Required variables:
        perforce_server
            required: True
            help: Perforce server to perform the integration on.
        perforce_client
            required: True
            help: Perforce client to perform the integration with.
        data_changelist
            required: True
            help: Which data changelist on from-stream was verified.
        code_changelist
            required: True
            help: Which code changelist has been used for smoke.
        code_branch
            required: True
            help: Which code branch the binaries come from.
        from_stream
            required: True
            help: Which stream to integrate from. Must be child of to-stream
        perforce_user
            required: True
            help: Perforce user name.

    Optional variables:
        submit/__no_submit
            default: True
            help: Use --no-submit to do a dry-run.
        exclude_path
            default: []
            multiple: True
            help: Don't integrate path
        force_redeployment
            default: False
            help: If True, redeploy zip file to networkshare
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    smoke_integrate
    --perforce-server {{ perforce_server }}
    --perforce-client {{ perforce_client }}
    --data-changelist {{ data_changelist }}
    --code-changelist {{ code_changelist }}
    --code-branch {{ code_branch }}
    --from-stream {{ from_stream }}
    --perforce-user {{ perforce_user }}
    {%- if submit/__no_submit %}
    --submit/--no-submit {{ submit/__no_submit }}
    {%- endif %}
    {%- if exclude_path %}
    --exclude-path {{ exclude_path }}
    {%- endif %}
    {%- if force_redeployment %}
    --force-redeployment {{ force_redeployment }}
    {%- endif %}
  displayName: elipy smoke_integrate
