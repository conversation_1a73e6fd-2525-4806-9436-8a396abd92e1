package scripts.schedulers.all

import com.ea.lib.LibJenkins
import com.ea.lib.LibSlack
import hudson.console.ModelHyperlinkNote
import hudson.model.Queue
import hudson.model.queue.CauseOfBlockage

/**
 * JobMonitoringScheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Determine if job fails to provision an agent') {
            steps {
                script {
                    String slackChannel = env.SLACK_CHANNEL
                    String projectShortName = env.PROJECT_SHORT_NAME
                    String cloudNodePrefix = env.CLOUD_NODE_PREFIX
                    StringJoiner log = new StringJoiner('\n')
                    StringJoiner slackMessage = new StringJoiner('\n')
                    slackMessage.add("A <${env.BUILD_URL}console|maintenance job> has found stuck jobs:")
                    boolean sendMessage = false
                    int maxCloudVmLimit = LibJenkins.maxCloudVirtualMachinesLimit
                    log.add("Maximum number of cloud VMs: $maxCloudVmLimit")
                    def cloudAgents = LibJenkins.getNodesWithPrefix(cloudNodePrefix)
                    int buildingCloudAgentsCount = cloudAgents.findAll { !it.toComputer().idle }.size()
                    log.add("Cloud agents currently building: ${buildingCloudAgentsCount}")
                    boolean spaceInQueue = maxCloudVmLimit - buildingCloudAgentsCount > 0
                    log.add("There is space in cloud queue: $spaceInQueue")
                    echo log.toString()
                    LibJenkins.jobsInQueue
                        .findAll { Queue.Item item -> item.stuck }
                        .each { Queue.Item item ->
                            CauseOfBlockage blockage = item.causeOfBlockage
                            Queue.Task task = item.task
                            echo "${ModelHyperlinkNote.encodeTo("/${task.url}", task.fullDisplayName)} has " +
                                "been stuck since ${new Date(item.inQueueSince)} for ${item.inQueueForString}. ${blockage.shortDescription}." +
                                "\n${blockage.class}"
                            if (task.fullDisplayName.indexOf('avalanche_maintenance') == -1) {
                                sendMessage = true
                                slackMessage.add("- <${LibJenkins.rootUrl}${task.url}|${task.fullDisplayName}> has been stuck " +
                                    "for ${item.inQueueForString}. ${blockage.shortDescription}. ${blockage.class}")
                            }
                        }
                    if (sendMessage) {
                        echo 'Sending slack message'
                        String color = 'danger'
                        LibSlack.sendMessage(this, slackChannel, slackMessage.toString(), projectShortName, color)
                        echo 'Message sent. Done.'
                    } else {
                        echo 'All is good. No stuck jobs.'
                    }
                }
            }
        }
    }
}
