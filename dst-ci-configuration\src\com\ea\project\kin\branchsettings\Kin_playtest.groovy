package com.ea.project.kin.branchsettings

class Kin_playtest {
    // Settings for jobs
    static Class project = com.ea.project.kin.Kingston
    static Map general_settings = [
        dataset           : project.dataset,
        elipy_call        : project.elipy_call,
        elipy_install_call: project.elipy_install_call,
        frostbite_licensee: project.frostbite_licensee,
        workspace_root    : project.workspace_root,
    ]
    static Map standard_jobs_settings = [
        asset                     : 'PlaytestLevels',
        denuvo_wrapping           : false,
        import_avalanche_autotest : false,
        linux_docker_images       : false,
        poolbuild_data            : true,
        poolbuild_frosty          : true,
        server_asset              : 'PlaytestLevels',
        shift_branch              : true,
        shift_every_build         : true,
        slack_channel_code        : [channels: ['#kin-build-notify'], skip_for_multiple_failures: true],
        slack_channel_data        : [channels: ['#kin-build-notify'], skip_for_multiple_failures: true],
        slack_channel_frosty      : [channels: ['#kin-build-notify'], skip_for_multiple_failures: true],
        statebuild_code_list      : ['xb1', 'tool'],
        timeout_hours_data        : 5,
        trigger_type_code         : 'none',
        use_snowcache             : true,
    ]
    static Map preflight_settings = [
        p4_code_server             : 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
        p4_code_creds              : 'dice-p4buildedge03-fb',
    ]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'linux64server', configs: [[name: 'final', allow_failure: false]]],
        [name: 'ps5', configs: [[name: 'final', allow_failure: true]]],
        [name: 'win64game', configs: [[name: 'final', allow_failure: false]]],
        [name: 'xbsx', configs: [[name: 'final', allow_failure: true]]],
        [name: 'tool', configs: [[name: 'release', allow_failure: false]]],

    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = []
    static List data_matrix = []
    static List data_downstream_matrix = [
        // [name: '.frosty.start', args: []],
    ]
    static List patchdata_matrix = []
    static List frosty_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww', args: '']]],
        [name: 'win64', variants: [[format: 'files', config: 'final', region: 'ww', args: '', allow_failure: false]]],
        [name: 'ps5', variants: [[format: 'files', config: 'final', region: 'dev', args: '', allow_failure: true]]],
        [name: 'xbsx', variants: [[format: 'files', config: 'final', region: 'ww', args: '', allow_failure: true]]],
    ]
    static List frosty_downstream_matrix = [
        [name: '.shift.upload', args: ['code_changelist', 'data_changelist']],
        [name: '.spin.linuxserver.digital.final.ww', args: ['code_changelist', 'data_changelist']],
    ]
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww']]],
    ]
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
