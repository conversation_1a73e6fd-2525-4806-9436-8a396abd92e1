"""
test_licensee_helper.py

Unit testing for licensee_helper
"""
import unittest
import pytest
import elipy2
from dice_elipy_scripts.utils.licensee_helper import (
    check_enabled_licensee,
    set_licensee,
)
from mock import patch


class TestLicenseeHelper(unittest.TestCase):
    def setUp(self):
        self.patcher_get_licensee_dict_int = patch(
            "elipy2.frostbite.licensee_utils.nant_generate_licensee_fragment_info"
        )
        self.mock_get_licensee_dict_int = self.patcher_get_licensee_dict_int.start()

        self.patcher_get_licensee_dict_api = patch(
            "elipy2.frostbite.licensee_utils.licensee_update_enables"
        )
        self.mock_get_licensee_dict_api = self.patcher_get_licensee_dict_api.start()

        self.patcher_set_licensee_config = patch(
            "elipy2.frostbite.licensee_utils.set_licensee_code_config"
        )
        self.mock_set_licensee_config = self.patcher_set_licensee_config.start()

        self.patcher_get_licensee_internal = patch(
            "elipy2.frostbite.licensee_utils.nant_get_enabled_licensee_names"
        )
        self.mock_get_licensee_internal = self.patcher_get_licensee_internal.start()

        self.patcher_get_licensee_api = patch(
            "elipy2.frostbite.licensee_utils.get_enabled_licensee_names"
        )
        self.mock_get_licensee_api = self.patcher_get_licensee_api.start()

        self.patcher_update_shell = patch("elipy2.core.update_shell")
        self.mock_update_shell = self.patcher_update_shell.start()

        # Return values used in multiple tests
        self.mock_get_licensee_dict_int.return_value = {
            "Licensee_1": {
                "enable": False,
                "fragment": "D:\\some\\path\\masterconfig_fragment.xml",
            },
            "Licensee_2": {
                "enable": False,
                "fragment": "D:\\other\\path\\masterconfig_fragment.xml",
            },
        }
        self.mock_get_licensee_dict_api.return_value = {
            "Licensee_1": {
                "enable": False,
                "fragment": "D:\\some\\path\\masterconfig_fragment.xml",
            },
            "Licensee_2": {
                "enable": False,
                "fragment": "D:\\other\\path\\masterconfig_fragment.xml",
            },
        }

    def tearDown(self):
        self.patcher_get_licensee_dict_int.stop()
        self.patcher_get_licensee_dict_api.stop()
        self.patcher_set_licensee_config.stop()
        self.patcher_get_licensee_internal.stop()
        self.patcher_get_licensee_api.stop()
        self.patcher_update_shell.stop()

    @patch("dice_elipy_scripts.utils.licensee_helper.check_enabled_licensee")
    def test_set_licensee_no_licensee(self, mock_check_enabled):
        assert set_licensee(None, ["some_arg"]) == ["some_arg"]
        assert not mock_check_enabled.called

    @patch("dice_elipy_scripts.utils.licensee_helper.check_enabled_licensee")
    def test_set_licensee_tuple_argument(self, mock_check_enabled):
        assert set_licensee(("test_licensee1", "test_licensee2"), ["some_arg"]) == [
            "some_arg",
            "-D:frostbite.licensees=test_licensee1",
            "-D:frostbite.licensees=test_licensee2",
        ]
        assert not mock_check_enabled.called

    @patch("dice_elipy_scripts.utils.licensee_helper.check_enabled_licensee")
    @patch("elipy2.frostbite_core.read_fb_version")
    def test_set_licensee_with_licensee_pre_2020(self, mock_read_fb_version, mock_check_enabled):
        mock_read_fb_version.return_value = "2019-PR7"
        assert set_licensee(["test_licensee"], ["some_arg"]) == [
            "some_arg",
            "-D:frostbite.licensees=test_licensee",
        ]
        assert not mock_check_enabled.called

    @patch("dice_elipy_scripts.utils.licensee_helper.check_enabled_licensee")
    @patch("os.path.exists")
    @patch("elipy2.frostbite_core.read_fb_version")
    def test_set_licensee_2020_with_licensee_without_file(
        self, mock_read_fb_version, mock_exists, mock_check_enabled
    ):
        mock_read_fb_version.return_value = "2020-PR4"
        mock_exists.return_value = False
        assert set_licensee(["test_licensee"], ["some_arg"]) == [
            "some_arg",
            "-D:frostbite.licensees=test_licensee",
        ]
        assert not mock_check_enabled.called

    @patch("dice_elipy_scripts.utils.licensee_helper.check_enabled_licensee")
    @patch("os.path.exists")
    @patch("elipy2.frostbite_core.read_fb_version")
    def test_set_licensee_2020_with_licensee_with_file(
        self, mock_read_fb_version, mock_exists, mock_check_enabled
    ):
        mock_read_fb_version.return_value = "2020-PR4"
        mock_exists.return_value = True
        assert set_licensee(["test_licensee"], ["some_arg"]) == ["some_arg"]
        self.mock_set_licensee_config.assert_called_once()
        assert mock_check_enabled.called

    @patch("dice_elipy_scripts.utils.licensee_helper.check_enabled_licensee")
    @patch("os.path.exists")
    @patch("elipy2.frostbite_core.read_fb_version")
    def test_set_licensee_2020_dict_internal(
        self, mock_read_fb_version, mock_exists, mock_check_enabled
    ):
        mock_read_fb_version.return_value = "2020-PR4"
        mock_exists.return_value = True
        set_licensee(["test_licensee"], ["some_arg"])
        self.mock_get_licensee_dict_int.assert_called_once()
        assert not self.mock_get_licensee_dict_api.called

    @patch("dice_elipy_scripts.utils.licensee_helper.check_enabled_licensee")
    @patch("os.path.exists")
    @patch("elipy2.frostbite_core.read_fb_version")
    def test_set_licensee_2022_dict_api(
        self, mock_read_fb_version, mock_exists, mock_check_enabled
    ):
        mock_read_fb_version.return_value = "2022-1-PR2"
        mock_exists.return_value = True
        set_licensee(["test_licensee"], ["some_arg"])
        assert not self.mock_get_licensee_dict_int.called
        self.mock_get_licensee_dict_api.assert_called_once()

    @patch("elipy2.frostbite_core.minimum_fb_version")
    def test_check_enabled_licensee_pre_2022(self, mock_fbver):
        mock_fbver.return_value = False
        self.mock_get_licensee_internal.return_value = ["test_licensee"]
        check_enabled_licensee(["test_licensee"])
        self.mock_get_licensee_internal.assert_called_once()
        assert not self.mock_get_licensee_api.called

    @patch("elipy2.frostbite_core.minimum_fb_version")
    def test_check_enabled_licensee_2022(self, mock_fbver):
        mock_fbver.return_value = True
        self.mock_get_licensee_api.return_value = ["test_licensee"]
        check_enabled_licensee(["test_licensee"])
        assert not self.mock_get_licensee_internal.called
        self.mock_get_licensee_api.assert_called_once()

    @patch("elipy2.frostbite_core.minimum_fb_version")
    def test_check_enabled_licensee_no_licensee(self, mock_fbver):
        mock_fbver.return_value = True
        self.mock_get_licensee_api.return_value = []
        with pytest.raises(elipy2.exceptions.ELIPYException):
            check_enabled_licensee(["test_licensee"])

    @patch("elipy2.frostbite_core.minimum_fb_version")
    def test_check_enabled_licensee_double_licensees(self, mock_fbver):
        mock_fbver.return_value = True
        self.mock_get_licensee_api.return_value = ["test_licensee", "extra_licensee"]
        check_enabled_licensee(["test_licensee", "extra_licensee"])

    @patch("elipy2.frostbite_core.minimum_fb_version")
    def test_check_enabled_licensee_wrong_licensee(self, mock_fbver):
        mock_fbver.return_value = True
        self.mock_get_licensee_api.return_value = ["wrong_licensee"]
        with pytest.raises(elipy2.exceptions.ELIPYException):
            check_enabled_licensee(["test_licensee"])

    @patch("elipy2.frostbite_core.minimum_fb_version")
    def test_set_licensee_correct_licensee_enabled_does_not_set_licensee(self, mock_fbver):
        self.mock_get_licensee_api.return_value = ["test_licensee"]
        set_licensee(["test_licensee"], ["some_arg"])
        assert not self.mock_set_licensee_config.called
