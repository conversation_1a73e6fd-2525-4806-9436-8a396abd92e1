package scripts.schedulers.testjobs

import com.ea.project.GetBranchFile

def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
def settings_map = branchfile.general_settings + branchfile.standard_jobs_settings
def project = ProjectClass(env.project_name)

/**
 * code_trigger_scheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Get changelist from Perforce') {
            steps {
                script {
                    def ignore_paths = branchfile.general_settings?.ignore_paths_code_preview ?: []
                    P4PreviewCode(project, 'stream', env.code_folder, env.code_branch, env.non_virtual_code_folder, env.non_virtual_code_branch, ignore_paths, [], settings_map)
                }
            }
        }
        stage('Trigger code jobs') {
            steps {
                script {
                    def code_changelist = env.P4_CHANGELIST
                    def clean_local = params.clean_local

                    echo 'Code changelist synced from Perforce: ' + code_changelist

                    if (code_changelist == null) {
                        echo 'Missing code changelist, aborting build!'
                        currentBuild.result = Result.UNSTABLE.toString()
                        return
                    }

                    def inject_map = [
                        'code_changelist': code_changelist,
                    ]
                    EnvInject(currentBuild, inject_map)
                    currentBuild.displayName = env.JOB_NAME + '.' + code_changelist

                    echo 'Clean local: ' + clean_local
                    echo 'Retry limit: ' + env.retry_limit

                    def cause_action = currentBuild.rawBuild.getAction(CauseAction)
                    if (cause_action.findCause(hudson.model.Cause.UpstreamCause)) {
                        echo 'Triggered by an upstream job.'
                    } else if (cause_action.findCause(hudson.model.Cause.UserIdCause)) {
                        echo 'Triggered manually by a user.'
                    } else if (cause_action.findCause(hudson.triggers.SCMTrigger.SCMTriggerCause)) {
                        echo 'Triggered by an SCM change.'
                    }
                }
            }
        }
    }
}
