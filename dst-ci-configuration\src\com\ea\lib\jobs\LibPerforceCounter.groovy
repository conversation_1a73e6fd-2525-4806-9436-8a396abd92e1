package com.ea.lib.jobs

import com.ea.lib.LibJobDsl
import com.ea.lib.jobsettings.JobSetting
import com.ea.lib.jobsettings.PerforceCounterSettings

class LibPerforceCounter {

    /**
     * Add job parameters for a job update p4 counter after auotest.start job is finished with success.
     */
    static void autotestP4CounterUpdater(def job, def project, def branchFile, def masterFile, String branchName) {
        JobSetting settings = new PerforceCounterSettings()
        settings.initializeAutotestP4CounterUpdater(branchFile, masterFile, project, branchName)
        job.with {
            description(settings.description)
            label(settings.jobLabel)
            logRotator(7, 50)
            quietPeriod(0)
            customWorkspace(settings.workspaceRoot)
            throttleConcurrentBuilds {
                maxPerNode(1)
                maxTotal(8)
            }
            parameters {
                stringParam {
                    name('code_countername')
                    defaultValue('')
                    description('Specifies p4 code counter name to use.')
                    trim(true)
                }
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist for p4 counter to set value to.')
                    trim(true)
                }
                stringParam {
                    name('data_countername')
                    defaultValue('')
                    description('Specifies p4 data counter name to use.')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist for p4 counter to set value to.')
                    trim(true)
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName('${JOB_NAME}')
                timeout {
                    absolute(100)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                batchFile(settings.elipyCmd)
            }
        }
    }
}
