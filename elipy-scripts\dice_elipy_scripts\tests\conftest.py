"""
conftest.py
"""
import os
import pytest
import psutil
from mock import patch, MagicMock
import time


@pytest.fixture(name="settings_env_vars")
def fixture_settings_env_vars():
    mock_env_vars = {"LOCALAPPDATA": "localappdata_path"}
    with patch.dict(os.environ, mock_env_vars):
        yield


@pytest.fixture(autouse=True)
def sleepless(monkeypatch):
    def sleep(seconds):
        pass

    monkeypatch.setattr(time, "sleep", sleep)


@pytest.fixture(autouse=True)
def fixture_metadata_manager():
    with patch(
        "elipy2.build_metadata_utils.setup_metadata_manager",
        autospec=True,
    ) as mock_manager:
        yield mock_manager.return_value


@pytest.fixture(autouse=True)
def sleeplessRetry():
    def mock_retry(
        f, exceptions=Exception, tries=-1, delay=0, max_delay=None, backoff=1, jitter=0, logger=None
    ):
        _tries, _delay, _backoff = 3, 0, 0
        while _tries:
            try:
                return f()
            except exceptions as e:
                _tries -= 1
                if not _tries:
                    raise

    with patch("retry.retry", mock_retry):
        yield


@pytest.fixture(autouse=True)
def fixture_upload_metrics():
    with patch("elipy2.telemetry.upload_metrics"):
        yield


@pytest.fixture(autouse=True)
def processProcess():
    with patch(
        "elipy2.running_processes.process_iter", autospec=True, return_value=[]
    ) as mocked_proc_iter:
        mocked_process = MagicMock(spec=psutil.Process)
        mocked_process.name.return_value = "test_process"
        mocked_process.return_value.pid = 9999999
        mocked_proc_iter.return_value = [mocked_process]

        yield


@pytest.fixture(autouse=False)
def fixture_p4_utils():
    with patch("elipy2.p4.P4Utils", autospec=True) as mocked_item:
        yield mocked_item.return_value


@pytest.fixture(autouse=False)
def fixture_data_utils():
    with patch("elipy2.data.DataUtils", autospec=True) as mocked_item:
        yield mocked_item


@pytest.fixture(autouse=False)
def fixture_code_utils():
    with patch("elipy2.code.CodeUtils", autospec=True) as mocked_item:
        yield mocked_item.return_value


@pytest.fixture(autouse=False)
def fixture_core_ensure_p4_config():
    with patch("elipy2.core.ensure_p4_config", autospec=True) as mocked_item:
        yield mocked_item.return_value
