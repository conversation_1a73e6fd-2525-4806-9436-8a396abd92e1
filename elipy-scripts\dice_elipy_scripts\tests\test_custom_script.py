"""
test_custom_script.py

Unit testing for custom_script
"""
from unittest.mock import patch

from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock.mock import MagicMock

from dice_elipy_scripts.custom_script import cli
from dice_elipy_scripts.utils.env_utils import env_string_to_dict


@patch("os.path.exists", MagicMock(return_value=True))
@patch("elipy2.frostbite_core.get_tnt_root", MagicMock(return_value="h:\\dev\\tnt"))
@patch("os.path.join", MagicMock(side_effect=lambda *args: "\\".join(args)))
@patch("dice_elipy_scripts.custom_script.add_sentry_tags", MagicMock())
class TestCustomCmd:
    OPTION_EXECUTABLE = "--executable"
    VALUE_EXECUTABLE = "cmd"
    OPTION_EXECUTABLE_ARGS = "--executable-args"
    VALUE_EXECUTABLE_ARGS = "/c"
    OPTION_SCRIPT_PATH = "--script-path"
    VALUE_SCRIPT_PATH = "Setup\\Drone\\GenerateSyncBuildCache.bat"
    OPTION_SCRIPT_ARGS = "--script-args"
    VALUE_SCRIPT_ARGS = "--arg1 arg1"
    OPTION_ENV_VARIABLES = "--env-variables"
    VALUE_ENV_VARIABLES = "var1=foo,var2=bar"

    @patch("elipy2.core.run")
    def test_custom_cmd_with_args(self, mock_run):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.OPTION_EXECUTABLE,
                self.VALUE_EXECUTABLE,
                self.OPTION_EXECUTABLE_ARGS,
                self.VALUE_EXECUTABLE_ARGS,
                self.OPTION_SCRIPT_PATH,
                self.VALUE_SCRIPT_PATH,
                self.OPTION_SCRIPT_ARGS,
                self.VALUE_SCRIPT_ARGS,
                self.OPTION_ENV_VARIABLES,
                self.VALUE_ENV_VARIABLES,
            ],
        )
        env_vars = env_string_to_dict(self.VALUE_ENV_VARIABLES)
        mock_run.assert_called_once_with(
            [
                "cmd",
                "/c",
                "h:\\dev\\tnt\\Setup\\Drone\\GenerateSyncBuildCache.bat",
                "--arg1",
                "arg1",
            ],
            print_std_out=True,
            env_patch=env_vars,
        )
        assert result.exit_code == 0

    @patch("elipy2.core.run")
    def test_custom_cmd_without_args(self, mock_run):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.OPTION_EXECUTABLE,
                self.VALUE_EXECUTABLE,
                self.OPTION_SCRIPT_PATH,
                self.VALUE_SCRIPT_PATH,
            ],
        )

        mock_run.assert_called_once_with(
            ["cmd", "h:\\dev\\tnt\\Setup\\Drone\\GenerateSyncBuildCache.bat"],
            print_std_out=True,
            env_patch={},
        )
        assert result.exit_code == 0
