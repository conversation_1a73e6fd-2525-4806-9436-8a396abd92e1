"""
test_bilbo_select_autotest.py

Unit testing for bilbo_select_autotest
"""
import pytest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
import json
import os
from mock import patch, mock_open, MagicMock
from dice_elipy_scripts.bilbo_select_autotest import (
    cli,
    get_frosty_builds,
    get_frosty_build_candidates,
)
from elipy2 import LOGGER
from elipy2.bilbo import Build


class BaseTestClass:
    GAME_ROOT = "game-root-huh"

    OPTION_CODE_BRANCH = "--code-branch"
    OPTION_DATA_BRANCH = "--data-branch"
    VALUE_CODE_BRANCH = "code-branch"
    VALUE_DATA_BRANCH = "dev-na-dice-next-build"
    VALUE_CURRENT_TEST = "test-name"
    OPTION_PREREQUISITE_TEST = "--prerequisite-test"
    OPTION_USE_SHIFT_BUILD = "--use-shift-build"
    OPTION_USE_AZURE_DRONE_BUILD = "--use-azure-drone-build"
    OPTION_USE_SPIN_BUILD = "--use-spin-build"
    OPTION_PLATFORM = "--platform"
    VALUE_PLATFORM = "ps5"
    OPTION_REQUIRED_PLATFORM = "--required-platforms"
    VALUE_REQUIRED_PLATFORM = "server"
    OPTION_CLIENT_PLATFORM = "--client-platforms"
    VALUE_CLIENT_PLATFORM = "ps5"
    OPTION_IS_TEST_WITH_LOOSE_FILES = "--is-test-with-loose-files"
    OPTION_REGION = "--region"
    VALUE_REGION = "dev"
    OPTION_CONFIG = "--config"
    VALUE_CONFIG = "release"
    OPTION_BUILD_TIMEOUT_HOURS = "--build-timeout-hours"
    VALUE_BUILD_TIMEOUT_HOURS = 8
    OPTION_JOB_URL = "--job-url"
    VALUE_JOB_URL = "https://ea.com"


@patch("elipy2.frostbite_core.get_game_root", MagicMock(return_value=BaseTestClass.GAME_ROOT))
class TestBilboSelectAutotestBilboDrone(BaseTestClass):
    DATA_CHANGELIST = 8793429
    CODE_CHANGELIST = 8792549
    BUILD_ID_BASE = f"\\\\\\\\filer.dice.ad.ea.com\\builds\\diceupgradenext\\fb1\\code\\dev-na-dice-next-build\\"

    @staticmethod
    def prepare_bilbo_response():
        data_file = os.path.join(os.path.dirname(__file__), "data", "bilbo_response.json")
        with open(data_file) as bilbo_response:
            LOGGER.info("Reading mock response")
            to_return = []
            for hit in json.load(bilbo_response)["hits"]["hits"]:
                bld = Build()
                for key, val in iter(list(hit.items())):
                    bld.__dict__[key[:1].lower() + key[1:]] = val
                to_return.append(bld)
            return to_return

    @pytest.fixture(autouse=True)
    def setUp(self, fixture_metadata_manager):
        fixture_metadata_manager.get_all_builds.return_value = self.prepare_bilbo_response()

    @patch(
        "dice_elipy_scripts.bilbo_select_autotest.open",
        new_callable=mock_open(),
        read_data="content file",
    )
    def test_write_result_to_correct_file_on_success(
        self, mock_open_file, fixture_metadata_manager
    ):
        fixture_metadata_manager.get_all_builds.return_value = self.prepare_bilbo_response()
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.OPTION_CODE_BRANCH,
                self.VALUE_CODE_BRANCH,
                self.OPTION_DATA_BRANCH,
                self.VALUE_DATA_BRANCH,
            ],
            None,
            None,
            False,
        )
        LOGGER.info(result.exit_code)
        mock_open_file.assert_called_once_with(
            "{}/autotest.properties".format(self.GAME_ROOT), "w+"
        )
        assert result.exit_code == 0

    @patch("dice_elipy_scripts.bilbo_select_autotest.open", new_callable=mock_open)
    def test_write_correct_changelist_with_no_prerequisite_test(
        self, mock_open_file, fixture_metadata_manager
    ):
        fixture_metadata_manager.get_all_builds.return_value = self.prepare_bilbo_response()
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.OPTION_CODE_BRANCH,
                self.VALUE_CODE_BRANCH,
                self.OPTION_DATA_BRANCH,
                self.VALUE_DATA_BRANCH,
            ],
        )

        mock_open_file.return_value.__enter__().write.assert_any_call(
            "data_changelist = {}\n".format(self.DATA_CHANGELIST)
        )
        mock_open_file.return_value.__enter__().write.assert_any_call(
            "code_changelist = {}\n".format(self.CODE_CHANGELIST)
        )
        mock_open_file.return_value.__enter__().write.assert_any_call(
            "client_build_id = {}\n".format(self.BUILD_ID_BASE + str(self.CODE_CHANGELIST))
        )
        assert result.exit_code == 0

    @patch("dice_elipy_scripts.bilbo_select_autotest.open", new_callable=mock_open())
    def test_write_correct_changelist_with_prerequisite_regular_test(
        self, mock_open_file, fixture_metadata_manager
    ):
        fixture_metadata_manager.get_all_builds.return_value = self.prepare_bilbo_response()
        value_prerequisite_test = "test"
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.OPTION_CODE_BRANCH,
                self.VALUE_CODE_BRANCH,
                self.OPTION_DATA_BRANCH,
                self.VALUE_DATA_BRANCH,
                self.OPTION_PREREQUISITE_TEST,
                value_prerequisite_test,
            ],
        )
        mock_open_file.return_value.__enter__().write.assert_any_call(
            "data_changelist = {}\n".format("8729955")
        )
        mock_open_file.return_value.__enter__().write.assert_any_call(
            "code_changelist = {}\n".format("8728935")
        )
        mock_open_file.return_value.__enter__().write.assert_any_call(
            "client_build_id = {}\n".format(self.BUILD_ID_BASE + "8728935")
        )
        mock_open_file.return_value.__enter__().write.assert_any_call(
            "server_build_id = {}\n".format(self.BUILD_ID_BASE + "8728935")
        )
        assert mock_open_file.return_value.__enter__().write.call_count == 4
        assert result.exit_code == 0

    @patch("dice_elipy_scripts.bilbo_select_autotest.open", new_callable=mock_open())
    def test_write_correct_changelist_with_prerequisite_not_found(self, mock_open_file):
        value_prerequisite_test = "not-found"
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.OPTION_CODE_BRANCH,
                self.VALUE_CODE_BRANCH,
                self.OPTION_DATA_BRANCH,
                self.VALUE_DATA_BRANCH,
                self.OPTION_PREREQUISITE_TEST,
                value_prerequisite_test,
            ],
        )
        assert mock_open_file.return_value.__enter__().write.call_count == 0
        assert result.exit_code == 0

    @patch(
        "dice_elipy_scripts.bilbo_select_autotest.open",
        new_callable=mock_open(),
        read_data="content file",
    )
    @patch("elipy2.frostbite_core.read_fb_version", MagicMock(return_value="2019-PR7"))
    def test_mark_frosty_build_as_in_use_on_success(self, mock_open_file, fixture_metadata_manager):
        with patch(
            "dice_elipy_scripts.bilbo_select_autotest.get_frosty_builds"
        ) as mock_get_frosty_builds:
            path_server = "\\\\not\\a\\real\\bilbo\\address\\server"
            path_ps5 = "\\\\not\\a\\real\\bilbo\\address\\ps5"
            mock_get_frosty_builds.return_value = {
                "code_changelist": "123",
                "data_changelist": "234",
                "builds": [
                    Build.from_dict(
                        {
                            "_id": path_server,
                            "_source": {
                                "updated": "2021-04-23T11:47:00.963425",
                                "code_changelist": "123",
                                "data_changelist": "234",
                                "platform": "server",
                                "config": self.VALUE_CONFIG,
                            },
                        }
                    ),
                    Build.from_dict(
                        {
                            "_id": path_ps5,
                            "_source": {
                                "updated": "2021-04-24T12:57:00.963425",
                                "code_changelist": "123",
                                "data_changelist": "234",
                                "platform": "ps5",
                                "config": self.VALUE_CONFIG,
                            },
                        }
                    ),
                ],
            }
            runner = CliRunner()
            result = runner.invoke(
                cli,
                [
                    self.OPTION_CODE_BRANCH,
                    self.VALUE_CODE_BRANCH,
                    self.OPTION_DATA_BRANCH,
                    self.VALUE_DATA_BRANCH,
                    self.OPTION_PLATFORM,
                    self.VALUE_PLATFORM,
                    self.OPTION_REQUIRED_PLATFORM,
                    self.VALUE_REQUIRED_PLATFORM,
                    self.OPTION_BUILD_TIMEOUT_HOURS,
                    self.VALUE_BUILD_TIMEOUT_HOURS,
                    self.OPTION_JOB_URL,
                    self.VALUE_JOB_URL,
                    self.OPTION_IS_TEST_WITH_LOOSE_FILES,
                    self.OPTION_CONFIG,
                    self.VALUE_CONFIG,
                ],
            )
            LOGGER.info(result.exit_code)
            assert result.exit_code == 0
            fixture_metadata_manager.mark_build_as_in_use_until.assert_any_call(
                path_server, self.VALUE_JOB_URL, self.VALUE_BUILD_TIMEOUT_HOURS
            )
            fixture_metadata_manager.mark_build_as_in_use_until.assert_any_call(
                path_ps5, self.VALUE_JOB_URL, self.VALUE_BUILD_TIMEOUT_HOURS
            )

    def test_get_frosty_build_candidates_returns_all_matching_CL_builds(
        self, fixture_metadata_manager
    ):
        code_branch = "code_branch"
        data_branch = "data_branch"
        path_server = "\\\\not\\a\\real\\bilbo\\address\\server"
        path_ps5 = "\\\\not\\a\\real\\bilbo\\address\\ps5"
        platforms = [
            {"name": "ps5", "region": "dev", "config": "final"},
            {"name": "server", "region": None, "config": None},
        ]
        code_changelist = "123"
        data_changelist = "4365735"

        ps5_frosty_builds = [
            Build.from_dict(
                {
                    "_id": path_ps5,
                    "_source": {
                        "data_changelist": data_changelist,
                        "code_changelist": code_changelist,
                        "platform": "ps5",
                        "region": "dev",
                        "config": "final",
                    },
                }
            ),
            Build.from_dict(
                {
                    "_id": path_ps5,
                    "_source": {
                        "data_changelist": "321",
                        "code_changelist": "5375634",
                        "platform": "ps5",
                        "region": "dev",
                        "config": "final",
                    },
                }
            ),
        ]

        server_frosty_builds = [
            Build.from_dict(
                {
                    "_id": path_server,
                    "_source": {
                        "data_changelist": data_changelist,
                        "code_changelist": code_changelist,
                        "platform": "server",
                        "region": "ww",
                        "config": "final",
                    },
                }
            ),
            Build.from_dict(
                {
                    "_id": path_server,
                    "_source": {
                        "data_changelist": "332211",
                        "code_changelist": "5375634",
                        "platform": "server",
                        "region": "ww",
                        "config": "final",
                    },
                }
            ),
        ]

        mock_get_builds_with_query = MagicMock(
            side_effect=[ps5_frosty_builds, server_frosty_builds]
        )
        fixture_metadata_manager.get_builds_with_query = mock_get_builds_with_query

        frosty_builds = get_frosty_build_candidates(
            fixture_metadata_manager, code_branch, data_branch, platforms
        )

        assert frosty_builds[0]["data_changelist"] == data_changelist
        assert frosty_builds[0]["code_changelist"] == code_changelist
        fixture_metadata_manager.get_builds_with_query.assert_any_call(
            {
                "query": {
                    "query_string": {
                        "query": "type:frosty AND package_type:files AND code_branch.keyword:code_branch "
                        "AND data_branch.keyword:data_branch AND platform.keyword: ps5 NOT deleted:* "
                        "AND region: dev AND (config: final OR additional_configs: final)"
                    }
                },
                "sort": {"created": {"order": "desc"}},
            }
        )
        fixture_metadata_manager.get_builds_with_query.assert_called_with(
            {
                "query": {
                    "query_string": {
                        "query": "type:frosty AND package_type:files AND code_branch.keyword:code_branch "
                        "AND data_branch.keyword:data_branch AND platform.keyword: server NOT deleted:*"
                    }
                },
                "sort": {"created": {"order": "desc"}},
            }
        )
        assert len(frosty_builds[0]["builds"]) == 2

    def test_get_frosty_changelists_returns_correct_builds(self, fixture_metadata_manager):
        code_branch = "code_branch"
        data_branch = "data_branch"

        code_changelist = "123"
        data_changelist = "4365735"

        platforms = [
            {"name": "ps5", "region": "dev", "config": "final"},
            {"name": "server", "region": "ww", "config": "final"},
        ]

        mock_get_builds_with_query = MagicMock(
            side_effect=[
                [
                    Build.from_dict(
                        {
                            "_id": "100",
                            "_source": {
                                "data_changelist": data_changelist,
                                "code_changelist": code_changelist,
                                "data_branch": data_branch,
                                "platform": "ps5",
                                "config": "final",
                            },
                        }
                    )
                ],
                [
                    Build.from_dict(
                        {
                            "_id": "200",
                            "_source": {
                                "data_changelist": "234",
                                "code_changelist": "123",
                                "data_branch": "branch",
                                "platform": "server",
                                "config": "final",
                            },
                        }
                    ),
                    Build.from_dict(
                        {
                            "_id": "300",
                            "_source": {
                                "data_changelist": data_changelist,
                                "code_changelist": code_changelist,
                                "data_branch": data_branch,
                                "platform": "server",
                                "config": "final",
                            },
                        }
                    ),
                ],
            ]
        )
        fixture_metadata_manager.get_builds_with_query = mock_get_builds_with_query
        mock_get_all_builds_query_string = MagicMock(
            return_value=[
                Build.from_dict(
                    {
                        "_source": {
                            "verified_data": [
                                {
                                    "changelist": data_changelist,
                                    "branch": data_branch,
                                    "platform": "ps5",
                                    "config": "final",
                                }
                            ]
                        }
                    }
                )
            ]
        )
        fixture_metadata_manager.get_all_builds_query_string = mock_get_all_builds_query_string

        frosty_build = get_frosty_builds(
            fixture_metadata_manager, code_branch, data_branch, platforms
        )
        actual_data_changelist = frosty_build["data_changelist"]
        actual_code_changelist = frosty_build["code_changelist"]

        assert actual_code_changelist == code_changelist
        assert actual_data_changelist == data_changelist

    def test_get_frosty_changelists_not_found(self, fixture_metadata_manager):
        fixture_metadata_manager.get_aggregations = MagicMock(return_value=[])
        frosty_build = get_frosty_builds(
            fixture_metadata_manager,
            "code_branch",
            "data_branch",
            [{"name": "platform", "region": "region", "config": "config"}],
        )
        assert frosty_build == None

    @patch("dice_elipy_scripts.bilbo_select_autotest.get_frosty_builds")
    def test_repeated_platforms_in_arguments(
        self, mock_get_frosty_builds, fixture_metadata_manager
    ):
        mock_get_frosty_builds.return_value = {}
        runner = CliRunner()
        runner.invoke(
            cli,
            [
                self.OPTION_CODE_BRANCH,
                self.VALUE_CODE_BRANCH,
                self.OPTION_DATA_BRANCH,
                self.VALUE_DATA_BRANCH,
                self.OPTION_PLATFORM,
                self.VALUE_PLATFORM,
                self.OPTION_REQUIRED_PLATFORM,
                self.VALUE_REQUIRED_PLATFORM,
                self.OPTION_REQUIRED_PLATFORM,
                self.VALUE_REQUIRED_PLATFORM,
                self.OPTION_CLIENT_PLATFORM,
                self.VALUE_CLIENT_PLATFORM,
                self.OPTION_BUILD_TIMEOUT_HOURS,
                self.VALUE_BUILD_TIMEOUT_HOURS,
                self.OPTION_JOB_URL,
                self.VALUE_JOB_URL,
                self.OPTION_IS_TEST_WITH_LOOSE_FILES,
                self.OPTION_REGION,
                self.VALUE_REGION,
                self.OPTION_CONFIG,
                self.VALUE_CONFIG,
            ],
        )
        mock_get_frosty_builds.assert_called_once_with(
            fixture_metadata_manager,
            self.VALUE_CODE_BRANCH,
            self.VALUE_DATA_BRANCH,
            [
                {
                    "name": self.VALUE_PLATFORM,
                    "region": self.VALUE_REGION,
                    "config": self.VALUE_CONFIG,
                },
                {"name": self.VALUE_REQUIRED_PLATFORM, "region": None, "config": None},
            ],
        )

    def test_get_frosty_changelists_no_platform_list(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.OPTION_CODE_BRANCH,
                self.VALUE_CODE_BRANCH,
                self.OPTION_DATA_BRANCH,
                self.VALUE_DATA_BRANCH,
                self.OPTION_BUILD_TIMEOUT_HOURS,
                self.VALUE_BUILD_TIMEOUT_HOURS,
                self.OPTION_JOB_URL,
                self.VALUE_JOB_URL,
                self.OPTION_IS_TEST_WITH_LOOSE_FILES,
            ],
        )
        assert result.exception.__class__.__name__ == "ELIPYException"


class TestBibloSelectAutotestBilboFrosty(BaseTestClass):
    DATA_CHANGELIST = 4359644
    CODE_CHANGELIST = 10970596
    BUILD_ID_BASE = (
        f"\\\\filer.dice.ad.ea.com\\builds\\diceupgradenext\\fb1\\code\\dev-na-dice-next-build\\"
    )

    @pytest.fixture
    def drone_builds(self):
        data_file = os.path.join(os.path.dirname(__file__), "data", "drone_bilbo_data.json")
        with open(data_file, "r") as f:
            json_data = json.load(f)
        return [Build.from_dict(item) for item in json_data]

    @patch("dice_elipy_scripts.bilbo_select_autotest.open", new_callable=mock_open())
    def test_azure_drone_build(self, mock_open_file, fixture_metadata_manager, drone_builds):
        fixture_metadata_manager.get_all_builds.return_value = drone_builds

        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.OPTION_CODE_BRANCH,
                self.VALUE_CODE_BRANCH,
                self.OPTION_DATA_BRANCH,
                self.VALUE_DATA_BRANCH,
                self.OPTION_USE_AZURE_DRONE_BUILD,
                True,
            ],
        )

        mock_open_file.return_value.__enter__().write.assert_any_call(
            "data_changelist = {}\n".format("1122")
        )
        mock_open_file.return_value.__enter__().write.assert_any_call(
            "code_changelist = {}\n".format("1234")
        )
        mock_open_file.return_value.__enter__().write.assert_any_call(
            "client_build_id = \\\\\\\\build_share\\code\\code_branch\\1234\n"
        )
        assert result.exit_code == 0

    @pytest.fixture
    def builds(self):
        data_file = os.path.join(os.path.dirname(__file__), "data", "frosty_bilbo_data_shift.json")
        with open(data_file, "r") as f:
            json_data = json.load(f)
        return [Build.from_dict(item) for item in json_data]

    @patch("dice_elipy_scripts.bilbo_select_autotest.open", new_callable=mock_open())
    def test_shift_build(self, mock_open_file, fixture_metadata_manager, builds):
        fixture_metadata_manager.get_all_builds.return_value = builds

        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.OPTION_CODE_BRANCH,
                self.VALUE_CODE_BRANCH,
                self.OPTION_DATA_BRANCH,
                self.VALUE_DATA_BRANCH,
                self.OPTION_USE_SHIFT_BUILD,
                True,
            ],
        )

        mock_open_file.return_value.__enter__().write.assert_any_call(
            "data_changelist = {}\n".format(self.DATA_CHANGELIST)
        )
        mock_open_file.return_value.__enter__().write.assert_any_call(
            "code_changelist = {}\n".format(self.CODE_CHANGELIST)
        )
        mock_open_file.return_value.__enter__().write.assert_any_call(
            "client_build_id = \\\\\\\\filer.dice.ad.ea.com\\builds\\kingston\\frosty\\"
            "battlefieldgame\\dev-na-dice-next-build\\{}\\code-branch\\{}\\xbsx\\files\\ww\\final\n".format(
                self.DATA_CHANGELIST,
                self.CODE_CHANGELIST,
            )
        )
        assert result.exit_code == 0

    @patch("dice_elipy_scripts.bilbo_select_autotest.open", new_callable=mock_open())
    def test_spin_build(self, mock_open_file, fixture_metadata_manager, builds):
        fixture_metadata_manager.get_all_builds.return_value = builds

        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.OPTION_CODE_BRANCH,
                self.VALUE_CODE_BRANCH,
                self.OPTION_DATA_BRANCH,
                self.VALUE_DATA_BRANCH,
                self.OPTION_USE_SPIN_BUILD,
                True,
            ],
        )

        mock_open_file.return_value.__enter__().write.assert_any_call(
            "data_changelist = {}\n".format(self.DATA_CHANGELIST)
        )
        mock_open_file.return_value.__enter__().write.assert_any_call(
            "code_changelist = {}\n".format(self.CODE_CHANGELIST)
        )
        mock_open_file.return_value.__enter__().write.assert_any_call(
            "client_build_id = \\\\\\\\filer.dice.ad.ea.com\\builds\\kingston\\frosty\\"
            "battlefieldgame\\dev-na-dice-next-build\\{}\\code-branch\\{}\\xbsx\\files\\ww\\final\n".format(
                self.DATA_CHANGELIST,
                self.CODE_CHANGELIST,
            )
        )
        assert result.exit_code == 0
