package com.ea.project.bctch1.branchsettings

import com.ea.project.bctch1.BctCh1

class Task8 {
    // Settings for jobs
    static Class project = BctCh1
    static Map general_settings = [
        dataset                 : project.dataset,
        elipy_call              : project.elipy_call_eala + ' --use-fbenv-core',
        elipy_install_call      : project.elipy_install_call,
        frostbite_licensee      : project.frostbite_licensee,
        workspace_root          : project.workspace_root,
        job_label_statebuild    : 'statebuild_eala',
        poolbuild_label         : 'poolbuild_eala',
        p4_code_creds           : 'bct-la-p4',
        p4_data_creds           : 'bct-la-p4',
        p4_code_server          : 'dicela-p4edge-fb.la.ad.ea.com:2001',
        p4_data_server          : 'dicela-p4edge-fb.la.ad.ea.com:2001',
        azure_elipy_call        : project.azure_elipy_call,
        azure_elipy_install_call: project.azure_elipy_install_call,
        azure_workspace_root    : project.azure_workspace_root,
    ]
    static Map code_settings = [
        deploy_tests                 : true,
        fake_ooa_wrapped_symbol      : false,
        skip_code_build_if_no_changes: false,
        slack_channel_code           : [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        report_build_version         : ' --reporting-build-version-id %code_changelist%',
        sndbs_enabled                : true,
    ]
    static Map data_settings = [
        timeout_hours_data: 6,
        slack_channel_data: [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
    ]
    static Map frosty_settings = [:]
    static Map standard_jobs_settings = code_settings + data_settings + frosty_settings + [
        asset                     : 'Task4Levels',
        enable_lkg_p4_counters    : true,
        extra_data_args           : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        extra_frosty_args         : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        server_asset              : 'Task4Levels',
        skip_icepick_settings_file: true,
        strip_symbols             : false,
        poolbuild_data            : true,
        poolbuild_frosty          : true,
        shift_branch              : true,
        shift_every_build         : false,
        shift_reference_job       : 'task8.frosty.start',
    ]
    static Map icepick_settings = [
        ignore_icepick_exit_code: false
    ]
    static Map preflight_settings = [:]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'win64game', configs: ['final', 'performance']],
        [name: 'tool', configs: [[name: 'release', compile_unit_tests: true, run_unit_tests: true]]],
        [name: 'xbsx', configs: ['final', 'performance']],
        [name: 'ps5', configs: ['final', 'performance']],
        [name: 'win64server', configs: ['final']],
        [name: 'linux64server', configs: ['final']],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = [
        [name: '.data.start', args: []],
    ]
    static List data_matrix = [
        [name: 'win64'],
        [name: 'server'],
        [name: 'xbsx'],
        [name: 'ps5'],
        [name: 'linux64'],
    ]
    static List data_downstream_matrix = []
    static List patchdata_matrix = []
    static List frosty_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'playtest', args: '']]],
        [name: 'win64', variants: [[format: 'files', config: 'performance', region: 'playtest', args: ' --additional-configs final']]],
        [name: 'ps5', variants: [[format: 'files', config: 'performance', region: 'playtest', args: ' --additional-configs final', allow_failure: true]]],
        [name: 'xbsx', variants: [[format: 'files', config: 'performance', region: 'playtest', args: ' --additional-configs final', allow_failure: true]]],
    ]
    static List frosty_downstream_matrix = [
        [name: '.shift.start', args: ['code_changelist', 'data_changelist']],
        [name: '.spin.linuxserver.digital.final.playtest', args: ['code_changelist', 'data_changelist']],
    ]
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'playtest']]],
    ]
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
