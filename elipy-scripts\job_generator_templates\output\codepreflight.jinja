{#
    Command:
        codepreflight
            short_help: Preflight the code build.

    Arguments:
        p4_port
        p4_client
        platform
        config
        pending_changelist

    Required variables:

    Optional variables:
        user
            default: None
            help: Perforce user name.
        clean
            default: false
            help: Delete TnT/Local if --clean true is passed, otherwise no cleanup is performed.
        nomaster
            is_flag: True
            help: Run nomaster build.
        code_branch
            default: None
            help: Perforce branch/stream name.
        import_local
            is_flag: True
            help: Import contents of TnT/Local from filer.
        icepick_test
            default: None
            help: Icepick tests to run.
        ignore_icepick_exit_code
            default: False
            type: bool
            help: Should icepick result be ignored. By ignoring it, the job will succeed when a unit test fails.
        data_directory
            help: Which data directory to use for the working data set.
        settings_files
            help: Settings files relative to the data folder.
            multiple: True
        framework_args
            multiple: True
            help: Framework arguments for gen sln.
        p4_compile
            is_flag: True
            help: Run p4_compile before building.
        licensee
            multiple: True
            default: None
            help: Frostbite Licensee to use
        password
            default: None
            help: User credentials to authenticate to package server
        email
            default: None
            help: User email to authenticate to package server
        domain_user
            default: None
            help: The user to authenticate to package server as <PERSON>OMA<PERSON>\user
        limit_cpu
            is_flag: True
            help: Only enable when codepreflight tool runs on AWS.
        do_warmup/__not_warmup
            default: False
            help: --do-warmup to warm up AWS agent; --not-warmup/not set, normal preflight.
        reporting_branch
            default: None
            help: Icepick reporting branch
        heartbeat_timeout
            default: None
            help: Icepick heartbeat timeout
        icepick_extra_framework_args
            default: None
            help: Extra arguments for Icepick to pass to any Framework commands it starts
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    codepreflight
    p4_port {{ p4_port }}
    p4_client {{ p4_client }}
    platform {{ platform }}
    config {{ config }}
    pending_changelist {{ pending_changelist }}
    {%- if user %}
    --user {{ user }}
    {%- endif %}
    {%- if clean %}
    --clean {{ clean }}
    {%- endif %}
    {%- if nomaster %}
    --nomaster {{ nomaster }}
    {%- endif %}
    {%- if code_branch %}
    --code-branch {{ code_branch }}
    {%- endif %}
    {%- if import_local %}
    --import-local {{ import_local }}
    {%- endif %}
    {%- if icepick_test %}
    --icepick-test {{ icepick_test }}
    {%- endif %}
    {%- if ignore_icepick_exit_code %}
    --ignore-icepick-exit-code {{ ignore_icepick_exit_code }}
    {%- endif %}
    {%- if data_directory %}
    --data-directory {{ data_directory }}
    {%- endif %}
    {%- if settings_files %}
    --settings-files {{ settings_files }}
    {%- endif %}
    {%- if framework_args %}
    --framework-args {{ framework_args }}
    {%- endif %}
    {%- if p4_compile %}
    --p4-compile {{ p4_compile }}
    {%- endif %}
    {%- if licensee %}
    --licensee {{ licensee }}
    {%- endif %}
    {%- if password %}
    --password {{ password }}
    {%- endif %}
    {%- if email %}
    --email {{ email }}
    {%- endif %}
    {%- if domain_user %}
    --domain-user {{ domain_user }}
    {%- endif %}
    {%- if limit_cpu %}
    --limit_cpu {{ limit_cpu }}
    {%- endif %}
    {%- if do_warmup/__not_warmup %}
    --do-warmup/--not-warmup {{ do_warmup/__not_warmup }}
    {%- endif %}
    {%- if reporting_branch %}
    --reporting-branch {{ reporting_branch }}
    {%- endif %}
    {%- if heartbeat_timeout %}
    --heartbeat-timeout {{ heartbeat_timeout }}
    {%- endif %}
    {%- if icepick_extra_framework_args %}
    --icepick-extra-framework-args {{ icepick_extra_framework_args }}
    {%- endif %}
  displayName: elipy codepreflight
