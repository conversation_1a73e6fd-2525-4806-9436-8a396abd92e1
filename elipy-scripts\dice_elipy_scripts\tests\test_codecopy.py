"""
test_codecopy.py

Unit testing for codecopy
"""
import os
import unittest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import Magic<PERSON>ock, patch
from dice_elipy_scripts.codecopy import cli


@patch("os.path.join", MagicMock(side_effect=lambda *args: "\\".join(args)))
@patch("dice_elipy_scripts.codecopy.add_sentry_tags", MagicMock())
@patch("dice_elipy_scripts.codecopy.throw_if_files_found", MagicMock())
@patch("elipy2.build_metadata.BuildMetadataManager", MagicMock())
@patch("elipy2.telemetry.collect_metrics", MagicMock())
@patch("elipy2.telemetry.upload_metrics", MagicMock())
class TestCodeBuildCli(unittest.TestCase):
    OPTION_CODE_BRANCH = "--code-branch"
    OPTION_SOURCE_CHANGELIST = "--source-changelist"
    OPTION_CURRENT_CHANGELIST = "--current-changelist"

    VALUE_CODE_BRANCH = "code_branch"
    VALUE_SOURCE_CHANGELIST = "1234"
    VALUE_CURRENT_CHANGELIST = "1357"

    BASIC_ARGS = [
        OPTION_CODE_BRANCH,
        VALUE_CODE_BRANCH,
        OPTION_SOURCE_CHANGELIST,
        VALUE_SOURCE_CHANGELIST,
        OPTION_CURRENT_CHANGELIST,
        VALUE_CURRENT_CHANGELIST,
    ]

    def setUp(self):
        self.patcher_get_code_build_root_path = patch("elipy2.filer_paths.get_code_build_root_path")
        self.mock_get_code_build_root_path = self.patcher_get_code_build_root_path.start()

        self.patcher_dirname = patch("os.path.dirname")
        self.mock_dirname = self.patcher_dirname.start()
        self.mock_dirname.return_value = "dest"

        self.patcher_exists = patch("os.path.exists")
        self.mock_exists = self.patcher_exists.start()
        self.mock_exists.return_value = False

        self.patcher_robocopy = patch("elipy2.core.robocopy")
        self.mock_robocopy = self.patcher_robocopy.start()

    def tearDown(self):
        patch.stopall()

    def test_basic_args(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0

    def test_failure_if_destination_exists(self):
        self.mock_get_code_build_root_path.side_effect = ["source\\path", "dest\\path"]
        self.mock_exists.side_effect = lambda x: True if x == "dest\\path" else False
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 1

    def test_copying_files(self):
        self.mock_get_code_build_root_path.side_effect = ["source\\path", "dest\\path"]
        lock_file = os.path.join("dest", self.VALUE_CURRENT_CHANGELIST + ".locked")
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_robocopy.assert_called_once_with(
            "source\\path", "dest\\path", lock_file=lock_file
        )
