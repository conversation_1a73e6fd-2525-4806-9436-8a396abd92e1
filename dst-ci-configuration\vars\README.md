# Stop using `./vars`
Even though putting helper methods in `./vars` for sharing between pipelines is
[encouraged and recommended by <PERSON>](https://www.jenkins.io/doc/book/pipeline/shared-libraries/#directory-structure)
it has the drawback of then not being understood by IDEA. <PERSON> also supports putting helper methods in `./src` like
any other "standard Java source directory structure". If we choose to put our code in `./src` we will get IDE inline
documentation, code completion and other blows and whistles. We are still adhering to Jenkins Shared Libraries'
directory structure, but limits it to:

```
(root)
+- src                     # Groovy source files
|   +- com
|       +- ea
|           +- Bar.groovy  # for org.foo.Bar class
+- resources               # resource files (external libraries only)
|   +- bar.json    # static helper data for com.ea.Bar
```
