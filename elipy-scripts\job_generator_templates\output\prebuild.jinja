{#
    Command:
        prebuild
            short_help: Create prebuilds.

    Arguments:

    Required variables:
        code_changelist
            required: True
            help: Perforce code changelist number.
        config
            required: True
            help: Config to generate solution for.
        p4_client_code
            required: True
            help: Perforce workspace name to sync code.
        p4_port
            required: True
            help: Perforce server address.
        p4_user
            required: True
            help: Perforce user name.

    Optional variables:
        clean
            default: false
            help: Delete TnT/Local if --clean true is passed.
        data_directory
            default: None
            help: Which data directory to use for fetching licensee settings.
        dry_run
            is_flag: True
            help: Run without submitting to Perforce.
        email
            default: None
            help: User email to authenticate to package server.
        domain_user
            default: None
            help: The user to authenticate to package server as DOMAIN\user
        framework_args
            multiple: True
            help: Framework arguments for gensln.
        input_param_path
            help: Path to file with input parameters (only used for submission).
        licensee
            multiple: True
            default: None
            help: Licensee to use.
        password
            default: None
            help: User credentials to authenticate to package server.
        p4_client_prebuild
            help: Perforce workspace to submit generated prebuilds (only used for submission).
        platform_prebuild
            multiple: True
            help: Platform to generate prebuilds for (only used for submission).
        platform_sln
            multiple: True
            help: Platform(s) to run gensln and buildsln or validation for.
        skip_nintendo_switch
            is_flag: True
            help: Deprecated. Use skip-platform instead.
        skip_platform
            multiple: True
            help: Platform(s) to skip inluding.
        validation
            is_flag: True
            help: Validate prebuild: download+gensln+buildsln
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    prebuild
    --code-changelist {{ code_changelist }}
    --config {{ config }}
    --p4-client-code {{ p4_client_code }}
    --p4-port {{ p4_port }}
    --p4-user {{ p4_user }}
    {%- if clean %}
    --clean {{ clean }}
    {%- endif %}
    {%- if data_directory %}
    --data-directory {{ data_directory }}
    {%- endif %}
    {%- if dry_run %}
    --dry-run {{ dry_run }}
    {%- endif %}
    {%- if email %}
    --email {{ email }}
    {%- endif %}
    {%- if domain_user %}
    --domain-user {{ domain_user }}
    {%- endif %}
    {%- if framework_args %}
    --framework-args {{ framework_args }}
    {%- endif %}
    {%- if input_param_path %}
    --input-param-path {{ input_param_path }}
    {%- endif %}
    {%- if licensee %}
    --licensee {{ licensee }}
    {%- endif %}
    {%- if password %}
    --password {{ password }}
    {%- endif %}
    {%- if p4_client_prebuild %}
    --p4-client-prebuild {{ p4_client_prebuild }}
    {%- endif %}
    {%- if platform_prebuild %}
    --platform-prebuild {{ platform_prebuild }}
    {%- endif %}
    {%- if platform_sln %}
    --platform-sln {{ platform_sln }}
    {%- endif %}
    {%- if skip_nintendo_switch %}
    --skip-nintendo-switch {{ skip_nintendo_switch }}
    {%- endif %}
    {%- if skip_platform %}
    --skip-platform {{ skip_platform }}
    {%- endif %}
    {%- if validation %}
    --validation {{ validation }}
    {%- endif %}
  displayName: elipy prebuild
