"""
test_integrate.py

Unit testing for integrate
"""
import pytest
import collections
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
import mock
from mock import patch, MagicMock
from elipy2.exceptions import AutomaticP4MergeResolveException, ELIPYException
from dice_elipy_scripts.integrate import cli

FilePath = collections.namedtuple("FilePath", ["local_path", "depot_path"])


@patch("elipy2.core.run", MagicMock(return_value=(0, [], [])))
class TestIntegrate:
    ARGUMENT_P4PORT = "myjenkinsp4port"
    ARGUMENT_P4CLIENT = "myjenkinsclient"
    ARGUMENT_MAPPING = "kindatastream"
    ARGUMENT_CHANGELIST = "1234567"

    OPTION_REVERSE = "--reverse"
    OPTION_NO_SUBMIT = "--no-submit"
    OPTION_STREAM = "--stream"
    OPTION_NO_STREAM_MERGE = "--no-stream-merge"
    OPTION_NO_SAFE_RESOLVE = "--no-safe-resolve"
    OPTION_SHELVE_CL = "--shelve-cl"
    OPTION_ACCEPT_THEIRS = "--accept-theirs"
    OPTION_USER = "--user"
    OPTION_EXCLUDE_PATH = "--exclude-path"
    OPTION_EXCLUDE_ACCEPT_YOURS = "--exclude-accept-yours"
    OPTION_SUBMIT_MESSAGE = "--submit-message"
    OPTION_REVERSE_MAPPING = "--reverse-mapping"
    OPTION_SOURCE_FILE_PATH = "--source-file-path"
    OPTION_DATA_DIR = "--data-dir"
    OPTION_PLATFORM = "--platform"
    OPTION_ASSETS = "--assets"
    OPTION_COOK = "--cook"
    OPTION_MERGE_VERIFICATION = "--merge-verification"
    OPTION_LICENSEE = "--licensee"
    OPTION_EMAIL = "--email"
    OPTION_PASSWORD = "--password"
    OPTION_CL_BY_CL = "--cl-by-cl"
    OPTION_TAGS = "--tags"
    OPTION_USE_FILE_PATH = "--use-file-path"
    OPTION_DOMAIN_USER = "--domain-user"
    OPTION_REMOTE_P4SERVER = "--remote-p4server"

    VALUE_USER = "user_name"
    VALUE_EXCLUDE_PATH = "excluded_path"
    VALUE_SUBMIT_MESSAGE = "submit_message"
    VALUE_REVERSE_MAPPING = "//data/kin/stage/kin-stage"
    VALUE_SOURCE_FILE_PATH = "//data/kin/dev/kin-dev"
    VALUE_DATA_DIR = "data_dir"
    VALUE_PLATFORM = "ps5"
    VALUE_ASSETS = "Game/Levels/MP/MP_Orbital"
    VALUE_LICENSEE = "some_licensee"
    VALUE_EMAIL = "e_mail"
    VALUE_PASSWORD = "pass_word"
    VALUE_TAGS = "some_tag"
    VALUE_DOMAIN_USER = "DOMAIN\\user"
    VALUE_REMOTE_P4SERVER = "remote.p4-server:1337"

    BASIC_ARGS = [
        ARGUMENT_P4PORT,
        ARGUMENT_P4CLIENT,
        ARGUMENT_MAPPING,
        ARGUMENT_CHANGELIST,
    ]

    @pytest.fixture(autouse=True)
    def fixture_p4(self):
        with patch("dice_elipy_scripts.integrate.p4.P4Utils") as mock_p4:
            mock_p4.return_value = MagicMock()
            mock_p4.return_value.unresolved.return_value = []
            yield mock_p4

    @pytest.fixture(autouse=True)
    def fixture_submit_integration(self):
        with patch("dice_elipy_scripts.integrate.submit_integration") as mock_submit_integration:
            yield mock_submit_integration

    def test_basic_args(self, fixture_p4):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert result.exit_code == 0
        fixture_p4.return_value.resolve.assert_called_once_with(mode=None)

    def test_accept_theirs(self, fixture_p4):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_ACCEPT_THEIRS])
        assert result.exit_code == 0
        fixture_p4.return_value.resolve.assert_called_once_with(mode="t")

    def test_no_safe_resolve(self, fixture_p4):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_NO_SAFE_RESOLVE])
        assert result.exit_code == 0
        fixture_p4.return_value.resolve.assert_called_once_with(mode="m")

    def test_stream_merge(self, fixture_p4):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_REVERSE_MAPPING, self.VALUE_REVERSE_MAPPING]
        )
        assert result.exit_code == 0
        fixture_p4.return_value.merge.assert_called_once_with(
            self.ARGUMENT_MAPPING,
            reverse=False,
            to_revision=self.ARGUMENT_CHANGELIST,
            parent=self.VALUE_REVERSE_MAPPING,
        )

    def test_no_stream_merge(self, fixture_p4):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_NO_STREAM_MERGE,
                self.OPTION_REVERSE_MAPPING,
                self.VALUE_REVERSE_MAPPING,
            ],
        )
        assert result.exit_code == 0
        fixture_p4.return_value.integrate.assert_called_once_with(
            mapping=self.ARGUMENT_MAPPING,
            reverse=False,
            stream=False,
            to_revision=self.ARGUMENT_CHANGELIST,
            parent=self.VALUE_REVERSE_MAPPING,
            use_file_paths=False,
            ignore_source_history=False,
        )

    def test_use_file_path(self, fixture_p4):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_NO_STREAM_MERGE,
                self.OPTION_USE_FILE_PATH,
                self.OPTION_REVERSE_MAPPING,
                self.VALUE_REVERSE_MAPPING,
            ],
        )
        assert result.exit_code == 0
        fixture_p4.return_value.integrate.assert_called_once_with(
            mapping=self.ARGUMENT_MAPPING + "/...",
            reverse=False,
            stream=False,
            to_revision=self.ARGUMENT_CHANGELIST,
            parent=None,
            use_file_paths=True,
            ignore_source_history=False,
        )

    def test_source_file_path(self, fixture_p4):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_NO_STREAM_MERGE,
                self.OPTION_USE_FILE_PATH,
                self.OPTION_SOURCE_FILE_PATH,
                self.VALUE_SOURCE_FILE_PATH,
                self.OPTION_REVERSE_MAPPING,
                self.VALUE_REVERSE_MAPPING,
            ],
        )
        assert result.exit_code == 0
        fixture_p4.return_value.integrate.assert_called_once_with(
            mapping=self.VALUE_SOURCE_FILE_PATH + "/...",
            reverse=False,
            stream=False,
            to_revision=self.ARGUMENT_CHANGELIST,
            parent=None,
            use_file_paths=True,
            ignore_source_history=False,
        )

    def test_stream_basic(self, fixture_p4):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_STREAM,
                self.OPTION_NO_STREAM_MERGE,
                self.OPTION_REVERSE_MAPPING,
                self.VALUE_REVERSE_MAPPING,
            ],
        )
        assert result.exit_code == 0
        fixture_p4.return_value.integrate.assert_called_once_with(
            mapping=self.ARGUMENT_MAPPING,
            reverse=False,
            stream=True,
            to_revision=self.ARGUMENT_CHANGELIST,
            parent=self.VALUE_REVERSE_MAPPING,
            use_file_paths=False,
            ignore_source_history=False,
        )

    def test_stream_use_file_path(self, fixture_p4):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_STREAM,
                self.OPTION_NO_STREAM_MERGE,
                self.OPTION_USE_FILE_PATH,
                self.OPTION_SOURCE_FILE_PATH,
                self.VALUE_SOURCE_FILE_PATH,
                self.OPTION_REVERSE_MAPPING,
                self.VALUE_REVERSE_MAPPING,
            ],
        )
        assert result.exit_code == 0
        fixture_p4.return_value.integrate.assert_called_once_with(
            mapping=self.VALUE_SOURCE_FILE_PATH + "/...",
            reverse=False,
            stream=True,
            to_revision=self.ARGUMENT_CHANGELIST,
            parent=self.VALUE_REVERSE_MAPPING + "/...",
            use_file_paths=True,
            ignore_source_history=False,
        )

    def test_stream_reverse(self, fixture_p4):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_REVERSE,
                self.OPTION_STREAM,
                self.OPTION_NO_STREAM_MERGE,
                self.OPTION_USE_FILE_PATH,
                self.OPTION_SOURCE_FILE_PATH,
                self.VALUE_SOURCE_FILE_PATH,
                self.OPTION_REVERSE_MAPPING,
                self.VALUE_REVERSE_MAPPING,
            ],
        )
        assert result.exit_code == 0
        fixture_p4.return_value.integrate.assert_called_once_with(
            mapping=self.VALUE_REVERSE_MAPPING + "/...",
            reverse=True,
            stream=True,
            to_revision=self.ARGUMENT_CHANGELIST,
            parent=self.VALUE_SOURCE_FILE_PATH + "/...",
            use_file_paths=True,
            ignore_source_history=False,
        )

    @patch("dice_elipy_scripts.integrate._fetch_remote_cl")
    def test_using_remote_p4server(self, mock_fetch_remote_cl, fixture_p4):
        mock_fetch_remote_cl.return_value = 112233
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_NO_STREAM_MERGE,
                self.OPTION_REVERSE_MAPPING,
                self.VALUE_REVERSE_MAPPING,
                self.OPTION_REMOTE_P4SERVER,
                self.VALUE_REMOTE_P4SERVER,
            ],
        )
        assert result.exit_code == 0
        mock_fetch_remote_cl.assert_called_once_with(
            self.ARGUMENT_CHANGELIST, self.VALUE_REMOTE_P4SERVER, None, self.ARGUMENT_P4PORT
        )
        fixture_p4.return_value.integrate.assert_called_once_with(
            mapping=self.ARGUMENT_MAPPING,
            reverse=False,
            stream=False,
            to_revision="112233",
            parent=self.VALUE_REVERSE_MAPPING,
            use_file_paths=False,
            ignore_source_history=False,
        )

    def test_cl_by_cl_get_list(self, fixture_p4):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_STREAM,
                self.OPTION_NO_STREAM_MERGE,
                self.OPTION_USE_FILE_PATH,
                self.OPTION_SOURCE_FILE_PATH,
                self.VALUE_SOURCE_FILE_PATH,
                self.OPTION_REVERSE_MAPPING,
                self.VALUE_REVERSE_MAPPING,
                self.OPTION_CL_BY_CL,
            ],
        )
        assert result.exit_code == 0
        fixture_p4.return_value.interchanges.assert_called_once_with(
            self.VALUE_SOURCE_FILE_PATH + "/...",
            self.VALUE_REVERSE_MAPPING + "/...",
            to_revision=self.ARGUMENT_CHANGELIST,
            use_file_paths=True,
        )

    def test_cl_by_cl_no_target_exception(self, fixture_p4):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_NO_STREAM_MERGE,
                self.OPTION_USE_FILE_PATH,
                self.OPTION_SOURCE_FILE_PATH,
                self.VALUE_SOURCE_FILE_PATH,
                self.OPTION_REVERSE_MAPPING,
                self.VALUE_REVERSE_MAPPING,
                self.OPTION_CL_BY_CL,
            ],
        )
        assert isinstance(result.exception, ELIPYException)

    def test_cl_by_cl_multiple_integrate(self, fixture_p4):
        fixture_p4.return_value.interchanges.return_value = [
            ("1234", "user1", "submit1"),
            ("5678", "user2", "submit2"),
        ]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_STREAM,
                self.OPTION_NO_STREAM_MERGE,
                self.OPTION_USE_FILE_PATH,
                self.OPTION_SOURCE_FILE_PATH,
                self.VALUE_SOURCE_FILE_PATH,
                self.OPTION_REVERSE_MAPPING,
                self.VALUE_REVERSE_MAPPING,
                self.OPTION_CL_BY_CL,
            ],
        )
        assert result.exit_code == 0
        assert fixture_p4.return_value.integrate.call_count == 2

    def test_cl_by_cl_tag_not_found(self, fixture_p4):
        fixture_p4.return_value.interchanges.return_value = [
            ("1234", "user1", "submit1"),
            ("5678", "user2", "submit2"),
        ]
        fixture_p4.return_value.check_for_tags.side_effect = [False, True]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_STREAM,
                self.OPTION_NO_STREAM_MERGE,
                self.OPTION_USE_FILE_PATH,
                self.OPTION_SOURCE_FILE_PATH,
                self.VALUE_SOURCE_FILE_PATH,
                self.OPTION_REVERSE_MAPPING,
                self.VALUE_REVERSE_MAPPING,
                self.OPTION_CL_BY_CL,
                self.OPTION_TAGS,
                self.VALUE_TAGS,
            ],
        )
        assert result.exit_code == 0
        assert fixture_p4.return_value.integrate.call_count == 1

    def test_exclude_path(self, fixture_p4):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_EXCLUDE_PATH, self.VALUE_EXCLUDE_PATH]
        )
        assert result.exit_code == 0
        fixture_p4.return_value.revert.assert_has_calls([mock.call(path=self.VALUE_EXCLUDE_PATH)])

    def test_exclude_path_accept_yours(self, fixture_p4):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [self.OPTION_EXCLUDE_PATH, self.VALUE_EXCLUDE_PATH, self.OPTION_EXCLUDE_ACCEPT_YOURS],
        )
        assert result.exit_code == 0
        fixture_p4.return_value.resolve.assert_has_calls(
            [mock.call(mode="y", path=self.VALUE_EXCLUDE_PATH)], [mock.call(mode=None)]
        )

    @patch("dice_elipy_scripts.utils.dbxmerge.DBXMerge.executable", "executable_path")
    @patch("dice_elipy_scripts.utils.dbxmerge.DBXMerge.resolve")
    def test_dbxmerge_basic(self, mock_dbxmerge_resolve, fixture_p4):
        fixture_p4.return_value.unresolved.side_effect = [
            [FilePath("d:/dev/file.dbx", "//data/kin/dev/kin-dev/file.dbx")],
            [],
        ]
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert result.exit_code == 0
        mock_dbxmerge_resolve.assert_called_once_with("d:/dev/file.dbx")

    @patch("dice_elipy_scripts.utils.dbxmerge.DBXMerge.executable", "executable_path")
    @patch("dice_elipy_scripts.utils.dbxmerge.DBXMerge.resolve")
    def test_dbxmerge_no_dbx_files_found(self, mock_dbxmerge_resolve, fixture_p4):
        fixture_p4.return_value.unresolved.return_value = []
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert result.exit_code == 0
        assert mock_dbxmerge_resolve.call_count == 0

    def test_unresolved(self, fixture_p4):
        fixture_p4.return_value.unresolved.return_value = [
            [FilePath("d:/dev/file.dbx", "//data/kin/dev/kin-dev/file.dbx")]
        ]
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert isinstance(result.exception, AutomaticP4MergeResolveException)
        fixture_p4.return_value.revert.assert_has_calls([mock.call(quiet=True)])

    def test_shelve_no_pending_cl(self, fixture_p4):
        fixture_p4.return_value.unresolved.return_value = [
            [FilePath("d:/dev/file.dbx", "//data/kin/dev/kin-dev/file.dbx")]
        ]
        fixture_p4.return_value.latest_pending_changelist.return_value = None
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_SHELVE_CL])
        assert isinstance(result.exception, AutomaticP4MergeResolveException)
        fixture_p4.return_value.latest_pending_changelist.assert_called_once()
        fixture_p4.return_value.revert.assert_has_calls([mock.call(quiet=True)])

    def test_shelve_has_pending_cl(self, fixture_p4):
        fixture_p4.return_value.unresolved.return_value = [
            [FilePath("d:/dev/file.dbx", "//data/kin/dev/kin-dev/file.dbx")]
        ]
        fixture_p4.return_value.latest_pending_changelist.return_value = "1234"
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_SHELVE_CL])
        assert isinstance(result.exception, AutomaticP4MergeResolveException)
        fixture_p4.return_value.latest_pending_changelist.assert_called_once()
        fixture_p4.return_value.set_description.assert_called_once_with(
            "1234", "Shelved changelist from failed integration"
        )
        fixture_p4.return_value.shelve.assert_called_once_with("1234", discard=False)
        fixture_p4.return_value.revert.assert_has_calls([mock.call(quiet=True)])

    @patch("dice_elipy_scripts.integrate.compile_code")
    def test_merge_verification(self, mock_compile_code, fixture_p4, fixture_submit_integration):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_MERGE_VERIFICATION,
                self.OPTION_LICENSEE,
                self.VALUE_LICENSEE,
                self.OPTION_EMAIL,
                self.VALUE_EMAIL,
                self.OPTION_PASSWORD,
                self.VALUE_PASSWORD,
                self.OPTION_USER,
                self.VALUE_USER,
                self.OPTION_DOMAIN_USER,
                self.VALUE_DOMAIN_USER,
            ],
        )
        assert result.exit_code == 0
        mock_compile_code.assert_called_once_with(
            licensee=[self.VALUE_LICENSEE],
            port=self.ARGUMENT_P4PORT,
            user=self.VALUE_USER,
            client=self.ARGUMENT_P4CLIENT,
            password=self.VALUE_PASSWORD,
            email=self.VALUE_EMAIL,
            domain_user=self.VALUE_DOMAIN_USER,
        )
        assert fixture_submit_integration.call_count == 1

    @patch("dice_elipy_scripts.integrate.compile_code")
    def test_merge_verification_exception(
        self, mock_compile_code, fixture_p4, fixture_submit_integration
    ):
        mock_compile_code.side_effect = ELIPYException()
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_MERGE_VERIFICATION,
                self.OPTION_LICENSEE,
                self.VALUE_LICENSEE,
                self.OPTION_EMAIL,
                self.VALUE_EMAIL,
                self.OPTION_PASSWORD,
                self.VALUE_PASSWORD,
                self.OPTION_USER,
                self.VALUE_USER,
                self.OPTION_DOMAIN_USER,
                self.VALUE_DOMAIN_USER,
            ],
        )
        assert isinstance(result.exception, ELIPYException)
        mock_compile_code.assert_called_once_with(
            licensee=[self.VALUE_LICENSEE],
            port=self.ARGUMENT_P4PORT,
            user=self.VALUE_USER,
            client=self.ARGUMENT_P4CLIENT,
            password=self.VALUE_PASSWORD,
            email=self.VALUE_EMAIL,
            domain_user=self.VALUE_DOMAIN_USER,
        )
        assert fixture_submit_integration.call_count == 0

    def test_submit(self, fixture_p4, fixture_submit_integration):
        submit_message = (
            f"Integrated from {self.VALUE_SOURCE_FILE_PATH}/..."
            f"@{self.ARGUMENT_CHANGELIST}.\n{self.VALUE_SUBMIT_MESSAGE}"
        )
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_SOURCE_FILE_PATH,
                self.VALUE_SOURCE_FILE_PATH,
                self.OPTION_SUBMIT_MESSAGE,
                self.VALUE_SUBMIT_MESSAGE,
            ],
        )
        assert result.exit_code == 0
        fixture_submit_integration.assert_called_once_with(
            p4_object=fixture_p4.return_value,
            submit_message=submit_message,
            submit=True,
        )

    def test_submit_no_extra_message(self, fixture_p4, fixture_submit_integration):
        submit_message = (
            f"Integrated from {self.VALUE_SOURCE_FILE_PATH}/...@{self.ARGUMENT_CHANGELIST}."
        )
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_SOURCE_FILE_PATH,
                self.VALUE_SOURCE_FILE_PATH,
            ],
        )
        assert result.exit_code == 0
        fixture_submit_integration.assert_called_once_with(
            p4_object=fixture_p4.return_value,
            submit_message=submit_message,
            submit=True,
        )

    def test_submit_cl_by_cl(self, fixture_p4, fixture_submit_integration):
        fixture_p4.return_value.interchanges.return_value = [
            ("1234", "user1", "submit1"),
            ("5678", "user2", "submit2"),
        ]
        submit_start = "Integrated from " + self.VALUE_SOURCE_FILE_PATH + "/...@"
        submit_message_1 = submit_start + "1234,1234 (submitted by user1).\nsubmit1"
        submit_message_2 = submit_start + "5678,5678 (submitted by user2).\nsubmit2"
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_STREAM,
                self.OPTION_NO_STREAM_MERGE,
                self.OPTION_USE_FILE_PATH,
                self.OPTION_SOURCE_FILE_PATH,
                self.VALUE_SOURCE_FILE_PATH,
                self.OPTION_REVERSE_MAPPING,
                self.VALUE_REVERSE_MAPPING,
                self.OPTION_SUBMIT_MESSAGE,
                self.VALUE_SUBMIT_MESSAGE,
                self.OPTION_CL_BY_CL,
            ],
        )
        assert result.exit_code == 0
        fixture_submit_integration.assert_has_calls(
            [
                mock.call(
                    p4_object=fixture_p4.return_value,
                    submit_message=submit_message_1,
                    submit=True,
                ),
                mock.call(
                    p4_object=fixture_p4.return_value,
                    submit_message=submit_message_2,
                    submit=True,
                ),
            ]
        )
        assert fixture_submit_integration.call_count == 2

    def test_no_submit(self, fixture_p4, fixture_submit_integration):
        submit_message = (
            f"Integrated from {self.VALUE_SOURCE_FILE_PATH}/...@{self.ARGUMENT_CHANGELIST}."
        )
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [self.OPTION_SOURCE_FILE_PATH, self.VALUE_SOURCE_FILE_PATH, self.OPTION_NO_SUBMIT],
        )
        assert result.exit_code == 0
        fixture_p4.return_value.revert.assert_called_with(quiet=True)
        assert fixture_p4.return_value.revert.call_count == 2
        fixture_submit_integration.assert_called_once_with(
            p4_object=fixture_p4.return_value,
            submit_message=submit_message,
            submit=False,
        )

    @patch("dice_elipy_scripts.integrate.cook_data")
    def test_cook_data(self, mock_cook_data, fixture_p4):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_COOK,
                self.OPTION_SOURCE_FILE_PATH,
                self.VALUE_SOURCE_FILE_PATH,
                self.OPTION_REVERSE_MAPPING,
                self.VALUE_REVERSE_MAPPING,
                self.OPTION_DATA_DIR,
                self.VALUE_DATA_DIR,
            ],
        )
        assert result.exit_code == 0
        mock_cook_data.assert_called_once_with(
            assets=["Game/Levels/MP/MP_Orbital"],
            data_directory=self.VALUE_DATA_DIR,
            platform="win64",
            clean_avalanche_drop_db=True,
            code_branch="kin-dev",
            data_branch="kin-stage",
            import_avalanche=True,
            p4_object=fixture_p4.return_value,
        )
