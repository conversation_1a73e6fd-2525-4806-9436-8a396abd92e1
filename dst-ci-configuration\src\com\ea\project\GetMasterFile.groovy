package com.ea.project

import com.ea.project.all.mastersettings.FrostedAutotestMaster

class GetMasterFile {
    static Collection get_masterfile(def buildUrl) {
        String master_name = buildUrl.split('\\.')[0].split('//')[1]
        def master_configs = [
            'aws-kin-preflight'             : com.ea.project.kin.mastersettings.AWSPreflight,
            'aws-kin-staging'               : com.ea.project.kin.mastersettings.AWSStaging,
            'bct-autotest-jenkins'          : com.ea.project.bct.mastersettings.BctAutotest,
            'bct-dev-jenkins'               : com.ea.project.bct.mastersettings.BctDevJenkins,
            'bct-preflight-jenkins'         : com.ea.project.bct.mastersettings.BctPreflight,
            'bct-ch1-autotest-jenkins'      : com.ea.project.bctch1.mastersettings.BctCh1Autotest,
            'bct-ch1-dev-jenkins'           : com.ea.project.bctch1.mastersettings.BctCh1Dev,
            'bct-ch1-rel-jenkins'           : com.ea.project.bctch1.mastersettings.BctCh1Rel,
            'dice-build-jenkins'            : com.ea.project.all.mastersettings.DiceBuild,
            'dice-joss-jenkins'             : com.ea.project.all.mastersettings.DiceJoss,
            'kin-autotest-jenkins'          : com.ea.project.kin.mastersettings.DiceKinAutotest,
            'kin-dev-jenkins'               : com.ea.project.kin.mastersettings.DiceKinDev,
            'kin-preflight-jenkins'         : com.ea.project.kin.mastersettings.DiceKinPreflight,
            'kin-release-jenkins'           : com.ea.project.kin.mastersettings.DiceKinRelease,
            'dice-kin-tasks'                : com.ea.project.kin.mastersettings.DiceKinTasks,
            'dice-san-autotest'             : com.ea.project.san.mastersettings.DiceSanAutotest,
            'dice-san-dev'                  : com.ea.project.san.mastersettings.DiceSanDev,
            'dice-san-preflight'            : com.ea.project.san.mastersettings.DiceSanPreflight,
            'test1-jenkins'                 : com.ea.project.all.mastersettings.DiceJenkinsTestenv,
            'dice-upgrade-validator'        : com.ea.project.all.mastersettings.DiceUpgradeValidator,
            'dre-frosted-azure-test-jenkins': FrostedAutotestMaster,
            'fb1-jenkins'                   : com.ea.project.fb1.mastersettings.Fb1,
            'mer-dev-jenkins'               : com.ea.project.mer.mastersettings.MerDev,
            'nfs-dev-jenkins'               : com.ea.project.nfs.mastersettings.NfsDev,
            'res-gnt-dev'                   : com.ea.project.gnt.mastersettings.ResGntDev,
        ]
        if (master_name == 'test') {
            return master_configs.values()
        } else if (master_configs.containsKey(master_name)) {
            return [master_configs[master_name]]
        }

        throw new IllegalArgumentException('GetMasterFile called with invalid master name: ' + master_name)
    }
}
