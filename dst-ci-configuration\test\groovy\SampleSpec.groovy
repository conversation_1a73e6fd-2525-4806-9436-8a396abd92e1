/**************************************************************************
 * This a sample unit test file that is written as a Groovy unittest (Spock)
 *
 * To run created tests you have to push your changes and the test will be
 * run automatically by the Gitlab pipeline.
 *
 **************************************************************************/

import spock.lang.Ignore
import spock.lang.Specification

@Ignore
// comment out Ignore to run tests
class SampleSpec extends Specification {

    void "test empty string"() {    // Sample test that check if a string is empty
        when:
        String myString = ''

        then:
        myString.isEmpty() == true
    }

    void "test match string"() {    // Sample test that check if the actual string is equal to the expected
        when:
        String myString = 'Hello'

        then:
        myString == 'Hello'
    }

    void "test match value"() {        // Sample test that check if the actual value is equal to the expected
        when:
        Integer myValue = 8

        then:
        myValue == 8
    }

    void "test multiple"() {        // Sample test that run 3 tests and throws an exception if at least 1 test fail
        when:
        String myString = ''
        String myStringActual = 'Hello'
        String myStringExpected = 'Hello'
        Integer myValueActual = 8
        Integer myValueExpected = 8

        then:
        myString.isEmpty() == true
        myStringExpected == myStringActual
        myValueExpected == myValueActual
    }

    void "test exception, specifically ArrayIndexOutOfBounds"() {
        when:
        def arr = new int[3] // 3 element array
        arr[5] = 5 // try to access index 5

        then:
        thrown(ArrayIndexOutOfBoundsException)
    }

    void "test parametrised Pythagorean triples"() {
        expect:
        Math.sqrt(sideA**2 + sideB**2) == hypothenuseC

        // press ctrl+alt+l to have IntelliJ auto-format the data table
        where:
        sideA | sideB || hypothenuseC
        3     | 4     || 5
        5     | 12    || 13
        8     | 15    || 17
    }
}

