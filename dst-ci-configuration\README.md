# Cobra platform JobDSL

Repository for the Cobra platform JobDSL, including the shared pipeline library `dst-lib`

## Local development environment

In order to follow this guide, start with a clean project:

- Close IntelliJ IDEA
- Open a terminal on the path of this project:

  ```bash
  # Clean all your settings
  git reset --hard HEAD
  git checkout master
  git clean -fxd
  ```

- Remove Gradle cache:

  ```
  # Remove gradle cache
  rm -rf ~/.gradle

  # In Windows
  # - Go to your home with the Windows Explorer
  # - Find the folder .gradle and delete it
  ```

- Set up AF2 you need to
    - Go to: <https://artifacts.ea.com/ui/user_profile>
    - Click in Generate an Identity Token
    - Add the following env vars on your OS (it may depend if Windows/Mac)
      ```
      ARTIFACTORY_USER: <<EMAIL>>
      ARTIFACTORY_APIKEY: the-token-you-generated-from-the-artifacts-page
      ```

### Recommended setup with IntelliJ IDEA

1. Install a Java SDK. Preferably same version as is used here to avoid using new language features when developing
   locally. Pre-built distributions of OpenJDK 21 can be found at
   [Adoptium Temurin](https://adoptium.net/temurin/archive/?version=21)
   .
2. Install the latest version of [IntelliJ IDEA Community Edition](https://www.jetbrains.com/idea/download/).
3. Open the cloned folder in IDEA.
    1. IDEA will detect that this project has a Gradle configuration:

       ![Gradle build scrip found](images/gradle-build-script-found.png)
    2. Click _Load Gradle Project_.
    3. Click _View_, _Tool Windows_, _Gradle_ to get a list of Gradle tasks.
        - _build_, `assemble` compiles everything
        - _verification_
            - `codenarc` runs static code analysis using CodeNarc
            - `seed` verifies the JobDSL seeds
            - `unit` runs all unit tests
            - `schedulers` runs all pipeline scheduler tests
            - `vars` runs all vars tests
            - `test` runs both _unit_ and _seed_
            - `check` runs _test_ and _codenarc_
4. _resources/validation.sh_ verifies the pipeline scheduler from the commandline.
   Example: `$ ./resources/validation.sh src/scripts/schedulers`
5. Set the Project SDK by right clicking on `dst-ci-configuration` in `Project Explorer` ("Project" on the left side of
   the IDE) and select `Open Module Settings`. Go to `Project Settings` -> `Project` and add the Java SDK you just
   installed by clicking `Edit` ![Project Settings](images/project-structure.png)
6. Update the Gradle JVM by clicking `File` -> `Settings...` -> `Build, Execution, Deployment` -> `Build Tools`
   -> `Gradle` and set `Gradle JVM` to the Java SDK you have installed. ![Gradle Settings](images/gradle-settings.png)

> **Note:** The Codenarc plugin has been integrated with IntelliJ using
> inspection rules from an unclear source (they don't match ours).
> These inspection rules will lead to large blocks of code being highlighted as
> warnings. A workaround until a better solution is found to is to disable the
> IntelliJ codenarc plugin in IntelliJ (you can still use codenarc gradle task
> without the IntelliJ codenarc plugin).*
>
> ![Gradle build scrip found](images/codenarc_plugin_workaround.png)

### Other IDE

If you choose to use another IDE you can still use the Gradle Wrapper (`./gradlew` or `.\gradlew.bat`) to verify your
changes by executing the Gradle tasks mentioned above:
`$ ./gradlew unit`

## Unit testing

We use the Spock unit testing framework and you can implement tests by extending `Specification`.

In order to test Jenkins Pipelines (`Schedulers`) or `./vars` functions, you should
extend `DeclarativePipelineSpockTest`.

`DeclarativePipelineSpockTest` also supports non-regression testing by calling `testNonRegression` at the end of a test.
To update the callstacks you should run the `updateCallstacks` Gradle task.

`Spock` can only mock static functions implemented in Groovy, which is fine when we mock code that we have written
ourselves. Sometimes we have to mock imported dependencies implemented in Java such as `Jenkins.get()`, and for those we
use `PowerMock`. In all other cases use `Spock's` mocking capabilities, for instance `Mock` or `GroovyMock`.

### Resources

- [Spock documentation](https://spockframework.org/spock/docs/2.0/all_in_one.html)
- [Spock mocking](https://spockframework.org/spock/docs/1.0/interaction_based_testing.html)
- [Mock and GroovyMock Javadoc](https://spockframework.org/spock/javadoc/1.0/spock/lang/MockingApi.html)
- [Jenkins Pipeline Unit (schedulers and vars)](https://github.com/jenkinsci/JenkinsPipelineUnit)
- [Powermock for mocking static Java functions](https://github.com/powermock/powermock)

## Stop using `./vars`

Even though putting helper methods in `./vars` for sharing between pipelines is
[encouraged and recommended by Jenkins](https://www.jenkins.io/doc/book/pipeline/shared-libraries/#directory-structure)
it has the drawback of then not being understood by IDEA. Jenkins also supports putting helper methods in `./src` like
any other "standard Java source directory structure". If we choose to put our code in `./src` we will get IDE inline
documentation, code completion and other blows and whistles. We are still adhering to Jenkins Shared Libraries'
directory structure, but limits it to:

```plain
(root)
+- src                     # Groovy source files
|   +- com
|       +- ea
|           +- Bar.groovy  # for com.ea.Bar class
+- resources               # resource files (external libraries only)
|   +- bar.json    # static helper data for com.ea.Bar
```

## Coding standards

We try to adhere to the [Groovy Style Guide](https://groovy-lang.org/style-guide.html)
and [Java Naming Conventions](https://www.oracle.com/java/technologies/javase/codeconventions-namingconventions.html)
except that we require the `return` keyword. By using IDEA you will get help with automatic formatting and some—but not
all—style rules are enforced by CodeNarc in CI.

## AutotestMatrix children

These files have a tendency to become large and also in bytecode. Here's some guidelines to avoid that, in order of
importance.

### Hand over populating `tests` to `TestInfo`

If the `tests` in a `TestInfo` just differ in name and share the other properties, instead give `testNames` and the
other properties to `TestInfo` to considerably decrease bytecode size.

### Members

#### No single use

Avoid adding members to the class that are used once.

#### `List` or other collections

You only save bytecode size if the `List` has a size larger than two. For smaller lists it's better to inline them.

## General setup

- Seed files, organized per type of job.
- Library files, with the more detailed job definitions.
- Schedulers, mainly one per type of job.
- GetMasterFile and GetBranchFile, to check which settings file that will be used in each case.
- Project settings files, generally one per game project.
- Master settings files, one per Jenkins master.
- Branch settings files, one per branch type (in many cases this translates to one file per branch).

## Seed files

Jobs listed, separated by type - standard jobs, autotests, preflights and integrations. The general setup is a start job
that is triggered either by perforce checkins, other jobs or user interaction. This is followed by one or many build
jobs of the same type, where most of the job is done.

One special case: `dice_build_jenkins` - this seed file is used for a Jenkins controller where the main jobs are
build deleter jobs for all projects we have.
Settings for build deleter jobs:

- `project_name`: the project settings file for the project we are deleting files for.
- `branch_folder`/`branch_name`: use a stable branch in the project.
- `trigger_string`: if we need a different trigger setting than default, specify a cron string here.
  Default is to trigger every 30 minutes.
- `extra_args`: extra arguments to send to the Elipy script. This is e.g. used to control what we run in a job,
  to be able to delete different parts of a project in different jobs running in parallel.
  See <https://gitlab.ea.com/dre-cobra/elipy/elipy-scripts/-/blob/master/dice_elipy_scripts/deleter.py?ref_type=heads>
  for available args.
- `label`: this label will be used together with `deleter` to find a machine to run the job on.
- `elipy_call`: if we are going to run a deleter job in another location, we need to use an updated Elipy call.
  This should ideally be defined in the project settings file.
- `p4_code_server`/`p4_code_creds`: if we are going to run a deleter job in another location,
  we need to specify a Perforce server and associated credentials to use in that location.
- `skip_empty_folders`: set to `true` to skip creating an extra start job which triggers the deleter job weekly
  with settings to delete empty folders for the project.

## Library files

Here we handle the definition of the jobs, to make the seed files easier to read.

## Schedulers

The schedulers collect information from the start jobs and from other sources, and then (usually) trigger one or more
build jobs. When the build jobs are finished, another start jobs is often triggered.

## GetMasterFile and GetBranchFile

## Project settings files

Here we specify settings that apply to all branches for this project.

- `frostbite_syncer_setup` - when set to true, assume that code and data are on the same stream.

## Master settings files

Here we specify which branches that will be built on this master. For integrations, we specify all settings here (
instead of in the branch settings files) since integrations by definition involve more than one branch.

### Integrate branches, settings

- `accept_theirs` - set to `true` to pass on the `--accept-theirs` flag, and ensure elipy calls for perforce to use
  it (`at` option) for merge resolving.
- `code`
- `data`
- `data_only_source_branch` - set to `true` if the source branch for a data-only integration does not have code.
- `data_upgrade_validator` - set to `true` for a job that will run a validation of the `FrostbiteDatabaseUpgrader` tool.
- `elipy_call` - format for the call to our execution layer Elipy.
- `exclude_drone`
- `exclude_sparta`
- `ignore_source_history` - set to `true` to ignore complicated source file history, using the Perforce integrate
  flag `-Di`. This should generally not be used, and should only be turned on after request from a TD.
- `integration_reference_job`
- `no_safe_resolve` - set to `true` to pass on the `--no-safe-resolve` flag, and ensure elipy calls for perforce to use
  it (`am` option) for merge resolving.
- `offsite_code_token`
- `offsite_drone_builds`
- `offsite_job_remote`
- `p4_code_force_copy`: `--no-force` - If '--force', will force copy and override target branch (code)
- `p4_data_force_copy`: `--no-force` - If '--force', will force copy and override target branch (data)
- `parent_to_child` - direction in the perforce stream graph, `true` for "down" and `false` for "up".
- `slack_channel` - Slack channel to report failures for the integration job.
- `smoke_integration`
- `stream_integration` - use branch mapping
- `source_branch`
- `source_folder`
- `target_branch`
- `target_folder`
- `verified_integration`
- `workspace_root` - location on the build machines where the builds are processed.

### Maintenance branch, settings: (in order to create maintenance job, we need to have one maintenance branch setting per master)

- `code_branch` - set to valid code branch name which defined in GetBranchFile's project block
- `code_folder`
- `data_branch` - set to valid data branch name which defined in GetBranchFile's project block
- `data_folder`
- `scheduled_nuke` - set value when regularly avalanche nuke to run (useful for autotest master if cannot get Avalanche
  gc
  properly run everyday or preflight cached too old assets)
- `avalanche_maint` - set to `false` to skip nightly running avalanche_maintenance gc job.
- `restart_controller` - boolean to enable restart of Jenkins controller in jenkins.shutdown job
- `restart_nodes` - boolean to enable restart of Jenkins agents in jenkins.shutdown job
- `wait_doquite_hours` - set value to determinate how long( unit: hour) to wait for master get quite before restart
- `wait_forcekill_hours` - set value to determinate how long( unit: hour) to wait for master before force restart
- `master_restart_timer` - set cron value to determinate when jenkins.shutdown job to run, default: "H 0 ** 7"
- `include_vault` - if set to `true` will create the vault job.
- `include_register_release_candidate` - if set to `true` will create the register_release_candidate job.

### MAINTENANCE_SETTINGS, settings

- `ORPHANED_PROJECT_SHORT_NAME` - Override for project short name, used to determine Slack credentials.
- `ORPHANED_SLACK_CHANNEL` - Which Slack channel to send messages to whenever there are orphaned jobs on a master.
- `ORPHANED_TRIGGER` - CRON trigger to determine when to run the `delete_orphaned_jobs` job.

## Branch settings files

Most setting that are specific for a certain branch end up here. For many settings, there are default values that will
be used if no value is specified.

### General settings

Required settings without default values:

- `elipy_call` - format for the call to our execution layer Elipy.
    - To enable `FbEnvCore` for a branch, add `' --use-fbenv-core'` to the Elipy call for that branch.
- `workspace_root` - location on the build machines where the builds are processed.

Non-required settings without default values:

- `frostbite_licensee` - Which licensee you would like to run gensln with.
- `gametool_settings` - Map used to configure gametool jobs:
    - `trigger` - Poll SCM CRON trigger
    - `non_virtual_code_branch` - The parent (source) code branch of the virtual stream.
    - `non_virtual_code_folder` - The p4 directory which contains the virtual stream's (source) code branch.
    - `non_virtual_data_branch` - The parent (source) data branch of the virtual stream.
    - `non_virtual_data_folder` - The p4 directory which contains the virtual stream's (source) data branch.
    - `gametools` - Map to enable and configure specific gametools
        - `<gametool_name>` - Name of the gametool to enable and configure (for example: icepick)
            - `timeout_hours` - Timeout in hours for the gametool build job
            - `config` - The config to build
            - `framework_args` - Framework arguments to the nant build command
    - `slack_channel` - Slack channel to report job results to
    - `filter_paths_icepick` - Overwrite the default filepath to the Icepick code in Perforce (triggers SCM change)
    - `ignore_paths_icepick` - Overwrite the default filepaths to ignore in Icepick code in Perforce (doesn't trigger
      SCM change)
- `unittests` - enables a unit testing job that triggers `TnT/Bin/unicron/unicron.py` - currently supported by Criterion

### Standard jobs settings

Required settings without default values:

- `asset`
- `server_asset`

Settings with default value `false`:

- `avalanche_compress_bundles` - set to `true` to compress avalanche bundles when exporting
- `bilbo_store_offsite`
- `build_without_new_code` - set to `true` to globally disable frosty build with no code change, can switch on per each
  run from Jenkins GUI
- `clean_local` - set to `true` to always run with clean builds.
- `clean_master_version_check` - if `true` run a clean build if we have a master version update.
- `code_drone_branch`
- `dataset_in_path` - set to `true` for a branch where the dataset is used in the Perforce path.
- `datasnooper` - set to `true` if you want to run `datasnooper.exe` (requires `datasnooper_dbmanifest_path` )
- `oreans_protection` - set to `true` if you want to protect the executable using oreans virtualizer.
- `oreans_artifactory_files` - fetch oreans files from artifactory
- `denuvo_wrapping`
- `denuvo_artifactory_files` - fetch deunvo files from artifactory
- `deployment_data_branch` - set to `true` to create a duplicate data job. This job has an additional step to deploy
  bundles to filer.
- `frosty_orchestrator_trigger` - set a CRON trigger to create a frosty-orchestrator job that will trigger
  deployment-data jobs which in turn trigger frosty jobs. Runs on a schedule.
- `disable_frosty_symbol_upload` - set to `true` for branches to disable frosty symbol upload.
- `dry_run_code` - set to `true` to build code, without copying the result to filer.
- `dry_run_data` - set to `true` to build data, without registering the result in Bilbo.
- `dry_run_drone` - set to `true` to build drone, without checking in the result in Perforce.
- `dry_run_frosty`
- `dry_run_navmesh`
- `dry_run_patchdata`
- `dry_run_patchfrosty`
- `enable_clean_build_validation` - if `true`, creates a daily clean data job for build validation
- `enable_steam_drm_wrapping` - set to `true` to enable Steam DRM wrapping for frosty and patch frosty builds on this
  stream.
- `export_data` - if `true` we are a `data-export` job and we don't want to export bundles
- `use_super_bundles` - if `true` export bundles in data build jobs and use exported bundles on Frosty build jobs
- `expression_debug_data` - set to `true` for a branch that's going to have expression debug data.
- `first_patch_all` - Used to say if all platforms are building their first patch or not.
- `first_patch_<modifier>` - Used to say if we are building first path for a certain subset, e.g. `first_patch_xb1`
  or `first_patch_ps4_final`.
- `frosty_only_build_on_new_code` - Specific instantiation of the `main_unverified_branch`. Flag specific for
  kin-dev-unverified used to signal frosty.
- `linux_docker_images`
- `main_unverified_branch` - Main branch for code/data dependencies for a project with a `game-dev`/
  `game-dev-unverified`
  type of setup.
- `marvin_trigger_upload` - set to `true` to UploadDiceBuildsToS3 at the end of frosty job
- `marvin_trigger_upload_and_test` - set to `true` to spawn a new job at the end of frosty which triggers
  UploadDiceBuildsToS3 AND testing.
- `mimallocEnabled` - set to `true` or `false` to pass `--framework-args -G:eaconfig.memory.mimalloc=<mimallocEnabled>`
- `move_location_parallel` - set to `true` to register Bilbo in two locations and move drone builds to a new location.
  Copies code platforms/configs in parallel to speed up the process.
- `move_location_frosty` - list of locations to move a frosty builds to, including register Bilbo in the new locations.
  The location details have to be specified under `new_locations`.
- `no_trim` - set to `true` to make data builds not run with `-trim` unless we are deploying bundles
- `offsite_drone_builds` - set to `true` to trigger offsite drone builds with a curl command using `offsite_code_token`
  and `offsite_job_remote`.
- `offsite_drone_basic_builds` - set to `true` to copy basic, stripped down versions of drone builds to a network share
  location usually for Outsourcers to use. This location should be specified in the project yml file in `elipy-scripts`.
- `offsite_basic_drone_zip_builds` - set to `true` to copy basic, stripped down versions of drone builds, that are
  zipped up, to a network share location. This location should be specified in the project yml file in `elipy-scripts`
- `offsite_basic_drone_zip_builds_force_zip_override` - set to `true` to overwrite an existing zip file if it exists.
- `pipeline_warning_job` - set to `true` to run the pipeline warning extraction scripts for this branch.
- `poolbuild_<job_type>` - set to `true` to use `poolbuild` machines, with separate pools per platform but shared with
  multiple streams. Available for `data`, `export_data`, `patchdata`, `frosty` and `patchfrosty`.
- `remote_masters_to_receive_code` - list of objects containing `name` and `allow_failure`for remote Jenkins
  controllers'
  URL which trigger `lastknowngood.code` job, currently only for preflight, but can be in general case too. Set
  `allow_failure` to `true` so that the trigger does not failure the job
- `remote_masters_to_receive_data` - list of objects containing `name` and `allow_failure`for remote Jenkins
  controllers'
  URL which trigger `lastknowngood.data` job, currently only for preflight, but can be in general case too. Set
  `allow_failure` to `true` so that the trigger does not failure the job
- `remote_masters_trigger_pipeline` - set to `true` to use the pipeline version of the receiving job on the remote
  master.
  This version of the job has more advanced triggering options.
- `reshift` - set to `true` to force push builds to shift which have already been pushed.
- `same_baseline_config` - set to `true` to use the same config for the baseline as we use for the patch. Default
  behaviour in Elipy is to use the `retail` config for the baseline, regardless of the config used for the patch.
- `separate_symbol_store_upload` - set to `false` to not have a separate symbol upload job.
- `shift_branch`
- `shift_every_build` - set to `true` to trigger a shift upload job in the end of the frosty /patchfrosty scheduler.
- `shift_compression` - zip big supplemental shift file
- `single_stream_smoke` - add job for registering smoke on branch set up without unverified branch
- `skip_frosty_scheduler` - set to `true` for branch with frosty start job inactive.
- `skip_importing_baseline_state` - set to `true` to skip importing the baseline state in a patchdata job.
- `skip_streaming_install_package` - set to `true` to use the default value
  of `STREAMING_INSTALL_CREATE_SUBMISSION_PACKAGES` when buliding a frosty patch package for ps4 retail
- `skip_symbols_backup` - set to `true` to skip uploading symbols to backup.
- `skip_symbols_to_symstore` - set to `true` to skip uploading symbols to Symstore.
- `slack_notify_bot_code`: Set to `true` to send a slack bot message for failed code builds.
- `slack_notify_bot_code_nomaster`: Set to `true` to send a slack bot message for failed code nomaster builds.
- `slack_notify_bot_code_stressbulkbuild`: Set to `true` to send a slack bot message for failed code stressbulkbuild
  builds.
- `slack_notify_bot_data`: Set to `true` to send a slack bot message for failed data builds.
- `strip_symbols` - set to `false` to not strip symbols out of the linux server exe.
- `sparta_branch`
- `store_regular_baseline_builds` - set to `true` to run store baseline job regularly.
- `timeout_hours_data` - timeout hours of a data job (default: 3)
- `timeout_hours_upgrade_data` - timeout hours of a data upgrade job (default: 4)
- `use_linuxclient` - set to `true` for branches where we need to use this name in `FrostyIsoTool` when building
  `linux64`.
- `use_new_pkg_server`
- `use_recompression_cache` - set to `true` to use alternative Avalanche server for the recompression cache.
  Specify a platform afterwards to have it be specifically turned off for that platform. E.g., for ps5.
- `use_recompression_cache_ps5` - can be set to `false` in combination with a true `use_recompression_cache` to
  specifically turn it off for ps5.
- `enable_eac` - set to `true` to create EasyAntiCheat protected .exe by passing `ANTICHEAT_ENABLED` to FrostyISOTool
- `use_dynamic_disc_baselines` - set to `true` to take disc baselines values from the last successful store baseline
  job.
- `use_win64trial`
- `use_snowcache` - set to `true` to use snowcache for code jobs
- `vault_default_platforms` - add platforms that should be vaulted by default in the vault.start job for that project.
- `vault_secrets_project` - a list of maps configuring project level vault secrets
- `vault_secrets_branch` - a list of maps configuring branch level vault secrets
- `vault_secrets_job` - a list of maps configuring job level vault secrets
- `verified_data_branch`
- `verify_post_vault` - if set to `true` in the project settings, when manually triggering vault.start the boolean
  parameter will by default be true. Passes on `--verify-post-vault` flag to Elipy.
- `webexport_branch` - set to `true` for a branch that's going to build the webexport job.
- `skip_icepick_settings_file` - set to `true` in order to not pass `--settings-files` (derived from `Project`) when
  running Autotests on the given branch.
- `skip_clean_label` - set to `true` to disable adding the "clean-build" label even if there are X recent passed builds
  without it.
- `compress_symbols` - set to `true` to enable symbol compression for builds.
- `compress_symbols_code_win64server` - set to `true` to enable symbol compression specifically for the `win64server`
  platform.
- `compress_symbols_code_win64game` - set to `true` to enable symbol compression specifically for the `win64game`
  platform.
- `compress_symbols_code_xb1` - set to `true` to enable symbol compression specifically for the `xb1` platform.
- `compress_symbols_code_xbsx` - set to `true` to enable symbol compression specifically for the `xbsx` platform.
- `file_hashes_frosty` - set to `true` to enable file hash generation for Frosty builds.
- `retry_limit_data` - specifies the maximum number of retries for data jobs. Default is `1`.
- `retry_limit_patchdata` - specifies the maximum number of retries for patchdata jobs. Default is `1`.

Settings with default value `true`:

- `baseline_set` - set to `false` to do baseline builds.
- `elipy_shift_config` - set to `false` for branches that don't use Shift configuration in the `elipy-script`
  repository.
- `import_local` - set to `false` to not import the state from the previous successful code build.
- `skip_code_build_if_no_changes` - set to `false` if you want to build code on every changelist.
- `smoke_cl_after_success` - set to `true` if you want to mark as smoked (qv-verified) the code build CL.
- `statebuild_code` - set to `false` to use specific build machines for this job type.
- `statebuild_code_nomaster` - set to `false` to use specific build machines for this job type.
- `statebuild_data` - set to `false` to use specific build machines for this job type.
- `statebuild_frosty` - set to `false` to use specific build machines for this job type.
- `statebuild_icepick` - set to `false` to use specific build machines for this job type.
- `statebuild_patchdata` - set to `false` to use specific build machines for this job type.
- `statebuild_patchfrosty` - set to `false` to use specific build machines for this job type.
- `statebuild_shift` - set to `false` to use specific build machines for this job type.
- `statebuild_verified_data` - set to `false` to use specific build machines for this job type.
- `statebuild_webexport` - set to `false` to use specific build machines for this job type.
- `webexport_import_avalanche` - set to `false` to stop importing and cooking an avalanche state.
- `wipe_python_dir` - set to `false` to disable initial Python directory wipe from disk.

Settings with other default values:

- `data_reference_job`: `code_branch + ".code.start"`
- `export_data_reference_job`: `code_branch + ".code.start"`
- `frosty_asset`: `asset`
- `frosty_reference_job`: `branch_name + ".data.start"`
- `frosty_server_asset`: `server_asset`
- `frosty_digital_asset`: `asset`
- `icepick_frosty_reference_job`: `branch_name + ".frosty.start"`
- `increase_version_by`: 1 - increase version for ps4 patchfrosty jobs, sometimes needs to be higher than 1.
- `job_label_statebuild`: `"statebuild"` - set another value here to build a branch on machines dedicated to that
  branch, but one pool for all jobs on that branch.
- `patch_branch`: `data_branch` - set another value to specify a different branch name to use when calling LibBaseline.
- `patch_type`: `incremental` - set to `from_disk` to generate a `ddelta` from the data shipped on disc.
- `retry_limit`: 1 for code, frosty and patchfrosty jobs, 0 for other jobs.
- `shift_reference_job`: `branch_name + ".frosty.start"`
- `smoke_downstream_jobs`: `""`
- `slack_channel_code`: `[channels: [], always_notify: false, skip_for_multiple_failures: false]` - Slack channel(s) to
  report failures for code builds.
- `slack_channel_code_drone`: `[channels: [], always_notify: false, skip_for_multiple_failures: false]` - Slack channel(
  s) to report failures for code drone builds.
- `slack_channel_code_nomaster`: `[channels: [], always_notify: false, skip_for_multiple_failures: false]` - Slack
  channel(s) to report failures for code nomaster builds.
- `slack_channel_data`: `[channels: [], always_notify: false, skip_for_multiple_failures: false]` - Slack channel(s) to
  report failures for data builds.
- `slack_channel_export_data`: `[channels: [], always_notify: false, skip_for_multiple_failures: false]` - Slack
  channel(s) to report failures for export-data builds.
- `slack_channel_frosty`: `[channels: [], always_notify: false, skip_for_multiple_failures: false]` - Slack channel(s)
  to report failures for frosty builds.
- `slack_channel_navmesh`: `[channels: [], always_notify: false, skip_for_multiple_failures: false]` - Slack channel(s)
  to report failures for navmesh builds.
- `slack_channel_patchdata`: `[channels: [], always_notify: false, skip_for_multiple_failures: false]` - Slack channel(
  s) to report failures for patchdata builds.
- `slack_channel_patchfrosty`: `[channels: [], always_notify: false, skip_for_multiple_failures: false]` - Slack
  channel(s) to report failures for patchfrosty builds.
- `slack_channel_shift`:
  `[channels: ["#cobra-outage-shift"], always_notify: false, skip_for_multiple_failures: false]` - Slack channel(s) to
  report failures for shift jobs.
- `slack_channel_sparta`: `[channels: [], always_notify: false, skip_for_multiple_failures: false]` - Slack channel(s)
  to report failures for sparta builds.
- `slack_channel_verified_data`: `[channels: [], always_notify: false, skip_for_multiple_failures: false]` - Slack
  channel(s) to report failures for verified-data builds.
- `store_baseline_reference_job`: `branch_info.branch_name + '.frosty.start'` - the job used to determine the latest
  stable CL for a store-baseline job if no user parameters are set.
- `timeout_hours_webexport`: `4` - timeout setting for webexport jobs
- `trigger_jobs_from_remote_master`:
  `[remote_branch: <no default value>, trigger_on_new_code: [], trigger_on_new_data:[]]` - set name for the branch on
  the remote Jenkins master that will send changelist information to this master. Provide lists with job(s) that should
  trigger on new code or data changelists.
- `trigger_string_code`: `"H/5 * * * 1-6\nH/5 6-23 * * 7"`
- `trigger_string_code_drone`: `"H/5 * * * 1-6\nH/5 6-23 * * 7"`
- `trigger_string_code_nomaster`: `"H * * * 1-6\nH 6-23 * * 7"`
- `trigger_string_data`: `"H/5 * * * 1-6\nH/5 6-23 * * 7"`
- `trigger_string_icepick_fs`: `"H 0 * * 1-6\nH 6 * * 7"`
- `trigger_string_icepick_smoke`: `"H 0 * * 1-6\nH 6 * * 7"`
- `trigger_string_navmesh`: `"H 0 * * 1-6\nH 6 * * 7"`
- `trigger_string_patchdata`: `"H/5 * * * 1-6\nH/5 6-23 * * 7"`
- `trigger_string_shift`: `"TZ=Europe/Stockholm \n H 0,13 * * 1-6\nH 6,13 * * 7"`
- `trigger_string_shift_offsite_drone`: `"TZ=Europe/Stockholm \n H 0,13 * * 1-6\nH 6,13 * * 7"`
- `trigger_type_code`: `"scm"` - Any value other than `scm` or `cron` will effectively disable the job
- `trigger_type_code_drone`: `"scm"` - Any value other than `scm` or `cron` will effectively disable the job
- `trigger_type_code_nomaster`: `"scm"` - Any value other than `scm` or `cron` will effectively disable the job
- `trigger_type_data`: `"scm"` - Any value other than `scm` or `cron` will effectively disable the job
- `trigger_type_icepick_fs`: `"none"` - Any value other than `scm` or `cron` will effectively disable the job
- `trigger_type_icepick_smoke`: `"none"` - Any value other than `scm` or `cron` will effectively disable the job
- `trigger_type_navmesh`: `"scm"` - Any value other than `scm` or `cron` will effectively disable the job
- `trigger_type_patchdata`: `"scm"` - Any value other than `scm` or `cron` will effectively disable the job
- `trigger_type_shift`: `"cron"` - Any value other than `scm` or `cron` will effectively disable the job
- `webexport_asset`: `asset`

Non-required settings without default values:

- `clean_build_cadence` - takes an integer, specify after how many builds a `clean_build` should run within a job. Can
  be set in project-level settings or branch-level settings.
- `clean_build_validation_job_label` - job label used for clean databuild validation job.
- `clean_data_validation_pipeline_args` - extra pipeline args for the clean data validation job.
- `content_layers` - a list of content layers to cook.
- `datasnooper_dbmanifest_path` - path to `dbmanifest` relative from `GAME_ROOT` (related to `datasnooper`).
- `drone_outsourcers` - a list of outsourcers that need a stripped version of drone builds for a branch.
- `elipy_call_new_location` - project `elipy` call, e.g., `${elipy_setup_call} && elipy --location criterion`.
- `elipy_shift_submission_tool` - tool used for submitting shifts in `elipy`.
- `extra_code_drone_args` - additional arguments for code drone jobs.
- `extra_code_args` - a list of strings that are appended to the `elipy` command for code jobs.
- `extra_data_args` - a list of strings that are appended to the `elipy` command for data jobs.
- `extra_data_path` - used for the Media team's branches; this value is the parent stream.
- `extra_frosty_args` - additional arguments for Frosty jobs.
- `extra_icepick_args` - additional arguments for Icepick jobs.
- `extra_navmesh_args` - additional arguments for Navmesh jobs.
- `extra_offsite_args` - additional arguments for offsite jobs.
- `extra_patchdata_args` - additional arguments for patch data jobs.
- `extra_patchfrosty_args` - additional arguments for patch Frosty jobs.
- `extra_shift_args` - additional arguments for Shift jobs.
- `extra_webexport_args` - additional arguments for web export jobs.
- `fb_env_values_code` - list of strings to pass as Frostbite environment values during `buildsln` and `gensln`.
- `fb_env_values_data` - list of strings to pass as Frostbite environment values during `cook`.
- `frosty_quick_job` - reference to a quicker Frosty job from which a slower Frosty job will try to fetch CL.
- `fetch_baseline_reference_job` - set to the `store_regular_baseline.start` reference job to fetch baseline CLs from.
- `icepick_extra_framework_args` - extra arguments for Icepick to pass to any Framework commands it starts. May be
  overridden by an Autotest test category.
- `link_time_code_generation` - set to `true` or `false` if you want to override the project setting located in
  Perforce.
- `new_location` - location as per the `elipy` project `.yml` file.
- `non_virtual_data_branch` - for most virtual streams, this is the stream that is treated as the main stream.
- `offsite_code_token` - token used for offsite job in `offsite_job_remote`.
- `offsite_job_remote` - remote url for offsite job to trigger.
- `remote_masters_to_receive_changelists` - a list of remote Jenkins masters that will receive code/data changelist
  information after a successful data job.
- `shift_retention_policy` - set a Shift retention policy for builds from the stream, if we want something else than the
  default `SpaceAvailable`. One common choice is `UntilDelete`, which in Shift ends up as `Until Archived`.
- `use_deprecated_blox_packages` - set to `true` to use deprecated Blox packages.
- `skip_standalone_patchdata` - patch data can be created as standalone or used for combined bundles. Setting this to
  `true` skips creating a standalone patch bundle.
- `combine_settings_file` - override the default Avalanche combine settings file used in frosty

### Preflight settings

Settings with default value `false`:

- `p4_compile` - set to `true` to use the Elipy flag `--p4-compile` for code preflights.
- `pre_preflight` - set to `true` to run a preflight if a node is idle for a set amount of time
- `use_icepick_test` - set to `true` to enable icepick test on preflights
- `use_last_known_good_code_cl`
- `enable_custom_cl` - set to `true` to allow custom changelists to be used in builds.

Settings with default value `true`:

- `statebuild_codepreflight_ps4` - set to `false` to use specific build machines for this job type.
- `statebuild_datapreflight` - set to `false` to use specific build machines for this job type.

Settings with other default values:

- `job_label_statebuild`: `"statebuild"` - set another value here to build a branch on machines dedicated to that
  branch,
  but one pool for all jobs on that branch.
- `codepreflight_reference_job`: `branch_name + ".code.start"`
- `datapreflight_reference_job`: `branch_name + ".data.start"`
- `slack_channel_preflight` `[channels: [], always_notify: false, skip_for_multiple_failures: false]` - Slack channel(s)
  to report failures for preflights.
- `prepreflight_idle_length`: number of minutes a node is idle before triggering `pre_preflight`. Default is 45

Non-required settings without default values:

- `concurrent_code`: max number of concurrent builds for code-preflight, pre-preflight and post-preflight. If not set,
  use '6' (on-prem solution we wont have more than 6 VM per each job)
- `concurrent_data`: max number of concurrent builds for data-preflight. If not set, use '6' (on-prem solution we wont
  have more than 6 VM per each job)
- `content_layers_preflight` - a list of content layers to cook on preflights
- `extra_codepreflight_args`
- `extra_datapreflight_args`:
  `[--cook-dbx-assets for cook extra assets based on .dbx file; --clean-master-version-check to run clean on master version update]`
- `max_builds_tokeep`: set rotation policy, max number of builds to keep. if not set default number is 100
- `timeout_hours_codepreflight`: default to 2hrs, can be set to overwrite per branch only for code preflight
- `timeout_hours_datapreflight`: default to 4hrs, can be set to overwrite per branch only for data preflight
- `timeout_hours_dataprepreflight`: default to 2hrs, can be set to overwrite per branch only for data preflight
- `timeout_hours_postpreflight`: default to 3hrs, can be set to overwrite per branch for both code and data preflight
- `timeout_hours_frosty`: default to 3hrs, can be set to overwrite per branch for frosty
- `timeout_hours_frosty_patchfrosty`: default to 4hrs, can be set to overwrite per branch for patchfrosty

## Matrix settings

- `bilbo_move_matrix` - if we move all codebuilds when a `code.start` job is finished, this is not needed. If we only
  want to move a subset of codebuilds, we specify which ones to move using this matrix
- `code_matrix` - list of objects containing `platform`, `config`, `region`, `format` and any extra `args` for code
  build
  jobs
- `code_nomaster_matrix` - list of objects containing `platform`, `config`, `region`, `format` and any extra `args` for
  no-master code build jobs
- `code_stressbulkbuild_matrix` - list of objects containing `platform` and `configs` for running stressbulkbuilds code
  build jobs
- `code_downstream_matrix` - a list of job names to trigger after a successful code build
- `data_matrix` - list of strings stating which data `platform`s to cook
- `data_downstream_matrix` - a list of job names to trigger after a successful data build
- `patchdata_matrix` - list of strings stating which data `platform`s to cook and generate ddeltas
- `frosty_matrix` - list of objects containing `platform`, `config`, `region`, `format` and any extra `args` for frosty
  build jobs
- `frosty_downstream_matrix` - a list of job names to trigger after a successful frosty build
- `frosty_for_patch_matrix` - same as `frosty_matrix` but triggered by the `patchfrosty.start` job
- `patchfrosty_matrix` - list of objects containing `platform`, `config`, `region`, `format` and any extra `args` for
  frosty patch build jobs
- `code_preflight_matrix` - list of code `platforms` and `configs` to build on code preflights, use `sync_code_and_data`
  to determine if sync both code and data. Use `nomaster_platform` to set a platform as nomaster
- `data_preflight_matrix` - list of data `platforms` and `levels` to cook on data preflights
- `shift_upload_matrix` - used by seed job to create shift upload jobs for the branch. Requires a shifter type (see
  `elipy2/shifters.py`) and the args needed to run the shift job.
- `freestyle_job_trigger_matrix` - a list of triggering relationships between freestyle jobs. The `downstream_job` is
  triggered_by the `upstream_job` with `args` being passed to the downstream job. Must be called at an appropriate
  moment of the chain of commands in the seed job e.g.
  `LibCommonCps.add_downstream_freestyle_job_triggers(bilbo_drone_job, current_branch, branchfile, '${code_changelist}')`.
- `spin_upload_matrix` -list of objects containing `platform`, `config`, `region`, `format` which define builds to
  upload to Spin's S3 bucket
- `shift_downstream_matrix` - a list of job names to trigger after a successful shift upload
- `azure_uploads_matrix` - defines the matrix for Azure-specific upload jobs. This is typically used for cloud-based
  builds.
- `pipeline_determinism_test_matrix` - defines the matrix for pipeline-terminism tests
    - list keys: `platform` (req), `job_label` (opt), `additional_script_args` (opt)

## Misc

- To run test locally on Windows:

  ```cmd
  $GIT_ROOT > misc/dockerbuild.cmd
  ```

- To update docs:

  ```cmd
  $GIT_ROOT> misc/groovydoc.sh
  ```

- To access docs

  <https://dre-cobra.gitlab.ea.com/dst-ci-configuration/>

## Create and run unit tests

- **Create unit test:** Have a look at ``` $GIT_ROOT> src/test/groovy/SampleSpec.groovy ``` and create your own unit
  tests in a new file in the same directory
- **Run the unit tests:** Push your tests to Gitlab and the unit tests will run automatically by the Gitlab pipeline

The [Spock Framework](https://spockframework.org/spock/docs/2.0/index.html) is the framework of choice for testing
dst-ci-configuration's Groovy codebase.
