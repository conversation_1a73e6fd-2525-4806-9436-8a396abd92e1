"""
avalanchecli_drop_all_dbs.py
"""
import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import avalanche
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command("avalanchecli_drop_all_dbs", short_help="Drop all avalanche databases")
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(_):  # pylint: disable=unused-argument
    """
    Perform Avalanchecli nuke -y on the localhost.
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    avalanche.drop_all_dbs()
