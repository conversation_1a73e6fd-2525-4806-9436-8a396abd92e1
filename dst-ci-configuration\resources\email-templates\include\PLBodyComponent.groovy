import groovy.xml.MarkupBuilder
import hudson.model.Run

public class PLBodyComponent implements IEmailComponent {

    private List<IEmailComponent> registeredComponents = []

    public PLBodyComponent(List<IEmailComponent> registeredComponents) {
        this.registeredComponents.addAll(registeredComponents)
    }


    private Map gather(Run run) {

        def fullDisplayName = run.getFullDisplayName()
        def result = run.getResult()?.toString()

        return [
            FullDisplayName: fullDisplayName,
            Result         : result,
        ]
    }


    public void render(Run run, MarkupBuilder builder) {
        def data = gather(run)
        if (data) {
            builder.body() {
                // hidden text
                div(class: "hidden-text") {
                    mkp.yield("BUILD ${data.Result}")
                }

                // each body component renders a row in the table
                table(border: "0", cellpadding: "0", cellspacing: "0", width: "100%") {
                    registeredComponents.each { component ->
                        if (component.isApplicable(run)) {
                            component.render(run, builder)
                        }
                    }
                }
            }
        }
    }


    public boolean isApplicable(Run run) {
        return true
    }


    public String getEmbeddedStyle(Run run) {
        return null
    }
}
