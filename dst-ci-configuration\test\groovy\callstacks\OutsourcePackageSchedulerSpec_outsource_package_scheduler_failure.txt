   outsource_package_scheduler.run()
      outsource_package_scheduler.ProjectClass(Bct)
      outsource_package_scheduler.pipeline(groovy.lang.Closure)
         outsource_package_scheduler.allowBrokenBuildClaiming()
         outsource_package_scheduler.timestamps()
         outsource_package_scheduler.echo(Executing on agent [label:any])
         outsource_package_scheduler.stage(Trigger outsource package job., groovy.lang.Closure)
            outsource_package_scheduler.script(groovy.lang.Closure)
               GetBranchFile.get_branchfile(Bct, build-outsourcer-code)
               LibJenkins.getLastStableCodeChangelist(build-main.code.start)
               LibJenkins.getLastStableCodeChangelist(build-outsourcer-code.outsource-package.start)
               outsource_package_scheduler.echo(No new changelist to generate outsource package from, aborting.)
