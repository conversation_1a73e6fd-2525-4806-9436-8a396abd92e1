"""
custom_script.py
"""
import os

import click

from elipy2 import LOGGER, frostbite_core, core
from elipy2.cli import pass_context
from elipy2.exceptions import ELIPYException
from elipy2.telemetry import collect_metrics
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from dice_elipy_scripts.utils.env_utils import env_string_to_dict


@click.command(
    "custom_script",
    short_help="Runs a script.",
    context_settings=dict(ignore_unknown_options=True),
)
@click.option(
    "--executable",
    type=str,
    required=True,
    help="The executable to run the script",
)
@click.option(
    "--executable-args",
    type=str,
    default="",
    help="Optional arguments to pass to the command. " "Pass None for no args.",
)
@click.option(
    "--script-path",
    type=str,
    required=True,
    help="The relative script path from TnT",
)
@click.option(
    "--script-args",
    type=str,
    default="",
    help="Optional arguments to pass to the command.",
)
@click.option(
    "--env-variables",
    type=str,
    default="",
    help="Comma seperated env variable to add. Example: VAR1=FOO,VAR2=BAR",
)
@pass_context
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    executable,
    executable_args,
    script_path,
    script_args,
    env_variables,
):
    """
    Runs a script.

    Provided script path must be relative to TnT root.
    """
    LOGGER.info("Running custom_script...")

    # adding sentry tags
    add_sentry_tags(__file__, "custom_script")

    script_path = os.path.join(frostbite_core.get_tnt_root(), script_path)
    if not os.path.exists(script_path):
        raise ELIPYException("Can not find script path: {}".format(script_path))

    env_vars = {}
    if env_variables:
        env_vars = env_string_to_dict(env_variables)

    command = [
        executable,
        *executable_args.split(),
        script_path,
        *script_args.split(),
    ]
    core.run(command, print_std_out=True, env_patch=env_vars)
    LOGGER.info("Done")
