"""
unittests.py
"""
import os

import click

from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.frosty_build_utils import authenticate_eapm_credstore
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import running_processes, LOGGER, core, frostbite_core
from elipy2.cli import pass_context
from elipy2.exceptions import ELIPYException
from elipy2.telemetry import collect_metrics


@click.command(
    "unittests",
    short_help="Runs unit tests.",
    context_settings=dict(ignore_unknown_options=True),
)
@click.option("--password", required=True)
@click.option("--email", required=True)
@click.option(
    "--domain-user",
    default=None,
    help="The user to authenticate to package server as DOMAIN\\user",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    password,
    email,
    domain_user,
):
    """
    Run unit tests
    """
    add_sentry_tags(__file__, "unittests")
    running_processes.kill()
    LOGGER.info("Running unicron.py")

    if password and email:
        authenticate_eapm_credstore(password, email, domain_user)

    script_path = os.path.join(frostbite_core.get_tnt_root(), "Bin", "unicron", "unicron.py")
    exit_code, stdout, stderr = core.run(["python", script_path], allow_non_zero_exit_code=True)
    LOGGER.info("\n".join(stdout))
    if exit_code != 0:
        LOGGER.error("Exit Code %s", exit_code)
        LOGGER.error("Error Message %s", stderr)
        raise ELIPYException("unicron.py Failed")
    LOGGER.info("Done.")
