"""
test_data_upgrade_integration.py

Unit testing for data_upgrade_integration
"""
import unittest
import os
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from dice_elipy_scripts.data_upgrade_integration import cli
from mock import MagicMock, patch


@patch("os.path.join", MagicMock(side_effect=lambda *args: "\\".join(args)))
@patch("elipy2.running_processes.kill", MagicMock())
@patch("dice_elipy_scripts.data_upgrade_integration.add_sentry_tags", MagicMock())
@patch("dice_elipy_scripts.data_upgrade_integration.throw_if_files_found", MagicMock())
class TestDataUpgradeIntegration(unittest.TestCase):
    OPTION_PORT = "--port"
    OPTION_CLIENT = "--client"
    OPTION_CHANGELIST = "--changelist"
    OPTION_CODE_CHANGELIST = "--code-changelist"
    OPTION_DATA_CHANGELIST = "--data-changelist"
    OPTION_NO_SUBMIT = "--no-submit"
    OPTION_LAST_DATA_CHANGELIST = "--last-data-changelist"
    OPTION_LICENSEE = "--licensee"
    OPTION_P4_PATH_SOURCE = "--p4-path-source"
    OPTION_SCRIPT_PATH = "--script-path"
    OPTION_REVERT_BRANCHID_FILE = "--revert-branchid-file"
    OPTION_CLEAN = "--clean"

    VALUE_PORT = "p4_port"
    VALUE_CLIENT = "p4_client"
    VALUE_CHANGELIST = "6543"
    VALUE_CODE_CHANGELIST = "1234"
    VALUE_DATA_CHANGELIST = "5678"
    VALUE_LAST_DATA_CHANGELIST = "5432"
    VALUE_LICENSEE_1 = "licensee_1"
    VALUE_LICENSEE_2 = "licensee_2"
    VALUE_P4_PATH_SOURCE = "//depot/path/source"
    VALUE_SCRIPT_PATH = "script_path"
    VALUE_CLEAN = "true"

    BASIC_ARGS = [
        OPTION_PORT,
        VALUE_PORT,
        OPTION_CLIENT,
        VALUE_CLIENT,
        OPTION_CODE_CHANGELIST,
        VALUE_CODE_CHANGELIST,
        OPTION_DATA_CHANGELIST,
        VALUE_DATA_CHANGELIST,
        OPTION_LICENSEE,
        VALUE_LICENSEE_1,
        OPTION_SCRIPT_PATH,
        VALUE_SCRIPT_PATH,
    ]

    def setUp(self):
        self.patcher_set_datadir = patch("elipy2.data.DataUtils.set_datadir")
        self.mock_set_datadir = self.patcher_set_datadir.start()

        self.patcher_run_frostbite_data_upgrade = patch(
            "elipy2.data.DataUtils.run_frostbite_data_upgrade"
        )
        self.mock_run_frostbite_data_upgrade = self.patcher_run_frostbite_data_upgrade.start()

        self.patcher_codeutils = patch("elipy2.code.CodeUtils")
        self.mock_codeutils = self.patcher_codeutils.start()
        self.mock_codeutils.return_value = MagicMock()

        self.patcher_p4utils = patch("elipy2.p4.P4Utils")
        self.mock_p4utils = self.patcher_p4utils.start()
        self.mock_p4utils.return_value = MagicMock()
        self.mock_p4utils.return_value.changes_range.return_value = [5656, 5678]

        self.patcher_ensure_p4_config = patch("elipy2.core.ensure_p4_config")
        self.mock_ensure_p4_config = self.patcher_ensure_p4_config.start()

        self.patcher_delete_folder = patch("elipy2.core.delete_folder")
        self.mock_delete_folder = self.patcher_delete_folder.start()

        self.patcher_get_game_data_dir = patch("elipy2.frostbite_core.get_game_data_dir")
        self.mock_get_game_data_dir = self.patcher_get_game_data_dir.start()
        self.mock_get_game_data_dir.return_value = "game_data_dir"

        self.patcher_get_tnt_local_path = patch("elipy2.local_paths.get_tnt_local_path")
        self.mock_get_tnt_local_path = self.patcher_get_tnt_local_path.start()
        self.mock_get_tnt_local_path.return_value = "tnt_local"

        self.patcher_compile_code = patch(
            "dice_elipy_scripts.data_upgrade_integration.compile_code"
        )
        self.mock_compile_code = self.patcher_compile_code.start()

        self.patcher_submit_integration = patch(
            "dice_elipy_scripts.data_upgrade_integration.submit_integration"
        )
        self.mock_submit_integration = self.patcher_submit_integration.start()

    def tearDown(self):
        patch.stopall()

    def test_basic_run(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        assert self.mock_p4utils.return_value.revert.call_count == 2

    def test_compile_code_default(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_compile_code.assert_called_once_with(
            licensee=[self.VALUE_LICENSEE_1],
            password=None,
            email=None,
            domain_user=None,
            port=self.VALUE_PORT,
            user=None,
            client=self.VALUE_CLIENT,
            clean=False,
        )

    def test_compile_code_clean(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_CLEAN, self.VALUE_CLEAN])
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_compile_code.assert_called_once_with(
            licensee=[self.VALUE_LICENSEE_1],
            password=None,
            email=None,
            domain_user=None,
            port=self.VALUE_PORT,
            user=None,
            client=self.VALUE_CLIENT,
            clean=True,
        )

    def test_run_upgrade_single_licensee(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_run_frostbite_data_upgrade.assert_called_once_with(
            source_game_data_dir=os.path.join("game_root", "SourceData"),
            dest_game_data_dir="game_data_dir",
            licensee=self.VALUE_LICENSEE_1,
            scripts_path="script_path",
        )

    def test_run_upgrade_multiple_licensees(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_LICENSEE, self.VALUE_LICENSEE_2])
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_run_frostbite_data_upgrade.assert_called_once_with(
            source_game_data_dir=os.path.join("game_root", "SourceData"),
            dest_game_data_dir="game_data_dir",
            licensee=self.VALUE_LICENSEE_1,
            scripts_path="script_path",
        )

    def test_run_upgrade_exception(self):
        self.mock_run_frostbite_data_upgrade.side_effect = Exception()
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 1
        self.mock_p4utils.return_value.clean.assert_called_once_with(folder="game_data_dir/...")
        self.mock_delete_folder.assert_called_once_with("tnt_local")

    def test_submit(self):
        fdu_message = f"Upgraded Data from CL#{self.VALUE_DATA_CHANGELIST} to Code CL#{self.VALUE_CODE_CHANGELIST}."
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert result.exit_code == 0
        self.mock_submit_integration.assert_called_once_with(
            p4_object=self.mock_p4utils.return_value,
            submit_message=fdu_message,
            submit=True,
            data_upgrade=True,
            revert_branchid_file=False,
        )
        assert self.mock_p4utils.return_value.changes_range.call_count == 0

    @patch.dict(os.environ, {"BUILD_URL": "jenkins-url.fake"})
    def test_submit_revert_branchid_file(self):
        fdu_message = f"Upgraded Data from CL#{self.VALUE_DATA_CHANGELIST} to Code CL#{self.VALUE_CODE_CHANGELIST}."
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_REVERT_BRANCHID_FILE])
        assert result.exit_code == 0
        self.mock_submit_integration.assert_called_once_with(
            p4_object=self.mock_p4utils.return_value,
            submit_message=fdu_message,
            submit=True,
            data_upgrade=True,
            revert_branchid_file=True,
        )

    def test_submit_changes_range_default(self):
        fdu_message = f"Upgraded Data from CL#[5656, 5678] to Code CL#{self.VALUE_CODE_CHANGELIST}."
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_LAST_DATA_CHANGELIST,
                self.VALUE_LAST_DATA_CHANGELIST,
                self.OPTION_P4_PATH_SOURCE,
                self.VALUE_P4_PATH_SOURCE,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_p4utils.return_value.changes_range.assert_called_once_with(
            self.VALUE_P4_PATH_SOURCE,
            self.VALUE_LAST_DATA_CHANGELIST,
            changelist_end=self.VALUE_DATA_CHANGELIST,
        )
        self.mock_submit_integration.assert_called_once_with(
            p4_object=self.mock_p4utils.return_value,
            submit_message=fdu_message,
            submit=True,
            data_upgrade=True,
            revert_branchid_file=False,
        )

    def test_submit_changes_range_empty_string_last_changelist(self):
        fdu_message = f"Upgraded Data from CL#{self.VALUE_DATA_CHANGELIST} to Code CL#{self.VALUE_CODE_CHANGELIST}."
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_LAST_DATA_CHANGELIST,
                "",
                self.OPTION_P4_PATH_SOURCE,
                self.VALUE_P4_PATH_SOURCE,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        assert self.mock_p4utils.return_value.changes_range.call_count == 0
        self.mock_submit_integration.assert_called_once_with(
            p4_object=self.mock_p4utils.return_value,
            submit_message=fdu_message,
            submit=True,
            data_upgrade=True,
            revert_branchid_file=False,
        )

    def test_no_submit(self):
        fdu_message = f"Upgraded Data from CL#{self.VALUE_DATA_CHANGELIST} to Code CL#{self.VALUE_CODE_CHANGELIST}."
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_NO_SUBMIT])
        assert result.exit_code == 0
        self.mock_submit_integration.assert_called_once_with(
            p4_object=self.mock_p4utils.return_value,
            submit_message=fdu_message,
            submit=False,
            data_upgrade=True,
            revert_branchid_file=False,
        )

    def test_sync_data_changelist(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert result.exit_code == 0
        self.mock_p4utils.return_value.sync.assert_called_with(
            path="game_root\\SourceData/...", to_revision=self.VALUE_DATA_CHANGELIST
        )

    def test_sync_legacy_data_changelist(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.OPTION_PORT,
                self.VALUE_PORT,
                self.OPTION_CLIENT,
                self.VALUE_CLIENT,
                self.OPTION_CODE_CHANGELIST,
                self.VALUE_CODE_CHANGELIST,
                self.OPTION_CHANGELIST,
                self.VALUE_CHANGELIST,
                self.OPTION_LICENSEE,
                self.VALUE_LICENSEE_1,
                self.OPTION_SCRIPT_PATH,
                self.VALUE_SCRIPT_PATH,
            ],
        )
        assert result.exit_code == 0
        self.mock_p4utils.return_value.sync.assert_called_with(
            path="game_root\\SourceData/...", to_revision=self.VALUE_CHANGELIST
        )

    def test_sync_no_data_changelist(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.OPTION_PORT,
                self.VALUE_PORT,
                self.OPTION_CLIENT,
                self.VALUE_CLIENT,
                self.OPTION_CODE_CHANGELIST,
                self.VALUE_CODE_CHANGELIST,
                self.OPTION_LICENSEE,
                self.VALUE_LICENSEE_1,
                self.OPTION_SCRIPT_PATH,
                self.VALUE_SCRIPT_PATH,
            ],
        )
        assert result.exit_code == 1
