package com.ea.lib.jobs

import com.ea.lib.LibJobDsl
import com.ea.lib.jobsettings.CodeCoverageSettings

class LibCodeCoverage {
    /**
     * Add generic job for autotest codecoverage jobs.
     */
    static void codecoverage_start(def job, def project, def branchFile, def masterFile, String branchName) {
        job.with {
            CodeCoverageSettings codeCoverageSettings = new CodeCoverageSettings()
            codeCoverageSettings.initializeCodeCoverageStart(branchFile, masterFile, project, branchName)
            description(codeCoverageSettings.description)
            disabled(codeCoverageSettings.isDisabled)
            logRotator(29, 20) // Keep 29 days no more than 20 logs
            quietPeriod(0)
            properties {
                disableConcurrentBuilds()
                disableResume()
                pipelineTriggers {
                    triggers {
                        cron {
                            spec(codeCoverageSettings.cronTrigger)
                        }
                    }
                }
            }
            environmentVariables { // Set env for codecoverage.grooy to consume
                env('codecoverage_ref_job', codeCoverageSettings.refJob)
                env('project_name', codeCoverageSettings.projectName)
                env('branch_name', codeCoverageSettings.branchName)
                env('code_folder', codeCoverageSettings.codeFolder)
                env('code_branch', codeCoverageSettings.codeBranch)
                env('non_virtual_code_branch', codeCoverageSettings.nonVirtualCodeBranch)
                env('non_virtual_code_folder', codeCoverageSettings.nonVirtualCodeFolder)
            }
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('input code_changelist')
                    trim(true)
                }
            }
        }
    }

    static void run_codecoverage(def job, def project, def branchFile, def masterFile, String branchName) {
        job.with {
            CodeCoverageSettings codeCoverageSettings = new CodeCoverageSettings()
            codeCoverageSettings.initializeCodeCoverageRun(branchFile, masterFile, project, branchName)
            // Jenkins Config
            description(codeCoverageSettings.description)
            logRotator(29, 20) // Keep 29 days no more than 20 logs
            quietPeriod(0)
            label(codeCoverageSettings.jobLabel)
            properties {
                disableConcurrentBuilds()
                disableResume()
            }
            customWorkspace(codeCoverageSettings.workspaceRoot)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('input code changelist')
                    trim(true)
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(codeCoverageSettings.buildName)
                timeout {
                    absolute(120) // Hardcode for 2 hours to timeout job
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, codeCoverageSettings.elipyInstallCall, project)
                batchFile(codeCoverageSettings.elipyCmd)
            }
        }
    }
}
