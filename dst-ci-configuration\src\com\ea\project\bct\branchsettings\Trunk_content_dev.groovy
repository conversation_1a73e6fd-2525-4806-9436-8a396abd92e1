package com.ea.project.bct.branchsettings

import com.ea.lib.jobsettings.ShiftSettings
import com.ea.project.bct.Bct

class Trunk_content_dev {
    // Settings for jobs
    static Class project = Bct
    static Map general_settings = [
        dataset                 : project.dataset,
        elipy_call              : project.elipy_call + ' --use-fbenv-core',
        elipy_install_call      : project.elipy_install_call,
        frostbite_licensee      : project.frostbite_licensee,
        workspace_root          : project.workspace_root,
        azure_elipy_call        : project.azure_elipy_call,
        azure_elipy_install_call: project.azure_elipy_install_call,
        azure_workspace_root    : project.azure_workspace_root,
        azure_fileshare         : [
            secret_context    : 'glacier_azure_fileshare',
            target_build_share: 'bfglacier',
        ],
        job_label_statebuild    : 'statebuild',
        webexport_script_path   : 'Code\\DICE\\BattlefieldGame\\fbcli\\webexport.py',
        autotest_remote_settings: [
            eala: [
                p4_code_creds : 'bct-la-p4',
                p4_code_server: 'dicela-p4edge-fb.la.ad.ea.com:2001',
            ],
            dice: [
                p4_code_creds : 'perforce-battlefield01',
                p4_code_server: 'dice-p4buildedge02-fb.dice.ad.ea.com:2001',
            ]
        ]
    ]
    static Map code_settings = [
        fake_ooa_wrapped_symbol      : false,
        skip_code_build_if_no_changes: true,
        slack_channel_code           : [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        statebuild_code              : false,
        sndbs_enabled                : true,
    ]
    static Map data_settings = [
        deployment_data_branch     : true,
        enable_daily_data_clean    : true,
        enable_lkg_cleaning        : true,
        poolbuild_data             : true,
        slack_channel_data         : [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        statebuild_data            : false,
        statebuild_webexport       : false,
        timeout_hours_data         : 8,
        webexport_allow_failure    : true,
        webexport_branch           : true,
        timeout_hours_enlighten_job: 12,
    ]
    static Map frosty_settings = [
        frosty_reference_job: 'trunk-content-dev.deployment-data.start',
        poolbuild_frosty    : true,
        slack_channel_frosty: [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        use_linuxclient     : true,
    ]
    static Map standard_jobs_settings = code_settings + data_settings + frosty_settings + [
        asset                             : 'DevLevels',
        bilbo_store_offsite               : true,
        enable_custom_cl_preflight_code   : true,
        deployment_data_reference_job     : 'trunk-content-dev.data.start',
        drone_outsourcers                 : ['PlanA', 'Jukebox'],
        enable_lkg_p4_counters            : true,
        environment_variables             : ['ASAN_WIN_CONTINUE_ON_INTERCEPTION_FAILURE': 1,],
        extra_data_args                   : ['--pipeline-args -Pipeline.MaxConcurrency --pipeline-args 12 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        extra_frosty_args                 : ['--pipeline-args -Pipeline.MaxConcurrency --pipeline-args 12 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        import_avalanche_autotest         : false,
        offsite_basic_drone_zip_builds    : true,
        offsite_code_token                : '<EMAIL>:1180acfb166f9612424e10e603d75acc0e',
        offsite_drone_basic_builds        : true,
        offsite_drone_builds              : true,
        offsite_job_remote                : 'http://dice-la-jenkins-tools.la.ad.ea.com:8080/job/GetNewDrone_trunk-content-dev/buildWithParameters?token=2Zm67RaPGVd6^&code_changelist=%code_changelist%^&cause=%BUILD_URL%^&share_root=\\\\filer.dice.ad.ea.com\\Builds\\Battlefield\\code\\trunk-content-dev',
        quickscope_db                     : 'kinpipeline',
        quickscope_import                 : true,
        remote_masters_to_receive_code    : [[name: 'bct-preflight-jenkins.cobra.dre.ea.com', allow_failure: false]],
        remote_masters_to_receive_data    : [[name: 'bct-preflight-jenkins.cobra.dre.ea.com', allow_failure: false]],
        reshift_offsitedrone              : true,
        server_asset                      : 'Game/Setup/Build/DevMPLevels',
        shift_branch                      : true,
        shift_reference_job               : 'trunk-content-dev.frosty.start',
        single_stream_smoke               : true,
        skip_icepick_settings_file        : true,
        strip_symbols                     : false,
        trigger_string_shift              : 'TZ=Europe/Stockholm \n H 1,14 * * 1-6\nH 6,13 * * 7',
        trigger_string_shift_offsite_drone: 'TZ=Europe/London \n H 16 * * 1-6\nH 16 * * 7',
        trigger_type_shift                : 'cron',
        upgrade_data_job                  : true,
        use_deprecated_blox_packages      : true,
        move_location_parallel            : true,
        new_locations                     : [
            earo     : [
                elipy_call_new_location: project.elipy_call_earo + ' --use-fbenv-core',
            ],
            Guildford: [
                elipy_call_new_location: project.elipy_call_criterion + ' --use-fbenv-core',
            ],
            Montreal : [
                elipy_call_new_location: project.elipy_call_montreal + ' --use-fbenv-core',
            ],
        ],
        shift_subscription_matrix         : [
            [
                dest_location : 'Guildford',
                build_type    : 'code',
                job_label     : '24_core && criterion',
                p4_code_creds : 'perforce-battlefield-criterion',
                p4_code_server: 'oh-p4edge-fb.eu.ad.ea.com:2001',
                p4_data_server: 'oh-p4edge-fb.eu.ad.ea.com:2001',
                src_location  : 'DiceStockholm',
            ]
        ],
    ]
    static Map icepick_settings = [
        ignore_icepick_exit_code: false
    ]
    static Map preflight_settings = [
        concurrent_code                  : 1,
        concurrent_data                  : 1,
        codepreflight_reference_job      : 'trunk-content-dev.code.lastknowngood',
        datapreflight_reference_job      : 'trunk-content-dev.data.lastknowngood',
        extra_codepreflight_args         : "--framework-args -D:ondemandp4proxymapfile=${general_settings.workspace_root}\\tnt\\build\\framework\\data\\P4ProtocolOptimalProxyMap.xml  --framework-args -D:eaconfig.optimization.ltcg=off",
        extra_datapreflight_args         : ' --pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true ',
        force_rebuild_preflight          : true,
        slack_channel_preflight          : [channels: ['#cobra-build-preflight']],
        statebuild_codepreflight         : false,
        statebuild_datapreflight         : false,
        use_icepick_test                 : true,
        timeout_hours_datapreflight      : 12,
        trigger_type                     : 'cron',
        trigger_string_pre_preflight_data: 'H * * * 1-5\nH 6-23 * * 6-7',
        prepreflight_idle_length         : '60', //minutes idling before to run pre-preflight.
        pre_preflight                    : true,
        trigger_ado                      : true,
        ado_organization                 : 'ea-battlefield',
        ado_project                      : 'BCT-Staging',
        ado_data_preflight_pipeline_id   : '2',
        validate_direct_references       : true,
    ]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'win64game', configs: ['final', 'release', 'performance']],
        [name: 'tool', configs: ['release']],
        [name: 'win64server', configs: ['final', 'release']],
        [name: 'xbsx', configs: ['final', 'release', 'performance']],
        [name: 'ps5', configs: ['final', 'release', 'performance']],
        [name: 'linux64server', configs: ['final']],
        [name: 'linux64', configs: ['final']],
    ]
    static List bilbo_move_matrix = [
        [name: 'win64game', configs: ['final', 'release', 'performance']],
        [name: 'tool', configs: [[name: 'release']]],
        [name: 'win64server', configs: ['final', 'release']],
        [name: 'xbsx', configs: ['final', 'release', 'performance']],
        [name: 'ps5', configs: ['final', 'release', 'performance']],
        [name: 'linux64server', configs: ['final']],
        [name: 'linux64', configs: ['final']],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = [
        [name: '.bfdata.upgrade.data', args: ['code_changelist'], trigger_only_on_new_code: true],
        [name: '.code.lastknowngood', args: ['code_changelist']],
        [name: '.code.p4counterupdater', args: ['code_changelist', 'code_countername']],
        [name: '.data.start', args: []],
        [name: '.register.smoke', args: ['code_changelist']],
    ]
    static List data_matrix = [
        [name: 'win64'],
        [name: 'server'],
        [name: 'xbsx'],
        [name: 'ps5'],
        [name: 'linux64'],
    ]
    static List data_downstream_matrix = [
        [name: '.data.lastknowngood', args: ['code_changelist', 'data_changelist']],
        [name: '.data.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.data.p4cleancounterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.frosty.start', args: []],
        [name: '.code.tool.release.copy-build-to-azure', args: ['code_changelist']],
        [name: '.code.win64game.final.copy-build-to-azure', args: ['code_changelist']],
        [name: '.code.win64server.final.copy-build-to-azure', args: ['code_changelist']],
        [name: '.deployment-data.start', args: []],
        [name : 'https://bct-autotest-jenkins.cobra.dre.ea.com/job/trunk-content-dev.autotest.lkg_checkmate.all.start/',
         creds: 'perforce-battlefield01',
         args : ['code_changelist']],
        [name : 'https://bct-autotest-jenkins.cobra.dre.ea.com/job/trunk-code-dev.autotest.lkg_auto.all.start/',
         creds: 'perforce-battlefield01',
         args : ['code_changelist']],
        [name : 'https://bct-autotest-jenkins.cobra.dre.ea.com/job/trunk-code-dev.autotest.lkg_bootanddeploy.all.start/',
         creds: 'perforce-battlefield01',
         args : ['code_changelist']],
        [name : 'https://bct-autotest-jenkins.cobra.dre.ea.com/job/trunk-code-dev.autotest.lkg_bootanddeploy_tool.all.start/',
         creds: 'perforce-battlefield01',
         args : ['code_changelist']],
        [name : 'https://bct-autotest-jenkins.cobra.dre.ea.com/job/trunk-code-dev.autotest.lkg_qv.all.start/',
         creds: 'perforce-battlefield01',
         args : ['code_changelist']],
        [name : 'https://bct-autotest-jenkins.cobra.dre.ea.com/job/trunk-code-dev.autotest.lkg_qv_win.all.start/',
         creds: 'perforce-battlefield01',
         args : ['code_changelist']],
    ]
    static List patchdata_matrix = []
    static List frosty_matrix = [
        [name: 'win64', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release']]],
        [name: 'ps5', variants: [[format: 'files', config: 'final', region: 'dev', args: ' --additional-configs release']]],
        [name: 'xbsx', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release']]],
        [name: 'server', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'linuxserver', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'linux64', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
    ]
    static List frosty_downstream_matrix = [
        [name: '.frosty.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
    ]
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = [
        [name: 'win64game', configs: ['final']],
        [name: 'xbsx', configs: ['performance']],
        [name: 'ps5', configs: ['retail']],
        [name: 'win64server', configs: ['final']],
        [name: 'linux64server', configs: ['final']],
        [name: 'tool', configs: ['release'], sync_code_and_data: true],
        [name: 'tool', configs: ['release'], nomaster_platform: true, sync_code_and_data: true],
    ]
    static List data_preflight_matrix = [
        [name: 'win64', platform: 'win64', assets: ['PreflightLevels'], extra_label: ''],
        [name: 'server', platform: 'server', assets: ['PreflightLevels'], extra_label: ''],
    ]
    static List spin_upload_matrix = [
        [name: 'linux64', variants: [[format: 'files', config: 'final', region: 'ww']]],
        [name: 'mod-level-tools', variants: [[format: 'na', config: 'na', region: 'na']]],
    ]
    static List shift_upload_matrix = [
        [shifter_type: ShiftSettings.SHIFTER_TYPE_OFFSITE_BASIC_DRONE, args: ['code_changelist']],
        [shifter_type: ShiftSettings.SHIFTER_TYPE_OFFSITE_DRONE, args: ['code_changelist']],
    ]
    static List shift_downstream_matrix = [
        [name: '.spin.linux64.files.final.ww', args: ['code_changelist', 'data_changelist']],
    ]
    static List freestyle_job_trigger_matrix = [
        [upstream_job: '.bilbo.register.local', downstream_job: ".shift.${ShiftSettings.SHIFTER_TYPE_OFFSITE_BASIC_DRONE}.start", args: ['code_changelist']],
        [upstream_job: '.bilbo.register.local', downstream_job: ".shift.${ShiftSettings.SHIFTER_TYPE_OFFSITE_DRONE}.start", args: ['code_changelist']],
        [upstream_job: '.autotest.lkg_checkmate.win64.parallel.job-1', downstream_job: 'https://bct-dev-jenkins.cobra.dre.ea.com/job/trunk-content-dev.integrate-upgrade-to.upgrade.trunk-code-dev.start', creds: 'jenkins-api-token-bct-dev-jenkins', args: ['code_changelist', 'data_changelist']],
    ]
    static List azure_uploads_matrix = [
        [platform: 'tool', content_type: ['code'], config: ['release']],
        [platform: 'win64game', content_type: ['code'], config: ['final']],
        [platform: 'win64server', content_type: ['code'], config: ['final']],
    ]
}
