package com.ea.project.bct.branchsettings

class Trunk_code_dev_clean {
    // Settings for jobs
    static Class project = com.ea.project.bct.Bct
    static Map general_settings = [
        dataset                 : project.dataset,
        elipy_call              : project.elipy_call + ' --use-fbenv-core',
        elipy_install_call      : project.elipy_install_call,
        frostbite_licensee      : project.frostbite_licensee,
        workspace_root          : project.workspace_root,
        azure_elipy_call        : project.azure_elipy_call + ' --use-fbenv-core',
        azure_elipy_install_call: project.azure_elipy_install_call,
        azure_workspace_root    : project.azure_workspace_root,
    ]
    static Map standard_jobs_settings = [
        asset                        : 'DevLevels',
        clean_local                  : true,
        code_reference_job           : 'trunk-code-dev.data.start',
        data_reference_job           : 'trunk-code-dev-clean.code.start',
        extra_data_args              : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        extra_frosty_args            : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        frosty_reference_job         : 'trunk-code-dev-clean.code.start',
        server_asset                 : 'Game/Setup/Build/DevMPLevels',
        skip_code_build_if_no_changes: false,
        skip_icepick_settings_file   : true,
        statebuild_code              : true,
        strip_symbols                : false,
        trigger_string_code          : 'TZ=Europe/Stockholm \n H 4,5 * * 1-6',
        trigger_type_code            : 'cron',
    ]
    static Map icepick_settings = [:]

    static Map preflight_settings = [
        skip_icepick_settings_file : true,
        enable_custom_cl           : true,
        datapreflight_reference_job: 'trunk-code-dev.data.lastknowngood',
        extra_codepreflight_args   : "--framework-args -D:ondemandp4proxymapfile=${general_settings.azure_workspace_root}\\tnt\\build\\framework\\data\\P4ProtocolOptimalProxyMap.xml  --framework-args -D:eaconfig.optimization.ltcg=off",
        force_rebuild_preflight    : true,
        slack_channel_preflight    : [channels: ['#cobra-build-preflight']],
        p4_code_server             : 'ssl:euwest-p4edge-fb.p4one.ea.com:2001',
        p4_code_creds              : 'euwest-p4edge-fb.p4one.ea.com',
        statebuild_codepreflight   : false,
        statebuild_datapreflight   : false,
        use_icepick_test           : true,
        pre_preflight              : true,
        run_on_azure               : true,
        keep_agent                 : false, // Prevent agent to be removed on failed azure builds
    ]

    static List code_matrix = [
        [name: 'win64game', configs: ['release']],
        [name: 'tool', configs: ['release']],
        [name: 'linuxserver', configs: ['release']],
        [name: 'xbsx', configs: ['release']],
        [name: 'ps5', configs: ['release']],
        [name: 'linux64', configs: ['release']],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = []
    static List data_matrix = []
    static List data_downstream_matrix = []
    static List patchdata_matrix = []
    static List frosty_matrix = []
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = []
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
