"""
integrate_upgrade_one_stream.py
"""
import click
import json
import os
from typing import List, Union
from dice_elipy_scripts.utils.dbxmerge import DBXMerge
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.integration_utils import compile_code, cook_data, submit_integration
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import core, data, frostbite_core, local_paths, LOGGER, p4, running_processes
from elipy2.cli import pass_context
from elipy2.exceptions import AutomaticP4MergeResolveException, ELIPYException
from elipy2.frostbite import package_utils
from elipy2.telemetry import collect_metrics


@click.command(
    "integrate_upgrade_one_stream",
    short_help="Copy and/or integrate code, upgrade data locally or with integration.",
)
@click.option("--assets", required=True, multiple=True, help="Assets to use for data cook.")
@click.option(
    "--branch-guardian-rules-file",
    default="default",
    help="File with rules used by the Branch Guardian tool.",
)
@click.option("--changelist", required=True, help="Perforce source changelist.")
@click.option(
    "--code-clean", default="false", help="Delete TnT/Local if --code-clean true is passed."
)
@click.option("--copy-mapping", default=None, help="Mapping for the copy step.")
@click.option(
    "--copy-reverse",
    is_flag=True,
    help="Reverse the direction for the copy step.",
)
@click.option(
    "--data-clean", default="false", help="Clean Avalanche if --data-clean true is passed."
)
@click.option("--data-directory", default="Data", help="Which data directory to use.")
@click.option("--data-directory-source", default="SourceData", help="Which data directory to use.")
@click.option("--data-platform", default="win64", help="Platform to use for data cook.")
@click.option(
    "--domain-user",
    default=None,
    help="The user to authenticate to package server as DOMAIN\\user.",
)
@click.option("--email", default=None, help="User email to authenticate to package server.")
@click.option("--framework-args", multiple=True, help="Framework arguments for gensln.")
@click.option(
    "--ignore-source-history",
    is_flag=True,
    help="Ignore source file history (sets the Perforce integrate flag -Di).",
)
@click.option("--integrate-mapping", default=None, help="Mapping for the integration step.")
@click.option(
    "--integrate-reverse",
    is_flag=True,
    help="Reverse the direction for the integration step.",
)
@click.option(
    "--integration-upgrade-script-path",
    default=None,
    help="Script path for the integration upgrade.",
)
@click.option("--last-changelist", default=None, help="Changelist for last successful build.")
@click.option("--licensee", multiple=True, required=True, help="Frostbite licensee.")
@click.option(
    "--local-upgrade",
    is_flag=True,
    help="Run the data upgrade on locally on the same stream, "
    "instead of a data upgrade integration.",
)
@click.option(
    "--local-upgrade-script-path", default=None, help="Script path for the local upgrade."
)
@click.option("--p4-client", required=True, help="Perforce client/workspace.")
@click.option(
    "--p4-client-branch-guardian-rules-cleanup",
    default=None,
    help="Perforce client/workspace used for the Branch Guardian rules cleanup.",
)
@click.option(
    "--p4-clean-failed-upgrade",
    is_flag=True,
    help="Run a Perforce clean after a failed upgrade step.",
)
@click.option("--p4-path-source", default=None, help="Perforce path for the source.")
@click.option("--p4-path-target", default=None, help="Perforce path for the target.")
@click.option("--p4-port", required=True, help="Perforce port/server.")
@click.option("--p4-user", default=None, help="Perforce user name.")
@click.option(
    "--password",
    default=None,
    help="User credentials to authenticate to package server.",
)
@click.option("--pipeline-args", multiple=True, help="Pipeline arguments for the data cook.")
@click.option("--revert-branchid-file", is_flag=True, help="Revert the BranchId.py file.")
@click.option(
    "--run-branch-guardian",
    default=False,
    help="Use the Branch Guardian tool to handle integration/upgrade.",
)
@click.option(
    "--run-cook",
    default=True,
    help="Run a cook for the integrated/upgraded data.",
)
@click.option(
    "--run-upgrade",
    default=True,
    help="Run an upgrade (local or FDU integration is controlled by --local-upgrade).",
)
@click.option(
    "--shelve-cl",
    default=False,
    help="Shelve a changelist from the integration if we run with --no-submit.",
)
@click.option("--submit/--no-submit", default=True)
@click.option(
    "--use-preview-dotnet-version",
    default=False,
    help="Use another version of Dotnet, listed in the masterconfig file as a preview version.",
)
@click.option(
    "--p4-ignore",
    default=".p4ignore",
    help=".p4ignore file to set, if not will set the default of .p4ignore",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    assets,
    branch_guardian_rules_file,
    changelist,
    code_clean,
    copy_mapping,
    copy_reverse,
    data_clean,
    data_directory,
    data_directory_source,
    data_platform,
    domain_user,
    email,
    framework_args,
    ignore_source_history,
    integrate_mapping,
    integrate_reverse,
    integration_upgrade_script_path,
    last_changelist,
    licensee,
    local_upgrade,
    local_upgrade_script_path,
    p4_client,
    p4_client_branch_guardian_rules_cleanup,
    p4_clean_failed_upgrade,
    p4_path_source,
    p4_path_target,
    p4_port,
    p4_user,
    password,
    pipeline_args,
    revert_branchid_file,
    run_branch_guardian,
    run_cook,
    run_upgrade,
    shelve_cl,
    submit,
    use_preview_dotnet_version,
    p4_ignore,
):
    """
    Copy and/or integrate code, depending on which mappings we include.
    Upgrade data, either locally on the same stream or as a data upgrade integration.
    """
    # Adding sentry tags.
    add_sentry_tags(__file__)

    # Clean the machine.
    running_processes.kill()

    # Set data directory.
    data.DataUtils.set_datadir(data_directory)

    if not copy_mapping and not integrate_mapping:
        raise ELIPYException(
            "No branch mapping specified, "
            "at least one of --copy-mapping and --integrate-mapping required."
        )

    LOGGER.info(
        "Running integration/copy of code and upgrading data, using source CL#%s", changelist
    )

    # Initialize
    perforce = p4.P4Utils(port=p4_port, client=p4_client, user=p4_user)
    perforce.revert()

    try:
        if copy_mapping:
            LOGGER.info(
                "Performing copy using client %s on server %s with branch mapping %s.",
                p4_client,
                p4_port,
                copy_mapping,
            )
            perforce.copy_mapping(
                mapping=copy_mapping, reverse=copy_reverse, to_revision=changelist
            )
        if integrate_mapping:
            LOGGER.info(
                "Performing integration using client %s on server %s with branch mapping %s.",
                p4_client,
                p4_port,
                integrate_mapping,
            )
            perforce.integrate(
                mapping=integrate_mapping,
                reverse=integrate_reverse,
                to_revision=changelist,
                ignore_source_history=ignore_source_history,
            )

        perforce.resolve(mode="m")
        _try_dbxmerge_resolve(perforce)

        unresolved_files = perforce.unresolved()
        if unresolved_files:
            LOGGER.info("Unresolved files found:")
            for unresolved_file in unresolved_files:
                LOGGER.info(unresolved_file.depot_path)
            LOGGER.error("Unable to automatically resolve merge. Reverting and aborting!")
            raise AutomaticP4MergeResolveException

        buildsln_framework_args = []
        framework_args = list(framework_args)
        dotnet_preview_arg = "-G:use.dotnet.preview.platform=windows"
        if run_branch_guardian and use_preview_dotnet_version:
            buildsln_framework_args = [dotnet_preview_arg]
            framework_args.append(dotnet_preview_arg)

        core.ensure_p4_config(
            port=p4_port,
            client=p4_client,
            user=p4_user,
            ignore=p4_ignore,
        )
        os.environ["P4CONFIG"] = ".p4config"

        # Compile code
        compile_code(
            licensee=list(licensee),
            password=password,
            email=email,
            domain_user=domain_user,
            port=p4_port,
            user=p4_user,
            client=p4_client,
            buildsln_framework_args=buildsln_framework_args,
            framework_args=framework_args,
            clean=code_clean.lower() == "true",
        )

        core.ensure_p4_config(
            root_dir=frostbite_core.get_game_data_dir(),
            port=p4_port,
            client=p4_client,
            user=p4_user,
            ignore=p4_ignore,
        )
        os.environ["P4CONFIG"] = ".p4config"
        if run_upgrade:
            # Upgrade data, locally or with integration.
            if local_upgrade:
                if not local_upgrade_script_path:
                    local_upgrade_script_path = os.path.join(
                        frostbite_core.get_tnt_root(),
                        "Code",
                        "DICE",
                        "UpgradeScripts",
                        "UpgradeLocal.bat",
                    )
                try:
                    core.run([local_upgrade_script_path], print_std_out=True)
                except Exception:
                    perforce.revert(quiet=True)
                    if p4_clean_failed_upgrade:
                        perforce.clean(folder=frostbite_core.get_game_data_dir() + "/...")
                    raise
            else:
                source_data_dir = os.path.join(
                    frostbite_core.get_game_root(), data_directory_source
                )
                core.ensure_p4_config(
                    root_dir=source_data_dir,
                    port=p4_port,
                    client=p4_client,
                    user=p4_user,
                    ignore=p4_ignore,
                )
                os.environ["P4CONFIG"] = ".p4config"
                perforce.sync(path=source_data_dir + "/...", to_revision=changelist)
                _prepare_and_run_fdu(
                    p4_object=perforce,
                    licensee=licensee[0],
                    source_data_dir=source_data_dir,
                    integration_upgrade_script_path=integration_upgrade_script_path,
                    p4_clean_failed_upgrade=p4_clean_failed_upgrade,
                )
        elif run_branch_guardian:
            if use_preview_dotnet_version:
                LOGGER.info("Install DotNetSdk and update environment variables.")
                path_to_eapm = os.path.join(
                    frostbite_core.get_tnt_root(), "Build", "Framework", "bin", "eapm.exe"
                )
                install_dotnetsdk_args = [
                    path_to_eapm,
                    "install",
                    "DotNetSdk",
                    "-masterconfigfile:masterconfig.xml",
                    dotnet_preview_arg,
                ]
                core.run(install_dotnetsdk_args)
                LOGGER.info("DotNetSdk installed.")

                # Get version and CL for the package we've just installed,
                # to be able to update environment variables.
                dotnet_info = package_utils.find_package(
                    "DotNetSdk",
                    framework_args=[dotnet_preview_arg],
                )
                dotnet_uri_info = dotnet_info["DotNetSdk"]["uri"].split("/")[-1].split("?cl=")
                dotnet_preview_version = dotnet_uri_info[0]
                dotnet_preview_cl = dotnet_uri_info[1]
                os.environ[
                    "DOTNET_MSBUILD_SDK_RESOLVER_SDKS_DIR"
                ] = "D:\\packages\\DotNetSdk\\{0}_{1}\\installed\\dotnet\\sdk\\{0}\\Sdks".format(
                    dotnet_preview_version, dotnet_preview_cl
                )
                os.environ["DOTNET_MSBUILD_SDK_RESOLVER_SDKS_VER"] = dotnet_preview_version
                os.environ[
                    "DOTNET_ROOT"
                ] = "D:\\packages\\DotNetSdk\\{}_{}\\installed\\dotnet".format(
                    dotnet_preview_version, dotnet_preview_cl
                )
                LOGGER.info("Environment variables updated.")

            branch_guardian_install_args = [
                "choco",
                "upgrade",
                "BranchGuardianCLI",
                "--source",
                "https://artifactory.ea.com/artifactory/api/nuget/dice-dtc-nuget-local",
                "--force",
                "-y",
                "--verbose",
            ]
            core.run(branch_guardian_install_args)
            branch_guardian_args = [
                "BranchGuardianCLI",
                "GetMergedownDataCommands",
                "-s",
                p4_path_source + "/" + data_directory,
                "-t",
                p4_path_target + "/" + data_directory,
                "-f",
                branch_guardian_rules_file,
            ]
            _, branch_guardian_result, _ = core.run(branch_guardian_args)
            branch_guardian_response = json.loads("".join(branch_guardian_result))
            LOGGER.info("Branch Guardian response:\n{}".format(branch_guardian_response))
            branch_guardian_commands = branch_guardian_response["ScriptCommands"]
            for command in branch_guardian_commands:
                if command["Type"] == "ABORTFORFILES":
                    opened_files = perforce.opened(path=command["Arg1"])
                    if opened_files:
                        raise ELIPYException(
                            "There are {} opened files in {}:\n{}".format(
                                len(opened_files), command["Arg1"], opened_files
                            )
                        )
                elif command["Type"] == "FDU":
                    _try_dbxmerge_resolve(perforce)
                    # Ignore changes in terrain's images
                    perforce.resolve(mode="y", path="//.../Tile_*.png")
                    perforce.resolve(mode="y", path="//.../Tile_*.raw")

                    unresolved_files = perforce.unresolved()
                    if unresolved_files:
                        LOGGER.info("Unresolved files found:")
                        for unresolved_file in unresolved_files:
                            LOGGER.info(unresolved_file.depot_path)
                        raise ELIPYException(
                            "Aborting, since we shouldn't run an upgrade with unresolved files."
                        )
                    extra_fdu_args = command["Flags"].split()
                    _prepare_and_run_fdu(
                        p4_object=perforce,
                        licensee=licensee[0],
                        source_data_dir=frostbite_core.get_game_data_dir(),
                        integration_upgrade_script_path=integration_upgrade_script_path,
                        extra_fdu_args=extra_fdu_args,
                        p4_clean_failed_upgrade=p4_clean_failed_upgrade,
                    )
                elif command["Type"] == "LOGINFO":
                    LOGGER.info("Branch Guardian info: {}".format(command["Arg1"]))
                elif command["Type"] == "P4INTEGRATE":
                    source_path = command["Arg1"]
                    target_path = command["Arg2"]
                    resolve_modes = list(command["Flags"][2:])
                    perforce.integrate(
                        mapping=source_path,
                        reverse=False,
                        stream=False,
                        to_revision=changelist,
                        parent=target_path,
                        use_file_paths=True,
                        ignore_source_history=ignore_source_history,
                        resolve_mode=resolve_modes,
                    )
                elif command["Type"] == "P4RESOLVE":
                    resolve_mode = command["Flags"][2:]
                    resolve_path = command["Arg1"]
                    perforce.resolve(mode=resolve_mode, path=resolve_path)
                elif command["Type"] == "P4REVERT":
                    # Currently supported flags:
                    # w - Files open for add will be wiped from the workspace when reverted.
                    #
                    # If support for more flags is added, add the flag in supported_flags.
                    # See https://www.perforce.com/manuals/cmdref/Content/CmdRef/p4_revert.html
                    # for available flags.
                    revert_flags = list(command["Flags"][1:])
                    supported_flags = {"w"}
                    if set(revert_flags) - supported_flags:
                        raise ELIPYException(
                            "Revert flag(s) not supported: {}".format(
                                set(revert_flags) - supported_flags
                            )
                        )
                    perforce.revert(
                        path=command["Arg1"],
                        wipe="w" in revert_flags,
                    )
                else:
                    raise ELIPYException(
                        "Command {} not supported, please update the integration script.".format(
                            command["Type"]
                        )
                    )

        if run_cook:
            # Cook data
            cook_data(
                assets=list(assets),
                data_directory=data_directory,
                platform=data_platform,
                clean_avalanche_cook=data_clean.lower() == "true",
                pipeline_args=list(pipeline_args),
                use_local_code=True,
            )

        # Submit the result to Perforce.
        message_changelist = (
            last_changelist
            if run_branch_guardian
            else (
                changelist
                if last_changelist == changelist or not last_changelist or not p4_path_source
                else perforce.changes_range(
                    p4_path_source, last_changelist, changelist_end=changelist
                )
            )
        )
        submit_message = ""
        if not run_branch_guardian:
            # The submit message needs to start with this format (Upgraded Data from CL#<list>),
            # to allow ChangeChaser to find the integration.
            #
            # When using Branch Guardian, this is no longer needed.
            # See https://battlefield-ea.slack.com/archives/C03TAPYA8DA/p1739190253853209
            submit_message += f"Upgraded Data from CL#{message_changelist} to Code CL#{changelist}."
        else:
            submit_message += f"Upgraded from CL#{message_changelist} to CL#{changelist}."
        submit_message += "\nIntegrated/copied code, "
        if local_upgrade:
            submit_message += "and upgraded data locally."
        elif run_upgrade:
            submit_message += "and performed a data upgrade integration."
        elif run_branch_guardian:
            submit_message += "and used Branch Guardian for data integration and upgrade."
        submit_message += "\n#branchguardian_bypass"
        core.run(
            [
                "pwsh.exe",
                "-command",
                (
                    "Register-PSRepository -SourceLocation "
                    "https://artifacts.at.ea.com/artifactory/api/nuget/dice-dtc-nuget-local -Name "
                    "dtc -ErrorAction:Ignore; Uninstall-PSResource BattlefieldIntegrationTools "
                    "-ErrorAction:SilentlyContinue; Install-PSResource "
                    "BattlefieldIntegrationTools,yayaml; Import-Module "
                    '"$((Get-InstalledPSResource -Name:BattlefieldIntegrationTools).'
                    "InstalledLocation)/BattlefieldIntegrationTools/$((Get-InstalledPSResource "
                    '-Name:BattlefieldIntegrationTools).Version)/IntegrationTracking.psm1"; '
                    f"Add-IntegrationTrackingInfo -SourceBranch: {p4_path_source} "
                    f"-Changelist:{changelist}"
                ),
            ],
            allow_non_zero_exit_code=True,
            print_std_out=True,
        )
        submit_integration(
            p4_object=perforce,
            submit_message=submit_message,
            submit=submit,
            data_upgrade=True,
            revert_branchid_file=revert_branchid_file,
            shelve_cl=shelve_cl,
        )

        if run_branch_guardian and p4_client_branch_guardian_rules_cleanup and submit:
            perforce_bg_cleanup = p4.P4Utils(
                port=p4_port, client=p4_client_branch_guardian_rules_cleanup, user=p4_user
            )
            perforce_bg_cleanup.revert(quiet=True)
            try:
                # Clean up the Branch Guardian rules file.
                perforce_bg_cleanup.sync("//bf/admin/branchguardian/rules.guardian.json")
                perforce_bg_cleanup.edit("//bf/admin/branchguardian/rules.guardian.json")
                branch_guardian_cleanup_args = [
                    "BranchGuardianCLI",
                    "CleanupRules",
                    "-f",
                    os.path.join(frostbite_core.get_game_root(), "rules.guardian.json"),
                    "--branch",
                    p4_path_source + "/",
                    "--ignore",
                    "bfdata/Source/BuildTrigger.txt",
                    "--verbose",
                    "true",
                    "--openfiles",
                    "true",
                    "--jsonOutput",
                    "true",
                ]
                _, branch_guardian_cleanup_result, _ = core.run(branch_guardian_cleanup_args)
                branch_guardian_cleanup_json = json.loads("".join(branch_guardian_cleanup_result))
                LOGGER.info("CleanupRules response: {}".format(branch_guardian_cleanup_json))
                cleanup_message = ""
                for response_item in branch_guardian_cleanup_json:
                    if response_item["Level"] == "Result":
                        cleanup_message = response_item["Message"]
                submit_message_cleanup = f"Auto cleanup: {cleanup_message}"
                submit_message_cleanup += f"\nJenkins URL: {os.environ.get('BUILD_URL', 'None')}"
                perforce_bg_cleanup.submit(
                    message=submit_message_cleanup, revert_unchanged_files=True
                )
            finally:
                perforce_bg_cleanup.revert(quiet=True)
    finally:
        perforce.revert(quiet=True)


def _prepare_and_run_fdu(
    p4_object: p4.P4Utils,
    licensee: str,
    source_data_dir: str,
    integration_upgrade_script_path: Union[str, None] = None,
    extra_fdu_args: Union[List, None] = None,
    p4_clean_failed_upgrade: bool = False,
):
    if not integration_upgrade_script_path:
        integration_upgrade_script_path = os.path.join(
            frostbite_core.get_tnt_root(),
            "Code",
            "DICE",
            "UpgradeScripts",
            "UpgradeScripts.txt",
        )
    try:
        data.DataUtils.run_frostbite_data_upgrade(
            source_game_data_dir=source_data_dir,
            dest_game_data_dir=frostbite_core.get_game_data_dir(),
            licensee=licensee,
            scripts_path=integration_upgrade_script_path,
            extra_fdu_args=extra_fdu_args,
        )
    except Exception:
        p4_object.revert(quiet=True)
        if p4_clean_failed_upgrade:
            p4_object.clean(folder=frostbite_core.get_game_data_dir() + "/...")
        core.delete_folder(local_paths.get_tnt_local_path())
        raise


def _try_dbxmerge_resolve(perforce: p4.P4Utils):
    if not DBXMerge.executable:
        return

    dbx_files = perforce.unresolved("*.dbx", resolve_type="content")
    if not dbx_files:
        return

    with DBXMerge(perforce.port, perforce.user, perforce.client) as dbxmerge:
        for dbx_file in dbx_files:
            dbxmerge.resolve(dbx_file.local_path)
