"""
p4_copy_data_upgrade.py

This script only works for a one stream setup,
i.e. where we have code and data in the same stream.
"""
import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.integration_utils import compile_code, submit_integration
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import core, data, frostbite_core, LOGGER, p4, running_processes
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command("p4_copy_data_upgrade", short_help="Copy code, and use this to upgrade data.")
@click.option(
    "--batch-file", default="UpgradeLocal.bat", help="Batch file to run for the upgrade step."
)
@click.option("--clean", default="false", help="Delete TnT/Local if --clean true is passed.")
@click.option("--copy-mapping", required=True, help="Perforce mapping for the copy step.")
@click.option(
    "--data-directory",
    default=None,
    help="Which data directory to use for fetching licensee settings.",
)
@click.option(
    "--domain-user",
    default=None,
    help="The user to authenticate to package server as DOMAIN\\user.",
)
@click.option("--email", default=None, help="User email to authenticate to package server")
@click.option("--exclude-path", default=[], multiple=True, help="Don't integrate path.")
@click.option("--force/--no-force", default=False)
@click.option(
    "--licensee", multiple=True, default=None, help="What licensee should gensln be ran against."
)
@click.option("--p4-client", required=True, help="Perforce client/workspace.")
@click.option("--p4-port", required=True, help="Perforce port/server.")
@click.option("--p4-user", default=None, help="Perforce user name.")
@click.option(
    "--password",
    default=None,
    help="User credentials to authenticate to package server",
)
@click.option("--reverse/--no-reverse", default=False)
@click.option("--source-branch", required=True, help="Perforce branch/stream name.")
@click.option("--source-changelist", required=True, help="Perforce changelist number.")
@click.option("--stream/--no-stream", default=False)
@click.option("--submit/--no-submit", default=True)
@click.option("--submit-message", default="", help="Message to include in the submit message.")
@click.option("--target-changelist", required=True, help="Perforce changelist number.")
@click.option(
    "--p4-ignore",
    default=".p4ignore",
    help=".p4ignore file to set, if not will set the default of .p4ignore",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
# pylint: disable=too-many-locals, invalid-name, too-many-statements, protected-access
def cli(
    _,
    batch_file,
    clean,
    copy_mapping,
    data_directory,
    domain_user,
    email,
    exclude_path,
    force,
    licensee,
    p4_client,
    p4_port,
    p4_user,
    password,
    reverse,
    source_branch,
    source_changelist,
    stream,
    submit,
    submit_message,
    target_changelist,
    p4_ignore,
):
    """
    Copy code from source branch to target branch, using a branch mapping.
    Use the copied code to upgrade data on the target branch, using UpgradeLocal.bat.

    This script only works for a one stream setup,
    i.e. where we have code and data in the same stream.
    """
    # Adding sentry tags.
    add_sentry_tags(__file__)

    running_processes.kill()

    if data_directory is not None:
        data.DataUtils.set_datadir(data_directory)

    # Initialize Perforce
    perforce = p4.P4Utils(port=p4_port, client=p4_client, user=p4_user)
    perforce.revert()
    core.ensure_p4_config(
        root_dir=frostbite_core.get_game_data_dir(),
        port=p4_port,
        client=p4_client,
        user=p4_user,
        ignore=p4_ignore,
    )
    os.environ["P4CONFIG"] = ".p4config"

    LOGGER.info(
        "Performing copy using workspace %s on server %s with branch mapping %s.",
        p4_client,
        p4_port,
        copy_mapping,
    )
    perforce.copy_mapping(
        mapping=copy_mapping,
        reverse=reverse,
        stream=stream,
        force=force,
        to_revision=source_changelist,
    )
    for exclude in exclude_path:
        perforce.revert(path=exclude)

    try:
        LOGGER.info("Run gensln and buildsln using the code copied above.")
        compile_code(
            licensee=list(licensee),
            password=password,
            email=email,
            domain_user=domain_user,
            port=p4_port,
            user=p4_user,
            client=p4_client,
            overwrite_p4config=False,
            clean=clean.lower() == "true",
        )

        try:
            LOGGER.info("Upgrade data using the code binary built above.")
            upgrade_script = os.path.join(
                frostbite_core.get_tnt_root(), "Code", "DICE", "UpgradeScripts", batch_file
            )
            core.run([upgrade_script], print_std_out=True)
        except Exception:
            perforce.clean(folder=frostbite_core.get_game_data_dir() + "/...")
            raise

        # Submit the result to Perforce.
        perforce_message = (
            f"Copied code from {source_branch} at CL#{source_changelist}, "
            f"using branch mapping {copy_mapping}. "
            f"Used this to upgrade data at CL#{target_changelist}."
        )
        if submit_message:
            perforce_message += f"\n{submit_message}"
        submit_integration(
            p4_object=perforce, submit_message=perforce_message, submit=submit, data_upgrade=True
        )
    finally:
        perforce.revert(quiet=False)
