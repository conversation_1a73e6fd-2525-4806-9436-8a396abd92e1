"""
webexport.py
"""
import os
import click
from inspect import getfullargspec
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.state_utils import import_avalanche_data_state
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import (
    LOGGER,
    core,
    avalanche,
    filer,
    data,
    SETTINGS,
    build_metadata_utils,
    frostbite_core,
)
from elipy2 import running_processes
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics
from elipy2.exceptions import WebexportException


# pylint: disable=too-many-locals
# pylint: disable=line-too-long
@click.command("webexport", short_help="Performs a webexport build.")
@click.option("--platform", help="Platform to be built, default win64.", default="win64")
@click.option(
    "--asset",
    help="Asset that will be build, default is RetailLevels.",
    default="RetailLevels",
)
@click.option("--code-branch", help="Branch/stream to fetch the code/binary build from.")
@click.option("--code-changelist", help="Changelist of binaries to fetch.")
@click.option("--data-changelist", help="Changelist of data being used.")
@click.option(
    "--save-to-filer/--dont-save-to-filer",
    default=True,
    help="Flag for writing webexport output to filer.",
)
@click.option(
    "--branch-name",
    required=True,
    help="Which branch to save as bf-hotfix, bf-release etc.",
)
@click.option("--nuke", is_flag=True, help="Nuke avalanche before run.")
@click.option("--data-dir", default="bfdata")
@click.option("--script-path", required=True)
@click.option(
    "--force-build/--not-force-build",
    default=False,
    help="Flag for setting force build arg.",
)
@click.option(
    "--levels-file/--not-levels-file",
    default=True,
    help="Flag for setting writeBuiltLevelsFile arg.",
)
@click.option(
    "--data-clean",
    default="false",
    help="Clean Avalanche if --data-clean true is passed.",
)
@click.option("--import-avalanche-state", is_flag=True, help="Imports Avalanche state from filer.")
@click.option("--aws-access-key-id", default="invalid key", help="AWS acceess key id.")
@click.option("--aws-secret-key", default="invalid key", help="AWS secret access key.")
@click.option("--skip-aws-upload", is_flag=True, help="Skip uploading files to AWS.")
@click.option(
    "--clean-master-version-check",
    is_flag=True,
    help="Run clean on master version update.",
)
@click.option(
    "--branch-override",
    default="",
    help="Set to branch name if uploading to AWS from a virtual stream",  # https://swarm.frostbite.com/reviews/20838951
)
@click.option(
    "--content-layers",
    multiple=True,
    default=[],
    help="Specific content layer to run an extra cook",
)
# pylint: enable=line-too-long
# pylint: disable=too-many-statements
# pylint: disable=too-many-locals
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    platform,
    asset,
    code_branch,
    code_changelist,
    data_changelist,
    save_to_filer,
    branch_name,
    nuke,
    data_dir,
    script_path,
    force_build,
    levels_file,
    data_clean,
    import_avalanche_state,
    aws_access_key_id,
    aws_secret_key,
    skip_aws_upload,
    clean_master_version_check,
    branch_override,
    content_layers,
):
    """
    Performs the webexport build and places work into filer
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    webexport = _get_webexport_module(script_path)

    if nuke:
        LOGGER.info("Performing avalanche nuke.")
        avalanche.nuke()

    running_processes.kill()

    builder = data.DataUtils(platform, [asset], monkey_build_label=data_changelist)
    builder.set_datadir(data_dir)

    extra_args = _fetch_binaries(
        code_branch,
        code_changelist,
        branch_name,
        platform,
        data_changelist,
        import_avalanche_state,
    )

    # The pylint-disable below is a workaround for a pylint parsing bug. (At the moment
    # inspect.getfullargspec is not deprecated.) The linter thinks it always imported getargspec as
    # getfullargspec and getargspec is deprecated.
    run_web_export_argnames = getfullargspec(webexport.run_web_export)[
        0
    ]  # pylint: disable=deprecated-method

    if data_clean.lower() == "true":
        builder.clean()
    # Import previous Avalanche state
    elif import_avalanche_state and not "pipeline_args" in run_web_export_argnames:
        _import_previous_avalanche_state(
            builder, extra_args, code_changelist, data_changelist, branch_name, platform
        )

    # Local Exports.
    write_directory = os.path.join(frostbite_core.get_tnt_root(), "Local", "webexport")

    branch_override = branch_override if branch_override else branch_name

    cook_args = []
    _run_webexport(
        webexport,
        builder,
        run_web_export_argnames,
        asset,
        clean_master_version_check,
        aws_access_key_id,
        aws_secret_key,
        cook_args,
        force_build,
        write_directory,
        extra_args,
        platform,
        skip_aws_upload,
        levels_file,
        branch_override,
    )

    if save_to_filer:
        _filer_save(write_directory, code_changelist, data_changelist, branch_name)

    if "cookArgs" in run_web_export_argnames:
        for layer in content_layers:
            LOGGER.info("Running webexport for content layer: '%s'", layer)
            layer_cook_args = cook_args.copy()
            layer_cook_args += ["-activeContentLayer", layer]
            _run_webexport(
                webexport,
                builder,
                run_web_export_argnames,
                asset,
                clean_master_version_check,
                aws_access_key_id,
                aws_secret_key,
                layer_cook_args,
                force_build,
                write_directory,
                extra_args,
                platform,
                skip_aws_upload,
                levels_file,
                branch_override,
            )

            if save_to_filer:
                _filer_save(
                    write_directory,
                    code_changelist,
                    data_changelist,
                    branch_name,
                )
    else:
        LOGGER.info(
            "Skipping webexport for content layers %s as the script does not support cookArgs.",
            content_layers,
        )


def _get_webexport_module(script_path):
    """
    Imports webexport.py from the game root.
    """
    full_script_path = os.path.join(frostbite_core.get_tnt_root(), script_path)
    if not os.path.exists(full_script_path):
        full_script_path = os.path.join(frostbite_core.get_game_root(), script_path)

    try:
        # This is horrible. We need to find a better way around this,
        # but right now this causes our beta builds to fail.
        return core.import_module_from_file("webexport", full_script_path)
    except Exception as exc:
        LOGGER.info("Unable to import the webexport script.")
        raise exc


def _fetch_binaries(
    code_branch,
    code_changelist,
    branch_name,
    platform,
    data_changelist,
    import_avalanche_state,
):
    """
    Fetches binaries.
    """
    _filer = filer.FilerUtils()
    mirror = True
    for platform_to_fetch in ["pipeline", "frosted"]:
        _filer.fetch_code(code_branch, code_changelist, platform_to_fetch, "release", mirror=mirror)
        mirror = False
    return (
        import_avalanche_data_state(branch_name, code_branch, platform, _filer, data_changelist)
        if import_avalanche_state
        else []
    )


def _import_previous_avalanche_state(
    builder, extra_args, code_changelist, data_changelist, branch_name, platform
):
    """
    Imports previous Avalanche state.
    """
    builder.cook(pipeline_args=extra_args, collect_mdmps=True)
    avalanche.set_avalanche_build_status(
        code_changelist=code_changelist,
        data_changelist=data_changelist,
        data_branch=branch_name,
        platform=platform,
    )


def _run_webexport(
    webexport,
    builder,
    run_web_export_argnames,
    asset,
    clean_master_version_check,
    aws_access_key_id,
    aws_secret_key,
    cook_args,
    force_build,
    write_directory,
    extra_args,
    platform,
    skip_aws_upload,
    levels_file,
    branch_override,
):
    """
    Runs the webexport job, calling webexport.py's run method directly.
    """
    possible_run_web_export_args = {
        "asset": asset,
        "attach": False,
        "doNotMinify": False,
        "awsAccessKeyId": aws_access_key_id,
        "awsSecretAccessKey": aws_secret_key,
        "forceBuild": True
        if "writeBuiltLevelsFile" in run_web_export_argnames
        else force_build,  # Setting this to true, to not change existing functionality for older projects.
        "outputDir": write_directory,
        "pipeline_args": extra_args,
        "platform": platform,
        "uploadToAws": not skip_aws_upload,
        "uploadToDreDiceSe": True,
        "writeBuiltLevelsFile": levels_file,
        "extraPipelineArgs": data.DataUtils.get_clean_master_version_args()
        if clean_master_version_check
        else [],
    }

    try:
        # Run webexport job
        # branchOverride is not always implemented in P4 webexport.py
        if "branchOverride" in run_web_export_argnames:
            possible_run_web_export_args["branchOverride"] = branch_override

        if "cookArgs" in run_web_export_argnames:
            possible_run_web_export_args["cookArgs"] = cook_args
        elif cook_args:
            LOGGER.warning(
                "The webexport script does not support cookArgs so the following arguments will be ignored: %s",
                cook_args,
            )

        arguments = {arg: possible_run_web_export_args[arg] for arg in run_web_export_argnames}
        if not webexport.run_web_export(**arguments):
            raise WebexportException(
                "Unable to build Webexport. Please reach out to @tvanommeren for help."
            )
    except Exception:
        LOGGER.error(
            "An error occurred whilst running Webexport for {0}".format(platform),
            exc_info=True,
        )
        builder.copy_dmp_and_mdmp_to_filer()
        raise


def _filer_save(write_directory, code_changelist, data_changelist, branch_name):
    """
    Saves to filer.
    """

    # Read patch version if written to eg. {write_directory}\casablanca.game-dev.Win32\Win32\patchversion.txt
    patch_version = None
    platform_name = "Win32"
    database_name = (
        os.environ["BRANCH_NAME"] + "." + os.environ["fb_branch_id"] + "." + platform_name
    )
    patch_version_file = os.path.join(
        write_directory, database_name, platform_name, "patchversion.txt"
    )
    if os.path.exists(patch_version_file):
        with open(patch_version_file, "r") as _file:
            for line in _file.readlines():
                patch_version = line.rstrip()
                LOGGER.info("Found patchversion.txt, read version '%s'", patch_version)
                break
    else:
        LOGGER.info("No patchversion.txt found at '%s'", patch_version_file)

    # Build filer path.
    changelist = data_changelist + "_" + code_changelist
    export_dir_final = os.path.join(
        SETTINGS.get("build_share"),
        "WebExport",
        branch_name,
        patch_version or "",
        changelist,
    )
    # Copy everything to the export directory.
    LOGGER.info("Copying web export output to the export directory")
    if not os.path.exists(write_directory):
        raise WebexportException("The write directory /%s/ did not exist!" % write_directory)
    LOGGER.info(
        "Will copy the web export to the export directory /%s/ (deleting anything in that folder).",
        export_dir_final,
    )
    LOGGER.info("Copying /%s/ to /%s/...", write_directory, export_dir_final)
    core.robocopy(write_directory, export_dir_final, purge=True, extra_args=["/NFL"])
    metadata_manager = build_metadata_utils.setup_metadata_manager()
    metadata_manager.register_web_export(export_dir_final, changelist, branch_name)
