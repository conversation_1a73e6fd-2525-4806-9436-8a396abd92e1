package com.ea.lib.jobsettings

import com.ea.lib.LibCommonNonCps

class BuildDeleterSettings extends JobSetting {

    Boolean deleteEmptyFolders
    Integer parallelJobs
    String projectWithLocation
    String vaultCredentials
    String vaultVariable

    void initializeStart(def project, def projectWithLocation, boolean deleteEmptyFolders, int hoursFromMidnight, def projectInfo) {
        if (!(hoursFromMidnight in 1..24)) {
            throw new IllegalArgumentException('hoursFromMidnight not a valid number')
        }
        isDisabled = LibCommonNonCps.get_setting_value(projectInfo, [], 'disable_build', false)
        cronTrigger = projectInfo.trigger_string ?: deleteEmptyFolders ? "0 ${24 - hoursFromMidnight} * * 6" : 'H/30 * * * 1-6\nH/30 6-23 * * 7'
        description = 'Runs build deletion using ELIPY2.'
        parallelJobs = projectInfo.parallel_jobs ?: 3
        projectName = project.name
        this.projectWithLocation = projectWithLocation
        this.deleteEmptyFolders = deleteEmptyFolders
    }

    void initializeJob(def project_info, def project_with_location, def project) {
        isDisabled = LibCommonNonCps.get_setting_value(project_info, [], 'disable_build', false)
        description = 'Deletes builds using ELIPY2.'
        jobLabel = "deleter && ${(project_info?.label ?: project_with_location).toLowerCase()}"
        def timeoutHours = project_info.timeout_hours ?: 9
        timeoutMinutes = timeoutHours * 60
        def extraArgs = project_info.extra_args ?: ''
        workspaceRoot = project_info.workspace_root
        buildName = '${JOB_NAME}.${BUILD_NUMBER}'
        vaultVariable = project.vault_variable
        vaultCredentials = project.vault_credentials
        elipyInstallCall = project_info.elipy_install_call
        elipyCmd = "${project_info.elipy_call} deleter ${extraArgs} %delete_empty_folders%"
    }
}
