package com.ea.lib.model.autotest

/**
 * Use WIN64 in the context of Autotests instead of WIN64GAME
 **/
enum Name {
    WIN64GAME('win64game'), WIN64DLL('win64'), TOOL('tool'), <PERSON><PERSON><PERSON>('win64'), PS3('ps3'), PS4('ps4'), PS5('ps5'), XB1('xb1'), XBSX('xbsx'), LINUX64('linux64'), ANY('Any')
    private final String name

    private Name(String s) {
        name = s
    }

    String getValue() {
        return this.name
    }

    String toString() {
        return this.name
    }
}
