package com.ea.project.mer

import com.ea.project.Cobra

class Merlin {
    static String name = 'merlin'
    static String short_name = 'mer'
    static Boolean frostbite_syncer_setup = true
    static Boolean single_perforce_server = true
    static Boolean presync_machines = true
    static String user_credentials = 'monkey.merlin'
    static String vault_server_credentials = ''
    static String vault_credentials = 'cobra-online-rob-prod-secret-id'
    static String vault_variable = 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
    static Boolean vault_win64_trial = false
    static String game_team_secrets_credential = 'game-team-secrets-secret-id'
    static String game_team_secrets_credential_extra = ''
    static String game_team_secrets_credential_online_prod = ''
    static String game_team_secrets_variable = 'GAME_TEAM_SECRETS_SECRET_ID'
    static List vault_default_platforms = [
        'linuxserver',
        'ps5',
        'server',
        'win64',
        'xbsx',
    ]

    static String dataset = 'Data'

    static String workspace_root = 'D:\\dev'
    static String fbcli_call = 'tnt\\bin\\fbcli\\cli.bat x64'
    static String location = 'criterion'
    static String elipy_scripts_config_file = 'elipy_merlin.yml'
    static String elipy_install_call = "${fbcli_call} && ${Cobra.elipy_install} $elipy_scripts_config_file >> ${workspace_root}\\logs\\install-elipy.log 2>&1"
    static String elipy_setup_call = "${fbcli_call} && ${Cobra.elipy_setup_env} $elipy_scripts_config_file >> ${workspace_root}\\logs\\setup-elipy-env.log 2>&1"
    static String elipy_call = "${elipy_setup_call} && elipy --location $location"

    static String azure_workspace_root = 'E:\\dev'
    static String azure_elipy_install_root = 'C:\\dev'
    static String azure_elipy_setup_call = "$fbcli_call && $azure_elipy_install_root\\ci\\setup-elipy-env.bat $elipy_scripts_config_file >> $azure_workspace_root\\logs\\setup-elipy-env.log 2>&1"
    static String azure_elipy_install_call = "$fbcli_call && $azure_elipy_install_root\\ci\\install-elipy.bat $elipy_scripts_config_file >> $azure_workspace_root\\logs\\install-elipy.log 2>&1"
    static String azure_elipy_call = "$azure_elipy_setup_call && elipy --location $location"

    static String p4_browser_url = 'http://swarm-oh-p4-criterion.eu.ad.ea.com/'
    static String p4_user_single_slash = '%USERDOMAIN%\\%USERNAME%'
    // These are used to create p4 tokens on other p4 servers so we can fetch packages.
    // We only need these for projects that don't normally connect to these p4 servers.
    static String p4_fb_creds = 'merlin_eaukproxy_dice-p4edge-fb'
    static String p4_fb_server = 'oh-p4edge-fb.eu.ad.ea.com:2001'
    static String p4_fb_stream = '//fbstream/2019_X'
    static Map p4_fb_settings = [
        p4_port               : p4_fb_server,
        p4_creds              : p4_fb_creds,
        p4_stream             : p4_fb_stream,
        workspace_name_postfix: '-authenticate-${NODE_NAME}',
        workspace_type        : 'manual',
        // none existant path just so we authenticate
        view                  : '//packages/authenticate/... //${P4_CLIENT}/packages/authenticate/...',

    ]
    static String p4_ghost_creds = 'robotomation_eaukproxy_perforce-ghost'
    static String p4_ghost_server = 'oh-p4proxy-perforce-ghost.eu.ad.ea.com:2031'
    static String p4_ghost_stream = '//roboto/ML'
    static Map p4_ghost_settings = [
        p4_creds : p4_ghost_creds,
        p4_stream: p4_ghost_stream,
    ]
    static Map p4_extra_servers = [:]

    static String p4_code_root = '//merlin'
    static String p4_code_creds = 'perforce-criterion-merlin'
    static String p4_code_server = 'oh-p4-criterion.eu.ad.ea.com:1666'
    static String p4_code_client = 'jenkins-${NODE_NAME}-codestream'
    static String p4_code_client_env = 'jenkins-%NODE_NAME%-codestream'
    // In a single server setup where TnT and Data live in the same stream, we can't have two workspaces
    // since they'll be writing to the same files on disk.
    // Exception to this is if you set up virtual streams for code and data respectively, and make
    // sure they never overlap.
    static String p4_data_root = p4_code_root
    static String p4_data_creds = p4_code_creds
    static String p4_data_server = p4_code_server
    static String p4_data_client = p4_code_client
    static String p4_data_client_env = p4_code_client_env

    static Map p4_code_servers = [
        'merlin_cri': p4_code_server
    ]

    static List<Map> p4_data_servers = [
        [name: 'merlin_cri', p4_port: p4_data_server],
    ]

    static Map icepick_settings = [:]
    static String webexport_script_path = 'Bin\\Casablanca\\fbcli\\webexport.py' // Not used by Criterion so Casablanca value is fine
    static String drone_exclude_path = 'TnT/Setup/Drone/...'
    static Boolean fake_ooa_wrapped_symbol = false
    static Boolean commerce_debug_disable = false
    static Boolean use_recompression_cache = false
    static Boolean separate_symbol_store_upload = false
    static Boolean is_cloud = false

    static String autotest_matrix = 'CriterionAutotestMatrix'
    static String retry_limit = 0
    static String denuvo_exclusion_path = 'TnT\\Build\\DenuvoExclusionList'
    static Boolean verify_post_vault = true
    static Boolean compress_symbols = false
    static List<Map> vault_secrets_project = Cobra.af2_vault_credentials
}
