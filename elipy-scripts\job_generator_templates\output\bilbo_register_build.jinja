{#
    Command:
        bilbo_register_build
            short_help: Registers a build in the configured metadata services from a given build.json file.

    Arguments:

    Required variables:
        build_json_path
            help: Location of build.json file to insert into the configured metadata services
            required: True

    Optional variables:
        extra_location
            multiple: True
            required: False
            help: Another location to register this build
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    bilbo_register_build
    --build-json-path {{ build_json_path }}
    {%- if extra_location %}
    --extra-location {{ extra_location }}
    {%- endif %}
  displayName: elipy bilbo_register_build
