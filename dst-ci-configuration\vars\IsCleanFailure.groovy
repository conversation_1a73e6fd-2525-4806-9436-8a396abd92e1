import com.ea.exceptions.CobraException
import com.sonyericsson.jenkins.plugins.bfa.model.FailureCauseBuildAction

/**
 * IsCleanFailure.groovy
 * Returns true if the last build for the given job failed with an issue that should be rebuilt with clean.
 * Examples:
 * 'pre-compiled header' (PCH) issue
 * Unknown types/missing files in DDC generated code.
 */
boolean call(def job_name) {
    echo '[IsCleanFailure] Start'
    def job = Jenkins.get().getItem(job_name)
    if (job == null) {
        throw new CobraException('Job ' + job_name + ' not found.')
    }

    def last_build = job.lastCompletedBuild
    if (last_build == null) {
        return false // No previous build found for this job, no PCH issue to find here.
    }

    def action = last_build.getAction(FailureCauseBuildAction)
    if (action == null || (action != null && action.foundFailureCauses.isEmpty())) {
        return false // Build not scanned, can't determine if PCH issues was the cause.
    }

    def result = false
    for (def cause in action.foundFailureCauses) {
        if (cause.categories.contains('pch')) {
            result = true
        } else if (cause.categories.contains('ddc')) {
            result = true
        } else if (cause.categories.contains('SYMCHK')) {
            result = true
        }
    }

    echo '[IsCleanFailure] End'
    return result
}
