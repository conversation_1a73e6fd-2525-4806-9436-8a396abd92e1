package scripts.schedulers.all

import hudson.model.Result

def project = ProjectClass(env.project_name)

/**
 * perforce_dvcs_scheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Trigger dvcs job') {
            steps {
                script {
                    def dvcs_remote_spec = env.dvcs_remote_spec
                    def slack_channel = env.slack_channel
                    def skip_fetch = env.skip_fetch.toBoolean()
                    def skip_push = env.skip_push.toBoolean()
                    def args = []
                    def inject_map = [
                        'dvcs_remote_spec': dvcs_remote_spec,
                    ]
                    EnvInject(currentBuild, inject_map)
                    currentBuild.displayName = env.JOB_NAME

                    def final_result = Result.SUCCESS

                    if (!skip_push) {
                        def job_push_name = 'dvcs.push.' + dvcs_remote_spec
                        def push_job = build(job: job_push_name, parameters: args, propagate: false)
                        final_result = push_job.result
                    }
                    if (final_result.toString() != 'SUCCESS' && !skip_push) {
                        echo 'DVCS push failed, aborting.'
                    } else if (!skip_fetch) {
                        def job_fetch_name = 'dvcs.fetch.' + dvcs_remote_spec
                        def fetch_job = build(job: job_fetch_name, parameters: args, propagate: false)
                        final_result = fetch_job.result
                    }

                    currentBuild.result = final_result.toString()
                    SlackMessageNew(currentBuild, slack_channel, project.short_name)

                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
        stage('Scan for errors') { steps { ScanForErrors(currentBuild, true) } }
    }
}
