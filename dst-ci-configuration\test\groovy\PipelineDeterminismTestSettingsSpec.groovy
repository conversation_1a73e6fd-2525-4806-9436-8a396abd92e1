import com.ea.lib.jobsettings.PipelineDeterminismTestSettings
import com.ea.lib.model.branchsettings.PipelineDeterminismTestConfiguration
import spock.lang.Specification

class PipelineDeterminismTestSettingsSpec extends Specification {
    class BranchFile {
        static Map standard_jobs_settings = [
            workspace_root      : 'workspace-root',
            elipy_call          : 'elipy-call',
            elipy_install_call  : 'elipy-install-call',
            job_label_statebuild: 'label',
            p4_code_server      : 'p4_code_server:2000',
            code_branch         : 'code-branch',
        ]
        static Map general_settings = [
            pipeline_determinism_test_configuration: new PipelineDeterminismTestConfiguration(
                cronTrigger: '@daily',
                referenceJob: 'reference-job',
            )
        ]
    }

    class BranchFileWithP4Config {
        static Map standard_jobs_settings = [
            workspace_root      : 'workspace-root',
            elipy_call          : 'elipy-call',
            elipy_install_call  : 'elipy-install-call',
            job_label_statebuild: 'label',
            p4_code_server      : 'p4-code-server:2000',
            code_branch         : 'code-branch',
        ]
        static Map general_settings = [
            pipeline_determinism_test_configuration: new PipelineDeterminismTestConfiguration(
                cronTrigger: '@daily',
                referenceJob: 'reference-job',
                perforceCodeCredentials: 'criterion-creds',
                perforceCodeServer: 'criterion.ad.ea.com:2001',
                perforceDataCredentials: 'criterion-creds',
                perforceDataServer: 'criterion.ad.ea.com:2001',
            )
        ]
    }

    class MasterFile {
        static Map branches = [branch: [data_branch: 'data-branch', code_branch: 'code-branch', code_folder: 'dev']]
    }

    class ProjectFile {
        static String name = 'Kingston'
    }

    void "test that we get expected job settings in initializeJob"() {
        when:
        PipelineDeterminismTestSettings settings = new PipelineDeterminismTestSettings()
        settings.initializeJob(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            timeoutMinutes == 720
            jobLabel == 'statebuild'
            isDisabled == false
            buildName == '${JOB_NAME}.${ENV, var="code_changelist"}'
            elipyCmd == [
                'elipy-call',
                'pipeline_determinism_test',
                '--code-branch',
                'code-branch',
                '--code-changelist',
                '%code_changelist%',
                '--script-args',
                '"%script_args%"',
                '--p4-port',
                'p4_code_server:2000',
            ].join(' ')
        }
    }

    void "test that we get expected job settings in initializeStart"() {
        when:
        PipelineDeterminismTestSettings settings = new PipelineDeterminismTestSettings()
        settings.initializeStart(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            cronTrigger == '@daily'
        }
    }

    void "test that we get the expected job settings with declared p4 configurations"() {
        when:
        PipelineDeterminismTestSettings settings = new PipelineDeterminismTestSettings()
        settings.initializeJob(BranchFileWithP4Config, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            timeoutMinutes == 720
            jobLabel == 'statebuild'
            isDisabled == false
            buildName == '${JOB_NAME}.${ENV, var="code_changelist"}'
            elipyCmd == [
                'elipy-call',
                'pipeline_determinism_test',
                '--code-branch',
                'code-branch',
                '--code-changelist',
                '%code_changelist%',
                '--script-args',
                '"%script_args%"',
                '--p4-port',
                'criterion.ad.ea.com:2001',
            ].join(' ')
        }
    }
}
