{#
    Command:
        icepick_warm_cache
            short_help: Run icepick cook and store the avalanche db for future use.
            context_settings: dict(ignore_unknown_options=True)

    Arguments:

    Required variables:
        platform
            required: True
        category_name
            required: True
        tests
            required: True
            multiple: True
        code_branch
            required: True
            help: Branch to fetch code from.
        code_changelist
            required: True
            help: Which code changelist to use.
        data_branch
            help: Branch to fetch Avalanche state from.
            required: True
        data_changelist
            help: Which data changelist to use.
            required: True

    Optional variables:
        fully_qualified_paths
            is_flag: True
            help: test names should be fully qualified
        datadir
            default: data
            help: Specify which data directory to use (relative to GAME_ROOT).
        inert_run
            is_flag: True
            help: Run but don't do anything
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    icepick_warm_cache
    --platform {{ platform }}
    --category-name {{ category_name }}
    --tests {{ tests }}
    --code-branch {{ code_branch }}
    --code-changelist {{ code_changelist }}
    --data-branch {{ data_branch }}
    --data-changelist {{ data_changelist }}
    {%- if fully_qualified_paths %}
    --fully-qualified-paths {{ fully_qualified_paths }}
    {%- endif %}
    {%- if datadir %}
    --datadir {{ datadir }}
    {%- endif %}
    {%- if inert_run %}
    --inert-run {{ inert_run }}
    {%- endif %}
  displayName: elipy icepick_warm_cache
