# Duplicated settings lower in the settings list will get higher priority
buildtype: "QA"
milestone: "Production"
distribution_type: "ExternalReview"
retention_policy: "SpaceAvailable"
priority: ""
version: "2.0"

linuxserver:
  content:
    digital:
      file_names:
        - "*_Server_*_Binaries.zip"
      supplemental_files:
        - "build.json"
      directory:
        - ""
    files:
      file_names:
        - "*"
      supplemental_files:
        - "*.xml"
      directory:
        - "Config"
        - "Data"
        - "Scripts"
  settings:
    bf-playtest-gnt:
      ww:
        final:
          digital:
            sku_id: "012ba272-2fad-4f37-a9e3-d8b417b78932"
            sku_name: "Server - WW (bf-playtest-gnt linuxserver digital fina)"
    kin-upgrade:
      ww:
        final:
          digital:
            sku_id: "c1ce8084-3ab7-4288-a314-85c790db5518"
            sku_name: "Server - WW (kin-upgrade linuxserver digital final)"

ps5:
  content:
    files:
      file_names:
        - "builtLevels.json"
        - "build.json"
        - "*.prx"
        - "*.buildlayout"
        - "*.BuildSettings"
        - "eboot.bin"
      supplemental_files:
        - "*.xml"
      directory:
        - "Config"
        - "data"
        - "Scripts"
        - "sce_sys"
    digital:
      file_names:
        - "*-V0100.pkg"
      upload_loop_filenames:
        - "*V0100.pkg"
        - "*remastered.pkg"
      supplemental_files:
        - "chunks*.gp5"
        - "build.json"
      directory:
        - ""
    patch:
      file_names:
        - "*-V0100.pkg"
      upload_loop_filenames:
        - "*V0100.pkg"
        - "*remastered.pkg"
      supplemental_files:
        - "chunks*.gp5"
        - "build.json"
      directory:
        - ""
  settings:
    bf-playtest-gnt:
      dev:
        final:
          files:
            sku_id: "ce50810b-7950-47ba-9577-72501cc7ac28"
            sku_name: "FG - EU (bf-playtest-gnt ps5 files final)"
        retail:
          digital:
            sku_id: "f8a0beee-866e-4457-b6b4-8c53f50aae43"
            sku_name: "FG - EU (bf-playtest-gnt ps5 digital retail)"
    bf-playtest-san:
      dev:
        final:
          files:
            sku_id: "c91d418b-8218-40af-bd2b-f2137824b74c"
            sku_name: "FG - EU (bf-playtest-san ps5 files final)"
        retail:
          digital:
            sku_id: "b4ce2b09-0918-4a78-bd34-1b61cc6bc65f"
            sku_name: "FG - EU (bf-playtest-san ps5 digital retail)"
    bf-stage:
      dev:
        final:
          files:
            sku_id: "3be0407f-d81d-42f3-88e9-3f3b49ecc6be"
            sku_name: "FG - EU (bf-stage ps5 files final)"
    kin-upgrade:
      dev:
        final:
          files:
            sku_id: "5160bfc4-49c1-4666-bf67-26ddcaf4742c"
            sku_name: "FG - EU (kin-upgrade ps5 files final)"
    bf-trunk:
      dev:
        final:
          files:
            sku_id: "7707cd33-f7f4-4666-900d-34d8839c0b96"
            sku_name: "FG - EU (bf-trunk ps5 files final)"
    bf-trunk-retail:
      dev:
        retail:
          digital:
            sku_id: "82684c8d-baf1-4e36-b178-29def217c683"
            sku_name: "FG - EU (bf-trunk ps5 digital retail)"
    bf-trunk-editor-release:
      dev:
        final:
          digital:
            sku_id: "c00b3dcd-d9af-44dc-aa9a-d5273f21d235"
            sku_name: "FG - EU (bf-trunk-er ps5 digital final)"
          files:
            sku_id: "e0e278ec-daa7-447a-8f2e-fb03bcd9a2aa"
            sku_name: "FG - EU (bf-trunk-er ps5 files final)"
        retail:
          digital:
            sku_id: "a70897a9-8352-4553-8418-f4e126a0466c"
            sku_name: "FG - EU (bf-trunk-er ps5 digital retail)"
    san-dev:
      ww:
        final:
          files:
            sku_id: "49493844-abbb-4818-9bcb-819e9f74ec72"
            sku_name: "FG - WW (san-dev ps5 files final)"

server:
  content:
    digital:
      file_names:
        - "*_Server_*_Binaries.zip"
      supplemental_files:
        - "build.json"
      directory:
        - ""
    files:
      file_names:
        - "builtLevels.json"
        - "build.json"
        - "*.buildlayout"
        - "*.exe"
        - "*.dll"
        - "*.BuildSettings"
      supplemental_files:
        - "*.xml"
      directory:
        - "Config"
        - "Data"
        - "Scripts"
  settings:
    bf-stage:
      ww:
        final:
          files:
            sku_id: "0715a16c-b4b4-4e3e-955f-c5eb7d0db929"
            sku_name: "Server - WW (bf-stage server files final)"
    kin-upgrade:
      ww:
        final:
          files:
            sku_id: "737edbf5-2f52-4385-a6a2-74c2356dc0ba"
            sku_name: "Server - WW (kin-upgrade server files final)"
    bf-trunk:
      ww:
        final:
          files:
            sku_id: "b713e218-9746-4e7a-aa61-2db9db1fedca"
            sku_name: "Server - WW (bf-trunk server files final)"
    san-proto-x:
      ww:
        final:
          files:
            sku_id: "6bc699aa-4d26-468b-97c0-29bfc957d54c"
            sku_name: "Server - WW (gnt-event-1)"

win64:
  content:
    files:
      file_names:
        - "builtLevels.json"
        - "build.json"
        - "*.BuildSettings"
        - "*.buildlayout"
        - "*.Main_Win64_*.exe"
        - "*.dll"
      supplemental_files:
        - "*.xml"
      directory:
        - "Config"
        - "Data"
        - "Scripts"
    digital:
      retail:
        file_names:
          - "BattlefieldGameData.zip"
        supplemental_files:
          - "build.json"
          - "installerdata.xml"
          - "*.Main_*_retail*"
        directory:
          - ""
      final:
        file_names:
          - "BattlefieldGameData.zip"
        supplemental_files:
          - "build.json"
          - "*.Main_*_retail*"
        directory:
          - ""
    patch:
      file_names:
        - "BattlefieldGameData.zip"
      supplemental_files:
        - "build.json"
        - "installerdata.xml"
        - "*.Main_*_retail*"
      directory:
        - ""
    iso:
      retail:
        file_names:
          - "*.iso"
        supplemental_files:
          - "build.json"
          - "*.Main_*_retail*"
        directory:
          - ""
  settings:
    bf-playtest-gnt:
      ww:
        final:
          files:
            sku_id: "e5c96dcf-9c1a-41a3-a395-580d8734f5e4"
            sku_name: "FG - WW (bf-playtest-gnt win64 files final)"
        performance:
          files:
            sku_id: "4ad361ba-dc8c-4b51-9b75-c0b55fc890a6"
            sku_name: "FG - WW (bf-playtest-gnt win64 files performance)"
        retail:
          digital:
            sku_id: "fa0bb9fa-951c-4d77-9925-5f3a8a1798a2"
            sku_name: "FG - WW (bf-playtest-gnt win64 digital retail)"
    bf-playtest-san:
      ww:
        final:
          files:
            sku_id: "74599c37-1b81-4ff5-b373-f6078f17a74a"
            sku_name: "FG - WW (bf-playtest-san win64 files final)"
        performance:
          files:
            sku_id: "a0b57d0d-8fc5-4308-8735-fad31c34a1ae"
            sku_name: "FG - WW (bf-playtest-san win64 files performance)"
        retail:
          digital:
            sku_id: "ee14d3e2-24b9-4415-9641-68893e629e8a"
            sku_name: "FG - WW (bf-playtest-san win64 digital retail)"
    bf-stage:
      ww:
        final:
          files:
            sku_id: "a4bcfb81-2cc9-4261-ad48-e78146fe546e"
            sku_name: "FG - WW (bf-stage win64 files final)"
    kin-upgrade:
      ww:
        final:
          files:
            sku_id: "a369e4fe-d349-4a1e-bfb8-38bc5e27515d"
            sku_name: "FG - WW (kin-upgrade win64 files final)"
    bf-trunk:
      ww:
        final:
          files:
            sku_id: "af70d8d0-fd9b-496f-b763-a275a64d9d60"
            sku_name: "FG - WW (bf-trunk win64 files final)"
    san-dev:
      ww:
        final:
          files:
            sku_id: "cf16d009-c802-4003-8b30-a4581018f2fa"
            sku_name: "FG - WW (san-dev win64 files final)"
    san-proto-x:
      ww:
        final:
          files:
            sku_id: "d0732f06-7a62-434a-8881-568497afab73"
            sku_name: "FG - WW (gnt-event-1)"

xbsx:
  content:
    files:
      file_names:
        - "builtLevels.json"
        - "*.buildlayout"
        - "*.BuildSettings"
        - "Logo.png"
        - "*.dll"
        - "SmallLogo.png"
        - "SplashScreen.png"
        - "StoreLogo.png"
        - "WideLogo.png"
        - "*.Main_Xbsx_*.exe"
        - "MicrosoftGame.config"
        - "gameos.xvd"
        - "nsal.json"
        - "package.mft"
        - "resources.pri"
        - "build.json"
      supplemental_files:
        - "*.xml"
      directory:
        - "Config"
        - "Data"
        - "Scripts"
    digital:
      file_names:
        - "*appxmanifest.xml"
        - "MicrosoftGame.config"
        - "*neutral__*.xvc"
      supplemental_files: #Removed PDBs since it was too big and failing the Shift Submission. Recommended by Kalle
        - "build.json"
        - "*.ekb"
        - "*neutral__*[!c]" # This is kind of a hack. glob.glob doesn't support do not match string; only set of characters.
      directory:
        - ""
  settings:
    bf-playtest-gnt:
      ww:
        final:
          files:
            sku_id: "6112b033-7d29-481e-ab3f-b8a619023fda"
            sku_name: "FG - WW (bf-playtest-gnt xbsx files final)"
        retail:
          digital:
            sku_id: "9b37c94f-745b-4d9c-b7bf-0db8345416c0"
            sku_name: "FG - WW (bf-playtest-gnt xbsx digital retail)"
    bf-playtest-san:
      ww:
        final:
          files:
            sku_id: "e0c10950-0900-4761-b3ee-df07834f6672"
            sku_name: "FG - WW (bf-playtest-san xbsx files final)"
        retail:
          digital:
            sku_id: "fb68d3fa-68fc-4fc5-b7e6-0cfba433fd7f"
            sku_name: "FG - WW (bf-playtest-san xbsx digital retail)"
    bf-stage:
      ww:
        final:
          files:
            sku_id: "81b707b4-cf10-4cd5-8174-c9390d6ed371"
            sku_name: "FG - WW (bf-stage xbsx files final)"
    kin-upgrade:
      ww:
        final:
          files:
            sku_id: "5b12d66e-efb3-4343-b878-cff6643b1245"
            sku_name: "FG - WW (kin-upgrade xbsx files final)"
    bf-trunk:
      ww:
        final:
          files:
            sku_id: "fd3c22a9-d613-4c08-8585-1cf74c198b87"
            sku_name: "FG - WW (bf-trunk xbsx files final)"
    bf-trunk-retail:
      ww:
        retail:
          digital:
            sku_id: "988003aa-8465-445d-bfa4-d86c6268f26a"
            sku_name: "FG - WW (bf-trunk xbsx digital retail)"
    bf-trunk-editor-release:
      ww:
        final:
          digital:
            sku_id: "cad5e509-d7d4-4699-a41d-20124fed9e5d"
            sku_name: "FG - WW (bf-trunk-er xbsx digital final)"
          files:
            sku_id: "7d7e306c-68a4-4455-a238-becc5be08ed5"
            sku_name: "FG - WW (bf-trunk-er xbsx files final)"
        retail:
          digital:
            sku_id: "f4b3417c-e01b-4524-8b0d-aa4250f9dfa1"
            sku_name: "FG - WW (bf-trunk-er xbsx digital retail)"
    san-dev:
      ww:
        final:
          files:
            sku_id: "760402e1-13f3-4f0e-b6fe-5957df8fcc20"
            sku_name: "FG - WW (san-dev xbsx files final)"
offsite_basic_drone:
  content:
    file_names:
      - '*'
    supplemental_files:
      - ""
  settings:
    bf-trunk-editor-release:
        sku_id: "e8b88433-7c88-424f-bec7-09b4c76c3518"
        distribution_type: "ExternalTechPartners"
        sku_name: "Tool - WW Us (bf-trunk-editor-release Off Drone Build)"
    bf-trunk:
        sku_id: "ceb59471-915c-4fd2-85d1-bfea4d96e4c5"
        distribution_type: "ExternalTechPartners"
        sku_name: "Tool - WW (bf-trunk Offsite Drone Build)"
