[{"_index": "bilbo", "_type": "build", "_id": "\\\\filer.dice.ad.ea.com\\builds\\kingston\\frosty\\battlefieldgame\\dev-na-dice-next-build\\4359644\\code-branch\\10970596\\xbsx\\files\\ww\\final", "_score": null, "_source": {"data_changelist": "4359644", "data_branch": "dev-na-dice-next-build", "code_changelist": "10970596", "code_branch": "code-branch", "dataset": "kindata", "region": "ww", "platform": "xbsx", "config": "final", "package_type": "files", "licensee_id": "BattlefieldGame", "type": "frosty", "created": "2021-04-15T17:35:08.561821", "location": "dice", "shift": {"uploaded": "2021-04-15 23:13"}, "updated": "2021-04-15T22:13:14.758999"}, "sort": [1618508108561]}, {"_index": "bilbo", "_type": "build", "_id": "\\\\filer.dice.ad.ea.com\\builds\\kingston\\frosty\\battlefieldgame\\dev-na-dice-next-build\\4357741\\code-branch\\10970596\\win64\\files\\ww\\final", "_score": null, "_source": {"data_changelist": "4357741", "data_branch": "dev-na-dice-next-build", "code_changelist": "10970596", "code_branch": "code-branch", "dataset": "kindata", "region": "ww", "platform": "win64", "config": "final", "package_type": "files", "licensee_id": "BattlefieldGame", "type": "frosty", "created": "2021-04-14T20:06:30.297446", "location": "dice", "shift": {"uploaded": "2021-04-15 16:10"}, "updated": "2021-04-15T15:10:36.312590"}, "sort": [1618430790297]}]