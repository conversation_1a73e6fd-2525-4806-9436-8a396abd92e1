"""
register_outage_metric.py

Register outage data in ElasticSearch
"""
import os
import datetime

from elasticsearch import Elasticsearch

import click

from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import LOGGER, SETTINGS
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command("register-outage-metric", short_help="Register outage data in ElasticSearch.")
@click.option("--end-date", help="End date of measuring period, YYMMDD.", required=True)
@click.option("--start-date", help="Start date of measuring period, YYMMDD.", required=True)
@click.option("--major-outage", help="Hours of major outage.", required=True)
@click.option("--significant-outage", help="Hours of significant outage.", required=True)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(_, end_date, start_date, major_outage, significant_outage):
    """
    Log outage metrics to elasticsearch
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    project = SETTINGS.get("project_name")
    elasticsearch_url = SETTINGS.get("jenkins_metrics_url")
    elasticsearch_port = SETTINGS.get("jenkins_metrics_port")

    LOGGER.info("Connecting to Elasticsearch @ {}:{}".format(elasticsearch_url, elasticsearch_port))
    es_connection = Elasticsearch([elasticsearch_url], port=elasticsearch_port)
    LOGGER.info("Connected: {}".format(es_connection.info()))

    current_date = datetime.datetime.utcnow()
    index_name = "build-outages_{}.{:0>2}".format(current_date.year, current_date.month)

    data = {
        "end_date": datetime.datetime.strptime(end_date, "%y%m%d"),
        "start_date": datetime.datetime.strptime(start_date, "%y%m%d"),
        "project": project,
        "major_outage": major_outage,
        "significant_outage": significant_outage,
        "created": datetime.datetime.utcnow().isoformat(),
    }

    es_id = os.path.join(project, "outage", start_date, end_date)

    LOGGER.info("registering {}".format(es_id))
    es_connection.index(index=index_name, id=es_id, body=data)
