import ast
import os
from jinja2 import Environment, FileSystemLoader


def literal_eval(node):
    try:
        return ast.literal_eval(node)
    except ValueError:
        return ast.unparse(node)


def extract_click_info(node):
    click_info = []
    for decorator in node.decorator_list:
        if isinstance(decorator, ast.Call) and isinstance(decorator.func, ast.Attribute):
            if decorator.func.value.id == "click":
                arg_info = {
                    "function": decorator.func.attr,
                    "args": [literal_eval(arg) for arg in decorator.args],
                    "kwargs": {kw.arg: literal_eval(kw.value) for kw in decorator.keywords},
                }
                click_info.append(arg_info)
    return click_info


def parse_python_file(filename):
    with open(filename, "r") as file:
        tree = ast.parse(file.read())
    for node in ast.walk(tree):
        if isinstance(node, ast.FunctionDef) and node.name == "cli":
            return extract_click_info(node)
    return []


def parse_python_files_in_folder(folder):
    files_info = {}
    for root, dirs, files in os.walk(folder):
        for file_name in files:
            if file_name.endswith(".py"):
                file_path = os.path.join(root, file_name)
                click_info = parse_python_file(file_path)
                if click_info:
                    files_info[file_name.removesuffix(".py")] = click_info

                    print(file_name)
                    for arg_info in click_info:
                        print(arg_info)
                    print()
    return files_info


def clean_leftover_templates(output_path, found_click_info):
    for file_name in os.listdir(output_path):
        if file_name.endswith(".jinja"):
            file_path = os.path.join(output_path, file_name)
            if file_name.removesuffix(".jinja") not in found_click_info.keys():
                os.remove(file_path)


def generate_job_generator_templates():
    elipy_scripts_dir = os.path.join(os.path.dirname(__file__), "../dice_elipy_scripts")
    elipy_scripts_click_info = parse_python_files_in_folder(elipy_scripts_dir)

    environment = Environment(loader=FileSystemLoader("./templates/"))
    template = environment.get_template("job_generator_template.jinja")
    output_path = os.path.join(os.path.dirname(__file__), "./output")

    for script_name, click_elements in elipy_scripts_click_info.items():
        context = {
            "script_name": script_name,
            "click_elements": click_elements,
        }
        try:
            content = template.render(context)
            output_file_path = os.path.join(output_path, f"{script_name}.jinja")
            with open(output_file_path, "w") as output_file:
                output_file.write(content)
            print(f"OK!     {script_name}")
        except Exception as error:
            print(f"NOT OK! {script_name}: {error}")

    clean_leftover_templates(output_path, elipy_scripts_click_info)


if __name__ == "__main__":
    generate_job_generator_templates()
