"""
misc.py

utility functions for testing
"""
import os
from typing import List
from elipy2.config import Config<PERSON>anager
from elipy2.avalanche_web_api.server import Server
import glob


def elipy_config_managers():
    """
    Fixture to return a list of config managers for testing
    """
    config_pattern = os.path.join(os.path.dirname(__file__), "..", "..", "yml", "elipy*.yml")
    config_managers = []
    config_files = glob.glob(config_pattern)
    for config_path in config_files:
        config_managers.append(ConfigManager(path=config_path))
    return config_managers


def avalanche_connection_test(hosts: List[str]):
    errors = []
    unique_hosts = list(set(hosts))
    for host in unique_hosts:
        try:
            av_server = Server(f"http://{host}:1338")
            av_server.get_status()
        except Exception as exec:
            errors.append(exec)

    if errors:
        raise Exception(
            (
                f"Failed to connect to {len(errors)} "
                f"avalanche servers out of {len(unique_hosts)}"
            ),
            errors,
        )
