{#
    Command:
        patch_frosty
            short_help: Deploys a playable game build with both binaries and data.

    Arguments:
        platform
        config

    Required variables:
        disc_data_branch
            help: Which branch the disc data was built on.
            required: True
        disc_data_changelist
            required: True
            help: Which changelist the disc data was deployed from.
        disc_code_branch
            help: Which branch the disc code was built on.
            required: True
        disc_code_changelist
            required: True
            help: Which changelist the disc code was deployed from.

    Optional variables:
        data_directory
            help: Which data directory to use for the working data set.
        code_branch
            help: Branch/stream to fetch the code/binary build from.
        code_changelist
            help: Changelist of binaries to fetch.
        data_branch
            help: Branch/stream that data is coming from.
        data_changelist
            help: Changelist of data being used.
        region
            help: Which region to deploy for (default is ww).
            default: ww
        first_patch/__not_first_patch
            default: False
            help: Flag for building the first patch.
        use_win64trial/__no_win64trial
            default: False
            help: Flag for using win64 trial for win64 patches.
        dry_run
            is_flag: True
            help: Build code without deploying.
        use_recompression_cache
            is_flag: True
            help: Alternative Avalanche server to use for the recompression cache.
        increase_version_by
            default: 1
            help: Number which we should increase the ps4 appversion with.
        patch_data_branch
            required: False
            help: Which branch the latest live patch data was built on.
        patch_data_changelist
            required: False
            help: Which changelist the latest live patch data was deployed from.
        patch_code_branch
            required: False
            help: Which branch the latest live patch code was built on.
        patch_code_changelist
            required: False
            help: Which changelist the latest live patch code was deployed from.
        disable_frosty_symbol_upload
            is_flag: True
            default: False
            help: Disable Frosty Symbol Upload.
        skip_streaming_install_package
            is_flag: True
            help: set STREAMING_INSTALL_CREATE_SUBMISSION_PACKAGES to false.
        password
            default: None
            help: User credentials to authenticate to package server.
        email
            default: None
            help: User email to authenticate to package server.
        domain_user
            default: None
            help: The user to authenticate to package server as DOMAIN\user
        enable_eac
            is_flag: True
            help: Enable EasyAntiCheat.
        same_baseline_config
            is_flag: True
            help: Use same config for the baseline and the patch (default is retail baseline).
        licensee
            multiple: True
            default: None
            help: Licensee to use.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    patch_frosty
    platform {{ platform }}
    config {{ config }}
    --disc-data-branch {{ disc_data_branch }}
    --disc-data-changelist {{ disc_data_changelist }}
    --disc-code-branch {{ disc_code_branch }}
    --disc-code-changelist {{ disc_code_changelist }}
    {%- if data_directory %}
    --data-directory {{ data_directory }}
    {%- endif %}
    {%- if code_branch %}
    --code-branch {{ code_branch }}
    {%- endif %}
    {%- if code_changelist %}
    --code-changelist {{ code_changelist }}
    {%- endif %}
    {%- if data_branch %}
    --data-branch {{ data_branch }}
    {%- endif %}
    {%- if data_changelist %}
    --data-changelist {{ data_changelist }}
    {%- endif %}
    {%- if region %}
    --region {{ region }}
    {%- endif %}
    {%- if first_patch/__not_first_patch %}
    --first-patch/--not-first-patch {{ first_patch/__not_first_patch }}
    {%- endif %}
    {%- if use_win64trial/__no_win64trial %}
    --use-win64trial/--no-win64trial {{ use_win64trial/__no_win64trial }}
    {%- endif %}
    {%- if dry_run %}
    --dry-run {{ dry_run }}
    {%- endif %}
    {%- if use_oreans %}
    --use-oreans {{ use_oreans }}
    {%- endif %}
    {%- if use_recompression_cache %}
    --use-recompression-cache {{ use_recompression_cache }}
    {%- endif %}
    {%- if increase_version_by %}
    --increase-version-by {{ increase_version_by }}
    {%- endif %}
    {%- if patch_data_branch %}
    --patch-data-branch {{ patch_data_branch }}
    {%- endif %}
    {%- if patch_data_changelist %}
    --patch-data-changelist {{ patch_data_changelist }}
    {%- endif %}
    {%- if patch_code_branch %}
    --patch-code-branch {{ patch_code_branch }}
    {%- endif %}
    {%- if patch_code_changelist %}
    --patch-code-changelist {{ patch_code_changelist }}
    {%- endif %}
    {%- if disable_frosty_symbol_upload %}
    --disable-frosty-symbol-upload {{ disable_frosty_symbol_upload }}
    {%- endif %}
    {%- if skip_streaming_install_package %}
    --skip-streaming-install-package {{ skip_streaming_install_package }}
    {%- endif %}
    {%- if password %}
    --password {{ password }}
    {%- endif %}
    {%- if email %}
    --email {{ email }}
    {%- endif %}
    {%- if domain_user %}
    --domain-user {{ domain_user }}
    {%- endif %}
    {%- if enable_eac %}
    --enable-eac {{ enable_eac }}
    {%- endif %}
    {%- if same_baseline_config %}
    --same-baseline-config {{ same_baseline_config }}
    {%- endif %}
    {%- if licensee %}
    --licensee {{ licensee }}
    {%- endif %}
  displayName: elipy patch_frosty
