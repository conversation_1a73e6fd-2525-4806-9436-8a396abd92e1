package com.ea.project.bctch1.branchsettings

import com.ea.lib.LibPerforce
import com.ea.lib.jobs.LibCustomScript
import com.ea.lib.jobsettings.ShiftSettings
import com.ea.lib.model.branchsettings.CustomScriptConfiguration
import com.ea.lib.model.branchsettings.PipelineDeterminismTestConfiguration
import com.ea.project.bctch1.BctCh1

class CH1_code_dev {
    // Settings for jobs
    static Class project = BctCh1
    static Map general_settings = [
        dataset                                : project.dataset,
        elipy_call                             : project.elipy_call + ' --use-fbenv-core',
        elipy_install_call                     : project.elipy_install_call,
        frostbite_licensee                     : project.frostbite_licensee,
        workspace_root                         : project.workspace_root,
        azure_elipy_call                       : project.azure_elipy_call,
        azure_elipy_install_call               : project.azure_elipy_install_call,
        azure_workspace_root                   : project.azure_workspace_root,
        azure_fileshare                        : [
            additional_tools_to_include: ['frostedtests', 'win64'],
            secret_context             : 'glacier_azure_fileshare',
            target_build_share         : 'bfglacier',
        ],
        job_label_statebuild                   : 'statebuild',
        gametool_settings                      : [
            gametools: [
                (LibPerforce.GAMETOOL_ICEPICK)                    : [
                    config        : 'release',
                    framework_args: ['-G:frostbite.use-prebuilt-native-binaries=true'],
                ],
                (LibPerforce.GAMETOOL_FROSTBITE_DATABASE_UPGRADER): [],
                (LibPerforce.GAMETOOL_FROSTYISOTOOL)              : [],
                (LibPerforce.GAMETOOL_DRONE)                      : [],
                (LibPerforce.GAMETOOL_FRAMEWORK)                  : [],
                (LibPerforce.GAMETOOL_FBENV)                      : [],
            ],
        ],
        coverity_settings                      : [
            credentials            : 'monkey.bct',
            ess_secrets_credential : 'bct-secrets-secret-id',
            ess_secrets_key        : 'BCT_SECRETS_SECRET_ID',
            run_coverity           : true,
            trigger                : '@daily',
            job_label              : 'ch1_code_dev_coverity',
            artifactory_source_path: 'bctinfrax-bctinfraxgeneric-generic-federated/tools/coverity/cov-analysis-win64-2024.12.0.zip',
            p4_code_creds          : 'bct-la-p4',
            p4_code_server         : 'dicela-p4edge-fb.la.ad.ea.com:2001'
        ],
        autotest_remote_settings               : [
            eala     : [
                p4_code_creds : 'bct-la-p4',
                p4_code_server: 'dicela-p4edge-fb.la.ad.ea.com:2001',
                p4_data_creds : 'bct-la-p4',
                p4_data_server: 'dicela-p4edge-fb.la.ad.ea.com:2001',
            ],
            dice     : [
                p4_code_creds : 'perforce-battlefield01',
                p4_code_server: 'dice-p4buildedge02-fb.dice.ad.ea.com:2001',
                p4_data_creds : 'perforce-battlefield01',
                p4_data_server: 'dice-p4buildedge02-fb.dice.ad.ea.com:2001',
            ],
            criterion: [
                p4_code_creds : 'perforce-battlefield-criterion',
                p4_code_server: 'oh-p4edge-fb.eu.ad.ea.com:2001',
                p4_data_creds : 'perforce-battlefield-criterion',
                p4_data_server: 'oh-p4edge-fb.eu.ad.ea.com:2001',
            ]
        ],
        pipeline_determinism_test_configuration: new PipelineDeterminismTestConfiguration(
            cronTrigger: 'H 6 * * *',
            referenceJob: '.data.start',
            label: 'pipeline_determinism',
            timeoutHours: 24,
        ),
        custom_script                          : [
            (LibCustomScript.PORTAL_MAKE_SDK.jobName): new CustomScriptConfiguration(
                scriptPath: LibCustomScript.PORTAL_MAKE_SDK.scriptPath,
                executable: LibCustomScript.PORTAL_MAKE_SDK.executable,
                executableArgs: LibCustomScript.PORTAL_MAKE_SDK.executableArgs,
                defaultScriptArgs: LibCustomScript.PORTAL_MAKE_SDK.defaultScriptArgs,
                label: 'statebuild',
                jobName: LibCustomScript.PORTAL_MAKE_SDK.jobName,
            ),
        ],
    ]
    static Map code_settings = [
        deploy_frostedtests          : true,
        deploy_tests                 : true,
        fake_ooa_wrapped_symbol      : false,
        skip_code_build_if_no_changes: false,
        slack_channel_code           : [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        statebuild_code              : false,
        statebuild_code_nomaster     : false,
        report_build_version         : ' --reporting-build-version-id %code_changelist%',
        sndbs_enabled                : true,
    ]
    static Map data_settings = [
        deployment_data_branch       : true,
        deployment_data_reference_job: 'CH1-code-dev.data.start',
        enable_lkg_cleaning          : true,
        slack_channel_data           : [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        statebuild_data              : false,
        statebuild_webexport         : false,
        webexport_allow_failure      : true,
        webexport_branch             : true,
        webexport_content_layers     : ['C1S2B1'],
    ]
    static Map frosty_settings = [
        frosty_reference_job: 'CH1-code-dev.deployment-data.start',
        poolbuild_frosty    : false,
        slack_channel_frosty: [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        statebuild_frosty   : true,
        use_linuxclient     : true,
    ]
    static Map standard_jobs_settings = code_settings + data_settings + frosty_settings + [
        asset                         : 'DevLevels',
        custom_tests                  : [
            custom_configs: [
                'online-integration-tests.json',
                'online-integration-tests-ade.json',
                'online-integration-tests-gsintegration.json',
            ],
        ],
        enable_eac                    : true,
        enable_lkg_p4_counters        : true,
        extra_data_args               : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        extra_frosty_args             : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        extra_icepick_args            : '--heartbeat-timeout 600',
        linux_docker_images           : false,
        move_location_parallel        : true,
        new_locations                 : [
            Guildford   : [
                elipy_call_new_location: project.elipy_call_criterion + ' --use-fbenv-core',
            ],
            Montreal    : [
                elipy_call_new_location: project.elipy_call_montreal + ' --use-fbenv-core',
            ],
            RippleEffect: [
                elipy_call_new_location: project.elipy_call_eala + ' --use-fbenv-core',
            ],
        ],
        remote_masters_to_receive_code: [[name: 'bct-preflight-jenkins.cobra.dre.ea.com', allow_failure: false]],
        retry_limit_data              : 1,
        server_asset                  : 'Game/Setup/Build/DevMPLevels',
        shift_branch                  : true,
        shift_reference_job           : 'CH1-code-dev.frosty.start',
        single_stream_smoke           : true,
        skip_icepick_settings_file    : true,
        // smoke_downstream_job          : 'CH1-code-dev.integrate-upgrade-to.CH1-content-dev.start',
        strip_symbols                 : false,
        timeout_hours_data            : 10,
        trigger_string_shift          : 'TZ=Europe/Stockholm\nH 1,7,14,20 * * 1-6\nH 6,13 * * 7',
    ]
    static Map icepick_settings = [
        ignore_icepick_exit_code: false
    ]
    static Map preflight_settings = [
        codepreflight_reference_job: 'CH1-code-dev.code.lastknowngood',
        concurrent_code            : 1,
        enable_custom_cl           : true,
        extra_codepreflight_args   : "--framework-args -D:ondemandp4proxymapfile=${general_settings.workspace_root}\\tnt\\build\\framework\\data\\P4ProtocolOptimalProxyMap.xml --framework-args -D:eaconfig.optimization.ltcg=off --framework-args -G:eaconfig.enablesndbs=true",
        force_rebuild_preflight    : true,
        pre_preflight              : true,
        slack_channel_preflight    : [channels: ['#cobra-build-preflight']],
        statebuild_codepreflight   : false,
        use_icepick_test           : true,
    ]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'win64game', configs: ['final', 'release', 'retail', 'performance']],
        [name: 'tool', configs: [[name: 'release', compile_unit_tests: true, run_unit_tests: true], [name: 'deprecation-test', allow_failure: true, compile_unit_tests: true]]],
        [name: 'win64server', configs: ['final', 'release']],
        [name: 'xbsx', configs: ['final', 'retail', 'release', 'performance']],
        [name: 'ps5', configs: ['final', 'retail', 'release', 'performance']],
        [name: 'linux64server', configs: ['final']],
        [name: 'linux64', configs: ['final']],
    ]
    static List bilbo_move_matrix = [
        [name: 'win64game', configs: ['final', 'release', 'performance']],
        [name: 'tool', configs: [[name: 'release']]],
        [name: 'win64server', configs: ['final', 'release']],
        [name: 'xbsx', configs: ['final', 'release', 'performance']],
        [name: 'ps5', configs: ['final', 'release', 'performance']],
        [name: 'linux64server', configs: ['final']],
        [name: 'linux64', configs: ['final']],
    ]
    static List code_nomaster_matrix = [
        [name: 'win64game', configs: ['retail']],
        [name: 'win64server', configs: ['final']],
        [name: 'ps5', configs: ['final']],
        [name: 'xbsx', configs: ['retail']],
        [name: 'linux64server', configs: ['final']],
        [name: 'tool', configs: ['release']],
    ]
    static List code_stressbulkbuild_matrix = [
        [name: 'tool', configs: ['release']],
    ]
    static List code_downstream_matrix = [
        [name: '.code.lastknowngood', args: ['code_changelist']],
        [name: '.code.p4counterupdater', args: ['code_changelist', 'code_countername']],
        [name: '.code.tool.release.copy-build-to-azure', args: ['code_changelist']],
        [name: '.data.start', args: []],
    ]
    static List data_matrix = [
        [name: 'win64'],
        [name: 'server'],
        [name: 'xbsx'],
        [name: 'ps5'],
        [name: 'linux64'],
        [name: 'validate-frosted', allow_failure: true, deployment_platform: false],
    ]
    static List data_downstream_matrix = [
        [name: '.data.p4cleancounterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.data.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.deployment-data.start', args: []],
        [name: '.frosty.start', args: []],
//        [name: 'CH1-code-dev.integrate-upgrade-to.CH1-content-dev.start', args: []],
    ]
    static List patchdata_matrix = []
    static List frosty_matrix = [
        [name: 'win64', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs performance'],
                                   [format: 'files', config: 'performance', region: 'ww', args: ''],
                                   [format: 'files', config: 'release', region: 'ww', args: '']]],
        [name: 'ps5', variants: [[format: 'files', config: 'final', region: 'dev', args: ' --additional-configs release'],
                                 [format: 'files', config: 'performance', region: 'dev', args: '']]],
        [name: 'xbsx', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release'],
                                  [format: 'files', config: 'performance', region: 'ww', args: '']]],
        [name: 'server', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww', args: ''],
                                         [format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'linux64', variants: [[format: 'files', config: 'final', region: 'ww', args: '']]],
    ]
    static List frosty_downstream_matrix = [
        [name: '.frosty.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.spin.linuxserver.digital.final.ww', args: ['code_changelist', 'data_changelist']],
    ]
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = [
        [name: 'win64game', configs: ['final']],
        [name: 'xbsx', configs: ['performance']],
        [name: 'ps5', configs: ['retail']],
        [name: 'win64server', configs: ['final']],
        [name: 'linux64server', configs: ['final']],
        [name: 'tool', configs: ['release'], sync_code_and_data: true],
    ]
    static List data_preflight_matrix = []
    static List spin_upload_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww']]],
        [name: 'linux64', variants: [[format: 'files', config: 'final', region: 'ww']]],
    ]
    static List shift_upload_matrix = [
        [shifter_type: ShiftSettings.SHIFTER_TYPE_OFFSITE_BASIC_DRONE, args: ['code_changelist']],
    ]
    static List shift_downstream_matrix = [
        [name: '.spin.linux64.files.final.ww', args: ['code_changelist', 'data_changelist']],
    ]
    static List freestyle_job_trigger_matrix = [
        [upstream_job: '.bilbo.register-bfdata-dronebuild', downstream_job: ".shift.${ShiftSettings.SHIFTER_TYPE_OFFSITE_BASIC_DRONE}.start", args: ['code_changelist']],
    ]
    static List azure_uploads_matrix = [
        [platform: 'tool', content_type: ['code'], config: ['release']]
    ]
    static List pipeline_determinism_test_matrix = [
        [platform: 'win64', job_label: 'CH1-code-dev && pipeline_determinism && win64'],
        // [platform: 'win64', job_name: 'debugdb', job_label: 'CH1-code-dev && pipeline_determinism && debugdb' additional_script_args: '-add_cook_args=\"-forceDebugTarget -Pipeline.MaxConcurrencyThrottleMemoryThreshold 0.8\"'],
        // [platform: 'ps5', job_label: 'CH1-code-dev && pipeline_determinism && ps5', additional_script_args: '-add_cook_args=\"-Pipeline.MaxConcurrencyThrottleMemoryThreshold 0.8\"'],
        // [platform: 'xbsx', job_label: 'CH1-code-dev && pipeline_determinism && xbsx', additional_script_args: '-add_cook_args=\"-Pipeline.MaxConcurrencyThrottleMemoryThreshold 0.8\"'],
    ]
}
