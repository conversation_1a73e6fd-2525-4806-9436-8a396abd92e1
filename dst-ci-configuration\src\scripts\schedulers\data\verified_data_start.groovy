package scripts.schedulers.data

import com.ea.lib.LibJenkins
import com.ea.project.GetBranchFile

def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
def project = ProjectClass(env.project_name)

/**
 * verified_data_start.groovy
 */
pipeline {
    agent { label '(scheduler && master) || scheduler || executor_agent' }
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Get changelist from Perforce') {
            steps {
                script {
                    def settings_map = branchfile.general_settings + branchfile.standard_jobs_settings
                    P4PreviewData(project, 'stream', env.data_folder, env.data_branch, env.non_virtual_data_folder, env.non_virtual_data_branch, settings_map)
                }
            }
        }
        stage('Trigger verified data jobs') {
            steps {
                script {
                    def last_good_code = LibJenkins.getLastStableCodeChangelist(env.verified_data_reference_job)
                    def code_changelist = params.code_changelist ?: last_good_code
                    def data_changelist = params.data_changelist ?: env.P4_CHANGELIST
                    def clean_data = params.clean_data

                    def args = [
                        string(name: 'code_changelist', value: code_changelist),
                        string(name: 'data_changelist', value: data_changelist),
                        string(name: 'clean_data', value: clean_data),
                    ]
                    def jobs = [:]

                    def inject_map = [
                        'code_changelist': code_changelist,
                        'data_changelist': data_changelist,
                    ]
                    EnvInject(currentBuild, inject_map)
                    currentBuild.displayName = env.JOB_NAME + '.' + data_changelist + '.' + code_changelist

                    def verified_data_matrix = branchfile.data_matrix

                    def final_result = Result.SUCCESS
                    def continue_build = true
                    for (def run = 0; run <= env.retry_limit.toInteger(); run++) { // Retry failed jobs if retry_limit > 0.
                        jobs = [:]
                        final_result = Result.SUCCESS
                        for (platform in verified_data_matrix) {
                            Boolean allow_failure = false
                            def platform_name = platform
                            if (platform instanceof Map) {
                                allow_failure = platform.allow_failure ?: false
                                platform_name = platform.name
                            }
                            def job_name = env.branch_name + '.verified-data.' + env.dataset + '.' + platform_name
                            if (NeedsRebuildData(job_name, code_changelist, data_changelist)) {
                                if (run > 0 && IsGameFailure(job_name, allow_failure)) {
                                    if (allow_failure == false) {
                                        final_result = Result.FAILURE
                                        // Set pipeline as failed if there are jobs from IsGameFailure category.
                                        continue_build = false
                                    }
                                    break
                                } else {
                                    jobs[job_name] = {
                                        def downstream_job = build(job: job_name, parameters: args, propagate: false)
                                        final_result = final_result.combine(Result.fromString(downstream_job.result))
                                        LibJenkins.printFailureMessage(this, downstream_job, allow_failure)
                                        LibJenkins.printRunningJobs(this)
                                    }
                                }
                            }
                        }
                        if (env.webexport_branch.toBoolean() == true) {
                            def webexport_job = env.branch_name + '.' + env.dataset + '.webexport.win64'
                            if (NeedsRebuildData(webexport_job, code_changelist, data_changelist)) {
                                jobs[webexport_job] = {
                                    def downstream_job = build(job: webexport_job, parameters: args, propagate: false)
                                    final_result = final_result.combine(Result.fromString(downstream_job.result))
                                    LibJenkins.printRunningJobs(this)
                                }
                            }
                        }
                        if (continue_build == false) {
                            break
                        }
                        parallel(jobs)
                        if (final_result == Result.SUCCESS) {
                            break
                        }
                    }

                    if (final_result == Result.SUCCESS) { // Only upload successful builds
                        build(job: env.branch_name + '.bilbo.register-' + env.dataset + '-dronebuild', parameters: args, propagate: false, wait: false)
                    }

                    if (final_result == Result.SUCCESS && env.datapreflight_info_sending.toBoolean() == true) {
                        // Only set this for successful builds
                        build(job: env.branch_name + '.data.lastknowngood', parameters: args, propagate: false)
                    }

                    currentBuild.result = final_result.toString()

                    def data_downstream_matrix = branchfile.data_downstream_matrix
                    if (currentBuild.result.toString() == 'SUCCESS') { // Only trigger after successful builds
                        echo 'Triggering downstream job(s):'
                        for (extra_job in data_downstream_matrix) {
                            def extra_job_name = extra_job
                            def extra_job_args = []
                            if (extra_job instanceof Map) {
                                extra_job_name = extra_job.name ?: extra_job_name
                                if (extra_job.args != null) {
                                    if (extra_job.args.contains('code_changelist')) {
                                        extra_job_args += string(name: 'code_changelist', value: code_changelist)
                                    }
                                    if (extra_job.args.contains('data_changelist')) {
                                        extra_job_args += string(name: 'data_changelist', value: data_changelist)
                                    }
                                }
                            }
                            build(job: extra_job_name, wait: false, parameters: extra_job_args, propagate: false)
                        }
                    }

                    def slack_settings = branchfile.standard_jobs_settings?.slack_channel_verified_data
                    SlackMessageNew(currentBuild, slack_settings, project.short_name)

                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
}
