"""
integrate_compile_upgrade_cook.py
"""
import os
import click
from elipy2 import core, local_paths, LOGGER, p4, running_processes, frostbite_core
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics
from elipy2.exceptions import AutomaticP4MergeResolveException
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.integration_utils import compile_code, cook_data, submit_integration
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from dice_elipy_scripts.utils.snowcache_utils import SNOWCACHE_MODES


@click.command(
    "integrate_compile_upgrade_cook", short_help="Runs a code integration, with extra validation."
)
@click.option("--assets", required=True, multiple=True, help="Assets to use for data cook.")
@click.option("--code-changelist", required=True, help="Code changelist in Perforce.")
@click.option(
    "--code-clean", default="false", help="Delete TnT/Local if --code-clean true is passed."
)
@click.option(
    "--data-clean", default="false", help="Clean Avalanche if --data-clean true is passed."
)
@click.option("--data-directory", required=True, help="Directory for game data.")
@click.option("--data-platform", default="win64", help="Platform to use for data cook.")
@click.option(
    "--domain-user",
    default=None,
    help="User to authenticate to package server as, e.g. DOMAIN\\user.",
)
@click.option("--email", default=None, help="User email to authenticate to package server.")
@click.option("--framework-args", multiple=True, help="Framework arguments for the code build.")
@click.option(
    "--ignore-source-history",
    is_flag=True,
    help="Ignore source file history (sets the Perforce integrate flag -Di).",
)
@click.option(
    "--licensee", multiple=True, default=None, help="Game licensee, e.g. BattelfieldGame."
)
@click.option("--mapping", required=True, help="Mapping to use for the integration.")
@click.option("--p4-client-code", required=True, help="Perforce workspace for code.")
@click.option("--p4-client-data", required=True, help="Perforce workspace for data.")
@click.option("--p4-port-code", required=True, help="Perforce server for code.")
@click.option("--p4-port-data", required=True, help="Perforce server for data.")
@click.option("--p4-user-code", default=None, help="Perforce user name for code.")
@click.option("--p4-user-data", default=None, help="Perforce user name for data.")
@click.option(
    "--password",
    default=None,
    help="User credentials to authenticate to package server.",
)
@click.option("--pipeline-args", multiple=True, help="Pipeline arguments for the data build.")
@click.option("--source-branch", required=True, help="Source branch for code.")
@click.option("--submit/--no-submit", default=True, help="Submit to Perforce or not.")
@click.option("--submit-message", default="", help="Message to include in the submit message.")
@click.option("--use-snowcache", is_flag=True, help="Use snowcache when building code.")
@click.option(
    "--snowcache-mode-override",
    type=click.Choice(SNOWCACHE_MODES, case_sensitive=False),
    default="",
    help="Override the logically evaluated snowcache mode with this",
)
@click.option(
    "--p4-ignore",
    default=".p4ignore",
    help=".p4ignore file to set, if not will set the default of .p4ignore",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    assets,
    code_changelist,
    code_clean,
    data_clean,
    data_directory,
    data_platform,
    domain_user,
    email,
    framework_args,
    ignore_source_history,
    licensee,
    mapping,
    p4_client_code,
    p4_client_data,
    p4_port_code,
    p4_port_data,
    p4_user_code,
    p4_user_data,
    password,
    pipeline_args,
    source_branch,
    submit,
    submit_message,
    use_snowcache,
    snowcache_mode_override,
    p4_ignore,
):
    """
    Runs a code integration.
    Includes compilation of code, and upgrading och cooking of data, for validation.
    """
    # Adding sentry tags.
    add_sentry_tags(__file__)

    # Clean up before running the job.
    running_processes.kill()
    core.clean_temp()
    core.close_file_handles(local_paths.get_packages_path())
    core.close_file_handles(local_paths.get_tnt_localpackages_path())

    # Initialize
    perforce_code = p4.P4Utils(port=p4_port_code, client=p4_client_code, user=p4_user_code)
    perforce_data = p4.P4Utils(port=p4_port_data, client=p4_client_data, user=p4_user_data)

    # Revert the Perforce state.
    perforce_code.revert(quiet=True)
    perforce_data.revert(quiet=True)

    # Integrate using the provided mapping.
    LOGGER.info(
        "Performing integration using client %s on server %s with branch mapping %s.",
        p4_client_code,
        p4_port_code,
        mapping,
    )
    perforce_code.integrate(
        mapping=mapping,
        to_revision=code_changelist,
        ignore_source_history=ignore_source_history,
    )

    # Resolve the result from the integration.
    resolve_mode = "m"  # Accept the result of the automatic merge
    perforce_code.resolve(mode=resolve_mode)

    if perforce_code.unresolved():
        LOGGER.error("Unable to automatically resolve the result. Reverting and aborting!")
        perforce_code.revert(quiet=True)
        raise AutomaticP4MergeResolveException

    try:
        compile_code(
            licensee=list(licensee),
            password=password,
            email=email,
            domain_user=domain_user,
            framework_args=list(framework_args),
            overwrite_p4config=True,
            clean=code_clean.lower() == "true",
            use_snowcache=use_snowcache,
            snowcache_mode_override=snowcache_mode_override,
        )

        # Run a local data upgrade.
        core.ensure_p4_config(
            root_dir=frostbite_core.get_game_data_dir(),
            port=p4_port_data,
            client=p4_client_data,
            user=p4_user_data,
            ignore=p4_ignore,
        )
        os.environ["P4CONFIG"] = ".p4config"

        try:
            upgrade_script = os.path.join(
                frostbite_core.get_tnt_root(), "Code", "DICE", "UpgradeScripts", "UpgradeLocal.bat"
            )
            core.run([upgrade_script], print_std_out=True)
        except Exception:
            perforce_data.clean(folder=frostbite_core.get_game_data_dir() + "/...")
            raise

        # Cook assets.
        cook_data(
            assets=list(assets),
            data_directory=data_directory,
            platform=data_platform,
            clean_avalanche_cook=data_clean.lower() == "true",
            pipeline_args=list(pipeline_args),
            use_local_code=True,
        )

        # Submit the integrated code to Perforce.
        perforce_message = f"Integrated from {source_branch}@{code_changelist}."
        if submit_message:
            perforce_message += f"\n{submit_message}"
        submit_integration(p4_object=perforce_code, submit_message=perforce_message, submit=submit)
    finally:
        perforce_code.revert(quiet=False)
        perforce_data.revert(quiet=False)
