"""
bilbo_register_autotest.py
"""

import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import filer_paths, LOGGER, SETTINGS, build_metadata_utils
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command("bilbo_register_autotest", short_help="Registers an autotest build in Bilbo.")
@click.option("--code-branch", help="Perforce code branch/stream name.", required=True)
@click.option(
    "--code-changelist",
    required=True,
    help="Changelist number of code build used to verify data.",
)
@click.option("--data-branch", help="Perforce data branch/stream name.", required=True)
@click.option("--data-changelist", help="Changelist number of data built.", required=True)
@click.option("--test-definition", help="Which test to register.", required=True)
@click.option("--test-status", help="The status of the test.")
@click.option(
    "--run-bilbo",
    default=False,
    help="Whether or not to report test results to the configured metadata services",
    required=False,
)
@click.option(
    "--register-smoke", default=False, help="Whether to mark the build as smoked.", required=False
)
@click.option(
    "--extra-location",
    multiple=True,
    required=False,
    help="Additional locations to register the autotest",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    code_branch,
    code_changelist,
    data_branch,
    data_changelist,
    test_definition,
    test_status,
    run_bilbo,
    register_smoke,
    extra_location,
):
    """
    Registers an autotest build in the configured metadata services.
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    if run_bilbo:
        test_location = SETTINGS.location
        locations = list(extra_location)
        locations.insert(0, test_location)

        metadata_manager = build_metadata_utils.setup_metadata_manager(
            SETTINGS.get("bilbo_url", location=test_location)
        )

        source = filer_paths.get_code_build_root_path(
            code_branch, code_changelist, location=test_location
        )

        LOGGER.info(
            "Registering Autotest build on code %s.CL%s with data %s.CL%s at location %s",
            code_branch,
            code_changelist,
            data_branch,
            data_changelist,
            test_location,
        )
        LOGGER.info("Registering test %s with status %s", test_definition, test_status)

        metadata_manager.set_autotest_category_status(
            source,
            data_changelist=data_changelist,
            data_branch=data_branch,
            test_category=test_definition,
            status=test_status,
        )

        if register_smoke:
            # Check if code is smoked in the original location
            code_smoked_at_origin = False

            # First, check if the code is smoked at original location
            query_string = (
                f"type.keyword:drone AND branch.keyword:{code_branch} "
                f"AND changelist.keyword:{code_changelist}"
            )
            builds = list(metadata_manager.get_all_builds_query_string(query_string=query_string))

            # Check if any of the builds at original location are smoked
            for build in builds:
                if build.source.get("build_promotion_level") == "qa_verified":
                    code_smoked_at_origin = True
                    LOGGER.info(
                        "Code build is already smoked at original location %s", test_location
                    )
                    break

            # Process all locations (including original location)
            for location in locations:
                metadata_manager = build_metadata_utils.setup_metadata_manager(
                    SETTINGS.get("bilbo_url", location=location), location=location
                )

                query_string = (
                    f"type.keyword:drone AND branch.keyword:{code_branch} "
                    f"AND changelist.keyword:{code_changelist}"
                )
                builds = list(
                    metadata_manager.get_all_builds_query_string(query_string=query_string)
                )

                # Only register build as smoked at locations if it exists in bilbo already
                if not builds:
                    LOGGER.warning("No builds found at %s to tag as smoked.", location)
                else:
                    remote_path = filer_paths.get_code_build_root_path(
                        code_branch, code_changelist, location=location
                    )

                    # Always mark data as qa_verified
                    metadata_manager.tag_data_only_as_smoked(
                        path=remote_path, data_changelist=data_changelist
                    )
                    LOGGER.info(
                        "Registered data smoke tag for CL %s at %s", data_changelist, location
                    )

                    # For QV smoke tests or if code is already smoked at origin,
                    # also mark code as smoked at all locations
                    if test_definition.lower() == "smoke" or code_smoked_at_origin:
                        LOGGER.info(
                            "Registering code smoke tag for %s with CL %s at location %s",
                            code_branch,
                            code_changelist,
                            location,
                        )
                        metadata_manager.tag_code_build_as_smoked(path=remote_path)

        LOGGER.info("Autotest build registered successfully at location %s", test_location)

    else:
        LOGGER.info(
            "Not registering Autotest result. "
            "Metadata service reporting is disabled for category %s",
            test_definition,
        )
