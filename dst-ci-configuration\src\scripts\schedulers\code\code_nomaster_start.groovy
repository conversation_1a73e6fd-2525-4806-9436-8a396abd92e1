package scripts.schedulers.code

import com.ea.lib.LibJenkins
import com.ea.project.GetBranchFile

def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
def settings_map = branchfile.general_settings + branchfile.standard_jobs_settings
def project = ProjectClass(env.project_name)

/**
 * code_nomaster_start.groovy
 */
pipeline {
    agent { label '(scheduler && master) || scheduler || executor_agent' }
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Get changelist from Perforce') {
            steps {
                script {
                    def ignore_paths = branchfile.general_settings?.ignore_paths_code_preview ?: []
                    P4PreviewCode(project, 'stream', env.code_folder, env.code_branch, env.non_virtual_code_folder, env.non_virtual_code_branch, ignore_paths, [], settings_map)
                }
            }
        }
        stage('Trigger code.nomaster jobs') {
            steps {
                script {
                    def code_changelist = params.code_changelist ?: env.P4_CHANGELIST
                    def clean_local = params.clean_local

                    def args = [
                        string(name: 'code_changelist', value: code_changelist),
                        string(name: 'clean_local', value: clean_local),
                    ]
                    def jobs = [:]

                    def inject_map = [
                        'code_changelist': code_changelist,
                    ]
                    EnvInject(currentBuild, inject_map)
                    currentBuild.displayName = env.JOB_NAME + '.' + code_changelist

                    def code_nomaster_matrix = branchfile.code_nomaster_matrix

                    def final_result = Result.SUCCESS
                    def continue_build = true
                    for (def run = 0; run <= env.retry_limit.toInteger(); run++) { // Retry failed jobs if retry_limit > 0.
                        jobs = [:]
                        final_result = Result.SUCCESS
                        for (platform in code_nomaster_matrix) {
                            for (config in platform.configs) {
                                Boolean allow_failure = false
                                def config_name = config
                                def custom_tag = null
                                if (config instanceof Map) {
                                    allow_failure = config.allow_failure ?: false
                                    config_name = config.name
                                    custom_tag = config?.custom_tag
                                }

                                def job_name = env.branch_name + '.code.nomaster.' + platform.name + '.' + config_name
                                if (custom_tag != null) {
                                    job_name += ".${custom_tag}"
                                }

                                if (NeedsRebuildCode(job_name, code_changelist)) {
                                    if (run > 0 && IsGameFailure(job_name, allow_failure)) {
                                        if (allow_failure == false) {
                                            final_result = Result.FAILURE
                                            // Set pipeline as failed if there are jobs from IsGameFailure category.
                                            continue_build = false
                                        }
                                        break
                                    } else {
                                        def code_nomaster_args = args
                                        if (run > 0 && IsCleanFailure(job_name)) {
                                            code_nomaster_args = [
                                                string(name: 'code_changelist', value: code_changelist),
                                                string(name: 'clean_local', value: 'true'),
                                            ]
                                        }
                                        jobs[job_name] = {
                                            def downstream_job = build(job: job_name, parameters: code_nomaster_args, propagate: false)
                                            if (allow_failure == false) {
                                                final_result = final_result.combine(Result.fromString(downstream_job.result))
                                            }
                                            LibJenkins.printFailureMessage(this, downstream_job, allow_failure)
                                            LibJenkins.printRunningJobs(this)
                                        }
                                    }
                                }
                            }
                            if (continue_build == false) {
                                break
                            }
                        }
                        if (continue_build == false) {
                            break
                        }
                        parallel(jobs)
                        if (final_result == Result.SUCCESS) {
                            break
                        }
                    }
                    currentBuild.result = final_result.toString()

                    def slack_settings = branchfile.standard_jobs_settings?.slack_channel_code_nomaster
                    SlackMessageNew(currentBuild, slack_settings, project.short_name)

                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
        stage('Scan for errors') { steps { ScanForErrors(currentBuild, env.slack_notify_bot_code_nomaster) } }
    }
    post { failure { SlackNotifyBot(currentBuild, env.slack_notify_bot_code_nomaster) } }
}
