{#
    Command:
        navmesh
            short_help: Cook and submit Navmesh asset.

    Arguments:

    Required variables:
        data_dir
            required: True
        p4_port
            required: True
        p4_client
            required: True

    Optional variables:
        asset
            default: Levels\MainLevel\MainLevel_racePathfinding
        code_branch
            default: None
            help: Perforce branch/stream name.
        code_changelist
            default: None
            help: Perforce changelist number for code.
        data_changelist
            default: None
            help: Perforce changelist number for data.
        pipeline_args
            multiple: True
            help: Pipeline arguments for data build.
            default: []
        user
            default: None
            help: Perforce user name.
        dry_run
            is_flag: True
            help: Don't submit the navmesh.
        data_clean
            default: false
            help: Clean Avalanche if --data-clean true is passed.
        p4_edit_list
            multiple: True
            help: Perforce files to open for edit
            default: []
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    navmesh
    --data-dir {{ data_dir }}
    --p4-port {{ p4_port }}
    --p4-client {{ p4_client }}
    {%- if asset %}
    --asset {{ asset }}
    {%- endif %}
    {%- if code_branch %}
    --code-branch {{ code_branch }}
    {%- endif %}
    {%- if code_changelist %}
    --code-changelist {{ code_changelist }}
    {%- endif %}
    {%- if data_changelist %}
    --data-changelist {{ data_changelist }}
    {%- endif %}
    {%- if pipeline_args %}
    --pipeline-args {{ pipeline_args }}
    {%- endif %}
    {%- if user %}
    --user {{ user }}
    {%- endif %}
    {%- if dry_run %}
    --dry-run {{ dry_run }}
    {%- endif %}
    {%- if data_clean %}
    --data-clean {{ data_clean }}
    {%- endif %}
    {%- if p4_edit_list %}
    --p4-edit-list {{ p4_edit_list }}
    {%- endif %}
  displayName: elipy navmesh
