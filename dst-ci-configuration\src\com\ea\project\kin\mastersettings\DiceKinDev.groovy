package com.ea.project.kin.mastersettings

import com.ea.project.kin.Kingston

class DiceKinDev {
    static Class project = Kingston
    static Map branches = [
        'kin-dev'                          : [code_folder: 'dev', code_branch: 'kin-dev', data_folder: 'dev', data_branch: 'kin-dev'],
        'kin-dev-unverified'               : [code_folder: 'dev', code_branch: 'kin-dev-unverified', data_folder: 'dev', data_branch: 'kin-dev-unverified'],
        'kin-dev-unverified-outsource-code': [code_folder: 'dev', code_branch: 'kin-dev-unverified-outsource-code', data_folder: 'dev', data_branch: 'kin-dev-unverified'],
        'kin-mkt-art'                      : [code_folder: 'dev', code_branch: 'kin-dev', data_folder: 'tasks', data_branch: 'kin-mkt-art'],
        'kin-playtest'                     : [code_folder: 'tasks', code_branch: 'kin-playtest', data_folder: 'tasks', data_branch: 'kin-playtest'],
        'kin-soaks'                        : [code_folder: 'tasks', code_branch: 'kin-soaks', data_folder: 'tasks', data_branch: 'kin-soaks'],
        'future-dev-content'               : [code_folder: 'dev', code_branch: 'kin-dev', data_folder: 'future', data_branch: 'future-dev-content',],
        'future-dev-runmode'               : [code_folder: 'dev', code_branch: 'kin-dev', data_folder: 'dev', data_branch: 'future-dev-runmode',],
        'future-dev-runmode-02'            : [code_folder: 'dev', code_branch: 'kin-dev', data_folder: 'dev', data_branch: 'future-dev-runmode-02'],
        'kin-dev-anticheat'                : [code_folder: 'tasks', code_branch: 'kin-dev-anticheat', data_folder: 'tasks', data_branch: 'kin-dev-anticheat',],
    ]
    static Map preflight_branches = [:]
    static Map autotest_branches = [:]
    static Map integrate_branches = [
        'kin-stage_to_kin-dev'                       : [
            cook_before_submit          : true,
            source_folder               : 'stage', source_branch: 'kin-stage',
            target_folder               : 'dev', target_branch: 'kin-dev',
            code                        : true, data: true, parent_to_child: true, no_submit: false, no_safe_resolve: true,
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call,
            workspace_root              : project.workspace_root, slack_channel: '#kin-integration-notify',
            trigger_type_integrate      : 'scm',
            freestyle_job_trigger_matrix: [],
        ],
        'kin-stage_to_kin-dev-unverified'            : [
            cook_before_submit          : true,
            source_folder               : 'stage', source_branch: 'kin-stage',
            target_folder               : 'dev', target_branch: 'kin-dev-unverified',
            code                        : true, data: false, parent_to_child: true, no_submit: false, no_safe_resolve: true,
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call,
            workspace_root              : project.workspace_root, slack_channel: '#kin-integration-notify',
            trigger_type_integrate      : 'scm',
            freestyle_job_trigger_matrix: [],
        ],
        'kin-dev-unverified_to_kin-dev'              : [
            cook_before_submit          : true,
            source_folder               : 'dev', source_branch: 'kin-dev-unverified',
            target_folder               : 'dev', target_branch: 'kin-dev',
            code                        : true, data: false, parent_to_child: false, no_submit: false, no_safe_resolve: true,
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call,
            workspace_root              : project.workspace_root, slack_channel: '#kin-integration-notify',
            trigger_type_integrate      : 'none',
            freestyle_job_trigger_matrix: [],
        ],
        'kin-dev_to_kin-dev-unverified'              : [
            cook_before_submit          : true,
            source_project              : project, source_folder: 'dev', source_branch: 'kin-dev',
            target_project              : project, target_folder: 'dev', target_branch: 'kin-dev-kin-dev-unverified-upgrade',
            preview_project             : project, preview_folder: 'dev', preview_branch: 'kin-dev-unverified',
            code                        : false, data: true, parent_to_child: true, data_upgrade: true, accept_theirs: true,
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call, workspace_root: project.workspace_root,
            slack_channel               : '#kin-integration-notify', job_label: 'data-upgrade-kin-dev-unverified',
            script_path                 : project.workspace_root + '\\TnT\\Code\\DICE\\UpgradeScripts\\UpgradeScripts.txt',
            verified_integration        : true, integration_reference_job: 'kin-dev.data.start',
            extra_args                  : ' --licensee BattlefieldGame', manual_trigger: false, create_ref_job: false,
            freestyle_job_trigger_matrix: [],
        ],
        'kin-dev_to_future-dev-runmode'              : [
            source_folder               : 'dev', source_branch: 'kin-dev',
            target_folder               : 'dev', target_branch: 'future-dev-runmode',
            code                        : false, data: true, parent_to_child: true, no_submit: false,
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call,
            workspace_root              : project.workspace_root, slack_channel: '#kin-integration-notify',
            extra_data_args             : [
                ' --exclude-path //' + project.p4_data_client_env + '/kindata/Source/Future/...',
            ],
            trigger_type_integrate      : 'scm', ignore_source_history: false,
            shelve_changelist           : true,
            manual_trigger              : true,
            freestyle_job_trigger_matrix: [],
        ],
        'future-dev-runmode_to_future-dev-runmode-02': [
            source_folder               : 'dev', source_branch: 'future-dev-runmode',
            target_folder               : 'dev', target_branch: 'future-dev-runmode-02',
            code                        : false, data: true, parent_to_child: true, no_submit: false,
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call,
            workspace_root              : project.workspace_root, slack_channel: '#kin-integration-notify',
            trigger_type_integrate      : 'scm', ignore_source_history: false,
            shelve_changelist           : true,
            custom_code_branch          : true,
            code_project                : project, code_folder: 'dev', code_branch: 'kin-dev',
            freestyle_job_trigger_matrix: [],
        ],
        'future-dev-runmode-02_to_future-dev-content': [
            source_folder               : 'dev', source_branch: 'future-dev-runmode-02',
            target_folder               : 'future', target_branch: 'future-dev-content',
            code                        : false, data: true, parent_to_child: true, no_submit: false,
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call,
            workspace_root              : project.workspace_root, slack_channel: '#kin-integration-notify',
            trigger_type_integrate      : 'scm', ignore_source_history: false,
            shelve_changelist           : true,
            custom_code_branch          : true,
            code_project                : project, code_folder: 'dev', code_branch: 'kin-dev',
            freestyle_job_trigger_matrix: [],
        ],
    ]
    static Map copy_branches = [
        'kin-release_to_kin-dev-irt'       : [
            source_folder               : 'release', source_branch: 'kin-release',
            target_folder               : 'tasks', target_branch: 'kin-dev-irt',
            code                        : true, data: true, parent_to_child: true,
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call,
            workspace_root              : project.workspace_root, slack_channel: '#kin-playtest-integrates',
            trigger_type_copy           : 'stop', trigger_string_copy: 'H */6 * * *',
            freestyle_job_trigger_matrix: [],
        ],
        'kin-stage_to_kin-dev-irt'         : [
            source_folder               : 'stage', source_branch: 'kin-stage',
            target_folder               : 'tasks', target_branch: 'kin-dev-irt',
            code                        : true, data: true, parent_to_child: true,
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call,
            workspace_root              : project.workspace_root, slack_channel: '#kin-playtest-integrates',
            trigger_type_copy           : 'stop', trigger_string_copy: 'H */6 * * *',
            freestyle_job_trigger_matrix: [],
        ],
        'kin-dev-unverified_to_kin-dev-irt': [
            source_project              : project, source_folder: 'dev', source_branch: 'kin-dev-unverified',
            target_project              : project, target_folder: 'tasks', target_branch: 'kin-dev-irt',
            code                        : true, data: true, parent_to_child: true, exclude_sparta: true,
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call,
            workspace_root              : project.workspace_root, slack_channel: '#kin-playtest-integrates',
            trigger_type_copy           : 'stop', trigger_string_copy: 'H */6 * * *',
            freestyle_job_trigger_matrix: [],
        ],
        'kin-dev_to_kin-dev-irt'           : [
            source_project              : project, source_folder: 'dev', source_branch: 'kin-dev',
            target_project              : project, target_folder: 'tasks', target_branch: 'kin-dev-irt',
            code                        : true, data: true, parent_to_child: true, exclude_sparta: true,
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call,
            workspace_root              : project.workspace_root, slack_channel: '#kin-playtest-integrates',
            trigger_type_copy           : 'cron', trigger_string_copy: 'H */6 * * *',
            freestyle_job_trigger_matrix: [],
        ],
        'kin-dev-anticheat_to_kin-dev-irt' : [
            source_project              : project, source_folder: 'tasks', source_branch: 'kin-dev-anticheat',
            target_project              : project, target_folder: 'tasks', target_branch: 'kin-dev-irt',
            code                        : true, data: true, parent_to_child: true, exclude_sparta: true,
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call,
            workspace_root              : project.workspace_root, slack_channel: '#kin-playtest-integrates',
            trigger_type_copy           : 'stop', trigger_string_copy: 'H */6 * * *',
            freestyle_job_trigger_matrix: [],
        ],
        'future-dev-runmode_to_kin-dev'    : [
            source_project              : project, source_folder: 'dev', source_branch: 'future-dev-runmode',
            target_project              : project, target_folder: 'dev', target_branch: 'kin-dev',
            code                        : false, data: true,
            parent_to_child             : false, p4_code_force_copy: '--force',
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call,
            workspace_root              : project.workspace_root, slack_channel: '#kin-integration-notify',
            trigger_type_copy           : 'stop', trigger_string_copy: 'TZ=Europe/Stockholm \n H 18 * * *',
            data_only_source_branch     : true,
            freestyle_job_trigger_matrix: [],
        ],
    ]
    static Map feature_integrations = [:]
    static Map maintenance_branch = [
        'kin-dev': [
            code_folder                       : 'dev', code_branch: 'kin-dev',
            data_folder                       : 'dev', data_branch: 'kin-dev',
            include_vault                     : true,
            include_register_release_candidate: true,
        ]
    ]
    static List dashboard_list = [
        'kin-dev',
        'kin-dev-unverified',
    ]
    static Map dvcs_configs = [:]
    static List code_downstream_matrix = []
    static Map MAINTENANCE_SETTINGS = [:]
}
