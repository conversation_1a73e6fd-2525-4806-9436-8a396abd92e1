   CoverityScheduler.run()
      GetBranchFile.get_branchfile(null, null)
      CoverityScheduler.ProjectClass(null)
      CoverityScheduler.pipeline(groovy.lang.Closure)
         CoverityScheduler.allowBrokenBuildClaiming()
         CoverityScheduler.timestamps()
         CoverityScheduler.echo(Executing on agent [label:any])
         CoverityScheduler.stage(Get changelist from Perforce, groovy.lang.Closure)
            CoverityScheduler.script(groovy.lang.Closure)
               CoverityScheduler.P4PreviewCode(class schedulers.CoveritySchedulerSpec$TestClassCoverity, stream, code-folder, code-branch, mainline, trunk-code-dev, [], [], {})
         CoverityScheduler.stage(Trigger Coverity job, groovy.lang.Closure)
            CoverityScheduler.script(groovy.lang.Closure)
               CoverityScheduler.retryOnFailureCause(3, [], groovy.lang.Closure)
                  CoverityScheduler.string({name=CODE_CHANGELIST, value=234})
                  CoverityScheduler.booleanParam({name=CLEAN_LOCAL, value=false})
                  CoverityScheduler.booleanParam({name=CLEAN_COVERITY_CLIENT, value=null})
                  CoverityScheduler.EnvInject({absoluteUrl=http://example.com/dummy, buildVariables={}, changeSets=[], currentResult=SUCCESS, description=dummy, displayName=#1, duration=1, durationString=1 ms, fullDisplayName=dummy #1, fullProjectName=dummy, id=1, keepLog=false, nextBuild=null, number=1, previousBuild=null, projectName=dummy, result=SUCCESS, startTimeInMillis=1, timeInMillis=1, upstreamBuilds=[]}, {CODE_CHANGELIST=234})
                  CoverityScheduler.build({job=a-branch.coverity, parameters=[{name=CODE_CHANGELIST, value=234}, {name=CLEAN_LOCAL, value=false}, {name=CLEAN_COVERITY_CLIENT, value=null}], propagate=false})
                  CoverityScheduler.SlackMessageNew({absoluteUrl=http://example.com/dummy, buildVariables={}, changeSets=[], currentResult=SUCCESS, description=dummy, displayName=my-job.234, duration=1, durationString=1 ms, fullDisplayName=dummy #1, fullProjectName=dummy, id=1, keepLog=false, nextBuild=null, number=1, previousBuild=null, projectName=dummy, result=SUCCESS, startTimeInMillis=1, timeInMillis=1, upstreamBuilds=[]}, null, s_n)
