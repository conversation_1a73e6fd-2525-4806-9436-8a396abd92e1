"""
gametool/gametool.py
"""

import os
import click
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics
from elipy2 import LOGGER, running_processes, code, frostbite_core
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.licensee_helper import set_licensee
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags


@click.command(
    "gametool",
    short_help="Build the Icepick gametool.",
    context_settings=dict(ignore_unknown_options=True),
)
@click.argument("package")
@click.option("--config", default="release", help="Config")
@click.option("--code-changelist", required=True, help="Which code changelist to use.")
@click.option("--clean", type=click.BOOL, default=False, help="Delete TnT/Local")
@click.option("--licensee", multiple=True, default=None, help="Frostbite Licensee to use")
@click.option("--framework-args", multiple=True, help="Framework arguments for gen sln.")
@click.option("--submit", type=click.BOOL, default=True, help="Set this to false for dry-run")
@click.option(
    "--dice-fragment",
    default="Code\\DICE\\masterconfig_fragment.xml",
    help="Path to DICE masterconfig fragment",
)
@click.option(
    "--icepick-fragment",
    default="Automation\\Icepick\\icepick_masterconfig_fragment.xml",
    help="Path to Icepick masterconfig fragment",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    package,
    config,
    code_changelist,
    clean,
    licensee,
    framework_args,
    submit,
    dice_fragment,
    icepick_fragment,
):
    """
    Build Icepick Gametool
    :return:
    """
    LOGGER.info("Building %s %s gametool on code changelist %s.", package, config, code_changelist)
    # adding sentry tags
    add_sentry_tags(__file__, "gametool")

    running_processes.kill()

    target = ["gensln", "buildsln"]
    if submit:
        target.extend(["deploy", "submit"])

    builder = code.CodeUtils(
        platform="win64-dll",
        config=config,
        monkey_build_label=code_changelist,
        package=package,
        target=target,
    )

    if clean:
        LOGGER.info("Clean set to True. Cleaning...")
        builder.clean_local(close_handles=True)

    framework_args = list(framework_args)

    framework_args.append(f"-D:change-list={code_changelist}")
    framework_args.append("-G:frostbite.buildingToolName=Icepick")
    framework_args.append("-G:frostbite.hasCustomSolutionfolders=true")
    framework_args.append("-G:frostbite.target.name=any")
    framework_args.append("-G:package.Icepick.uber-solution=true")
    framework_args.append("-G:vsversion=2022")

    # This issue was introduced in Frostbite version 2025.1.PR2

    # Only add the Icepick masterconfig fragment if the Frostbite version is >= 2025.1.PR2
    if frostbite_core.minimum_fb_version(year=2025, season=1, version_nr="PR2"):
        LOGGER.info("Frostbite version >= 2025.1.PR2, adding Icepick masterconfig fragment")

        # Check if we already have a masterconfig fragments argument
        has_masterconfig = False

        for i, arg in enumerate(framework_args):
            if "-masterconfigfragments:" in arg:
                has_masterconfig = True
                # Append the Icepick fragment to the existing masterconfig
                # fragments using semicolon separator
                if icepick_fragment not in arg:
                    # Extract the existing path from the argument
                    existing_path = arg.replace("-masterconfigfragments:", "")
                    # Format with separate quotes for each path
                    args = f'-masterconfigfragments:{existing_path};"{icepick_fragment}"'
                    framework_args[i] = args
                break

        # If no masterconfig fragments argument exists, add a new one with both fragments
        if not has_masterconfig:
            # Include both the DICE and Icepick masterconfig fragments with separate quotes
            masterconfig_arg = f'-masterconfigfragments:"{dice_fragment}";"{icepick_fragment}"'
            framework_args.append(masterconfig_arg)
            LOGGER.info("Added both DICE and Icepick masterconfig fragments")
    else:
        LOGGER.info("Frostbite version < 2025.1.PR2, not adding Icepick masterconfig fragment")

    LOGGER.info("Framework arguments: %s", " ".join(framework_args))

    if licensee:
        framework_args = set_licensee(list(licensee), framework_args)

    LOGGER.info("Running nantonpackage")

    builder.nantonpackage(framework_args=framework_args)

    LOGGER.info("Done building %s %s.", package, config)
