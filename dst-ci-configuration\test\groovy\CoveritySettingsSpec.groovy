import com.ea.lib.jobsettings.CoveritySettings
import spock.lang.Specification

class CoveritySettingsSpec extends Specification {
    class BranchFile {
        static Map standard_jobs_settings = [
            workspace_root      : 'workspace-root',
            elipy_call          : 'elipy-call',
            elipy_install_call  : 'elipy-install-call',
            job_label_statebuild: 'label',
        ]
        static Map general_settings = [
            frostbite_licensee: 'battlefieldgame',
            coverity_settings : [
                credentials            : 'monkey.bct',
                ess_secrets_credential : 'bct-secrets-secret-id',
                ess_secrets_key        : 'BCT_SECRETS_SECRET_ID',
                run_coverity           : true,
                trigger                : 'H/2 * * * 1-6',
                non_virtual_code_branch: 'trunk-code-dev',
                non_virtual_code_folder: 'mainline',
                job_label              : 'coverity-job-label',
                extra_args             : ' --extra-args arg',
                timeout_hours          : 10,
                artifactory_source_path: 'dreeu-generic-local/coverity/cov-analysis-win64.zip'
            ],
        ]
    }

    class MasterFile {
        static Map branches = [branch: [data_branch: 'data-branch', code_branch: 'code-branch', code_folder: 'dev']]
    }

    class ProjectFile {
        static String name = 'BCT'
    }

    void "test that we get expected job settings in initializeStart"() {
        when:
        CoveritySettings settings = new CoveritySettings()
        settings.initializeStart(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            description == "Sync ${branchInfo.branch_name} code and run Coverity."
            cronTrigger == BranchFile.general_settings.coverity_settings.trigger
            nonVirtualCodeBranch == BranchFile.general_settings.coverity_settings.non_virtual_code_branch
            nonVirtualCodeFolder == BranchFile.general_settings.coverity_settings.non_virtual_code_folder
            codeBranch == 'code-branch'
            codeFolder == 'dev'
            projectName == ProjectFile.name
        }
    }

    void "test that we get expected job settings in initializeCoverity"() {
        when:
        CoveritySettings settings = new CoveritySettings()
        settings.initializeCoverity(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            jobLabel == BranchFile.general_settings.coverity_settings.job_label
            timeoutMinutes == BranchFile.general_settings.coverity_settings.timeout_hours * 60
            extraArgs == "${BranchFile.general_settings.coverity_settings.extra_args}"
            buildName == '${JOB_NAME}.${ENV, var="CODE_CHANGELIST"}'
            description == 'Run Coverity and submit results'
            coverityCredentials == 'monkey.bct'
            essSecretsCredential == 'bct-secrets-secret-id'
            essSecretsKey == 'BCT_SECRETS_SECRET_ID'
            elipyCmd == "${BranchFile.standard_jobs_settings.elipy_call} coverity --code-changelist %CODE_CHANGELIST% --code-branch" +
                " ${branchInfo.code_branch} --artifactory-user %ARTIFACTORY_USER% --artifactory-apikey %ARTIFACTORY_API_KEY%" +
                ' --artifactory-coverity-source-path dreeu-generic-local/coverity/cov-analysis-win64.zip --coverity-user %COVERITY_USER%' +
                " --coverity-password %COVERITY_PASSWORD% --clean %CLEAN_LOCAL% --clean-coverity-client %CLEAN_COVERITY_CLIENT% ${extraArgs}"
        }
    }
}
