package com.ea.project.kin.branchsettings

import com.ea.project.kin.KingstonAWS

class AWS_kin_dev_unverified {
    // Settings for jobs
    static Class project = KingstonAWS
    static Map general_settings = [
        dataset           : project.dataset,
        frostbite_licensee: project.frostbite_licensee,
        elipy_install_call: project.elipy_install_call,
        elipy_call        : project.elipy_call,
        workspace_root    : project.workspace_root,
        import_local      : false,
    ]
    static Map standard_jobs_settings = [
        elipy_shift_config          : false,
        asset                       : 'PreflightLevels',
        baseline_set                : false,
        clean_local                 : true,
        server_asset                : 'PreflightLevels',
        disable_frosty_symbol_upload: true,
        skip_frosty_trigger         : true,
        skip_symbols_to_avalanche   : true,
        use_super_bundles           : true,
        skip_icepick_settings_file  : true,
        schedule                    : '30 7 * * 1-5',
    ]
    static Map preflight_settings = [
        concurrent_code            : 10,
        use_last_known_good_code_cl: true,
        use_icepick_test           : true,
        pre_preflight              : false,
        statebuild_codepreflight   : false,
        slack_channel_preflight    : [channels: ['#cobra-build-preflight']],
        codepreflight_reference_job: 'kin-dev-unverified.code.lastknowngood',
        datapreflight_reference_job: 'kin-dev-unverified.data.lastknowngood',
        extra_postpreflight_args   : "--framework-args -D:ondemandp4proxymapfile=${general_settings.workspace_root}\\tnt\\build\\framework\\data\\P4ProtocolOptimalProxyMap.xml", // for postpreflight use
        extra_codepreflight_args   : "--framework-args -D:ondemandp4proxymapfile=${general_settings.workspace_root}\\tnt\\build\\framework\\data\\P4ProtocolOptimalProxyMap.xml",
        p4_code_server             : 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
        p4_code_creds              : 'dice-p4buildedge03-fb',
    ]

    // Matrix definitions for jobs
    static List code_matrix = []
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = []
    static List data_matrix = []
    static List data_downstream_matrix = []
    static List patchdata_matrix = []
    static List frosty_matrix = []
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = [
        [name: 'win64game', configs: ['retail']],
        [name: 'ps4', configs: ['release']],
        [name: 'xb1', configs: ['final']],
        [name: 'xbsx', configs: ['retail']],
        [name: 'ps5', configs: ['retail']],
        [name: 'win64server', configs: ['final']],
        [name: 'linux64server', configs: ['release']],
        [name: 'tool', configs: ['release'], sync_code_and_data: true],
    ]
    static List data_preflight_matrix = []
    static List spin_upload_matrix = []
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}

