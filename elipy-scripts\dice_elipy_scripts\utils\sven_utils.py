"""
Functions to handle sven deployments
"""

import os
from elipy2 import core, avalanche, LOGGER, frostbite_core
from elipy2.exceptions import ELIPYException


def get_svendeploy_exe_path():
    """
    Returns:
        The path to Sven.Deploy
    Raises:
        ELIPYException if exe is not found.
    """
    exe_path = os.path.join(frostbite_core.get_tnt_root(), "bin", "Sven", "Sven.Deploy.CLI.exe")
    if os.path.exists(exe_path):
        return exe_path
    else:
        raise ELIPYException("{0} not found on disk.".format(exe_path))


def export_sven_bundles(platform, bundles_location):
    """
    Export sven deployement from avalanche
    """
    licensee = avalanche.get_database_id()
    branch = avalanche.get_fb_branch_id()
    avalanche_platform = avalanche.get_avalanche_platform_name(platform)
    db_name = avalanche.get_full_database_name(platform)

    LOGGER.info("Deploying from {0} to {1} using Sven Deploy".format(db_name, bundles_location))

    cmd = [
        get_svendeploy_exe_path(),
        "deploy",
        "--licensee=" + licensee,
        "--branch=" + branch,
        "--platform=" + avalanche_platform,
        "--out=" + bundles_location,
        "--clear",
    ]

    core.run(cmd)

    # Get builtLevels.json and place with HEAD bundles.
    build_levels_filename = os.path.join(bundles_location, "ship", "data", "builtLevels.json")
    avalanche.get_built_levels(db_name, to_file=build_levels_filename)


def export_sven_patch_bundles(platform, base_location, bundles_location):
    """
    Export sven patch deployment from avalanche
    """
    licensee = avalanche.get_database_id()
    branch = avalanche.get_fb_branch_id()
    avalanche_platform = avalanche.get_avalanche_platform_name(platform)
    db_name = avalanche.get_full_database_name(platform)

    LOGGER.info("Patching from {0} to {1} using Sven Deploy".format(db_name, bundles_location))

    cmd = [
        get_svendeploy_exe_path(),
        "deploy",
        "--licensee=" + licensee,
        "--branch=" + branch,
        "--platform=" + avalanche_platform,
        "--out=" + bundles_location,
        "--base=" + base_location,
        "--clear",
    ]

    core.run(cmd)

    # Get builtLevels.json and place with HEAD bundles.
    build_levels_filename = os.path.join(bundles_location, "ship", "data", "builtLevels.json")
    avalanche.get_built_levels(db_name, to_file=build_levels_filename)
