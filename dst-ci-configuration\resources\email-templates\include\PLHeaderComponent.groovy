import groovy.xml.MarkupBuilder
import hudson.model.Result
import hudson.model.Run

public class PLHeaderComponent implements IEmailComponent {

    private List<IEmailComponent> registeredComponents = []

    public PLHeaderComponent(List<IEmailComponent> registeredComponents) {
        this.registeredComponents.addAll(registeredComponents)
    }


    private Map gather(Run run) {

        def fullDisplayName = run.getFullDisplayName()
        def result = run.getResult()
        if (!result) {
            result = Result.SUCCESS
        }

        return [
            FullDisplayName: fullDisplayName,
            Result         : result.toString(),
        ]
    }


    void render(Run run, MarkupBuilder markupBuilder) {
        def data = gather(run)
        if (data) {
            markupBuilder.head {
                title {
                    mkp.yield("${data.FullDisplayName} - ${data.Result}")
                }
                meta(charset: "utf-8")
                meta(name: "viewport", content: "width=device-width, initial-scale=1")
                meta("http-equiv": "X-UA-Compatible", content: "IE=edge")
                style(type: "text/css") {
                    mkp.yieldUnescaped(getEmbeddedStyle(run))
                    registeredComponents.each { component ->
                        if (component.isApplicable(run)) {
                            def componentEmbeddedStyle = component.getEmbeddedStyle(run)
                            if (componentEmbeddedStyle) {
                                mkp.yieldUnescaped(componentEmbeddedStyle)
                            }
                        }
                    }
                }
            }
        }
    }


    public boolean isApplicable(Run run) {
        return true
    }


    public String getEmbeddedStyle(Run run) {
        return """
                    /* CLIENT-SPECIFIC STYLES */
                    body, table, td, a {-webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%;} /* Prevent WebKit and Windows mobile changing default text sizes */
                    table, td {mso-table-lspace: 0pt; mso-table-rspace: 0pt;} /* Remove spacing between tables in Outlook 2007 and up */
                    img {-ms-interpolation-mode: bicubic;} /* Allow smoother rendering of resized image in Internet Explorer */

                    /* RESET STYLES */
                    img {border: 0; height: auto; line-height: 100%; outline: none; text-decoration: none;}
                    table {border-collapse: collapse !important;}
                    body {height: 100% !important; margin: 0 !important; padding: 0 !important; width: 100% !important;}

                    /* iOS BLUE LINKS */
                    a[x-apple-data-detectors] {
                        color: inherit !important;
                        text-decoration: none !important;
                        font-size: inherit !important;
                        font-family: inherit !important;
                        font-weight: inherit !important;
                        line-height: inherit !important;
                    }

                    * {
                        font-family: "LatoLatinWeb", "Lato", "Helvetica Neue", Helvetica, Arial, sans-serif;
                    }

                    pre {
                        white-space: pre-wrap;            /* Since CSS 2.1 */
                        white-space: -moz-pre-wrap;        /* Mozilla, since 1999 */
                        white-space: -pre-wrap;            /* Opera 4-6 */
                        white-space: -o-pre-wrap;        /* Opera 7 */
                        word-wrap: break-word;            /* Internet Explorer 5.5+ */
                    }

                    div.hidden-text {
                        color: #FEFEFE;
                        display: none;
                        font-size: 1px;
                        line-height: 1px;
                        max-height: 0px;
                        max-width: 0px;
                        opacity: 0;
                        overflow: hidden;
                    }

                    td.section {
                        background-color: #FFFFFF;
                        padding: 15px;
                    }

                    td.section-title {
                        color: #4A4A4A;
                        font-family: "LatoLatinWeb", "Lato", "Helvetica Neue", Helvetica, Arial, sans-serif;
                        font-size: 14px;
                        font-weight: bold;
                        padding: 0px 0px 10px 0px;
                        text-align: left;
                    }

                    /* MOBILE STYLES */
                    @media screen and (max-width: 525px) {

                        /* ALLOWS FOR FLUID TABLES */
                        .wrapper {
                        width: 100% !important;
                        max-width: 100% !important;
                        }

                        /* USE THESE CLASSES TO HIDE CONTENT ON MOBILE */
                        .mobile-hide {
                        display: none !important;
                        }

                        /* FULL-WIDTH TABLES */
                        .responsive-table {
                        width: 100% !important;
                        }

                        /* UTILITY CLASSES FOR ADJUSTING PADDING ON MOBILE */
                        .padding {
                        padding: 10px 5% 15px 5% !important;
                        }

                        .padding-meta {
                        padding: 30px 5% 0px 5% !important;
                        text-align: center;
                        }

                        .padding-copy {
                            padding: 10px 5% 10px 5% !important;
                            text-align: center;
                        }

                        .no-padding {
                        padding: 0 !important;
                        }

                        .section-padding {
                        padding: 50px 15px 50px 15px !important;
                        }
                    }

                    /* ANDROID CENTER FIX */
                    div[style*="margin: 16px 0;"] { margin: 0 !important; }
        """
    }
}
