package com.ea.lib.jobs

import com.ea.lib.LibJobDsl
import com.ea.lib.jobsettings.CoveritySettings

class LibCoverity {

    static void start(def job, def project, def branchFile, def masterFile, String branchName) {
        def settings = new CoveritySettings()
        settings.initializeStart(branchFile, masterFile, project, branchName)
        job.with {
            description(settings.description)
            environmentVariables {
                env('BRANCH_NAME', settings.branchName)
                env('CODE_BRANCH', settings.codeBranch)
                env('CODE_FOLDER', settings.codeFolder)
                env('PROJECT_NAME', settings.projectName)
                env('NON_VIRTUAL_CODE_BRANCH', settings.nonVirtualCodeBranch)
                env('NON_VIRTUAL_CODE_FOLDER', settings.nonVirtualCodeFolder)
            }
            disabled(false)
            logRotator(7, 100)
            parameters {
                stringParam {
                    name('CODE_CHANGELIST')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                booleanParam('CLEAN_LOCAL', false, 'If true, TnT/Local will be deleted at the beginning of the run.')
                booleanParam('CLEAN_COVERITY_CLIENT', false, 'Re-download the Coverity client and regenerate the license.')
            }
            properties {
                disableConcurrentBuilds()
                disableResume()
                pipelineTriggers {
                    triggers {
                        pollSCM {
                            scmpoll_spec(settings.cronTrigger)
                        }
                    }
                }
            }
            quietPeriod(0)
        }
    }

    static void coverityJob(def job, def project, def branchFile, def masterFile, String branchName) {
        def settings = new CoveritySettings()
        settings.initializeCoverity(branchFile, masterFile, project, branchName)
        job.with {
            description(settings.description)
            label(settings.jobLabel)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(settings.workspaceRoot)
            parameters {
                stringParam {
                    name('CODE_CHANGELIST')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                booleanParam('CLEAN_LOCAL', false, 'If true, TnT/Local will be deleted at the beginning of the run.')
                booleanParam('CLEAN_COVERITY_CLIENT', false, 'Re-download the Coverity client and regenerate the license.')
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(settings.buildName)
                timeout {
                    absolute(settings.timeoutMinutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
                credentialsBinding {
                    usernamePassword('COVERITY_USER', 'COVERITY_PASSWORD', settings.coverityCredentials)
                    usernamePassword('ARTIFACTORY_USER', 'ARTIFACTORY_API_KEY', 'coverity-af2-credentials')
                    if (settings.essSecretsCredential && settings.essSecretsKey) {
                        string(settings.essSecretsKey, settings.essSecretsCredential)
                    }
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                batchFile(settings.elipyCmd)
            }
        }
    }
}
