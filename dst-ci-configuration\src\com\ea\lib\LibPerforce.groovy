package com.ea.lib

import org.apache.commons.lang.StringUtils

class LibPerforce {

    final static String GAMETOOL_ICEPICK = 'icepick'
    final static String GAMETOOL_FROSTBITE_DATABASE_UPGRADER = 'frostbiteDatabaseUpgrader'
    final static String GAMETOOL_FROSTYISOTOOL = 'frostyisotool'
    final static String GAMETOOL_DRONE = 'drone'
    final static String GAMETOOL_FRAMEWORK = 'framework'
    final static String GAMETOOL_FBENV = 'fbenv'

    Map triggerPaths = [
        (GAMETOOL_ICEPICK)                    : [
            filter: [
                'TnT/Automation/AutoTestFrameworkTarget/...',
                'TnT/Automation/FrostingReporter/...',
                'TnT/Automation/Icepick/...',
                'TnT/Build/KitCommunicator/...',
                'TnT/Code/Tools/Shared/Frostbite.Base/...',
                'TnT/Code/Tools/Shared/Frostbite.Ensemble/...',
                'TnT/Code/Tools/Shared/Frostbite.Math/...',
            ],
            ignore: [
                'TnT/Automation/Icepick/bin/...',
                'TnT/Automation/Icepick/netcorebinaries/...',
            ],
        ],
        (GAMETOOL_FROSTBITE_DATABASE_UPGRADER): [
            filter: [
                'TnT/Code/Utils/FrostbiteDatabaseUpgrader/...',
            ],
            ignore: [
                'TnT/Code/Utils/FrostbiteDatabaseUpgrader/bin/...',
            ],
        ],
        (GAMETOOL_FROSTYISOTOOL)              : [
            filter: [
                'TnT/Code/Tools/FrostyIsoTool/FrostyGetEnv/...',
                'TnT/Code/Tools/FrostyIsoTool/source/...',
                'TnT/Code/Tools/FrostyIsoTool/test/...',
                'TnT/Code/Tools/FrostyIsoTool/*',
                'TnT/Build/fbenvcore/Source/...',
                'TnT/Code/Engine/Base/...',
                'TnT/Code/Tools/Shared/Frostbite.Math/...',
            ],
            ignore: [
                'TnT/Code/Tools/FrostyIsoTool/bin/...',
            ],
        ],
        (GAMETOOL_DRONE)                      : [
            filter: [
                'TnT/Code/Tools/Drone/...',
                'TnT/Code/Extension/DiceCommons/Drone/...',
                'TnT/Code/DICE/Extensions/DiceCommons/Drone/...',
                'TnT/Build/fbenvcore/...',
                'TnT/Build/fbenv/...',
                'TnT/Build/KitCommunicator/...',
            ],
        ],
        (GAMETOOL_FRAMEWORK)                      : [
            filter: [
                'TnT/Automation/FrostingReporter/...',
                'TnT/Build/fbenvcore/...',
                'TnT/Build/fbenv/...',
                'TnT/Build/Framework/...',
                'TnT/Build/KitCommunicator/...',
                'TnT/Build/XcodeProjectizer/...',
            ],
            ignore: [
                'TnT/Build/Framework/bin/...',
                'TnT/Build/fbenv/bin/...',
            ],
        ],
        (GAMETOOL_FBENV)                      : [
            filter: [
                'TnT/Build/fbenvcore/...',
                'TnT/Build/fbenv/...',
                'TnT/Build/KitCommunicator/...',
            ],
            ignore: [
                'TnT/Build/fbenv/bin/...',
            ],
        ]
    ]

    private final Object context
    private final List<String> gametools
    private final String p4Credentials
    private final String stream
    private final String workspacePrefix

    LibPerforce(def context, String projectShortName, List<String> gametools, String codeFolder, String codeBranch,
                String p4CodeRoot, String p4Credentials, String jobName, boolean isTestEnvironment,
                String nonVirtualCodeFolder = null, String nonVirtualCodeBranch = null) {
        this.context = context
        this.gametools = gametools
        this.p4Credentials = p4Credentials
        String filterFolder = codeFolder
        if (nonVirtualCodeFolder) {
            filterFolder = nonVirtualCodeFolder
        }
        String filterBranch = codeBranch
        if (nonVirtualCodeBranch) {
            filterBranch = nonVirtualCodeBranch
        }
        this.stream = "${p4CodeRoot}/${filterFolder}/${filterBranch}"
        String hostname = 'master'
        if (isTestEnvironment) {
            hostname += '-test'
        }
        this.workspacePrefix = "jenkins-${projectShortName}-${hostname}-${jobName}"
    }

    /**
     * Creates a pollScm trigger for the configured paths, which means that if any of those paths are modified, it will trigger a build.
     * Sets env.P4_CHANGELIST.
     */
    void setPollScmTriggers() {
        List<String> filter = []
        List<String> ignore = []

        gametools.each { String gametool ->
            def trigger = triggerPaths.get(gametool)
            if (trigger) {
                if (trigger.filter) {
                    filter.addAll(trigger.filter)
                }
                if (trigger.ignore) {
                    ignore.addAll(trigger.ignore)
                }
            } else {
                throw new IllegalArgumentException("Trying to set invalid poll SCM trigger: \"${gametool}\"")
            }
        }
        StringJoiner sj = new StringJoiner('\n')
        String workspaceName = composeWorkspaceName('setPollScmTriggers')
        filter.each { String path ->
            sj.add("${stream}/${path} //${workspaceName}/${path}")
        }
        ignore.each { String path ->
            sj.add("-${stream}/${path} //${workspaceName}/${path}")
        }
        context.echo "Poll SCM triggers:\n $sj"
        context.p4CheckoutManual(sj.toString(), workspaceName, p4Credentials)
    }

    /**
     * Determines which gametools have been modified in the specified changelist
     * @return A list of modified gametools
     */
    List<String> getModifiedTools(String codeChangelist) {
        String workspaceName = composeWorkspaceName('getModifiedTools')
        def workspace = [$class : 'StreamWorkspaceImpl',
                         charset: 'none', format: workspaceName,
                         pinHost: false, streamName: stream,]
        def p4 = context.p4(credential: p4Credentials, workspace: workspace)
        def output = p4.run('files', "@=${codeChangelist}")

        Set toReturn = []
        output.each { key, value ->
            String depotFile = key.depotFile
            triggers:
            for (triggerPath in triggerPaths) {
                boolean isIgnored = false
                for (String filter in triggerPath.value.ignore) {
                    if (containsFilter(filter, depotFile)) {
                        isIgnored = true
                        break
                    }
                }
                if (!isIgnored) {
                    for (String filter in triggerPath.value.filter) {
                        if (containsFilter(filter, depotFile)) {
                            toReturn << triggerPath.key
                            break triggers
                        }
                    }
                }
            }
        }
        return new ArrayList<String>(toReturn)
    }

    /**
     * Checks if depotFile contains the filter. For example:
     * {@code filter = '/tmp/test/...'} and {@code depotFile = 'var/tmp/test/file'} it would return {@code true}, since {@code depotFile}
     * contains {@code filter}. Takes perforce wildcards into account, see
     * <a href='https://www.perforce.com/manuals/p4guide/Content/P4Guide/configuration.workspace_view.wildcards.html'>the official documentation.</a>
     * @param filter filter to check
     * @param depotFile target path
     * @return {@code true} if {@code depotFile} contains the {@code filter}, {@code false} otherwise
     */
    private static boolean containsFilter(String filter, String depotFile) {
        if (filter.endsWith('...')) {
            String filterPath = StringUtils.removeEnd(filter, '...')
            List<String> splitPath = depotFile.split(filterPath)
            if (splitPath.size() == 2) {
                return true
            }
        } else if (filter.endsWith('*')) {
            String filterPath = StringUtils.removeEnd(filter, '*')
            List<String> splitPath = depotFile.split(filterPath)
            if (splitPath.size() == 2 && !splitPath[1].contains('/')) {
                return true
            }
        }
        return depotFile.endsWith(filter)
    }

    /**
     * Composes a perforce workspace name
     * @param callingMethod the name of the method calling this method
     * @return a workspace name
     */
    private String composeWorkspaceName(String callingMethod) {
        return "${workspacePrefix}-${callingMethod}"
    }
}
