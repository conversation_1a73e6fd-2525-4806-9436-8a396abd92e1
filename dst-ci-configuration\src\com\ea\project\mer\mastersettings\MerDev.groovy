package com.ea.project.mer.mastersettings

import com.ea.project.mer.Merlin

class MerDev {
    static Class project = Merlin
    static Map preflight_branches = [:]
    static Map branches = [
        'build-main': [
            code_folder            : 'game',
            code_branch            : 'build-main',
            data_folder            : 'game',
            data_branch            : 'build-main',
            non_virtual_code_branch: 'main',
            non_virtual_data_branch: 'main',
        ],
    ]
    static Map autotest_branches = [:]
    static Map integrate_branches = [
        'refresh_to_main': [
            project                     : project,
            source_folder               : 'game',
            source_branch               : 'refresh',
            target_folder               : 'game',
            target_branch               : 'main',
            code                        : true,
            data                        : false,
            parent_to_child             : false,
            data_only_source_branch     : false,
            elipy_call                  : project.elipy_call,
            elipy_install_call          : project.elipy_install_call,
            workspace_root              : project.workspace_root,
            verified_integration        : false,
            trigger_type_integrate      : 'stop',
            no_safe_resolve             : true,
            slack_channel               : '#merlin-integrates',
            slack_always_notify         : true,
            stream_integration          : false,
            branch_mapping              : 'Merlin_refresh_to_main',
            job_label_statebuild        : 'build-main && util',
            exclude_drone               : true,
            freestyle_job_trigger_matrix: [],
        ]
    ]
    static Map copy_branches = [:]
    static Map feature_integrations = [:]
    static Map maintenance_branch = [
        'build-main': [
            code_folder                       : 'game', code_branch: 'build-main',
            data_folder                       : 'game', data_branch: 'build-main',
            include_vault                     : true,
            include_register_release_candidate: true,
            extra_vault_args                  : ['--no-win64-trial'],
            job_label_vault                   : 'build-main && util',
            p4_delete_workspace_label         : 'build-main && util',
        ]
    ]
    static List dashboard_list = []
    static Map dvcs_configs = [
        'merlin-frostbite': [
            project            : project,
            branch_name        : 'build-main',
            slack_channel      : 'nfs-dvcs',
            slack_always_notify: true,
            dry_run            : false,
            trigger_type       : 'cron',
            trigger_string     : 'H/5 * * * 1-6',
            remote_spec        : 'nfs-frostbite',
            workspace_root     : project.workspace_root,
            code_folder        : 'game',
            code_branch        : 'build-main',
            data_folder        : 'game',
            data_branch        : 'build-main',
            elipy_install_call : project.elipy_install_call,
            elipy_call         : project.elipy_call,
            job_label          : 'dvcs',
        ]
    ]
    static List code_downstream_matrix = []
    static Map MAINTENANCE_SETTINGS = [:]
}
