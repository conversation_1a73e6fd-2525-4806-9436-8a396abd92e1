{#
    Command:
        p4_dvcs
            short_help: Runs a P4 DVCS command on the given server and remote spec.

    Arguments:

    Required variables:
        cmd
            required: True
            type: click.Choice(['push', 'fetch'])
        remote_spec
            required: True
        port
            required: True
        user
            required: True
        client
            required: True

    Optional variables:
        dry_run
            is_flag: True
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    p4_dvcs
    --cmd {{ cmd }}
    --remote-spec {{ remote_spec }}
    --port {{ port }}
    --user {{ user }}
    --client {{ client }}
    {%- if dry_run %}
    --dry-run {{ dry_run }}
    {%- endif %}
  displayName: elipy p4_dvcs
