# DRE-<PERSON> as default code owners for everything
* @dre-cobra/cobra-reviewers

# DICE autotest
src/com/ea/matrixfiles/DunAutotestMatrix.groovy @dre-cobra/cobra-reviewers @aledobre1 @sjitariu1 @fpasarica @dpop
src/com/ea/matrixfiles/KinAutotestMatrix.groovy @dre-cobra/cobra-reviewers @aledobre1 @sjitariu1 @fpasarica @dpop
src/com/ea/matrixfiles/FrostedAutotestMatrix.groovy @dre-cobra/cobra-reviewers @aledobre1 @sjitariu1 @fpasarica @dpop
src/com/ea/matrixfiles/BctAutotestMatrix.groovy @dre-cobra/cobra-reviewers @aledobre1 @sjitariu1 @fpasarica @dpop @hoang.tran
src/com/ea/matrixfiles/BctCh1AutotestMatrix.groovy @dre-cobra/cobra-reviewers @aledobre1 @sjitariu1 @fpasarica @dpop @hoang.tran

# DICE project setting
src/com/ea/project/kin/LibBaseline.groovy @dre-cobra/cobra-reviewers @kmoore @m<PERSON><PERSON><PERSON> @ewesterlund @empersson @oandersen
src/com/ea/project/bct/ @dre-cobra/cobra-reviewers @dannilsson-c @emagnusson @ewesterlund @dice/bf-release
src/com/ea/project/bctch1/LibBaseline.groovy @dre-cobra/cobra-reviewers @carl.ryding @mhasslund

# Merlin codeowners
src/com/ea/project/mer/ @dre-cobra/cobra-reviewers @kunal.gujar
