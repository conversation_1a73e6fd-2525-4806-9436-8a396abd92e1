package support

import groovy.io.FileType

import java.nio.file.Files
import java.nio.file.Path

class TestUtil {

    /**
     * Retrieves all seed files
     */
    static List<File> getJobFiles() {
        return getFiles(Path.of('src/seeds'))
    }

    /**
     * Retrieves all masterSetting files
     */
    static List<File> getMasterSettingsFiles() {
        List<File> masterSettingsFiles = []
        Path.of('src/com/ea/project').eachDirRecurse {
            if (it.fileName.toString() == 'mastersettings') {
                masterSettingsFiles += getFiles(it)
            }
        }
        return masterSettingsFiles
    }

    /**
     * Write a single XML file, creating any nested dirs.
     */
    static void writeFile(Path dir, String name, String xml) {
        List<String> tokens = name.split('/')
        Path folderDir = tokens[0..<-1].inject(dir) { Path tokenDir, String token ->
            tokenDir.resolve(token)
        }
        Files.createDirectories(folderDir)
        Path xmlFile = folderDir.resolve("${tokens[-1]}.xml")
        Files.write(xmlFile, xml.bytes)
    }

    private static List<File> getFiles(Path path) {
        List<File> files = []
        path.eachFileRecurse(FileType.FILES) {
            if (it.fileName.toString().endsWith('.groovy')) {
                files << it.toFile()
            }
        }
        return files
    }

}
