"""
sparta_bundle.py
"""
import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import LOGGER, core, filer, data, frostbite_core
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics
from elipy2.exceptions import ELIPYException


@click.command("sparta_bundle", short_help="Performs a Sparta submission build.")
@click.argument("data-dir")
@click.option("--branch", help="Perforce branch/stream name")
@click.option("--code-branch", default=None, help="Perforce branch/stream name")
@click.option(
    "--code-changelist",
    help="Perforce changelist number for last smoked/built pipeline",
    default="",
)
@click.option("--source-bundle-path", help="Path to the Sparta bundle", default="")
@click.option("--bundle-changelist", help="Changelist for the bundle", default="1337")
@click.option("--description", help="Description for the P4 submit", default="")
@click.option("--no-submit", help="Specifies whether or not to submit to P4", default="False")
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    data_dir,
    branch,
    code_branch,
    code_changelist,
    source_bundle_path,
    bundle_changelist,
    description,
    no_submit,
):
    """
    Performs a Sparta submission build.
    Main part of the Sparta code is placed on fbcli, and maintained by the Sparta team.
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    if code_branch is None:
        code_branch = branch
    # Fetch pipeline binary
    _filer = filer.FilerUtils()
    _filer.fetch_code(code_branch, code_changelist, "pipeline", "release")

    # Set data dir
    data.DataUtils.set_datadir(data_dir)

    # Import the main Sparta logic
    try:
        # This is horrible. We need to find a better way around this.
        sparta_bundle = core.import_module_from_file(
            "sparta_bundle",
            os.path.join(
                frostbite_core.get_tnt_root(), "bin", "fbcli", "contrib", "sparta_bundle.py"
            ),
        )
    except Exception as exc:
        LOGGER.error("Unable to import fbcli.contrib.sparta_bundle")
        raise exc

    # Run Sparta bundle job
    exit_code = sparta_bundle.run_sparta_build(
        source_bundle_path, bundle_changelist, no_submit, description
    )
    if exit_code != 0:
        raise ELIPYException(
            "Unable to run sparta_bundle. Please reach out to build or sparta team."
        )
    LOGGER.info("Sparta bundle submitted succesfuly")
