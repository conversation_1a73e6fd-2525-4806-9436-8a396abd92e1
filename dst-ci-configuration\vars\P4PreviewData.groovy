import com.ea.lib.LibCommonNonCps

/**
 * P4PreviewData.groovy
 * Preview data in Perforce, to see if there's anything new to build and to get the latest changelist.
 */
void call(Class project, String previewType, String dataFolder, String dataBranch, String nonVirtualDataFolder = '', String nonVirtualDataBranch = '', Map settingsMap = [:]) {
    // Set filter folder to use, this will only be relevant for a virtual stream.
    String filterFolder = dataFolder
    if (nonVirtualDataFolder != '') {
        filterFolder = nonVirtualDataFolder
    }
    // Set filter branch to use, this will only be relevant for a virtual stream.
    String filterBranch = dataBranch
    if (nonVirtualDataBranch != '') {
        filterBranch = nonVirtualDataBranch
    }

    // Set paths to ignore for all stream based preview types (currently 'navmesh' and 'stream').
    List ignorePathsStream = [
        'AutoIntegrate.json',
    ]

    // Create the workspace name.
    String hostname = 'master'
    if (env.PRODUCTION == 'False') {
        hostname += '-test'
    }
    String workspaceName = "jenkins-${project.short_name}-${hostname}-${env.JOB_NAME}-previewdata"

    // Use method depending on previewType.
    switch (previewType) {
        case 'cherrypick': // Check if there's been a change on what to cherrypick.
            cherrypickPreview(project, filterFolder, filterBranch, settingsMap, workspaceName)
            break
        case 'navmesh': // Check the stream except for the Navmesh asset, to avoid having the Navmesh job trigger itself.
            navmeshPreview(project, dataFolder, dataBranch, filterFolder, filterBranch, settingsMap, workspaceName, ignorePathsStream)
            break
        case 'stream':
            if (nonVirtualDataBranch.startsWith('//')) { // Check a different depot path.
                differentDepotPreview(project, nonVirtualDataBranch, settingsMap, workspaceName)
            } else { // Check the whole stream.
                streamPreview(project, dataFolder, dataBranch, filterFolder, filterBranch, settingsMap, workspaceName, ignorePathsStream)
            }
            break
        default: throw new IllegalArgumentException('Unknown preview type: ' + previewType + ', aborting.')
    }
}

// Method for the previewType 'cherrypick'.
void cherrypickPreview(Class project, String filterFolder, String filterBranch, Map settingsMap, String workspaceName) {
    // Set variables to use.
    List filterPaths = settingsMap.filter_paths_data_cherrypick as List ?: ['AutoIntegrate.json']
    String viewName = ''
    workspaceName += 'cherrypick'
    // Create the view to use.
    for (filterPath in filterPaths) {
        if (viewName != '') {
            viewName += ' \n'
        }
        viewName += project.p4_data_root + '/' + filterFolder + '/' + filterBranch + '/' + filterPath + ' //' +
            workspaceName + '/' + filterPath
    }
    echo "Poll SCM triggers:\n $viewName"
    p4CheckoutManual(project, workspaceName, viewName, settingsMap)
}

// Method for the previewType 'stream', in cases where we have data located in a different depot path.
void differentDepotPreview(Class project, String nonVirtualDataBranch, Map settingsMap, String workspaceName) {
    String viewName = nonVirtualDataBranch + '/... //' + workspaceName + '/' + settingsMap.dataset + '/...'
    echo "Poll SCM triggers:\n $viewName"
    p4CheckoutManual(project, workspaceName, viewName, settingsMap)
}

// Method for the previewType 'navmesh'.
void navmeshPreview(Class project, String dataFolder, String dataBranch, String filterFolder, String filterBranch, Map settingsMap, String workspaceName, List ignorePathsStream) {
    // Set variables to use.
    String dataset = settingsMap.dataset
    workspaceName += 'navmesh'
    // Check if we have a navmesh asset defined, if so create the path to it and add this to the paths to ignore.
    if (settingsMap.navmesh_asset == null) {
        throw new IllegalArgumentException('Navmesh asset required but not defined, aborting.')
    }
    String navmesh_path = 'Source/' + settingsMap.navmesh_asset.replace('\\', '/')
    if (project.single_perforce_server) {
        navmesh_path = dataset + '/' + navmesh_path
    }
    List ignorePaths = [navmesh_path] + ignorePathsStream
    // Create stream and filter to use.
    String streamName = project.p4_data_root + '/' + dataFolder + '/' + dataBranch
    String filterName = project.p4_data_root + '/' + filterFolder + '/' + filterBranch
    if (project.single_perforce_server) {
        filterName += '/' + dataset
    }
    for (ignorePath in ignorePaths) {
        filterName += '\n-' + project.p4_data_root + '/' + filterFolder + '/' + filterBranch + '/' + ignorePath
    }
    echo "Poll SCM triggers:\n $filterName"
    p4CheckoutStream(project, streamName, workspaceName, filterName, settingsMap)
}

// Standard method for the previewType 'stream'.
void streamPreview(Class project, String dataFolder, String dataBranch, String filterFolder, String filterBranch, Map settingsMap, String workspaceName, List ignorePathsStream) {
    // Set variables to use.
    String dataset = settingsMap.dataset
    List ignorePaths = settingsMap.ignore_paths_data_preview as List ?: []
    ignorePaths += ignorePathsStream
    String extraDataPath = settingsMap.extra_data_path ?: ''
    // Create stream and filter to use.
    String streamName = project.p4_data_root + '/' + dataFolder + '/' + dataBranch
    String filterName = project.p4_data_root + '/' + filterFolder + '/' + filterBranch
    if (project.single_perforce_server) {
        filterName += '/' + dataset
    }
    for (ignorePath in ignorePaths) {
        filterName += '\n-' + project.p4_data_root + '/' + filterFolder + '/' + filterBranch + '/' + ignorePath
    }
    echo "Poll SCM triggers:\n $filterName"
    p4CheckoutStream(project, streamName, workspaceName, filterName, settingsMap)
    // For a special type of streams where we need to check also in the stream used as base stream.
    if (extraDataPath != '') {
        // Set variables to use.
        String dataChangelist = env.P4_CHANGELIST // Keep this value for later.
        String workspaceNameExtra = workspaceName + 'extra'
        // Create filter to use.
        String filterNameExtra = project.p4_data_root + '/' + extraDataPath
        if (project.single_perforce_server) {
            filterNameExtra += '/' + dataset
        }
        for (ignorePath in ignorePaths) {
            filterNameExtra += '\n-' + project.p4_data_root + '/' + extraDataPath + '/' + ignorePath
        }
        p4CheckoutStream(project, streamName, workspaceNameExtra, filterNameExtra, settingsMap)
        String dataChangelistExtra = env.P4_CHANGELIST
        if (Integer.parseInt(dataChangelistExtra) > Integer.parseInt(dataChangelist)) { // Use the latest of the two changelists.
            dataChangelist = dataChangelistExtra
        }
        EnvInject(currentBuild, ['P4_CHANGELIST': dataChangelist])
    }
}

// Checkout used for a previewType based on the whole stream.
void p4CheckoutStream(Class project, String streamName, String workspaceName, String filterName, Map branchInfo) {
    def p4_data_creds = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_data_creds', '', project)
    checkout perforce(
        credential: p4_data_creds,
        workspace: streamSpec(
            charset: 'none',
            pinHost: true,
            streamName: streamName,
            format: workspaceName
        ),
        filter: [viewFilter(filterName)],
        populate: previewOnly(quiet: true)
    )
}

// Checkout used for a previewType based on a specified view.
void p4CheckoutManual(Class project, String workspaceName, String viewName, Map branchInfo) {
    def p4_data_creds = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_data_creds', '', project)
    checkout perforce(
        credential: p4_data_creds,
        workspace: manualSpec(
            charset: 'none',
            pinHost: true,
            name: workspaceName,
            spec: clientSpec(
                allwrite: false,
                clobber: true,
                compress: false,
                locked: false,
                modtime: false,
                rmdir: false,
                streamName: '',
                line: 'LOCAL',
                view: viewName,
                changeView: '',
                type: 'WRITABLE',
                serverID: '',
                backup: true
            ),
            cleanup: false,
            syncID: ''
        ),
        populate: previewOnly(
            quiet: true
        )
    )
}
