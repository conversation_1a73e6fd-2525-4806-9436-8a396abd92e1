# Shift service account user DICE.Auth Monkey.Shift
# Duplicated settings lower in the settings list will get higher priority
buildtype: "QA"
milestone: "Production"
distribution_type: "InternalOnly"
retention_policy: "SpaceAvailable"
priority: ""
version: "2.0"

linuxserver:
  content:
    digital:
      file_names:
        - "*_Server_*_Binaries.zip"
      supplemental_files:
        - "*.dbg"
      directory:
        - ""
    files:
      file_names:
        - "*"
      supplemental_files:
        - "*.xml"
      directory:
        - "Config"
        - "Data"
        - "Scripts"
  settings:
    bf-playtest-gnt:
      playtest:
        final:
          digital:
            sku_id: "012ba272-2fad-4f37-a9e3-d8b417b78932"
            sku_name: "Server - PLAYTEST (bf-playtest-gnt linuxserver digital fina)"
    task1:
      final:
        files:
          sku_id: "ed83eaa6-b0c0-49e7-b815-f6821691d5ae"
          sku_name: "Server - WW (task1 linuxserver final files)"
        digital:
          sku_id: "6bc699aa-4d26-468b-97c0-29bfc957d54c"
          sku_name: "Server - WW (task1 linuxserver final digital)"
    task8:
      final:
        files:
          sku_id: "3bb04920-4ff1-401f-a85c-ced095ea9dc8"
          sku_name: "Server - WW (task8 linuxserver final files)"
        digital:
          sku_id: "6f20c04e-adc1-4e5c-8829-4f4ac428c050"
          sku_name: "Server - WW (task8 linuxserver final digital)"
    CH1-stage:
      final:
        digital:
          sku_id: "11d457c1-1f9d-43ec-a0b1-f40210e34a6c"
          sku_name: "Server - WW (CH1-stage linuxserver final digital)"
    CH1-release:
      digital:
        final:
          sku_id: "9259c710-1ff9-4f3e-bd87-aa7f96b40d51"
          sku_name: "Server - WW (CH1-release linuxserver digital final)"
    CH1-qol:
      final:
        files:
          sku_id: "8af8baf3-e5f5-41ec-9ae6-dc42eb38182b"
          sku_name: "Server - WW (CH1-qol linuxserver final files)"
        digital:
          sku_id: "8c63d241-9c46-4c22-8e0a-e88c42a338f6"
          sku_name: "Server - WW (CH1-qol linuxserver final digital)"
    trunk-code-dev:
      final:
        files:
          sku_id: "7d752d1b-1293-4e34-b615-674d7593073e"
          sku_name: "Server - WW (trunk-code-dev linuxserver files final)"
        digital:
          sku_id: "a7ec2994-13c7-4966-9258-7eb99074482e"
          sku_name: "Server - WW (trunk-code-dev linuxserver digital final)"
    CH1-code-dev:
      final:
        files:
          sku_id: "86e0e299-ab49-4ca5-83ea-850ec0a373f2"
          sku_name: "Server - WW (CH1-code-dev linuxserver files final)"
        digital:
          sku_id: "bfeba099-d852-44c3-a51f-2a9c766ed59f"
          sku_name: "Server - WW (CH1-code-dev linuxserver digital final)"
    CH1-content-dev:
      final:
        digital:
          sku_id: "c3531e80-e724-404e-b626-bd2ed6156189"
          sku_name: "Server - WW (CH1-content-dev linuxserv digital final)"
        files:
          sku_id: "5007bd14-cb7a-494a-9ea4-91da18b5c658"
          sku_name: "Server - WW (CH1-content-dev linuxserver files final)"
    CH1-SP-content-dev:
      final:
        digital:
          sku_id: "093378f1-0e3e-43fa-91e8-a5b03416a4b2"
          sku_name: "Server - WW (CH1-SP-content-dev linuxserver digit fin)"
        files:
          sku_id: "24bfc250-068b-4d0c-a05a-79acee9e00e2"
          sku_name: "Server - WW (CH1-SP-content-dev linuxserver files fin)"
    bflabs:
      digital:
        final:
          sku_id: "bcfa52e8-77aa-4bbe-8731-ba88a3b1f434"
          sku_name: "Server - WW (bflabs linuxserver digital final)"
    CH1-bflabs-stage:
      digital:
        final:
          sku_id: "00764eb6-1951-4b3a-aa75-7c8f77dfb877"
          sku_name: "Server - WW (CH1-bflabs-stage linuxserv digital final)"
    CH1-bflabs-release:
      digital:
        final:
          sku_id: "e982a0b5-7438-495e-ba7b-0be637fcacdf"
          sku_name: "Server - WW (CH1-bflabs-release linuxserv digital fin)"
      files:
        final:
          sku_id: "b5596b31-05a1-473f-ae04-5dbc0366f752"
          sku_name: "Server - WW (CH1-bflabs-release linuxserv files final)"
    CH1-bflabs-qol:
      digital:
        final:
          sku_id: "311f4bb8-f8f2-4ac6-b98b-982fccf5e9ab"
          sku_name: "Server - WW (CH1-bflabs-qol linuxserver digital final)"
      files:
        final:
          sku_id: "32feed68-f4fb-42d9-bade-505b270bd968"
          sku_name: "Server - WW (CH1-bflabs-qol linuxserv files final)"
    trunk-playtest:
      final:
        digital:
          sku_id: "63f08d9e-b89d-464e-8aaa-58d0aefb9e34"
          sku_name: "Server - WW (trunk-playtest linuxserver digital final)"
        files:
          sku_id: "23c7a380-3926-49fb-b1c8-8fcbb16a9de1"
          sku_name: "Server - WW (trunk-playtest linuxserver files final)"
    CH1-playtest:
      digital:
        final:
          sku_id: "4013f190-69d3-4cdf-b480-424dff665ec8"
          sku_name: "Server - WW (CH1-playtest linuxserver digital final)"
    CH1-playtest-gnt:
      digital:
        final:
          sku_id: "092d87a2-fa65-471c-a10c-e3bdd4a50038"
          sku_name: "Server - WW (CH1-playtest-gnt linuxserv digital final)"
    CH1-playtest-gnt-na:
      digital:
        final:
          sku_id: "206dfd4a-922e-4007-8ba8-1d6621f6a93c"
          sku_name: "Server - WW (CH1-playtest-gnt-na linuxserver digital)"
    CH1-release-playtest-gnt-na:
      digital:
        final:
          sku_id: "8461551c-f480-4685-98ca-4781bb559185"
          sku_name: "Server - WW (CH1-release-playtest-gnt-na linuxserver)"
    CH1-playtest-san:
      digital:
        final:
          sku_id: "fad62e2d-b4f5-478e-99a8-10b380b45d9f"
          sku_name: "Server - WW (CH1-playtest-san linuxserv digital final)"
    CH1-playtest-stage:
      digital:
        final:
          sku_id: "3a04a863-b0b5-416a-81a2-060408837a89"
          sku_name: "Server - WW (CH1-playtest-stage linuxserv digit final)"
    CH1-event:
      digital:
        final:
          sku_id: "56ea171b-0a81-4289-af2e-befcd73d1aa0"
          sku_name: "Server - WW (CH1-event linuxserv digital final)"
    CH1-event-release:
      digital:
        final:
          sku_id: "31aab9c3-5b1b-4b20-bc65-a584a62551eb"
          sku_name: "Server - WW (CH1-event-release linuxserv digit final)"
    task13:
      files:
        release:
          sku_id: "043b51f7-1017-44c2-9a0d-bc19d26ed5c5"
          sku_name: "Server - WW (task13 linuxserver files release)"
          distribution_type: "ExternalReview"
        final:
          sku_id: "a48239c8-2a5d-428c-87ff-ffe2193550e0"
          sku_name: "Server - WW (task13 linuxserver files final)"
          distribution_type: "ExternalReview"

ps5:
  content:
    files:
      file_names:
        - "builtLevels.json"
        - "*build*.json"
        - "*.prx"
        - "*.buildlayout"
        - "*.BuildSettings"
        - "eboot.bin"
        - "Elipy_MD5_hashes.csv"
        - "*.elf"
        - "package.mft"
      supplemental_files:
        - "*.xml"
        - "*BattlefieldGame*.pdb"
      directory:
        - "Config"
        - "data"
        - "Scripts"
        - "sce_sys"
    digital:
      file_names:
        - "*-V????.pkg"
      upload_loop_filenames:
        - "*GLACIERGAME*V????.pkg"
      supplemental_files:
        - "chunks*.gp5"
      directory:
        - ""
    digital_combine:
      file_names:
        - "*-V????.pkg"
      upload_loop_filenames:
        - "*GLACIERGAME*V????.pkg"
        - "*CONTENTPACK00004*V????.pkg"
        - "*CONTENTPACK00003*V????.pkg"
        - "*CONTENTPACK00002*V????.pkg"
        - "*CONTENTPACK00001*V????.pkg"
        - "*HDCONTENTPACK000*V????.pkg"
        - "*HDCONTENTPACK004*V????.pkg"
        - "*HDCONTENTPACK003*V????.pkg"
        - "*HDCONTENTPACK002*V????.pkg"
        - "*HDCONTENTPACK001*V????.pkg"
      supplemental_files:
        - "chunks*.gp5"
        - "*_CL_*.txt"
      directory:
        - ""
    patch_combine:
      file_names:
        - "*-V????.pkg"
      upload_loop_filenames:
        - "*LOREMIPSUMX*V????.pkg"
        - "*BFLABSCONTENT001*V????.pkg"
        - "*BFLABSCONTENT002*V????.pkg"
        - "*BFLABSCONTENT003*V????.pkg"
        - "*LOREMIPSUMX*V????.pkg.remastered.pkg"
        - "*BFLABSCONTENT001*V????.pkg.remastered.pkg"
        - "*BFLABSCONTENT002*V????.pkg.remastered.pkg"
        - "*BFLABSCONTENT003*V????.pkg.remastered.pkg"
      supplemental_files:
        - "chunks*.gp5"
        - "*_CL_*.txt"
      directory:
        - ""
    patch:
      file_names:
        - "*-V????.pkg"
      upload_loop_filenames:
        - "*GLAEVENT*V????.pkg"
        - "*GLAEVENT*V????.pkg.remastered.pkg"
      supplemental_files:
        - "chunks*.gp5"
      directory:
        - ""
  settings:
    trunk-code-dev:
      upload_loop:
        - "true"
      final:
        files:
          sku_id: "7707cd33-f7f4-4666-900d-34d8839c0b96"
          sku_name: "FG - EU trunk-code-dev ps5 files final)"
        digital:
          sku_id: "54f1a675-e0c1-4348-9b62-ad5e76f19c66"
          sku_name: "FG - EU (trunk-code-dev ps5 digital final)"
      performance:
        files:
          sku_id: "1c71cb23-4e31-44eb-b8c5-a7961eec383e"
          sku_name: "FG - EU (trunk-code-dev ps5 files performance)"
    CH1-code-dev:
      upload_loop:
        - "true"
      final:
        files:
          sku_id: "fbefd98b-ab71-4ac2-8590-c7d399769fbb"
          sku_name: "FG - EU (CH1-code-dev ps5 files final)"
        digital:
          sku_id: "a9662194-95d8-4571-8cb1-e156894fbdce"
          sku_name: "FG - EU (CH1-code-dev ps5 digital final)"
      performance:
        files:
          sku_id: "b9a3e72e-1148-443c-bcbb-2f170110f3aa"
          sku_name: "FG - EU (CH1-code-dev ps5 files performance)"
    trunk-content-dev:
      #final:
        #digital:
        #  sku_id: "c00b3dcd-d9af-44dc-aa9a-d5273f21d235"
        #  sku_name: "FG - EU (trunk-content-dev-er ps5 digital final)"
        #files:
        #  sku_id: "e0e278ec-daa7-447a-8f2e-fb03bcd9a2aa"
        #  sku_name: "FG - EU (trunk-content-dev-er ps5 files final)"
      retail:
        digital:
          sku_id: "a70897a9-8352-4553-8418-f4e126a0466c"
          sku_name: "FG - EU (trunk-content-dev-er ps5 digital retail)"
    CH1-content-dev:
      upload_loop:
        - "true"
      final:
        digital_combine:
          sku_id: "664f3200-7b57-4ff8-993d-8049e6edbcf9"
          sku_name: "FG - EU (CH1-content-dev ps5 combine final)"
        files:
          sku_id: "5f82776c-648f-483b-8f93-c6de3703b49a"
          sku_name: "FG - WW (CH1-content-dev ps5 files final)"
          distribution_type: "ExternalReview"
      performance:
        files:
          sku_id: "7420e37e-88dc-431a-a03b-c66e573a2b83"
          sku_name: "FG - WW (CH1-content-dev ps5 files performance)"
      retail:
        digital_combine:
          sku_id: "eb7e368e-5dc8-44e1-9cba-76c3c44887c8"
          sku_name: "FG - WW (CH1-content-dev ps5 retail combine)"
    dev-na-to-trunk:
      final:
        files:
          sku_id: "3be0407f-d81d-42f3-88e9-3f3b49ecc6be"
          sku_name: "FG - EU (dev-na-to-trunk ps5 files final)"
    dev-na-to-trunk-sub:
      final:
        files:
          sku_id: "d2da29f3-0b04-471d-ae68-e1d452b4f4e4"
          sku_name: "FG - EU (dev-na-to-trunk-sub ps5 files final)"
    bf-playtest-gnt:
      playtest:
        final:
          files:
            sku_id: "ce50810b-7950-47ba-9577-72501cc7ac28"
            sku_name: "FG - PLAYTEST (bf-playtest-gnt ps5 files final)"
            incremental_delivery: "true"
        retail:
          digital:
            sku_id: "f8a0beee-866e-4457-b6b4-8c53f50aae43"
            sku_name: "FG - PLAYTEST (bf-playtest-gnt ps5 digital retail)"
        performance:
          digital:
            sku_id: "0499516b-a084-43cc-9516-93412eb94988"
            sku_name: "FG - EU (bf-playtest-gnt ps5 digital performance)"
    bf-playtest-san:
      playtest:
        performance:
          files:
            sku_id: "c91d418b-8218-40af-bd2b-f2137824b74c"
            sku_name: "FG - EU (bf-playtest-san ps5 files)"
            incremental_delivery: "true"
    task1:
      upload_loop:
        - "true"
      performance:
        files:
          sku_id: "d9c0e598-55fa-4362-861d-4b35eb956bc9"
          sku_name: "FG - EU (task1 ps5 performance files)"
      final:
        files:
          sku_id: "86681c67-4d82-4210-9acc-9196881c5eaa"
          sku_name: "FG - EU (task1 ps5 final files)"
#        digital:
#          sku_id: "5f40a81a-7832-4f30-b3d8-1f67d05b92a4"
#          sku_name: "FG - EU (task1 ps5 final digital)"
    task8:
      upload_loop:
        - "true"
      performance:
        files:
          sku_id: "7d794ac0-9ddc-4f78-8153-d3e678b97594"
          sku_name: "FG - EU (task8 ps5 performance files)"
    CH1-stage:
      upload_loop:
        - "true"
      performance:
        files:
          sku_id: "645a6a2a-9dcc-4674-93ff-c964f44c40e5"
          sku_name: "FG - EU (CH1-stage ps5 performance files)"
      final:
        files:
          sku_id: "17695d17-ef26-48a6-9865-707d145b74dd"
          sku_name: "FG - EU (CH1-stage ps5 final files)"
          distribution_type: "ExternalReview"
      digital_combine:
        retail:
          sku_id: "4d8ecefd-0fe0-4ef9-bcd8-1e98a8b46c5c"
          sku_name: "FG - WW (CH1-stage ps5 combine retail)"
        final:
          sku_id: "f911b58f-8288-4f44-a078-9fa259775973"
          sku_name: "FG - WW (CH1-stage ps5 combine final)"
    CH1-release:
      upload_loop:
        - "true"
      files:
        final:
          sku_id: "8be34525-f62d-48f6-acc4-909648c09dae"
          sku_name: "FG - WW (CH1-release ps5 files final)"
          distribution_type: "ExternalReview"
        performance:
          sku_id: "3e92d459-e1b4-4c45-85a5-d4b6a875d115"
          sku_name: "FG - WW (CH1-release ps5 files performance)"
      digital_combine:
        retail:
          sku_id: "f8f0a60f-c863-4be3-b207-44b2fac5b5ad"
          sku_name: "FG - WW (CH1-release ps5 combine retail)"
        final:
          sku_id: "21d68bd2-45b6-4dc9-a5c4-e750d807b7ca"
          sku_name: "FG - WW (CH1-release ps5 combine final)"
    CH1-qol:
      upload_loop:
        - "true"
      performance:
        files:
          sku_id: "687bc94b-a6e9-4c57-aac1-cabb08746260"
          sku_name: "FG - EU (CH1-qol ps5 performance files)"
      final:
        files:
          sku_id: "b97a52e2-3359-4a0a-91c2-39d4f0e87e40"
          sku_name: "FG - EU (CH1-qol ps5 final files)"
      digital:
        final:
          sku_id: "efca2cf6-2fea-4a1c-bef7-a004273509c2"
          sku_name: "FG - WW (CH1-qol ps5 digital final)"
        retail:
          sku_id: "65ebf40d-6606-4487-a5b0-270077b82156"
          sku_name: "FG - WW (CH1-qol ps5 digital retail)"
      digital_combine:
        retail:
          sku_id: "f621c02a-2ddf-4b88-8ded-dec908ff51f6"
          sku_name: "FG - WW (CH1-qol ps5 combine retail)"
        final:
          sku_id: "b19af917-0362-41ea-99cb-99f691216279"
          sku_name: "FG - WW (CH1-qol ps5 combine final)"
    bf-playtest-sp:
      performance:
        files:
          sku_id: "57d4ce46-b331-4178-84ca-63dcf1b93c1f"
          sku_name: "FG - EU (bf-playtest-sp ps5 files performance)"
          incremental_delivery: "true"
    bflabs:
      files:
        final:
          sku_id: "bd2d0dcf-15ae-4b2f-82f7-9ea8bd3f1009"
          sku_name: "FG - WW (bflabs ps5 files final)"
      digital:
        final:
          sku_id: "21d3c1fb-3b59-4319-8cf1-1ce4fba40214"
          sku_name: "FG - WW (bflabs ps5 digital final)"
        retail:
          sku_id: "1c7fd4e7-3564-448c-af43-6f9df86d7b7a"
          sku_name: "FG - WW (bflabs ps5 digital retail)"
      patch:
        final:
          sku_id: "02fa2a8d-5e6e-4e81-a551-3a2fd4ef6792"
          sku_name: "Patch - WW (bflabs ps5 patch final)"
        retail:
          sku_id: "fc957f9e-cae4-4c95-8ac8-c7354f7ecb05"
          sku_name: "Patch - WW (bflabs ps5 patch retail)"
    CH1-bflabs-stage:
      upload_loop:
        - "true"
      files:
        final:
          sku_id: "3b0d035f-ede2-4b92-9516-1361851b1c71"
          sku_name: "FG - WW (CH1-bflabs-stage ps5 files final)"
      patch:
        final:
          sku_id: "cdccc4a6-986d-427b-b7ed-713616bfd78e"
          sku_name: "Patch - WW (CH1-bflabs-stage ps5 patch final)"
        retail:
          sku_id: "51859aa5-fdfd-4736-b6b9-2267c150f3b1"
          sku_name: "Patch - WW (CH1-bflabs-stage ps5 patch retail)"
      patch_combine:
        final:
          sku_id: "fad861ec-fbf7-4c91-aa94-d675a95a613c"
          sku_name: "Patch - WW (CH1-bflabs-stage ps5 combine final)"
        retail:
          sku_id: "30f5cbf7-b816-4ccc-bbf7-29c5fcaecc28"
          sku_name: "Patch - WW (CH1-bflabs-stage ps5 combine retail)"
    CH1-bflabs-release:
      upload_loop:
        - "true"
      files:
        final:
          sku_id: "f02f27ee-69c1-47aa-8a05-0e9397b11a3b"
          sku_name: "FG - WW (CH1-bflabs-release ps5 files final)"
      patch:
        final:
          sku_id: "c2555dd0-1f5f-4cc8-a4c2-6b971a0367e4"
          sku_name: "Patch - WW (CH1-bflabs-release ps5 patch final)"
        retail:
          sku_id: "47835c4b-44de-4b9c-afb6-a15b67962a54"
          sku_name: "Patch - WW (CH1-bflabs-release ps5 patch retail)"
      patch_combine:
        final:
          sku_id: "fdb27100-4419-4439-bf37-bdba641acdff"
          sku_name: "Patch - WW (CH1-bflabs-release ps5 combine final)"
        retail:
          sku_id: "9395d412-1590-48f1-baa4-794100d6b6a3"
          sku_name: "Patch - WW (CH1-bflabs-release ps5 combine retail)"
    CH1-bflabs-qol:
      upload_loop:
        - "true"
      files:
        final:
          sku_id: "5e8df057-95df-4cdf-a391-0843f81e9339"
          sku_name: "FG - WW (CH1-bflabs-qol ps5 files final)"
      patch:
        final:
          sku_id: "eae9f18f-1434-41ac-a17b-68c1baac5144"
          sku_name: "Patch - WW (CH1-bflabs-qol ps5 patch final)"
        retail:
          sku_id: "c475e248-f928-43b3-af10-88a0afd25e4c"
          sku_name: "Patch - WW (CH1-bflabs-qol ps5 patch retail)"
      patch_combine:
        final:
          sku_id: "355ee595-c454-4129-ac9e-3397bec1e0c7"
          sku_name: "Patch - WW (CH1-bflabs-qol ps5 combine final)"
        retail:
          sku_id: "28ef6e2f-61bc-4b73-8521-8ae0ec9d033c"
          sku_name: "Patch - WW (CH1-bflabs-qol ps5 combine retail)"
    trunk-playtest:
      upload_loop:
        - "true"
      final:
        files:
          sku_id: "57a0ca1e-4546-4e94-8b98-a2e8469052b9"
          sku_name: "FG - WW (trunk-playtest ps5 files final)"
        digital:
          sku_id: "3810efb0-a928-4b50-8554-7e38c5684148"
          sku_name: "FG - WW (trunk-playtest ps5 final digital)"
      performance:
        files:
          sku_id: "7e3f4585-8a0b-438e-a4a8-db71ec768d1a"
          sku_name: "FG - WW (trunk-playtest ps5 files performance)"
    CH1-playtest:
      files:
        performance:
          sku_id: "8fc592db-374c-423f-a1d7-b8d711f7687c"
          sku_name: "FG - EU (CH1-playtest ps5 files performance)"
    CH1-playtest-gnt:
      files:
        performance:
          sku_id: "0b957abc-d561-4265-bfa4-b7f072182be4"
          sku_name: "FG - EU (CH1-playtest-gnt ps5 files)"
    CH1-playtest-gnt-na:
      files:
        performance:
          sku_id: "ff015912-3031-4b11-9267-8ed2120bf113"
          sku_name: "FG - EU (CH1-playtest-gnt-na ps5 files)"
    CH1-release-playtest-gnt-na:
      files:
        performance:
          sku_id: "da5042f0-d36e-4c77-a099-b6d13b5d70ea"
          sku_name: "FG - EU (CH1-release-playtest-gnt-na ps5 files)"
    CH1-playtest-san:
      files:
        performance:
          sku_id: "e2281352-9604-4a76-9cc9-563cb86b1163"
          sku_name: "FG - EU (CH1-playtest-san ps5 files performance)"
    CH1-playtest-san-s2:
      files:
        performance:
          sku_id: "78a9b1fe-798b-40e6-8dea-c2e3380d3b96"
          sku_name: "FG - EU (CH1-playtest-san-s2 ps5 files perf)"
    CH1-playtest-sp:
      files:
        performance:
          sku_id: "4f36d289-c963-4881-bea0-38142d4744db"
          sku_name: "FG - EU (CH1-playtest-sp ps5 files performance)"
    CH1-stage-playtest-sp:
      files:
        performance:
          sku_id: "e205dbb9-b7a9-4996-88bf-4d27c23f6118"
          sku_name: "FG - EU (CH1-stage-playtest-sp ps5 files perf)"
    CH1-playtest-stage:
      files:
        performance:
          sku_id: "33191c36-d74c-48db-8142-5a32e6ec1805"
          sku_name: "FG - EU (CH1-playtest-stage ps5 files performance)"
    CH1-to-trunk:
      files:
        final:
          sku_id: "8e59a996-90f6-4f7a-a8cc-75ff719003fa"
          sku_name: "FG - EU (CH1-to-trunk ps5 files final)"
    2024_1_dev-bf-to-CH1:
      files:
        final:
          sku_id: "604af809-bab5-466f-bcba-d6ac447e4d9a"
          sku_name: "FG - EU (2024_1_dev-bf-to-CH1 ps5 files final)"
    CH1-event:
      upload_loop:
        - "true"
      files:
        final:
          sku_id: "66cf2c7b-8eb6-46f0-9d64-92cef2d79a72"
          sku_name: "FG - WW (CH1-event ps5 files final)"
        retail:
          sku_id: "8310d0f5-cd05-4604-b283-9a2f1bf20b85"
          sku_name: "FG - WW (CH1-event ps5 files retail)"
      patch:
        performance:
          sku_id: "6de95a84-a2b1-4295-ad08-ae1aa8fcab05"
          sku_name: "Patch - WW (CH1-event ps5 digital performance)"
        final:
          sku_id: "d0df53d0-ab92-420e-a0c7-c4b9c451cb45"
          sku_name: "Patch - WW (CH1-event ps5 patch final)"
        retail:
          sku_id: "0d1abedd-6470-4ac9-bca1-e4e2fe53e1fa"
          sku_name: "Patch - WW (CH1-event ps5 patch retail)"
    CH1-event-release:
      files:
        final:
          sku_id: "cc864fe2-d029-4166-b989-577a7c76a965"
          sku_name: "FG - WW (CH1-event-release ps5 files final)"
      digital:
        final:
          sku_id: "0086050d-6747-4d89-a076-910a480c419f"
          sku_name: "FG - WW (CH1-event-release ps5 digtial final)"
        retail:
          sku_id: "7a8ca811-87b1-46d2-a17f-218e2ab6da9e"
          sku_name: "FG - WW (CH1-event-release ps5 digtial retail)"

server:
  content:
    digital:
      file_names:
        - "*_Server_*_Binaries.zip"
      supplemental_files:
        - "*build*.json"
      directory:
        - ""
    files:
      file_names:
        - "builtLevels.json"
        - "*build*.json"
        - "*.buildlayout"
        - "*.exe"
        - "*.dll"
        - "*.BuildSettings"
        - "Elipy_MD5_hashes.csv"
      supplemental_files:
        - "*.xml"
      directory:
        - "Config"
        - "Data"
        - "Scripts"
  settings:
    dev-na-to-trunk:
      final:
        files:
          sku_id: "0715a16c-b4b4-4e3e-955f-c5eb7d0db929"
          sku_name: "Server - WW (dev-na-to-trunk server files final)"
    dev-na-to-trunk-sub:
      final:
        files:
          sku_id: "e3462005-f378-4964-aa86-cec3825b4efb"
          sku_name: "Server - WW (dev-na-to-trunk-sub server files final)"
    trunk-code-dev:
      final:
        files:
          sku_id: "b713e218-9746-4e7a-aa61-2db9db1fedca"
          sku_name: "Server - WW (trunk-code-dev server files final)"
    CH1-code-dev:
      final:
        files:
          sku_id: "4ed24fd5-860c-439a-b7c8-09bd64c05293"
          sku_name: "Server - WW (CH1-code-dev server files final)"
    CH1-content-dev:
      final:
        digital:
          sku_id: "a6e749aa-a763-4d62-be47-1817447bd506"
          sku_name: "Server - WW (CH1-content-dev server digital final)"
        files:
          sku_id: "e5a8b355-41dd-4c4a-827b-56f892917160"
          sku_name: "Server - WW (CH1-content-dev server files final)"
    CH1-SP-content-dev:
      final:
        files:
          sku_id: "66999c1e-e6b4-4cd2-81d2-2601246dfcf4"
          sku_name: "Server - WW (CH1-SP-content-dev server files final)"
        digital:
          sku_id: "6ad8071e-1f72-48a3-a676-13cd37b56b2a"
          sku_name: "Server - WW (CH1-SP-content-dev server digital final)"
    bf-playtest-maps:
      playtest:
        final:
          files:
            sku_id: "0d2f9869-8133-48ae-b021-2e790dfa5c44"
            sku_name: "Server - WW (bf-playtest-maps server files final)"
    task1:
      final:
        files:
          sku_id: "806cf15c-fe95-4732-badd-fd19a292b518"
          sku_name: "Server - WW (task1 server final files)"
        digital:
          sku_id: "fb68fae3-801a-41c4-a403-dee3bbcdb816"
          sku_name: "Server - WW (task1 server final digital)"
    task8:
      final:
        files:
          sku_id: "01fbaa20-e8d3-40ab-83bc-5ef37f6ba032"
          sku_name: "Server - WW (task8 server final files)"
        digital:
          sku_id: "d22ce54b-e94f-4b02-a35f-6d41f3c03494"
          sku_name: "Server - WW (task8 server final digital)"
    CH1-stage:
      final:
        files:
          sku_id: "14a453b5-0019-40dc-b83a-b0fff15b2eaf"
          sku_name: "Server - WW (CH1-stage server final files)"
    CH1-release:
      files:
        final:
          sku_id: "b2a5c132-dd94-4dfb-82de-26586df7a1d1"
          sku_name: "Server - WW (CH1-release server files final)"
    CH1-qol:
      final:
        files:
          sku_id: "15f69cb9-2153-4dc1-800f-22175b61e365"
          sku_name: "Server - WW (CH1-qol server final files)"
        digital:
          sku_id: "9c88599a-53c1-487a-862f-e574d51283a4"
          sku_name: "Server - WW (CH1-qol server final digital)"
    bflabs:
      files:
        final:
          sku_id: "e8ea9993-82e5-4b70-8937-5e4e7fe1fa1f"
          sku_name: "Server - WW (bflabs server files final)"
      digital:
        final:
          sku_id: "b81a2cfc-2876-49ad-9889-fa292a772e57"
          sku_name: "Server - WW (bflabs server digital final)"
    CH1-bflabs-stage:
      final:
        files:
          sku_id: "45272b1b-fcb2-454e-9d0d-750b215b9f09"
          sku_name: "Server - WW (CH1-bflabs-stage server files final)"
    CH1-bflabs-release:
      final:
        files:
          sku_id: "70c27117-7f5b-4cae-a31a-c93c065dfef5"
          sku_name: "Server - WW (CH1-bflabs-release server files final)"
    trunk-playtest:
      final:
        files:
          sku_id: "fda8904d-3607-4809-90c3-d6be83eefe1f"
          sku_name: "Server - WW (trunk-playtest server files final)"
    CH1-playtest-gnt:
      files:
        final:
          sku_id: "74d99d07-7562-48d4-b561-548d43143580"
          sku_name: "Server - WW (CH1-playtest-gnt server files final)"
    CH1-playtest-gnt-na:
      files:
        final:
          sku_id: "beb61d1d-bb13-42db-a1c1-769b3b53c0b1"
          sku_name: "Server - WW (CH1-plyatest-gnt-na server files final)"
    CH1-playtest-maps:
      files:
        final:
          sku_id: "1bcc4142-9999-47ad-b50a-9b90ad157724"
          sku_name: "Server - WW (CH1-playtest-maps server files final)"
    CH1-to-trunk:
      files:
        final:
          sku_id: "89590295-0176-41a7-a54e-93c1ef426217"
          sku_name: "Server - WW (CH1-to-trunk server files final)"
    2024_1_dev-bf-to-CH1:
      files:
        final:
          sku_id: "909e7d1e-3dd3-4d48-9cf1-f402c4caba69"
          sku_name: "Server - WW (2024_1_dev-bf-to-CH1 server files final)"
    CH1-event:
      files:
        final:
          sku_id: "c4078b95-d1a7-4ecd-869b-7ec13c22327e"
          sku_name: "Server - WW (CH1-event server files final)"
      digital:
        final:
          sku_id: "10384c09-1cfa-45d5-afd5-4c624072e53d"
          sku_name: "Server - WW (CH1-event server digital final)"
    CH1-event-release:
      files:
        final:
          sku_id: "92953cf1-9c97-4c61-bef4-d45a45e6d3b1"
          sku_name: "Server - WW (CH1-event-release server files final)"
    task13:
      files:
        release:
          sku_id: "57ebfd26-eae1-45df-afc1-28f7126dcb6c"
          sku_name: "Server - WW (task13 server files release)"
          distribution_type: "ExternalReview"
        final:
          sku_id: "d2b18039-7d52-4402-9af2-eefa0fc0ef9c"
          sku_name: "Server - WW (task13 server files final)"
          distribution_type: "ExternalReview"


win64:
  content:
    files:
      file_names:
        - "builtLevels.json"
        - "*build*.json"
        - "*.BuildSettings"
        - "*.buildlayout"
        - "*.Main_Win64_*.exe"
        - "*.dll"
        - "Elipy_MD5_hashes.csv"
      supplemental_files:
        - "*.xml"
        - "*BattlefieldGame*.pdb"
      directory:
        - "Config"
        - "Data"
        - "Scripts"
        - "Quicksilver"
      bf-playtest-gnt:
        file_names:
          - "*BattlefieldGame*.pdb"
      CH1-playtest-gnt:
        file_names:
          - "*BattlefieldGame*.pdb"
      CH1-playtest-gnt-na:
        file_names:
          - "*BattlefieldGame*.pdb"
      task13:
        file_names:
          - "*.bat"
    digital:
      retail:
        file_names:
          - "BattlefieldGameData.zip"
        supplemental_files:
          - "installerdata.xml"
          - "*.Main_*_retail*"
        directory:
          - ""
        bf-playtest-gnt:
          file_names:
            - "*BattlefieldGame*.pdb"
      final:
        file_names:
          - "BattlefieldGameData.zip"
        supplemental_files:
          - "*.Main_*_retail*"
        directory:
          - ""
      performance:
        file_names:
          - "BattlefieldGameData.zip"
        supplemental_files:
          - "installerdata.xml"
        directory:
          - ""
    digital_combine:
      retail:
        file_names:
          - "BattlefieldGameData.zip"
          - "SHARED CONTENT.zip"
          - "*_DLC.zip"
          - "installerdata.xml"
          - "patching_ranges.krl"
        supplemental_files:
          - "*.Main_*_retail*"
          - "*_CL_*.txt"
        directory:
          - ""
        bf-playtest-gnt:
          file_names:
            - "*BattlefieldGame*.pdb"
      final:
        file_names:
          - "BattlefieldGameData.zip"
          - "SHARED CONTENT.zip"
          - "*_DLC.zip"
          - "installerdata.xml"
          - "patching_ranges.krl"
        supplemental_files:
          - "*.Main_*_retail*"
        directory:
          - ""
      performance:
        file_names:
          - "BattlefieldGameData.zip"
        supplemental_files:
          - "installerdata.xml"
        directory:
          - ""
    patch_combine:
      retail:
        file_names:
          - "BattlefieldGameData.zip"
        upload_loop_filenames:
          - "BattlefieldGameData.zip"
          - "*- content 1.zip"
          - "*- content 2.zip"
          - "*- content 3.zip"
        supplemental_files:
          - "installerdata.xml"
          - "*.Main_*_retail*"
        directory:
          - ""
        bf-playtest-gnt:
          file_names:
            - "*BattlefieldGame*.pdb"
      final:
        file_names:
          - "BattlefieldGameData.zip"
          - "*- content ?.zip"
          - "installerdata.xml"
          - "patching_ranges.krl"
        supplemental_files:
          - "*.Main_*_retail*"
        directory:
          - ""
      performance:
        file_names:
          - "BattlefieldGameData.zip"
        supplemental_files:
          - "installerdata.xml"
        directory:
          - ""
    patch:
      file_names:
        - "BattlefieldGameData.zip"
      supplemental_files:
        - "installerdata.xml"
        - "*.Main_*_retail*"
      directory:
        - ""
    iso:
      retail:
        file_names:
          - "*.iso"
        supplemental_files:
          - "*.Main_*_retail*"
        directory:
          - ""
  settings:
    dev-na-to-trunk:
      final:
        files:
          sku_id: "a4bcfb81-2cc9-4261-ad48-e78146fe546e"
          sku_name: "FG - WW (dev-na-to-trunk win64 files final)"
    dev-na-to-trunk-sub:
      final:
        files:
          sku_id: "5a16ced1-4804-48f1-a231-653242b0baf5"
          sku_name: "FG - WW (dev-na-to-trunk-sub win64 files final)"
    trunk-code-dev:
      final:
        files:
          sku_id: "af70d8d0-fd9b-496f-b763-a275a64d9d60"
          sku_name: "FG - WW (trunk-code-dev win64 files final)"
        digital:
          sku_id: "60c90e30-8d30-4514-8cc0-ab9c5c380c7f"
          sku_name: "FG - WW (trunk-code-dev win64 digital final)"
      performance:
        files:
          sku_id: "386ed480-8325-4c6c-84e8-626769e50634"
          sku_name: "FG - WW (trunk-code-dev win64 files performance)"
    CH1-code-dev:
      final:
        files:
          sku_id: "5ad726ea-af0f-46b1-abd8-e9a9bbd33155"
          sku_name: "FG - WW (CH1-code-dev win64 files final)"
        digital:
          sku_id: "c7740347-9294-4ce2-b89a-6efaeadde1a4"
          sku_name: "FG - WW (CH1-code-dev win64 digital final)"
      performance:
        files:
          sku_id: "119d290e-3ecc-4798-bb65-c113d601412e"
          sku_name: "FG - WW (CH1-code-dev win64 files performance)"
    trunk-content-dev:
      final:
        digital:
          sku_id: "aa8991b5-e268-4289-9a8b-3c427e9b2fc4"
          sku_name: "FG - WW (trunk-content-dev win64 digital final)"
        files:
          sku_id: "0285818d-6a7f-4a29-93b3-8d2078a91cc8"
          sku_name: "FG - WW (trunk-content-dev win64 files final)"
      retail:
        digital:
          sku_id: "1c8641c4-fcc6-4d41-a5c6-8aff3fe8072a"
          sku_name: "FG - WW (trunk-content-dev win64 digital retail)"
    CH1-content-dev:
      final:
        digital_combine:
          sku_id: "e31be304-bab7-4ebc-b961-92c5ba309516"
          sku_name: "FG - WW (CH1-content-dev win64 combine final)"
        files:
          sku_id: "5b361a76-f69b-4c19-b6cc-a46d1db23e06"
          sku_name: "FG - WW (CH1-content-dev win64 files final)"
      performance:
        files:
          sku_id: "7fa44f87-7c0f-406b-ab5c-a2fa09a33a84"
          sku_name: "FG - WW (CH1-content-dev win64 files performance)"
      release:
        files:
          sku_id: "7216d726-3780-45c9-a6f1-fc08c6976b7f"
          sku_name: "FG - WW (CH1-content-dev win64 files release)"
      retail:
        digital:
          sku_id: "1366ac6b-ad3d-4a48-a8a9-579ffbf4c4ca"
          sku_name: "FG - WW (CH1-content-dev win64 retail digital)"
        digital_combine:
          sku_id: "c6107196-b307-4f48-aee2-b36837b07716"
          sku_name: "FG - WW (CH1-content-dev win64 retail combine)"
    bf-playtest-gnt:
      playtest:
        final:
          files:
            sku_id: "e5c96dcf-9c1a-41a3-a395-580d8734f5e4"
            sku_name: "FG - PLAYTEST (bf-playtest-gnt win64 files final)"
            incremental_delivery: "true"
        performance:
          files:
            sku_id: "4ad361ba-dc8c-4b51-9b75-c0b55fc890a6"
            sku_name: "FG - PLAYTEST (bf-playtest-gnt win64 files performance)"
            incremental_delivery: "true"
        retail:
          digital:
            sku_id: "fa0bb9fa-951c-4d77-9925-5f3a8a1798a2"
            sku_name: "FG - PLAYTEST (bf-playtest-gnt win64 digital retail)"
    bf-playtest-maps:
      playtest:
        final:
          files:
            sku_id: "c673048d-d6ce-4e8a-9c63-dc4b41c09651"
            sku_name: "FG - PLAYTEST (bf-playtest-maps win64 files final)"
    bf-playtest-san:
      playtest:
        final:
          files:
            sku_id: "74599c37-1b81-4ff5-b373-f6078f17a74a"
            sku_name: "FG - PLAYTEST (bf-playtest-san win64 files final)"
            incremental_delivery: "true"
        performance:
          files:
            sku_id: "a0b57d0d-8fc5-4308-8735-fad31c34a1ae"
            sku_name: "FG - PLAYTEST (bf-playtest-san win64 files performance)"
            incremental_delivery: "true"
        retail:
          digital:
            sku_id: "ee14d3e2-24b9-4415-9641-68893e629e8a"
            sku_name: "FG - PLAYTEST (bf-playtest-san win64 digital retail)"
    task1:
      performance:
        files:
          sku_id: "9e6db0e8-e2ca-45a0-a2f1-6af759c37835"
          sku_name: "FG - WW (task1 win64 performance files)"
        digital:
          sku_id: "f40d7213-0497-4955-9adc-c13727a91566"
          sku_name: "FG - WW (task1 win64 performance digital)"
      final:
        files:
          sku_id: "d0732f06-7a62-434a-8881-568497afab73"
          sku_name: "FG - WW (task1 win64 final files)"
        digital:
          sku_id: "761abbd2-a2e2-423e-8860-e20977bdf8eb"
          sku_name: "FG - WW (task1 win64 final digital)"
    task8:
      performance:
        files:
          sku_id: "fca6838a-7401-4ed2-bc0d-4a8b4784d9e4"
          sku_name: "FG - WW (task8 win64 performance files)"
    CH1-stage:
      performance:
        files:
          sku_id: "8624891e-488d-42ef-9b15-47f6e889a8a0"
          sku_name: "FG - WW (CH1-stage win64 performance files)"
      final:
        files:
          sku_id: "a3f300e5-75ee-459f-8386-5ca418b5dce0"
          sku_name: "FG - WW (CH1-stage win64 final files)"
        digital_combine:
          sku_id: "cf07bd9b-744f-479f-82fe-86c5aea5753d"
          sku_name: "FG - WW (CH1-stage win64 final combine)"
      retail:
        digital_combine:
          sku_id: "3a9cac5c-6830-4eb6-accd-b7f595593c4e"
          sku_name: "FG - WW (CH1-stage win64 retail combine)"
    CH1-release:
      files:
        final:
          sku_id: "a1ede4f8-e885-4b35-8d0a-5ffaf476e9c6"
          sku_name: "FG - WW (CH1-release win64 files final)"
        performance:
          sku_id: "7bd90d77-ee66-45aa-91be-4c97fadb139b"
          sku_name: "FG - WW (CH1-release win64 files performance)"
      digital_combine:
        final:
          sku_id: "ba9b4fa0-7dd4-4839-8b94-c0e23160ec46"
          sku_name: "FG - WW (CH1-release win64 combine final)"
        retail:
          sku_id: "648e8462-f9c9-41bb-bf15-8abaf6110e42"
          sku_name: "FG - WW (CH1-release win64 combine retail)"
    CH1-qol:
      performance:
        files:
          sku_id: "1eb92741-a3db-4305-9409-2897e2005cc1"
          sku_name: "FG - WW (CH1-qol win64 performance files)"
        digital:
          sku_id: "0a56ff91-d312-4ef3-89a4-556534c4a0fb"
          sku_name: "FG - WW (CH1-qol win64 performance digital)"
      final:
        files:
          sku_id: "94729ddd-c7a8-46d1-8b8b-b443a28dde10"
          sku_name: "FG - WW (CH1-qol win64 final files)"
        digital:
          sku_id: "37716268-a0b7-4142-973c-94c40015b5d9"
          sku_name: "FG - WW (CH1-qol win64 final digital)"
        digital_combine:
          sku_id: "3eb429d1-4f11-4951-86a6-81dd6a76d863"
          sku_name: "FG - WW (CH1-qol win64 final combine)"
      retail:
        digital:
          sku_id: "d89009ef-7d89-481c-b30f-bd81cf8b7a22"
          sku_name: "FG - WW (CH1-qol win64 retail digital)"
        digital_combine:
          sku_id: "45d3cf2e-649b-42ef-ade1-8c1324439435"
          sku_name: "FG - WW (CH1-qol win64 retail combine)"
    anticheat:
      digital:
        final:
          sku_id: "b09b06b5-102c-454a-8322-e53840a5974f"
          sku_name: "FG - WW (bf-anticheat win64 final digital)"
        retail:
          sku_id: "531c6edc-3e4d-4f9e-907a-c803f0ed9907"
          sku_name: "FG - WW (bf-anticheat win64 retail digital)"
    bf-playtest-sp:
      playtest:
        final:
          files:
            sku_id: "42d5d3f7-f0a3-4a2d-b820-b2bd44a89bb6"
            sku_name: "FG - WW (bf-playtest-sp win64 files final)"
            incremental_delivery: "true"
    bflabs:
      files:
        final:
          sku_id: "5760531d-54c3-4a2b-a29d-2b8c98fa39cf"
          sku_name: "FG - WW (bflabs win64 files final)"
      digital:
        final:
          sku_id: "69e9eeaf-8175-4bf2-ac4c-1a50890fc58d"
          sku_name: "FG - WW (bflabs win64 digital final)"
        retail:
          sku_id: "1ac9c97c-bcd9-4038-81aa-8337864cf543"
          sku_name: "FG - WW (bflabs win64 digital retail)"
      patch:
        final:
          sku_id: "c382b4bd-5bc7-4d30-a588-6942d6092714"
          sku_name: "Patch - WW (bflabs win64 patch final)"
        retail:
          sku_id: "2a0ea514-9886-47cf-a33c-3c88676e13eb"
          sku_name: "Patch - WW (bflabs win64 patch retail)"
    CH1-bflabs-stage:
      upload_loop:
        - "true"
      patch:
        final:
          sku_id: "9bc2f994-64ab-4123-bdd2-905fae62e179"
          sku_name: "Patch - WW (CH1-bflabs-stage win64 patch final)"
        retail:
          sku_id: "b1d3afbc-44de-47b0-a62a-5552005aecd7"
          sku_name: "Patch - WW (CH1-bflabs-stage win64 patch retail)"
      final:
        files:
          sku_id: "1d2237d5-687d-41ab-a533-13fd64e6be87"
          sku_name: "FG - WW (CH1-bflabs-stage win64 files final)"
      patch_combine:
        final:
          sku_id: "bfba45a9-c70d-467f-b9b6-77d196fd649d"
          sku_name: "Patch - WW (CH1-bflabs-stage win64 combine final)"
        retail:
          sku_id: "b7a10935-a5f5-458a-ba16-5bd2f836e869"
          sku_name: "Patch - WW (CH1-bflabs-stage win64 combine retail)"
    CH1-bflabs-release:
      upload_loop:
        - "true"
      patch:
        final:
          sku_id: "c448630d-c424-46a4-96b7-15c57fc5c3fe"
          sku_name: "Patch - WW (CH1-bflabs-release win64 patch final)"
        retail:
          sku_id: "a58b1d1e-b99a-49bc-88ea-30a0f79b09fc"
          sku_name: "Patch - WW (CH1-bflabs-release win64 patch retail)"
      final:
        files:
          sku_id: "b1b69bec-de29-491f-b5b0-edc51e24180a"
          sku_name: "FG - WW (CH1-bflabs-release win64 files final)"
      patch_combine:
        final:
          sku_id: "0aabc764-fc11-4310-9509-0f752c534068"
          sku_name: "Patch - WW (CH1-bflabs-release win64 combine final)"
        retail:
          sku_id: "7839345c-bf5f-4623-bb7b-f3be97ed6429"
          sku_name: "Patch - WW (CH1-bflabs-release win64 combine retail))"
    CH1-bflabs-qol:
      upload_loop:
        - "true"
      patch:
        final:
          sku_id: "940cab62-f15e-44d1-b2c3-8607271e6584"
          sku_name: "Patch - WW (CH1-bflabs-qol win64 patch final)"
        retail:
          sku_id: "4a775b9f-8694-4ac8-9c5a-5f5acc736f47"
          sku_name: "Patch - WW (CH1-bflabs-qol win64 patch retail)"
      final:
        files:
          sku_id: "26e76a1a-9832-4af4-82e3-9def78490af3"
          sku_name: "FG - WW (CH1-bflabs-qol win64 files final)"
      patch_combine:
        final:
          sku_id: "5be92640-5b7e-4f7e-8001-af0f7184767a"
          sku_name: "Patch - WW (CH1-bflabs-qol win64 combine final)"
        retail:
          sku_id: "878a7245-2aae-489d-9381-c205a09841b5"
          sku_name: "Patch - WW (CH1-bflabs-qol win64 combine retail)"
    trunk-playtest:
      final:
        files:
          sku_id: "7ff4ddfc-033b-4b4c-877c-6f450c4c4314"
          sku_name: "FG - WW (trunk-playtest win64 files final)"
        digital:
          sku_id: "5d5a8643-b934-44a8-9865-1c74adeb8c36"
          sku_name: "FG - WW (trunk-playtest win64 digital final)"
      performance:
        files:
          sku_id: "3eb6de25-6a4b-4b4b-8c08-d4167273c791"
          sku_name: "FG - WW (trunk-playtest win64 files performance)"
    CH1-playtest:
      files:
        performance:
          sku_id: "aa07a3be-e963-4d08-90ad-5bb8792929e1"
          sku_name: "FG - WW (CH1-playtest win64 files performance)"
    CH1-playtest-gnt:
      files:
        performance:
          sku_id: "2d0c04b5-f94c-4aa4-889e-934fbe3eb455"
          sku_name: "FG - WW (CH1-playtest-gnt win64 files performance)"
    CH1-playtest-gnt-na:
      files:
        performance:
          sku_id: "14de7528-4223-46b4-b06a-03b4c1a75135"
          sku_name: "FG - WW (CH1-playtest-gnt-na win64 files)"
    CH1-release-playtest-gnt-na:
      files:
        performance:
          sku_id: "c17e21b9-ac82-4af7-b098-fe6459281f94"
          sku_name: "FG - WW (CH1-release-playtest-gnt-na win64 files)"
    CH1-playtest-maps:
      files:
        final:
          sku_id: "b51bd197-45c9-4fa7-81fa-436a02b45aa4"
          sku_name: "FG - WW (CH1-playtest-maps win64 files final)"
    CH1-playtest-san:
      files:
        final:
          source:
            sku_id: "b2e6aa0f-30ae-49fb-a8fa-e1c07d48e28a"
            sku_name: "FG - WW (CH1-playtest-san win64 files final)"
          C1S2B1:
            sku_id: "2129fe86-632b-49ff-b5b5-e5b836d97e2c"
            sku_name: "FG - WW (CH1-playtest-san win64 files final C1S2B1)"
    CH1-playtest-san-s2:
      files:
        final:
          sku_id: "848904af-310c-4f22-a36d-b8619a97fe8c"
          sku_name: "FG - WW (CH1-playtest-san-s2 win64 files final)"
    CH1-playtest-sp:
      files:
        final:
          sku_id: "47668c22-2eba-4285-8ae5-26ce5ddc08ce"
          sku_name: "FG - WW (CH1-playtest-sp win64 files final)"
    CH1-stage-playtest-sp:
      files:
        final:
          sku_id: "9dc085fb-90eb-4f51-9be5-eeff686d286e"
          sku_name: "FG - WW (CH1-stage-playtest-sp win64 files final)"
    CH1-playtest-stage:
      files:
        performance:
          sku_id: "7624c56c-b9e3-4578-aa53-3ef58fa6d3ff"
          sku_name: "FG - WW (CH1-playtest-stage win64 files performan)"
    CH1-to-trunk:
      files:
        final:
          sku_id: "d1ace9c4-9d08-4c2c-a870-30df18874010"
          sku_name: "FG - WW (CH1-to-trunk win64 files final)"
    2024_1_dev-bf-to-CH1:
      files:
        final:
          sku_id: "f950b5ac-22ba-43ac-8679-24169ae3844c"
          sku_name: "FG - WW (2024_1_dev-bf-to-CH1 win64 files final)"
    CH1-event-release:
      files:
        final:
          sku_id: "8a26ac62-36fb-4395-9141-29e53d8e2d2f"
          sku_name: "FG - WW (CH1-event-release win64 files final)"
      digital:
        final:
          sku_id: "5c8d99aa-e281-44c1-820b-bd4b0c241650"
          sku_name: "FG - WW (CH1-event-release win64 digital final)"
        retail:
          sku_id: "bdbdfc1f-00d4-49af-8b9d-5f41fba71b5d"
          sku_name: "FG - WW (CH1-event-release win64 digital retail)"
    CH1-event:
      files:
        final:
          sku_id: "ddfbc34b-4972-4548-93f0-5d53a5dba49d"
          sku_name: "FG - WW (CH1-event win64 files final)"
      patch:
        final:
          sku_id: "41cff41d-24be-4892-9c60-30605c0e40b9"
          sku_name: "Patch - WW (CH1-event win64 patch final)"
        retail:
          sku_id: "11ef4960-8a56-42d5-b76d-dff79f945bf0"
          sku_name: "Patch - WW (CH1-event win64 patch retail)"
    task13:
      files:
        release:
          sku_id: "47b30fb6-8772-4272-90ef-7d41fffa81be"
          sku_name: "FG - WW (task13 win64 files release)"
          distribution_type: "ExternalReview"
        performance:
          sku_id: "55466efd-09ab-4178-9495-1488bdebf375"
          sku_name: "FG - WW (task13 win64 files performance)"
          distribution_type: "ExternalReview"
        final:
          sku_id: "9e2389d3-bc9b-4131-baf7-4ed574062b16"
          sku_name: "FG - WW (task13 win64 files final)"
          distribution_type: "ExternalReview"



xbsx:
  content:
    files:
      file_names:
        - "builtLevels.json"
        - "*.buildlayout"
        - "*.BuildSettings"
        - "Logo.png"
        - "*.dll"
        - "SmallLogo.png"
        - "SplashScreen.png"
        - "StoreLogo.png"
        - "WideLogo.png"
        - "*.Main_Xbsx_*.exe"
        - "MicrosoftGame.config"
        - "gameos.xvd"
        - "nsal.json"
        - "package.mft"
        - "resources.pri"
        - "*build*.json"
        - "Elipy_MD5_hashes.csv"
      supplemental_files:
        - "*.xml"
        - "*BattlefieldGame*.pdb"
      directory:
        - "Config"
        - "Data"
        - "Scripts"
      bf-playtest-gnt:
        file_names:
          - "*BattlefieldGame*.pdb"
      CH1-playtest-gnt:
        file_names:
          - "*BattlefieldGame*.pdb"
      CH1-playtest-gnt-na:
        file_names:
          - "*BattlefieldGame*.pdb"
    digital:
      file_names:
        - "*appxmanifest.xml"
        - "MicrosoftGame.config"
        - "*neutral__*.xvc"
      supplemental_files: #Removed PDBs since it was too big and failing the Shift Submission. Recommended by Kalle
        - "*.ekb"
        - "*neutral__*[!c]" # This is kind of a hack. glob.glob doesn't support do not match string; only set of characters.
      directory:
        - ""
      bf-playtest-gnt:
        file_names:
          - "*BattlefieldGame*.pdb"
    digital_combine:
      file_names:
        - "*appxmanifest.xml"
        - "MicrosoftGame.config"
        - "*neutral__*.xvc"
      supplemental_files: #Removed PDBs since it was too big and failing the Shift Submission. Recommended by Kalle
        - "*.ekb"
        - "*neutral__*[!c]" # This is kind of a hack. glob.glob doesn't support do not match string; only set of characters.
        - "*_CL_*.txt"
        - "DiscLayout.xml" #needed for disc submission
    patch_combine:
      file_names:
        - "*appxmanifest.xml"
        - "MicrosoftGame.config"
        - "*neutral__*.xvc"
      supplemental_files: #Removed PDBs since it was too big and failing the Shift Submission. Recommended by Kalle
        - "*.ekb"
        - "*neutral__*[!c]" # This is kind of a hack. glob.glob doesn't support do not match string; only set of characters.
        - "*_CL_*.txt"
      directory:
        - ""
      bf-playtest-gnt:
        file_names:
          - "*BattlefieldGame*.pdb"
    patch:
      file_names:
        - "*appxmanifest.xml"
        - "MicrosoftGame.config"
        - "*neutral__*.xvc"
        - "Validator_*.xml"
      supplemental_files: #Removed PDBs since it was too big and failing the Shift Submission. Recommended by Kalle
        - "*.ekb"
        - "*neutral__*[!c]" # This is kind of a hack. glob.glob doesn't support do not match string; only set of characters.
      directory:
        - ""
  settings:
    dev-na-to-trunk:
      final:
        files:
          sku_id: "81b707b4-cf10-4cd5-8174-c9390d6ed371"
          sku_name: "FG - WW (dev-na-to-trunk xbsx files final)"
    dev-na-to-trunk-sub:
      final:
        files:
          sku_id: "227df871-23f3-4a86-a11e-2e10ffdeb367"
          sku_name: "FG - WW (dev-na-to-trunk-sub xbsx files final)"
    trunk-content-dev:
      #final:
      #  digital:
      #    sku_id: "cad5e509-d7d4-4699-a41d-20124fed9e5d"
      #    sku_name: "FG - WW (trunk-content-dev-er xbsx digital final)"
      #  files:
      #    sku_id: "7d7e306c-68a4-4455-a238-becc5be08ed5"
      #    sku_name: "FG - WW (trunk-content-dev-er xbsx files final)"
      retail:
        digital:
          sku_id: "f4b3417c-e01b-4524-8b0d-aa4250f9dfa1"
          sku_name: "FG - WW (trunk-content-dev-er xbsx digital retail)"
    CH1-content-dev:
      final:
        digital_combine:
          sku_id: "615bc29b-c3b8-4613-bbae-f72ee8015f17"
          sku_name: "FG - WW (CH1-content-dev xbsx combine final)"
        files:
          sku_id: "6b609f15-0013-407e-a278-389ec7bb9f74"
          sku_name: "FG - WW (CH1-content-dev xbsx files final)"
          distribution_type: "ExternalReview"
      performance:
        files:
          sku_id: "01747f98-aedc-4ea5-8edd-6bd306b36824"
          sku_name: "FG - WW (CH1-content-dev xbsx files performance)"
      retail:
        digital_combine:
          sku_id: "d32e03b7-322b-4409-8294-6a4972eee330"
          sku_name: "FG - WW (CH1-content-dev xbsx retail combine)"
    trunk-code-dev:
      final:
        files:
          sku_id: "fd3c22a9-d613-4c08-8585-1cf74c198b87"
          sku_name: "FG - WW (trunk-code-dev xbsx files final)"
        digital:
          sku_id: "f1441f10-8df8-49bc-8f85-74e306cbc2b0"
          sku_name: "FG - WW (trunk-code-dev xbsx digital final)"
      performance:
        files:
          sku_id: "54b74573-74a5-4669-a96c-9f9299ece287"
          sku_name: "FG - WW (trunk-code-dev xbsx files performance)"
    CH1-code-dev:
      final:
        files:
          sku_id: "9c8b098b-faa8-4dc8-817b-bd73e1306f5a"
          sku_name: "FG - WW (CH1-code-dev xbsx files final)"
        digital:
          sku_id: "1d0ec01f-dfef-4e7f-a0a6-817a4175932a"
          sku_name: "FG - WW (CH1-code-dev xbsx digital final)"
      performance:
        files:
          sku_id: "114eb996-7b61-456d-9cbe-3bed1bdb177c"
          sku_name: "FG - WW (CH1-code-dev xbsx files performance)"
    bf-playtest-gnt:
      playtest:
        final:
          files:
            sku_id: "6112b033-7d29-481e-ab3f-b8a619023fda"
            sku_name: "FG - PLAYTEST (bf-playtest-gnt xbsx files final)"
            incremental_delivery: "true"
        retail:
          digital:
            sku_id: "9b37c94f-745b-4d9c-b7bf-0db8345416c0"
            sku_name: "FG - PLAYTEST (bf-playtest-gnt xbsx digital retail)"
        performance:
          digital:
            sku_id: "4497842d-1f2b-4c75-8d07-38188f4f100d"
            sku_name: "FG - WW (bf-playtest-gnt xbsx digital performance)"
    bf-playtest-san:
      playtest:
        performance:
          files:
            sku_id: "e0c10950-0900-4761-b3ee-df07834f6672"
            sku_name: "FG - WW (bf-playtest-san xbsx files)"
            incremental_delivery: "true"
    bf-trunk-retail:
      ww:
        retail:
          digital:
            sku_id: "988003aa-8465-445d-bfa4-d86c6268f26a"
            sku_name: "FG - WW (bf-trunk xbsx digital retail)"
    task1:
      performance:
        files:
          sku_id: "94cbf26d-e41e-4c32-9845-349755d8d48b"
          sku_name: "FG - WW (task1 xbsx performance files)"
      final:
        files:
          sku_id: "1b7ac36f-2a8c-49d4-8a8e-e6dcb91dbccb"
          sku_name: "FG - WW (task1 xbsx final files)"
        digital:
          sku_id: "4bd045b5-b3d6-4e94-91e5-396987a08564"
          sku_name: "FG - WW (task1 xbsx final digital)"
    task8:
      performance:
        files:
          sku_id: "8ff070ae-3eda-42dd-90a4-7f54878d1b5b"
          sku_name: "FG - WW (task8 xbsx performance files)"
    CH1-stage:
      performance:
        files:
          sku_id: "60fced4f-d881-4149-8221-3788781ff5ca"
          sku_name: "FG - WW (CH1-stage xbsx performance files)"
      final:
        files:
          sku_id: "c4873883-7e4b-46b0-b791-0404106493ae"
          sku_name: "FG - WW (CH1-stage xbsx final files)"
          distribution_type: "ExternalReview"
        digital_combine:
          sku_id: "d0a84305-9d67-47c0-a582-1394aaaad0f5"
          sku_name: "FG - WW (CH1-stage xbsx final combine)"
      retail:
        digital_combine:
          sku_id: "30e3b849-e4f1-42aa-8dcf-c45a20331cf8"
          sku_name: "FG - WW (CH1-stage xbsx retail combine)"
    CH1-release:
      files:
        final:
          sku_id: "13e158a1-acc2-42e9-941b-7aeb7458c6b5"
          sku_name: "FG - WW (CH1-release xbsx files final)"
          distribution_type: "ExternalReview"
        performance:
          sku_id: "8f9fbf41-5aea-4d03-b8fb-b1a654f2266f"
          sku_name: "FG - WW (CH1-release xbsx files performance)"
      digital_combine:
        final:
          sku_id: "321368d3-1bc4-482d-b1fd-9b9c9a57ed75"
          sku_name: "FG - WW (CH1-release xbsx combine final)"
        retail:
          sku_id: "ee68e0f4-2c4d-4e8c-a933-cba77da87b7e"
          sku_name: "FG - WW (CH1-release xbsx combine retail)"
    CH1-qol:
      performance:
        files:
          sku_id: "35d6e40f-9ee0-4b78-8453-096393dbec42"
          sku_name: "FG - WW (CH1-qol xbsx performance files)"
      final:
        files:
          sku_id: "7e0a7df2-5c2e-409d-ae00-fc46cc872c21"
          sku_name: "FG - WW (CH1-qol xbsx final files)"
        digital:
          sku_id: "2b35dbf5-2ce1-42ee-8ca8-f736fadfb576"
          sku_name: "FG - WW (CH1-qol xbsx final digital)"
        digital_combine:
          sku_id: "c9bfe9f6-c401-4535-acb9-dd902a4d7f6d"
          sku_name: "FG - WW (CH1-qol xbsx final combine)"
      retail:
        digital:
          sku_id: "f06c0a98-787b-4582-95c6-14884fd740aa"
          sku_name: "FG - WW (CH1-qol xbsx retail digital)"
        digital_combine:
          sku_id: "03076dde-c2ba-4306-b0a7-cd2e326cae2c"
          sku_name: "FG - WW (CH1-qol xbsx retail combine)"
    bf-playtest-sp:
      performance:
        files:
          sku_id: "84eb4a2c-1c8d-43cd-834c-22542886c38c"
          sku_name: "FG - WW (bf-playtest-sp xbsx files performance)"
          incremental_delivery: "true"
    bflabs:
      files:
        final:
          sku_id: "2d6c8d69-f073-441a-aef5-5fe7858a9d5d"
          sku_name: "FG - WW (bflabs xbsx files final)"
      digital:
        final:
          sku_id: "e54a0f87-c2e4-4e49-9fab-a83891d94bbc"
          sku_name: "FG - WW (bflabs xbsx digital final)"
        retail:
          sku_id: "4b4739c0-50a4-4018-bea2-75c45189ad0b"
          sku_name: "FG - WW (bflabs xbsx digital retail)"
      patch:
        final:
          sku_id: "9c28ceaa-f568-401e-9241-f86aa42f9dbc"
          sku_name: "Patch - WW (bflabs xbsx patch final)"
        retail:
          sku_id: "1fe277fe-ad00-4f06-a155-2e6be32fc27a"
          sku_name: "Patch - WW (bflabs xbsx patch retail)"
    CH1-bflabs-stage:
      patch:
        final:
          sku_id: "219f0c3a-f1ee-4181-8453-2b6d6901459e"
          sku_name: "Patch - WW (CH1-bflabs-stage xbsx patch final)"
        retail:
          sku_id: "3307c802-6b83-4fb7-b1a6-807b3a593865"
          sku_name: "Patch - WW (CH1-bflabs-stage xbsx patch retail)"
      final:
        files:
          sku_id: "650b90b8-e68b-4386-ba44-77f5238c7b0d"
          sku_name: "FG - WW (CH1-bflabs-stage xbsx files final)"
      patch_combine:
        final:
          sku_id: "11aa3f2a-c616-44f9-b170-603233c319d4"
          sku_name: "Patch - WW (CH1-bflabs-stage xbsx combine final)"
        retail:
          sku_id: "36b23106-5b4f-4e2d-b6da-635581946e20"
          sku_name: "Patch - WW (CH1-bflabs-stage xbsx combine retail))"
    CH1-bflabs-release:
      patch:
        final:
          sku_id: "4fe13301-0808-4a05-b3fe-a0dbdaad7526"
          sku_name: "Patch - WW (CH1-bflabs-release xbsx patch final)"
        retail:
          sku_id: "49cc8e84-650a-4f3d-bd9c-a70d1f0155f1"
          sku_name: "Patch - WW (CH1-bflabs-release xbsx patch retail)"
      final:
        files:
          sku_id: "8e4a7099-7edd-43d9-80c7-90cd6bfaf268"
          sku_name: "FG - WW (CH1-bflabs-release xbsx files final)"
      patch_combine:
        final:
          sku_id: "0a2e0385-81a3-4a92-bbbe-f44282380fd9"
          sku_name: "Patch - WW (CH1-bflabs-release xbsx combine final)"
        retail:
          sku_id: "ed7d5da0-a057-426b-b130-c112eab9938e"
          sku_name: "Patch - WW (CH1-bflabs-release xbsx combine retail)"
    CH1-bflabs-qol:
      patch:
        final:
          sku_id: "f022462d-7248-4134-b68b-54701bd67b98"
          sku_name: "Patch - WW (CH1-bflabs-qol xbsx patch final)"
        retail:
          sku_id: "5a30e798-152e-4e5d-8cfe-176eaa7573f1"
          sku_name: "Patch - WW (CH1-bflabs-qol xbsx patch retail)"
      final:
        files:
          sku_id: "7a4dd500-049a-456a-8358-22b7b9fc8000"
          sku_name: "FG - WW (CH1-bflabs-qol xbsx files final)"
      patch_combine:
        final:
          sku_id: "5329e90f-dce7-47e5-8d62-ee044702c34d"
          sku_name: "Patch - WW (CH1-bflabs-qol xbsx combine final)"
        retail:
          sku_id: "db3509a4-5c7b-4974-bc6a-668f30a92137"
          sku_name: "Patch - WW (CH1-bflabs-qol xbsx combine retail)"
    trunk-playtest:
      final:
        files:
          sku_id: "6b463995-8342-49c1-ba82-30e178165d2e"
          sku_name: "FG - WW (trunk-playtest xbsx files final)"
        digital:
          sku_id: "50d6f7fc-334a-4090-8a6c-b70b7ed72e44"
          sku_name: "FG - WW (trunk-playtest xbsx final digital)"
      performance:
        files:
          sku_id: "ab3b0a93-aa1c-4779-b356-ceac93ec3a18"
          sku_name: "FG - WW (trunk-playtest xbsx files performance)"
    CH1-playtest:
      files:
        performance:
          sku_id: "60e8347d-9087-4ceb-a825-aa7a7ad0602d"
          sku_name: "FG - WW (CH1-playtest xbsx files performance)"
    CH1-playtest-gnt:
      files:
        performance:
          sku_id: "39973254-b9c1-417e-ae51-c002ef49b847"
          sku_name: "FG - WW (CH1-playtest-gnt xbsx files)"
    CH1-playtest-gnt-na:
      files:
        performance:
          sku_id: "d5daf122-cc94-4d38-bf52-0b8db8e51759"
          sku_name: "FG - WW (CH1-playtest-gnt-na xbsx files)"
    CH1-release-playtest-gnt-na:
      files:
        performance:
          sku_id: "be1412e4-0159-4021-9b75-9018b0783afd"
          sku_name: "FG - WW (CH1-release-playtest-gnt-na xbsx files)"
    CH1-playtest-san:
      files:
        performance:
          sku_id: "f896e16f-3ddd-490c-bd18-254e6d0ee765"
          sku_name: "FG - WW (CH1-playtest-san xbsx files performance)"
    CH1-playtest-san-s2:
      files:
        performance:
          sku_id: "688de354-be46-4ab4-9a0f-7c5929f9800b"
          sku_name: "FG - WW (CH1-playtest-san-s2 xbsx files perf)"
    CH1-playtest-sp:
      files:
        performance:
          sku_id: "f89d69fb-ef7e-4de4-a458-678eec3f1369"
          sku_name: "FG - WW (CH1-playtest-sp xbsx files performance)"
    CH1-stage-playtest-sp:
      files:
        performance:
          sku_id: "1c7794a0-e540-4697-b093-586bf778f3a2"
          sku_name: "FG - WW (CH1-stage-playtest-sp xbsx files perf)"
    CH1-playtest-stage:
      files:
        performance:
          sku_id: "efaa0c83-5886-47e5-9744-f61306f54eb4"
          sku_name: "FG - WW (CH1-playtest-stage xbsx files performanc)"
    CH1-to-trunk:
      files:
        final:
          sku_id: "9d29d59b-e22f-4ae9-8f69-01358351c983"
          sku_name: "FG - WW (CH1-to-trunk xbsx files final)"
    2024_1_dev-bf-to-CH1:
      files:
        final:
          sku_id: "f90159be-6f68-453a-a36e-8a9d658470d7"
          sku_name: "FG - WW (2024_1_dev-bf-to-CH1 xbsx files final)"
    CH1-event:
      files:
        final:
          sku_id: "5c8c579e-c0f2-4c9f-a3d3-0ab0d89d6d27"
          sku_name: "FG - WW (CH1-event xbsx files final)"
        retail:
          sku_id: "64d19140-d9e3-42f6-a7a4-81ed08b3908c"
          sku_name: "FG - WW (CH1-event xbsx files retail)"
      patch:
        final:
          sku_id: "8585c80b-7a0b-4a26-a56d-dc0600689124"
          sku_name: "Patch - WW (CH1-event xbsx patch final)"
        retail:
          sku_id: "c3d8392a-d84b-4c52-9ad5-0eb34cd7dcbf"
          sku_name: "Patch - WW (CH1-event xbsx patch retail)"
    CH1-event-release:
      files:
        final:
          sku_id: "7e4c0750-0b3c-49b6-b9d1-5e311cf49b8d"
          sku_name: "FG - WW (CH1-event-release xbsx files final)"
      digital:
        final:
          sku_id: "d7f5b50d-0707-43b4-9604-22504b430cd9"
          sku_name: "FG - WW (CH1-event-release xbsx digtial final)"
        retail:
          sku_id: "5bd705ff-6179-4423-bd33-f89833a98ea5"
          sku_name: "FG - WW (CH1-event-release xbsx digtial retail)"

linux64:
  content:
    files:
      file_names:
          - "builtLevels.json"
          - "*build*.json"
          - "*.BuildSettings"
          - "*.buildlayout"
          - "*.Main_Linux64_*"
          - "Elipy_MD5_hashes.csv"
      supplemental_files:
          - "*.xml"
      directory:
          - "Config"
          - "Data"
          - "Scripts"
  settings:
    trunk-code-dev:
      final:
        files:
          sku_id: "95f405ea-fe34-4b19-95ab-cbd865591fdc"
          sku_name: "FG - WW (trunk-code-dev linux64 files final)"
    CH1-code-dev:
      final:
        files:
          sku_id: "9df25bdd-a6ed-479b-a6dd-b0138218b7da"
          sku_name: "FG - WW (CH1-code-dev linux64 files final)"
    CH1-content-dev:
      final:
        files:
          sku_id: "606e86bd-9ea0-44d7-8104-b010e23fa825"
          sku_name: "FG - WW (CH1-content-dev linux64 files final)"
    trunk-playtest:
      final:
        files:
          sku_id: "c03ccf24-6a44-4e72-9b5b-e2b13d2c5f34"
          sku_name: "FG - WW (trunk-playtest linux64 files final)"
    bflabs:
      files:
        final:
          sku_id: "acb4a45a-6b6e-459f-8cb3-dc7948c45f01"
          sku_name: "Server - WW (bflabs linux64 files final)"
    CH1-release:
      files:
        final:
          sku_id: "4ebaffd0-b6f1-49a5-87a8-1335f3de40c9"
          sku_name: "FG - WW (CH1-release linux64 files final)"
    CH1-bflabs-release:
      files:
        final:
          sku_id: "4c2e7a7e-8733-4fb0-a8f2-92bd0aa0d18a"
          sku_name: "FG - WW (CH1-bflabs-release linux64 files final)"
    CH1-bflabs-stage:
      files:
        final:
          sku_id: "9aabcab5-5583-4e77-af2a-970a2dc54c55"
          sku_name: "FG - WW (CH1-bflabs-stage linux64 files final)"
    CH1-event:
      files:
        final:
          sku_id: "cd7f0215-8f0a-4165-817a-94e49e376fb1"
          sku_name: "FG - WW (CH1-event linux64 files final)"
    CH1-event-release:
      files:
        final:
          sku_id: "1af11375-8924-4523-bb4a-efa9c49e6ca4"
          sku_name: "FG - WW (CH1-event-release linux64 files final)"

offsite_basic_drone:
  content:
    file_names:
      - '*'
    supplemental_files:
      - ""
  settings:
    trunk-content-dev:
        sku_id: "e8b88433-7c88-424f-bec7-09b4c76c3518"
        distribution_type: "ExternalTechPartners"
        sku_name: "Tool - WW Us (trunk-content-dev-editor-release Off Drone Build)"
    CH1-content-dev:
        sku_id: "8a228067-cb37-48b1-a6a8-8195cc181c34"
        distribution_type: "ExternalTechPartners"
        sku_name: "Tool - WW (CH1-content-dev Offsite Drone Build)"
    trunk-code-dev:
        sku_id: "ceb59471-915c-4fd2-85d1-bfea4d96e4c5"
        distribution_type: "ExternalTechPartners"
        sku_name: "Tool - WW (trunk-code-dev Offsite Drone Build)"
    CH1-code-dev:
        sku_id: "fd29a298-ba3b-4b25-bbb8-a032f3e1e05a"
        distribution_type: "ExternalTechPartners"
        sku_name: "Tool - WW (CH1-code-dev Offsite Drone Build)"
