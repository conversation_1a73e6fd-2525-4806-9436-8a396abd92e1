package com.ea.project.fb1

import com.ea.project.Cobra

class Fb1Battlefieldgame {
    static String name = 'fb1-battlefieldgame'
    static String short_name = 'fb1_bfg'
    static Boolean frostbite_syncer_setup = false
    static Boolean single_perforce_server = true
    static Boolean presync_machines = false
    static String user_credentials = 'monkey.commons'
    static String vault_server_credentials = 'dice-online-cas-prod-secret-id'
    static String vault_server_variable = 'VAULT_ONLINE_CAS_PROD_SECRET_ID'
    static String vault_credentials = 'cobra-online-rob-prod-secret-id'
    static String vault_variable = 'VAULT_ONLINE_EXC_PROD_SECRET_ID'
    static String game_team_secrets_credential = 'game-team-secrets-secret-id'
    static String game_team_secrets_credential_extra = 'dice-online-gla-prod-secret-id'
    static String game_team_secrets_credential_online_prod = ''
    static String game_team_secrets_variable = 'GAME_TEAM_SECRETS_SECRET_ID'
    static List vault_default_platforms = [
        'ps4',
        'win64',
        'xbsx',
    ]

    static String dataset = 'DiceNextData'
    static String frostbite_licensee = 'BattlefieldGame'

    static String workspace_root = 'D:\\dev'
    static String fbcli_call = 'tnt\\bin\\fbcli\\cli.bat x64'
    static String location = 'DiceStockholm'
    static String elipy_scripts_config_file = 'elipy_fb1.yml'
    static String elipy_install_call = "${fbcli_call} && ${Cobra.elipy_install} $elipy_scripts_config_file >> ${workspace_root}\\logs\\install-elipy.log 2>&1"
    static String elipy_setup_call = "${fbcli_call} && ${Cobra.elipy_setup_env} $elipy_scripts_config_file >> ${workspace_root}\\logs\\setup-elipy-env.log 2>&1"
    static String elipy_call = "${elipy_setup_call} && elipy --location $location"
    static String elipy_call_earo = "${elipy_setup_call} && elipy --location earo"
    static String elipy_call_eala = "${elipy_setup_call} && elipy --location RippleEffect"
    static String elipy_call_criterion = "${elipy_setup_call} && elipy --location criterion"

    static String azure_workspace_root = 'E:\\dev'
    static String azure_elipy_install_root = 'C:\\dev'
    static String azure_elipy_setup_call = "$fbcli_call && $azure_elipy_install_root\\ci\\setup-elipy-env.bat $elipy_scripts_config_file >> $azure_workspace_root\\logs\\setup-elipy-env.log 2>&1"
    static String azure_elipy_install_call = "$fbcli_call && $azure_elipy_install_root\\ci\\install-elipy.bat $elipy_scripts_config_file >> $azure_workspace_root\\logs\\install-elipy.log 2>&1"
    static String azure_elipy_call = "$azure_elipy_setup_call && elipy --location $location"
    static String azure_elipy_call_eala = "$azure_elipy_setup_call && elipy --location RippleEffect"

    static String p4_browser_url = 'https://swarm.frostbite.com/'
    static String p4_user_single_slash = '%USERDOMAIN%\\%USERNAME%'
    static Map p4_extra_servers = [:]

    static String p4_code_root = '/'
    static String p4_code_creds = 'perforce-frostbite02-commons'
    static String p4_code_server = 'dice-p4buildedge02-fb.dice.ad.ea.com:2001'
    static String p4_code_client = 'jenkins-${NODE_NAME}-codestream'
    static String p4_code_client_env = 'jenkins-%NODE_NAME%-codestream'

    static String p4_data_root = p4_code_root
    static String p4_data_creds = p4_code_creds
    static String p4_data_server = p4_code_server
    static String p4_data_client = 'jenkins-${NODE_NAME}-' + dataset + 'stream'
    static String p4_data_client_env = 'jenkins-%NODE_NAME%-' + dataset + 'stream'

    static Map p4_code_servers = [
        'frostbite_build_dice': p4_code_server,
        'frostbite_build_eala': 'dicela-p4edge-fb.la.ad.ea.com:2001',
    ]

    static List<Map> p4_data_servers = [
        [name: 'frostbite_build_dice', p4_port: p4_data_server],
        [name: 'frostbite_build_dice_bfdata', p4_port: p4_data_server, dataset: 'bfdata'],
        [name: 'frostbite_build_eala', p4_port: 'dicela-p4edge-fb.la.ad.ea.com:2001'],
        [name: 'frostbite_build_eala_bfdata', p4_port: 'dicela-p4edge-fb.la.ad.ea.com:2001', dataset: 'bfdata'],
    ]

    static Map icepick_settings = [
        ignore_icepick_exit_code: false,
        icepick_test            : 'BFUnitTests',
        icepick_preflight_test  : 'BFUnitTests',
        settings_files          : '',
    ]
    static String webexport_script_path = 'Scripts\\DICE\\webexport.py'
    static String drone_exclude_path = 'TnT/Setup/Drone/...'
    static Boolean fake_ooa_wrapped_symbol = false
    static Boolean commerce_debug_disable = false
    static Boolean use_recompression_cache = true
    static Boolean clean_master_version_check = true
    static Boolean compress_symbols = true
    static Boolean compress_symbols_code_win64server = false
    static Boolean compress_symbols_code_win64game = false
    static Boolean compress_symbols_code_xb1 = false
    static Boolean compress_symbols_code_xbsx = false

    static Boolean is_cloud = false

    static String autotest_matrix = 'DunAutotestMatrix'
    static Boolean deploy_tests = true
    static Boolean file_hashes_frosty = true
    static List<Map> vault_secrets_project = Cobra.af2_vault_credentials
}
