package com.ea.project.gnt.mastersettings

class ResGntPreflight {
    static Class project = com.ea.project.gnt.Granite
    static Map branches = [:]
    static Map preflight_branches = [:]
    static Map autotest_branches = [:]
    static Map integrate_branches = [:]
    static Map copy_branches = [:]
    static Map feature_integrations = [:]
    static Map maintenance_branch = [:]
    static List dashboard_list = []
    static Map dvcs_configs = [:]
    static List code_downstream_matrix = []
    static Map MAINTENANCE_SETTINGS = [:]
}
