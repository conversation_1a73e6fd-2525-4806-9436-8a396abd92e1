"""
prebuild.py
"""
import os
import click
import time

from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.licensee_helper import set_licensee
from dice_elipy_scripts.utils.code_utils import run_gensln
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import (
    code,
    core,
    data,
    local_paths,
    LOGGER,
    p4,
    running_processes,
    frostbite_core,
)
from elipy2.frostbite import fbenv_layer
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command("prebuild", short_help="Create prebuilds.")
@click.option("--clean", default="false", help="Delete TnT/Local if --clean true is passed.")
@click.option("--code-changelist", required=True, help="Perforce code changelist number.")
@click.option("--config", required=True, help="Config to generate solution for.")
@click.option(
    "--data-directory",
    default=None,
    help="Which data directory to use for fetching licensee settings.",
)
@click.option("--dry-run", is_flag=True, help="Run without submitting to Perforce.")
@click.option("--email", default=None, help="User email to authenticate to package server.")
@click.option(
    "--domain-user",
    default=None,
    help="The user to authenticate to package server as DOMAIN\\user",
)
@click.option("--framework-args", multiple=True, help="Framework arguments for gensln.")
@click.option(
    "--input-param-path",
    help="Path to file with input parameters (only used for submission).",
)
@click.option("--licensee", multiple=True, default=None, help="Licensee to use.")
@click.option("--p4-client-code", required=True, help="Perforce workspace name to sync code.")
@click.option("--p4-port", required=True, help="Perforce server address.")
@click.option("--p4-user", required=True, help="Perforce user name.")
@click.option(
    "--password",
    default=None,
    help="User credentials to authenticate to package server.",
)
@click.option(
    "--p4-client-prebuild",
    help="Perforce workspace to submit generated prebuilds (only used for submission).",
)
@click.option(
    "--platform-prebuild",
    multiple=True,
    help="Platform to generate prebuilds for (only used for submission).",
)
@click.option(
    "--platform-sln",
    multiple=True,
    help="Platform(s) to run gensln and buildsln or validation for.",
)
@click.option(
    "--skip-nintendo-switch",
    is_flag=True,
    help="Deprecated. Use skip-platform instead.",
)
@click.option("--skip-platform", multiple=True, help="Platform(s) to skip inluding.")
@click.option("--validation", is_flag=True, help="Validate prebuild: download+gensln+buildsln")
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
# pylint: disable=too-many-locals, invalid-name, too-many-statements, protected-access
def cli(
    _,
    clean,
    code_changelist,
    config,
    data_directory,
    dry_run,
    email,
    domain_user,
    framework_args,
    input_param_path,
    licensee,
    p4_client_code,
    p4_port,
    p4_user,
    password,
    p4_client_prebuild,
    platform_prebuild,
    platform_sln,
    skip_nintendo_switch,
    skip_platform,
    validation,
):
    """
    Create prebuilds or validate prebuild is working
    These will be used when outsourcing code development to third party companies.
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    # Clean up before running the job.
    running_processes.kill()
    core.clean_temp()
    core.close_file_handles(local_paths.get_packages_path())
    core.close_file_handles(local_paths.get_tnt_localpackages_path())
    if clean.lower() == "true":
        tnt_local_path = local_paths.get_tnt_local_path()
        core.delete_folder(tnt_local_path, close_handles=True)

    # Initialize
    if data_directory is not None:
        data.DataUtils.set_datadir(data_directory)

    # Set framework args, handle licensee settings if needed.
    framework_args = list(framework_args)
    framework_args = set_licensee(list(licensee), framework_args)

    # Delete the Prebuild folder on the machine.
    prebuild_folder = os.path.join(frostbite_core.get_tnt_root(), "Prebuild")
    core.delete_folder(prebuild_folder)

    # Set platforms and generate and build solution for.
    platforms_gensln_buildsln = list(platform_sln)

    # Skip including some platforms.
    platforms_to_skip = list(skip_platform)
    # Include deprecated parameter if specified.
    if skip_nintendo_switch:
        platforms_to_skip.append("NX")
    for platform in platforms_to_skip:
        framework_args.append("-G:frostbite.pipeline." + platform + ".supported=false")

    # Generate Prebuild packages if not set --validation
    if not validation:
        # Generate and build solution for the listed platforms.
        # Submit to Perforce and cleanup local.
        _code_build(
            code_changelist,
            config,
            email,
            domain_user,
            framework_args,
            p4_port,
            p4_user,
            password,
            p4_client_code,
            platforms_gensln_buildsln,
            True,
        )

        # Generate Prebuild packages.
        input_param_file = os.path.join(frostbite_core.get_tnt_root(), input_param_path)
        LOGGER.info("Waiting for 10 seconds for file handles to be cleaned up...")
        time.sleep(10)

        fbenv_layer.pkgprebuilds(
            platform=platform_prebuild,
            input_param_file=input_param_file,
            framework_args=framework_args,
        )

        try:
            # Create a Perforce object, to be used for reconcile and submit.
            perforce = p4.P4Utils(port=p4_port, client=p4_client_prebuild, user=p4_user)

            # Reconcile difference between generated and check-in versions of the Prebuild folder
            LOGGER.info("Reconcile difference between generated and check-in versions of Prebuild")
            prebuild_p4_path = prebuild_folder + os.sep + "..."
            perforce.reconcile(path=prebuild_p4_path, options=["a", "d", "e", "f"], quiet=False)

            if not dry_run:
                # Submit to Perforce.
                LOGGER.info("Submitting the created prebuilds to Perforce.")
                submit_message = "Prebuilds generated from CL {}.".format(code_changelist)
                submit_message += "\nJenkins URL: " + os.environ.get("BUILD_URL", "None")
                perforce.submit(message=submit_message)
            else:
                LOGGER.info("Running with --dry-run, not submitting the result to Perforce.")
        finally:
            # Clean up the Perforce environment.
            perforce.revert(quiet=True)
            perforce.clean(prebuild_p4_path)
    # Only validate prebuild package
    else:
        # fetch prebuild package
        # run >fb outsource-package-install TnT/OutsourcePackages/
        # build on platforms
        installer = os.path.join(
            frostbite_core.get_tnt_root(),
            "Code",
            "DICE",
            "BattlefieldGame",
            "fbcli",
            "outsource-package-install.py",
        )
        try:
            core.run(["python", installer, "TnT/OutsourcePackages/"])
        except Exception as exception:
            LOGGER.error("Unable to download and install packages from prebuild")
            raise exception

        _code_build(
            code_changelist,
            config,
            email,
            domain_user,
            framework_args,
            p4_port,
            p4_user,
            password,
            p4_client_prebuild,
            platforms_gensln_buildsln,
            False,
        )


def _code_build(
    code_changelist,
    config,
    email,
    domain_user,
    framework_args,
    p4_port,
    p4_user,
    password,
    p4_client_prebuild,
    platforms_gensln_buildsln,
    increment_client_version,
):
    for platform in platforms_gensln_buildsln:
        platform_config = config
        build_frosted = platform == "tool" and "frosted" in platforms_gensln_buildsln
        if platform in ["tool", "frosted"]:
            platform_config = "release"

        # use the tool gensln/buildsln to compile frosted
        if platform == "frosted":
            continue

        builder = code.CodeUtils(
            platform,
            platform_config,
            monkey_build_label=code_changelist,
            p4_port=p4_port,
            p4_user=p4_user,
            p4_client=p4_client_prebuild,
        )

        try:
            # Increment Client Version
            if increment_client_version:
                builder.increment_client_version()

            # Generate solution
            run_gensln(
                password=password,
                user=email,
                domain_user=domain_user,
                framework_args=framework_args,
                builder=builder,
            )

            # Build solution on project(s)
            builder.buildsln(build_frosted=build_frosted)

        finally:
            # Revert Increment Client Version files when done
            if increment_client_version:
                builder.clean_platform_temp_files()
