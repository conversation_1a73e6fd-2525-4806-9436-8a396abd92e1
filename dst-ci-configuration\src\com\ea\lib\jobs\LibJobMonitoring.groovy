package com.ea.lib.jobs

import com.ea.lib.jobsettings.JobMonitoringSettings
import javaposse.jobdsl.dsl.DslFactory
import javaposse.jobdsl.dsl.jobs.WorkflowJob

class LibJobMonitoring {
    static WorkflowJob startJob(DslFactory dslFactory, def masterFile, def project) {
        JobMonitoringSettings settings = new JobMonitoringSettings()
        settings.initializeStartJob(masterFile, project)
        return dslFactory.pipelineJob('controller.job_monitoring') {
            description(settings.description)
            logRotator(7, 50)
            properties {
                disableConcurrentBuilds()
                disableResume()
                pipelineTriggers {
                    triggers {
                        cron {
                            spec(settings.cronTrigger)
                        }
                    }
                }
            }
            quietPeriod(0)
            environmentVariables {
                env('SLACK_CHANNEL', settings.slackChannel)
                env('PROJECT_SHORT_NAME', settings.projectShortName)
                env('CLOUD_NODE_PREFIX', settings.cloudNodePrefix)
            }
            definition {
                cps {
                    script(dslFactory.readFileFromWorkspace('src/scripts/schedulers/all/JobMonitoringScheduler.groovy'))
                    sandbox(true)
                }
            }
        }
    }
}
