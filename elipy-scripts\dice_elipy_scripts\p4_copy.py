"""
p4_copy.py
"""
import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.integration_utils import submit_integration
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import LOGGER, p4
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command("p4_copy", short_help="Performs a Perforce copy.")
@click.argument("port", required=True)
@click.argument("client", required=True)
@click.argument("mapping", required=True)
@click.argument("changelist", required=True)
@click.option("--reverse/--no-reverse", default=False)
@click.option("--submit/--no-submit", default=True)
@click.option("--stream/--no-stream", default=False)
@click.option("--user", default=None, help="Perforce user name.")
@click.option("--force/--no-force", default=False)
@click.option("--exclude-path", default=[], multiple=True, help="Don't integrate path.")
@click.option("--submit-message", default="", help="Message to include in submit message.")
@click.option("--source-branch", default="", help="Branch from which we are integrating.")
@click.option("--target-branch", default="", help="Branch to which we are integrating.")
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    port,
    client,
    mapping,
    changelist,
    reverse,
    submit,
    stream,
    user,
    force,
    exclude_path,
    submit_message,
    source_branch,
    target_branch,
):
    """
    Performs the Perforce copy of branch mapping.
    """
    # Adding sentry tags.
    add_sentry_tags(__file__)

    LOGGER.info(
        "Performing copy using client %s on server %s with branch mapping %s.",
        client,
        port,
        mapping,
    )
    perforce = p4.P4Utils(port=port, client=client, user=user)
    perforce.revert()
    try:
        perforce.copy_mapping(
            mapping=mapping,
            reverse=reverse,
            stream=stream,
            force=force,
            to_revision=changelist,
        )
        for exclude in exclude_path:
            perforce.revert(path=exclude)

        # Submit the copied result to Perforce.
        if source_branch == "" or target_branch == "":
            message = f"Copied using branch mapping {mapping}"
        else:
            message = f"Copied from {source_branch} to {target_branch}"
        message += f" at CL#{changelist}."
        message += "\n#branchguardian_bypass"
        if submit_message:
            message += f"\n{submit_message}"
        submit_integration(p4_object=perforce, submit_message=message, submit=submit)
    finally:
        perforce.revert(quiet=True)
