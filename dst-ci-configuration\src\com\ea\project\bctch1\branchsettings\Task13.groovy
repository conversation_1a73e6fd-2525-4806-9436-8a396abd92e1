package com.ea.project.bctch1.branchsettings

import com.ea.lib.jobsettings.ShiftSettings
import com.ea.project.bctch1.BctCh1

class Task13 {
    // Settings for jobs
    static Class project = BctCh1
    static Map general_settings = [
        dataset                 : project.dataset,
        elipy_call              : project.elipy_call_task13 + ' --use-fbenv-core',
        elipy_install_call      : project.elipy_install_call,
        frostbite_licensee      : project.frostbite_licensee,
        workspace_root          : project.workspace_root,
        p4_code_creds           : 'bct-la-p4',
        p4_data_creds           : 'bct-la-p4',
        p4_code_server          : 'dicela-p4edge-fb.la.ad.ea.com:2001',
        p4_data_server          : 'dicela-p4edge-fb.la.ad.ea.com:2001',
        job_label_statebuild    : 'statebuild_eala',
    ]
    static Map code_settings = [
        deploy_tests                 : true,
        fake_ooa_wrapped_symbol      : false,
        skip_code_build_if_no_changes: false,
        slack_channel_code           : [
            channels                  : ['#r13-build-notify'],
            skip_for_multiple_failures: true,
        ],
        report_build_version         : ' --reporting-build-version-id %code_changelist%',
        sndbs_enabled                : true,
        statebuild_code              : false,
    ]
    static Map data_settings = [
        poolbuild_data    : false,
        timeout_hours_data: 6,
        slack_channel_data: [
            channels                  : ['#r13-build-notify'],
            skip_for_multiple_failures: true,
        ],
        statebuild_data   : false,
    ]
    static Map frosty_settings = [
        poolbuild_frosty    : false,
        slack_channel_frosty: [
            channels                  : ['#r13-build-notify'],
            skip_for_multiple_failures: true,
        ],
        statebuild_frosty   : true,
        use_linuxclient     : true,
    ]
    static Map standard_jobs_settings = code_settings + data_settings + frosty_settings + [
        asset                     : 'Game/Setup/Build/Task13Levels',
        enable_lkg_p4_counters    : true,
        extra_data_args           : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        extra_frosty_args         : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        server_asset              : 'Game/Setup/Build/Task13Levels',
        skip_icepick_settings_file: true,
        strip_symbols             : false,
        shift_branch              : true,
        shift_every_build         : true,
        shift_reference_job       : 'task13.frosty.start',
    ]
    static Map icepick_settings = [
        ignore_icepick_exit_code: false
    ]
    static Map preflight_settings = [:]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'tool', configs: ['release']],
        [name: 'win64game', configs: ['release', 'performance', 'final']],
        [name: 'win64server', configs: ['final', 'release']],
        [name: 'linux64server', configs: ['final', 'release']],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = [
        [name: '.data.start', args: []],
    ]
    static List data_matrix = [
        [name: 'win64'],
        [name: 'server'],
    ]
    static List data_downstream_matrix = [
        [name: '.frosty.start', args: []],
    ]
    static List patchdata_matrix = []
    static List frosty_matrix = [
        [name: 'win64', variants: [[format: 'files', config: 'performance', region: 'ww', args: ''],
                                   [format: 'files', config: 'release', region: 'ww', args: ''],
                                   [format: 'files', config: 'final', region: 'ww', args: '']]],
        [name: 'server', variants: [[format: 'files', config: 'final', region: 'ww', args: ''],
                                    [format: 'files', config: 'release', region: 'ww', args: '']]],
        [name: 'linuxserver', variants: [[format: 'files', config: 'final', region: 'ww', args: ''],
                                    [format: 'files', config: 'release', region: 'ww', args: '']]],
    ]
    static List frosty_downstream_matrix = [
        [name: '.shift.upload', args: ['code_changelist', 'data_changelist']],
    ]
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = [
        [name: 'linuxserver', variants: [[format: 'files', config: 'final', region: 'ww']]],
    ]
    static List shift_upload_matrix = [
        [shifter_type: ShiftSettings.SHIFTER_TYPE_OFFSITE_BASIC_DRONE, args: ['code_changelist']],
    ]
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
