package scripts.schedulers

import com.ea.lib.LibJenkins
import com.ea.lib.LibSlack
import hudson.model.Node
import jenkins.model.Jenkins

def project = ProjectClass(env.project_name)

/**
 * sdelete.standard.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Trigger sdelete jobs for non-statebuild machines') {
            steps {
                script {
                    def jenkins_nodes = Jenkins.get().nodes
                    def job_name = 'sdelete.standard'
                    def jobs = [:]

                    def final_result = Result.SUCCESS

                    currentBuild.displayName = env.JOB_NAME + '.' + env.BUILD_NUMBER

                    for (Node node in jenkins_nodes) {
                        // Make sure the node is online.
                        if (!node.computer.offline) {
                            // Only run this for nodes without statebuild label.
                            if (node.labelString != 'statebuild' && !node.labelString.contains('utility')) {
                                def node_name = node.nodeName

                                // Define parameters for the job
                                def args = [
                                    string(name: 'Node', value: node_name),
                                ]

                                // Create a job and add it to the job map
                                jobs["node_${node_name}"] = {
                                    def downstream_job = build(job: job_name, parameters: args, propagate: false)
                                    final_result = final_result.combine(Result.fromString(downstream_job.result))
                                    LibJenkins.printRunningJobs(this)
                                }
                            }
                        }
                    }
                    // Trigger all jobs
                    parallel(jobs)
                    currentBuild.result = final_result.toString()
                    LibSlack.forPipelines(this, '#cobra-support-alerts', project.short_name)
                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
}
