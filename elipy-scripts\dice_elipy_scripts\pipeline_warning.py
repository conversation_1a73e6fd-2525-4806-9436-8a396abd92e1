"""
pipeline_warning.py

The script starts in the same way as a normal databuild, with copying pipeline binaries to the
machine and cooking data.
It's possible to have the build stateless by importing an Avalanche state that has previously been
exported by a normal databuild job.
A detailed diagram on how the stateless flow for data work can be found here:
https://drive.google.com/file/d/1hdY4wLEQSoUrUS1MwZGIn_ceAqh99gHT/view?usp=sharing

This is followed by a call to the pipeline warning extraction scripts.

- General setup:
    - Clean up the machine by killing running processes.
    - Initialize objects using packages from Elipy core, to be used later.
        - One instance of data.DataUtils().
        - One instance of filer.FilerUtils().
    - Set data directory.
    - Copy code binaries for pipeline (produced when building the code platform tool)
      from a network share.
    - Set licensee
        - Optional step that we run when a licensee is sent as an argument. We need to set this
          when we don't build the default licensee on a stream. For example when we build dev-na
          where the default licensee is ExampleGame.
    - Handle the Avalanche state before the build:
        - If a clean flag is set we clean Avalanche and do not import any state.
        - If building stateless we import the state by remote cloning it from where it's stored.
          We find this out by checking the primary metadata service. And then we...
    - Cook the data.
        - When cooking we call fbenv which in turn uses the pipeline,
          built by the codebuild job with platform tools. This is copied to the machine
          in an earlier step. You specify which asset/level to cook and can also add import
          of state flags and other pipeline args.
        - Set Avalanche status, so we know which machine has built which
          platform using which changelist. This is used to check if we need
          to import state when building statelessly. Importing a state takes time and
          should only be done when the machine does not have any
          recent data for the specific platform.
    - Pipeline warning specific parts:
        - Run the scripts. These have been synced to the machine as a part of the Jenkins job.
        - Remove the scripts after the job to clean up the machine.
"""

# pylint: disable=duplicate-code
import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.licensee_helper import set_licensee
from dice_elipy_scripts.utils.state_utils import import_avalanche_data_state
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import avalanche, core, data, filer, LOGGER, running_processes, frostbite_core
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command("pipeline_warning", short_help="Run the pipeline warning extraction scripts.")
@click.option("--data-directory", required=True, help="Data directory/dataset to build.")
@click.option("--platform", default="win64", help="Platform to build")
@click.option(
    "--assets",
    default=["preflightlevels"],
    multiple=True,
    help="Asset(s) to be cooked.",
)
@click.option("--code-branch", default=None, help="Perforce branch/stream name for binaries.")
@click.option("--code-changelist", default=None, help="Perforce changelist number for binaries.")
@click.option("--data-branch", help="Branch to fetch Avalanche state from.", default="")
@click.option("--data-changelist", default="", help="Changelist of data being cooked.")
@click.option("--pipeline-args", multiple=True, help="Pipeline arguments for data build.")
@click.option("--import-avalanche-state", is_flag=True, help="Imports Avalanche state from filer.")
@click.option(
    "--data-clean",
    default="false",
    help="Clean Avalanche if --data-clean true is passed.",
)
@click.option("--use-local", is_flag=True, help="Use local binaries (don't copy from filer).")
@click.option("--trim/--no-trim", default=True)
@click.option("--licensee", multiple=True, default=None, help="Licensee to use.")
@click.option(
    "--clean-master-version-check",
    is_flag=True,
    help="Run clean on master version update.",
)
@click.option("--username", required=True, help="Username for the pipeline warning service.")
@click.option("--password", required=True, help="Password for the pipeline warning service.")
@click.option(
    "--target-address",
    required=True,
    help="Target to upload the result from the pipeline warning tool.",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
# pylint: disable=too-many-locals
def cli(
    _,
    data_directory,
    platform,
    assets,
    code_branch,
    code_changelist,
    data_branch,
    data_changelist,
    pipeline_args,
    import_avalanche_state,
    data_clean,
    use_local,
    trim,
    licensee,
    clean_master_version_check,
    username,
    password,
    target_address,
):
    """
    Cook the game data.
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    # Clean up before running the job.
    running_processes.kill()

    # Restart Avalanche service if necessary
    avalanche.restart_avalanche()

    # Initialize
    builder = data.DataUtils(platform, list(assets), monkey_build_label=data_changelist)
    _filer = filer.FilerUtils()

    # Set data directory.
    builder.set_datadir(data_directory)

    # Fetch pipeline binary
    if not use_local:
        set_licensee(list(licensee), list())
        _filer.fetch_code(code_branch, code_changelist, "pipeline", "release")

    # Set pipeline args for the data build
    pipeline_args = list(pipeline_args)

    if data_clean.lower() == "true":
        builder.clean()
    elif import_avalanche_state:
        extra_args = import_avalanche_data_state(
            data_branch, code_branch, platform, _filer, data_changelist
        )
        pipeline_args += extra_args

    try:
        # Build data
        builder.cook(
            pipeline_args=pipeline_args,
            collect_mdmps=True,
            trim=trim,
            clean_master_version_check=clean_master_version_check,
        )

        # Set build status in Avalanche
        avalanche.set_avalanche_build_status(
            code_changelist=code_changelist,
            data_changelist=data_changelist,
            data_branch=data_branch,
            platform=platform,
        )

        LOGGER.info("Data cooked, continuing with the pipeline warning extraction script.")

        # Prepare for running the pipeline warning extraction script
        script_location = os.path.join(
            frostbite_core.get_game_root(),
            "pwarn",
            "scripts",
            "binaries",
            "OplogParser",
            "OplogParser.exe",
        )
        db_name = avalanche.get_full_database_name(platform)
        run_args = [script_location, db_name, target_address, username, password]

        # Run the pipeline warning extraction script
        core.run(run_args)

    finally:
        # Remove the scripts folder from the machine after the job has finished
        core.delete_folder(os.path.join(frostbite_core.get_game_root(), "pwarn"))
