"""
bilbo_select_autotest.py
"""
import os

import click
from elipy2 import LOGGER, frostbite_core, build_metadata_utils
from elipy2.cli import pass_context
from elipy2.exceptions import ELIPYException
from elipy2.telemetry import collect_metrics

from dice_elipy_scripts.utils.autotest_utils import get_builds_with_config
from dice_elipy_scripts.utils.bilbo_utils import (
    get_latest_drone_build,
    verify_tests_have_passed,
)
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags


@click.command(
    "bilbo_select_autotest",
    short_help="Uses the primary metadata service to find build for testing.",
)
@click.option("--code-branch", help="Perforce code branch/stream name.", required=True)
@click.option("--data-branch", help="Perforce data branch/stream name.", required=True)
@click.option(
    "--is-test-with-loose-files/--is-not-test-with-loose-files",
    default=False,
    help="Whether or not it's a test with loose files.",
)
@click.option(
    "--use-azure-drone-build",
    default=False,
    type=bool,
    help="Whether or not to test a drone build from Azure.",
)
@click.option(
    "--use-shift-build",
    default=False,
    type=bool,
    help="Whether or not it's a test with Shift builds.",
)
@click.option(
    "--use-spin-build",
    default=False,
    type=bool,
    help="Whether or not it's a test with Spin builds.",
)
@click.option("--platform", help="Which platform the tests run on", required=False, default="None")
@click.option(
    "--required-platforms",
    help="What platforms are required",
    required=False,
    multiple=True,
)
@click.option(
    "--client-platforms",
    help="Multi platforms for client running with large scale tests",
    required=False,
    multiple=True,
)
@click.option(
    "--prerequisite-test",
    help="What test tier should already be successful on the build.",
    default=None,
)
@click.option(
    "--use-latest-drone",
    default=False,
    type=bool,
    help="Whether or not to use the latest Drone build.",
)
@click.option(
    "--build-timeout-hours",
    default=1,
    type=int,
    help="Mark frosty builds as in use for build-timeout-hours => won't be deleted",
)
@click.option("--job-url", default="", help="The URL to the start job")
@click.option("--region", help="Which frosty region to find builds for", default="ww")
@click.option("--config", help="Which frosty config to find builds for", default="final")
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    code_branch,
    data_branch,
    is_test_with_loose_files,
    use_azure_drone_build,
    use_shift_build,
    use_spin_build,
    platform,
    required_platforms,
    client_platforms,
    prerequisite_test,
    use_latest_drone,
    build_timeout_hours,
    job_url,
    region,
    config,
):
    """
    Uses Bilbo to find build for testing.
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    metadata_manager = build_metadata_utils.setup_metadata_manager()
    LOGGER.info("Searching for a suitable build.")

    code_changelist = 0
    data_changelist = 0
    client_build_id = ""
    server_build_id = ""

    if platform.lower() == "none":
        platform = None

    platforms = []
    # start working with lists of platforms instead individual platforms
    required_platforms = list(required_platforms)
    if platform:
        platforms.append({"name": platform, "region": region, "config": config})
    for required_platform in required_platforms:
        if required_platform not in [platform["name"] for platform in platforms]:
            platforms.append({"name": required_platform, "region": None, "config": None})

    if use_azure_drone_build:
        LOGGER.info("Searching for Drone builds that have been uploaded to Azure.")
        query = {
            "type": "drone",
            "branch.keyword": code_branch,
            "azure": "*",
        }

        builds = list(
            metadata_manager.get_all_builds(limit=100, query=query, exclude_deleted=False)
        )
        client_build_id, data_changelist, code_changelist = process_drone_builds(
            data_branch, prerequisite_test, builds
        )
    elif use_shift_build:
        LOGGER.info("Searching for builds that have been uploaded to Shift.")
        query = {
            "type": "frosty",
            "code_branch.keyword": code_branch,
            "data_branch.keyword": data_branch,
            "shift": "*",
        }

        builds = list(
            metadata_manager.get_all_builds(limit=100, query=query, exclude_deleted=False)
        )
        client_build_id, data_changelist, code_changelist = process_frosty_builds(builds)
    elif use_spin_build:
        LOGGER.info("Searching for builds that have been uploaded to Spin.")

        query = {
            "type": "frosty",
            "code_branch.keyword": code_branch,
            "data_branch.keyword": data_branch,
        }

        # Client Build Query with Spin
        query_client = query.copy()
        client_build_id = []
        query_client["platform.keyword"] = "linux64"
        query_client["spin"] = "*"
        LOGGER.info("query_client_spin: {}".format(query_client))
        client_builds = list(
            metadata_manager.get_all_builds(limit=100, query=query_client, exclude_deleted=False)
        )

        if not client_builds:
            LOGGER.error("Client Spin build not found.")
        else:
            cbi, data_changelist, code_changelist = process_frosty_builds(client_builds)
            LOGGER.info("cbi: {}".format(cbi))
            client_build_id.append(cbi)

            query_package_type = client_builds[0].source.get("package_type")
            query_config = client_builds[0].source.get("config")

            # Client Build Query with other platform which does not have Spin
            query_client = query.copy()
            for _platform in client_platforms:
                query_client["platform.keyword"] = _platform
                query_client["package_type.keyword"] = query_package_type
                query_client["config.keyword"] = query_config
                query_client["data_changelist"] = data_changelist
                query_client["code_changelist"] = code_changelist
                LOGGER.info("query_client: {}".format(query_client))

                client_builds = list(
                    metadata_manager.get_all_builds(
                        limit=100, query=query_client, exclude_deleted=False
                    )
                )

                if not client_builds:
                    LOGGER.error("Client build not found.")
                else:
                    cbi, data_changelist, code_changelist = process_frosty_builds(client_builds)
                    LOGGER.info("cbi: {}".format(cbi))
                    client_build_id.append(cbi)

            # Server Build Query with Spin
            query_server = query.copy()
            query_server["platform.keyword"] = "linuxserver"
            query_server["data_changelist"] = data_changelist
            query_server["code_changelist"] = code_changelist
            query_server["spin"] = "*"

            server_builds = list(
                metadata_manager.get_all_builds(
                    limit=100, query=query_server, exclude_deleted=False
                )
            )

            if not server_builds:
                LOGGER.error("Server Spin build not found.")
            else:
                server_build_id, data_changelist, code_changelist = process_frosty_builds(
                    server_builds
                )

    elif is_test_with_loose_files:
        if not platforms:
            raise ELIPYException("No required platforms have been specified!")
        LOGGER.info("Searching for loose file builds.")
        frosty_builds = get_frosty_builds(metadata_manager, code_branch, data_branch, platforms)
        if frosty_builds:
            code_changelist = frosty_builds["code_changelist"]
            data_changelist = frosty_builds["data_changelist"]
            LOGGER.info("Marking {} builds as in use".format(len(frosty_builds["builds"])))
            for build in frosty_builds["builds"]:
                mark_build_as_in_use(metadata_manager, build, job_url, build_timeout_hours)
                if "server" in build.source["platform"].lower():
                    LOGGER.info("Setting server build id to: {}".format(build.id))
                    server_build_id = build.id
                if platform.lower() == build.source["platform"].lower():
                    LOGGER.info("Setting client build id to: {}".format(build.id))
                    client_build_id = build.id
    elif use_latest_drone:
        LOGGER.info("Searching for latests drone build.")
        latest_drone_build = get_latest_drone_build(code_branch, metadata_manager)
        data_changelist = "now"
        code_changelist = latest_drone_build.source["changelist"]
        client_build_id = latest_drone_build.id
        server_build_id = latest_drone_build.id
    else:
        LOGGER.info("Searching for drone build.")
        query = {"type": "drone", "branch.keyword": code_branch}
        builds = list(metadata_manager.get_all_builds(limit=1100, query=query))
        client_build_id, data_changelist, code_changelist = process_drone_builds(
            data_branch, prerequisite_test, builds
        )
        server_build_id = client_build_id

    # Print info to log and to prop file
    with open("{}/autotest.properties".format(frostbite_core.get_game_root()), "w+") as prop_file:
        if data_changelist != 0 and code_changelist != 0 and client_build_id != "":
            LOGGER.info("Build found.")
            LOGGER.info("Data changelist: {}".format(data_changelist))
            LOGGER.info("Code changelist: {}".format(code_changelist))
            LOGGER.info("Client build ID: {}".format(client_build_id))
            prop_file.write("data_changelist = {}\n".format(data_changelist))
            prop_file.write("code_changelist = {}\n".format(code_changelist))
            str_cbi = "client_build_id = "
            if isinstance(client_build_id, list):
                _cbi_list = [item.replace("\\\\", "\\\\\\\\") for item in client_build_id]
                str_cbi += ",".join(_cbi_list)
            else:
                str_cbi += client_build_id.replace("\\\\", "\\\\\\\\")
            LOGGER.info("str_cbi: {}".format(str_cbi))
            prop_file.write("{}\n".format(str_cbi))
            if server_build_id:
                LOGGER.info("Server build ID: {}".format(server_build_id))
                prop_file.write(
                    "server_build_id = {}\n".format(server_build_id.replace("\\\\", "\\\\\\\\"))
                )

        else:
            LOGGER.error("No build Found, check requirements!")
    LOGGER.info("Writing to file done, script complete.")


def process_drone_builds(data_branch, prerequisite_test, builds):
    """
    Process drone builds to find the best match
    """
    data_changelist = 0
    code_changelist = 0
    build_id = ""

    for build in builds:
        data = (datum for datum in build.source["verified_data"] if datum["branch"] == data_branch)
        for verified_data in data:
            if int(verified_data["changelist"]) > data_changelist:
                category = verified_data.get("autotests", {}).get(prerequisite_test)
                if prerequisite_test is None or (category and verify_tests_have_passed(category)):
                    data_changelist = int(verified_data["changelist"])
                    code_changelist = int(build.source["changelist"])
                    build_id = build.id

    return (build_id, data_changelist, code_changelist)


def process_frosty_builds(builds):
    """
    Process frosty builds
    """
    builds.sort(
        key=lambda i: (
            i.source["code_changelist"],
            i.source["data_changelist"],
            i.source["platform"],
            i.source["region"],
        ),
        reverse=True,
    )

    latest_build = builds[0].source
    latest_build_id = builds[0].id
    data_changelist = latest_build["data_changelist"]
    code_changelist = latest_build["code_changelist"]
    return (latest_build_id, data_changelist, code_changelist)


def build_has_value(build, property_name, value):
    """
    Check that the build has the given property and matching value
    """

    build_info = build.source
    result = False
    try:
        result = build_info[property_name] == value
    except Exception as _:
        result = False

    return result


def get_frosty_builds(_bilbo, code_branch, data_branch, platforms):
    """ " Finds the latest frosty build"""

    frosty_builds_candidates = get_frosty_build_candidates(
        _bilbo, code_branch, data_branch, platforms
    )

    LOGGER.info(
        "{} frosty builds found. Starting to look for corresponding Drone builds.".format(
            len(frosty_builds_candidates)
        )
    )

    for frosty_build_candidate in frosty_builds_candidates:
        data_changelist = frosty_build_candidate["data_changelist"]
        code_changelist = frosty_build_candidate["code_changelist"]
        query_string = (
            "type:drone AND branch.keyword:{} AND changelist:{} AND "
            "verified_data.changelist:{} AND verified_data.branch:{}".format(
                code_branch, code_changelist, data_changelist, data_branch
            )
        )
        LOGGER.info('Fetching Drone builds with query "{}"'.format(query_string))
        drone_builds = list(_bilbo.get_all_builds_query_string(query_string=query_string))

        for drone_build in drone_builds:
            for verified_data in drone_build.source["verified_data"]:
                if (
                    verified_data["changelist"] == data_changelist
                    and verified_data["branch"] == data_branch
                ):
                    LOGGER.info(
                        "Drone changelists found! Data changelist: {}, code changelist: {}".format(
                            data_changelist, code_changelist
                        )
                    )
                    return frosty_build_candidate
        LOGGER.info(
            "No Drone builds for code_changelist {} and data_changelist {} found.".format(
                code_changelist, data_changelist
            )
        )
    LOGGER.info("No changelists found.")
    return None


def get_frosty_build_candidates(_bilbo, code_branch, data_branch, platforms):
    """
    Get a list of frosty build candidates
    """
    frosty_platform_builds = list()
    good_frosty_builds = list()
    frosty_builds_candidates = list()

    for platform in platforms:
        frosty_platform_builds += get_frosty_platform_builds(
            _bilbo, code_branch, data_branch, platform
        )

    # Get all builds from a platform to check CLs
    for platform_builds in list(
        filter(lambda item: item.source["platform"] == platforms[0]["name"], frosty_platform_builds)
    ):
        # Get all the builds, from all platforms, with same data/code CLs
        good_frosty_builds = find_frosty_platform_builds(
            frosty_platform_builds,
            platform_builds.source["code_changelist"],
            platform_builds.source["data_changelist"],
        )
        # Check if the CLs were found in all required platform
        for platform in platforms:
            platform_check = list(
                filter(
                    lambda item: item.source["platform"] == platform["name"],
                    good_frosty_builds,
                )
            )
            # If one platform was not found, clear the list and try again with next CL
            if not platform_check:
                good_frosty_builds.clear()
                break
        if good_frosty_builds:
            frosty_builds_candidates.append(
                {
                    "code_changelist": platform_builds.source["code_changelist"],
                    "data_changelist": platform_builds.source["data_changelist"],
                    "builds": good_frosty_builds,
                }
            )

    return frosty_builds_candidates


def find_frosty_platform_builds(frosty_platform_builds, code_changelist, data_changelist):
    """ " Finds the frosty build in the list with given changelist"""
    return list(
        filter(
            lambda item: item.source["code_changelist"] == code_changelist
            and item.source["data_changelist"] == data_changelist,
            frosty_platform_builds,
        )
    )


def get_frosty_platform_builds(_bilbo, code_branch, data_branch, platform):
    """
    Return a list of frosty builds
    """
    query_string = (
        "type:frosty AND package_type:files AND code_branch.keyword:{} AND "
        "data_branch.keyword:{} AND platform.keyword: {} NOT deleted:*".format(
            code_branch, data_branch, platform["name"]
        )
    )
    if platform["region"]:
        query_string += " AND region: {}".format(platform["region"])
    if platform["config"]:
        query_string += (
            f" AND (config: {platform['config']} OR additional_configs: {platform['config']})"
        )

    LOGGER.info("Fetching frosty builds. Query: {}".format(query_string))

    frosty_query = {
        "query": {"query_string": {"query": query_string}},
        "sort": {"created": {"order": "desc"}},
    }
    frosty_builds = get_builds_with_config(
        list(_bilbo.get_builds_with_query(frosty_query)),
        platform["config"],
    )

    return frosty_builds


def mark_build_as_in_use(_bilbo, frosty_build, job_url, build_timeout_hours):
    """
    Marks the frosty builds as in use in Bilbo. Sets the build as unavailable for deletion for
    build_timeout_hours as defined in the test's AutotestMatrix.
    """
    code_changelist = frosty_build.source["code_changelist"]
    data_changelist = frosty_build.source["data_changelist"]
    LOGGER.info(
        "Marking frosty build with code CL {} and data CL {} as unavailable for deletion "
        "for {} hours".format(code_changelist, data_changelist, build_timeout_hours)
    )
    _bilbo.mark_build_as_in_use_until(frosty_build.id, job_url, build_timeout_hours)
