package com.ea.project.kin.mastersettings

import com.ea.project.kin.Kingston

class DiceKinTasks {
    static Class project = Kingston
    static Map branches = [
        'future-dev-content': [
            code_folder: 'dev',
            code_branch: 'kin-dev',
            data_folder: 'future',
            data_branch: 'future-dev-content',
        ],
        'future-dev-runmode': [
            code_folder: 'dev',
            code_branch: 'kin-dev',
            data_folder: 'dev',
            data_branch: 'future-dev-runmode',
        ],
        'kin-dev-anticheat' : [
            code_folder: 'tasks',
            code_branch: 'kin-dev-anticheat',
            data_folder: 'tasks',
            data_branch: 'kin-dev-anticheat',
        ]
    ]

    static Map preflight_branches = [:]
    static Map autotest_branches = [:]
    static Map integrate_branches = [
        'kin-dev_to_future-dev-runmode'           : [
            source_folder               : 'dev', source_branch: 'kin-dev',
            target_folder               : 'dev', target_branch: 'future-dev-runmode',
            code                        : false, data: true, parent_to_child: true, no_submit: false,
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call,
            workspace_root              : project.workspace_root, slack_channel: '#kin-integration-notify',
            extra_data_args             : [
                ' --exclude-path //' + project.p4_data_client_env + '/kindata/Source/Future/...',
            ],
            trigger_type_integrate      : 'scm', ignore_source_history: false,
            shelve_changelist           : true,
            manual_trigger              : true,
            freestyle_job_trigger_matrix: [],
        ],
        'future-dev-runmode_to_future-dev-content': [
            source_folder               : 'dev', source_branch: 'future-dev-runmode',
            target_folder               : 'future', target_branch: 'future-dev-content',
            code                        : false, data: true, parent_to_child: true, no_submit: false,
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call,
            workspace_root              : project.workspace_root, slack_channel: '#kin-integration-notify',
            trigger_type_integrate      : 'scm', ignore_source_history: false,
            shelve_changelist           : true,
            custom_code_branch          : true,
            code_project                : project, code_folder: 'dev', code_branch: 'kin-dev',
            freestyle_job_trigger_matrix: [],
        ],
    ]
    static Map copy_branches = [:]
    static Map feature_integrations = [:]
    static Map maintenance_branch = [
        'kin-dev': [
            code_folder: 'dev',
            code_branch: 'kin-dev',
            data_folder: 'dev',
            data_branch: 'future-dev-content',
        ]
    ]
    static List dashboard_list = []
    static Map dvcs_configs = [:]
    static List code_downstream_matrix = []
    static Map MAINTENANCE_SETTINGS = [:]
}
