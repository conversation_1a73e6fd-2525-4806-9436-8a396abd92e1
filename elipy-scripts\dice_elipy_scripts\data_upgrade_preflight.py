"""
data_upgrade.py
"""

import os
import click
from pathlib import Path, PurePath
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from dice_elipy_scripts.utils.preflight_utils import truncate_log_list
from elipy2 import code, core, data, LOGGER, frostbite_core, p4, running_processes, local_paths
from elipy2.cli import pass_context
from elipy2.exceptions import ELIPYException
from elipy2.telemetry import collect_metrics


@click.command("data_upgrade", short_help="Upgrade data locally on a branch.")
@click.option("--pending-changelist", help="Perforce pending changelist.")
@click.option(
    "--p4-output-files",
    multiple=True,
    help="File containing text output of p4 command.",
)
@click.option("--p4-port", required=True, help="Perforce port/server.")
@click.option("--p4-client", required=True, help="Perforce client/workspace.")
@click.option("--p4-user", default=None, help="Perforce user name.")
@click.option(
    "--p4-stream",
    default=None,
    help="Perforce stream path. Required with --p4-output-files. Format: //depot/branch/stream",
)
@click.option(
    "--data-directory",
    default=None,
    help="Which data directory to use for fetching licensee settings.",
)
@click.option(
    "--batch-file", default="IntegrationUpgradeLocal.bat", help="Upgrade batch file to run."
)
@click.option(
    "--p4-ignore",
    default=".p4ignore",
    help=".p4ignore file to set, if not will set the default of .p4ignore",
)
@click.option(
    "--build-frosted", default=None, help="Build frosted if upgrade scripts are to be run."
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
# pylint: disable=too-many-locals, invalid-name, too-many-statements, protected-access
def cli(
    _,
    pending_changelist,
    p4_output_files,
    p4_port,
    p4_client,
    p4_user,
    p4_stream,
    data_directory,
    batch_file,
    p4_ignore,
    build_frosted,
):
    """
    Upgrade data locally on a branch/stream, using UpgradeLocal.bat.
    """
    # Adding sentry tags.
    add_sentry_tags(__file__)

    running_processes.kill()

    if not p4_output_files and not pending_changelist:
        raise ELIPYException("Either --p4-output-files or --pending-changelist must be set.")

    if p4_output_files and not p4_stream:
        raise ELIPYException("--p4-stream is required with --p4-output-files.")

    if data_directory is not None:
        data.DataUtils.set_datadir(data_directory)

    file_list = []
    if pending_changelist:
        perforce = p4.P4Utils(port=p4_port, client=p4_client, user=p4_user)
        unshelve_list = perforce.unshelve(pending_changelist)

        for item in unshelve_list:
            if b"depotFile" in item:
                file_list.append(item[b"depotFile"].decode("utf-8"))
    if p4_output_files:
        for p4_file in p4_output_files:
            file_list += get_p4_files(p4_file, p4_stream)

    # Get the last folder from TnT path to be base_path or the common directory from
    # paths in UpgradeScripts.txt file and the depot paths from returned by unshelving
    # the CL.
    base_path = PurePath(frostbite_core.get_tnt_root()).parts[-1]
    match_changed_files = verify_upgrade_scripts(list(set(file_list)), str(base_path))

    core.ensure_p4_config(
        root_dir=frostbite_core.get_game_data_dir(),
        port=p4_port,
        client=p4_client,
        user=p4_user,
        ignore=p4_ignore,
    )

    os.environ["P4CONFIG"] = ".p4config"

    try:
        if not match_changed_files:
            LOGGER.info("Not necessary to run upgrade scripts. No elegible files detected")
        else:
            LOGGER.info(
                "Running upgrade scripts. Changes found in files: {}".format(match_changed_files)
            )
            if build_frosted:
                do_build_frosted(p4_port, p4_client, p4_user, pending_changelist)
            create_build_directory()
            upgrade_script = os.path.join(
                frostbite_core.get_tnt_root(), "Code", "DICE", "UpgradeScripts", batch_file
            )
            core.run([upgrade_script], print_std_out=True)
            LOGGER.info("Upgraded Data with shelf changelist")
    except Exception:
        # Clean machine if upgrade fails
        LOGGER.info("Update scripts failed, deleting VM")
        raise


def do_build_frosted(p4_port, p4_client, p4_user, changelist):
    """
    Build frosted.
    """
    builder = code.CodeUtils(
        "frosted",
        "release",
        monkey_build_label=changelist,
        p4_port=p4_port,
        p4_user=p4_user,
        p4_client=p4_client,
    )
    builder.buildsln()


def get_p4_files(p4_output_file: str, p4_stream: str) -> list:
    """
    Get a list of files from the output of a p4 command
    """
    file_list = []
    if not os.path.exists(p4_output_file):
        LOGGER.info("File {} does not exist.".format(p4_output_file))
    else:
        with open(p4_output_file) as file:
            for line in file.readlines():
                if line.startswith(p4_stream):
                    file_list.append(line.split("#")[0])
        truncate_log_list(
            file_list, "Parsed the following files out of '{}'".format(p4_output_file)
        )
    return file_list


def create_build_directory():
    """
    Create build directory. Usually located at dev/TnT/Local/Build
    """
    build_directory = os.path.join(local_paths.get_tnt_local_path(), "Build")
    if not os.path.exists(build_directory):
        os.makedirs(build_directory)
        LOGGER.info("Directory {} created.".format(build_directory))
    else:
        LOGGER.info("Directory {} already exists.".format(build_directory))


def read_upgrade_scripts_file(upgrade_scripts_path):
    """
    Read UpgradeScripts.txt file
    """
    with open(upgrade_scripts_path) as file:
        return file.readlines()


def process_upgrade_scripts_line(path, base_path):
    """
    Process UpdateScripts.txt to set excluded and included paths
    """
    if "#" in path or path.strip() == "":
        return None, None

    # Return exclude_path without - and from TnT(base_path) folder onwards
    if path.startswith("-"):
        # Remove - from path, remove spaces and resolve path
        # (which means make the path absolute, resolving any symlinks)
        upgrade_script_absolute_path: str = str(Path(path[len("-") :].strip()).resolve())
        return None, upgrade_script_absolute_path[upgrade_script_absolute_path.index(base_path) :]

    # Remove spaces and resolve path
    # (which means make the path absolute, resolving any symlinks)
    upgrade_script_absolute_path = str(Path(path.strip()).resolve())
    # Return include_path from TnT (base_path) folder onwards
    return upgrade_script_absolute_path[upgrade_script_absolute_path.index(base_path) :], None


def are_paths_related(file: PurePath, path: PurePath):
    """
    Creating a custom is_relative_to function to check if a path is related
    to another path. This is necessary because is_relative_to need python 3.10
    https://docs.python.org/3/library/pathlib.html#pathlib.PurePath.is_relative_to
    """

    return file == path or path in file.parents


def verify_upgrade_scripts(file_list, base_path="TnT"):
    """
    Check if is necessary to run upgrade scripts.
    """
    truncate_log_list(file_list, "Running with file_list:")
    LOGGER.info("Running with base_path: {0}".format(base_path))
    upgrade_scripts_path = os.path.join(
        frostbite_core.get_tnt_root(),
        "Code",
        "DICE",
        "UpgradeScripts",
        "UpgradeScripts.txt",
    )

    upgrade_scripts_lines = read_upgrade_scripts_file(upgrade_scripts_path)

    include_paths = []
    exclude_paths = []
    hits = []
    if not upgrade_scripts_lines:
        LOGGER.info("Empty UpgradeScripts.txt file")
    else:
        # Set current directory to UpgradeScripts.txt path
        os.chdir(Path(upgrade_scripts_path).resolve().parent)
        for path in upgrade_scripts_lines:
            include_path, exclude_path = process_upgrade_scripts_line(path, base_path)
            if include_path:
                include_paths.append(include_path)
            if exclude_path:
                exclude_paths.append(exclude_path)
        LOGGER.info("include_path: {0}".format(include_paths))
        LOGGER.info("exclude_paths: {0}".format(exclude_paths))

        for depot_file in file_list:
            # Search only for files in TnT folder
            if base_path in depot_file:
                # Get file path from TnT folder onwards
                file = PurePath((depot_file[depot_file.index(base_path) :]).strip())
                if any(
                    are_paths_related(file, PurePath(path.replace("\\", "/")))
                    for path in exclude_paths
                ):
                    LOGGER.info("File {0} was excluded based on UpgradeScripts.txt".format(file))
                elif any(
                    are_paths_related(file, PurePath(path.replace("\\", "/")))
                    for path in include_paths
                ):
                    hits.append(str(file))

    return hits
