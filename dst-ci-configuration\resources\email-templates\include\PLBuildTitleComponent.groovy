import com.sonyericsson.jenkins.plugins.bfa.model.FailureCauseBuildAction
import groovy.xml.MarkupBuilder
import hudson.model.Result
import hudson.model.Run

public class PLBuildTitleComponent implements IEmailComponent {

    private String getSectionClass(Map data) {
        def sectionClass = "build-title-section"
        def result = data.Result
        if (result) {
            sectionClass = "${sectionClass}-${data.Result.toLowerCase()}"
        }
        return sectionClass
    }


    private boolean isInfrastructureFailure(Run run) {
        def infrastructureFailure = false
        def result = run.getResult()
        if (result && result.equals(Result.FAILURE)) {
            def foundCauses = run.getAction(FailureCauseBuildAction.class)?.getFoundFailureCauses()
            if (foundCauses && !foundCauses.isEmpty()) {
                def categories = foundCauses[0].getCategories()
                if (categories) {
                    infrastructureFailure = categories.contains("dre")
                }
            }
        }
        return infrastructureFailure
    }


    private Map gather(Run run) {
        def displayName = run.getDisplayName()
        def fullDisplayName = run.getFullDisplayName()
        def projectName = run.getParent().getName()

        def result = run.getResult()?.toString()
        if (!result) {
            result = "IN_PROGRESS"
        }

        if (isInfrastructureFailure(run)) {
            result = "INFRASTRUCTURE"
        }

        def resultDisplay = result.replace("_", " ")

        // url
        def runURL = JobUtil.getClassicDisplayURL(run)

        return [
            DisplayName    : displayName,
            FullDisplayName: fullDisplayName,
            ProjectName    : projectName,
            Result         : result,
            ResultDisplay  : resultDisplay,
            RunURL         : runURL,
        ]
    }


    public void render(Run run, MarkupBuilder builder) {
        def data = gather(run)
        if (data) {
            builder.tr {
                td(class: getSectionClass(data), align: "center") {

                    mkp.yieldUnescaped("<!-- BUILD TITLE -->")
                    table(border: "0", cellpadding: "0", cellspacing: "0", width: "100%", style: "max-width: 500px;", class: "responsive-table") {
                        tr(class: "build-title") {
                            td(class: "build-title", align: "left") {
                                def buildTitleContent = "${data.FullDisplayName} - ${data.ResultDisplay}"
                                def runURL = data.RunURL
                                if (runURL) {
                                    a(href: runURL, class: "build-title") {
                                        mkp.yield(buildTitleContent)
                                    }
                                } else {
                                    mkp.yield(buildTitleContent)
                                }
                            }
                        }
                    }
                }
            }
        }
    }


    public boolean isApplicable(Run run) {
        return true
    }


    public String getEmbeddedStyle(Run run) {
        return """
                    /* BUILD TITLE SPECIFIC STYLES */

                    td.build-title-section-in_progress {
                        background-color: #35649E;
                        padding: 15px;
                    }
                    td.build-title-section-success {
                        background-color: #6B9E31;
                        padding: 15px;
                    }
                    td.build-title-section-infrastructure {
                        background-color: #858484;
                        padding: 15px;
                    }
                    td.build-title-section-unstable {
                        background-color: #F5A623;
                        padding: 15px;
                    }
                    td.build-title-section-failure {
                        background-color: #C0424A;
                        padding: 15px;
                    }
                    td.build-title-section-aborted {
                        background-color: #858484;
                        padding: 15px;
                    }
                    td.build-title-section-not_built {
                        background-color: #858484;
                        padding: 15px;
                    }
                    td.build-title {
                        padding: 15px 0px 15px 0px;
                        text-align: left;
                        vertical-align: top;
                    }
                    tr.build-title {
                        color: #FFFFFF;
                        font-family: "LatoLatinWeb", "Lato", "Helvetica Neue", Helvetica, Arial, sans-serif;
                        font-size: 18px;
                        font-weight: bold;
                    }
                    a.build-title {
                        color: #FFFFFF;
                        font-family: "LatoLatinWeb", "Lato", "Helvetica Neue", Helvetica, Arial, sans-serif;
                        font-size: 18px;
                        font-weight: bold;
                        text-decoration: none;
                    }
        """
    }
}
