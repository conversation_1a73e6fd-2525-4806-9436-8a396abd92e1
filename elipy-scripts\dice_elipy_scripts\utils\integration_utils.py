"""
integration_utils.py

Helper functions for Perforce integrations.
"""

import os
from typing import List, Optional
from dice_elipy_scripts.utils.bilbo_utils import get_latest_drone_changelist
from dice_elipy_scripts.utils.code_utils import run_gensln
from dice_elipy_scripts.utils.licensee_helper import set_licensee
from dice_elipy_scripts.utils.snowcache_utils import (
    clean_required,
    get_snowcache_host_arg,
    get_snowcache_mode_args,
    validate_use_snowcache_param,
)
from dice_elipy_scripts.utils.state_utils import import_avalanche_data_state
from elipy2 import (
    avalanche,
    build_metadata_utils,
    code,
    data,
    filer,
    frostbite_core,
    LOGGER,
    p4,
    SETTINGS,
)
from elipy2.exceptions import ELIPYException


def compile_code(
    licensee: List[str],
    password: str,
    email: str,
    domain_user: str,
    port: Optional[str] = None,
    user: Optional[str] = None,
    client: Optional[str] = None,
    buildsln_framework_args: Optional[List[str]] = None,
    framework_args: Optional[List[str]] = [],
    overwrite_p4config: Optional[bool] = False,
    clean: Optional[bool] = False,
    use_snowcache: Optional[bool] = False,
    snowcache_mode_override: Optional[str] = "",
):
    """
    Run gensln for the platform/config tool/release.
    Run buildsln for the platform/config tool/release, which includes frosted/release.
    """
    # Set licensee, which is required when running gensln.
    framework_args = set_licensee(licensee, framework_args)

    # Ensure that the Avalanche service is healthy before using Snowcache.
    use_snowcache = validate_use_snowcache_param(SETTINGS, "tool", use_snowcache)

    if use_snowcache:
        # Add additional snowcache args
        framework_args.append(get_snowcache_host_arg(SETTINGS, "tool"))
        snowcache_mode_args = get_snowcache_mode_args(clean, snowcache_mode_override, True)
        framework_args += snowcache_mode_args

    builder = code.CodeUtils(
        "tool",
        "release",
        p4_port=port,
        p4_user=user,
        p4_client=client,
        overwrite_p4config=overwrite_p4config,
    )

    if clean_required(use_snowcache, snowcache_mode_override, clean):
        builder.clean_local(close_handles=True)

    run_gensln(
        builder=builder,
        password=password,
        user=email,
        domain_user=domain_user,
        framework_args=framework_args,
    )
    builder.buildsln(framework_args=buildsln_framework_args)


def cook_data(
    assets: List[str],
    data_directory: str,
    platform: str,
    clean_avalanche_cook: Optional[bool] = False,
    clean_avalanche_drop_db: Optional[bool] = False,
    code_branch: Optional[str] = None,
    data_branch: Optional[str] = None,
    import_avalanche: Optional[bool] = False,
    p4_object: Optional[p4.P4Utils] = None,
    pipeline_args: Optional[List[str]] = [],
    use_local_code: Optional[bool] = False,
):
    """
    Run cook for the requested platform and data assets.
    Two options available for which code to use when cooking the data:
    - local, built on the machine earlier in the same build.
    - prebuilt, fetched from a network share.
    Two options available for Avalanche cleaning:
    - Cleaning the database by running a simplified cook.
    - Dropping databases we no longer need.
    """
    filer_object = filer.FilerUtils()

    if not use_local_code:
        if code_branch:
            metadata_manager = build_metadata_utils.setup_metadata_manager()
            # Fetch pipeline build from a network share, skip using primary metadata service.
            filer_object.fetch_code(
                code_branch,
                get_latest_drone_changelist(code_branch, metadata_manager),
                "pipeline",
                "release",
                use_bilbo=False,
            )
        else:
            raise ELIPYException(
                "To fetch code from the network share, you need to specify a code branch."
            )

    builder = data.DataUtils(platform=platform, assets=assets)
    builder.set_datadir(data_directory)

    # Avalanche cleaning
    if clean_avalanche_cook:
        builder.clean()
    if clean_avalanche_drop_db:
        avalanche.cleanup_temp_dbs(cleanup_threshold=10)

    if import_avalanche:
        # Import previous Avalanche state.
        if code_branch and data_branch and p4_object:
            data_changelist = p4_object.latest_pending_changelist()
            pipeline_args += import_avalanche_data_state(
                data_branch, code_branch, platform, filer_object, data_changelist
            )
        else:
            raise ELIPYException(
                f"To import Avalanche, you need to specify both a code branch and a data branch, "
                f"and also pass a Perforce object.\n"
                f"Current state - code branch: {code_branch}, "
                f"data branch: {data_branch}, Perforce object: {p4_object}"
            )

    # Cook data
    builder.cook(
        pipeline_args=pipeline_args,
        trim=False,
        collect_mdmps=True,
        clean_master_version_check=True,
    )


def submit_integration(
    p4_object: p4.P4Utils,
    submit_message: str,
    submit: bool,
    data_upgrade: Optional[bool] = False,
    revert_branchid_file: Optional[bool] = False,
    shelve_cl: Optional[bool] = False,
    submit_folder: Optional[str] = None,
):
    """
    Submit the result of the integration to Perforce.
    Skip submitting if we pass the flag --no-submit to the script.
    """
    submit_message += f"\nJenkins URL: {os.environ.get('BUILD_URL', 'None')}"
    if submit:
        if data_upgrade:
            reopen_file_name = "//..."
            if submit_folder is not None:
                reopen_file_name = os.path.join(
                    frostbite_core.get_game_data_dir(), submit_folder, "..."
                )
            p4_object.reopen(file_name=reopen_file_name)
            p4_object.revert(wipe=False, only_unchanged=True)
            if revert_branchid_file:
                # Revert the BranchId.py file.
                branchid_file = os.path.join(
                    frostbite_core.get_game_data_dir(), "Scripts", "BranchId.py"
                )
                p4_object.revert(path=branchid_file)
        p4_object.submit(message=submit_message)
    else:
        LOGGER.info("Skipping submission since --no-submit flag was passed.")
        LOGGER.info("Skipped submit:\n%s", submit_message)
        if shelve_cl:
            LOGGER.info("Shelving changelist for review.")
            pending_cl = p4_object.latest_pending_changelist()
            if pending_cl:
                shelve_message = "Shelved changelist from integration running with --no-submit."
                shelve_message += f"\nJenkins URL: {os.environ.get('BUILD_URL', 'None')}"
                p4_object.set_description(pending_cl, shelve_message)
                p4_object.shelve(pending_cl, discard=False)
                LOGGER.info("Shelved changelist: %s.", pending_cl)
            else:
                LOGGER.info("No changelist found to shelve.")
