"""
delete_git_lock_file.py
"""
import os

import click

from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2.cli import pass_context
from elipy2 import LOGGER
from elipy2.telemetry import collect_metrics
from elipy2.telemetry import log_error_to_es


@click.command("delete_git_lock_file", short_help="delete git lock file.")
@click.option(
    "--repo-path", default="D:\\dev\\ci", help="Path to folder where the git repo located."
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(_, repo_path):
    """
    Check if git has left a .lock file and delete it.
    """
    # adding sentry tags
    add_sentry_tags(__file__)
    path = f"{repo_path}\\.git\\shallow.lock"

    LOGGER.info("Checking for shallow.lock...")
    if os.path.exists(path):
        LOGGER.info("shallow.lock file exists")
        try:
            LOGGER.info("Attempting to delete .lock file")
            os.remove(path)

            LOGGER.info("Logging to ES")
            error = "gitlockfile"
            error_info = {"LockFile": path}
            log_error_to_es(error, error_info)
        except Exception as exc:
            LOGGER.error("Failed to delete git .lock file: with exception {0}".format(exc))
    else:
        LOGGER.info("No shallow.lock found - no clean-up required")
