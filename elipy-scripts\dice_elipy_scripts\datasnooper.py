"""
datasnooper.py

Runs the Datasnooper application found in the branch.

You can find more details about the tool here:
    https://gitlab.ea.com/lshulyayeva/datasnooper/-/tree/master/
"""

import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import LOGGER, core, frostbite_core
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics
from elipy2.exceptions import ELIPYException


@click.command("datasnooper", short_help="Run the datasnooper.")
@click.option(
    "--datasnooper-path",
    help="location of datasnooper.exe, relative from GAME_ROOT",
    default="tnt/bin/cm/fbcli/datasnooper.exe",
)
@click.option("--dbmanifest-path", help="location of dbmanifest file, relative from GAME_ROOT")
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(_, datasnooper_path, dbmanifest_path):
    """
    Runs datasnooper on a given data branch
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    game_root = frostbite_core.get_game_root()
    full_datasnooper_path = os.path.join(game_root, datasnooper_path)
    full_dbmanifest_path = os.path.join(game_root, dbmanifest_path)

    locations_to_test = [full_datasnooper_path, full_dbmanifest_path]

    LOGGER.info("Validating file paths")
    failures = list()
    for loc in locations_to_test:
        if not os.path.exists(loc):
            failures.append("Failed to find {}".format(loc))

    # raise exception if required
    if len(failures) > 0:
        message = os.linesep.join(failures)
        raise ELIPYException(message)

    # run datasnooper
    LOGGER.info("Running datasnooper")
    args = [full_datasnooper_path, "-dbmanifest", full_dbmanifest_path]
    core.run(args)
    LOGGER.info("Done")
