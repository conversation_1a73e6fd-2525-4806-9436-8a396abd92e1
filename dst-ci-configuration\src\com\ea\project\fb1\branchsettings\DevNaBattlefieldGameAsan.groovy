package com.ea.project.fb1.branchsettings

import com.ea.project.fb1.Fb1Battlefieldgame

class DevNaBattlefieldGameAsan {
    // Settings for jobs.
    static Class project = Fb1Battlefieldgame
    static Map general_settings = [
        dataset           : project.dataset,
        elipy_install_call: project.elipy_install_call,
        elipy_call        : "${project.elipy_call} --use-fbenv-core",
        frostbite_licensee: 'BattlefieldGame',
        workspace_root    : project.workspace_root,
    ]
    static Map standard_jobs_settings = [
        asset                       : 'DevLevels',
        claim_builds                : true,
        extra_code_args             : [' --framework-args -G:eaconfig.sanitize.address=on'],
        clean_local                 : false,
        code_data_sync              : true,
        enable_lkg_cleaning         : true,
        enable_lkg_p4_counters      : true,
        import_avalanche_state      : false,
        extra_data_args             : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -Pipeline.AssetTypeTimeSummary --pipeline-args -1 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true --pipeline-args -Pipeline.EmitAssetTypeSummaryFile --pipeline-args true --pipeline-args -Pipeline.MaxWarnings --pipeline-args 0 --pipeline-args Pipeline.EnableWarningCountSummary --pipeline-args true '],
        icepick_extra_framework_args: '',
        icepick_settings_files      : '',
        strip_symbols               : false,
        poolbuild_data              : true,
        trigger_type_data           : 'none',
        trigger_type_patchdata      : 'none',
        server_asset                : 'Game/Setup/Build/DevMPLevels',
        skip_icepick_settings_file  : true,
        slack_channel_code          : [
            channels                  : ['#bf-dev-na-build-notify'],
            skip_for_multiple_failures: true,
        ],
        slack_channel_data          : [
            channels                  : ['#bf-dev-na-build-notify'],
            skip_for_multiple_failures: true,
        ],
        deploy_tests                : false,
        data_reference_job          : 'dev-na-battlefieldgame-asan.code.start',
        split_code_data_sync        : true,
        statebuild_code             : false,
        timeout_hours               : 8,
        timeout_hours_data          : 3,
    ]
    static Map icepick_settings = [
        ignore_icepick_exit_code: false
    ]
    static Map preflight_settings = [:]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'tool', configs: ['release']],
        [name: 'win64game', configs: ['final']],
        [name: 'win64server', configs: ['final']],
        [name: 'ps5', configs: ['final']],
        [name: 'xbsx', configs: ['final']],
        [name: 'linux64server', configs: ['final']],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = [
        [name: '.code.p4counterupdater', args: ['code_changelist', 'code_countername']],
        [name: '.data.start', args: []],
    ]

    static List data_matrix = [
        'server',
    ]
    static List data_downstream_matrix = []
    static List patchdata_matrix = []
    static List frosty_matrix = []
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = []
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
