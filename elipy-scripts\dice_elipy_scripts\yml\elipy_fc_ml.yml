# Mandatory
default:
  script_path:
    - "D:\\git\\elipy\\elipy-scripts\\dice_elipy_scripts"
    #- "TnT\\Bin\\Python\\virtual\\Lib\\site-packages\\dice_elipy_scripts"
    - "Python\\virtual\\Lib\\site-packages\\dice_elipy_scripts"
  project_name: "fc"
  studio_location: "fc"
  vault_symstore: "false"
  handle_exe_path: "C:\\ProgramData\\chocolatey\\bin\\handle"
  build_share: ""
  p4_package_server: "eac-p4edge-fb.eac.ad.ea.com:1999"
  licensee_code_folder_name: "Code\\FIFAGame"
  skip_increment_client_version: true
  game_binaries: [""] # this maybe required when I get to their build setup
  release_candidate_retention: 56 # max_config(56) * number of release candidates
  tasks_to_kill:
    - 'AvalancheCLI.exe'
    - 'FrostyIsoTool.exe'
    - 'msbuild.exe'
    - 'nant.exe'
    - 'devenv.exe'
    - 'Tool.Pipeline_Win64_release_Dll.exe'
    - 'mspdbsrv.exe'
    - 'vctip.exe'
    - 'snowcacheserver.exe'
    - 'cl.exe'
    - 'animationapp.exe'
    - 'prospero-clang.exe'
    - 'Icepick.exe'
    - 'eapm.exe'

dev_ml_eav:
  path_retention:
    - \\\\eac.ad.ea.com\\sports\\drebuilds\\fc\\devg5\\code\\ml-no-raw: 5
  build_share: "\\\\eac.ad.ea.com\\sports\\drebuilds\\fc\\devg5"
  retention_categories:
    code:
      - 'default': 5
      - 'ml-no-raw': 5

dev_ml_earo:
  path_retention:
    - \\\\ro-dre-ext-b.eamobile.ad.ea.com\\drebuilds_fc_devg5\devg5_builds\\code\\ml-no-raw: 5
  build_share: "\\\\ro-dre-ext-b.eamobile.ad.ea.com\\drebuilds_fc_devg5\\devg5_builds"
  retention_categories:
    code:
      - 'default': 5
      - 'ml-no-raw': 5

g4_ml_eav:
  path_retention:
    - \\\\eac.ad.ea.com\\sports\\drebuilds\\fc\\devg4\\code\\ml-no-raw: 5
  build_share: "\\\\eac.ad.ea.com\\sports\\drebuilds\\fc\\devg4"
  retention_categories:
    code:
      - 'default': 5
      - 'ml-no-raw': 5

g4_ml_earo:
  path_retention:
    - \\\\ro-dre-ext.eamobile.ad.ea.com\\drebuilds_fifa_devg4\\devg4_builds\\code\\ml-no-raw: 5
  build_share: "\\\\ro-dre-ext.eamobile.ad.ea.com\\drebuilds_fifa_devg4\\devg4_builds"
  retention_categories:
    code:
      - 'default': 5
      - 'ml-no-raw': 5

#Test instances
test_dev_ml_eav:
  path_retention:
    - \\\\eac.ad.ea.com\\sports\\drebuilds\\fc\\devg5\\code\\ml-no-raw: 5
  build_share: "\\\\eac.ad.ea.com\\sports\\drebuilds\\fifa\\test"
  retention_categories:
    code:
      - 'default': 5
      - 'ml-no-raw': 5

test_dev_ml_earo:
  path_retention:
    - \\\\ro-dre-ext-b.eamobile:.ad.ea.com\\drebuilds_fc_devg5\\devg5_builds\\code\\ml-no-raw: 5
  build_share: "\\\\ro-dre-ext-b.eamobile:.ad.ea.com\\drebuilds_fc_devg5\\devg5_builds"
  retention_categories:
    code:
      - 'default': 5
      - 'ml-no-raw': 5

test_g4_ml_eav:
  path_retention:
    - \\\\eac.ad.ea.com\\sports\\drebuilds\\fifa\\testg4\\code\\ml-no-raw: 5
  build_share: "\\\\eac.ad.ea.com\\sports\\drebuilds\\fifa\\testg4"
  retention_categories:
    code:
      - 'default': 5
      - 'ml-no-raw': 5

test_g4_dev_ml_earo:
  path_retention:
    - \\\\eac.ad.ea.com\\sports\\drebuilds\\fc\\devg5\\code\\ml-no-raw: 5
  build_share: "\\\\eac.ad.ea.com\\sports\\drebuilds\\fc\\devg5"
  retention_categories:
    code:
      - 'default': 5
      - 'ml-no-raw': 5

