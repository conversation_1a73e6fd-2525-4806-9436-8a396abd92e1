{#
    Command:
        icepick_run_codetests
            short_help: Run icepick test after running gensln.
            context_settings: dict(ignore_unknown_options=True)

    Arguments:
        platform
            required: True
        run_args
            nargs: -1
            type: click.UNPROCESSED

    Required variables:
        test_suites
            help: The test suite to run followed by pool-type. Multiple.
            multiple: True
            required: True
        code_changelist
            default: None
            required: True
            help: Which code changelist to use.
        code_branch
            default: None
            required: True
            help: Branch to fetch code from.
        data_changelist
            default: None
            help: Which data changelist to use.
            required: True

    Optional variables:
        settings_files
            help: Settings files relative to the data folder.
            multiple: True
        data_branch
            help: Branch to fetch Avalanche state from.
            default: ''
        datadir
            default: data
            help: Specify which data directory to use (relative to GAME_ROOT).
        build_type
            default: static
            type: click.Choice(['static', 'dll', None])
            help: Static
        autobuild
            default: False
            help: Autobuild
            type: bool
        config
            default: release
            help: Config
        test_definition
            help: Which test to register.
            default: test_def
        licensee
            multiple: True
            default: None
            help: Licensee to use
        password
            default: None
            help: User credentials to authenticate to package server
        email
            default: None
            help: User email to authenticate to package server
        domain_user
            default: None
            help: The user to authenticate to package server as DOMAIN\user
        show_test_results
            default: False
            help: Report test failures on jenkins
        run_bilbo
            default: False
            help: Whether or not to report test results to the configured metadata services
        test_group
            default: nogroup
            help: Set test group name as part of metadata.
        inert_run
            default: False
            help: Run but don't do anything
        framework_args_icepick
            default: None
            help: Extra arguments for Icepick to pass to any Framework commands it starts
        framework_args_gensln
            default: None
            multiple: True
            help: Extra arguments for gensln to pass to any Framework commands it starts
        clean
            default: false
            help: Delete TnT/Local if --clean true is passed.
        custom_test_suite_data
            default: ''
            help: Custom test suite metadata to pass to Icepick.
        icepick_cook
            default: False
            help: Cook data in icepick.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    icepick_run_codetests
    platform {{ platform }}
    run-args {{ run_args }}
    --test-suites {{ test_suites }}
    --code-changelist {{ code_changelist }}
    --code-branch {{ code_branch }}
    --data-changelist {{ data_changelist }}
    {%- if settings_files %}
    --settings-files {{ settings_files }}
    {%- endif %}
    {%- if data_branch %}
    --data-branch {{ data_branch }}
    {%- endif %}
    {%- if datadir %}
    --datadir {{ datadir }}
    {%- endif %}
    {%- if build_type %}
    --build-type {{ build_type }}
    {%- endif %}
    {%- if autobuild %}
    --autobuild {{ autobuild }}
    {%- endif %}
    {%- if config %}
    --config {{ config }}
    {%- endif %}
    {%- if test_definition %}
    --test-definition {{ test_definition }}
    {%- endif %}
    {%- if licensee %}
    --licensee {{ licensee }}
    {%- endif %}
    {%- if password %}
    --password {{ password }}
    {%- endif %}
    {%- if email %}
    --email {{ email }}
    {%- endif %}
    {%- if domain_user %}
    --domain-user {{ domain_user }}
    {%- endif %}
    {%- if show_test_results %}
    --show-test-results {{ show_test_results }}
    {%- endif %}
    {%- if run_bilbo %}
    --run-bilbo {{ run_bilbo }}
    {%- endif %}
    {%- if test_group %}
    --test-group {{ test_group }}
    {%- endif %}
    {%- if inert_run %}
    --inert-run {{ inert_run }}
    {%- endif %}
    {%- if framework_args_icepick %}
    --framework-args-icepick {{ framework_args_icepick }}
    {%- endif %}
    {%- if framework_args_gensln %}
    --framework-args-gensln {{ framework_args_gensln }}
    {%- endif %}
    {%- if clean %}
    --clean {{ clean }}
    {%- endif %}
    {%- if custom_test_suite_data %}
    --custom-test-suite-data {{ custom_test_suite_data }}
    {%- endif %}
    {%- if icepick_cook %}
    --icepick-cook {{ icepick_cook }}
    {%- endif %}
  displayName: elipy icepick_run_codetests
