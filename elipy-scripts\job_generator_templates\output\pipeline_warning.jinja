{#
    Command:
        pipeline_warning
            short_help: Run the pipeline warning extraction scripts.

    Arguments:

    Required variables:
        data_directory
            required: True
            help: Data directory/dataset to build.
        username
            required: True
            help: Username for the pipeline warning service.
        password
            required: True
            help: Password for the pipeline warning service.
        target_address
            required: True
            help: Target to upload the result from the pipeline warning tool.

    Optional variables:
        platform
            default: win64
            help: Platform to build
        assets
            default: ['preflightlevels']
            multiple: True
            help: Asset(s) to be cooked.
        code_branch
            default: None
            help: Perforce branch/stream name for binaries.
        code_changelist
            default: None
            help: Perforce changelist number for binaries.
        data_branch
            help: Branch to fetch Avalanche state from.
            default: ''
        data_changelist
            default: ''
            help: Changelist of data being cooked.
        pipeline_args
            multiple: True
            help: Pipeline arguments for data build.
        import_avalanche_state
            is_flag: True
            help: Imports Avalanche state from filer.
        data_clean
            default: false
            help: Clean Avalanche if --data-clean true is passed.
        use_local
            is_flag: True
            help: Use local binaries (don't copy from filer).
        trim/__no_trim
            default: True
        licensee
            multiple: True
            default: None
            help: Licensee to use.
        clean_master_version_check
            is_flag: True
            help: Run clean on master version update.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    pipeline_warning
    --data-directory {{ data_directory }}
    --username {{ username }}
    --password {{ password }}
    --target-address {{ target_address }}
    {%- if platform %}
    --platform {{ platform }}
    {%- endif %}
    {%- if assets %}
    --assets {{ assets }}
    {%- endif %}
    {%- if code_branch %}
    --code-branch {{ code_branch }}
    {%- endif %}
    {%- if code_changelist %}
    --code-changelist {{ code_changelist }}
    {%- endif %}
    {%- if data_branch %}
    --data-branch {{ data_branch }}
    {%- endif %}
    {%- if data_changelist %}
    --data-changelist {{ data_changelist }}
    {%- endif %}
    {%- if pipeline_args %}
    --pipeline-args {{ pipeline_args }}
    {%- endif %}
    {%- if import_avalanche_state %}
    --import-avalanche-state {{ import_avalanche_state }}
    {%- endif %}
    {%- if data_clean %}
    --data-clean {{ data_clean }}
    {%- endif %}
    {%- if use_local %}
    --use-local {{ use_local }}
    {%- endif %}
    {%- if trim/__no_trim %}
    --trim/--no-trim {{ trim/__no_trim }}
    {%- endif %}
    {%- if licensee %}
    --licensee {{ licensee }}
    {%- endif %}
    {%- if clean_master_version_check %}
    --clean-master-version-check {{ clean_master_version_check }}
    {%- endif %}
  displayName: elipy pipeline_warning
