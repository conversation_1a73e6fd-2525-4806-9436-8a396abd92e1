import com.sonyericsson.jenkins.plugins.bfa.model.FailureCauseBuildAction
import groovy.xml.MarkupBuilder
import hudson.Functions
import hudson.Util
import hudson.model.Result
import hudson.model.Run
import hudson.model.TaskListener
import jenkins.metrics.impl.TimeInQueueAction

public class PLBuildSummaryComponent implements IEmailComponent {


    private String getRootCauseString(Run run) {
        def rootCauseString = null
        def rootCause = JobUtil.getRootUpstreamCause(run)
        if (rootCause) {
            rootCauseString = rootCause.getShortDescription().minus("Started by ")
        }
        if (rootCauseString.contains('anonymous')) {
            def args = run.getEnvironment(TaskListener.NULL)
            if (args.preflighter) rootCauseString = args.preflighter
        }
        return rootCauseString
    }


    private Map getPrimaryCauseData(Run run) {
        def primaryCause
        def primaryCauseData = [:]
        def foundCauses = run.getAction(FailureCauseBuildAction.class)?.getFoundFailureCauses()
        if (foundCauses && !foundCauses.isEmpty()) {
            primaryCause = foundCauses[0]
        }
        if (primaryCause) {
            def casuseName = primaryCause.getName()
            primaryCauseData.Name = casuseName

            def causeId = primaryCause.getId()
            def causeIndications = primaryCause.getIndications()
            if (causeIndications) {
                def indication = causeIndications.first()
                def indicationHash = indication.getMatchingHash()
                def runUrl = JobUtil.getClassicDisplayURL(run)
                primaryCauseData.Url = "${runUrl}consoleFull#${indicationHash}${causeId}"
            }
        }
        return primaryCauseData
    }


    private boolean isInfrastructureFailure(Run run) {
        def infrastructureFailure = false
        def result = run.getResult()
        if (result && result.equals(Result.FAILURE)) {
            def foundCauses = run.getAction(FailureCauseBuildAction.class)?.getFoundFailureCauses()
            if (foundCauses && !foundCauses.isEmpty()) {
                def categories = foundCauses[0].getCategories()
                if (categories) {
                    infrastructureFailure = categories.contains("dre")
                }
            }
        }
        return infrastructureFailure
    }


    private Map gatherCommonMetadata(Run run) {
        def commonMetadataKeys = ["Codeline", "Depot", "GameChangelist"]
        def commonMetadataMap = run.getEnvironment(TaskListener.NULL)

        def changelist = commonMetadataMap.unshelve_changelist
        if (changelist == null || changelist.isEmpty()) changelist = commonMetadataMap.P4_CHANGELIST
        def split = commonMetadataMap.JOB_NAME?.split('\\.')
        def branch = split?.size() > 0 ? split[0] : null

        return [
            Branch    : branch,
            Changelist: changelist,
        ]
    }


    private String getQueuingDurationString(Run run) {
        def queueingDurationString = null
        def timeInQueueAction = run.getAction(TimeInQueueAction)
        def runExecutor = run.getExecutor()

        def timeSpentInQueue = 0
        if (timeInQueueAction) {
            timeSpentInQueue = timeInQueueAction.getQueuingDurationMillis()
        } else if (runExecutor) {
            timeSpentInQueue = runExecutor.getTimeSpentInQueue()
        }

        def oneMinute = 1000 * 60
        if (timeSpentInQueue > oneMinute) {
            queueingDurationString = Util.getTimeSpanString(timeSpentInQueue)
        }
        return queueingDurationString
    }


    private String getDurationString(Run run) {
        def sb = new StringBuilder()

        def duration = run.getDuration()
        if (duration == 0) {
            def startTime = run.getStartTimeInMillis()
            def currentTime = System.currentTimeMillis()
            duration = currentTime - startTime
        }
        sb.append(Util.getTimeSpanString(duration))

        def queuingDurationString = getQueuingDurationString(run)
        if (queuingDurationString) {
            sb.append(String.format(" (Queued: %s)", queuingDurationString))
        }

        return sb.toString()
    }


    private Map gatherRunBasicData(Run run) {

        // basic run info
        def cause = getRootCauseString(run)
        def duration = getDurationString(run)
        def failureCauseData = getPrimaryCauseData(run)
        def result = run.getResult()?.toString()
        if (!result) {
            result = "IN_PROGRESS"
        }

        if (isInfrastructureFailure(run)) {
            result = "INFRASTRUCTURE"
        }

        def started = Functions.rfc822Date(run.getTimestamp())

        return [
            Cause           : cause,
            Duration        : duration,
            FailureCauseData: failureCauseData,
            Result          : result,
            Started         : started,
        ]
    }


    private Map gather(Run run) {
        def basicData = gatherRunBasicData(run)
        def commmonMetadata = gatherCommonMetadata(run)
        def data = basicData + commmonMetadata
        return data
    }


    private String getSectionClass(Map data) {
        def sectionClass = "build-summary-section"
        def result = data.Result
        if (result) {
            sectionClass = "${sectionClass}-${data.Result.toLowerCase()}"
        }
        return sectionClass
    }


    public void render(Run run, MarkupBuilder builder) {
        def data = gather(run)
        if (data) {
            builder.tr {
                td(class: getSectionClass(data), align: "center") {
                    mkp.yieldUnescaped("<!-- BUILD SUMMARY -->")
                    builder.table(border: "0", cellpadding: "0", cellspacing: "0", width: "100%", style: "max-width: 500px;", class: "responsive-table") {
                        mkp.yieldUnescaped("<!-- 1ST ROW -->")
                        tr(class: "build-summary-data") {
                            mkp.yieldUnescaped("<!-- BRANCH -->")
                            td(class: "build-summary-field") {
                                mkp.yield("Branch:")
                            }
                            td(class: "build-summary-value") {
                                mkp.yield(data.Branch ?: "")
                            }

                            mkp.yieldUnescaped("<!-- DURATION -->")
                            td(class: "build-summary-field") {
                                mkp.yield("Duration:")
                            }
                            td(class: "build-summary-value") {
                                mkp.yield(data.Duration ?: "")
                            }
                        }

                        mkp.yieldUnescaped("<!-- 2ND ROW -->")
                        tr(class: "build-summary-data") {
                            mkp.yieldUnescaped("<!-- COMMIT INFO -->")
                            td(class: "build-summary-field") {
                                mkp.yield("Changelist:")
                            }
                            td(class: "build-summary-value") {
                                mkp.yield(data.Changelist ?: "")
                            }

                            mkp.yieldUnescaped("<!-- STARTED INFO -->")
                            td(class: "build-summary-field") {
                                mkp.yield("Started:")
                            }
                            td(class: "build-summary-value") {
                                mkp.yield(data.Started ?: "")
                            }
                        }

                        def failureCauseData = data.FailureCauseData
                        if (failureCauseData) {
                            def failureCauseName = failureCauseData.Name
                            def failureCauseUrl = failureCauseData.Url

                            mkp.yieldUnescaped("<!-- ADDITIONAL ROW -->")
                            tr(class: "build-summary-data") {
                                mkp.yieldUnescaped("<!-- CAUSE INFO -->")
                                td(class: "build-summary-field") {
                                    mkp.yield("Failure Cause:")
                                }
                                td(class: "build-summary-value", colspan: "3") {
                                    if (failureCauseUrl) {
                                        a(href: failureCauseUrl, class: "build-summary-value") {
                                            mkp.yield(failureCauseName)
                                        }
                                    } else {
                                        mkp.yield(failureCauseName)
                                    }
                                }
                            }
                        }

                        def cause = data.Cause
                        if (cause) {
                            mkp.yieldUnescaped("<!-- ADDITIONAL ROW -->")
                            tr(class: "build-summary-data") {
                                mkp.yieldUnescaped("<!-- CAUSE INFO -->")
                                td(class: "build-summary-field") {
                                    mkp.yield("Started by:")
                                }
                                td(class: "build-summary-value", colspan: "3") {
                                    mkp.yield(cause)
                                }
                            }
                        }
                    }
                }
            }
        }
    }


    public boolean isApplicable(Run run) {
        return true
    }


    public String getEmbeddedStyle(Run run) {
        return """
                    /* BUILD SUMMARY SPECIFIC STYLES */

                    td.build-summary-section-in_progress {
                        background-color: #3A6FB0;
                        padding: 15px;
                    }
                    td.build-summary-section-success {
                        background-color: #77B037;
                        padding: 15px;
                    }
                    td.build-summary-section-infrastructure {
                        background-color: #949393;
                        padding: 15px;
                    }
                    td.build-summary-section-unstable {
                        background-color: #F6B44B;
                        padding: 15px;
                    }
                    td.build-summary-section-failure {
                        background-color: #D64B53;
                        padding: 15px;
                    }
                    td.build-summary-section-aborted {
                        background-color: #949393;
                        padding: 15px;
                    }
                    td.build-summary-section-not_built {
                        background-color: #949393;
                        padding: 15px;
                    }
                    tr.build-summary-data {
                        color: #FFFFFF;
                        font-family: "LatoLatinWeb", "Lato", "Helvetica Neue", Helvetica, Arial, sans-serif;
                        font-size: 12px;
                        text-align: left;
                    }
                    td.build-summary-field {
                        font-weight: bold;
                        padding-top: 10px;
                    }
                    td.build-summary-value {
                        font-weight: normal;
                        padding-top: 10px;
                    }
                    a.build-summary-value {
                        color: #FFFFFF;
                        font-family: "LatoLatinWeb", "Lato", "Helvetica Neue", Helvetica, Arial, sans-serif;
                        font-size: 12px;
                        font-weight: normal;
                        text-decoration: none;
                    }
        """
    }
}
