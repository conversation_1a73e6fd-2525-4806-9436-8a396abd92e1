"""
data_upgrader_validator.py
"""
import os
import click
from deprecated import deprecated

from elipy2 import code, core, data, LOGGER, p4, frostbite_core
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics
from elipy2.exceptions import ELIPYException
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.licensee_helper import set_licensee
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags


@click.command("data_upgrader_validator", short_help="Runs a data upgrader validation.")
@click.option(
    "--code-changelist",
    default=None,
    help="Code changelist for the branch where the upgrader is located.",
)
@click.option("--data-branch-dest", default=None, help="Perforce dest branch/stream name.")
@click.option("--data-branch-source", default=None, help="Perforce source branch/stream name.")
@click.option("--data-dir-dest", default=None, help="Data directory for destination.")
@click.option("--data-dir-source", default=None, help="Data directory for source.")
@click.option(
    "--licensee",
    multiple=True,
    default=None,
    help="Game licensee used for FrostbiteDatabaseUpgrader.",
)
@click.option("--p4-client-code", required=True, help="Perforce workspace for code.")
@click.option("--p4-client-dest", required=True, help="Perforce workspace for destination data.")
@click.option("--p4-client-source", required=True, help="Perforce workspace for source data.")
@click.option("--p4-port-code", required=True, help="Perforce server for code.")
@click.option("--p4-port-dest", required=True, help="Perforce server for destination data.")
@click.option("--p4-port-source", required=True, help="Perforce server for source data.")
@click.option("--p4-user", default=None, help="Perforce user name.")
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
@deprecated(version="8.1", reason="data_upgrader_validator was never used or activated")
def cli(
    _,
    code_changelist,
    data_branch_dest,
    data_branch_source,
    data_dir_dest,
    data_dir_source,
    licensee,
    p4_client_code,
    p4_client_dest,
    p4_client_source,
    p4_port_code,
    p4_port_dest,
    p4_port_source,
    p4_user,
):
    """
    Runs a data upgrader validation.
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    LOGGER.info(
        "Running a validation for the FrostbiteDatabaseUpgrader, for upgrades from {} to {}.".format(  # pylint: disable=line-too-long
            data_branch_source, data_branch_dest
        )
    )

    # Create Perforce objects.
    perforce_dest = p4.P4Utils(port=p4_port_dest, client=p4_client_dest, user=p4_user)
    perforce_source = p4.P4Utils(port=p4_port_source, client=p4_client_source, user=p4_user)

    # Set data directory to be the destination data directory.
    data.DataUtils.set_datadir(data_dir_dest)

    # Generate solution
    framework_args = set_licensee(list(licensee), [])
    platform = "tool"
    config = "release"
    builder = code.CodeUtils(
        platform,
        config,
        monkey_build_label=code_changelist,
        p4_client=p4_client_code,
        p4_port=p4_port_code,
        p4_user=p4_user,
    )
    builder.gensln(framework_args=framework_args)
    # Build solution
    builder.buildsln()

    # Set the path to the FrostbiteDatabaseUpgrader folder.
    fdu_path = os.path.join(
        frostbite_core.get_tnt_root(), "Code", "Utils", "FrostbiteDatabaseUpgrader"
    )
    if not os.path.exists(fdu_path):
        raise ELIPYException("Couldn't find the FrostbiteDatabaseUpgrader folder.")

    # Set the path to the Kingston-specific scripts.
    kingston_path = os.path.join(frostbite_core.get_tnt_root(), "Code", "DICE", "DataUpgrade")
    if not os.path.exists(kingston_path):
        raise ELIPYException("Couldn't find location for Kingston-specific scripts.")

    # Check if the old path to these scripts exist.
    # It shouldn't be there, but may still exist on some streams that could be copied to dev-na.
    old_kingston_path = os.path.join(fdu_path, "Kingston")
    if os.path.exists(old_kingston_path):
        raise ELIPYException(
            (
                "Old location for Kingston-specific scripts exists in parallel, "
                "this shouldn't be duplicated."
            )
        )  # pylint: disable=line-too-long

    # Get game root, which will be used in several places.
    game_root = frostbite_core.get_game_root()

    # Create .p4config files for both datasets.
    core.ensure_p4_config(
        root_dir=os.path.join(game_root, data_dir_source),
        port=p4_port_source,
        client=p4_client_source,
        user=p4_user,
    )
    core.ensure_p4_config(
        root_dir=os.path.join(game_root, data_dir_dest),
        port=p4_port_dest,
        client=p4_client_dest,
        user=p4_user,
    )

    # Set paths to use when validating.
    exe_path = os.path.join(fdu_path, "bin", "FrostbiteDatabaseUpgrader.exe")
    source_game_data_dir = os.path.join(game_root, data_dir_source)
    dest_game_data_dir = os.path.join(game_root, data_dir_dest)
    scripts_path = os.path.join(kingston_path, "BattlefieldGameScripts.txt")
    roots_path = os.path.join(kingston_path, "Roots.txt")

    validator_args = [
        exe_path,
        "/SOURCE",
        source_game_data_dir,
        "/DEST",
        dest_game_data_dir,
        "/GAMEROOT",
        game_root,
        "/SCRIPTS",
        scripts_path,
        "/SOURCECONTROL",
        "/WRITE",
        "/PURGE",
        "/GENERATETYPEDB",
        "/VERBOSE",
        "/ROOTS",
        roots_path,
        "/LICENSEE",
        licensee,
    ]

    try:
        _, stdout, _ = core.run(validator_args)
        LOGGER.info("".join(stdout))
    finally:
        perforce_dest.revert(quiet=True)
        perforce_source.revert(quiet=True)
