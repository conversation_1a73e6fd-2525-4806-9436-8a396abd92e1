import com.ea.lib.jobsettings.OrphanedSettings
import spock.lang.Specification

class OrphanedSettingsSpec extends Specification {

    class MasterFile {
        static Map MAINTENANCE_SETTINGS = [
            ORPHANED_TRIGGER      : '0 7,18 * * *',
            ORPHANED_SLACK_CHANNEL: '#test-channel',
        ]
    }

    class ProjectFile {
        static String short_name = 'kin'
    }

    void "test that we get expected job settings in initializeStartJob"() {
        when:
        OrphanedSettings settings = new OrphanedSettings()
        settings.initializeStartJob(MasterFile, ProjectFile)
        then:
        with(settings) {
            description == 'Maintenance job that periodically removes orphaned jobs from the Jenkins master'
            cronTrigger == MasterFile.MAINTENANCE_SETTINGS.ORPHANED_TRIGGER
            slackChannel == MasterFile.MAINTENANCE_SETTINGS.ORPHANED_SLACK_CHANNEL
            projectShortName == ProjectFile.short_name
        }
    }
}
