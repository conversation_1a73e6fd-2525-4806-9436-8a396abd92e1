package com.ea.project.bctch1.branchsettings

import com.ea.project.bctch1.BctCh1

class CH1_content_dev_C1S2B1 {
    // Settings for jobs
    static Class project = BctCh1
    static Map general_settings = [
        dataset           : project.dataset,
        elipy_call        : project.elipy_call + ' --use-fbenv-core',
        elipy_install_call: project.elipy_install_call,
        frostbite_licensee: project.frostbite_licensee,
        workspace_root    : project.workspace_root,
    ]
    static Map standard_jobs_settings = [
        asset                     : 'Game/Setup/Build/C1S2Levels Game/GlacierFlow/Flow_MainMenu/Flow_MainMenu',
        frosty_reference_job      : 'CH1-content-dev.deployment-data.start',
        timeout_hours_frosty      : 6,
        extra_frosty_args         : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true --pipeline-args -activeContentLayer --pipeline-args C1S2B1'],
        use_linuxclient           : true,
        server_asset              : 'Game/Setup/Build/C1S2Levels',
        shift_branch              : true,
        shift_every_build         : false,
        skip_icepick_settings_file: true,
    ]
    // Matrix definitions for jobs
    static List code_matrix = []
    static List code_downstream_matrix = []
    static List code_nomaster_matrix = []
    static List data_matrix = []
    static List data_downstream_matrix = [
        [name: '.frosty.start', args: []],
    ]
    static List patchdata_matrix = []
    static List frosty_matrix = [
        [name: 'win64', variants: [
            [format: 'files', config: 'final', region: 'ww', args: ' --additional-configs performance'],
            [format: 'files', config: 'performance', region: 'ww', args: ' --additional-configs final']
        ]],
        [name: 'ps5', variants: [
            [format: 'files', config: 'final', region: 'dev', args: ' --additional-configs release'],
            [format: 'files', config: 'performance', region: 'dev', args: ' --additional-configs final']
        ]],
        [name: 'xbsx', variants: [
            [format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release'],
            [format: 'files', config: 'performance', region: 'ww', args: ' --additional-configs final']
        ]],
        [name: 'server', variants: [
            [format: 'digital', config: 'final', region: 'ww', args: ''],
            [format: 'files', config: 'final', region: 'ww', args: '']
        ]],
        [name: 'linuxserver', variants: [
            [format: 'digital', config: 'final', region: 'ww', args: ''],
            [format: 'files', config: 'final', region: 'ww', args: '']
        ]],
    ]
    static List frosty_downstream_matrix = [
        [name: '.shift.start', args: ['code_changelist', 'data_changelist']],
        [name: '.spin.linuxserver.digital.final.ww', args: ['code_changelist', 'data_changelist']],
        [name: '.bilbo.register-bfdata-dronebuild', args: ['code_changelist', 'data_changelist']],
    ]
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww']]],
    ]
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
