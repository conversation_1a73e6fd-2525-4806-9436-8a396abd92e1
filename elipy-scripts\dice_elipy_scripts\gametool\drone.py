"""
gametool/drone.py
"""
import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.licensee_helper import set_licensee
from dice_elipy_scripts.utils.frosty_build_utils import authenticate_eapm_credstore
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import code, frostbite_core, p4, running_processes
from elipy2.cli import pass_context
from elipy2.exceptions import ELIPYException
from elipy2.telemetry import collect_metrics


@click.command("drone", short_help="Build and submit Drone")
@click.option("--changelist", help="Deprecated parameter. Use --code-changelist instead")
@click.option("--code-changelist", default=None, help="Perforce changelist number")
@click.option(
    "--clean",
    type=click.BOOL,
    default=False,
    help="Delete TnT/Local if --clean true is passed, otherwise no cleanup is performed.",
)
@click.option("--config", default="release")
@click.option("--p4-port", required=True)
@click.option("--p4-client", required=True)
@click.option("--password", required=False)
@click.option("--email", required=False)
@click.option(
    "--domain-user",
    default=None,
    help="The user to authenticate to package server as DOMAIN\\user",
)
@click.option("--user", default=None, help="Perforce user name")
@click.option("--licensee", multiple=True, default=None, help="Licensee to use")
@click.option(
    "--framework-args",
    multiple=True,
    default=[],
    help="Framework arguments for nantonpackage.",
)
@click.option("--dry-run", is_flag=True, help="Don't submit the built result.")
@click.option("--submit", type=click.BOOL, default=True, help="Set this to false for dry-run")
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    changelist,
    code_changelist,
    clean,
    config,
    p4_port,
    p4_client,
    password,
    email,
    domain_user,
    user,
    licensee,
    framework_args,
    dry_run,
    submit,
):
    """
    Generate and build the solution.
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    # Ensure backwards compatibility
    if code_changelist is None:
        if changelist is None:
            raise ELIPYException("No code changelist specified, this is required.")
        code_changelist = changelist

    running_processes.kill()
    builder = code.CodeUtils(
        platform=None,
        config=config,
        monkey_build_label=code_changelist,
        package="Drone",
        target="releasepackage",
    )

    if clean:
        builder.clean_local(close_handles=True)

    perforce = p4.P4Utils(p4_port, user=user, client=p4_client)
    perforce.set_environment()
    drone_setup_path = (
        os.path.join(frostbite_core.get_tnt_root(), "Setup", "Drone") + os.sep + "..."
    )
    perforce.sync(drone_setup_path)
    perforce.edit(drone_setup_path)

    if password and email:
        authenticate_eapm_credstore(password, email, domain_user)

    framework_args = list(framework_args)
    framework_args = set_licensee(list(licensee), framework_args)
    if frostbite_core.minimum_fb_version(year=2022, season=2, version_nr=1):
        framework_args.append("-G:nant.net-family-target=dotnet")
        framework_args.append("-G:nant.packageserverondemand=true")

    builder.nantonpackage(framework_args=framework_args)
    if (not dry_run) and submit:
        perforce.reconcile(path=drone_setup_path)
        submit_message = "Drone binaries for CL {}".format(code_changelist)
        submit_message += "\nJenkins URL: " + os.environ.get("BUILD_URL", "None")
        perforce.submit(message=submit_message)
    else:
        perforce.revert(quiet=True)
