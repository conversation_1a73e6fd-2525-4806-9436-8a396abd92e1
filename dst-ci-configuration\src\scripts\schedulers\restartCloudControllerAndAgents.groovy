package scripts.schedulers

import com.ea.lib.LibSlack
import hudson.Launcher
import hudson.model.Computer
import hudson.model.FreeStyleBuild
import hudson.model.Job
import hudson.util.StreamTaskListener
import jenkins.model.<PERSON>

import java.time.Duration
import java.time.Instant

/**
 * restartCloudControllerAndAgents.groovy
 * Tries to initiate Jenkins restart if no jobs are running within $wait_doquite_hours hours
 * Eventually kills all running jobs after $wait_forcekill_hours hours and puts them in the queue instead.
 * After that schedules a restart.
 */

pipeline {
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    agent { label 'master' }
    stages {
        stage('Run') {
            steps {
                script {
                    echo '--> Job Start'
                    if (params.restart_controller || params.restart_nodes) {
                        disableJobs(this)
                        if (numberOfRunningBuilds() > 1) {
                            echo "--> There are still ${numberOfRunningBuilds()} busy executor(s) on the controller, continue waiting..."
                            rescheduleAndKillJobs(this)
                        } else {
                            echo '--> This is the only running job, can continue restart'
                        }

                        if (params.restart_nodes) {
                            rebootJenkinsNodes(this)
                        }
                        if (params.restart_controller) {
                            restartJenkinsController(this)
                        } else {
                            // disableJobs does not persist a controller restart. If no restart occurs, re-enable jobs instead
                            enableJobs(this)
                        }
                    } else {
                        echo '--> No controller or node restart configured. Skipping job...'
                    }
                    echo '--> Job complete'
                }
            }
        }
    }
    post {
        changed {
            script {
                LibSlack.forPipelines(this, '#cobra-outage-jenkins', 'cob')
            }
        }
    }
}

/**
 * disable jobs on the controller. Note that disabling jobs from here does not survive a controller reboot
 */
void disableJobs(context) {
    Jenkins controller = Jenkins.get()
    List<Job> jobs = controller.getItems(Job)
    def controllerNode = controller.toComputer()
    tryToFindWindow(context, params.wait_doquite_hours.toInteger(), controllerNode) {
        context.echo '--> Disable all Jobs - preparation for a restart.'
        for (Job job in jobs.findAll { it.triggers && ![currentBuild.projectName, 'seed'].contains(it.name) }) {
            context.echo "--> Disabling ${job.name}"
            job.disabled = true
        }
    }
}

/**
 * enable jobs on the controller
 */
void enableJobs(context) {
    Jenkins controller = Jenkins.get()
    List<Job> jobs = controller.getItems(Job)
    def controllerNode = controller.toComputer()
    tryToFindWindow(context, params.wait_doquite_hours.toInteger(), controllerNode) {
        context.echo '--> Re-enable Jobs'
        for (Job job in jobs.findAll { it.triggers && ![currentBuild.projectName, 'seed'].contains(it.name) }) {
            context.echo "--> Enabling ${job.name}"
            job.disabled = false
        }
    }
}

/**
* Reschedule running jobs to recover as soon as possible following the shutdown process. Then kill all jobs
*/
void rescheduleAndKillJobs(context) {
    Jenkins controller = Jenkins.get()
    List<Job> jobs = controller.getItems(Job)
    def controllerNode = controller.toComputer()
    tryToFindWindow(context, params.wait_forcekill_hours.toInteger(), controllerNode) {
        def runningBuilds = (jobs.findAll { it.name != currentBuild.projectName && it.building }*.builds).flatten().findAll { it.building }
        def numberOfRunningBuilds = runningBuilds.size()
        for (run in runningBuilds) {
            run.class == FreeStyleBuild ? run.doStop() : run.doKill()
            def job = run.parent
            if (!job.inQueue) {
                int quietPeriod = Math.max(numberOfRunningBuilds * 60 * 2, 600)
                job.scheduleBuild2(quietPeriod)
            }
        }

        def maxWaitMinutes = numberOfRunningBuilds // wait 1 minute per job
        waitForKilledJobsToStop(context, maxWaitMinutes)
        if (controllerNode.countBusy() > 1) {
            context.echo '--> Timed out waiting for killed jobs to stop'
        }
    }
}

/**
 * Try to find a window where not Jobs are running on the controller (except this one)
 * @param forHours How many hours should we try for
 * @param controllerNode The jenkins controller node
 * @param andThen What should run after the wait time has expired or a window is found
 */
void tryToFindWindow(context, Integer forHours, Computer controllerNode, Closure andThen) {
    def startTime = Instant.now()
    long numWaitedHours = 0
    while (controllerNode.countBusy() > 1 && numWaitedHours < forHours) {
        // we assume that all running jobs have been scheduled by controller
        context.echo "${controllerNode.displayName} is busy"
        // print running jobs
        for (executor in controllerNode.executors) {
            if (executor.currentExecutable) {
                context.echo " > ${executor.currentExecutable}"
            }
        }
        int sleepSeconds = 60
        context.echo "--> Jobs are still running. Sleeping for $sleepSeconds seconds.\n"
        Thread.sleep(sleepSeconds * 1000)
        numWaitedHours = Duration.between(startTime, Instant.now()).toHours()
        context.echo "--> DEBUG: startTime: ${startTime}, currenTime: ${Instant.now()}, numHoursWaited: ${numWaitedHours}. numHoursToWait: ${forHours}"
    }
    andThen()
}

/**
 * Reboot all nodes connected to this controller
 */
void rebootJenkinsNodes(context) {
    context.echo '--> Rebooting all nodes.'
    runCommandOnAllNodes(
        context,
        'echo Nothing to delete',
        'cmd /c del /Q C:\\jenkins_agent.pid C:\\JenkinsSlave\\jenkins_agent.pid',
        'Deleted PID file for '
    )
    runCommandOnAllNodes(context, 'reboot', 'shutdown /r /t 1', 'Rebooted ')
}

/**
 * For all the jobs that we have tried to kill to stop
 * @param controller The jenkins controller instance
 * @param maxWaitMinutes How long should we wait for jobs to stop running
 */
void waitForKilledJobsToStop(context, int maxWaitMinutes) {
    // Sometimes Jenkins seems to need a few moments to finish the doStop() and doKill() calls.
    // I haven't been able to figure out when or why that happens.
    def startTime = Instant.now()
    int sleepSeconds = 10
    while (Duration.between(startTime, Instant.now()).toMinutes() < maxWaitMinutes && numberOfRunningBuilds() > 1) {
        context.echo "--> Waiting for killed jobs to actually stop (${numberOfRunningBuilds()} jobs left). Sleeping for $sleepSeconds seconds.\n"
        Thread.sleep(sleepSeconds * 1000)
        sleepSeconds += 10
    }
}

/**
 * Run a give command on all nodes. Unix command will run on Unix based nodes and the windows command will run on Windows nodes
 * @param unixCommand The command to run on Unix nodes
 * @param windowsCommand The command to run on Windows nodes
 * @param successMessagePrefix What message to show after the command was ran successfully
 */
void runCommandOnAllNodes(context, String unixCommand, String windowsCommand, String successMessagePrefix) {
    for (Node node in Jenkins.get().nodes) {
        runCommandOnNode(context, node, unixCommand, windowsCommand, successMessagePrefix)
    }
}

/**
 * Run a give command on a given node. Unix command will run on Unix based nodes and the windows command will run on Windows nodes
 * @param node What node should we try and run the command on
 * @param unixCommand The command to run on Unix nodes
 * @param windowsCommand The command to run on Windows nodes
 * @param successMessagePrefix What message to show after the command was ran successfully
 */
void runCommandOnNode(context, Node node, String unixCommand, String windowsCommand, String successMessagePrefix) {
    if (node.toComputer().online) {
        StreamTaskListener listener = new StreamTaskListener(new ByteArrayOutputStream())
        Launcher launcher = node.createLauncher(listener)
        Launcher.ProcStarter process = Launcher.ProcStarter.newInstance()
        process.stdout(listener).pwd(node.rootPath)
        process.cmdAsSingleString(launcher.unix ? unixCommand : windowsCommand)
        try {
            launcher.launch(process).join()
            context.echo "$successMessagePrefix ${node.nodeName}"
        }
        catch (Exception ex) {
            context.echo ex.message
            context.echo "\tat ${ex.stackTrace.join('\n\tat ')}"
        }
    } else {
        context.echo "Node ${node.displayName} is offline. Skipping."
    }
}

/**
 * Get number of running builds on Jenkins
 */
Boolean numberOfRunningBuilds() {
    Jenkins controller = Jenkins.get()
    return controller.toComputer().countBusy()
}

/**
 * Restart controller
 */
void restartJenkinsController(context) {
    context.echo '--> Restart Jenkins service!'
    Jenkins controller = Jenkins.get()
    controller.restart()
}
