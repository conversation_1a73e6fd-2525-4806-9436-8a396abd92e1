/**
 * ReturnBaselineFile.groovy
 * Returns a baseline with branch and changelist for code and data, when given a project name, branch name and platform.
 */
Class call(def project_name) {
    def baseline_file = null

    switch (project_name.toLowerCase()) {
        case 'bct': baseline_file = com.ea.project.bct.LibBaseline; break
        case 'bctch1': baseline_file = com.ea.project.bctch1.LibBaseline; break
        case 'fb1-battlefieldgame': baseline_file = com.ea.project.fb1.LibBaseline; break
        case 'kingston': baseline_file = com.ea.project.kin.LibBaseline; break
        case 'merlin': baseline_file = com.ea.project.mer.LibBaseline; break
        case 'nfs': baseline_file = com.ea.project.nfs.LibBaseline; break
        default: error 'ReturnBaseline called with invalid project name: ' + project_name
    }

    return baseline_file
}
