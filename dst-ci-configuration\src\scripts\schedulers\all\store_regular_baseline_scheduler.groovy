package scripts.schedulers.all

import com.ea.lib.LibJenkins
import com.ea.project.GetBranchFile

def project = ProjectClass(env.project_name)

/**
 * store_regular_baseline_scheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Trigger Store Regular Baseline') {
            steps {
                script {
                    def last_successful_code_build = LibJenkins.getLastStableCodeChangelist(env.store_baseline_reference_job)
                    def last_successful_data_build = LibJenkins.getLastStableDataChangelist(env.store_baseline_reference_job)
                    def code_changelist = params.code_changelist ?: last_successful_code_build
                    def data_changelist = params.data_changelist ?: last_successful_data_build

                    if (code_changelist == null || data_changelist == null) {
                        echo 'Missing changelist, aborting build!'
                        currentBuild.result = Result.FAILURE.toString()
                        return
                    }

                    def platform = params.platform

                    def args = [
                        string(name: 'code_changelist', value: code_changelist),
                        string(name: 'data_changelist', value: data_changelist),
                        string(name: 'platform', value: platform),
                    ]

                    def inject_map = [
                        'code_changelist': code_changelist,
                        'data_changelist': data_changelist,
                        'platform'       : platform,
                    ]

                    if (env.combine_target_branch.toBoolean() == true) {
                        def last_successful_combine_code_build = LibJenkins.getEnvironmentForLastStableBuild(env.store_baseline_reference_job)?.combine_code_changelist
                        def last_successful_combine_data_build = LibJenkins.getEnvironmentForLastStableBuild(env.store_baseline_reference_job)?.combine_data_changelist
                        def combine_code_changelist = params.combine_code_changelist ?: last_successful_combine_code_build
                        def combine_data_changelist = params.combine_data_changelist ?: last_successful_combine_data_build

                        inject_map += [
                            'combine_code_changelist': combine_code_changelist,
                            'combine_data_changelist': combine_data_changelist,
                        ]
                    }

                    EnvInject(currentBuild, inject_map)
                    currentBuild.displayName = env.JOB_NAME + '.' + data_changelist + '.' + code_changelist

                    def job_name = env.branch_name + '.store_regular_baseline_builds'
                    def baseline_job = build(job: job_name, parameters: args, propagate: false)
                    currentBuild.result = baseline_job.result.toString()

                    //support for slack notification
                    def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
                    def slack_settings = branchfile.standard_jobs_settings?.slack_channel_baseline
                    SlackMessageNew(currentBuild, slack_settings, project.short_name)

                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
}
