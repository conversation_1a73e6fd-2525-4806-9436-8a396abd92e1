"""
info.py
"""
import json
import click
from elipy2 import config, LOGGER
from elipy2.cli import pass_context


@click.command("info", short_help="Prints out information about ELIPY configuration state.")
@pass_context
def cli(_):
    """
    Dumps information about how ELIPY is currently configured to console.
    Useful for debugging/verifying that ELIPY is loading the config you expect etc.
    """

    config_manager = config.ConfigManager()
    LOGGER.info("Current configuration:")
    LOGGER.info(json.dumps(config_manager.config, indent=4))
