{#
    Command:
        custom_script
            short_help: Runs a script.
            context_settings: dict(ignore_unknown_options=True)

    Arguments:

    Required variables:
        executable
            type: str
            required: True
            help: The executable to run the script
        script_path
            type: str
            required: True
            help: The relative script path from TnT

    Optional variables:
        executable_args
            type: str
            default: ''
            help: Optional arguments to pass to the command. Pass None for no args.
        script_args
            type: str
            default: ''
            help: Optional arguments to pass to the command.
        env_variables
            type: str
            default: ''
            help: Comma seperated env variable to add. Example: VAR1=FOO,VAR2=BAR
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    custom_script
    --executable {{ executable }}
    --script-path {{ script_path }}
    {%- if executable_args %}
    --executable-args {{ executable_args }}
    {%- endif %}
    {%- if script_args %}
    --script-args {{ script_args }}
    {%- endif %}
    {%- if env_variables %}
    --env-variables {{ env_variables }}
    {%- endif %}
  displayName: elipy custom_script
