import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll
import support.TestDataUtil

/**
 * Specification class to validate that the configurations specified in the
 * --additional-configs argument match the configurations in the code_matrix.
 */
class ValidateAdditionalConfigsArgsSpec extends Specification {

    /**
     * Shared test data prepared for all branches.
     */
    @Shared
    List testData = TestDataUtil.prepareTestDataForAllBranches()

    /**
     * Validates that the code_matrix configurations for a branch are not missing
     * any configurations specified in the --additional-configs argument.
     *
     * @param branchName The name of the branch being validated.
     */
    @Unroll
    void '#branchName: "code_matrix" configs is missing a configuration, specified on the --additional-configs "<config>"'() {
        expect:
        def validationResult = validateFrostyPatchMatchesCodeMatrix(branch)
        assert validationResult.isEmpty(): 'Failed configurations:\n' + validationResult.collect {
            "Branch: ${it[0]}, Missing: code_matrix.${it[2]} -> Config: ${it[1]}"
        }.join('\n')
        where:
        branch << testData
        branchName = branch.name
    }

    /**
     * Validates that the frosty patch configurations match the code_matrix configurations.
     *
     * @param branch The branch object containing branch settings and configurations.
     * @return A list of tuples containing branchName, additionalConfig, and platform for failed tests.
     */
    private static List<Tuple> validateFrostyPatchMatchesCodeMatrix(def branch) {
        List<Tuple> failedTests = []
        if (branch.code_matrix) {
            def codeMatrixMap = branch.code_matrix.collectEntries { codeEntry ->
                [codeEntry.name, codeEntry.configs.collect { it instanceof String ? it : it['name'] }]
            }

            [
                branch.frosty_downstream_matrix,
                branch.frosty_for_patch_matrix,
                branch.frosty_matrix,
                branch.patchfrosty_downstream_matrix,
                branch.patchfrosty_matrix,
            ]
                .flatten()
                .each { entry ->
                    def platform = [
                        'win64'      : 'win64game',
                        'server'     : 'win64server',
                        'linuxserver': 'linux64server',
                    ].get(entry.name, entry.name)

                    entry.variants.each { variant ->
                        def additionalConfigs = (variant?.args =~ /--additional-configs\s([a-z]+)/).collect { it[1] }
                        additionalConfigs.each { additionalConfig ->
                            def codeMatrixConfigs = codeMatrixMap[platform] ?: []
                            if (!codeMatrixConfigs.contains(additionalConfig)) {
                                failedTests.add(new Tuple(branch.name, additionalConfig, platform))
                            }
                        }
                    }
                }
        }
        return failedTests
    }
}
