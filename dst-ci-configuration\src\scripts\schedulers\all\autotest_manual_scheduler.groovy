package scripts.schedulers.all

import com.ea.lib.model.JobReference
import com.ea.lib.model.autotest.Name
import com.ea.matrixfiles.AutotestMatrix
import com.ea.matrixfiles.AutotestMatrixFactory
import hudson.model.Result

def project = ProjectClass(env.projectName)
AutotestMatrix autotestMatrix = AutotestMatrixFactory.getInstance(env.autotestMatrix)
Map slackSettings = autotestMatrix.getSlackSettings(env.branchName)
def finalResult = Result.SUCCESS

/**
 * autotest_manual_scheduler.groovy
 */
pipeline {
    agent { label '( scheduler && master ) || executor_agent' }
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Validate changelists') {
            steps {
                script {
                    String dataChangelist = params.data_changelist
                    String codeChangelist = params.code_changelist
                    String clientBuildId = params.client_build_id
                    String serverBuildId = params.server_build_id
                    if (!dataChangelist || !codeChangelist) {
                        echo 'No changelist to test could be found.'
                        currentBuild.displayName = env.JOB_NAME + '.no-changelists-to-run'
                        currentBuild.rawBuild.executor.interrupt(Result.UNSTABLE)
                        // Interrupt doesn't await
                        sleep(5) // jenkinsfile use second as unit for sleep
                    }
                    currentBuild.displayName = env.JOB_NAME + '.' + dataChangelist + '.' + codeChangelist
                    echo 'Running tests on data CL: ' + dataChangelist + '.'
                    echo 'Running tests on code CL: ' + codeChangelist + '.'
                    echo 'Running tests with client build id: ' + clientBuildId
                    echo 'Running tests with server build id: ' + serverBuildId
                    Map target_build_info = [
                        (Name.ANY.toString()): [
                            dataChangelist: dataChangelist,
                            codeChangelist: codeChangelist,
                            clientBuildId : clientBuildId,
                            serverBuildId : serverBuildId,
                        ]
                    ]
                    def injectMap = [
                        target_build_info: target_build_info.inspect(),
                    ]
                    EnvInject(currentBuild, injectMap)
                }
            }
        }
        stage('Trigger manual autotest jobs') {
            steps {
                script {
                    List<JobReference> jobReferences = []
                    finalResult = finalResult.combine(retryOnFailureCause(3, jobReferences) {
                        Map target_build_info = (Map) Eval.me(env.target_build_info)
                        Map manualTestCategoriesSettings = autotestMatrix.getManualTestCategoriesSetting(env.branchName)
                        finalResult = AutotestRunCategoryParallel(manualTestCategoriesSettings, env.branchName,
                            env.autotestMatrix, target_build_info, jobReferences)
                    })
                }
            }
        }
    }
    post {
        cleanup {
            AutotestSetPipelineResult(currentBuild, finalResult)
            SlackMessageNew(currentBuild, slackSettings, project.short_name)
        }
    }
}
