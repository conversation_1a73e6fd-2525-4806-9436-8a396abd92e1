import hudson.model.TaskListener
import hudson.model.User
import hudson.tasks.MailAddressResolver

import jakarta.mail.Message

def args = run.getEnvironment(TaskListener.NULL)
def preflighter = null
def changelist = null
if (args.preflighter) preflighter = args.preflighter;
if (args.unshelve_changelist) changelist = args.unshelve_changelist;

def user = User.getById(preflighter, true);
if (user != null) {
    def userEmailAddress = MailAddressResolver.resolve(user);
    if (userEmailAddress != null) {
        msg.setRecipients(Message.RecipientType.TO, userEmailAddress);
        //  msg.setRecipients(Message.RecipientType.CC, "<EMAIL>");
    }
}

msg.setSubject('Preflighting CL ' + changelist + ' finished with ' + run.getResult() + ', ' + args.BUILD_URL)
