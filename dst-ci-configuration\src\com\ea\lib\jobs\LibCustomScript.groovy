package com.ea.lib.jobs

import com.ea.lib.LibJobDsl
import com.ea.lib.jobsettings.CustomScriptSettings
import com.ea.lib.jobsettings.JobSetting
import com.ea.lib.model.branchsettings.CustomScriptConfiguration

class Lib<PERSON>ustomScript {
    final static CustomScriptConfiguration GENERATE_SYNC_BUILD_CACHE = new CustomScriptConfiguration(
        scriptPath: 'Setup\\Drone\\GenerateSyncBuildCache.bat',
        executable: 'cmd.exe',
        executableArgs: '/c',
        jobName: 'generate-sync-build-cache',
    )

    final static CustomScriptConfiguration PORTAL_MAKE_SDK = new CustomScriptConfiguration(
        scriptPath: 'Code\\DICE\\BattlefieldGame\\Scripts\\portal_make_sdk.py',
        executable: 'D:\\dev\\Python\\virtual\\Scripts\\python.exe',
        jobName: 'portal-make-sdk',
        defaultScriptArgs: './code/sdk/distributed.txt \\\\filer.dice.ad.ea.com\\Builds\\Tools\\mod-level-tools\\out --clean_output --remote_repo \\\\dice-la.la.ad.ea.com\\Projects\\Granite\\PortalNext\\mod-level-tools'
    )

    /**
     * Start build custom script job
     */
    static void start(def job, def project, def branchFile, def masterFile, String branchName, String settingKey) {
        def settings = new CustomScriptSettings()
        settings.initializeStart(branchFile, masterFile, project, branchName, settingKey)
        job.with {
            description(settings.description)
            environmentVariables {
                env('BRANCH_NAME', settings.branchName)
                env('CODE_BRANCH', settings.codeBranch)
                env('CODE_FOLDER', settings.codeFolder)
                env('PROJECT_NAME', settings.projectName)
            }
            disabled(false)
            logRotator(7, 100)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('script_args')
                    defaultValue(settings.defaultScriptArgs)
                    description(settings.argumentDescription)
                    trim(true)
                }
            }
            properties {
                disableConcurrentBuilds()
                disableResume()
                if (settings.cronTrigger) {
                    pipelineTriggers {
                        triggers {
                            cron {
                                spec(settings.cronTrigger)
                            }
                        }
                    }
                }
            }
            quietPeriod(0)
        }
    }

    static void job(def job, def project, def branchFile, def masterFile, String branchName, String settingKey, String p4Port) {
        JobSetting settings = new CustomScriptSettings()
        settings.initializeJob(branchFile, masterFile, project, branchName, settingKey)
        job.with {
            description(settings.description)
            environmentVariables {
                env('BRANCH_NAME', settings.branchName)
                env('CODE_BRANCH', settings.codeBranch)
                env('CODE_FOLDER', settings.codeFolder)
                env('PROJECT_NAME', settings.projectName)
                env('P4PORT', p4Port)
            }
            label(settings.jobLabel)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(settings.workspaceRoot)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                stringParam {
                    name('script_args')
                    defaultValue(settings.defaultScriptArgs)
                    description(settings.argumentDescription)
                    trim(true)
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName(settings.buildName)
                timeout {
                    absolute(settings.timeoutMinutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
            }
            steps {
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                batchFile(settings.elipyCmd)
            }
        }
    }
}
