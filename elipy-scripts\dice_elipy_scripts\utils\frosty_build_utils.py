"""
frosty_build_utils.py

Utility module
"""
import dicttoxml
import json
import logging
import os
import re
from deprecated import deprecated
from winregistry import WinRegistry as Reg
from xml.dom.minidom import parseString
from elipy2 import core, frostbite_core, local_paths, LOGGER
from elipy2.frostbite import package_utils, sdk_utils
from elipy2.telemetry import collect_metrics

dicttoxml.LOG.setLevel(logging.ERROR)


@collect_metrics()
def patch_eainstaller_signtool(password=None, user=None, domain_user=None):
    """
    Helper function to patch ea installer signtool since
    it looks in the wrong location for the exe.

    TODO: make a MR to update where EAInstaller looks for signtool
    """
    if password is not None and user is not None:
        sdk_utils.authenticate_eapm_credstore(password, user, domain_user)

    if frostbite_core.minimum_fb_version(year=2021, version_nr=2):
        LOGGER.info("Making sure EAInstaller is available")
        package_utils.package_download(package="EAInstaller")
        package_utils.package_download(package="OOA")

    LOGGER.info("Patching EAInstaller signtool")
    win_kit_root = None
    reg = Reg()
    path = "HKLM\\SOFTWARE\\Microsoft\\Windows Kits\\Installed Roots"
    try:
        win_kit_root = os.path.join(reg.read_entry(path, "KitsRoot10").value, "bin")
    except FileNotFoundError:
        LOGGER.warning(
            "Could not find Windows Kits Root",
            exc_info=True,
        )

    if win_kit_root is not None:
        installed_sdks = reg.read_key(path).reg_keys
        for installed_sdk in installed_sdks:
            signtool = "signtool.exe"
            sdk_path = os.path.join(win_kit_root, installed_sdk, "x64")
            sdk_patch_path = os.path.join(win_kit_root, "x64")

            signtool_path = os.path.join(sdk_path, signtool)
            signtool_patch_path = os.path.join(sdk_patch_path, signtool)

            if os.path.exists(signtool_path) and not os.path.exists(signtool_patch_path):
                core.robocopy(sdk_path, sdk_patch_path, extra_args=[os.path.basename(signtool)])
                LOGGER.info("Patching EAInstaller signtool successful")
                break


@collect_metrics()
@deprecated(version="10.1", reason="Use the function in elipy2.frostbite.sdk_utils instead.")
def install_required_sdks(
    password=None, user=None, domain_user=None, platform=None, use_shift_build=False
):
    """
    Install the required SDKs based on the values passed.
    Deprecated, just used as a wrapper around the function in sdk_utils.
    Will be removed later.
    """
    sdk_utils.install_required_sdks(
        password=password,
        user=user,
        domain_user=domain_user,
        platform=platform,
        use_shift_build=use_shift_build,
    )


@collect_metrics()
@deprecated(version="10.1", reason="Use the function in elipy2.frostbite.sdk_utils instead.")
def install_sdks(password=None, user=None, domain_user=None, platform=None):
    """
    Installs needed sdks and authenticates to package server.
    Deprecated, just used as a wrapper around the function in sdk_utils.
    Will be removed later.
    """
    sdk_utils.install_sdks_elipy(
        password=password, user=user, domain_user=domain_user, platform=platform
    )


@collect_metrics()
@deprecated(version="10.1", reason="Use the function in elipy2.frostbite.sdk_utils instead.")
def authenticate_eapm_credstore(password, user, domain_user=None):
    """
    Authenticate to package server.
    Deprecated, just used as a wrapper around the function in sdk_utils.
    Will be removed later.
    """
    sdk_utils.authenticate_eapm_credstore(password=password, user=user, domain_user=domain_user)


def generate_buildlayout_xml(buildlayout_root):
    """
    Create an xml file from the json buildlayout file.
    """
    layout_files = [
        item for item in os.listdir(buildlayout_root) if re.match(r".*\.buildlayout$", item)
    ]

    # Generate xml file from buildlayout file
    for layout_file in layout_files:
        xml_buildlayout_name = layout_file + ".xml"

        json_path = os.path.join(buildlayout_root, layout_file)
        xml_path = os.path.join(buildlayout_root, xml_buildlayout_name)

        if os.path.exists(json_path):
            with open(json_path, "r") as data_file:
                json_data = data_file.read()

            buildlayout = dict(json.loads(json_data))
            xml = dicttoxml.dicttoxml(buildlayout, attr_type=False, custom_root="buildlayout")
            dom = parseString(xml)
            xml_data = dom.toprettyxml()

            with open(xml_path, "w+") as data_file:
                data_file.write(xml_data)
        else:
            LOGGER.warning("Cannot generate buildlayout xml, cannot find {}".format(json_path))


def add_frosty_log_to_output():
    """
    Copy the frosty log to the frosty output directory.
    """
    try:
        frosty_iso_package = package_utils.find_package("FrostyIsoTool")
        frosty_iso_package_path = frosty_iso_package["FrostyIsoTool"]["path"]
        log_file_path = os.path.join(frosty_iso_package_path, "bin", "FrostyLogFile.txt")

        dest = local_paths.get_local_frosty_path()

        core.robocopy(
            os.path.dirname(log_file_path),
            dest,
            extra_args=[os.path.basename(log_file_path)],
        )
    except Exception as exce:
        LOGGER.debug(exce)
        LOGGER.warning("Failed to copy log file to the output directory.")


def add_files_to_frosty_output(platform, package_type, config):
    """
    Copy additional files to the frosty output directory.
    This is just until we can find a way to do this via the frosty config.
    """
    try:
        if platform.lower() == "linuxserver" and package_type.lower() == "digital":
            source_path = os.path.join(local_paths.get_local_build_path(platform, config), config)
            file = f"BattlefieldGame.Main_Linux64_{config}_Server.dbg"
            core.robocopy(
                os.path.dirname(source_path),
                local_paths.get_local_frosty_path(),
                extra_args=[file],
            )
    except Exception as exce:
        LOGGER.debug(exce)
        LOGGER.warning("Failed to copy additional files to the output directory.")


def fix_data_name_casing():
    """
    There are some runtime assumptions about the casing of the data directory for ps4:
    https://electronic-arts.slack.com/archives/G01BE8EMER2/p1618997781297800
    """
    bad_data_dir = os.path.join(local_paths.get_local_frosty_path(), "data")
    good_data_dir = os.path.join(local_paths.get_local_frosty_path(), "Data")

    if os.path.isdir(bad_data_dir):
        os.rename(bad_data_dir, good_data_dir)
