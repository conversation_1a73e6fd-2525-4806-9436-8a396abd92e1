package com.ea.matrixfiles

import com.ea.lib.model.autotest.AutotestCategory
import com.ea.lib.model.autotest.Platform

/**
 * See <a href='https://docs.google.com/document/d/1pTxwXhm0T5Mstbg4uaBkjWruC5ntz7pZxqGsmBEcTwk/preview'>Flow Overview</a>
 * See <a href='https://docs.google.com/document/d/1mTfOtPPX93529M7EgmmaGBpmcuMoDyhZ7ZlYCKoVUzw/preview'>Jenkins Config</a>
 **/
class CriterionAutotestMatrix extends AutotestMatrix {

    @Override
    List<String> getBranches() {
        return []
    }

    @Override
    List<AutotestCategory> getTestCategories() {
        return []
    }

    @Override
    List<AutotestCategory> getManualTestCategories() {
        return []
    }

    @Override
    Map<String, List<Platform>> getPlatforms() {
        return [:]
    }

    @Override
    boolean shouldLevelsRunInParallel(String branchName) {
        return false
    }

    @Override
    Map getSlackSettings(String branchName) {
        return [:]
    }

    @Override
    Map getManualTestCategoriesSetting(String branchName) {
        Map settings = [
            parallel_limit: 1,
            categories    : getManualTestCategories(branchName),
        ]
        return settings
    }

}
