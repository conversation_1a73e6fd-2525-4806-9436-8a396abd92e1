# Duplicated settings lower in the settings list will get higher priority
milestone: ""
distribution_type: "InternalOnly"
retention_policy: "SpaceAvailable"
priority: "3"
version: "2.0"
release:
  buildtype: "Release BranchRelease"
final:
  buildtype: "Final BranchRelease"
retail:
  buildtype: "Ship BranchRelease"
dev-cu1:
  release:
    buildtype: "Release BranchDev"
  final:
    buildtype: "Final BranchDev"
  retail:
    buildtype: "Ship BranchDev"
dev-cu2:
  release:
    buildtype: "Release BranchDev"
  final:
    buildtype: "Final BranchDev"
  retail:
    buildtype: "Ship BranchDev"
dev-cu3:
  release:
    buildtype: "Release BranchDev"
  final:
    buildtype: "Final BranchDev"
  retail:
    buildtype: "Ship BranchDev"
hashtag:
  release:
    buildtype: "Release BranchMain"
  final:
    buildtype: "Final BranchMain"
  retail:
    buildtype: "Ship BranchMain"
content:
  files:
    file_names:
      - '*'
    supplemental_files:
      - ""
xb1:
  digital:
    file_names:
      - "GU1_*zwks512sysnyr"
    supplemental_files:
      - "*.ekb"
      - "*.pdb"
      - "layout.xml"
      - "Validator_*.xml"
      - "appxmanifest.xml"
      - "*.xml"
      - "*.log"
      - "*.pdb"
      - "*.csv"
      - "*.map"
      - "*GU1*.zip"
      - "*.json"
      - "*.txt"
  patch:
    file_names:
      - "GU1_*zwks512sysnyr"
    supplemental_files:
      - "*.ekb"
      - "*.pdb"
      - "layout.xml"
      - "Validator_*.xml"
      - "appxmanifest.xml"
      - "*.xml"
      - "*.log"
      - "*.pdb"
      - "*.csv"
      - "*.map"
      - "*GU1*.zip"
      - "*.json"
      - "*.txt"
    directory:
      - ""
  settings:
    rel-gu1:
      final:
        digital:
          sku_id: "78b88af4-cb8b-409c-b054-0299996a68e4"
          sku_name: "FG - WW (rel-gu1 XB1 Digital Final DTE)"
        patch:
          sku_id: "b267fe71-e282-4b5b-98e5-d4e73546a86e"
          sku_name: "FG - WW (rel-gu1 XB1 Patch Final DTE)"
        files:
          sku_id: "669fbab3-062a-430b-a7a3-1da09c9fb791"
          sku_name: "FG - WW (rel-gu1 XB1 Loose Files Final DTE)"
      retail:
        digital:
          sku_id: "befbf6b8-530d-4795-a9fe-9f07f88d6a16"
          sku_name: "FG - WW (rel-gu1 XB1 Digital Retail DTE)"
        patch:
          sku_id: "90a47ec1-29ba-45ca-aa41-ed220f579c14"
          sku_name: "FG - WW (rel-gu1 XB1 Patch Retail DTE)"
ps4:
  content:
    digital:
      file_names:
        - "*pkg"
      supplemental_files:
        - "*.gp4"
        - "*-submission_materials.zip"
        - "*.json"
        - "*.txt"
        - "*.log"
        - "*.csv"
        - "*.xml"
    patch:
      file_names:
        - "*pkg"
      supplemental_files:
        - "*.gp4"
        - "*-submission_materials.zip"
        - "*.json"
        - "*.txt"
        - "*.log"
        - "*.csv"
        - "*.xml"
    directory:
      - ""
  settings:
    rel-gu1:
      final:
        files:
          sku_id: "6c0aa522-757c-4387-8817-554316b5f978"
          sku_name: "FG - EU (rel-gu1 PS4 Loose Files Final DTE)"
        digital:
          sku_id: "d844954d-fd4c-4727-9913-ca5c2795d536"
          sku_name: "FG - EU (rel-gu1 PS4 Digital Final DTE)"
        patch:
          sku_id: "34d2d5ac-4e1f-4281-bcdc-505fc93589f4"
          sku_name: "Patch - EU (rel-gu1 PS4 Patch Final DTE)"
      retail:
        digital:
          sku_id: "e3564ae3-c9ec-4b41-95c3-60eeb1733ede"
          sku_name: "FG - EU (rel-gu1 PS4 Digital Retail DTE)"
        patch:
          sku_id: "999dafef-bc72-4342-a189-1c6e2c8d72fb"
          sku_name: "Patch - EU (rel-gu1 PS4 Patch Retail DTE)"

server:
  content:
    supplemental_files:
      - ""
    directory:
      - ""
  settings:
    rel-gu1:
      final:
        digital:
          sku_id: "afe99031-01ca-466b-bf5e-88e672abbacc"
          sku_name: "Server - WW (rel-gu1 Win32 Digital Final DTE)"
        files:
          sku_id: "afe99031-01ca-466b-bf5e-88e672abbacc"
          sku_name: "Server - WW (rel-gu1 Win32 Loose Files Final DTE)"

win64:
  content:
    digital:
      file_names:
        - "gu1.zip"
      supplemental_files:
        - "*.pdb"
        - "*.yaml"
        - "*.exe"
        - "*.csv"
        - "*.json"
        - "*.txt"
        - "*.log"
    patch:
      file_names:
        - "gu1.zip"
      supplemental_files:
        - "*.pdb"
        - "*.yaml"
        - "*.exe"
        - "*.csv"
        - "*.json"
        - "*.txt"
        - "*.log"
    directory:
      - ""
  settings:
    rel-gu1:
      final:
        digital:
          sku_id: "92a5dc52-2fb7-44ff-a790-534a748aa7a9"
          sku_name: "FG - WW(rel-gu1 win32 Digital Final DTE)"
        patch:
          sku_id: "3a2e2434-198e-4a0b-a207-fa3ca1de3423"
          sku_name: "FG - WW(rel-gu1 win32 Patch Final DTE)"
        files:
          sku_id: "4f41e90f-cc05-4b36-a492-0219ffc34dea"
          sku_name: "FG - WW (rel-gu1 win32 Files Final DTE)"
      retail:
        digital:
          sku_id: "a45ae637-54c2-4570-9cd8-7876074997a6"
          sku_name: "FG - WW (rel-gu1 win32 Digital Retail DTE)"
        patch:
          sku_id: "4fed0a08-a107-4be5-b7e7-84f466b7328d"
          sku_name: "FG - WW (rel-gu1 win32 Patch Retail DTE)"

linuxserver:
  content:
    files:
      file_names:
        - '*'
      supplemental_files:
        - ""
    digital:
      file_names:
        - "Roboto_Linux_Server_*_Binaries.zip"
      supplemental_files:
        - "build.json"
  settings:
    rel-gu1:
      final:
        digital:
          sku_id: "d1696a12-21ae-4936-9c0a-1c5af20ac6b5"
          sku_name: "Server - WW (rel-gu1 LinuxServer Digital Final DTE)"
        files:
          sku_id: "680dd0da-96d8-4005-8572-42c463da2e6d"
          sku_name: "Server - WW (rel-gu1 LinuxServer Loose Files Final DTE)"
      release:
        digital:
          sku_id: "5a2cdce7-fe70-4a64-b61d-6caaca60596c"
          sku_name: "Server - WW (rel-gu1 LinuxServer Digital Release DTE)"
