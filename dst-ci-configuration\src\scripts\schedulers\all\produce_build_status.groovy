package scripts.schedulers.all

import com.ea.project.GetBranchFile

def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
def settings_map = branchfile.general_settings + branchfile.standard_jobs_settings
def project = ProjectClass(env.project_name)

/**
 * produce_build_status.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Get changelist from Perforce') {
            steps {
                script {
                    def ignore_paths = branchfile.general_settings?.ignore_paths_code_preview ?: []
                    P4PreviewCode(project, 'stream', env.code_folder, env.code_branch, env.non_virtual_code_folder, env.non_virtual_code_branch, ignore_paths, [], settings_map)
                }
            }
        }
        stage('Trigger job') {
            steps {
                script {
                    currentBuild.displayName = env.JOB_NAME + '.' + env.BUILD_NUMBER

                    def job_name = env.branch_name + '.produce-build-status'
                    def build_status_job = build(job: job_name, parameters: [], propagate: false)
                    currentBuild.result = build_status_job.result.toString()

                    def slack_settings = branchfile.standard_jobs_settings?.slack_channel_produce_build_status
                    SlackMessageNew(currentBuild, slack_settings, project.short_name)

                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
}
