"""
test_trigger_docker_image_gitlab_pipeline.py

Unit testing for test_trigger_docker_image_gitlab_pipeline
"""
from unittest.mock import patch, MagicMock
from click.testing import CliRunner
import requests

from dice_elipy_scripts.trigger_docker_image_gitlab_pipeline import cli
from elipy2.exceptions import ELIPYException


@patch("dice_elipy_scripts.trigger_docker_image_gitlab_pipeline.add_sentry_tags", MagicMock())
@patch("dice_elipy_scripts.trigger_docker_image_gitlab_pipeline.LOGGER")
class TestTriggerGitLabPipeline:
    @patch("requests.post")
    def test_trigger_pipeline_success(self, mock_post, mock_logger):
        mock_post.return_value.status_code = 201
        mock_post.return_value.json.return_value = {"web_url": "http://example.com/pipeline/123"}

        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                "--platform",
                "server",
                "--code-changelist",
                "12345",
                "--data-changelist",
                "12345",
                "--code-branch",
                "master",
                "--data-branch",
                "data-feature",
            ],
        )

        assert result.exit_code == 0
        mock_logger.info.assert_called_with(
            "Pipeline triggered successfully! Pipeline URL: %s", "http://example.com/pipeline/123"
        )

    @patch("requests.post")
    def test_trigger_pipeline_failure(self, mock_post, mock_logger):
        mock_post.return_value.status_code = 401
        mock_post.return_value.text = "Unauthorized access"

        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                "--platform",
                "server",
                "--code-changelist",
                "12345",
                "--data-changelist",
                "12345",
                "--code-branch",
                "master",
                "--data-branch",
                "data-feature",
            ],
        )

        assert result.exit_code == 1
        assert isinstance(result.exception, ELIPYException)
        assert (
            str(result.exception) == f"Failed to trigger pipeline: HTTP 401 - Unauthorized access"
        )

    @patch("requests.post")
    def test_trigger_pipeline_timeout(self, mock_post, mock_logger):
        mock_post.side_effect = requests.exceptions.Timeout

        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                "--platform",
                "server",
                "--code-changelist",
                "12345",
                "--data-changelist",
                "12345",
                "--code-branch",
                "master",
                "--data-branch",
                "data-feature",
            ],
        )

        assert result.exit_code == 1
        assert isinstance(result.exception, ELIPYException)
        assert str(result.exception) == "The request timed out. Please try again later."
