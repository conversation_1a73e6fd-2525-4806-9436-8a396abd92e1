package scripts.schedulers.all

import com.ea.lib.LibJenkins
import com.ea.lib.model.JobReference
import com.ea.project.GetBranchFile

def branchFile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
def branchInfo = branchFile.general_settings + branchFile.standard_jobs_settings

/**
 * CustomScriptScheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Trigger custom script job') {
            steps {
                script {
                    List<JobReference> jobReferences = []
                    retryOnFailureCause(3, jobReferences) {
                        def scriptArgs = params.script_args

                        def args = [
                            string(name: 'script_args', value: scriptArgs),
                        ]
                        Map jobs = [:]
                        branchInfo.custom_script.each { String scriptJobName, configuration ->
                            jobs[scriptJobName] = {
                                String jobName = "${env.BRANCH_NAME}.custom-script.${scriptJobName}"
                                def downstreamJob = build(job: jobName, parameters: args, propagate: false)
                                jobReferences << new JobReference(downstreamJob: downstreamJob, jobName: jobName, parameters: args, propagate: false)
                                LibJenkins.printRunningJobs(this)
                            }
                        }
                        parallel(jobs)
                    }
                }
            }
        }
    }
}

String toString() {
    return 'scripts.schedulers.all.CustomScriptScheduler'
}
