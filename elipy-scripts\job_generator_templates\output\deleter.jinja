{#
    Command:
        deleter

    Arguments:

    Required variables:

    Optional variables:
        empty_folders
            is_flag: True
            help: Crawl project network path for empty folders and delete them.
            default: False
            required: False
        dry_run
            is_flag: True
            help: Print the delete list without modifying it.
            required: False
        no_shift_delete
            is_flag: True
            help: Do not delete shift entries.
            required: False
        avalanche_host
            help: Host e.g. cg-buildrepo.eu.ad.ea.com
            required: False
        clean_record
            is_flag: True
            help: Clean db records.
            required: False
        use_onefs_api
            help: Delete using onefs_api method
            is_flag: True
            required: False
        include_path_retention
            help: Do not perform cleanup on the paths in the path_retention settings
            is_flag: True
            required: False
        include
            help: Include only these categories. By default all categories are included
            multiple: True
        exclude
            help: Exclude these categories. Excludes take precedence over includes
            multiple: True
        clean_symstore_days
            default: None
            help: Clean up symstores files older than days
        creation_time_deletion
            default: False
            is_flag: True
            required: False
            help: Clean up build dirs based on creation time
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    deleter
    {%- if empty_folders %}
    --empty-folders {{ empty_folders }}
    {%- endif %}
    {%- if dry_run %}
    --dry-run {{ dry_run }}
    {%- endif %}
    {%- if no_shift_delete %}
    --no-shift-delete {{ no_shift_delete }}
    {%- endif %}
    {%- if avalanche_host %}
    --avalanche-host {{ avalanche_host }}
    {%- endif %}
    {%- if clean_record %}
    --clean-record {{ clean_record }}
    {%- endif %}
    {%- if use_onefs_api %}
    --use-onefs-api {{ use_onefs_api }}
    {%- endif %}
    {%- if include_path_retention %}
    --include-path-retention {{ include_path_retention }}
    {%- endif %}
    {%- if include %}
    --include {{ include }}
    {%- endif %}
    {%- if exclude %}
    --exclude {{ exclude }}
    {%- endif %}
    {%- if clean_symstore_days %}
    --clean-symstore-days {{ clean_symstore_days }}
    {%- endif %}
    {%- if creation_time_deletion %}
    --creation-time-deletion {{ creation_time_deletion }}
    {%- endif %}
  displayName: elipy deleter
