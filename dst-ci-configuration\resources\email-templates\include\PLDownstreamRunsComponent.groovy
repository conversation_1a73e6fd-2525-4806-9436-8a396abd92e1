import groovy.xml.MarkupBuilder
import hudson.model.Result
import hudson.model.Run

public class PLDownstreamRunsComponent implements IEmailComponent {

    private Map gather(Run run) {
        def downstreamRunList = JobUtil.getDownstreamRuns(run)

        def runEntryList = []
        downstreamRunList.each { downstreamRun ->
            def displayName = downstreamRun.getProject().getName()
            def duration = downstreamRun.getDurationString()
            def result = downstreamRun.getResult()
            if (!result) {
                def rawBuild = downstreamRun.getRawBuild()
                result = (rawBuild && rawBuild.isBuilding()) ? "IN_PROGRESS" : Result.NOT_BUILT.toString()
            }
            def url = JobUtil.getClassicDisplayURL(downstreamRun)
            def baseline = false

            def entry = [
                Baseline   : baseline,
                DisplayName: displayName,
                Duration   : duration,
                Result     : result,
                Url        : url,
            ]
            runEntryList.add(entry)
        }

        def data = [:]
        if (runEntryList) {
            data.Entries = runEntryList
        }
        return data
    }


    private static String getSectionClass(Map data) {
        return "section"
    }


    public void render(Run run, MarkupBuilder builder) {
        def data = gather(run)
        if (data) {
            builder.tr {
                td(class: getSectionClass(data), align: "center") {
                    mkp.yieldUnescaped("<!-- DOWNSTREAM RUNS -->")
                    table(border: "0", cellpadding: "0", cellspacing: "0", width: "100%", style: "max-width: 500px;", class: "responsive-table") {
                        tr {
                            td(class: "section-title") {
                                mkp.yield("Downstream Jobs")
                            }
                        }
                    }
                    table(border: "0", cellpadding: "0", cellspacing: "0", width: "100%", style: "max-width: 500px;", class: "responsive-table") {
                        tr(class: "run-header") {
                            // result
                            td(class: "run-header-status")

                            // job name
                            td(class: "run-header-name") {
                                mkp.yield("JOB")
                            }

                            // duration
                            td(class: "run-header-duration") {
                                mkp.yield("DURATION")
                            }
                        }

                        // list each run as a new table row
                        data.Entries.each { runEntry ->
                            tr(class: "run-entry") {

                                // result colored cell
                                def result = runEntry.Result.toString().toLowerCase()
                                td(class: "run-entry-status-${result}")

                                def displayName = runEntry.DisplayName
                                if (runEntry.Baseline) {
                                    displayName = "${displayName} [Baseline]"
                                }

                                def url = runEntry.Url
                                td(class: "run-entry-name") {
                                    if (url) {
                                        a(href: url, class: "run-entry-name") {
                                            mkp.yield(displayName)
                                        }
                                    } else {
                                        mkp.yield(displayName)
                                    }
                                }

                                td(class: "run-entry-duration") {
                                    mkp.yield(runEntry.Duration)
                                }
                            }
                        }
                    }
                }
            }
        }
    }


    public boolean isApplicable(Run run) {
        def downstreamRunList = JobUtil.getDownstreamRuns(run)
        return !downstreamRunList.isEmpty()
    }


    public String getEmbeddedStyle(Run run) {
        return """
                    /* DOWNSTREAM RUN SPECIFIC STYLES */

                    tr.run-header {
                        border: 0px solid #C8C8C8;
                        color: #9A9fA8;
                        font-family: "LatoLatinWeb", "Lato", "Helvetica Neue", Helvetica, Arial, sans-serif;
                        font-size: 12px;
                        font-weight: bold;
                        text-align: left;
                    }
                    td.run-header-status {
                        border-top: 1px solid #C8C8C8;
                        border-bottom: 1px solid #C8C8C8;
                        border-left: 1px solid #C8C8C8;
                        padding: 10px;
                    }
                    td.run-header-name {
                        border-top: 1px solid #C8C8C8;
                        border-bottom: 1px solid #C8C8C8;
                        padding: 10px;
                    }
                    td.run-header-duration {
                        border-top: 1px solid #C8C8C8;
                        border-right: 1px solid #C8C8C8;
                        border-bottom: 1px solid #C8C8C8;
                        padding: 10px;
                    }
                    tr.run-entry {
                        border: 0px solid #C8C8C8;
                        color: #4A4A4A;
                        font-family: "LatoLatinWeb", "Lato", "Helvetica Neue", Helvetica, Arial, sans-serif;
                        font-size: 12px;
                        text-align: left;
                    }
                    td.run-entry-name {
                        border-top: 1px solid #C8C8C8;
                        border-bottom: 1px solid #C8C8C8;
                        border-left: 1px solid #C8C8C8;
                        font-weight: bold;
                        padding: 10px;
                    }
                    a.run-entry-name {
                        color: #4A90E2;
                        font-family: "LatoLatinWeb", "Lato", "Helvetica Neue", Helvetica, Arial, sans-serif;
                        font-size: 12px;
                        font-weight: bold;
                        text-decoration: none;
                    }
                    td.run-entry-duration {
                        border-top: 1px solid #C8C8C8;
                        border-right: 1px solid #C8C8C8;
                        border-bottom: 1px solid #C8C8C8;
                        font-weight: normal;
                        padding: 10px;
                    }
                    td.run-entry-status-in_progress {
                        background-color: #3A6FB0;
                        border: 1px solid #C8C8C8;
                        width: 30px;
                    }
                    td.run-entry-status-success {
                        background-color: #77B037;
                        border: 1px solid #C8C8C8;
                        width: 30px;
                    }
                    td.run-entry-status-unstable {
                        background-color: #F6B44B;
                        border: 1px solid #C8C8C8;
                        width: 30px;
                    }
                    td.run-entry-status-failure {
                        background-color: #D64B53;
                        border: 1px solid #C8C8C8;
                        width: 30px;
                    }
                    td.run-entry-status-aborted {
                        background-color: #949393;
                        border: 1px solid #C8C8C8;
                        width: 30px;
                    }
                    td.run-entry-status-not_built {
                        background-color: #FFFFFF;
                        border: 1px solid #C8C8C8;
                        width: 30px;
                    }
        """
    }
}
