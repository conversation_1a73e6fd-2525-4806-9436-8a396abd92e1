import com.ea.lib.jobsettings.StatusSettings
import spock.lang.Specification

class StatusSettingsSpec extends Specification {
    class BranchFile {
        static Map standard_jobs_settings = [
            workspace_root                    : 'workspace-root',
            disable_build_status              : true,
            timeout_hours_produce_build_status: 8,
            job_label_statebuild              : 'build-release && util',
        ]
        static Map general_settings = [:]
    }

    class MasterFile {
        static Map branches = [branch: [
            data_branch            : 'data-branch',
            code_branch            : 'code-branch',
            code_folder            : 'game',
            non_virtual_code_branch: 'non-virtual-code-branch',
            non_virtual_code_folder: 'non-virtual-code-folder',
        ]]
    }

    class ProjectFile {
        static String name = 'Kingston'
    }

    void "test that we get expected job settings in initializeProduceBuildStatusStart"() {
        when:
        StatusSettings statusSettings = new StatusSettings()
        statusSettings.initializeProduceBuildStatusStart(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(statusSettings) {
            description == 'Start job for produce build status job'
            isDisabled == BranchFile.standard_jobs_settings.disable_build_status
            nonVirtualCodeBranch == 'non-virtual-code-branch'
            nonVirtualCodeFolder == 'non-virtual-code-folder'
            branchName == 'branch'
            codeBranch == 'code-branch'
            codeFolder == 'game'
            projectName == ProjectFile.name
        }
    }

    void "test that we get expected job settings in initializeProduceBuildStatusJob"() {
        when:
        StatusSettings statusSettings = new StatusSettings()
        statusSettings.initializeProduceBuildStatusJob(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(statusSettings) {
            description == 'Job for producing build status'
            jobLabel == BranchFile.standard_jobs_settings.job_label_statebuild
            timeoutMinutes == BranchFile.standard_jobs_settings.timeout_hours_produce_build_status * 60
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${BUILD_NUMBER}'
            batchScript == "tnt\\bin\\fbcli\\cli.bat x64 && ${workspaceRoot}\\TnT\\Code\\DICE\\Tools\\produceBuildStatus.bat"
        }
    }
}
