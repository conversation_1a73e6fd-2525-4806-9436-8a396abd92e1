"""
bake_flux.py
"""
# pylint: disable=duplicate-code

import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import core, exceptions, filer, LOGGER, os, p4, frostbite_core
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics
from elipy2.exceptions import BakeFluxException


@click.command(
    "bake_flux",
    short_help="Calculate irradiance using Flux and store it into static databases.",
)
@click.option(
    "--level",
    help="Level(s) to use. Repeatable option.",
    multiple=True,
    required=True,
    type=str,
)
@click.option(
    "--variation",
    help="Variation(s) to use. Repeatable option.",
    multiple=True,
    required=True,
    type=str,
)
@click.option(
    "--proxyterrain",
    help="Proxyterrain to use.",
    required=False,
    default=False,
    type=bool,
)
@click.option(
    "--zone",
    help="Zone(s) to build. Repeatable option.",
    required=False,
    default="",
    multiple=True,
    type=str,
)
@click.option("--code-branch", required=True, help="Perforce branch/stream name.", type=str)
@click.option("--code-changelist", required=True, help="Perforce changelist number.", type=str)
@click.option("--dry-run", is_flag=True, help="Don't submit the bake.")
@click.option("--script-path", required=True, help="", type=str)
@click.option("--p4-port", required=True)
@click.option("--p4-client", required=True)
@click.option("--user", default=None, help="Perforce user name.")
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
# pylint: disable=too-many-locals
def cli(
    _,
    level,
    variation,
    proxyterrain,
    zone,
    code_branch,
    code_changelist,
    dry_run,
    script_path,
    p4_port,
    p4_client,
    user,
):
    """
    Calculate irradiance using Flux and store it into static databases.
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    # Fetch pipeline binary
    _filer = filer.FilerUtils()
    _filer.fetch_code(code_branch, code_changelist, "pipeline", "release")

    try:
        # This is horrible. We need to find a better way around this,
        # but right now this causes our beta builds to fail.
        # print os.path.join(frostbite_core.get_tnt_root(), "sdfdsf", script_path)
        bake_flux = core.import_module_from_file(
            "bake_flux", os.path.join(frostbite_core.get_tnt_root(), script_path, "bake_flux.py")
        )
    except Exception as exception:
        LOGGER.error("Unable to import fbcli.contrib.bake_flux")
        raise exception

    # Concatenate arguments that can be repeated
    level = ",".join(level)
    variation = ",".join(variation)
    zone = ",".join(zone)

    asset = level
    function = "bakeFluxRadiosity"
    function += " -zsbake.DisableTerrain %s" % False
    function += " -zsbake.DistributedBuild %s" % True
    function += " -zsbake.GlobalSolution %s" % True
    function += " -zsbake.ProxyTerrain %s" % proxyterrain
    function += " -zsbake.Variations %s" % ",".join(variation)
    if zone:
        function += " -zsbake.Zones %s" % ",".join(zone)
    function += " -zsbake.EnableGiGrid %s" % True
    function += " -zsbake.EnableLightMaps %s" % True

    function += " -gigrid.DistributeBuild %s" % True

    try:
        bake_flux.execute_pipeline(asset, function)
    except Exception as exc:
        raise BakeFluxException("Unable to bake flux. {0}".format(exc))

    perforce = p4.P4Utils(port=p4_port, user=user, client=p4_client)

    # Submit flux bake
    perforce.reopen()
    resolved, _ = perforce.resolve(mode="t")
    if not resolved:
        LOGGER.error("Unable to automatically resolve. Reverting and aborting!")
        perforce.revert(quiet=True)
        raise exceptions.AutomaticP4MergeResolveException

    # pylint: disable=line-too-long
    message = "Automated Flux bake. Level={0}, Variation:{1}, Proxyterrain: {2}, Zone: {3}, Changelist: {4}".format(
        level, variation, proxyterrain, zone, code_changelist
    )
    message += "\nJenkins URL: " + os.environ.get("BUILD_URL", "None")
    if not dry_run:
        try:
            perforce.submit(message=message)
        except exceptions.ELIPYException:
            perforce.revert(quiet=True)
            raise
    else:
        LOGGER.info("Skipping submission since --dry_run flag was passed.")
