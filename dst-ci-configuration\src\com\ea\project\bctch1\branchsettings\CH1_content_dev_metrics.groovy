package com.ea.project.bctch1.branchsettings

import com.ea.project.bctch1.BctCh1

class CH1_content_dev_metrics {
    // Settings for jobs
    static Class project = BctCh1
    static Map general_settings = [
        dataset                 : project.dataset,
        elipy_call              : project.elipy_call + ' --use-fbenv-core',
        elipy_install_call      : project.elipy_install_call,
        frostbite_licensee      : project.frostbite_licensee,
        workspace_root          : project.workspace_root,
        job_label_statebuild    : 'statebuild',
    ]
    static Map code_settings = [
        fake_ooa_wrapped_symbol      : false,
        run_code_unittests           : false,
        skip_code_build_if_no_changes: true,
        slack_channel_code           : [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        statebuild_code              : false,
    ]
    static Map data_settings = [
        deployment_data_branch     : false,
        enable_daily_data_clean    : true,
        enable_lkg_cleaning        : true,
        poolbuild_data             : false,
        slack_channel_data         : [
            channels                  : ['#bct-build-notify'],
            skip_for_multiple_failures: true,
        ],
        statebuild_data            : false,
    ]
    static Map frosty_settings = [:]
    static Map standard_jobs_settings = code_settings + data_settings + frosty_settings + [
        asset                              : 'ShippingLevels',
        bilbo_store_offsite                : false,
        clean_data_validation_pipeline_args: ' --disable-caches true',
        clean_build_validation_job_label   : 'CH1-content-dev-metrics',
        clean_local                        : true,
        timeout_hours_clean_data_validation: 20,
        enable_clean_build_validation      : true,
        enable_lkg_p4_counters             : true,
        extra_data_args                    : ['--pipeline-args -Pipeline.MaxConcurrencyThrottleMemoryThreshold --pipeline-args 0.8 --pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true'],
        import_avalanche_autotest          : false,
        linux_docker_images                : false,
        separate_symbol_store_upload       : false,
        server_asset                       : 'Game/Setup/Build/DevMPLevels',
        skip_icepick_settings_file         : true,
        strip_symbols                      : false,
        trigger_type_code                  : 'cron',
        trigger_string_code                : 'H 0 * * *',
        trigger_type_data                  : 'none',
        use_deprecated_blox_packages       : true,
    ]
    static Map icepick_settings = [
        ignore_icepick_exit_code: false
    ]
    static Map preflight_settings = [:]

    // Matrix definitions for jobs
    static List code_matrix = [
        [
            name: 'win64game', configs: [
            [
                name: 'final',
                dry_run_code             : true,
                skip_symbols_to_symstore : true,
                fb_env_values: [
                    'fbenv.extratelemetry=gensln:Labels=gla.build.metrics,Win64Game_GenSln,Final,CL%code_changelist%,NoTests,NoSNDBS',
                    'fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,Win64Game_BuildSln,Final,CL%code_changelist%,NoTests,NoSNDBS',
                ]
            ],
            [
                name: 'final',
                custom_tag               : 'sndbs',
                dry_run_code             : true,
                skip_symbols_to_symstore : true,
                sndbs_enabled            : true,
                fb_env_values: [
                    'fbenv.extratelemetry=gensln:Labels=gla.build.metrics,Win64Game_GenSln,Final,CL%code_changelist%,NoTests,UsesSNDBS',
                    'fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,Win64Game_BuildSln,Final,CL%code_changelist%,NoTests,UsesSNDBS',
                ]
            ]
        ]
        ],
        [
            name: 'win64game', configs: [
            [
                name: 'performance',
                dry_run_code             : true,
                skip_symbols_to_symstore : true,
                fb_env_values: [
                    'fbenv.extratelemetry=gensln:Labels=gla.build.metrics,Win64Game_GenSln,Performance,CL%code_changelist%,NoTests,NoSNDBS',
                    'fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,Win64Game_BuildSln,Performance,CL%code_changelist%,NoTests,NoSNDBS',
                ]
            ],
            [
                name: 'performance',
                custom_tag               : 'sndbs',
                dry_run_code             : true,
                skip_symbols_to_symstore : true,
                sndbs_enabled            : true,
                fb_env_values: [
                    'fbenv.extratelemetry=gensln:Labels=gla.build.metrics,Win64Game_GenSln,Performance,CL%code_changelist%,NoTests,UsesSNDBS',
                    'fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,Win64Game_BuildSln,Performance,CL%code_changelist%,NoTests,UsesSNDBS',
                ]
            ]
        ]
        ],
        [
            name: 'win64game', configs: [
            [
                name: 'retail',
                dry_run_code             : true,
                skip_symbols_to_symstore : true,
                fb_env_values: [
                    'fbenv.extratelemetry=gensln:Labels=gla.build.metrics,Win64Game_GenSln,Retail,CL%code_changelist%,NoTests,NoSNDBS',
                    'fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,Win64Game_BuildSln,Retail,CL%code_changelist%,NoTests,NoSNDBS',
                ]
            ],
            [
                name: 'retail',
                custom_tag               : 'sndbs',
                dry_run_code             : true,
                skip_symbols_to_symstore : true,
                sndbs_enabled            : true,
                fb_env_values: [
                    'fbenv.extratelemetry=gensln:Labels=gla.build.metrics,Win64Game_GenSln,Retail,CL%code_changelist%,NoTests,UsesSNDBS',
                    'fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,Win64Game_BuildSln,Retail,CL%code_changelist%,NoTests,UsesSNDBS',
                ]
            ]
        ]
        ],
        [
            name: 'ps5', configs: [
            [
                name: 'final',
                dry_run_code             : true,
                skip_symbols_to_symstore : true,
                fb_env_values: [
                    'fbenv.extratelemetry=gensln:Labels=gla.build.metrics,PS5_GenSln,Final,CL%code_changelist%,NoTests,NoSNDBS',
                    'fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,PS5_BuildSln,Final,CL%code_changelist%,NoTests,NoSNDBS',
                ]
            ],
            [
                name: 'final',
                custom_tag               : 'sndbs',
                dry_run_code             : true,
                skip_symbols_to_symstore : true,
                sndbs_enabled            : true,
                fb_env_values: [
                    'fbenv.extratelemetry=gensln:Labels=gla.build.metrics,PS5_GenSln,Final,CL%code_changelist%,NoTests,UsesSNDBS',
                    'fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,PS5_BuildSln,Final,CL%code_changelist%,NoTests,UsesSNDBS',
                ]
            ]
        ]
        ],
        [
            name: 'ps5', configs: [
            [
                name: 'performance',
                dry_run_code             : true,
                skip_symbols_to_symstore : true,
                fb_env_values: [
                    'fbenv.extratelemetry=gensln:Labels=gla.build.metrics,PS5_GenSln,Performance,CL%code_changelist%,NoTests,NoSNDBS',
                    'fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,PS5_BuildSln,Performance,CL%code_changelist%,NoTests,NoSNDBS',
                ]
            ],
            [
                name: 'performance',
                custom_tag               : 'sndbs',
                dry_run_code             : true,
                skip_symbols_to_symstore : true,
                sndbs_enabled            : true,
                fb_env_values: [
                    'fbenv.extratelemetry=gensln:Labels=gla.build.metrics,PS5_GenSln,Performance,CL%code_changelist%,NoTests,UsesSNDBS',
                    'fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,PS5_BuildSln,Performance,CL%code_changelist%,NoTests,UsesSNDBS',
                ]
            ]
        ]
        ],
        [
            name: 'ps5', configs: [
            [
                name: 'retail',
                dry_run_code             : true,
                skip_symbols_to_symstore : true,
                fb_env_values: [
                    'fbenv.extratelemetry=gensln:Labels=gla.build.metrics,PS5_GenSln,Retail,CL%code_changelist%,NoTests,NoSNDBS',
                    'fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,PS5_BuildSln,Retail,CL%code_changelist%,NoTests,NoSNDBS',
                ]
            ],
            [
                name: 'retail',
                custom_tag               : 'sndbs',
                dry_run_code             : true,
                skip_symbols_to_symstore : true,
                sndbs_enabled            : true,
                fb_env_values: [
                    'fbenv.extratelemetry=gensln:Labels=gla.build.metrics,PS5_GenSln,Retail,CL%code_changelist%,NoTests,UsesSNDBS',
                    'fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,PS5_BuildSln,Retail,CL%code_changelist%,NoTests,UsesSNDBS',
                ]
            ]
        ]
        ],
        [
            name: 'xbsx', configs: [
            [
                name: 'final',
                dry_run_code             : true,
                skip_symbols_to_symstore : true,
                fb_env_values: [
                    'fbenv.extratelemetry=gensln:Labels=gla.build.metrics,XBSX_GenSln,Final,CL%code_changelist%,NoTests,NoSNDBS',
                    'fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,XBSX_BuildSln,Final,CL%code_changelist%,NoTests,NoSNDBS',
                ]
            ],
            [
                name: 'final',
                custom_tag               : 'sndbs',
                dry_run_code             : true,
                skip_symbols_to_symstore : true,
                sndbs_enabled            : true,
                fb_env_values: [
                    'fbenv.extratelemetry=gensln:Labels=gla.build.metrics,XBSX_GenSln,Final,CL%code_changelist%,NoTests,UsesSNDBS',
                    'fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,XBSX_BuildSln,Final,CL%code_changelist%,NoTests,UsesSNDBS',
                ]
            ]
        ]
        ],
        [
            name: 'xbsx', configs: [
            [
                name: 'performance',
                dry_run_code             : true,
                skip_symbols_to_symstore : true,
                fb_env_values: [
                    'fbenv.extratelemetry=gensln:Labels=gla.build.metrics,XBSX_GenSln,Performance,CL%code_changelist%,NoTests,NoSNDBS',
                    'fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,XBSX_BuildSln,Performance,CL%code_changelist%,NoTests,NoSNDBS',
                ]
            ],
            [
                name: 'performance',
                custom_tag               : 'sndbs',
                dry_run_code             : true,
                skip_symbols_to_symstore : true,
                sndbs_enabled            : true,
                fb_env_values: [
                    'fbenv.extratelemetry=gensln:Labels=gla.build.metrics,XBSX_GenSln,Performance,CL%code_changelist%,NoTests,UsesSNDBS',
                    'fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,XBSX_BuildSln,Performance,CL%code_changelist%,NoTests,UsesSNDBS',
                ]
            ]
        ]
        ],
        [
            name: 'xbsx', configs: [
            [
                name: 'retail',
                dry_run_code             : true,
                skip_symbols_to_symstore : true,
                fb_env_values: [
                    'fbenv.extratelemetry=gensln:Labels=gla.build.metrics,XBSX_GenSln,Retail,CL%code_changelist%,NoTests,NoSNDBS',
                    'fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,XBSX_BuildSln,Retail,CL%code_changelist%,NoTests,NoSNDBS',
                ]
            ],
            [
                name: 'retail',
                custom_tag               : 'sndbs',
                dry_run_code             : true,
                skip_symbols_to_symstore : true,
                sndbs_enabled            : true,
                fb_env_values: [
                    'fbenv.extratelemetry=gensln:Labels=gla.build.metrics,XBSX_GenSln,Retail,CL%code_changelist%,NoTests,UsesSNDBS',
                    'fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,XBSX_BuildSln,Retail,CL%code_changelist%,NoTests,UsesSNDBS',
                ]
            ]
        ]
        ],
        [
            name: 'linux64server', configs: [
            [
                name: 'final',
                dry_run_code             : true,
                skip_symbols_to_symstore : true,
                fb_env_values: [
                    'fbenv.extratelemetry=gensln:Labels=gla.build.metrics,Linux_GenSln,Final,CL%code_changelist%,NoTests,NoSNDBS',
                    'fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,Linux_BuildSln,Final,CL%code_changelist%,NoTests,NoSNDBS',
                ]
            ],
            [
                name: 'final',
                custom_tag               : 'sndbs',
                dry_run_code             : true,
                skip_symbols_to_symstore : true,
                sndbs_enabled            : true,
                fb_env_values: [
                    'fbenv.extratelemetry=gensln:Labels=gla.build.metrics,Linux_GenSln,Final,CL%code_changelist%,NoTests,UsesSNDBS',
                    'fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,Linux_BuildSln,Final,CL%code_changelist%,NoTests,UsesSNDBS',
                ]
            ]
        ]
        ],
        [
            name: 'tool', configs: [
            [
                name: 'release',
                compile_unit_tests       : true,
                fb_env_values: [
                    'fbenv.extratelemetry=gensln:Labels=gla.build.metrics,Tool_GenSln,Release,CL%code_changelist%,IncludesTests,NoSNDBS',
                    'fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,Tool_BuildSln,Release,CL%code_changelist%,IncludesTests,NoSNDBS',
                ]
            ],
            [
                name: 'release',
                compile_unit_tests       : true,
                custom_tag               : 'sndbs',
                dry_run_code             : true,
                skip_symbols_to_symstore : true,
                sndbs_enabled            : true,
                fb_env_values: [
                    'fbenv.extratelemetry=gensln:Labels=gla.build.metrics,Tool_GenSln,Release,CL%code_changelist%,IncludesTests,UsesSNDBS',
                    'fbenv.extratelemetry=buildsln:Labels=gla.build.metrics,Tool_BuildSln,Release,CL%code_changelist%,IncludesTests,UsesSNDBS',
                ]
            ]
        ]
        ],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = [
        // [name: '.data.start', args: []],
    ]
    static List data_matrix = [
        // [
        //     name: 'win64',
        //     nightly_clean_build: true,
        //     fb_env_values: [
        //         'fbenv.extratelemetry=cook:Labels=gla.cook.metrics',
        //     ]
        // ],
    ]
    static List data_downstream_matrix = []
    static List patchdata_matrix = []
    static List frosty_matrix = []
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = []
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
