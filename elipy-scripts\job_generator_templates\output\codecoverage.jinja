{#
    Command:
        CodeCoverage
            short_help: Run CodeCoverage.

    Arguments:

    Required variables:

    Optional variables:
        codecoverage_path
            default: TestAutomation/CodeCoverageTestAutomation.bat
            help: Test script with relative path from Jenkins workspace
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    CodeCoverage
    {%- if codecoverage_path %}
    --codecoverage-path {{ codecoverage_path }}
    {%- endif %}
  displayName: elipy CodeCoverage
