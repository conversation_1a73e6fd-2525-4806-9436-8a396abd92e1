package com.ea.lib.jobs

import com.ea.lib.LibJobDsl
import com.ea.lib.jobsettings.AutotestSettings
import com.ea.lib.jobsettings.JobSetting
import com.ea.lib.model.autotest.AutotestCategory
import com.ea.lib.model.autotest.Platform
import com.ea.lib.model.autotest.TestSuite
import com.ea.lib.model.autotest.jobs.AutotestModel

class LibAutotest {

    /**
     * Return a standardized autotest start job name
     */
    static String getAutotestStartJobName(def branch, def groupName, def subGroupName = 'all') {
        return "${branch}.autotest.${groupName}.${subGroupName}.start"
    }

    /**
     * Return a standardized job name for autotest worker jobs
     */
    static String getAutotestJobName(def branch, def groupName, Platform platform, def testSuiteName, def suffix = null) {
        // Have to use toString due to CPS method mismatch:
        // https://www.jenkins.io/doc/book/pipeline/cps-method-mismatches/#overrides-of-non-cps-transformed-methods
        // codenarc-disable UnnecessaryToString
        String jobName = "${branch}.autotest.${groupName}.${platform.toString()}.${testSuiteName}"
        if (suffix) {
            jobName += ".${suffix}"
        }
        return jobName
    }

    /**
     * Adds generic job parameters for autotest start jobs.
     */
    static void autotestStart(def job, def project, def branchFile, def masterFile, String branchName, AutotestCategory testCategory) {
        JobSetting settings = new AutotestSettings()
        settings.initializeAutotestStart(branchFile, masterFile, project, branchName, testCategory)
        job.with {
            disabled(settings.isDisabled)
            logRotator(7, 50)
            quietPeriod(0)
            properties {
                disableConcurrentBuilds()
                disableResume()
            }
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to sync')
                    trim(true)
                }
                stringParam {
                    name('client_build_id')
                    defaultValue('')
                    description('Specifies client build id in bilbo')
                    trim(true)
                }
                if (settings.needGameServer) {
                    stringParam {
                        name('server_build_id')
                        defaultValue('')
                        description('Specifies server build id in bilbo')
                        trim(true)
                    }
                }
                // Do not clean when testing with loose files since it is not possible to clean
                // files build (performance tests).
                if (!settings.isTestWithLooseFiles) {
                    booleanParam('clean', false, 'Run a clean on the Autotest step')
                }
            }
            environmentVariables {
                env('branchName', settings.branchName)
                env('dataset', settings.dataset)
                env('projectName', settings.projectName)
                env('testCategory', settings.testCategoryName)
                env('autotestMatrix', settings.autotestMatrixName)
                env('lkg_p4_counters_enabled', settings.enableP4Counters)
                env('set_integration_info', settings.setIntegrationInfo)
            }
        }
    }

    /**
     * Adds generic job parameters for manual icepick start jobs.
     */
    static void autotestManualStart(def job, def project, def branchFile, def masterFile, String branchName) {
        JobSetting settings = new AutotestSettings()
        settings.initializeAutotestManualStart(branchFile, masterFile, project, branchName)
        job.with {
            description(settings.description)
            logRotator(7, 50)
            quietPeriod(0)
            properties {
                disableConcurrentBuilds()
                disableResume()
            }
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('Specifies data changelist to sync')
                    trim(true)
                }
                stringParam {
                    name('client_build_id')
                    defaultValue('')
                    description('Specifies client build id in bilbo')
                    trim(true)
                }
                stringParam {
                    name('server_build_id')
                    defaultValue('')
                    description('Specifies server build id in bilbo')
                    trim(true)
                }
                stringParam {
                    name('clean')
                    defaultValue('false')
                    description('Run a clean during this job')
                    trim(true)
                }
            }
            environmentVariables {
                env('branchName', settings.branchName)
                env('dataset', settings.dataset)
                env('projectName', settings.projectName)
                env('autotestMatrix', settings.autotestMatrixName)
            }
        }
    }

    static boolean checkGameServer(TestSuite testSuite, AutotestCategory testCategory) {
        return ((testSuite.needGameServer == null && testCategory.needGameServer) || testSuite.needGameServer)
    }

    /**
     * Adds generic job parameters for autotest test jobs.
     */
    static void autotestRun(def job, def project, def branchFile, def masterFile, String branchName, AutotestModel autotestModel, AutotestCategory testCategory) {
        JobSetting settings = new AutotestSettings()
        settings.initializeAutotestRun(branchFile, masterFile, project, branchName, autotestModel, testCategory)
        job.with {
            logRotator(7, 50)
            quietPeriod(0)
            label(settings.jobLabel)
            customWorkspace(settings.workspaceRoot)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('code_changelist')
                    trim(true)
                }
                stringParam {
                    name('data_changelist')
                    defaultValue('')
                    description('data_changelist')
                    trim(true)
                }
                stringParam {
                    name('client_build_id')
                    defaultValue('')
                    description('Specifies client build id in bilbo')
                    trim(true)
                }
                if (settings.needGameServer) {
                    stringParam {
                        name('server_build_id')
                        defaultValue('')
                        description('Specifies server build id in bilbo')
                        trim(true)
                    }
                }
                stringParam {
                    name('test_definition')
                    defaultValue(settings.testDefinition)
                    description('test_definition')
                    trim(true)
                }
                // Do not clean when testing with loose files since it is not possible to clean
                // files build (performance tests).
                if (!settings.isTestWithLooseFiles) {
                    booleanParam('clean', false, 'Run a clean at the start of the job.')
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName('${JOB_NAME}.${ENV, var="data_changelist"}.${ENV, var="code_changelist"}')
                timeout {
                    absolute(settings.timeoutMinutes)
                    failBuild()
                }
                credentialsBinding {
                    if (settings.userCredentials) {
                        usernamePassword('monkey_email', 'monkey_passwd', settings.userCredentials)
                    }
                    if (settings.fbLoginDetails.p4_creds) {
                        usernamePassword('fb_p4_user', 'fb_p4_passwd', settings.fbLoginDetails.p4_creds as String)
                    }
                }
            }
            steps {
                if (settings.fbLoginDetails) {
                    batchFile("echo %fb_p4_passwd%|p4 -p ${settings.fbLoginDetails.p4_port} -u %fb_p4_user% login & exit 0")
                }
                LibJobDsl.installElipy(delegate, settings.elipyInstallCall, project)
                if (settings.installPypiwin32) {
                    batchFile('pip install pypiwin32')
                }
                batchFile("echo ${settings.testSuitesString} > ${settings.testSuitesJsonFile}")
                batchFile(settings.elipyCmd)
            }
        }
    }

    static void cleanIcepickLogs(def job, def branchInfo, boolean isCloud = false) {
        job.with {
            publishers {
                flexiblePublish {
                    conditionalAction {
                        condition {
                            alwaysRun()
                        }
                        steps {
                            batchFile((isCloud ? branchInfo.azure_elipy_call : branchInfo.elipy_call) + ' clean_icepick_logs > %WORKSPACE%\\logs\\clean_icepick_logs.log 2>&1')
                        }
                    }
                }
            }
        }
    }
}
