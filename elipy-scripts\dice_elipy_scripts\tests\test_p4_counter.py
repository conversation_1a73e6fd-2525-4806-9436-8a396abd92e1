"""
test_p4_counter.py

Unit testing for p4_counter
"""
import unittest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import call, MagicMock, patch
from dice_elipy_scripts.p4_counter import cli, get_counter_as_int
from dice_elipy_scripts import p4_counter
from elipy2.exceptions import ELIPYException


class P4CounterTests(unittest.TestCase):
    OPTION_PORT = "--port"
    OPTION_CLIENT = "--client"
    OPTION_USER = "--user"
    OPTION_COUNTERNAME = "--countername"
    OPTION_VALUE = "--value"
    OPTION_EXTRA_PORT = "--extra-port"
    OPTION_EXTRA_CLIENT = "--extra-client"
    OPTION_EXTRA_COUNTERNAME = "--extra-countername"
    OPTION_EXTRA_VALUE = "--extra-value"
    OPTION_FORCE = "--force"

    VALUE_PORT = "p4_port_1"
    VALUE_CLIENT = "client_name_1"
    VALUE_USER = "user_name"
    VALUE_COUNTERNAME = "counter_name_1"
    VALUE_VALUE = "1000"
    VALUE_EXTRA_PORT = "p4_port_2"
    VALUE_EXTRA_CLIENT = "client_name_2"
    VALUE_EXTRA_COUNTERNAME = "counter_name_2"
    VALUE_EXTRA_VALUE = "2000"

    BASIC_ARGS = [
        OPTION_PORT,
        VALUE_PORT,
        OPTION_CLIENT,
        VALUE_CLIENT,
        OPTION_USER,
        VALUE_USER,
        OPTION_COUNTERNAME,
        VALUE_COUNTERNAME,
        OPTION_VALUE,
        VALUE_VALUE,
    ]

    def setUp(self):
        self.patcher_get_counter_as_int = patch("dice_elipy_scripts.p4_counter.get_counter_as_int")
        self.mock_get_counter_as_int = self.patcher_get_counter_as_int.start()
        self.mock_get_counter_as_int.return_value = 999

        self.patcher_p4utils = patch("elipy2.p4.P4Utils")
        self.mock_p4utils = self.patcher_p4utils.start()

    def tearDown(self):
        patch.stopall()

    def test_basic_args(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_p4utils.assert_called_once_with(
            port=self.VALUE_PORT, client=self.VALUE_CLIENT, user=self.VALUE_USER
        )

    def test_setcounter(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_p4utils.return_value.setcounter.assert_called_once_with(
            self.VALUE_COUNTERNAME, self.VALUE_VALUE
        )

    def test_setcounter_skip_lower_value(self):
        self.mock_get_counter_as_int.return_value = 1001
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        assert self.mock_p4utils.return_value.setcounter.call_count == 0

    def test_setcounter_force_set_lower_value(self):
        self.mock_get_counter_as_int.return_value = 1001
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS + [self.OPTION_FORCE])
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_p4utils.return_value.setcounter.assert_called_once_with(
            self.VALUE_COUNTERNAME, self.VALUE_VALUE
        )

    def test_setcounter_failure(self):
        self.mock_p4utils.return_value.setcounter.side_effect = ELIPYException()
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 1

    def test_set_extra_counter_on_another_port(self):
        self.mock_get_counter_as_int.side_effect = [999, 1999]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_EXTRA_PORT,
                self.VALUE_EXTRA_PORT,
                self.OPTION_EXTRA_CLIENT,
                self.VALUE_EXTRA_CLIENT,
                self.OPTION_EXTRA_COUNTERNAME,
                self.VALUE_EXTRA_COUNTERNAME,
                self.OPTION_EXTRA_VALUE,
                self.VALUE_EXTRA_VALUE,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_p4utils.assert_has_calls(
            [
                call(port=self.VALUE_PORT, client=self.VALUE_CLIENT, user=self.VALUE_USER),
                call(
                    port=self.VALUE_EXTRA_PORT, client=self.VALUE_EXTRA_CLIENT, user=self.VALUE_USER
                ),
            ],
            any_order=True,
        )
        self.mock_p4utils.return_value.setcounter.assert_has_calls(
            [
                call(self.VALUE_COUNTERNAME, self.VALUE_VALUE),
                call(self.VALUE_EXTRA_COUNTERNAME, self.VALUE_EXTRA_VALUE),
            ]
        )

    def test_set_extra_counter_on_same_port(self):
        self.mock_get_counter_as_int.side_effect = [999, 1999]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_EXTRA_COUNTERNAME,
                self.VALUE_EXTRA_COUNTERNAME,
                self.OPTION_EXTRA_VALUE,
                self.VALUE_EXTRA_VALUE,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_p4utils.assert_called_once_with(
            port=self.VALUE_PORT, client=self.VALUE_CLIENT, user=self.VALUE_USER
        )
        self.mock_p4utils.return_value.setcounter.assert_has_calls(
            [
                call(self.VALUE_COUNTERNAME, self.VALUE_VALUE),
                call(self.VALUE_EXTRA_COUNTERNAME, self.VALUE_EXTRA_VALUE),
            ]
        )

    def test_set_extra_counter_skip_lower_value(self):
        self.mock_get_counter_as_int.side_effect = [999, 2001]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_EXTRA_COUNTERNAME,
                self.VALUE_EXTRA_COUNTERNAME,
                self.OPTION_EXTRA_VALUE,
                self.VALUE_EXTRA_VALUE,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_p4utils.assert_called_once_with(
            port=self.VALUE_PORT, client=self.VALUE_CLIENT, user=self.VALUE_USER
        )
        self.mock_p4utils.return_value.setcounter.assert_called_once_with(
            self.VALUE_COUNTERNAME, self.VALUE_VALUE
        )

    def test_set_extra_counter_force_set_lower_value(self):
        self.mock_get_counter_as_int.side_effect = [999, 2001]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_EXTRA_COUNTERNAME,
                self.VALUE_EXTRA_COUNTERNAME,
                self.OPTION_EXTRA_VALUE,
                self.VALUE_EXTRA_VALUE,
                self.OPTION_FORCE,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        self.mock_p4utils.assert_called_once_with(
            port=self.VALUE_PORT, client=self.VALUE_CLIENT, user=self.VALUE_USER
        )
        self.mock_p4utils.return_value.setcounter.assert_has_calls(
            [
                call(self.VALUE_COUNTERNAME, self.VALUE_VALUE),
                call(self.VALUE_EXTRA_COUNTERNAME, self.VALUE_EXTRA_VALUE),
            ]
        )

    def test_set_extra_counter_failure(self):
        self.mock_get_counter_as_int.side_effect = [999, 1999]
        self.mock_p4utils.return_value.setcounter.side_effect = [None, ELIPYException]
        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.BASIC_ARGS
            + [
                self.OPTION_EXTRA_COUNTERNAME,
                self.VALUE_EXTRA_COUNTERNAME,
                self.OPTION_EXTRA_VALUE,
                self.VALUE_EXTRA_VALUE,
            ],
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 1


class TestCounterLocalFunctions(unittest.TestCase):
    def setUp(self):
        self.patcher_p4utils = patch("elipy2.p4.P4Utils")
        self.mock_p4utils = self.patcher_p4utils.start()

    def tearDown(self):
        patch.stopall()

    def test_get_counter_as_int_input_as_string(self):
        self.mock_p4utils.return_value.getcounter.return_value = "1234"
        assert get_counter_as_int(self.mock_p4utils.return_value, "some_counter") == 1234

    def test_get_counter_as_int_input_as_bytes(self):
        self.mock_p4utils.return_value.getcounter.return_value = b"1234"
        assert get_counter_as_int(self.mock_p4utils.return_value, "some_counter") == 1234

    def test_get_counter_as_int_input_as_int(self):
        self.mock_p4utils.return_value.getcounter.return_value = 1234
        assert get_counter_as_int(self.mock_p4utils.return_value, "some_counter") == 1234

    def test_get_counter_as_int_type_error(self):
        self.mock_p4utils.return_value.getcounter.return_value = [12, 34]
        assert (
            get_counter_as_int(self.mock_p4utils.return_value, "some_counter", default_value=1000)
            == 1000
        )
