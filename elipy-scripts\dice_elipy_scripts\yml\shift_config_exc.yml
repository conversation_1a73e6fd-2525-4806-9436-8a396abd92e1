# Duplicated settings lower in the settings list will get higher priority
buildtype: "QA"
milestone: "Production"
distribution_type: "InternalOnly"
retention_policy: "SpaceAvailable"
priority: ""
version: "2.0"
xbsx:
  content:
    files:
      file_names:
        - "builtLevels.json"
        - "*.buildlayout"
        - "*.BuildSettings"
        - "Logo.png"
        - "*.dll"
        - "SmallLogo.png"
        - "SplashScreen.png"
        - "StoreLogo.png"
        - "WideLogo.png"
        - "*.Main_Xbsx_*.exe"
        - "MicrosoftGame.config"
        - "gameos.xvd"
        - "nsal.json"
        - "package.mft"
        - "resources.pri"
        - "build.json"
      supplemental_files:
        - "*.xml"
        - "*.pdb"
      directory:
        - "Data"
        - "Scripts"
    digital:
      file_names:
        - "*appxmanifest.xml"
        - "MicrosoftGame.config"
        - "*neutral__*.xvc"
        - "*.ekb"
        - "Validator_*.xml"
      supplemental_files:
        - "build.json"
        - "*.pdb"
        - "*neutral__*[!c]" #This is kind of a hack. glob.glob doesn't support do not match string; only set of characters.
      directory:
        - ""
    patch:
      file_names:
        - "*appxmanifest.xml"
        - "MicrosoftGame.config"
        - "*neutral__*.xvc"
        - "*.ekb"
        - "Validator_*.xml"
      supplemental_files:
        - "build.json"
        - "*.pdb"
        - "*neutral__*[!c]" #This is kind of a hack. glob.glob doesn't support do not match string; only set of characters.
      directory:
        - ""
  settings:
    build-bikes:
      ww:
        final:
          digital:
            sku_id: "fe9d6ce1-18f7-46c0-ba6d-af3fbebd2769"
            sku_name: "FG - WW (build-bikes digital final ww)"
    build-upg:
      ww:
        final:
          files:
            sku_id: "430609e1-51ac-4bc7-97a7-bd4a747ee9ce"
            sku_name: "FG - WW (build-upg files final ww)"
    build-main:
      ww:
        final:
          digital:
            sku_id: "6d7dcf9c-6bd4-42c6-bee9-f578f7476bf9"
            sku_name: "FG - WW (build-main digital final ww)"
    build-stage:
      ww:
        final:
          files:
            sku_id: "ba911c2b-777d-42b2-aa62-a0a18e4986d5"
            sku_name: "FG - WW (build-stage Final Files ww)"
          digital:
            sku_id: "558953a5-f743-4214-b12e-fa2afda0b896"
            distribution_type: "ExternalTechPartners"
            sku_name: "FG - WW (build-stage digital final ww)"
        performance:
          digital:
            sku_id: "e7cab819-1543-45f9-80c2-ca8b82785f32"
            sku_name: "FG - WW (build-stage digital perf ww)"
        retail:
          digital:
            sku_id: "b142084d-0128-48ce-9af5-7337519bee86"
            distribution_type: "ExternalTechPartners"
            sku_name: "FG - WW (build-stage digital retail WW)"
    build-stage-patch:
      ww:
        final:
          patch:
            sku_id: "4e90b706-c100-4733-902e-5d6d5db18a45"
            sku_name: "Patch - WW (build-stage patch final WW)"
        retail:
          patch:
            sku_id: "a63ec614-ea2a-4651-8f54-fb34a4c50c30"
            sku_name: "Patch - WW (build-stage patch retail WW)"
    build-release:
      ww:
        final:
          files:
            sku_id: "5c9d4d83-a79a-48e4-8294-d4961efbac08"
            sku_name: "FG - W (build-release files final ww)"
          digital:
            sku_id: "52d2f9f7-e21c-4b86-ba01-cb14a26167a2"
            distribution_type: "ExternalTechPartners"
            sku_name: "FG - WW (build-release digital final ww)"
          patch:
            sku_id: "30665059-80bd-49f4-86ec-a932ff24ba2a"
            sku_name: "Patch - WW (build-release patch final ww)"
        retail:
          digital:
            sku_id: "0fadded1-6091-4c8d-8d03-73da878d3f96"
            distribution_type: "ExternalTechPartners"
            sku_name: "FG - WW (build-release digital retail ww)"
          patch:
            sku_id: "40b8aed4-1bd8-4dbb-b890-8af6bfb6b338"
            sku_name: "Patch - WW (build-release patch retail ww)"
    build-hotfix:
      ww:
        final:
          files:
            sku_id: "6391e820-7d3b-4f1a-8d55-8ccac932e7eb"
            sku_name: "FG - WW (build-hotfix files final ww)"
          digital:
            sku_id: "0096df71-bac2-42bc-a718-23f63ea51a20"
            distribution_type: "ExternalTechPartners"
            sku_name: "FG - WW (build-hotfix digital final ww)"
          patch:
            sku_id: "3c21a136-461c-44bc-bfb0-584c39f62ca5"
            sku_name: "Patch - WW (build-hotfix patch final ww)"
        retail:
          digital:
            sku_id: "7948d658-b2c0-4853-a2a0-e69c25a2cf55"
            distribution_type: "ExternalTechPartners"
            sku_name: "FG - WW (build-hotfix digital retail ww)"
          patch:
            sku_id: "51794a51-4244-404f-859f-132eb18aeae6"
            sku_name: "Patch - WW (build-hotfix patch retail ww)"
    build-release-irt:
      irt:
        retail:
          digital:
            sku_id: "c5973dd9-fd9d-4f86-90a7-fa6102375043"
            sku_name: "FG - WW (build-release digital retail IRT)"
          patch:
            sku_id: "4f059aff-cce1-492d-ba89-e4a203681fca"
            sku_name: "Patch - WW (build-release patch retail IRT)"
    build-volume9:
      ww:
        final:
          digital:
            sku_id: "7e15150c-c404-4201-a324-c790d85048b7"
            sku_name: "FG - WW (build-volume9 digital final ww)"

ps5:
  content:
    files:
      file_names:
        - "builtLevels.json"
        - "build.json"
        - "*.prx"
        - "*.elf"
        - "*.buildlayout"
        - "*.BuildSettings"
        - "eboot.bin"
      supplemental_files:
        - "*.xml"
      directory:
        - "Data"
        - "Scripts"
        - "sce_sys"
    digital:
      file_names:
        - "*-V0100.pkg"
      upload_loop_filenames:
        - "*V0100.pkg"
        - "*remastered.pkg"
      supplemental_files:
        - "chunks*.gp5"
        - "build.json"
      directory:
        - ""
    patch:
      file_names:
        - "*-V0100.pkg"
      supplemental_files:
        - "chunks*.gp5"
        - "build.json"
      directory:
        - ""
    patch-remaster:
      file_names:
        - "*remastered.pkg"
      supplemental_files:
        - "chunks*.gp5"
        - "build.json"
      directory:
        - ""
  settings:
    build-bikes:
      ww:
        final:
          digital:
            sku_id: "5e5f44f8-5aaf-4f59-8916-dc85ef06c92b"
            sku_name: "FG - WW (build-bikes digital final ww)"
    build-upg:
      ww:
        final:
          files:
            sku_id: "1fa8c9df-5b49-48d7-8f05-38097f789f5e"
            sku_name: "FG - WW (build-upg files final WW)"
    build-main:
      ww:
        final:
          digital:
            sku_id: "133c9ec8-dcae-47fe-86be-a27659fbee99"
            sku_name: "FG - WW (build-main digital final ww)"
    build-stage:
      ww:
        final:
          files:
            sku_id: "85236796-2e05-482a-8b13-c447fbfe9b92"
            sku_name: "FG - WW (build-stage files final WW)"
          digital:
            sku_id: "594669f2-16b0-46df-adaf-d36856dcd8d7"
            distribution_type: "ExternalTechPartners"
            sku_name: "FG - WW (build-stage digital final WW)"
        performance:
          digital:
            sku_id: "b3b60c59-9e9f-4ad6-a77c-1778926b7bc1"
            sku_name: "FG - WW (build-stage digital perf ww)"
        retail:
          digital:
            sku_id: "9e5616c7-2514-4b94-a5c5-c25b8cb57203"
            distribution_type: "ExternalTechPartners"
            sku_name: "FG - WW (build-stage digital retail WW)"
    build-stage-patch:
      upload_loop:
        - "true"
      ww:
        final:
          patch:
            sku_id: "43220301-2bc8-429d-83f8-6f9ac3e4973c"
            sku_name: "Patch - WW (build-stage patch final WW)"
          patch-remaster:
            sku_id: "ae829400-3fc0-4686-bee6-dd1150e1c2b2"
            sku_name: "Patch - WW (build-stage patch remaster final WW)"
        retail:
          patch:
            sku_id: "d813a9ea-b891-4911-9e6e-98a523a143b5"
            sku_name: "Patch - WW (build-stage patch retail WW)"
          patch-remaster:
            sku_id: "e81d4d9d-bb57-4ce7-b5db-a28c0bfee0dc"
            sku_name: "Patch - WW (build-stage patch remaster retail WW)"
    build-release:
      upload_loop:
        - "true"
      ww:
        final:
          files:
            sku_id: "71146213-355b-4152-990d-a6b369b4f964"
            sku_name: "FG - WW (build-release files final WW)"
          digital:
            sku_id: "616d56e8-845b-44d9-babc-21302fd3571d"
            distribution_type: "ExternalTechPartners"
            sku_name: "FG - WW (build-release digital final WW)"
          patch:
            sku_id: "f033ab3c-cc42-44e4-8ce9-8998be49c1bc"
            sku_name: "Patch - WW (build-release patch final ww)"
          patch-remaster:
            sku_id: "31be079c-5cb7-44fa-9546-1e266bd3303a"
            sku_name: "Patch - WW (build-release patch remaster final ww)"
        retail:
          digital:
            sku_id: "a3122737-5860-4948-8369-566e68bc9f80"
            distribution_type: "ExternalTechPartners"
            sku_name: "FG - WW (build-release digital retail WW)"
          patch:
            sku_id: "4662495b-0358-4c5c-bdf9-fc329f2c2ad4"
            sku_name: "Patch - WW (build-release patch retail ww)"
          patch-remaster:
            sku_id: "9e058d64-3182-4633-b85d-38a5549dbca6"
            sku_name: "Patch - WW (build-release patch remaster retail ww)"
    build-hotfix:
      upload_loop:
        - "true"
      ww:
        final:
          files:
            sku_id: "0f09d9cc-4239-42a0-9e84-5488b6e6938c"
            sku_name: "FG - WW (build-hotfix files final WW)"
          digital:
            sku_id: "346e78f6-1816-4eb2-b217-d91b50efc2c6"
            distribution_type: "ExternalTechPartners"
            sku_name: "FG - WW (build-hotfix digital final WW)"
          patch:
            sku_id: "cbd21909-7809-4cfd-ba3e-88701feb6df9"
            sku_name: "Patch - WW (build-hotfix patch final ww)"
          patch-remaster:
            sku_id: "0c74accb-e847-4420-8796-6f172ecfed30"
            sku_name: "Patch - WW (build-hotfix patch remaster final ww)"
        retail:
          digital:
            sku_id: "7e586eb0-ac25-4eed-8f36-8db41a37cb61"
            distribution_type: "ExternalTechPartners"
            sku_name: "FG - WW (build-hotfix digital retail WW)"
          patch:
            sku_id: "0054e694-0ea4-4ce4-8c41-0233900f009d"
            sku_name: "Patch - WW (build-hotfix patch retail ww)"
          patch-remaster:
            sku_id: "dcb4692f-3ae2-4821-a52f-0c72f74a9bfa"
            sku_name: "Patch - WW (build-hotfix patch remaster retail ww)"
    build-release-irt:
      upload_loop:
        - "true"
      irt:
        retail:
          digital:
            sku_id: "f2bee253-03ea-49a4-ae5b-d610cd26cdd6"
            distribution_type: "ExternalTechPartners"
            sku_name: "FG - EU (build-release digital retail IRT)"
          patch:
            sku_id: "fbb4a0b3-70e0-4e3b-aff7-2293da95c244"
            distribution_type: "ExternalTechPartners"
            sku_name: "patch - EU (build-release-IRT digital retail IRT)"
          patch-remaster:
            sku_id: "2eb6b296-2c96-4595-a052-a73f24725186"
            distribution_type: "ExternalTechPartners"
            sku_name: "Patch - EU (build-release-IRT remas digital retail)"
    build-volume9:
      ww:
        final:
          digital:
            sku_id: "0db765e5-1966-485c-a488-1484683cca17"
            sku_name: "FG - WW (build-volume9 digital final ww)"

server:
  content:
    file_names:
      - '*'
    supplemental_files:
      - ""
  settings:
    build-bikes:
      final:
        files:
          sku_id: "ed57a9d9-5d36-4822-a34d-b19993314ade"
          sku_name: "Server - WW (build-bikes Win32 Files Final WW)"
    build-main:
      final:
        files:
          sku_id: "b517d7ed-6aa9-4497-adfb-1e07057b980a"
          sku_name: "Server - WW (build-main Win32 Files Final WW)"
    build-stage:
      final:
        files:
          sku_id: "50a46142-d7dc-41d3-8d01-088fcb5673f1"
          sku_name: "Server - WW (build-stage Win32 Files Final WW)"
    build-stage-patch:
      final:
        files:
          sku_id: "24e0ed62-6782-4780-9a1c-4ddb344b2099"
          sku_name: "Server - WW (build-stage-patch server files final)"
    build-volume9:
      final:
        files:
          sku_id: "f95b0677-95ec-4cfe-8e32-3dc9d15826ac"
          sku_name: "Server - WW (build-volume9 server files final ww)"

win64:
  content:
    files:
      file_names:
        - '*'
      supplemental_files:
      - "*.xml"
    digital:
      file_names:
        - "Excalibur.zip"
      supplemental_files:
        - "build.json"
        - "installerdata.xml"
      directory:
        - ""
      retail:
        supplemental_files:
          - "*.Main_*_retail*"
        directory:
          - ""
      final:
        supplemental_files:
          - "*.Main_*_retail*"
        directory:
          - ""
      performance:
        supplemental_files:
          - "*.Main_*_performance*"
    patch:
      file_names:
        - "Excalibur.zip"
      supplemental_files:
        - "build.json"
        - "installerdata.xml"
        - "*.Main_*_retail*"
      directory:
        - ""
  settings:
    build-bikes:
      final:
        files:
          sku_id: "bc73d998-240b-4ee1-bb85-094cd14016de"
          sku_name: "FG - WW (build-bikes Win32 Files Final WW)"
    build-upg:
      final:
        files:
          sku_id: "b7af931a-6d0d-4c6a-bbb0-0f6074f50b4a"
          sku_name: "FG - WW (build-upg Win32 files final ww)"
    build-main:
      final:
        files:
          sku_id: "6970ffef-1d83-4f79-a363-ca06bef96e0d"
          sku_name: "FG - WW (build-main Win32 Files Final WW)"
        digital:
          sku_id: "49e065ef-df3e-4bfb-bd51-2dfbe7f20406"
          sku_name: "FG - WW (build-main Win32 Digital Final WW)"
    build-stage:
      final:
        files:
          sku_id: "c59e9cdf-493e-4520-aaba-82ecfd487416"
          distribution_type: "ExternalTechPartners"
          sku_name: "FG - WW (build-stage Win32 Files Final WW)"
        digital:
          sku_id: "02c9c747-c491-4000-b02e-d223a040f13f"
          distribution_type: "ExternalTechPartners"
          sku_name: "FG - WW (build-stage Win32 Digital Final WW)"
      performance:
        digital:
          sku_id: "66499ff0-da7c-4c96-b69f-65f5622761c9"
          sku_name: "FG - WW (build-stage Win32 Digital Perf WW)"
      retail:
        digital:
          sku_id: "1ddd1749-20ec-440d-924b-f36afa4b1030"
          distribution_type: "ExternalTechPartners"
          sku_name: "FG - WW (build-stage Win32 Digital Retail WW)"
    build-stage-patch:
      ww:
        final:
          patch:
            sku_id: "9776d047-d72a-4be0-a300-4d60fd54c248"
            sku_name: "Patch - WW (build-stage patch final WW)"
        retail:
          patch:
            sku_id: "0663a766-8654-4d2a-91cd-b756d0ce2ea7"
            sku_name: "Patch - WW (build-stage patch retail WW)"
    build-release:
      final:
        files:
          sku_id: "213e115a-0d7c-4e45-b2b1-cdc512f26f62"
          sku_name: "FG - WW (build-release Win32 Files Final WW)"
        digital:
          sku_id: "de901cc5-c4a6-4248-b443-d185d64c4a2e"
          distribution_type: "ExternalTechPartners"
          sku_name: "FG - WW (build-release Win32 Digital Final WW)"
        patch:
          sku_id: "cd863195-92eb-4e0d-b96b-1acd60c8e86d"
          distribution_type: "ExternalTechPartners"
          sku_name: "FG - WW (build-release patch final ww)"
      retail:
        digital:
          sku_id: "10a1c3f1-c420-4086-9169-7d60857a9f5b"
          distribution_type: "ExternalTechPartners"
          sku_name: "FG - WW (build-release Win32 Digital Retail WW)"
        patch:
          sku_id: "9f40b378-bf8a-4cf6-b7ab-d8e2a84b20fb"
          distribution_type: "ExternalTechPartners"
          sku_name: "FG - WW (build-release patch retail ww)"
    build-hotfix:
      final:
        files:
          sku_id: "891fb965-271e-493d-a28d-0c82ffa91241"
          sku_name: "FG - WW (build-hotfix Win32 Files Final WW)"
        digital:
          sku_id: "09f27e4b-0b8e-495d-849e-36657f654150"
          distribution_type: "ExternalTechPartners"
          sku_name: "FG - WW (build-hotfix Win32 Digital Final WW)"
        patch:
          sku_id: "b9cb07b7-24b3-4ff1-9de4-3edc9a464e02"
          distribution_type: "ExternalTechPartners"
          sku_name: "FG - WW (build-hotfix patch final ww)"
      retail:
        digital:
          sku_id: "17420747-79c7-4676-8222-c161bd7493df"
          distribution_type: "ExternalTechPartners"
          sku_name: "FG - WW (build-hotfix Win32 Digital Retail WW)"
        patch:
          sku_id: "b4be7a3a-e985-4294-95eb-0d617b65c39d"
          distribution_type: "ExternalTechPartners"
          sku_name: "FG - WW (build-hotfix patch retail ww)"
    build-release-irt:
      final:
        files:
          sku_id: "0259b2d3-936f-4a52-978e-0ad69e8454a0"
          sku_name: "FG - WW (build-release Win32 Files final IRT)"
    build-volume9:
      final:
        files:
          sku_id: "f87d49e9-0917-4bd0-aabb-b409d2c3f94c"
          sku_name: "FG - WW (build-volume9 Win32 files final ww)"


offsite_basic_drone:
  content:
    file_names:
      - '*'
    supplemental_files:
      - ""
  settings:
    build-main:
        sku_id: "bdfa321b-c0b4-46a6-8dab-6b8995827fa2"
        distribution_type: "ExternalTechPartners"
        sku_name: "Tool - WW (build-main Offsite Drone Build)"
    build-stage:
        sku_id: "a292f145-f199-4dc8-8899-d217957c4967"
        distribution_type: "ExternalTechPartners"
        sku_name: "Tool - WW (build-stage Offsite Drone Build)"
    build-live-vehicles:
        sku_id: "3a2ee6f7-6671-4511-b413-8ae38bbc1742"
        distribution_type: "ExternalTechPartners"
        sku_name: "Tool - WW (build-live-vehicles Offsite Drone Build)"
linuxserver:
  content:
    file_names:
      - '*binaries.Zip'
    supplemental_files:
      - "build.json"
      - "builtLevels.json"
      - "*Symbols.zip"
  settings:
    build-upg:
      final:
        digital:
          sku_id: "1b12fd6e-fde3-4403-ab9c-169ac315b0c7"
          sku_name: "Server - WW (build-upg LinuxServer digital final ww)"
    build-main:
      final:
        digital:
          sku_id: "f16a3fc6-18e9-486c-b31b-52e4df49f2fd"
          sku_name: "FG - WW (build-main LinuxServer digital final ww)"
    build-stage:
      final:
        digital:
          sku_id: "be29a70f-6d42-4c99-a7ac-b06d5751db67"
          sku_name: "Server - WW (build-stage LinuxServer Digital Final WW)"
      release:
        digital:
          sku_id: "792c7c54-9a75-410e-b9fe-870d02d22db0"
          sku_name: "Server - WW (build-stage LinuxServer Digital Release)"
    build-stage-patch:
      final:
        digital:
          sku_id: "94c31e90-9e0b-4642-b713-108da9eaaa5b"
          sku_name: "Server - WW (build-stage-patch linuxser digital final)"
    build-stagerte-release:
      final:
        digital:
          sku_id: "86520a36-b181-440b-ab19-25012a87539c"
          sku_name: "Server - WW (build-stagerte-release Digital Final)"
    build-release:
      final:
        digital:
          sku_id: "1623d825-2383-4b9e-a073-5a048aef1d66"
          sku_name: "Server - WW (build-release LinuxServer Digital Final)"
    build-hotfix:
      final:
        digital:
          sku_id: "54080e68-e75d-4f31-83a9-e7a40acb158d"
          sku_name: "Server - WW (build-hotfix LinuxServer Digital Final)"
    build-release-irt:
      final:
        digital:
          sku_id: "68f5b1c8-2c51-448a-b81c-6b4d38755bc6"
          sku_name: "Server - WW (build-release LinuxServ Dig Final IRT)"
    build-volume9:
      final:
        digital:
          sku_id: "11c81143-d04b-44f8-8418-a297ea114e09"
          sku_name: "Server - WW (build-volume9 LinuxServ Dig Final ww)"
