{#
    Command:
        post_preflight
            short_help: Performs a post preflight maintenance run.

    Arguments:

    Required variables:
        platform
            help: Platform to run post preflight on.
            required: True
        datadir
            help: Which datadir to build from.
            required: True
        p4_client
            help: Which p4 client to use.
            required: True
        p4_user
            help: Which p4 user is being used.
            required: True
        p4_port
            help: Which p4 port to be used.
            required: True
        p4_client_code
            help: Which p4 client to use.
            required: True
        p4_port_code
            help: Which p4 port to be used.
            required: True

    Optional variables:
        asset
            help: Asset to be cooked.
            default: ['preflightlevels']
            multiple: True
        preflight_type
            default: content
            help: Code or content post preflight to perform.
        config
            help: Configuration to build with (code only).
            default: final
        password
            default: None
            help: User credentials to authenticate to package server
        email
            default: None
            help: User email to authenticate to package server
        domain_user
            default: None
            help: The user to authenticate to package server as DOMAIN\user
        limit_cpu
            is_flag: True
            help: Only enable when code preflight tool on AWS.
        framework_args
            multiple: True
            help: Framework arguments for gen sln.
        clean_master_version_check
            is_flag: True
            help: Run clean on master version update.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    post_preflight
    --platform {{ platform }}
    --datadir {{ datadir }}
    --p4_client {{ p4_client }}
    --p4_user {{ p4_user }}
    --p4_port {{ p4_port }}
    --p4_client_code {{ p4_client_code }}
    --p4_port_code {{ p4_port_code }}
    {%- if asset %}
    --asset {{ asset }}
    {%- endif %}
    {%- if preflight_type %}
    --preflight_type {{ preflight_type }}
    {%- endif %}
    {%- if config %}
    --config {{ config }}
    {%- endif %}
    {%- if password %}
    --password {{ password }}
    {%- endif %}
    {%- if email %}
    --email {{ email }}
    {%- endif %}
    {%- if domain_user %}
    --domain-user {{ domain_user }}
    {%- endif %}
    {%- if limit_cpu %}
    --limit_cpu {{ limit_cpu }}
    {%- endif %}
    {%- if framework_args %}
    --framework-args {{ framework_args }}
    {%- endif %}
    {%- if clean_master_version_check %}
    --clean-master-version-check {{ clean_master_version_check }}
    {%- endif %}
  displayName: elipy post_preflight
