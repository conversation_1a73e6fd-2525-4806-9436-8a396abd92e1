root:
  default:
    base: ['$DESTINATION_BASEPATH$', '$VERSION$']
    paths:
    - path_location: []
      min_items: 1

server:
  default:
    base: ['$DESTINATION_BASEPATH$', '$VERSION$', 'server', '$DATACHANGELIST$_$CODECHANGELIST$']
    paths:
    - path_location: []
      min_items: 5
    - path_location: ['symbol']

linuxserver:
  default:
    base: ['$DESTINATION_BASEPATH$', '$VERSION$', 'linuxserver', '$DATACHANGELIST$_$CODECHANGELIST$']
    paths:
    - path_location: []
      min_items: 5
    - path_location: ['symbol']
    - path_location: ['digital', 'ww', 'final', 'FrostyLogFile.txt']
    - path_location: ['digital', 'ww', 'final', 'build.json']
    - path_location: ['digital', 'ww', 'final', 'builtLevels.json']
    - path_location: ['digital', 'ww', 'final', 'Frostbite_win32_Server_$DATACHANGELIST$_Binaries.zip']
    - path_location: ['digital', 'ww', 'final', 'BattlefieldGame.Main_Linux64_final_Server.dbg']

ps4:
  default:
    base: ['$DESTINATION_BASEPATH$', '$VERSION$', 'ps4', '$DATACHANGELIST$_$CODECHANGELIST$']
    paths:
    - path_location: ['symbol']
    - path_location: ['bundles', 'delta', 'data', 'initfs_Gen4b']
    - path_location: ['bundles', 'delta', 'Meta', 'base_ops_chain.zip']
    - path_location: ['bundles', 'head', 'data', 'ops_chain.zip']
    - path_location: ['bundles', 'state', 'cas.cat']
    - path_location: ['bundles', 'state', 'meta', 'CURRENT']
    - path_location: ['patch', 'na', 'final', 'publish.log']
    - path_location: ['patch', 'na', 'final', 'streaminstall_chunk.log']
    - path_location: ['patch', 'na', 'retail', 'publish.log']
    - path_location: ['patch', 'na', 'retail', 'streaminstall_chunk.log']
    - path_location: ['patch', 'eu', 'final', 'publish.log']
    - path_location: ['patch', 'eu', 'final', 'streaminstall_chunk.log']
    - path_location: ['patch', 'eu', 'retail', 'publish.log']
    - path_location: ['patch', 'eu', 'retail', 'streaminstall_chunk.log']
    - path_location: []
      min_items: 6
    - path_location: ['bundles']
      min_items: 3
    - path_location: ['bundles', 'delta']
      min_items: 2
    - path_location: ['bundles', 'head', 'data']
      min_items: 10
    - path_location: ['bundles', 'state']
      min_items: 20
    - path_location: ['bundles', 'state', 'meta']
      min_items: 400
    - path_location: ['patch', 'eu', 'final']
      min_items: 13
    - path_location: ['patch', 'eu', 'retail']
      min_items: 14
    - path_location: ['patch', 'na', 'final']
      min_items: 13
    - path_location: ['patch', 'na', 'retail']
      min_items: 14

ps5:
  default:
    base: ['$DESTINATION_BASEPATH$', '$VERSION$', 'ps5', '$DATACHANGELIST$_$CODECHANGELIST$']
    paths:
    - path_location: ['bundles', 'delta', 'data', 'initfs_ps5']
    - path_location: ['bundles', 'delta', 'Meta', 'base_ops_chain.zip']
    - path_location: ['bundles', 'head', 'data', 'ops_chain.zip']
    - path_location: ['bundles', 'state', 'cas.cat']
    - path_location: ['bundles', 'state', 'meta', 'CURRENT']
    - path_location: ['patch', 'na', 'final', 'publish.log']
    - path_location: ['patch', 'na', 'final', 'streaminstall_chunk.log']
    - path_location: ['patch', 'na', 'retail', 'publish.log']
    - path_location: ['patch', 'na', 'retail', 'streaminstall_chunk.log']
    - path_location: ['patch', 'eu', 'final', 'publish.log']
    - path_location: ['patch', 'eu', 'final', 'streaminstall_chunk.log']
    - path_location: ['patch', 'eu', 'retail', 'publish.log']
    - path_location: ['patch', 'eu', 'retail', 'streaminstall_chunk.log']
    - path_location: []
    - path_location: []
      min_items: 6
    - path_location: ['bundles']
      min_items: 3
    - path_location: ['bundles', 'delta']
      min_items: 2
    - path_location: ['bundles', 'head', 'data']
      min_items: 10
    - path_location: ['bundles', 'state']
      min_items: 20
    - path_location: ['bundles', 'state', 'meta']
      min_items: 400
    - path_location: ['patch', 'eu', 'final']
      min_items: 13
    - path_location: ['patch', 'eu', 'retail']
      min_items: 14
    - path_location: ['patch', 'na', 'final']
      min_items: 13
    - path_location: ['patch', 'na', 'retail']
      min_items: 14

xb1:
  default:
    base: ['$DESTINATION_BASEPATH$', '$VERSION$', 'xb1', '$DATACHANGELIST$_$CODECHANGELIST$']
    paths:
    - path_location: ['symbol']
    - path_location: ['bundles', 'delta', 'data', 'initfs_Gen4a']
    - path_location: ['bundles', 'delta', 'Meta', 'base_ops_chain.zip']
    - path_location: ['bundles', 'head', 'data', 'ops_chain.zip']
    - path_location: ['bundles', 'state', 'cas.cat']
    - path_location: ['bundles', 'state', 'Meta', 'CURRENT']
    - path_location: ['patch', 'ww', 'final', 'streaminstall_chunk.log']
    - path_location: ['patch', 'ww', 'retail', 'streaminstall_chunk.log']
    - path_location: ['patch', 'ww', 'retail', 'BattlefieldGame.Main_Xb1_retail.exe']
    - path_location: ['patch', 'ww', 'retail', 'BattlefieldGame.Main_Xb1_retail.pdb']
    - path_location: []
      min_items: 6
    - path_location: ['bundles']
      min_items: 3
    - path_location: ['bundles', 'delta']
      min_items: 2
    - path_location: ['bundles', 'head', 'data']
      min_items: 10
    - path_location: ['bundles', 'state']
      min_items: 25
    - path_location: ['bundles', 'state', 'Meta']
      min_items: 450

xbsx:
  default:
    base: ['$DESTINATION_BASEPATH$', '$VERSION$', 'xbsx', '$DATACHANGELIST$_$CODECHANGELIST$']
    paths:
    - path_location: ['symbol']
    - path_location: ['bundles', 'delta', 'data', 'initfs_Xbsx']
    - path_location: ['bundles', 'delta', 'meta', 'base_ops_chain.zip']
    - path_location: ['bundles', 'head', 'data', 'ops_chain.zip']
    - path_location: ['bundles', 'state', 'cas.cat']
    - path_location: ['bundles', 'state', 'meta', 'CURRENT']
    - path_location: ['patch', 'ww', 'final', 'streaminstall_chunk.log']
    - path_location: ['patch', 'ww', 'retail', 'streaminstall_chunk.log']
    - path_location: ['patch', 'ww', 'retail', 'BattlefieldGame.Main_Xbsx_retail.exe']
    - path_location: ['patch', 'ww', 'retail', 'BattlefieldGame.Main_Xbsx_retail.pdb']
    - path_location: ['bundles', 'delta', 'data', 'initfs_xbsx']
    - path_location: ['bundles', 'delta', 'meta', 'base_ops_chain.zip']
    - path_location: ['bundles', 'head', 'data', 'ops_chain.zip']
    - path_location: ['bundles', 'state', 'cas.cat']
    - path_location: ['bundles', 'state', 'meta', 'CURRENT']
    - path_location: ['patch', 'ww', 'final', 'streaminstall_chunk.log']
    - path_location: ['patch', 'ww', 'retail', 'streaminstall_chunk.log']
    - path_location: ['patch', 'ww', 'retail', 'BattlefieldGame.Main_Xbsx_retail.exe']
    - path_location: ['patch', 'ww', 'retail', 'BattlefieldGame.Main_Xbsx_retail.pdb']
    - path_location: []
      min_items: 6
    - path_location: ['bundles']
      min_items: 3
    - path_location: ['bundles', 'delta']
      min_items: 2
    - path_location: ['bundles', 'head', 'data']
      min_items: 10
    - path_location: ['bundles', 'state']
      min_items: 25
    - path_location: ['bundles', 'state', 'meta']
      min_items: 450

win64:
  default:
    base: ['$DESTINATION_BASEPATH$', '$VERSION$', 'win64', '$DATACHANGELIST$_$CODECHANGELIST$']
    paths:
    - path_location: ['symbol']
    - path_location: ['bundles', 'delta', 'data', 'initfs_Win32']
    - path_location: ['bundles', 'delta', 'Meta', 'base_ops_chain.zip']
    - path_location: ['bundles', 'head', 'data', 'ops_chain.zip']
    - path_location: ['bundles', 'state', 'cas.cat']
    - path_location: ['bundles', 'state', 'meta', 'CURRENT']
    - path_location: ['patch', 'ww', 'final', 'streaminstall_chunk.log']
    - path_location: ['patch', 'ww', 'retail', 'streaminstall_chunk.log']
    - path_location: ['patch', 'ww', 'retail', 'BattlefieldGame.Main_Win64_retail.map']
    - path_location: ['patch', 'ww', 'retail', 'BattlefieldGame.Main_Win64_retail.pdb']
    - path_location: ['patch', 'ww', 'retail', 'BattlefieldGame.Main_Win64_retail_prot.pdb']
    - path_location: ['patch', 'ww', 'retail', 'BattlefieldGameData.zip']
    - path_location: ['patch', 'ww', 'retail', 'BF2042.exe']
    - path_location: []
      min_items: 7
    - path_location: [ 'bundles']
      min_items: 3
    - path_location: [ 'bundles', 'delta']
      min_items: 2
    - path_location: [ 'bundles', 'delta', 'data']
      min_items: 4
    - path_location: [ 'bundles', 'head', 'data']
      min_items: 10
    - path_location: [ 'bundles', 'state']
      min_items: 23
    - path_location: [ 'bundles', 'state', 'meta']
      min_items: 453
