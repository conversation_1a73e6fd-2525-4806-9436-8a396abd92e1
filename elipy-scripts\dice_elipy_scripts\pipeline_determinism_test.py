"""
pipeline_determinism_test.py
"""
import os

import click

from elipy2 import LOGGER, filer, frostbite_core, core
from elipy2.cli import pass_context
from elipy2.exceptions import ELIPYException
from elipy2.telemetry import collect_metrics
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags


@click.command(
    "pipeline_determinism_test",
    short_help="Runs pipeline_determinism_test script.",
    context_settings=dict(ignore_unknown_options=True),
)
@click.option(
    "--code-branch",
    type=str,
    required=True,
    help="Code branch to fetch tool binaries.",
)
@click.option(
    "--code-changelist",
    type=str,
    required=True,
    help="Code changelist to fetch tool binaries.",
)
@click.option(
    "--script-args",
    type=str,
    default="",
    help="Optional arguments to pass to the command.",
)
@click.option(
    "--p4-port",
    type=str,
    required=True,
    help="P4 PORT for pipeline determinism test script",
)
@pass_context
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    code_branch,
    code_changelist,
    script_args,
    p4_port,
):
    """
    Runs pipeline determinism test script.
    """
    LOGGER.info("Running pipeline_determinism_test...")

    # adding sentry tags
    add_sentry_tags(__file__, "pipeline_determinism_test")

    script_path = os.path.join(
        frostbite_core.get_tnt_root(),
        "Code\\DICE\\BattlefieldGame\\fbcli\\pipeline_determinism_test.py",
    )
    if not os.path.exists(script_path):
        raise ELIPYException("Can not find pipeline determinism script: {}".format(script_path))

    env_vars = {
        "P4PORT": p4_port,
        "P4CONFIG": ".p4config",
    }

    command = [
        "python.exe",
        script_path,
    ]

    if script_args and script_args.strip() and script_args.strip() not in {"''", '""'}:
        command.append(script_args.strip())

    _filer = filer.FilerUtils()
    _filer.fetch_code(code_branch, code_changelist, "pipeline", "release")

    core.run(command, print_std_out=True, env_patch=env_vars)
    LOGGER.info("Done")
