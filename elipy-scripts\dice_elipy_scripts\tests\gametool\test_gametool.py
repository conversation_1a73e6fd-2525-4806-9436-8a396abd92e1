"""
test_gametool.py

Unit testing for gametool
"""

import unittest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import patch, MagicMock
from dice_elipy_scripts.gametool.gametool import cli
from elipy2 import frostbite_core


@patch("dice_elipy_scripts.gametool.gametool.add_sentry_tags", MagicMock())
@patch("elipy2.running_processes.kill", MagicMock())
class TestGametool(unittest.TestCase):
    ARGUMENT_ICEPICK = "icepick"

    OPTION_CODE_CHANGELIST = "--code-changelist"
    OPTION_LICENSEE = "--licensee"
    OPTION_CONFIG = "--config"
    OPTION_FRAMEWORK_ARGS = "--framework-args"
    OPTION_CLEAN = "--clean"
    OPTION_DICE_FRAGMENT = "--dice-fragment"
    OPTION_ICEPICK_FRAGMENT = "--icepick-fragment"

    VALUE_CODE_CHANGELIST = "1234"
    VALUE_LICENSEE = "BattlefieldGame"
    VALUE_CONFIG = "release"
    VALUE_FRAMEWORK_ARGS1 = "-G:test=True"
    VALUE_FRAMEWORK_ARGS2 = "-G:another=True"
    VALUE_CLEAN = "true"
    VALUE_DICE_FRAGMENT = "Code\\DICE\\masterconfig_fragment.xml"
    VALUE_ICEPICK_FRAGMENT = "Automation\\Icepick\\icepick_masterconfig_fragment.xml"

    DEFAULT_ARGS = [
        OPTION_CODE_CHANGELIST,
        VALUE_CODE_CHANGELIST,
        OPTION_LICENSEE,
        VALUE_LICENSEE,
        OPTION_CONFIG,
        VALUE_CONFIG,
        OPTION_FRAMEWORK_ARGS,
        VALUE_FRAMEWORK_ARGS1,
        OPTION_FRAMEWORK_ARGS,
        VALUE_FRAMEWORK_ARGS2,
        OPTION_CLEAN,
        VALUE_CLEAN,
        OPTION_DICE_FRAGMENT,
        VALUE_DICE_FRAGMENT,
        OPTION_ICEPICK_FRAGMENT,
        VALUE_ICEPICK_FRAGMENT,
    ]

    def mocked_set_licensee(self, licensee, framework_args):
        return framework_args

    @patch("elipy2.code.CodeUtils")
    @patch("dice_elipy_scripts.gametool.gametool.set_licensee")
    @patch("elipy2.frostbite_core.minimum_fb_version", return_value=True)
    def test_icepick_with_version_check_true(
        self, mock_min_version, mock_set_licensee, mock_code_utils
    ):
        """Test that Icepick build adds the masterconfig fragment when version >= 2025.1.PR2."""
        mock_set_licensee.side_effect = self.mocked_set_licensee
        mock_clean_local = mock_code_utils.return_value.clean_local = MagicMock()
        mock_nantonpackage = mock_code_utils.return_value.nantonpackage = MagicMock()
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_ICEPICK] + self.DEFAULT_ARGS)
        assert result.exit_code == 0
        mock_clean_local.assert_called_once()
        mock_nantonpackage.assert_called_once_with(
            framework_args=[
                self.VALUE_FRAMEWORK_ARGS1,
                self.VALUE_FRAMEWORK_ARGS2,
                f"-D:change-list={self.VALUE_CODE_CHANGELIST}",
                "-G:frostbite.buildingToolName=Icepick",
                "-G:frostbite.hasCustomSolutionfolders=true",
                "-G:frostbite.target.name=any",
                "-G:package.Icepick.uber-solution=true",
                "-G:vsversion=2022",
                # Format with separate quotes for each path
                '-masterconfigfragments:"Code\\DICE\\masterconfig_fragment.xml";'
                '"Automation\\Icepick\\icepick_masterconfig_fragment.xml"',
            ],
        )

        # Verify that the minimum_fb_version function was called with the correct arguments
        mock_min_version.assert_called_with(year=2025, season=1, version_nr="PR2")

    @patch("elipy2.code.CodeUtils")
    @patch("dice_elipy_scripts.gametool.gametool.set_licensee")
    @patch("elipy2.frostbite_core.minimum_fb_version", return_value=False)
    def test_icepick_with_version_check_false(
        self, mock_min_version, mock_set_licensee, mock_code_utils
    ):
        """Test that Icepick build doesn't add the masterconfig fragment when version < 2025.1.PR2."""
        mock_set_licensee.side_effect = self.mocked_set_licensee
        mock_clean_local = mock_code_utils.return_value.clean_local = MagicMock()
        mock_nantonpackage = mock_code_utils.return_value.nantonpackage = MagicMock()
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_ICEPICK] + self.DEFAULT_ARGS)
        assert result.exit_code == 0
        mock_clean_local.assert_called_once()
        mock_nantonpackage.assert_called_once_with(
            framework_args=[
                self.VALUE_FRAMEWORK_ARGS1,
                self.VALUE_FRAMEWORK_ARGS2,
                f"-D:change-list={self.VALUE_CODE_CHANGELIST}",
                "-G:frostbite.buildingToolName=Icepick",
                "-G:frostbite.hasCustomSolutionfolders=true",
                "-G:frostbite.target.name=any",
                "-G:package.Icepick.uber-solution=true",
                "-G:vsversion=2022",
            ],
        )

        # Verify that the minimum_fb_version function was called with the correct arguments
        mock_min_version.assert_called_with(year=2025, season=1, version_nr="PR2")

    @patch("elipy2.code.CodeUtils")
    @patch("dice_elipy_scripts.gametool.gametool.set_licensee")
    @patch("elipy2.frostbite_core.minimum_fb_version", return_value=True)
    def test_icepick_with_existing_masterconfig(
        self, mock_min_version, mock_set_licensee, mock_code_utils
    ):
        """Test that Icepick build appends the masterconfig fragment to existing fragments."""
        mock_set_licensee.side_effect = self.mocked_set_licensee
        mock_clean_local = mock_code_utils.return_value.clean_local = MagicMock()
        mock_nantonpackage = mock_code_utils.return_value.nantonpackage = MagicMock()

        # Add a masterconfig fragment to the arguments
        custom_dice_fragment = "E:\\dev\\fb2bf\\TnT\\Code\\DICE\\masterconfig_fragment.xml"
        args_with_masterconfig = self.DEFAULT_ARGS.copy()
        args_with_masterconfig.extend(
            [self.OPTION_FRAMEWORK_ARGS, f"-masterconfigfragments:{custom_dice_fragment}"]
        )

        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_ICEPICK] + args_with_masterconfig)
        assert result.exit_code == 0
        mock_clean_local.assert_called_once()

        # Verify that our masterconfig fragment was appended to the existing one
        mock_nantonpackage.assert_called_once()
        framework_args = mock_nantonpackage.call_args[1]["framework_args"]

        icepick_fragment = self.VALUE_ICEPICK_FRAGMENT
        expected_arg = f'-masterconfigfragments:{custom_dice_fragment};"{icepick_fragment}"'

        # Find the masterconfig fragments argument
        masterconfig_args = [arg for arg in framework_args if "-masterconfigfragments:" in arg]
        assert (
            len(masterconfig_args) == 1
        ), "Should have exactly one masterconfig fragments argument"
        assert (
            masterconfig_args[0] == expected_arg
        ), "Should have both fragments separated by semicolon"

        # Verify that the minimum_fb_version function was called with the correct arguments
        mock_min_version.assert_called_with(year=2025, season=1, version_nr="PR2")

    @patch("elipy2.code.CodeUtils")
    @patch("dice_elipy_scripts.gametool.gametool.set_licensee")
    @patch("elipy2.frostbite_core.minimum_fb_version", return_value=True)
    def test_icepick_with_fragment_already_included(
        self, mock_min_version, mock_set_licensee, mock_code_utils
    ):
        """Test that Icepick build doesn't add duplicate fragments."""
        mock_set_licensee.side_effect = self.mocked_set_licensee
        mock_clean_local = mock_code_utils.return_value.clean_local = MagicMock()
        mock_nantonpackage = mock_code_utils.return_value.nantonpackage = MagicMock()

        # Add a masterconfig fragment that already includes the Icepick fragment
        icepick_fragment = self.VALUE_ICEPICK_FRAGMENT
        custom_dice_fragment = "E:\\dev\\fb2bf\\TnT\\Code\\DICE\\masterconfig_fragment.xml"
        combined_fragments = f"{custom_dice_fragment};{icepick_fragment}"

        args_with_both_fragments = self.DEFAULT_ARGS.copy()
        args_with_both_fragments.extend(
            [self.OPTION_FRAMEWORK_ARGS, f"-masterconfigfragments:{combined_fragments}"]
        )

        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_ICEPICK] + args_with_both_fragments)
        assert result.exit_code == 0
        mock_clean_local.assert_called_once()

        # Verify that our masterconfig fragment wasn't duplicated
        mock_nantonpackage.assert_called_once()
        framework_args = mock_nantonpackage.call_args[1]["framework_args"]

        # Find the masterconfig fragments argument
        masterconfig_args = [arg for arg in framework_args if "-masterconfigfragments:" in arg]
        assert (
            len(masterconfig_args) == 1
        ), "Should have exactly one masterconfig fragments argument"
        expected = f"-masterconfigfragments:{combined_fragments}"
        assert masterconfig_args[0] == expected, "Should not modify existing fragments"

        # Verify that the minimum_fb_version function was called with the correct arguments
        mock_min_version.assert_called_with(year=2025, season=1, version_nr="PR2")

    @patch("elipy2.code.CodeUtils")
    @patch("dice_elipy_scripts.gametool.gametool.set_licensee")
    @patch("elipy2.frostbite_core.minimum_fb_version", return_value=True)
    def test_icepick_with_custom_fragment_paths(
        self, mock_min_version, mock_set_licensee, mock_code_utils
    ):
        """Test that Icepick build uses custom fragment paths when provided."""
        mock_set_licensee.side_effect = self.mocked_set_licensee
        mock_clean_local = mock_code_utils.return_value.clean_local = MagicMock()
        mock_nantonpackage = mock_code_utils.return_value.nantonpackage = MagicMock()

        # Use custom fragment paths
        custom_dice_fragment = "Custom\\Path\\To\\DICE\\masterconfig_fragment.xml"
        custom_icepick_fragment = "Custom\\Path\\To\\Icepick\\icepick_masterconfig_fragment.xml"

        # Create args with custom fragment paths
        args_with_custom_paths = self.DEFAULT_ARGS.copy()

        # Replace the default fragment paths with custom ones
        dice_index = args_with_custom_paths.index(self.VALUE_DICE_FRAGMENT)
        icepick_index = args_with_custom_paths.index(self.VALUE_ICEPICK_FRAGMENT)
        args_with_custom_paths[dice_index] = custom_dice_fragment
        args_with_custom_paths[icepick_index] = custom_icepick_fragment

        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_ICEPICK] + args_with_custom_paths)
        assert result.exit_code == 0
        mock_clean_local.assert_called_once()

        # Verify that our custom fragment paths were used
        mock_nantonpackage.assert_called_once()
        framework_args = mock_nantonpackage.call_args[1]["framework_args"]

        # Find the masterconfig fragments argument
        masterconfig_args = [arg for arg in framework_args if "-masterconfigfragments:" in arg]
        assert len(masterconfig_args) == 1, "Should have one masterconfig fragments argument"

        # Format with separate quotes for each path
        expected_arg = (
            f'-masterconfigfragments:"{custom_dice_fragment}";"{custom_icepick_fragment}"'
        )
        assert masterconfig_args[0] == expected_arg, "Should use the custom fragment paths"

        # Verify that the minimum_fb_version function was called with the correct arguments
        mock_min_version.assert_called_with(year=2025, season=1, version_nr="PR2")
