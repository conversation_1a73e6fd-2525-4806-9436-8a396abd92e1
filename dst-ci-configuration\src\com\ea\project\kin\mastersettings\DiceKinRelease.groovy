package com.ea.project.kin.mastersettings

import com.ea.project.kin.Kingston

class DiceKinRelease {
    static Class project = Kingston
    static Map branches = [
        'kin-stage'      : [code_folder: 'stage', code_branch: 'kin-stage', data_folder: 'stage', data_branch: 'kin-stage'],
        'kin-release'    : [code_folder: 'release', code_branch: 'kin-release', data_folder: 'release', data_branch: 'kin-release'],
        'kin-live'       : [code_folder: 'release', code_branch: 'kin-live', data_folder: 'release', data_branch: 'kin-live'],
        'kin-live-server': [code_folder: 'release', code_branch: 'kin-live-server', data_folder: 'release', data_branch: 'kin-live-server', non_virtual_data_branch: 'kin-live', non_virtual_code_branch: 'kin-live'],
    ]
    static Map preflight_branches = [:]
    static Map autotest_branches = [:]
    static Map integrate_branches = [
        'kin-release_to_kin-stage': [
            cook_before_submit          : true,
            source_folder               : 'release', source_branch: 'kin-release',
            target_folder               : 'stage', target_branch: 'kin-stage',
            code                        : true, data: true, parent_to_child: true, no_submit: false,
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call,
            workspace_root              : project.workspace_root, slack_channel: '#kin-integration-notify',
            trigger_type_integrate      : 'scm',
            freestyle_job_trigger_matrix: [],
        ],
    ]
    static Map copy_branches = [
        'kin-dev_to_kin-stage'    : [
            source_folder               : 'dev', source_branch: 'kin-dev',
            target_folder               : 'stage', target_branch: 'kin-stage',
            code                        : true, data: true, parent_to_child: false,
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call,
            workspace_root              : project.workspace_root, slack_channel: '#kin-integration-notify',
            trigger_type_copy           : 'stop', trigger_string_copy: 'H 3,9,15,21 * * *',
            p4_data_force_copy          : '--force',
            freestyle_job_trigger_matrix: [],
        ],
        'kin-stage_to_kin-release': [
            source_folder               : 'stage', source_branch: 'kin-stage',
            target_folder               : 'release', target_branch: 'kin-release',
            code                        : true, data: true, parent_to_child: false,
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call,
            workspace_root              : project.workspace_root, slack_channel: '#kin-integration-notify',
            trigger_type_copy           : 'stop', trigger_string_copy: 'H 5,11,17,23 * * *',
            freestyle_job_trigger_matrix: [],
        ],
        'kin-release_to_kin-live' : [
            source_folder               : 'release', source_branch: 'kin-release',
            target_folder               : 'release', target_branch: 'kin-live',
            code                        : true, data: true, parent_to_child: false,
            elipy_call                  : project.elipy_call, elipy_install_call: project.elipy_install_call,
            workspace_root              : project.workspace_root, slack_channel: '#kin-integration-notify',
            freestyle_job_trigger_matrix: [],
        ],
    ]
    static Map feature_integrations = [:]
    static Map maintenance_branch = [
        'kin-dev': [code_folder: 'dev', code_branch: 'kin-dev', data_folder: 'dev', data_branch: 'kin-dev', avalanche_maint: true],
    ]
    static List dashboard_list = []
    static Map dvcs_configs = [:]
    static List code_downstream_matrix = []
    static Map MAINTENANCE_SETTINGS = [:]
}
