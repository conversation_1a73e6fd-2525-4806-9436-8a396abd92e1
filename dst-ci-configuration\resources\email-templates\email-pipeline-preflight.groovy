<%
import groovy.xml.MarkupBuilder
import hudson.model.AbstractBuild
import hudson.model.Cause
import hudson.model.Cause.UpstreamCause
import hudson.model.Cause.UserIdCause
import hudson.model.CauseAction
import hudson.model.Job
import hudson.model.Run
import hudson.triggers.SCMTrigger.SCMTriggerCause
import hudson.triggers.TimerTrigger.TimerTriggerCause
import java.io.StringWriter
import java.util.LinkedList
import java.util.List
import java.util.Queue
import java.util.Set
import jenkins.model.Jenkins
import org.jenkinsci.plugins.displayurlapi.ClassicDisplayURLProvider
import org.jenkinsci.plugins.displayurlapi.DisplayURLProvider
import org.jenkinsci.plugins.workflow.job.WorkflowRun
import org.jenkinsci.plugins.workflow.flow.FlowExecution;
import org.jenkinsci.plugins.workflow.graph.FlowGraphWalker;
import org.jenkinsci.plugins.workflow.graph.FlowNode;
import org.jenkinsci.plugins.workflow.graph.StepStartNode;
import org.jenkinsci.plugins.workflow.cps.nodes.StepStartNode;
import org.jenkinsci.plugins.workflow.actions.WorkspaceAction


public interface IEmailComponent {
    // render component specific data for the run to the markup builder

    public void render(Run run, MarkupBuilder builder)

    // returns if the component is applicable to the current run

    public boolean isApplicable(Run run)

    // returns the embedded styles specific to the component

    public String getEmbeddedStyle(Run run)
}


public class JobUtil {

    /**
    * getClassicDisplayURL returns the Classic URL for the given run
    *
    * @param run
    *            the Run
    *
    * @return the Classic Run URL as a String
    */

    static String getClassicDisplayURL(Run run) {
        def runURL = null
        def classicDisplayURLProvider = DisplayURLProvider.all().find { it instanceof ClassicDisplayURLProvider }
        if(classicDisplayURLProvider) {
            runURL = classicDisplayURLProvider.getRunURL(run)
        }
        return runURL
    }

    /**
    * getPrimaryCause returns the primary cause
    * for the given run in order of priority:
    * - UpstreamCause
    * - UserIdCause
    * - SCMTriggerCause
    * - TimerTriggerCause
    *
    * @param run
    *            the Run instance to find a primary
    *            cause for
    *
    * @return the primary cause or null if no causes found
    */

    static Cause getPrimaryCause(Run run) {
        def causeAction = run.getAction(CauseAction.class)
        if(causeAction) {
            def upstreamCause = causeAction.findCause(UpstreamCause.class)
            if(upstreamCause) {
                return upstreamCause
            }
            def userCause = causeAction.findCause(UserIdCause.class)
            if(userCause) {
                return userCause
            }
            def scmTriggerCause = causeAction.findCause(SCMTriggerCause.class)
            if(scmTriggerCause) {
                return scmTriggerCause
            }
            def timeTriggerCause = causeAction.findCause(TimerTriggerCause.class)
            if(timeTriggerCause) {
                return timeTriggerCause
            }
        }
        return null
    }

    /**
    * getRootUpstreamCause returns the root cause
    * for the given run, traversing upstream causes
    * if they exist
    *
    * @param run
    *            the Run instance to find a root
    *            upstream cause for
    *
    * @return the root upstream cause or null if no causes found
    */

    static Cause getRootUpstreamCause(Run run) {
        def cause = getPrimaryCause(run)
        if(cause instanceof UpstreamCause) {
            def upstreamCause = (UpstreamCause) cause
            def upstreamRun = upstreamCause.getUpstreamRun()
            if(upstreamRun) {
                return getRootUpstreamCause(upstreamRun)
            }
            return upstreamCause
        }
        return getPrimaryCause(run)
    }

    /**
    * helper method for getting builds started by a pipeline job
    *
    * @return List of Run objects descending from upstreamRun
    */
    private static List<Run> getWorkflowRuns (Run upstreamRun) {
        // find all jobs that were scheduled within the timespan of the upstream run
        // and that have a BuildUpstreamNodeAction with an upstreamRunId matching
        // the upstreamRun's externalizableId: <projectName>#<runId>
        def startTime = upstreamRun.getStartTimeInMillis()

        // by default use the current System time as the endTime, but
        // if the upstreamRun has completed, calculate the endTime
        // as startTime + duration to narrow the timespan searched
        def endTime = System.currentTimeMillis()
        if(!upstreamRun.isBuilding()) {
            endTime = startTime + upstreamRun.getDuration()
        }

        def jenkins = Jenkins.getInstanceOrNull();
        def upstreamRunId = upstreamRun.getExternalizableId()
        return jenkins.getAllItems(Job.class).collectMany { job ->
            job.getBuilds().byTimestamp(startTime, endTime).findAll { r ->
                r.getActions(org.jenkinsci.plugins.workflow.support.steps.build.BuildUpstreamNodeAction.class).any { it.getUpstreamRunId() == upstreamRunId }
            }
        }
    }

    /**
    * getDownstreamRuns returns a list of downstream runs
    * of the given run instance, sorted by start time
    *
    * @param runWrapper
    *            the parent run
    *
    * @return a never null but possibly empty list of downstream runs
    */

    static List<Run> getDownstreamRuns(Run upstreamRun) {
        def downstreamRuns = []
        def jenkins = Jenkins.getInstanceOrNull()
        if(jenkins) {
            downstreamRuns.addAll(getWorkflowRuns(upstreamRun));
        }
        // sort by start time
        downstreamRuns.removeAll([null])
        return downstreamRuns.toSorted { a, b -> a.getStartTimeInMillis() <=> b.getStartTimeInMillis() }
    }
}


public class EmailNotification {

    private List<IEmailComponent> registeredComponents
    private ClassLoader classLoader
    private Run run
    String basePath

    public EmailNotification(Run run) {
        classLoader = this.getClass().getClassLoader()
        this.run = run
        // Get the basePath
        def run_execution = run.getExecution()
        /*
        if(run_execution != null) {
            FlowGraphWalker run_walker = new FlowGraphWalker(run_execution)
            for(FlowNode run_node : run_walker) {
                if(run_node instanceof StepStartNode) {
                    def action = run_node.getAction(WorkspaceAction)
                    if(action) {
                        basePath = action.getPath().toString() + '/email-templates/include'
                    }
                }
            }
        }
        */
        basePath = run.getEnvironment(TaskListener.NULL).JENKINS_HOME + '/email-templates/include'
        registeredComponents = new ArrayList<IEmailComponent>()
        registerComponents()
    }


    void registerComponents() {

        // Note: registration order is render order

        // Build name and result
        registeredComponents.add(classLoader.parseClass(new File("${basePath}/PLBuildTitleComponent.groovy")).newInstance())

        // Basic build information
        registeredComponents.add(classLoader.parseClass(new File("${basePath}/PLBuildSummaryComponent.groovy")).newInstance())

        // Downstream runs
        registeredComponents.add(classLoader.parseClass(new File("${basePath}/PLDownstreamRunsComponent.groovy")).newInstance())

        // Error logs (if non SUCCESS)
        registeredComponents.add(classLoader.parseClass(new File("${basePath}/PLErrorLogComponent.groovy")).newInstance())

        // Links to published shares or jenkins artifacts
        registeredComponents.add(classLoader.parseClass(new File("${basePath}/PLArtifactsComponent.groovy"))
            .getDeclaredConstructor(ClassLoader.class, List.class)
            .newInstance(classLoader, ["${basePath}/PLConsoleOutputArtifact.groovy"]))
    }


    public void render(MarkupBuilder builder) {

        // Header, structure, style(s)
        classLoader.parseClass(new File("${basePath}/PLHeaderComponent.groovy")).newInstance(registeredComponents).render(run, builder)

        // Section, content
        classLoader.parseClass(new File("${basePath}/PLBodyComponent.groovy")).newInstance(registeredComponents).render(run, builder)
    }


    public String generate() {
        def writer = new StringWriter()
        def htmlBuilder = new MarkupBuilder(writer)
        htmlBuilder.html() {
            render(htmlBuilder)
        }

        return writer.toString()
    }
}

/*** main() ***/
def emailBody = new EmailNotification(build).generate()
%>
${emailBody}
