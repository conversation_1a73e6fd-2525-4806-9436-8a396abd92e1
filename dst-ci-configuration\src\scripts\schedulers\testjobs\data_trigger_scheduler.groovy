package scripts.schedulers.testjobs

import com.ea.project.GetBranchFile

def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
def project = ProjectClass(env.project_name)

/**
 * data_trigger_scheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Get changelist from Perforce') {
            steps {
                script {
                    def settings_map = branchfile.general_settings + branchfile.standard_jobs_settings
                    P4PreviewData(project, 'stream', env.data_folder, env.data_branch, env.non_virtual_data_folder, env.non_virtual_data_branch, settings_map)
                }
            }
        }
        stage('Trigger data jobs') {
            steps {
                script {
                    def data_changelist = env.P4_CHANGELIST
                    def clean_data = params.clean_data

                    echo 'Data changelist synced from Perforce: ' + data_changelist

                    if (data_changelist == null) {
                        echo 'Missing data changelist, aborting build!'
                        currentBuild.result = Result.UNSTABLE.toString()
                        return
                    }

                    def inject_map = [
                        'data_changelist': data_changelist,
                    ]
                    EnvInject(currentBuild, inject_map)
                    currentBuild.displayName = env.JOB_NAME + '.' + data_changelist

                    echo 'Clean data: ' + clean_data
                    echo 'Retry limit: ' + env.retry_limit

                    def cause_action = currentBuild.rawBuild.getAction(CauseAction)
                    if (cause_action.findCause(hudson.model.Cause.UpstreamCause)) {
                        echo 'Triggered by an upstream job.'
                    } else if (cause_action.findCause(hudson.model.Cause.UserIdCause)) {
                        echo 'Triggered manually by a user.'
                    } else if (cause_action.findCause(hudson.triggers.SCMTrigger.SCMTriggerCause)) {
                        echo 'Triggered by an SCM change.'
                    }
                }
            }
        }
    }
}
