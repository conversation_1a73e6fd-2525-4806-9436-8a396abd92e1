{#
    Command:
        webexport
            short_help: Performs a webexport build.

    Arguments:

    Required variables:
        branch_name
            required: True
            help: Which branch to save as bf-hotfix, bf-release etc.
        script_path
            required: True

    Optional variables:
        platform
            help: Platform to be built, default win64.
            default: win64
        asset
            help: Asset that will be build, default is RetailLevels.
            default: RetailLevels
        code_branch
            help: Branch/stream to fetch the code/binary build from.
        code_changelist
            help: Changelist of binaries to fetch.
        data_changelist
            help: Changelist of data being used.
        save_to_filer/__dont_save_to_filer
            default: True
            help: Flag for writing webexport output to filer.
        nuke
            is_flag: True
            help: Nuke avalanche before run.
        data_dir
            default: bfdata
        force_build/__not_force_build
            default: False
            help: Flag for setting force build arg.
        levels_file/__not_levels_file
            default: True
            help: Flag for setting writeBuiltLevelsFile arg.
        data_clean
            default: false
            help: Clean Avalanche if --data-clean true is passed.
        import_avalanche_state
            is_flag: True
            help: Imports Avalanche state from filer.
        aws_access_key_id
            default: invalid key
            help: AWS acceess key id.
        aws_secret_key
            default: invalid key
            help: AWS secret access key.
        skip_aws_upload
            is_flag: True
            help: Skip uploading files to AWS.
        clean_master_version_check
            is_flag: True
            help: Run clean on master version update.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    webexport
    --branch-name {{ branch_name }}
    --script-path {{ script_path }}
    {%- if platform %}
    --platform {{ platform }}
    {%- endif %}
    {%- if asset %}
    --asset {{ asset }}
    {%- endif %}
    {%- if code_branch %}
    --code-branch {{ code_branch }}
    {%- endif %}
    {%- if code_changelist %}
    --code-changelist {{ code_changelist }}
    {%- endif %}
    {%- if data_changelist %}
    --data-changelist {{ data_changelist }}
    {%- endif %}
    {%- if save_to_filer/__dont_save_to_filer %}
    --save-to-filer/--dont-save-to-filer {{ save_to_filer/__dont_save_to_filer }}
    {%- endif %}
    {%- if nuke %}
    --nuke {{ nuke }}
    {%- endif %}
    {%- if data_dir %}
    --data-dir {{ data_dir }}
    {%- endif %}
    {%- if force_build/__not_force_build %}
    --force-build/--not-force-build {{ force_build/__not_force_build }}
    {%- endif %}
    {%- if levels_file/__not_levels_file %}
    --levels-file/--not-levels-file {{ levels_file/__not_levels_file }}
    {%- endif %}
    {%- if data_clean %}
    --data-clean {{ data_clean }}
    {%- endif %}
    {%- if import_avalanche_state %}
    --import-avalanche-state {{ import_avalanche_state }}
    {%- endif %}
    {%- if aws_access_key_id %}
    --aws-access-key-id {{ aws_access_key_id }}
    {%- endif %}
    {%- if aws_secret_key %}
    --aws-secret-key {{ aws_secret_key }}
    {%- endif %}
    {%- if skip_aws_upload %}
    --skip-aws-upload {{ skip_aws_upload }}
    {%- endif %}
    {%- if clean_master_version_check %}
    --clean-master-version-check {{ clean_master_version_check }}
    {%- endif %}
  displayName: elipy webexport
