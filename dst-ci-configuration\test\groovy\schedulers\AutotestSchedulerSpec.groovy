package schedulers

import com.ea.lib.model.autotest.AutotestCategory
import com.ea.matrixfiles.AutotestMatrix
import hudson.model.Result
import spock.lang.Unroll
import support.DeclarativePipelineSpockTest

class AutotestSchedulerSpec extends DeclarativePipelineSpockTest {

    Map changelists = [
        ps4: [
            dataChangelist: '123',
            codeChangelist: '234',
            clientBuildId : '\\\\client\\build\\id',
            serverBuildId : '\\\\server\\build\\id',
        ]
    ]

    void setup() {
        binding.setVariable('env', [
            autotestMatrix        : 'TestAutotestMatrix',
            branchName            : 'kin-dev',
            BUILD_URL             : 'https://url.com',
            dataset               : 'kindata',
            lkg_p4_counters_enabled : 'false',
            projectName           : 'kingston',
            set_integration_info  : 'false',
            testCategory          : 'build-tests',
            target_build_info     : changelists.inspect(),
        ])
        AutotestMatrix mockMatrix = GroovyMock()
        AutotestCategory mockCategory = GroovyMock()
        mockCategory.runBilbo >> true
        mockCategory.testDefinition >> 'awesomeTest'
        mockCategory.slackChannel >> '#cobra-test'
        mockCategory.testInfo >> [:]
        mockMatrix.getSlackSettings(_ as String) >> [:]
        mockMatrix.getTestCategory(_ as String, _ as String) >> mockCategory
        helper.with {
            registerAllowedMethod('AutotestRunCategory', [Object, String, String, Map, List])
            registerAllowedMethod('AutotestRegisterBilboResult', [boolean, boolean, String, String, String, Map, String, Result, List])
            registerAllowedMethod('AutotestSetPipelineResult', [Map, Result])
            registerAllowedMethod('ProjectClass', [String]) { projectName -> [short_name: 'kin'] }
            registerAllowedMethod('getInstance', [String]) { name -> mockMatrix }
            registerAllowedMethod('trigger', [AutotestMatrix, String, String]) { Result.SUCCESS }
        }
    }

    @Unroll
    void 'test autotest_scheduler to post #status to Bilbo when step result is #currentResult '() {
        given:
        helper.registerAllowedMethod('retryOnFailureCause', [Integer, List, Closure]) { retryCount, list, closure ->
            closure.call()
            return stepResult
        }
        when:
        runScript('autotest_scheduler.groovy')
        printCallStack()
        then:
        assertCalledWith('AutotestRegisterBilboResult',
            'true',
            'false',
            status,
            'kin-dev',
            'kindata',
            changelists,
            'awesomeTest',
            currentResult.toString(),
            []
        )
        assertJobStatusSuccess()
        testNonRegression("data_preflight_scheduler_bilbo_${currentResult}")
        where:
        stepResult     || status   | currentResult
        Result.SUCCESS || 'done'   | Result.SUCCESS
        Result.FAILURE || 'failed' | Result.FAILURE
    }

}
