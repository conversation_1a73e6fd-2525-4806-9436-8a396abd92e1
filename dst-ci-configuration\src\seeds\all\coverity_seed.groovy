package all

import com.ea.lib.LibCommonCps
import com.ea.lib.LibJobDsl
import com.ea.lib.LibScm
import com.ea.lib.jobs.LibCoverity
import com.ea.project.GetBranchFile
import com.ea.project.GetMasterFile
import com.ea.project.all.All

GetMasterFile.get_masterfile(BUILD_URL).each { masterSettings ->
    def branches = masterSettings.branches

    branches.each { String currentBranch, info ->
        out.println("   Processing branch: $currentBranch")
        def project = masterSettings.project
        if (All.isAssignableFrom(project)) {
            project = info.project
        }
        def branchFile = GetBranchFile.get_branchfile(project.name, currentBranch)
        def generalSettings = branchFile.general_settings
        def standardJobsSettings = branchFile.standard_jobs_settings
        def branchInfo = info + generalSettings + standardJobsSettings + [branch_name: currentBranch, project: project]
        def freestyle_jobs = []
        branchInfo += [p4_code_creds: branchInfo.coverity_settings?.p4_code_creds ?: project.p4_code_creds]
        branchInfo += [p4_code_server: branchInfo.coverity_settings?.p4_code_server ?: project.p4_code_server]

        // Start and check jobs
        if (branchInfo.coverity_settings?.run_coverity) {
            out.println('       Processing coverity start job')
            def coverityStart = pipelineJob("${currentBranch}.coverity.start") {
                definition {
                    cps {
                        script(readFileFromWorkspace('src/scripts/schedulers/all/CoverityScheduler.groovy'))
                        sandbox(true)
                    }
                }
            }
            LibCoverity.start(coverityStart, project, branchFile, masterSettings, currentBranch)
            out.println('       Processing Coverity')
            def runCoverity = job("${currentBranch}.coverity") {}
            freestyle_jobs.add(runCoverity)
            LibCoverity.coverityJob(runCoverity, project, branchFile, masterSettings, currentBranch)
            LibScm.sync_code(runCoverity, project, branchInfo, '${CODE_CHANGELIST}')
            LibJobDsl.kill_processes(runCoverity, branchInfo)
            LibJobDsl.initialP4revert(runCoverity, project, branchInfo, true, false)
            LibJobDsl.addVaultSecrets(runCoverity, branchInfo)
            LibJobDsl.archive_non_build_logs(runCoverity, branchInfo)
            LibJobDsl.postclean_silverback(runCoverity, project, branchInfo)
        }
        LibCommonCps.add_downstream_freestyle_job_triggers(freestyle_jobs, currentBranch, branchFile.freestyle_job_trigger_matrix)
    }
}
