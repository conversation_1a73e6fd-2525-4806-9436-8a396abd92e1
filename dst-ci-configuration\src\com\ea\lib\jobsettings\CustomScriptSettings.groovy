package com.ea.lib.jobsettings

import com.ea.lib.model.branchsettings.CustomScriptConfiguration

class CustomScriptSettings extends JobSetting {

    String argumentDescription
    String jobName
    String defaultScriptArgs

    void initializeStart(def branchFile, def masterFile, def projectFile, String branchName, String settingKey) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        CustomScriptConfiguration customScriptConfiguration = (CustomScriptConfiguration) this.branchInfo.custom_script[settingKey]
        description = "Runs the following command: ${customScriptConfiguration.command}"
        argumentDescription = customScriptConfiguration.argumentDescription
        defaultScriptArgs = customScriptConfiguration.defaultScriptArgs
        cronTrigger = customScriptConfiguration.cronTrigger
    }

    void initializeJob(def branchFile, def masterFile, def projectFile, String branchName, String settingKey) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        CustomScriptConfiguration customScriptConfiguration = (CustomScriptConfiguration) this.branchInfo.custom_script[settingKey]
        timeoutMinutes = customScriptConfiguration.timeoutMinutes
        jobLabel = customScriptConfiguration.label
        isDisabled = !customScriptConfiguration.enabled
        description = "Runs the following command: ${customScriptConfiguration.command}"
        argumentDescription = customScriptConfiguration.argumentDescription
        defaultScriptArgs = customScriptConfiguration.defaultScriptArgs
        buildName = '${JOB_NAME}.${ENV, var="code_changelist"}'
        jobName = customScriptConfiguration.jobName ?: settingKey
        elipyCmd = "${this.elipyCall} custom_script --executable \"${customScriptConfiguration.executable}\"" +
            " --executable-args \"${customScriptConfiguration.executableArgs}\"" +
            " --script-path \"${customScriptConfiguration.scriptPath}\" --script-args \"%script_args%\"" +
            " --env-variables \"${customScriptConfiguration.environmentVariables}\""
    }
}
