"""
test_bilbo_register_verified_for_preflight.py

Unit testing for bilbo_register_verified_for_preflight
"""
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import patch, MagicMock
from dice_elipy_scripts.bilbo_register_verified_for_preflight import cli


@patch("elipy2.filer_paths.get_code_build_root_path", MagicMock(return_value="PATH"))
class TestBilboRegisterVerifiedForPreflight:
    OPTION_BRANCH = "--branch"
    OPTION_CHANGE_LIST = "--changelist"

    VALUE_BRANCH = "branch"
    VALUE_CHANGE_LIST = "123"

    def test_register_verified_for_preflight(self, fixture_metadata_manager):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.OPTION_BRANCH,
                self.VALUE_BRANCH,
                self.OPTION_CHANGE_LIST,
                self.VALUE_CHANGE_LIST,
            ],
        )
        assert result.exit_code == 0
        fixture_metadata_manager.tag_build_as_verified_for_preflight.assert_called_once_with("PATH")

    def test_should_not_register_verified_for_preflight_if_branch_is_missing(
        self, fixture_metadata_manager
    ):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.OPTION_CHANGE_LIST,
                self.VALUE_CHANGE_LIST,
            ],
        )
        assert result.exit_code != 0
        fixture_metadata_manager.tag_build_as_verified_for_preflight.assert_not_called()

    def test_should_not_register_verified_for_preflight_if_changelist_is_missing(
        self, fixture_metadata_manager
    ):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [
                self.OPTION_BRANCH,
                self.VALUE_BRANCH,
            ],
        )
        assert result.exit_code != 0
        fixture_metadata_manager.tag_build_as_verified_for_preflight.assert_not_called()
