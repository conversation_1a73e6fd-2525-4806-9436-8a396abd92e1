"""
test_coverity.py

Unit testing for coverity
"""
import os
from unittest.mock import patch

from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock.mock import MagicMock

from dice_elipy_scripts.coverity import cli


@patch("os.path.join", MagicMock(side_effect=lambda *args: "\\".join(args)))
@patch("os.path.exists", MagicMock(return_value=False))
@patch("os.sep", "\\")
@patch.dict(os.environ, {"PATH": "path"})
@patch("os.getenv", MagicMock(side_effect=lambda var: var))
@patch("elipy2.frostbite_core.get_tnt_root", MagicMock(return_value="h:\\dev\\tnt"))
@patch(
    "elipy2.artifactory_client.ArtifactoryClient.download_artifact",
    MagicMock(return_value="game_root\\Coverity\\cov-analysis-win64-2023.3.2"),
)
@patch("elipy2.secrets.get_secrets", MagicMock())
@patch("elipy2.core.robocopy", MagicMock())
@patch("dice_elipy_scripts.coverity.add_sentry_tags", MagicMock())
class TestCoverity:
    OPTION_CODE_BRANCH = "--code-branch"
    OPTION_CODE_CHANGELIST = "--code-changelist"
    OPTION_CONFIG = "--config"
    OPTION_ARTIFACTORY_USER = "--artifactory-user"
    OPTION_ARTIFACTORY_APIKEY = "--artifactory-apikey"
    OPTION_ARTIFACTORY_COVERITY_SOURCE_PATH = "--artifactory-coverity-source-path"
    OPTION_COVERITY_USER = "--coverity-user"
    OPTION_COVERITY_PASSWORD = "--coverity-password"
    OPTION_CLEAN = "--clean"

    VALUE_CODE_BRANCH = "code_branch"
    VALUE_CODE_CHANGELIST = "code_changelist"
    VALUE_CONFIG = "release"
    VALUE_ARTIFACTORY_USER = "<EMAIL>"
    VALUE_ARTIFACTORY_APIKEY = "apikey"
    VALUE_ARTIFACTORY_COVERITY_SOURCE_PATH = (
        "dreeu-generic-local/coverity/cov-analysis-win64-2023.3.2.zip"
    )
    VALUE_COVERITY_USER = "cov-user"
    VALUE_COVERITY_PASSWORD = "cov-password"
    VALUE_CLEAN = "true"

    DEFAULT_ARGS = [
        OPTION_CODE_BRANCH,
        VALUE_CODE_BRANCH,
        OPTION_CODE_CHANGELIST,
        VALUE_CODE_CHANGELIST,
        OPTION_CONFIG,
        VALUE_CONFIG,
        OPTION_ARTIFACTORY_USER,
        VALUE_ARTIFACTORY_USER,
        OPTION_ARTIFACTORY_APIKEY,
        VALUE_ARTIFACTORY_APIKEY,
        OPTION_ARTIFACTORY_COVERITY_SOURCE_PATH,
        VALUE_ARTIFACTORY_COVERITY_SOURCE_PATH,
        OPTION_COVERITY_USER,
        VALUE_COVERITY_USER,
        OPTION_COVERITY_PASSWORD,
        VALUE_COVERITY_PASSWORD,
        OPTION_CLEAN,
        VALUE_CLEAN,
    ]

    @patch("elipy2.core.run")
    @patch("dice_elipy_scripts.coverity.code.CodeUtils")
    def test_coverity(
        self,
        mock_code_utils: MagicMock,
        mock_core_run: MagicMock,
    ):
        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)

        mock_code_utils.assert_any_call(
            platform="win64game",
            config=self.VALUE_CONFIG,
            monkey_build_label=self.VALUE_CODE_CHANGELIST,
        )
        mock_builder = mock_code_utils.return_value

        mock_builder.clean_local.assert_called_once_with(close_handles=True)
        mock_builder.gensln.assert_any_call(
            framework_args=["-D:nant.p4protocol.timeout.ms=240000"], nomaster=False
        )
        assert mock_builder.gensln.call_count == 2

        coverity_bin_path = "game_root\\Coverity\\cov-analysis-win64-2023.3.2\\bin"
        expected_env_patch = {"PATH": f"path;{coverity_bin_path}"}

        generate_config_cmd = [
            "cov-configure",
            "--config",
            "game_root\\Coverity\\cov-analysis-win64-2023.3.2\\config\\coverity_config.xml",
            "--msvc",
        ]
        mock_core_run.assert_any_call(
            cmd=generate_config_cmd, print_std_out=True, env_patch=expected_env_patch
        )

        build_cmd = [
            "cov-build",
            "--disable-ms-pch",
            "--no-log-server",
            "--dir",
            "h:\\dev\\tnt\\Local\\coverity-out",
            "fb",
            "buildsln",
            "win64",
            self.VALUE_CONFIG,
            # "rebuild",
        ]
        mock_core_run.assert_any_call(
            cmd=build_cmd, print_std_out=True, env_patch=expected_env_patch
        )

        import_scm_cmd = [
            "cov-import-scm",
            "--dir",
            "h:\\dev\\tnt\\Local\\coverity-out",
            "--scm",
            "perforce",
            "--filename-regex",
            "TnT",
            "--verbose",
            "0",
        ]
        mock_core_run.assert_any_call(
            cmd=import_scm_cmd, print_std_out=True, env_patch=expected_env_patch
        )

        analyze_cmd = [
            "cov-analyze",
            "-t",
            ".\\temp",
            "-j",
            "auto",
            "--dir",
            "h:\\dev\\tnt\\Local\\coverity-out",
            "--security",
            "--concurrency",
            "--enable-callgraph-metrics",
            "--paths",
            "100000",
            "--all",
            "--enable-constraint-fpp",
            "--strip-path",
            "/dev/na",
            "--disable-parse-warnings",
        ]
        mock_core_run.assert_any_call(
            cmd=analyze_cmd, print_std_out=True, env_patch=expected_env_patch
        )

        commit_defects_cmd = [
            "cov-commit-defects",
            "--dir",
            "h:\\dev\\tnt\\Local\\coverity-out",
            "--description",
            f"FB-eac-freeze-{self.VALUE_CODE_BRANCH}",
            "--scm",
            "perforce",
            "--url",
            "https://dice-coverity.dice.ad.ea.com",
            "--user",
            "%COVERITY_USER%",
            "--password",
            "%COVERITY_PASSWORD%",
            "--stream",
            f"Frostbite.{self.VALUE_CODE_BRANCH}.win64.nightly",
        ]

        expected_env_patch["COVERITY_USER"] = "cov-user"
        expected_env_patch["COVERITY_PASSWORD"] = "cov-password"

        mock_core_run.assert_any_call(
            cmd=commit_defects_cmd, print_std_out=True, env_patch=expected_env_patch
        )

        assert mock_core_run.call_count == 6
        assert result.exit_code == 0
