package scripts.schedulers.all

/**
 * integration_changelist_scheduler.groovy
 */
pipeline {
    agent any
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Trigger the downstream job') {
            steps {
                script {
                    currentBuild.displayName = env.JOB_NAME + '.' + params.data_changelist
                    def args = []
                    if (!env.smoke_integrate_at_latest.toBoolean()) {
                        args = [
                            string(name: 'data_changelist', value: params.data_changelist),
                        ]
                    }
                    if (env.smoke_downstream_jobs != '') {
                        downstream_jobs = env.smoke_downstream_jobs.split(',')
                        for (job_name in downstream_jobs) {
                            build(job: job_name, wait: false, parameters: args, propagate: false)
                        }
                    }
                }
            }
        }
    }
}
