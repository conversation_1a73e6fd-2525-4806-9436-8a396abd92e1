"""
submit_to_shift.py

<PERSON><PERSON> to handle submits of finished builds to Shift.
Shift settings are handled in a yaml config in the
elipy-scripts repository. We first create a sku
for the correct game on the shift webb page, then
we look at the submission template to find the sku_id
for that build. This we set under settings in the settings
file to be applied when building the matching platform/config/region.
In the file we also specify which files/suplemental files / folder
that should be included for each shift upload for the build.
There’s also some other settings we can set.

Shift module then looks on filer for builds that it can shift
on the supplied path, for each of these it tries to find
matching settings in the settings file. If there’s a
valid sku-id for the build, we try to shift it.
Shifted builds are registered in the configured metadata service,
we use this to check so we don’t shift the same build twice.

There’s some validation done before uploading. For example
that for retail builds we have all the files listed for
that build, otherwise we fail the upload.

When shifting we print a shift template with all settings.
Copy all files and folders needed to a temp location and then
we call the shift upload exe tool that uses the template info to upload the build.

General setup:
    - For jobs where we send basic drone builds to Shift:
        - Initialize an object using a package from Elipy core, to be used later.
            - One instance of shift.ShiftUtils().
        - Run the shifter.upload_shift_offsite_drone method from Elipy core.
    - For normal Shift jobs:
        - Set the path where we have the build to upload.
        - Initialize an object using a package from Elipy core, to be used later.
            - One instance of shift.ShiftUtils().
        - Run the shifter.upload method from Elipy core.

"""

import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import LOGGER, SETTINGS, windows_tools
from elipy2.shifters import ShifterFactory
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


# pylint: disable=unused-argument
@click.command("submit_to_shift", short_help="Submits a build to Shift.")
@click.option("--code-branch", help="Branch/stream that the code/binary is coming from.")
@click.option("--code-changelist", help="Changelist of binaries.")
@click.option("--data-branch", help="Branch/stream that data is coming from.")
@click.option("--data-changelist", help="Changelist of data being used.")
@click.option("--user", help="User for Shift authentication", required=True)
@click.option("--password", help="Password for Shift authentication.", required=True)
@click.option(
    "--submission-path",
    default=None,
    help="Folder path to use to stage build. Shift service account needs access to this.",
)
@click.option(
    "--use-bilbo/--no-bilbo",
    default=True,
    help="If passed, staged build will be registered in the configured metadata services.",
)
@click.option("--compression", default=False, help="Compress big supplemental files")
@click.option(
    "--shift-url",
    help="Override the default Shift URL.",
    default="https://shift.ea.com",
)
@click.option("--submission-tool", help="Override which submission tool gets used.", default=None)
@click.option(
    "--shifter-type",
    help="Override which shifter type to use. Defaults to frosty_shifter.",
    default="frosty_shifter",
)
@click.option(
    "--force-reshift",
    default=False,
    help="Shift builds even though they are marked as shifted in the configured metadata services.",
)
@click.option(
    "--use-elipy-config",
    is_flag=True,
    help="Use shift elipy config instead of shift template.",
)
@click.option(
    "--artifactory-user",
    default=None,
    help="Specify a user to download the shift submission tool from artifactory.",
)
@click.option(
    "--artifactory-apikey",
    default=None,
    help="Specify an api key to download the shift submission tool from artifactory.",
)
@click.option(
    "--use-zipped-drone-builds",
    is_flag=True,
    help="Use zipped drone builds, instead of loose files in a folder.",
)
@click.option(
    "--shift-retention-policy",
    default=None,
    help="Specify a retention policy for a build on shift",
)
@click.option(
    "--build-id",
    default="None",
    help="The BUILD_ID of the existing Shift build for incremental submission.",
)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    code_branch,
    code_changelist,
    data_branch,
    data_changelist,
    user,
    password,
    submission_path,
    use_bilbo,
    compression,
    shift_url,
    submission_tool,
    shifter_type,
    force_reshift,
    use_elipy_config,
    artifactory_user,
    artifactory_apikey,
    use_zipped_drone_builds,
    shift_retention_policy,
    build_id,
):
    """
    Uploads a given build to Shift using a child class of ShiftUtilsBase.
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    if build_id.lower() == "none":
        build_id = None

    args = (user, password)
    kwargs = {
        "artifactory_api_key": artifactory_apikey,
        "artifactory_user": artifactory_user,
        "code_branch": code_branch,
        "code_changelist": code_changelist,
        "compression": compression,
        "data_branch": data_branch,
        "data_changelist": data_changelist,
        "re_shift": force_reshift,
        "shift_retention_policy": shift_retention_policy,
        "shift_url": shift_url,
        "submission_path": submission_path,
        "submission_tool": submission_tool,
        "use_bilbo": use_bilbo,
        "use_elipy_config": use_elipy_config,
        "use_zipped_drone_builds": use_zipped_drone_builds,
        "build_id": build_id,
    }
    kwargs["data_changelist"] = validate_data_changelist(kwargs)

    # Ensuring PCShiftAgent tool is in the correct state

    service_name = "connectsvc"
    check_shift_pc_agent(service_name)
    check_shift_pc_agent(service_name, startup=True)

    shifter = ShifterFactory.get_shifter_instance(shifter_type, *args, **kwargs)
    shifter.process_shift_upload()

    # Ensuring PCShiftAgent tool is shutdown after use
    check_shift_pc_agent(service_name)


def validate_data_changelist(kwargs):
    """
    Validate that the data changelist is set
    to the code changelist value as required.
    """
    is_unified_code_data_stream_project = SETTINGS.get(
        "unified_code_data_stream_project", default=False
    )
    data_changelist = kwargs["data_changelist"]
    code_changelist = kwargs["code_changelist"]
    data_changelist_not_set = data_changelist in [None, False, "not_available"]

    if is_unified_code_data_stream_project and data_changelist_not_set:
        LOGGER.info(
            "Data changelist not set. Using code changelist {} as data changelist.".format(
                code_changelist
            )
        )
        data_changelist = code_changelist
    return data_changelist


def check_shift_pc_agent(service_name, startup=False):
    """
    Will ensure that the resilio based PCShiftAgent
    is in the correct state before a shift upload happens,
    will check to ensure that the service exists also and
    print a wanring to console if it does not.
    """

    # Always ensure PCShiftAgent is shutdown before starting
    if windows_tools.service_installed(servicename=service_name):
        if startup:
            windows_tools.start_service(servicename=service_name)
        else:
            windows_tools.shutdown_service(servicename=service_name)
