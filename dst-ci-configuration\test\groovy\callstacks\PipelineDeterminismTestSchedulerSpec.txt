   PipelineDeterminismTestScheduler.run()
      GetBranchFile.get_branchfile(null, null)
      PipelineDeterminismTestScheduler.pipeline(groovy.lang.Closure)
         PipelineDeterminismTestScheduler.allowBrokenBuildClaiming()
         PipelineDeterminismTestScheduler.timestamps()
         PipelineDeterminismTestScheduler.echo(Executing on agent [label:any])
         PipelineDeterminismTestScheduler.stage(Get changelist from reference job, groovy.lang.Closure)
            PipelineDeterminismTestScheduler.script(groovy.lang.Closure)
               LibJenkins.getLastStableCodeChangelist(a-branch.data.start)
               PipelineDeterminismTestScheduler.EnvInject({absoluteUrl=http://example.com/dummy, buildVariables={}, changeSets=[], currentResult=SUCCESS, description=dummy, displayName=#1, duration=1, durationString=1 ms, fullDisplayName=dummy #1, fullProjectName=dummy, id=1, keepLog=false, nextBuild=null, number=1, previousBuild=null, projectName=dummy, result=SUCCESS, startTimeInMillis=1, timeInMillis=1, upstreamBuilds=[]}, {code_changelist=[123]})
         PipelineDeterminismTestScheduler.stage(Trigger pipeline determinism test jobs, groovy.lang.Closure)
            PipelineDeterminismTestScheduler.script(groovy.lang.Closure)
               PipelineDeterminismTestScheduler.retryOnFailureCause(3, [], groovy.lang.Closure)
                  PipelineDeterminismTestScheduler.EnvInject({absoluteUrl=http://example.com/dummy, buildVariables={}, changeSets=[], currentResult=SUCCESS, description=dummy, displayName=#1, duration=1, durationString=1 ms, fullDisplayName=dummy #1, fullProjectName=dummy, id=1, keepLog=false, nextBuild=null, number=1, previousBuild=null, projectName=dummy, result=SUCCESS, startTimeInMillis=1, timeInMillis=1, upstreamBuilds=[]}, {code_changelist=[123]})
                  PipelineDeterminismTestScheduler.parallel({pipeline-determinism-test=groovy.lang.Closure, pipeline-determinism-test.xbsx.test_job_name=groovy.lang.Closure})
                     PipelineDeterminismTestScheduler.string({name=code_changelist, value=[123]})
                     PipelineDeterminismTestScheduler.string({name=script_args, value=})
                     PipelineDeterminismTestScheduler.build({job=a-branch.pipeline-determinism-test, parameters=[{name=code_changelist, value=[123]}, {name=script_args, value=}], propagate=false})
                        PipelineDeterminismTestScheduler.clone()
                        PipelineDeterminismTestScheduler.toString()
                     LibJenkins.printRunningJobs(scripts.schedulers.all.PipelineDeterminismTestScheduler)
                     PipelineDeterminismTestScheduler.string({name=code_changelist, value=[123]})
                     PipelineDeterminismTestScheduler.string({name=script_args, value=-platform=xbsx test_args})
                     PipelineDeterminismTestScheduler.build({job=a-branch.pipeline-determinism-test.xbsx.test_job_name, parameters=[{name=code_changelist, value=[123]}, {name=script_args, value=-platform=xbsx test_args}], propagate=false})
                        PipelineDeterminismTestScheduler.clone()
                        PipelineDeterminismTestScheduler.toString()
                     LibJenkins.printRunningJobs(scripts.schedulers.all.PipelineDeterminismTestScheduler)
