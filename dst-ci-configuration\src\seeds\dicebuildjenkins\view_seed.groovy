package dicebuildjenkins

sectionedView('DRE jobs') {
    sections {
        listView {
            name('VM administration jobs')
            width('FULL')
            alignment('CENTER')
            jobs {
                regex('.*(utilities).*')
            }
            columns {
                status()
                weather()
                name()
                lastSuccess()
                lastFailure()
                lastDuration()
                buildButton()
            }
        }
        listView {
            name('Build deleter jobs')
            width('FULL')
            alignment('CENTER')
            jobs {
                regex('.*(utility.build-deleter).*')
            }
            columns {
                status()
                weather()
                name()
                lastSuccess()
                lastFailure()
                lastDuration()
            }
        }
    }
}
