package com.ea.project.all.mastersettings

import com.ea.project.all.All
import com.ea.project.fb1.Fb1Battlefieldgame
import com.ea.project.kin.Kingston

class FrostedAutotestMaster {
    static Class project = All
    static Map branches = [:]
    static Map preflight_branches = [:]
    static Map autotest_branches = [
        'kin-dev-unverified'    : [
            code_folder               : 'dev',
            code_branch               : 'kin-dev-unverified',
            data_folder               : 'dev',
            data_branch               : 'kin-dev-unverified',
            statebuild_autotest       : true,
            poolbuild_autotest        : true,
            project                   : KinDevUnvFrostedProject,
            job_label_statebuild_bilbo: 'kin-dev-unverified-statebuild',
            build_frosted             : true,
            timeout_hours_code        : 5,
        ],
        'dev-na-battlefieldgame': [
            code_branch               : 'dev-na-dice-next-build',
            code_folder               : 'fbstream',
            data_branch               : 'dev-na-dice-next-build-data',
            data_folder               : 'fbstream',
            statebuild_autotest       : true,
            poolbuild_autotest        : true,
            project                   : DevNaBfFrostedProject,
            job_label_statebuild_bilbo: 'dev-na-statebuild',
            build_frosted             : true,
            timeout_hours_code        : 5,
        ],
        '2024_1_dev-bf'         : [
            code_branch               : '2024_1_dev-bf',
            code_folder               : 'fbstream',
            data_branch               : '2024_1_dev-bf',
            data_folder               : 'fbstream',
            statebuild_autotest       : true,
            poolbuild_autotest        : true,
            project                   : DevBfPatchStreamFrostedProject,
            job_label_statebuild_bilbo: '2024_1_dev-bf-statebuild',
            build_frosted             : true,
            timeout_hours_code        : 5,
        ],
    ]
    static Map integrate_branches = [:]
    static Map copy_branches = [:]
    static Map feature_integrations = [:]
    static Map maintenance_branch = [:]
    static List dashboard_list = []
    static Map dvcs_configs = [:]
    static List code_downstream_matrix = []
    static Map MAINTENANCE_SETTINGS = [:]

    private static class DevNaBfFrostedProject extends Fb1Battlefieldgame {
        static String autotest_matrix = 'FrostedAutotestMatrix'
    }

    private static class DevBfPatchStreamFrostedProject extends Fb1Battlefieldgame {
        static String autotest_matrix = 'FrostedAutotestMatrix'
    }

    private static class KinDevUnvFrostedProject extends Kingston {
        static String autotest_matrix = 'FrostedAutotestMatrix'
    }
}
