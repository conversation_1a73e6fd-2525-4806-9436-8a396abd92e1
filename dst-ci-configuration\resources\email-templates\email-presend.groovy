import com.sonyericsson.jenkins.plugins.bfa.model.FailureCauseBuildAction
import com.sonyericsson.jenkins.plugins.bfa.model.FoundFailureCause
import hudson.model.AbstractBuild
import hudson.model.Result
import hudson.model.TaskListener
import hudson.model.User
import hudson.tasks.MailAddressResolver

import jakarta.mail.Message
import jakarta.mail.internet.InternetAddress
import java.nio.charset.Charset
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.Paths

Map<String, String> env = build.getEnvironment(TaskListener.NULL);

if (isInfrastructureFailure(build)) {
    logger.println('Infrastructure failure. Sending <NAME_EMAIL>.');
    InternetAddress[] addresses = new InternetAddress[1];
    addresses[0] = new InternetAddress('<EMAIL>');
    msg.setRecipients(Message.RecipientType.TO, addresses);
    return;
}

if (env.containsKey('HUDSON_CHANGELOG_FILE')) {
    String changelogPath = env['HUDSON_CHANGELOG_FILE'];
    changelogPath = changelogPath.replaceAll('changelog[0-9]*.xml', 'changelog.xml');
    Path filePath = Paths.get(changelogPath);
    List<InternetAddress> sendTo = new ArrayList<InternetAddress>();

    if (Files.exists(filePath)) {
        for (String line : Files.readAllLines(filePath, Charset.forName('UTF-8'))) {
            if (line.contains('changeUser')) {
                String firstString = '&gt;';
                String secondString = '&lt;/';
                int first = line.indexOf(firstString);
                int second = line.indexOf(secondString);

                if (first < second) {
                    InternetAddress address = getRecipient(line.substring(first + firstString.length(), second));
                    if (address != null) {
                        sendTo.add(address);
                    }
                }
            }
        }
    }
    logger.println('Recipients: ' + sendTo.toArray().toString()); // For debugging
    msg.setRecipients(Message.RecipientType.TO, sendTo.toArray(new InternetAddress[0]));
}

InternetAddress getRecipient(String userId) {
    try {
        User user = User.getById(userId, true);
        if (user != null) {
            def userEmailAddress = MailAddressResolver.resolve(user);
            if (userEmailAddress != null) {
                return new InternetAddress(userEmailAddress);
            }
        }
    }
    catch (Exception ex) {
    }
    return null;
}

boolean isInfrastructureFailure(AbstractBuild run) {
    Result result = run.getResult();
    if (result != null && result.equals(Result.FAILURE)) {
        FailureCauseBuildAction bfa = run.getAction(FailureCauseBuildAction.class);
        if (bfa != null && !bfa.getFoundFailureCauses().isEmpty()) {
            FoundFailureCause cause = bfa.getFoundFailureCauses().get(0);
            if (cause != null && cause.getCategories() != null) {
                return !cause.getCategories().contains("game");
            }
        }
    }
    return false;
}
