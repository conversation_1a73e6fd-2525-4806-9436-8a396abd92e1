package com.ea.lib

/*
 * This class hold NonCps Compliant code. The code in this class should not be used in a Jenkins Pipeline script since
 * Pipeline scripts cannot use some language features. You should avoid using any features that require a Closure to be passed, for example:
 *  - list.toSorted { ... }
 *  - list.each { ... }
 *  - list.foreach { ... }
 *
 * See https://www.jenkins.io/doc/book/pipeline/cps-method-mismatches/#calling-non-cps-transformed-methods-with-cps-transformed-arguments for
 * more details.
 *
 */

class LibCommonNonCps {
    /**
     * Look for setting variations and return the value of the most specific setting found, default value otherwise.
     * More specific settings take precedence (e.g. 'job_label_statebuild_bilbo_drone_job' will be used over 'job_label_statebuild').
     *
     * Example
     * 		get_setting_value(branch_info, ['bilbo', 'drone', 'job'], 'job_label_statebuild', 'statebuild')
     *
     * 	Would search for the following settings in this order (most specific, to least specifc):
     *
     * 		'job_label_statebuild_bilbo_drone_job', 'job_label_statebuild_bilbo_drone', 'job_label_statebuild_bilbo' , 'job_label_statebuild'
     *
     * 	If no settings can be found, the default value 'statebuild' would be returned
     *
     * @param branch_info branch info to parse
     * @param modifiers a list of modifiers ['bilbo', 'drone', 'job'] to apply to setting_name.
     * @param setting_name Setting to look for (e.g. 'job_label_statebuild')
     * @param default_value value to return if there are no matches
     * @param project option to get default value for setting from the project settings
     * @return matching variation, default_value otherwise
     */
    // Only disable when you want a method to be able to return several different types.
    @SuppressWarnings('MethodReturnTypeRequired')
    static def get_setting_value(def branch_info, def modifiers, def setting_name, def default_value = null, def project = null) {
        def result = default_value
        def current_variation = ''
        List<String> possible_setting_names = []

        def all_mofifiers = [setting_name] + modifiers
        // Create variations
        for (modifier in all_mofifiers) {
            current_variation = [current_variation, modifier].findAll { it != '' }.join('_')
            possible_setting_names.add(current_variation)
        }

        // reverse so we have the most specific setting names at the start
        possible_setting_names = possible_setting_names.reverse()

        // search for the possible setting names in the project object
        if (project && project.getClass() == Class) {
            def inProject = possible_setting_names.find { project.metaClass.hasProperty(project, it) }
            result = inProject ? project."$inProject" : result
        }

        // special case for dice-build-jenkins settings
        def project_value = null
        if (project && project.getClass() == LinkedHashMap) {
            // Get the first matching variation
            for (s in possible_setting_names) {
                project_value = project."${s}"
                if (project_value != null) {
                    result = project_value
                    break
                }
            }
        }

        def branch_info_value = null

        // Get the first matching variation
        for (s in possible_setting_names) {
            branch_info_value = branch_info."${s}"
            if (branch_info_value != null) {
                result = branch_info_value
                break
            }
        }

        return result
    }

    /*
    * Check if any settings passed are null, if so thrown an exception
    */

    static void throw_if_any_are_null(def value_array) {
        def null_settings = value_array.findAll { item -> item.value == null }

        if (null_settings) {
            throw new IllegalArgumentException("The following settings cannot be null: ${(null_settings.keySet().collect().join(', '))}")
        }
    }

    /**
     * Contructs a script used to do a p4 revert files in both code and data workspaces
     */

    static String p4AWSRevertScript(def project, def branch) {
        def user = LibCommonNonCps.get_setting_value(branch, [], 'p4_user_single_slash', '', project)
        def code_server = branch.p4_code_server ?: project.p4_code_server
        def code_client = project.p4_code_client_env
        def data_server = branch.p4_data_server ?: project.p4_data_server
        def data_client = project.p4_data_client_env
        def LOG_FOLDER = branch.workspace_root + '\\logs'
        def scriptLines = [
            '@ECHO OFF',
            "DEL /F /Q /S ${LOG_FOLDER}\\* > nul 2>&1",
            "mkdir ${LOG_FOLDER} 2> NUL",
            'IF EXIST C:\\JenkinsBuildtrack.txt (',
            "echo 'Jenkins has done build on this agent' ",
            'exit 0',
            ')',
            'IF EXIST C:\\ProgramData\\Amazon\\EC2-Windows\\Launch\\Log\\UserdataExecution.log (',
            'FOR /L %%i IN (1,1,30) DO (',
            'find "Userdata execution finished" C:\\ProgramData\\Amazon\\EC2-Windows\\Launch\\Log\\UserdataExecution.log > NUL && (',
            'goto :post_userdata',
            ') || (',
            'ping localhost -n 10 > NUL',
            ')',
            ')',
            ')',
            ':post_userdata',
            "powershell -F C:\\scripts\\syncp4.ps1 ${code_server} ${user} ${code_client} //dicestudio/kin/dev/kin-dev dice-p4buildedge02-fb code",
            "powershell -F C:\\scripts\\syncp4.ps1 ${data_server} ${user} ${data_client} //data/kin/dev/kin-dev-unverified-aws-code-preflight p4-tunguska-build01 data",
            'del C:\\scripts\\temp_ps.txt',
            'exit 0',
        ]
        return scriptLines.join('\n')
    }
}
