package com.ea.lib.jobsettings

import com.ea.lib.LibCommonNonCps

class CodeCoverageSettings extends JobSetting {
    String nonVirtualCodeBranch
    String nonVirtualCodeFolder
    String refJob

    void initializeCodeCoverageStart(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        def modifiers = ['codecoverage']
        description = "Scheduler to codecoverage start job for ${branchName}"
        isDisabled = LibCommonNonCps.get_setting_value(branchInfo, modifiers, 'disable_build', false)
        nonVirtualCodeBranch = branchInfo.non_virtual_code_branch ?: ''
        nonVirtualCodeFolder = branchInfo.non_virtual_code_folder ?: ''
        cronTrigger = 'H 7 * * 6' // Runs every Sat at 7A.M-ish
        codeBranch = branchInfo.code_branch
        codeFolder = branchInfo.code_folder
        projectName = projectFile.name
        refJob = branchInfo.codecoverage_args.reference_job
    }

    void initializeCodeCoverageRun(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        extraArgs = "--codecoverage-path ./autotest/${branchInfo.codecoverage_args.run_script ?: ''}"
        jobLabel = branchInfo.job_label ?: 'statebuild'
        description = "Run CodeCoverage code on: ${branchName}"
        buildName = '${JOB_NAME}.${ENV, var="code_changelist"}'
        elipyCmd = "${this.elipyCall} codecoverage ${extraArgs}"
    }
}
