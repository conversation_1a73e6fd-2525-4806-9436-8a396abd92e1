package scripts.schedulers.all

import com.ea.lib.LibJenkins
import com.ea.project.GetBranchFile
import hudson.model.Result

def branchfile = GetBranchFile.get_branchfile(env.project_name, env.branch_name)
def project = ProjectClass(env.project_name)

/**
 * bilbo_move_location.groovy
 */
pipeline {
    agent { label 'scheduler || executor_agent' }
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('Register Drone in the build location.') {
            steps {
                script {
                    currentBuild.displayName = env.JOB_NAME + '.' + params.data_changelist + '.' + params.code_changelist

                    def args = [
                        string(name: 'code_changelist', value: params.code_changelist),
                        string(name: 'data_changelist', value: params.data_changelist),
                    ]

                    def last_code_built = LibJenkins.getLastStableCodeChangelist(env.JOB_NAME)
                    def final_result = Result.SUCCESS
                    if (env.code_changelist == last_code_built) {
                        echo('Last code copy used code CL ' + last_code_built + ' and current code CL is ' + code_changelist + ', skipping copy.')
                    } else { // Only run the copy if we have a new code changelist.
                        // Copy code to the new location.
                        def bilbo_move_matrix = branchfile.metaClass.hasProperty(branchfile, 'bilbo_move_matrix') ? branchfile.bilbo_move_matrix : []
                        def platforms = bilbo_move_matrix ?: branchfile.code_matrix

                        def jobs = [:]
                        for (def run = 0; run <= env.retry_limit.toInteger(); run++) { // Retry failed jobs if retry_limit > 0.
                            jobs = [:]
                            final_result = Result.SUCCESS
                            for (platform in platforms) {
                                for (config in platform.configs) {
                                    def config_name = config instanceof Map ? config.name : config
                                    if (!config_name.contains('deprecation-test')) {
                                        def job_name = env.branch_name + '.bilbo.move.' + platform.name + '.' + config_name + '.' + params.location
                                        if (NeedsRebuildData(job_name, code_changelist, data_changelist)) {
                                            jobs[job_name] = {
                                                def downstream_job = build(job: job_name, parameters: args, propagate: false)
                                                final_result = final_result.combine(Result.fromString(downstream_job.result))
                                                LibJenkins.printRunningJobs(this)
                                            }
                                        }
                                    }
                                }
                            }
                            parallel(jobs)
                            if (final_result == Result.SUCCESS) {
                                break
                            }
                        }
                    }

                    if (final_result == Result.SUCCESS) {
                        def job_name_remote = env.branch_name + '.bilbo.register.remote.' + params.location
                        def bilbo_drone_job_remote = build(job: job_name_remote, parameters: args, propagate: false)
                        final_result = final_result.combine(Result.fromString(bilbo_drone_job_remote.result))
                    } else {
                        echo('Failed to copy code to the new location. Skipping bilbo registration')
                    }

                    currentBuild.result = final_result.toString()

                    def slack_settings = branchfile.standard_jobs_settings?.slack_channel_bilbo ?: branchfile.standard_jobs_settings?.slack_channel_data
                    SlackMessageNew(currentBuild, slack_settings, project.short_name)
                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
}
