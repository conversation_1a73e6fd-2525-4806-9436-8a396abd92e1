package com.ea.lib.jobsettings

import com.ea.exceptions.CobraException
import com.ea.lib.LibCommonNonCps

class PerforceCounterSettings extends JobSetting {

    void initializeAutotestP4CounterUpdater(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.autotest_branches as Map)
        String p4CodeServer = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_code_server', null, projectFile)
        String p4DataServer = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_data_server', null, projectFile)
        String p4CodeClient = projectFile.p4_code_client
        String p4DataClient = projectFile.p4_data_client
        String p4User = projectFile.p4_user_single_slash
        if (!p4CodeServer) {
            throw new CobraException('Please configure a p4_code_server')
        }
        if (!p4DataServer) {
            throw new CobraException('Please configure a p4_data_server')
        }
        if (!p4CodeClient) {
            throw new CobraException('Please configure a p4_code_client')
        }
        if (!p4DataClient) {
            throw new CobraException('Please configure a p4_data_client')
        }
        if (!p4User) {
            throw new CobraException('Please configure a p4_user_single_slash')
        }
        jobLabel = branchInfo.job_label_statebuild ?: 'statebuild'
        description = 'P4 counter update after data start job done.'
        elipyCmd = "$elipyCall p4_counter --port $p4CodeServer --client $p4CodeClient --user $p4User --countername %code_countername%" +
            " --value %code_changelist% --extra-port $p4DataServer --extra-client $p4DataClient --extra-countername %data_countername%" +
            ' --extra-value %data_changelist%'
    }

}
