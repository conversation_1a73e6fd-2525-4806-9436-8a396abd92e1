# Shift service account user DICE.Auth Monkey.Shift
# Duplicated settings lower in the settings list will get higher priority
buildtype: "QA"
milestone: "Production"
distribution_type: "InternalOnly"
retention_policy: "SpaceAvailable"
priority: ""
version: "2.0"

xbsx:
  content:
    files:
      file_names:
        - "builtLevels.json"
        - "*.buildlayout"
        - "*.BuildSettings"
        - "Logo.png"
        - "*.dll"
        - "SmallLogo.png"
        - "SplashScreen.png"
        - "StoreLogo.png"
        - "WideLogo.png"
        - "*.Main_Xbsx_*.exe"
        - "MicrosoftGame.config"
        - "gameos.xvd"
        - "nsal.json"
        - "package.mft"
        - "resources.pri"
        - "build.json"
      supplemental_files:
        - "*.xml"
        - "*.pdb"
      directory:
        - "Data"
        - "Scripts"
    digital:
      file_names:
        - "*appxmanifest.xml"
        - "MicrosoftGame.config"
        - "*neutral__*.xvc"
        - "*.ekb"
        - "Validator_*.xml"
      supplemental_files:
        - "build.json"
        - "*.pdb"
        - "*neutral__*[!c]" #This is kind of a hack. glob.glob doesn't support do not match string; only set of characters.
      directory:
        - ""
    patch:
      file_names:
        - "*appxmanifest.xml"
        - "MicrosoftGame.config"
        - "*neutral__*.xvc"
        - "*.ekb"
        - "Validator_*.xml"
      supplemental_files:
        - "build.json"
        - "*.pdb"
        - "*neutral__*[!c]" #This is kind of a hack. glob.glob doesn't support do not match string; only set of characters.
      directory:
        - ""
  settings:
    upgrade:
      ww:
        final:
          files:
            sku_id: "1a0262e7-9f53-4e01-90a1-3383de8e6d10"
            sku_name: "FG - WW (upgrade - xbsx - files - final)"

ps5:
  content:
    files:
      file_names:
        - "builtLevels.json"
        - "build.json"
        - "*.prx"
        - "*.buildlayout"
        - "*.BuildSettings"
        - "eboot.bin"
      supplemental_files:
        - "*.xml"
      directory:
        - "Data"
        - "Scripts"
        - "sce_sys"
    digital:
      file_names:
        - "*-V0100.pkg"
      upload_loop_filenames:
        - "*V0100.pkg"
        - "*remastered.pkg"
      supplemental_files:
        - "chunks*.gp5"
        - "build.json"
      directory:
        - ""
    patch:
      file_names:
        - "*-V0100.pkg"
      supplemental_files:
        - "chunks*.gp5"
        - "build.json"
      directory:
        - ""
    patch-remaster:
      file_names:
        - "*remastered.pkg"
      supplemental_files:
        - "chunks*.gp5"
        - "build.json"
      directory:
        - ""
  settings:
    upgrade:
      ww:
        final:
          files:
            sku_id: "fd17165a-3d1d-43c1-9525-78a3410b8559"
            sku_name: "FG - WW (upgrade - ps5 - files - final)"

server:
  content:
    file_names:
      - '*'
    supplemental_files:
      - ""
  settings:
    upgrade:
      final:
        files:
          sku_id: "55d9140e-b810-4984-8c4d-3a561e4db00a"
          sku_name: "Server - WW (upgrade - server - files - final)"

win64:
  content:
    files:
      file_names:
        - '*'
      supplemental_files:
      - "*.xml"
    digital:
      file_names:
        - "Merlin.zip"
      supplemental_files:
        - "build.json"
        - "installerdata.xml"
      directory:
        - ""
      retail:
        supplemental_files:
          - "*.Main_*_retail*"
        directory:
          - ""
      final:
        supplemental_files:
          - "*.Main_*_retail*"
        directory:
          - ""
      performance:
        supplemental_files:
          - "*.Main_*_performance*"
    patch:
      file_names:
        - "Merlin.zip"
      supplemental_files:
        - "build.json"
        - "installerdata.xml"
        - "*.Main_*_retail*"
      directory:
        - ""
  settings:
    upgrade:
      final:
        files:
          sku_id: "26bf92f6-f3f0-44b8-af0b-2cd099640535"
          sku_name: "FG - WW (upgrade - win64 - files - final)"

linuxserver:
  content:
    file_names:
      - '*binaries.Zip'
    supplemental_files:
      - "build.json"
      - "builtLevels.json"
      - "*Symbols.zip"
  settings:
    upgrade:
      final:
        digital:
          sku_id: "cc2e49d3-9dc1-4c06-8a94-ef56f0262430"
          sku_name: "Server - WW (upgrade - linuxserver - digital - final)"
