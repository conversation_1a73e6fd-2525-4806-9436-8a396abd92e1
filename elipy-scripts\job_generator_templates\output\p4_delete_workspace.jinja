{#
    Command:
        p4_delete_workspace
            short_help: Performs a deletion of perforce workspace.

    Arguments:

    Required variables:
        codeport
            required: True
            help: Perforce code server specification
        codeclient
            required: True
        dataport
            required: True
            help: Perforce data server specification
        dataclient
            required: True
        user
            required: True
        codeworkspace
            required: True
            help: code workspace name
        dataworkspace
            required: True
            help: data workspace name

    Optional variables:
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    p4_delete_workspace
    --codeport {{ codeport }}
    --codeclient {{ codeclient }}
    --dataport {{ dataport }}
    --dataclient {{ dataclient }}
    --user {{ user }}
    --codeworkspace {{ codeworkspace }}
    --dataworkspace {{ dataworkspace }}
  displayName: elipy p4_delete_workspace
