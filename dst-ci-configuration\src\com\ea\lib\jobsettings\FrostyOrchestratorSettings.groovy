package com.ea.lib.jobsettings

class FrostyOrchestratorSettings extends JobSetting {

    void initializeStart(def branchFile, def masterFile, def projectFile, String branchName) {
        this.init(branchFile, masterFile, projectFile, branchName, masterFile.branches as Map)
        description = 'Triggers deployment-data jobs which in turn trigger frosty jobs. Runs on a schedule.'
        cronTrigger = branchInfo.frosty_orchestrator_trigger
    }
}
