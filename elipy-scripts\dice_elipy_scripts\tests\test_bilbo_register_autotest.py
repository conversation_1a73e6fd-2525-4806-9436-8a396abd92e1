"""
test_bilbo_register_autotest.py

Unit testing for bilbo_register_autotest
"""

import os
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import call, patch, MagicMock
from dice_elipy_scripts.bilbo_register_autotest import cli
from elipy2.config import ConfigManager

config_path = os.path.join(os.path.dirname(__file__), "data", "elipy_test.yml")


@patch("dice_elipy_scripts.bilbo_register_autotest.SETTINGS", ConfigManager(path=config_path))
@patch("dice_elipy_scripts.bilbo_register_autotest.build_metadata_utils.setup_metadata_manager")
@patch("elipy2.filer_paths.get_code_build_root_path", MagicMock(return_value="PATH"))
class TestBilboRegisterAutotest:
    OPTION_CODE_BRANCH = "--code-branch"
    OPTION_CODE_CHANGELIST = "--code-changelist"
    OPTION_DATA_BRANCH = "--data-branch"
    OPTION_DATA_CHANGELIST = "--data-changelist"
    OPTION_TEST_DEFINITION = "--test-definition"
    OPTION_TEST_STATUS = "--test-status"
    OPTION_RUN_BILBO = "--run-bilbo"
    OPTION_REGISTER_SMOKE = "--register-smoke"
    OPTION_EXTRA_LOCATION = "--extra-location"

    VALUE_CODE_BRANCH = "code-branch"
    VALUE_CODE_CHANGELIST = "123"
    VALUE_DATA_BRANCH = "dev-na-dice-next-build"
    VALUE_DATA_CHANGELIST = "123"
    VALUE_TEST_DEFINITION = "test-definition"
    VALUE_TEST_STATUS = "SUCCESS"

    DEFAULT_ARGS = [
        OPTION_CODE_BRANCH,
        VALUE_CODE_BRANCH,
        OPTION_CODE_CHANGELIST,
        VALUE_CODE_CHANGELIST,
        OPTION_DATA_BRANCH,
        VALUE_DATA_BRANCH,
        OPTION_DATA_CHANGELIST,
        VALUE_DATA_CHANGELIST,
        OPTION_TEST_DEFINITION,
        VALUE_TEST_DEFINITION,
        OPTION_TEST_STATUS,
        VALUE_TEST_STATUS,
        OPTION_RUN_BILBO,
        True,
        OPTION_REGISTER_SMOKE,
        False,
    ]

    def test_register_tests(self, setup_metadata_manager_mock):
        mock_metadata_manager = MagicMock()
        setup_metadata_manager_mock.return_value = mock_metadata_manager

        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)

        assert result.exit_code == 0
        mock_metadata_manager.set_autotest_category_status.assert_called_once_with(
            "PATH",
            data_changelist=self.VALUE_DATA_CHANGELIST,
            data_branch=self.VALUE_DATA_BRANCH,
            test_category=self.VALUE_TEST_DEFINITION,
            status=self.VALUE_TEST_STATUS,
        )
        mock_metadata_manager.tag_code_build_as_smoked.assert_not_called()

    def test_should_not_register_tests_if_run_bilbo_is_false(self, setup_metadata_manager_mock):
        mock_metadata_manager = MagicMock()
        setup_metadata_manager_mock.return_value = mock_metadata_manager

        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_ARGS[:-4] + [self.OPTION_RUN_BILBO, False],  # Disable run_bilbo
        )

        assert result.exit_code == 0
        mock_metadata_manager.set_autotest_category_status.assert_not_called()
        mock_metadata_manager.tag_code_build_as_smoked.assert_not_called()

    def test_should_not_register_smoke_if_register_smoke_is_false(
        self, setup_metadata_manager_mock
    ):
        mock_metadata_manager = MagicMock()
        setup_metadata_manager_mock.return_value = mock_metadata_manager

        runner = CliRunner()
        result = runner.invoke(
            cli,
            self.DEFAULT_ARGS[:-2] + [self.OPTION_REGISTER_SMOKE, False],
        )

        assert result.exit_code == 0

        mock_metadata_manager.set_autotest_category_status.assert_called_once_with(
            "PATH",
            data_changelist=self.VALUE_DATA_CHANGELIST,
            data_branch=self.VALUE_DATA_BRANCH,
            test_category=self.VALUE_TEST_DEFINITION,
            status=self.VALUE_TEST_STATUS,
        )

        mock_metadata_manager.tag_code_build_as_smoked.assert_not_called()

    def test_should_not_register_smoke_if_builds_missing_in_remote_bilbo(
        self, setup_metadata_manager_mock
    ):
        mock_metadata_manager = MagicMock()
        setup_metadata_manager_mock.return_value = mock_metadata_manager

        # Configure get_all_builds_query_string to return no builds
        mock_metadata_manager.get_all_builds_query_string.return_value = []

        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS)

        assert result.exit_code == 0

        mock_metadata_manager.set_autotest_category_status.assert_called_once_with(
            "PATH",
            data_changelist=self.VALUE_DATA_CHANGELIST,
            data_branch=self.VALUE_DATA_BRANCH,
            test_category=self.VALUE_TEST_DEFINITION,
            status=self.VALUE_TEST_STATUS,
        )

        mock_metadata_manager.tag_code_build_as_smoked.assert_not_called()

    def test_register_tests_single_location(self, setup_metadata_manager_mock):
        mock_metadata_manager = MagicMock()
        setup_metadata_manager_mock.return_value = mock_metadata_manager

        # Setup mock build with smoke tag
        mock_build = MagicMock()
        mock_build.source = {
            "build_promotion_level": "qa_verified",
            "location": "DiceStockholm",
            "changelist": "4381933",
            "verified_data": [
                {
                    "changelist": "4381933",
                    "branch": "data_branch",
                    "dataset": "a_data_set",
                    "timestamp": "2025-04-24T19:40:35.217590",
                }
            ],
        }

        # Configure get_all_builds_query_string to return our mock builds
        mock_metadata_manager.get_all_builds_query_string.return_value = [mock_build]

        runner = CliRunner()
        result = runner.invoke(cli, self.DEFAULT_ARGS[:-2] + [self.OPTION_REGISTER_SMOKE, True])

        assert result.exit_code == 0

        setup_manager_calls = [
            call for call in setup_metadata_manager_mock.mock_calls if call[0] == ""
        ]

        expected_calls = [
            call(
                ConfigManager(path=config_path).get(
                    "bilbo_url", location=ConfigManager(path=config_path).location
                )
            ),
            call(
                ConfigManager(path=config_path).get(
                    "bilbo_url", location=ConfigManager(path=config_path).location
                ),
                location="default",
            ),
        ]

        assert setup_manager_calls == expected_calls
        assert len(setup_manager_calls) == 2

        mock_metadata_manager.set_autotest_category_status.assert_called_once_with(
            "PATH",
            data_changelist=self.VALUE_DATA_CHANGELIST,
            data_branch=self.VALUE_DATA_BRANCH,
            test_category=self.VALUE_TEST_DEFINITION,
            status=self.VALUE_TEST_STATUS,
        )

        mock_metadata_manager.tag_code_build_as_smoked.assert_called_once_with(path="PATH")

    def test_register_tests_multiple_locations(self, setup_metadata_manager_mock):
        # Setup mock for original location and remote location
        mock_original_manager = MagicMock()
        mock_remote_manager = MagicMock()

        # Configure mock to return different instances for different locations
        # We need to return the same manager for the first two calls (original location check and first location in the loop)
        # and then the remote manager for the second location
        setup_metadata_manager_mock.side_effect = [
            mock_original_manager,
            mock_original_manager,
            mock_remote_manager,
        ]

        # Setup mock build with smoke tag
        mock_build = MagicMock()
        mock_build.source = {
            "build_promotion_level": "qa_verified",
            "location": "DiceStockholm",
            "changelist": "4381933",
            "verified_data": [
                {
                    "changelist": "4381933",
                    "branch": "data_branch",
                    "dataset": "a_data_set",
                    "timestamp": "2025-04-24T19:40:35.217590",
                }
            ],
        }

        # Configure get_all_builds_query_string to return our mock builds
        mock_original_manager.get_all_builds_query_string.return_value = [mock_build]
        mock_remote_manager.get_all_builds_query_string.return_value = [mock_build]

        location_args = self.DEFAULT_ARGS[:-2] + [
            self.OPTION_REGISTER_SMOKE,
            True,
            self.OPTION_EXTRA_LOCATION,
            "different_location",
        ]

        runner = CliRunner()
        result = runner.invoke(cli, location_args)

        assert result.exit_code == 0

        setup_manager_calls = [
            call for call in setup_metadata_manager_mock.mock_calls if call[0] == ""
        ]

        expected_calls = [
            call(
                ConfigManager(path=config_path).get(
                    "bilbo_url", location=ConfigManager(path=config_path).location
                )
            ),
            call(
                ConfigManager(path=config_path).get(
                    "bilbo_url", location=ConfigManager(path=config_path).location
                ),
                location="default",
            ),
            call(
                ConfigManager(path=config_path).get("bilbo_url", location="different_location"),
                location="different_location",
            ),
        ]

        assert setup_manager_calls == expected_calls
        assert len(setup_manager_calls) == 3

        mock_original_manager.set_autotest_category_status.assert_called_with(
            "PATH",
            data_changelist=self.VALUE_DATA_CHANGELIST,
            data_branch=self.VALUE_DATA_BRANCH,
            test_category=self.VALUE_TEST_DEFINITION,
            status=self.VALUE_TEST_STATUS,
        )

        mock_remote_manager.tag_code_build_as_smoked.assert_called_with(path="PATH")
