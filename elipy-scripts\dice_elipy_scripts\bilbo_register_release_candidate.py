"""
bilbo_register_release_candidate.py
"""
import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import LOGGER, build_metadata_utils
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command(
    "bilbo_register_release_candidate",
    short_help="Registers a build in Bilbo as a Release Candidate.",
)
@click.option("--code-branch", help="Perforce code branch/stream name.", required=True)
@click.option(
    "--code-changelist",
    required=True,
    help="Changelist number for code build to tag.",
)
@click.option("--data-branch", help="Perforce data branch/stream name.", required=True)
@click.option("--data-changelist", help="Changelist number of data to tag.", required=True)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    code_branch,
    code_changelist,
    data_branch,
    data_changelist,
):
    """
    Registers a build in Bilbo explicitly as a Release Candidate.
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    if not data_changelist.isnumeric():
        data_changelist = None

    metadata_manager = build_metadata_utils.setup_metadata_manager()

    LOGGER.info(
        "Registering build code {0}.CL{1} with data {2}.CL{3} as a Release Candidate".format(
            code_branch, code_changelist, data_branch, data_changelist
        )
    )
    metadata_manager.tag_build_as_release_candidate(
        data_branch, data_changelist, code_branch, code_changelist
    )
