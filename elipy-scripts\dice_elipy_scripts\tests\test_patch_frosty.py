"""
test_patch_frosty.py

Unit testing for patch_frosty
"""

import mock
import os
import unittest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import ANY, call, MagicMock, patch
from dice_elipy_scripts.patch_frosty import cli
from elipy2.config import ConfigManager
from elipy2.oreans import __OUTPUT_SUFFIX as OREANS_OUTPUT_SUFFIX


@patch("dice_elipy_scripts.patch_frosty.set_licensee", MagicMock())
@patch("elipy2.telemetry.upload_metrics", MagicMock())
@patch("dice_elipy_scripts.patch_frosty.data.DataUtils.set_datadir", MagicMock())
@patch("dice_elipy_scripts.patch_frosty.patch_eainstaller_signtool", MagicMock())
@patch("dice_elipy_scripts.patch_frosty.install_required_sdks", MagicMock())
@patch(
    "dice_elipy_scripts.patch_frosty.symbols.SymbolsUtils.upload_game_binary",
    MagicMock(),
)
class TestPatchFrosty(unittest.TestCase):
    ARGUMENT_PLATFORM_1 = "xb1"
    ARGUMENT_PLATFORM_2 = "xbsx"
    ARGUMENT_PLATFORM_3 = "ps4"
    ARGUMENT_PLATFORM_4 = "ps5"
    ARGUMENT_PLATFORM_5 = "win64"
    ARGUMENT_CONFIG = "retail"

    OPTION_DATA_DIRECTORY = "--data-directory"
    OPTION_CODE_BRANCH = "--code-branch"
    OPTION_CODE_CHANGELIST = "--code-changelist"
    OPTION_DATA_BRANCH = "--data-branch"
    OPTION_DATA_CHANGELIST = "--data-changelist"
    OPTION_REGION = "--region"
    OPTION_FIRST_PATCH = "--first-patch"
    OPTION_USE_WIN64TRIAL = "--use-win64trial"
    OPTION_USE_OREANS = "--use-oreans"
    OPTION_DISC_CODE_BRANCH = "--disc-code-branch"
    OPTION_DISC_CODE_CHANGELIST = "--disc-code-changelist"
    OPTION_DISC_DATA_BRANCH = "--disc-data-branch"
    OPTION_DISC_DATA_CHANGELIST = "--disc-data-changelist"
    OPTION_PATCH_CODE_BRANCH = "--patch-code-branch"
    OPTION_PATCH_CODE_CHANGELIST = "--patch-code-changelist"
    OPTION_PATCH_DATA_BRANCH = "--patch-data-branch"
    OPTION_PATCH_DATA_CHANGELIST = "--patch-data-changelist"
    OPTION_LICENSEE = "--licensee"
    OPTION_USE_COMBINE_BUNDLES = "--use-combine-bundles"
    OPTION_USE_HEAD_BUNDLES_AS_BASE_BUNDLES = "--use-head-bundles-as-base-bundles"
    OPTION_COMBINE_CODE_BRANCH = "--combine-code-branch"
    OPTION_COMBINE_CODE_CHANGELIST = "--combine-code-changelist"
    OPTION_COMBINE_DATA_BRANCH = "--combine-data-branch"
    OPTION_COMBINE_DATA_CHANGELIST = "--combine-data-changelist"
    OPTION_COMBINE_DISC_CODE_BRANCH = "--combine-disc-code-branch"
    OPTION_COMBINE_DISC_CODE_CHANGELIST = "--combine-disc-code-changelist"
    OPTION_COMBINE_DISC_DATA_BRANCH = "--combine-disc-data-branch"
    OPTION_COMBINE_DISC_DATA_CHANGELIST = "--combine-disc-data-changelist"
    OPTION_COMBINE_PATCH_CODE_BRANCH = "--combine-patch-code-branch"
    OPTION_COMBINE_PATCH_CODE_CHANGELIST = "--combine-patch-code-changelist"
    OPTION_COMBINE_PATCH_DATA_BRANCH = "--combine-patch-data-branch"
    OPTION_COMBINE_PATCH_DATA_CHANGELIST = "--combine-patch-data-changelist"
    OPTION_COMBINE_SETTINGS_FILE = "--combine-settings-file"
    OPTION_STEAM_BUILD = "--steam-build"
    OPTION_STEAM_DRMWRAP = "--steam-drmwrap"
    OPTION_STANDALONE_DISC_BASELINE = "--standalone-disc-baseline"
    OPTION_STANDALONE_PATCH_BASELINE = "--standalone-patch-baseline"
    OPTION_VIRTUAL_BRANCH_OVERRIDE = "--virtual-branch-override"

    VALUE_DATA_DIRECTORY = "SomeDatadir"
    VALUE_CODE_BRANCH = "some-branch"
    VALUE_CODE_CHANGELIST = "1234"
    VALUE_DATA_BRANCH = "other-branch"
    VALUE_DATA_CHANGELIST = "5678"
    VALUE_REGION = "ww"
    VALUE_DISC_CODE_BRANCH = "some-branch"
    VALUE_DISC_CODE_CHANGELIST = "8888"
    VALUE_DISC_DATA_BRANCH = "other-branch"
    VALUE_DISC_DATA_CHANGELIST = "9999"
    VALUE_PATCH_CODE_BRANCH = "some-branch"
    VALUE_PATCH_CODE_CHANGELIST = "1234"
    VALUE_PATCH_DATA_BRANCH = "other-branch"
    VALUE_PATCH_DATA_CHANGELIST = "5678"
    VALUE_LICENSEE = "BattlefieldGame"
    VALUE_COMBINE_CODE_BRANCH = "some-branch"
    VALUE_COMBINE_CODE_CHANGELIST = "1122"
    VALUE_COMBINE_DATA_BRANCH = "other-branch"
    VALUE_COMBINE_DATA_CHANGELIST = "3344"
    VALUE_COMBINE_DISC_CODE_BRANCH = "some-branch"
    VALUE_COMBINE_DISC_CODE_CHANGELIST = "5566"
    VALUE_COMBINE_DISC_DATA_BRANCH = "other-branch"
    VALUE_COMBINE_DISC_DATA_CHANGELIST = "7788"
    VALUE_COMBINE_PATCH_CODE_BRANCH = "some-branch"
    VALUE_COMBINE_PATCH_CODE_CHANGELIST = "1212"
    VALUE_COMBINE_PATCH_DATA_BRANCH = "other-branch"
    VALUE_COMBINE_PATCH_DATA_CHANGELIST = "3434"
    VALUE_COMBINE_SETTINGS_FILE = "combine-settigs-file.yml"

    DEFAULT_ARGS = [
        ARGUMENT_CONFIG,
        OPTION_DATA_DIRECTORY,
        VALUE_DATA_DIRECTORY,
        OPTION_CODE_BRANCH,
        VALUE_CODE_BRANCH,
        OPTION_CODE_CHANGELIST,
        VALUE_CODE_CHANGELIST,
        OPTION_DATA_BRANCH,
        VALUE_DATA_BRANCH,
        OPTION_DATA_CHANGELIST,
        VALUE_DATA_CHANGELIST,
        OPTION_DISC_CODE_BRANCH,
        VALUE_DISC_CODE_BRANCH,
        OPTION_DISC_CODE_CHANGELIST,
        VALUE_DISC_CODE_CHANGELIST,
        OPTION_DISC_DATA_BRANCH,
        VALUE_DISC_DATA_BRANCH,
        OPTION_DISC_DATA_CHANGELIST,
        VALUE_DISC_DATA_CHANGELIST,
        OPTION_PATCH_CODE_BRANCH,
        VALUE_PATCH_CODE_BRANCH,
        OPTION_PATCH_CODE_CHANGELIST,
        VALUE_PATCH_CODE_CHANGELIST,
        OPTION_PATCH_DATA_BRANCH,
        VALUE_PATCH_DATA_BRANCH,
        OPTION_PATCH_DATA_CHANGELIST,
        VALUE_PATCH_DATA_CHANGELIST,
    ]

    COMBINE_ARGS = [
        OPTION_USE_COMBINE_BUNDLES,
        "true",
        OPTION_COMBINE_DISC_CODE_BRANCH,
        VALUE_COMBINE_DISC_CODE_BRANCH,
        OPTION_COMBINE_DISC_CODE_CHANGELIST,
        VALUE_COMBINE_DISC_CODE_CHANGELIST,
        OPTION_COMBINE_DISC_DATA_BRANCH,
        VALUE_COMBINE_DISC_DATA_BRANCH,
        OPTION_COMBINE_DISC_DATA_CHANGELIST,
        VALUE_COMBINE_DISC_DATA_CHANGELIST,
        OPTION_COMBINE_PATCH_CODE_BRANCH,
        VALUE_COMBINE_PATCH_CODE_BRANCH,
        OPTION_COMBINE_PATCH_CODE_CHANGELIST,
        VALUE_COMBINE_PATCH_CODE_CHANGELIST,
        OPTION_COMBINE_PATCH_DATA_BRANCH,
        VALUE_COMBINE_PATCH_DATA_BRANCH,
        OPTION_COMBINE_PATCH_DATA_CHANGELIST,
        VALUE_COMBINE_PATCH_DATA_CHANGELIST,
    ]

    LICENSEE_ARGS = [OPTION_LICENSEE, VALUE_LICENSEE]

    XBOX_PLATFORMS = ["xb1", "xbsx"]

    CALL_TO_FETCH_XB_BASEPACKAGE = mock.call(
        **{
            "code_branch": VALUE_DISC_CODE_BRANCH,
            "code_changelist": VALUE_DISC_CODE_CHANGELIST,
            "config": ANY,
            "data_branch": VALUE_DISC_DATA_BRANCH,
            "data_changelist": VALUE_DISC_DATA_CHANGELIST,
            "destination": ANY,
            "package_type": ANY,
            "platform": ANY,
            "region": ANY,
            "combine_data_branch": ANY,
            "combine_data_changelist": ANY,
            "combine_code_branch": ANY,
            "combine_code_changelist": ANY,
        }
    )

    def setUp(self):
        config_path = os.path.join(os.path.dirname(__file__), "data", "elipy_test.yml")
        config_manager = ConfigManager(path=config_path)

        def settings_get_side_effect(key):
            if key == "elsa_patch":
                return "true"
            elif key == "recompression_cache":
                return {"win64": "server.address"}
            elif key == "skip_frosty_game_config_flags":
                return "true"
            return key  # Hack to not care about keys that are irrelevant for the test.

        self.patcher_read_fb_version = patch("elipy2.frostbite_core.read_fb_version")
        self.mock_read_fb_version = self.patcher_read_fb_version.start()
        self.mock_read_fb_version.return_value = "2019-PR7"

        self.patcher_os_exists = patch("dice_elipy_scripts.patch_frosty.os.path.exists")
        self.mock_os_exists = self.patcher_os_exists.start()
        self.mock_os_exists.return_value = False

        self.patcher_settings_get = patch(
            "dice_elipy_scripts.patch_frosty.SETTINGS", config_manager
        )
        self.mock_settings_get = self.patcher_settings_get.start()

        self.patcher_filerutils = patch("elipy2.filer.FilerUtils")
        self.mock_filerutils = self.patcher_filerutils.start()
        self.mock_filerutils.return_value = MagicMock()

        self.patcher_packageutils = patch("elipy2.package.PackageUtils")
        self.mock_packageutils = self.patcher_packageutils.start()
        self.mock_packageutils.return_value = MagicMock()

        self.patcher_avalanche_combine = patch("elipy2.avalanche.combine")
        self.mock_avalanche_combine = self.patcher_avalanche_combine.start()

        self.patcher_avalanche_ddelta = patch("elipy2.avalanche.ddelta")
        self.mock_avalanche_ddelta = self.patcher_avalanche_ddelta.start()

        self.patcher_get_baseline_build_path = patch("elipy2.filer_paths.get_baseline_build_path")
        self.mock_get_baseline_build_path = self.patcher_get_baseline_build_path.start()
        self.mock_get_baseline_build_path.return_value = "baseline_build_path"

    def tearDown(self):
        patch.stopall()

    @staticmethod
    def run_patch_frosty(platform=None, args=DEFAULT_ARGS, license_args=LICENSEE_ARGS):
        """
        Run patch frosty test with given parameters and return the result
        """
        runner = CliRunner()
        result = runner.invoke(cli, [platform] + args + license_args)
        return result

    @patch("dice_elipy_scripts.patch_frosty.SETTINGS")
    def test_basepackage_with_disc_options_fetched_when_fetch_xb_basepackage_true_in_config(
        self, mock_settings
    ):
        # Given fetch_xb_basepackage true in config and xbox platform
        mock_settings.get.return_value = True
        mock_fetch_baseline_xb_priorpackage = (
            self.mock_filerutils.return_value.fetch_baseline_xb_priorpackage
        ) = MagicMock()

        for xbox_platform in self.XBOX_PLATFORMS:
            result = self.run_patch_frosty(
                platform=xbox_platform, args=self.DEFAULT_ARGS, license_args=self.LICENSEE_ARGS
            )

            # Then fetch_baseline_xb_priorpackage is called with disc arguments
            fetch_xb_baseline_package_call = self.CALL_TO_FETCH_XB_BASEPACKAGE

            assert (
                fetch_xb_baseline_package_call in mock_fetch_baseline_xb_priorpackage.call_args_list
            )
            assert result.exit_code == 0

    @patch("dice_elipy_scripts.patch_frosty.SETTINGS")
    def test_basepackage_with_disc_options_not_fetched_when_fetch_xb_basepackage_false_in_config(
        self, mock_settings
    ):
        # Given fetch_xb_basepackage false in config and xbox platform
        mock_settings.get.return_value = False

        for xbox_platform in self.XBOX_PLATFORMS:
            result = self.run_patch_frosty(
                platform=xbox_platform, args=self.DEFAULT_ARGS, license_args=self.LICENSEE_ARGS
            )

            # Then fetch_baseline_xb_priorpackage is not called with disc arguments
            fetch_xb_baseline_package_call = self.CALL_TO_FETCH_XB_BASEPACKAGE

            assert (
                fetch_xb_baseline_package_call
                not in self.mock_filerutils.return_value.fetch_baseline_xb_priorpackage.call_args_list
            )
            assert result.exit_code == 0

    def test_basepackage_with_disc_options_not_fetched_when_fetch_xb_basepackage_not_in_config(
        self,
    ):
        # Given fetch_xb_basepackage not in config and xbox platform
        try:
            del self.mock_settings_get.config["default"]["fetch_xb_basepackage"]
        except KeyError:
            pass

        mock_fetch_baseline_xb_priorpackage = (
            self.mock_filerutils.return_value.fetch_baseline_xb_priorpackage
        ) = MagicMock()

        for xbox_platform in self.XBOX_PLATFORMS:
            result = self.run_patch_frosty(
                platform=xbox_platform, args=self.DEFAULT_ARGS, license_args=self.LICENSEE_ARGS
            )

            fetch_xb_baseline_package_call = self.CALL_TO_FETCH_XB_BASEPACKAGE

            assert (
                fetch_xb_baseline_package_call
                not in mock_fetch_baseline_xb_priorpackage.call_args_list
            )
            assert result.exit_code == 0

    @patch("dice_elipy_scripts.patch_frosty.SETTINGS")
    def test_basepackage_not_in_frosty_args_when_fetch_xb_basepackage_false_in_config_and_xbox(
        self, mock_settings
    ):
        mock_frosty = self.mock_packageutils.return_value.frosty = MagicMock()

        # Given fetch_xb_basepackage false in config and xbox platform
        mock_settings.get.return_value = False

        for xbox_platform in self.XBOX_PLATFORMS:
            result = self.run_patch_frosty(
                platform=xbox_platform, args=self.DEFAULT_ARGS, license_args=self.LICENSEE_ARGS
            )

            # The args passed to frosty DO NOT INCLUDE the BASEPACKAGE arg
            passed_frosty_args = mock_frosty.call_args[1]["frosty_args"]
            assert not any([arg.startswith("BASEPACKAGE=") for arg in passed_frosty_args])
            assert result.exit_code == 0

    @patch("dice_elipy_scripts.patch_frosty.SETTINGS")
    def test_basepackage_in_frosty_args_when_fetch_xb_basepackage_true_in_config_and_xbox(
        self, mock_settings
    ):
        mock_frosty = self.mock_packageutils.return_value.frosty = MagicMock()

        # Given fetch_xb_basepackage true in config and xbox platform
        mock_settings.get.return_value = True

        for xbox_platform in self.XBOX_PLATFORMS:
            result = self.run_patch_frosty(
                platform=xbox_platform, args=self.DEFAULT_ARGS, license_args=self.LICENSEE_ARGS
            )

            # The args passed to frosty INCLUDE the BASEPACKAGE
            passed_frosty_args = mock_frosty.call_args[1]["frosty_args"]
            assert any([arg.startswith("BASEPACKAGE=") for arg in passed_frosty_args])
            assert result.exit_code == 0

    def test_basepackage_not_in_frosty_args_when_fetch_xb_basepackage_not_in_config_and_xbox(self):
        mock_frosty = self.mock_packageutils.return_value.frosty = MagicMock()

        # Given fetch_xb_basepackage not in config and xbox platform
        try:
            del self.mock_settings_get.config["default"]["fetch_xb_basepackage"]
        except KeyError:
            pass

        for xbox_platform in self.XBOX_PLATFORMS:
            result = self.run_patch_frosty(
                platform=xbox_platform, args=self.DEFAULT_ARGS, license_args=self.LICENSEE_ARGS
            )

            # The args passed to frosty DO NOT INCLUDE the BASEPACKAGE
            passed_frosty_args = mock_frosty.call_args[1]["frosty_args"]
            assert not any([arg.startswith("BASEPACKAGE=") for arg in passed_frosty_args])
            assert result.exit_code == 0

    def test_xb1_battlefieldgame(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, [self.ARGUMENT_PLATFORM_1] + self.DEFAULT_ARGS + self.LICENSEE_ARGS
        )
        assert result.exit_code == 0

    def test_xbsx_battlefieldgame(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, [self.ARGUMENT_PLATFORM_2] + self.DEFAULT_ARGS + self.LICENSEE_ARGS
        )
        assert result.exit_code == 0

    def test_ps4(self):
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_3] + self.DEFAULT_ARGS)
        assert result.exit_code == 0

    def test_ps5(self):
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_4] + self.DEFAULT_ARGS)
        assert result.exit_code == 0

    def test_win64(self):
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_5] + self.DEFAULT_ARGS)
        assert result.exit_code == 0

    def test_copy_submissionvalidator_xb1(self):
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_1] + self.DEFAULT_ARGS)
        assert result.exit_code == 0
        self.mock_packageutils.return_value.copy_submissionvalidator.assert_called_once()

    def test_copy_submissionvalidator_xbsx(self):
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_2] + self.DEFAULT_ARGS)
        assert result.exit_code == 0
        self.mock_packageutils.return_value.copy_submissionvalidator.assert_called_once()

    def test_not_copy_submissionvalidator_new_fb(self):
        self.mock_read_fb_version.return_value = "2022-1-PR2"
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_1] + self.DEFAULT_ARGS)
        assert result.exit_code == 0
        assert not self.mock_packageutils.return_value.copy_submissionvalidator.called

    def test_not_copy_submissionvalidator_ps4(self):
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_3] + self.DEFAULT_ARGS)
        assert result.exit_code == 0
        assert not self.mock_packageutils.return_value.copy_submissionvalidator.called

    def test_not_first_patch_xbsx(self):
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_2] + self.DEFAULT_ARGS)
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_baseline_xb_priorpackage.assert_called_once_with(
            data_branch=self.VALUE_PATCH_DATA_BRANCH,
            data_changelist=self.VALUE_PATCH_DATA_CHANGELIST,
            code_branch=self.VALUE_PATCH_CODE_BRANCH,
            code_changelist=self.VALUE_PATCH_CODE_CHANGELIST,
            package_type="patch",
            config=self.ARGUMENT_CONFIG,
            region=self.VALUE_REGION,
            destination=os.path.join("tnt_root", "local", "basepriorpackage"),
            platform=self.ARGUMENT_PLATFORM_2,
            combine_data_branch=None,
            combine_data_changelist=None,
            combine_code_branch=None,
            combine_code_changelist=None,
        )

    def test_first_patch_xbsx(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, [self.ARGUMENT_PLATFORM_2] + self.DEFAULT_ARGS + [self.OPTION_FIRST_PATCH]
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_baseline_xb_priorpackage.assert_called_once_with(
            data_branch=self.VALUE_DISC_DATA_BRANCH,
            data_changelist=self.VALUE_DISC_DATA_CHANGELIST,
            code_branch=self.VALUE_DISC_CODE_BRANCH,
            code_changelist=self.VALUE_DISC_CODE_CHANGELIST,
            package_type="digital",
            config=self.ARGUMENT_CONFIG,
            region=self.VALUE_REGION,
            destination=os.path.join("tnt_root", "local", "basepriorpackage"),
            platform=self.ARGUMENT_PLATFORM_2,
            combine_data_branch=None,
            combine_data_changelist=None,
            combine_code_branch=None,
            combine_code_changelist=None,
        )

    def test_not_first_patch_xbsx_combine(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, [self.ARGUMENT_PLATFORM_2] + self.DEFAULT_ARGS + self.COMBINE_ARGS
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_baseline_xb_priorpackage.assert_called_once_with(
            data_branch=self.VALUE_PATCH_DATA_BRANCH,
            data_changelist=self.VALUE_PATCH_DATA_CHANGELIST,
            code_branch=self.VALUE_PATCH_CODE_BRANCH,
            code_changelist=self.VALUE_PATCH_CODE_CHANGELIST,
            package_type="patch_combine",
            config=self.ARGUMENT_CONFIG,
            region=self.VALUE_REGION,
            destination=os.path.join("tnt_root", "local", "basepriorpackage"),
            platform=self.ARGUMENT_PLATFORM_2,
            combine_data_branch=self.VALUE_COMBINE_PATCH_DATA_BRANCH,
            combine_data_changelist=self.VALUE_COMBINE_PATCH_DATA_CHANGELIST,
            combine_code_branch=self.VALUE_COMBINE_PATCH_CODE_BRANCH,
            combine_code_changelist=self.VALUE_COMBINE_PATCH_CODE_CHANGELIST,
        )

    def test_first_patch_xbsx_combine(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_2]
            + self.DEFAULT_ARGS
            + self.COMBINE_ARGS
            + [self.OPTION_FIRST_PATCH],
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_baseline_xb_priorpackage.assert_called_once_with(
            data_branch=self.VALUE_DISC_DATA_BRANCH,
            data_changelist=self.VALUE_DISC_DATA_CHANGELIST,
            code_branch=self.VALUE_DISC_CODE_BRANCH,
            code_changelist=self.VALUE_DISC_CODE_CHANGELIST,
            package_type="digital_combine",
            config=self.ARGUMENT_CONFIG,
            region=self.VALUE_REGION,
            destination=os.path.join("tnt_root", "local", "basepriorpackage"),
            platform=self.ARGUMENT_PLATFORM_2,
            combine_data_branch=self.VALUE_COMBINE_DISC_DATA_BRANCH,
            combine_data_changelist=self.VALUE_COMBINE_DISC_DATA_CHANGELIST,
            combine_code_branch=self.VALUE_COMBINE_DISC_CODE_BRANCH,
            combine_code_changelist=self.VALUE_COMBINE_DISC_CODE_CHANGELIST,
        )

    def test_use_combine_bundles_basic(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_5]
            + self.DEFAULT_ARGS
            + [self.OPTION_USE_COMBINE_BUNDLES, "true"],
        )
        assert result.exit_code == 0

    def test_use_combine_bundles_run_combine(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_5]
            + self.DEFAULT_ARGS
            + [self.OPTION_USE_COMBINE_BUNDLES, "true"],
        )
        assert result.exit_code == 0
        self.mock_avalanche_combine.assert_called_once_with(
            input_dir_1=os.path.join("tnt_root", "local", "deployed_bundles_main"),
            input_dir_2=os.path.join("tnt_root", "local", "deployed_bundles_combine"),
            output_dir=os.path.join("tnt_root", "local", "head_bundles_combine"),
            extra_combine_args=["-s", "project-combine-hres.yaml"],
        )

    def test_use_combine_bundles_run_combine_xbsx(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_2]
            + self.DEFAULT_ARGS
            + [self.OPTION_USE_COMBINE_BUNDLES, "true"],
        )
        assert result.exit_code == 0
        self.mock_avalanche_combine.assert_called_once_with(
            input_dir_1=os.path.join("tnt_root", "local", "deployed_bundles_main"),
            input_dir_2=os.path.join("tnt_root", "local", "deployed_bundles_combine"),
            output_dir=os.path.join("tnt_root", "local", "head_bundles_combine"),
            extra_combine_args=["-s", "project-combine-hres-smart-delivery.yaml"],
        )

    def test_combine_bundles_run_with_combine_settings_file(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_5]
            + self.DEFAULT_ARGS
            + [self.OPTION_USE_COMBINE_BUNDLES, "true"]
            + [self.OPTION_COMBINE_SETTINGS_FILE, self.VALUE_COMBINE_SETTINGS_FILE],
        )
        assert result.exit_code == 0
        self.mock_avalanche_combine.assert_called_once_with(
            input_dir_1=os.path.join("tnt_root", "local", "deployed_bundles_main"),
            input_dir_2=os.path.join("tnt_root", "local", "deployed_bundles_combine"),
            output_dir=os.path.join("tnt_root", "local", "head_bundles_combine"),
            extra_combine_args=["-s", self.VALUE_COMBINE_SETTINGS_FILE],
        )

    def test_use_combine_fetch_baseline(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_2]
            + self.DEFAULT_ARGS
            + [self.OPTION_USE_COMBINE_BUNDLES, "true"],
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_baseline_bundles.assert_called_once_with(
            data_branch=self.VALUE_PATCH_DATA_BRANCH,
            data_changelist=self.VALUE_PATCH_DATA_CHANGELIST,
            code_branch=self.VALUE_PATCH_CODE_BRANCH,
            code_changelist=self.VALUE_PATCH_CODE_CHANGELIST,
            platform=self.ARGUMENT_PLATFORM_2,
            dest=os.path.join("tnt_root", "local", "baseline_bundles"),
            bundles_dir_name="combine_bundles",
        )

    def test_use_combine_fetch_baseline_first_patch(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_2]
            + self.DEFAULT_ARGS
            + [self.OPTION_USE_COMBINE_BUNDLES, "true", self.OPTION_FIRST_PATCH],
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_baseline_bundles_head.assert_called_once_with(
            data_branch=self.VALUE_DISC_DATA_BRANCH,
            data_changelist=self.VALUE_DISC_DATA_CHANGELIST,
            code_branch=self.VALUE_DISC_CODE_BRANCH,
            code_changelist=self.VALUE_DISC_CODE_CHANGELIST,
            platform=self.ARGUMENT_PLATFORM_2,
            dest=os.path.join("tnt_root", "local", "baseline_bundles"),
            bundles_dir_name="combine_bundles",
        )

    def test_use_combine_fetch_baseline_standalone_patch_baseline(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_2]
            + self.DEFAULT_ARGS
            + [self.OPTION_USE_COMBINE_BUNDLES, "true", self.OPTION_STANDALONE_PATCH_BASELINE],
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_baseline_bundles.assert_called_once_with(
            data_branch=self.VALUE_PATCH_DATA_BRANCH,
            data_changelist=self.VALUE_PATCH_DATA_CHANGELIST,
            code_branch=self.VALUE_PATCH_CODE_BRANCH,
            code_changelist=self.VALUE_PATCH_CODE_CHANGELIST,
            platform=self.ARGUMENT_PLATFORM_2,
            dest=os.path.join("tnt_root", "local", "baseline_bundles"),
            bundles_dir_name="bundles",
        )

    def test_use_combine_fetch_baseline_first_patch_standalone_disc_baseline(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_2]
            + self.DEFAULT_ARGS
            + [
                self.OPTION_USE_COMBINE_BUNDLES,
                "true",
                self.OPTION_FIRST_PATCH,
                self.OPTION_STANDALONE_DISC_BASELINE,
            ],
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_baseline_bundles_head.assert_called_once_with(
            data_branch=self.VALUE_DISC_DATA_BRANCH,
            data_changelist=self.VALUE_DISC_DATA_CHANGELIST,
            code_branch=self.VALUE_DISC_CODE_BRANCH,
            code_changelist=self.VALUE_DISC_CODE_CHANGELIST,
            platform=self.ARGUMENT_PLATFORM_2,
            dest=os.path.join("tnt_root", "local", "baseline_bundles"),
            bundles_dir_name="bundles",
        )

    def test_use_combine_ddelta(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_2]
            + self.DEFAULT_ARGS
            + [self.OPTION_USE_COMBINE_BUNDLES, "true"],
        )
        assert result.exit_code == 0
        self.mock_avalanche_ddelta.assert_called_once_with(
            os.path.join("tnt_root", "local", "head_bundles_combine"),
            os.path.join("tnt_root", "local", "baseline_bundles"),
            os.path.join("tnt_root", "local", "current_delta"),
        )

    def test_use_combine_ddelta_use_head_as_base(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_2]
            + self.DEFAULT_ARGS
            + [
                self.OPTION_USE_COMBINE_BUNDLES,
                "true",
                self.OPTION_USE_HEAD_BUNDLES_AS_BASE_BUNDLES,
            ],
        )
        assert result.exit_code == 0
        self.mock_avalanche_ddelta.assert_called_once_with(
            os.path.join("tnt_root", "local", "head_bundles_combine"),
            os.path.join("tnt_root", "local", "head_bundles_combine"),
            os.path.join("tnt_root", "local", "current_delta"),
        )

    def test_fetch_baseline_win64_chunkmanifest_non_win64(self):
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_2] + self.DEFAULT_ARGS)
        assert result.exit_code == 0
        assert self.mock_filerutils.return_value.fetch_baseline_win64_chunkmanifest.call_count == 0

    def test_fetch_baseline_win64_chunkmanifest_win64(self):
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_5] + self.DEFAULT_ARGS)
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_baseline_win64_chunkmanifest.assert_has_calls(
            [
                call(
                    data_branch=self.VALUE_PATCH_DATA_BRANCH,
                    data_changelist=self.VALUE_PATCH_DATA_CHANGELIST,
                    code_branch=self.VALUE_PATCH_CODE_BRANCH,
                    code_changelist=self.VALUE_PATCH_CODE_CHANGELIST,
                    package_type="patch",
                    config=self.ARGUMENT_CONFIG,
                    region=self.VALUE_REGION,
                    destination=os.path.join("tnt_root", "local", "win64_base"),
                    combine_data_branch=None,
                    combine_data_changelist=None,
                    combine_code_branch=None,
                    combine_code_changelist=None,
                ),
                call(
                    data_branch=self.VALUE_PATCH_DATA_BRANCH,
                    data_changelist=self.VALUE_PATCH_DATA_CHANGELIST,
                    code_branch=self.VALUE_PATCH_CODE_BRANCH,
                    code_changelist=self.VALUE_PATCH_CODE_CHANGELIST,
                    package_type="patch",
                    config=self.ARGUMENT_CONFIG,
                    region=self.VALUE_REGION,
                    destination=os.path.join("tnt_root", "local", "win64_live"),
                    combine_data_branch=None,
                    combine_data_changelist=None,
                    combine_code_branch=None,
                    combine_code_changelist=None,
                ),
            ]
        )
        assert self.mock_filerutils.return_value.fetch_baseline_win64_chunkmanifest.call_count == 2

    def test_fetch_baseline_win64_chunkmanifest_win64_first_patch(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, [self.ARGUMENT_PLATFORM_5] + self.DEFAULT_ARGS + [self.OPTION_FIRST_PATCH]
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_baseline_win64_chunkmanifest.assert_called_once_with(
            data_branch=self.VALUE_DISC_DATA_BRANCH,
            data_changelist=self.VALUE_DISC_DATA_CHANGELIST,
            code_branch=self.VALUE_DISC_CODE_BRANCH,
            code_changelist=self.VALUE_DISC_CODE_CHANGELIST,
            package_type="digital",
            config=self.ARGUMENT_CONFIG,
            region=self.VALUE_REGION,
            destination=os.path.join("tnt_root", "local", "win64_base"),
            combine_data_branch=None,
            combine_data_changelist=None,
            combine_code_branch=None,
            combine_code_changelist=None,
        )

    def test_fetch_baseline_win64_chunkmanifest_combine(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_5]
            + self.DEFAULT_ARGS
            + [
                self.OPTION_COMBINE_PATCH_CODE_BRANCH,
                self.VALUE_COMBINE_PATCH_CODE_BRANCH,
                self.OPTION_COMBINE_PATCH_CODE_CHANGELIST,
                self.VALUE_COMBINE_PATCH_CODE_CHANGELIST,
                self.OPTION_COMBINE_PATCH_DATA_BRANCH,
                self.VALUE_COMBINE_PATCH_DATA_BRANCH,
                self.OPTION_COMBINE_PATCH_DATA_CHANGELIST,
                self.VALUE_COMBINE_PATCH_DATA_CHANGELIST,
            ],
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_baseline_win64_chunkmanifest.assert_has_calls(
            [
                call(
                    data_branch=self.VALUE_PATCH_DATA_BRANCH,
                    data_changelist=self.VALUE_PATCH_DATA_CHANGELIST,
                    code_branch=self.VALUE_PATCH_CODE_BRANCH,
                    code_changelist=self.VALUE_PATCH_CODE_CHANGELIST,
                    package_type="patch",
                    config=self.ARGUMENT_CONFIG,
                    region=self.VALUE_REGION,
                    destination=os.path.join("tnt_root", "local", "win64_base"),
                    combine_data_branch=self.VALUE_COMBINE_PATCH_DATA_BRANCH,
                    combine_data_changelist=self.VALUE_COMBINE_PATCH_DATA_CHANGELIST,
                    combine_code_branch=self.VALUE_COMBINE_PATCH_CODE_BRANCH,
                    combine_code_changelist=self.VALUE_COMBINE_PATCH_CODE_CHANGELIST,
                ),
                call(
                    data_branch=self.VALUE_PATCH_DATA_BRANCH,
                    data_changelist=self.VALUE_PATCH_DATA_CHANGELIST,
                    code_branch=self.VALUE_PATCH_CODE_BRANCH,
                    code_changelist=self.VALUE_PATCH_CODE_CHANGELIST,
                    package_type="patch",
                    config=self.ARGUMENT_CONFIG,
                    region=self.VALUE_REGION,
                    destination=os.path.join("tnt_root", "local", "win64_live"),
                    combine_data_branch=self.VALUE_COMBINE_PATCH_DATA_BRANCH,
                    combine_data_changelist=self.VALUE_COMBINE_PATCH_DATA_CHANGELIST,
                    combine_code_branch=self.VALUE_COMBINE_PATCH_CODE_BRANCH,
                    combine_code_changelist=self.VALUE_COMBINE_PATCH_CODE_CHANGELIST,
                ),
            ]
        )

    def test_fetch_baseline_win64_chunkmanifest_combine_first_patch(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_5]
            + self.DEFAULT_ARGS
            + [
                self.OPTION_FIRST_PATCH,
                self.OPTION_COMBINE_DISC_CODE_BRANCH,
                self.VALUE_COMBINE_DISC_CODE_BRANCH,
                self.OPTION_COMBINE_DISC_CODE_CHANGELIST,
                self.VALUE_COMBINE_DISC_CODE_CHANGELIST,
                self.OPTION_COMBINE_DISC_DATA_BRANCH,
                self.VALUE_COMBINE_DISC_DATA_BRANCH,
                self.OPTION_COMBINE_DISC_DATA_CHANGELIST,
                self.VALUE_COMBINE_DISC_DATA_CHANGELIST,
            ],
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_baseline_win64_chunkmanifest.assert_called_once_with(
            data_branch=self.VALUE_DISC_DATA_BRANCH,
            data_changelist=self.VALUE_DISC_DATA_CHANGELIST,
            code_branch=self.VALUE_DISC_CODE_BRANCH,
            code_changelist=self.VALUE_DISC_CODE_CHANGELIST,
            package_type="digital",
            config=self.ARGUMENT_CONFIG,
            region=self.VALUE_REGION,
            destination=os.path.join("tnt_root", "local", "win64_base"),
            combine_data_branch=self.VALUE_COMBINE_DISC_DATA_BRANCH,
            combine_data_changelist=self.VALUE_COMBINE_DISC_DATA_CHANGELIST,
            combine_code_branch=self.VALUE_COMBINE_DISC_CODE_BRANCH,
            combine_code_changelist=self.VALUE_COMBINE_DISC_CODE_CHANGELIST,
        )

    def test_fetch_code(self):
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_2] + self.DEFAULT_ARGS)
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_code.assert_called_once_with(
            self.VALUE_CODE_BRANCH,
            self.VALUE_CODE_CHANGELIST,
            self.ARGUMENT_PLATFORM_2,
            self.ARGUMENT_CONFIG,
        )

    def test_fetch_code_win64game(self):
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_5] + self.DEFAULT_ARGS)
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_code.assert_called_once_with(
            self.VALUE_CODE_BRANCH, self.VALUE_CODE_CHANGELIST, "win64game", self.ARGUMENT_CONFIG
        )

    def test_fetch_code_win64game_win64trial(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, [self.ARGUMENT_PLATFORM_5] + self.DEFAULT_ARGS + [self.OPTION_USE_WIN64TRIAL]
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_code.assert_has_calls(
            [
                call(
                    self.VALUE_CODE_BRANCH,
                    self.VALUE_CODE_CHANGELIST,
                    "win64game",
                    self.ARGUMENT_CONFIG,
                ),
                call(
                    self.VALUE_CODE_BRANCH,
                    self.VALUE_CODE_CHANGELIST,
                    "win64trial",
                    self.ARGUMENT_CONFIG,
                    purge=False,
                ),
            ]
        )

    def test_fetch_code_use_combine(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_2]
            + self.DEFAULT_ARGS
            + [
                self.OPTION_USE_COMBINE_BUNDLES,
                "true",
                self.OPTION_COMBINE_CODE_BRANCH,
                self.VALUE_COMBINE_CODE_BRANCH,
                self.OPTION_COMBINE_CODE_CHANGELIST,
                self.VALUE_COMBINE_CODE_CHANGELIST,
            ],
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_code.assert_has_calls(
            [
                call(
                    self.VALUE_CODE_BRANCH,
                    self.VALUE_CODE_CHANGELIST,
                    self.ARGUMENT_PLATFORM_2,
                    self.ARGUMENT_CONFIG,
                ),
                call(
                    self.VALUE_COMBINE_CODE_BRANCH,
                    self.VALUE_COMBINE_CODE_CHANGELIST,
                    self.ARGUMENT_PLATFORM_2,
                    self.ARGUMENT_CONFIG,
                ),
            ]
        )

    def test_deploy_frosty_build(self):
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_2] + self.DEFAULT_ARGS)
        assert result.exit_code == 0
        self.mock_filerutils.return_value.deploy_frosty_build.assert_called_once_with(
            data_changelist=self.VALUE_DATA_CHANGELIST,
            data_branch=self.VALUE_DATA_BRANCH,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            code_branch=self.VALUE_CODE_BRANCH,
            package_type="patch",
            config=self.ARGUMENT_CONFIG,
            region=self.VALUE_REGION,
            dataset=self.VALUE_DATA_DIRECTORY,
            platform=self.ARGUMENT_PLATFORM_2,
        )

    def test_deploy_frosty_build_combine(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_2]
            + self.DEFAULT_ARGS
            + [
                self.OPTION_USE_COMBINE_BUNDLES,
                "true",
                self.OPTION_COMBINE_CODE_BRANCH,
                self.VALUE_COMBINE_CODE_BRANCH,
                self.OPTION_COMBINE_CODE_CHANGELIST,
                self.VALUE_COMBINE_CODE_CHANGELIST,
                self.OPTION_COMBINE_DATA_BRANCH,
                self.VALUE_COMBINE_DATA_BRANCH,
                self.OPTION_COMBINE_DATA_CHANGELIST,
                self.VALUE_COMBINE_DATA_CHANGELIST,
            ],
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.deploy_frosty_build.assert_called_once_with(
            data_changelist=self.VALUE_DATA_CHANGELIST,
            data_branch=self.VALUE_DATA_BRANCH,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            code_branch=self.VALUE_CODE_BRANCH,
            package_type="patch_combine",
            config=self.ARGUMENT_CONFIG,
            region=self.VALUE_REGION,
            dataset=self.VALUE_DATA_DIRECTORY,
            platform=self.ARGUMENT_PLATFORM_2,
            combine_code_changelist=self.VALUE_COMBINE_CODE_CHANGELIST,
            combine_code_branch=self.VALUE_COMBINE_CODE_BRANCH,
            combine_data_changelist=self.VALUE_COMBINE_DATA_CHANGELIST,
            combine_data_branch=self.VALUE_COMBINE_DATA_BRANCH,
        )

    def test_fetch_baseline_ps_package(self):
        self.mock_packageutils.return_value.get_disc_package_type.return_value = "digital"
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_4] + self.DEFAULT_ARGS)
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_baseline_ps_package.assert_called_once_with(
            data_branch=self.VALUE_PATCH_DATA_BRANCH,
            data_changelist=self.VALUE_PATCH_DATA_CHANGELIST,
            code_branch=self.VALUE_PATCH_CODE_BRANCH,
            code_changelist=self.VALUE_PATCH_CODE_CHANGELIST,
            package_type="patch",
            config="retail",
            region=self.VALUE_REGION,
            destination=os.path.join("tnt_root", "local", "ps5_live_package"),
            platform=self.ARGUMENT_PLATFORM_4,
            combine_data_branch=None,
            combine_data_changelist=None,
            combine_code_branch=None,
            combine_code_changelist=None,
        )

    def test_fetch_baseline_ps_package_first_patch(self):
        self.mock_packageutils.return_value.get_disc_package_type.return_value = "digital"
        runner = CliRunner()
        result = runner.invoke(
            cli, [self.ARGUMENT_PLATFORM_4] + self.DEFAULT_ARGS + [self.OPTION_FIRST_PATCH]
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_baseline_ps_package.assert_called_once_with(
            data_branch=self.VALUE_DISC_DATA_BRANCH,
            data_changelist=self.VALUE_DISC_DATA_CHANGELIST,
            code_branch=self.VALUE_DISC_CODE_BRANCH,
            code_changelist=self.VALUE_DISC_CODE_CHANGELIST,
            package_type="digital",
            config="retail",
            region=self.VALUE_REGION,
            destination=os.path.join("tnt_root", "local", "ps5_disk_package"),
            platform=self.ARGUMENT_PLATFORM_4,
            combine_data_branch=None,
            combine_data_changelist=None,
            combine_code_branch=None,
            combine_code_changelist=None,
        )

    def test_fetch_baseline_ps_package_combine(self):
        self.mock_packageutils.return_value.get_disc_package_type.return_value = "digital"
        runner = CliRunner()
        result = runner.invoke(
            cli, [self.ARGUMENT_PLATFORM_4] + self.DEFAULT_ARGS + self.COMBINE_ARGS
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_baseline_ps_package.assert_called_once_with(
            data_branch=self.VALUE_PATCH_DATA_BRANCH,
            data_changelist=self.VALUE_PATCH_DATA_CHANGELIST,
            code_branch=self.VALUE_PATCH_CODE_BRANCH,
            code_changelist=self.VALUE_PATCH_CODE_CHANGELIST,
            package_type="patch_combine",
            config="retail",
            region=self.VALUE_REGION,
            destination=os.path.join("tnt_root", "local", "ps5_live_package"),
            platform=self.ARGUMENT_PLATFORM_4,
            combine_data_branch=self.VALUE_COMBINE_PATCH_DATA_BRANCH,
            combine_data_changelist=self.VALUE_COMBINE_PATCH_DATA_CHANGELIST,
            combine_code_branch=self.VALUE_COMBINE_PATCH_CODE_BRANCH,
            combine_code_changelist=self.VALUE_COMBINE_PATCH_CODE_CHANGELIST,
        )

    def test_fetch_baseline_ps_package_first_patch(self):
        self.mock_packageutils.return_value.get_disc_package_type.return_value = "digital"
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_4]
            + self.DEFAULT_ARGS
            + self.COMBINE_ARGS
            + [self.OPTION_FIRST_PATCH],
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.fetch_baseline_ps_package.assert_called_once_with(
            data_branch=self.VALUE_DISC_DATA_BRANCH,
            data_changelist=self.VALUE_DISC_DATA_CHANGELIST,
            code_branch=self.VALUE_DISC_CODE_BRANCH,
            code_changelist=self.VALUE_DISC_CODE_CHANGELIST,
            package_type="digital_combine",
            config="retail",
            region=self.VALUE_REGION,
            destination=os.path.join("tnt_root", "local", "ps5_disk_package"),
            platform=self.ARGUMENT_PLATFORM_4,
            combine_data_branch=self.VALUE_COMBINE_DISC_DATA_BRANCH,
            combine_data_changelist=self.VALUE_COMBINE_DISC_DATA_CHANGELIST,
            combine_code_branch=self.VALUE_COMBINE_DISC_CODE_BRANCH,
            combine_code_changelist=self.VALUE_COMBINE_DISC_CODE_CHANGELIST,
        )

    def test_fetch_contversion(self):
        self.mock_packageutils.return_value.get_disc_package_type.return_value = "digital"
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_4] + self.DEFAULT_ARGS)
        assert result.exit_code == 0
        self.mock_packageutils.return_value.fetch_contversion.assert_called_once_with(
            "baseline_build_path",
            region="ww",
            first_patch=False,
            inc_version=1,
            config="retail",
            use_combine_bundles=False,
            standalone_disc_baseline=False,
            standalone_patch_baseline=False,
            combine_data_branch=None,
            combine_data_changelist=None,
            combine_code_branch=None,
            combine_code_changelist=None,
        )

    def test_fetch_contversion_combine(self):
        self.mock_packageutils.return_value.get_disc_package_type.return_value = "digital"
        runner = CliRunner()
        result = runner.invoke(
            cli, [self.ARGUMENT_PLATFORM_4] + self.DEFAULT_ARGS + self.COMBINE_ARGS
        )
        assert result.exit_code == 0
        self.mock_packageutils.return_value.fetch_contversion.assert_called_once_with(
            "baseline_build_path",
            region="ww",
            first_patch=False,
            inc_version=1,
            config="retail",
            use_combine_bundles=True,
            standalone_disc_baseline=False,
            standalone_patch_baseline=False,
            combine_data_branch=self.VALUE_COMBINE_PATCH_DATA_BRANCH,
            combine_data_changelist=self.VALUE_COMBINE_PATCH_DATA_CHANGELIST,
            combine_code_branch=self.VALUE_COMBINE_PATCH_CODE_BRANCH,
            combine_code_changelist=self.VALUE_COMBINE_PATCH_CODE_CHANGELIST,
        )

    def test_deploy_avalanche_combine_output(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, [self.ARGUMENT_PLATFORM_4] + self.DEFAULT_ARGS + self.COMBINE_ARGS
        )
        assert result.exit_code == 0
        self.mock_filerutils.return_value.deploy_avalanche_combine_output.assert_called_once_with(
            source=os.path.join("tnt_root", "local", "head_bundles_combine"),
            data_branch=self.VALUE_DATA_BRANCH,
            data_changelist=self.VALUE_DATA_CHANGELIST,
            code_branch=self.VALUE_CODE_BRANCH,
            code_changelist=self.VALUE_CODE_CHANGELIST,
            platform=self.ARGUMENT_PLATFORM_4,
        )

    def test_win64_oreans(self):
        mock_frosty = self.mock_packageutils.return_value.frosty = MagicMock()
        self.run_patch_frosty(
            platform=self.ARGUMENT_PLATFORM_5,
            args=self.DEFAULT_ARGS + [self.OPTION_USE_OREANS],
            license_args=self.LICENSEE_ARGS,
        )
        mock_frosty.assert_called_once()
        passed_frosty_args = mock_frosty.call_args[1]["frosty_args"]
        self.assertIn("EXECUTABLE_POSTFIX=" + OREANS_OUTPUT_SUFFIX, passed_frosty_args)

    @patch.dict(os.environ, {"FB_DEFAULT_PLATFORM_DB": "GameData.other-branch.{}"})
    def test_virtual_branch_override(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_5]
            + self.DEFAULT_ARGS
            + [self.OPTION_VIRTUAL_BRANCH_OVERRIDE, "true"],
        )
        assert result.exit_code == 0
        mock_frosty = self.mock_packageutils.return_value.frosty
        mock_frosty.assert_called_once()
        passed_frosty_args = mock_frosty.call_args[1]["frosty_args"]
        self.assertIn("DATABASE_ID=GameData.other-branch.{}", passed_frosty_args)
        self.assertIn("FB_BRANCH_ID=other-branch", passed_frosty_args)

    @patch("elipy2.core.robocopy")
    @patch("os.listdir")
    @patch.dict(os.environ, {"FB_DEFAULT_PLATFORM_DB": "GameData.other-branch.{}"})
    def test_frostyisotool_exception(self, mock_listdir, mock_robocopy):
        mock_listdir.return_value = ["some_file.txt", "other_file.pkg.verify.log"]
        self.mock_packageutils.return_value.frosty.side_effect = Exception()
        runner = CliRunner()
        result = runner.invoke(cli, [self.ARGUMENT_PLATFORM_5] + self.DEFAULT_ARGS)
        assert mock_robocopy.call_count == 1

    def test_steam_build(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_5] + self.DEFAULT_ARGS + [self.OPTION_STEAM_BUILD, "True"],
        )
        SUPERBUNDLE_DELTA_DIR = os.path.join("tnt_root", "local", "current_delta")
        WIN64_DIGITAL_BASE_DIR = os.path.join("tnt_root", "local", "win64_base")
        CURRENT_LIVE_PATCH_DIR = os.path.join("tnt_root", "local", "win64_live")

        assert result.exit_code == 0
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww",
            frosty_args=[
                "USE_ALREADY_CREATED_SUPERBUNDLES=true",
                f"SUPERBUNDLE_DELTA_DIR={SUPERBUNDLE_DELTA_DIR}",
                "USE_ALREADYCREATED_SUPERBUNDLES=true",
                "ELSA_PATCH=true",
                "IS_TRIAL_BUILD=False",
                "USE_DENUVO=False",
                "PC_PUT_BUILD_LABEL_INTO_VERSION=True",
                f"WIN64_DIGITAL_BASE_DIR={WIN64_DIGITAL_BASE_DIR}",
                f"CURRENT_LIVE_PATCH_DIR={CURRENT_LIVE_PATCH_DIR}",
                "WIN32_DIGITAL_PLATFORM=Steam",
                'STEAM_BUILD_DESCRIPTION="retail build from other-branch on CL 5678"',
            ],
        )

    @patch(
        "elipy2.steam_utils.SteamUtils.download_steam_sdk",
        MagicMock(return_value="D:/packages/SteamSDK/version"),
    )
    @patch(
        "elipy2.steam_utils.SteamUtils.get_ess_steam_config_name",
        MagicMock(return_value="DRE_SVC_STEAM01"),
    )
    @patch(
        "elipy2.steam_utils.SteamUtils.prepare_steam_user_session",
        MagicMock(return_value="DRE_STEAM_ACCOUNT"),
    )
    @patch("elipy2.steam_utils.SETTINGS.get", MagicMock(return_value="test.yml"))
    @patch(
        "elipy2.steam_utils.SETTINGS.load_auxiliary_file_contents",
        MagicMock(
            return_value={
                "unique_sessions": {
                    "other-branch": {
                        "ww": {
                            "retail": {
                                "frosty": ["username"],
                            }
                        }
                    }
                }
            }
        ),
    )
    def test_steam_build_combined_bundles(self):
        runner = CliRunner()
        result = runner.invoke(
            cli,
            [self.ARGUMENT_PLATFORM_5]
            + self.DEFAULT_ARGS
            + [
                self.OPTION_STEAM_BUILD,
                "True",
                self.OPTION_USE_COMBINE_BUNDLES,
                True,
                self.OPTION_STEAM_DRMWRAP,
            ],
        )
        ADDITIONAL_LOCAL_ROOT_DIR = os.path.join("tnt_root", "SP_Local")
        SUPERBUNDLE_DELTA_DIR = os.path.join("tnt_root", "local", "current_delta")
        WIN64_DIGITAL_BASE_DIR = os.path.join("tnt_root", "local", "win64_base")
        CURRENT_LIVE_PATCH_DIR = os.path.join("tnt_root", "local", "win64_live")

        assert result.exit_code == 0
        self.mock_packageutils.return_value.frosty.assert_called_once_with(
            region="ww",
            frosty_args=[
                f"ADDITIONAL_LOCAL_ROOT={ADDITIONAL_LOCAL_ROOT_DIR}",
                "USE_ALREADY_CREATED_SUPERBUNDLES=true",
                f"SUPERBUNDLE_DELTA_DIR={SUPERBUNDLE_DELTA_DIR}",
                "USE_ALREADYCREATED_SUPERBUNDLES=true",
                "ELSA_PATCH=true",
                "IS_TRIAL_BUILD=False",
                "USE_DENUVO=False",
                "PC_PUT_BUILD_LABEL_INTO_VERSION=True",
                f"WIN64_DIGITAL_BASE_DIR={WIN64_DIGITAL_BASE_DIR}",
                f"CURRENT_LIVE_PATCH_DIR={CURRENT_LIVE_PATCH_DIR}",
                "WIN32_DIGITAL_PLATFORM=Steam",
                'STEAM_BUILD_DESCRIPTION="retail build from other-branch on CL 5678"',
                "STEAM_DRMWRAP=True",
                f"STEAM_USERID=DRE_STEAM_ACCOUNT",
                "STEAM_DRM_LOCAL=True",
            ],
        )
