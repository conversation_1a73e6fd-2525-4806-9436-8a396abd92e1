   autotest_scheduler.run()
      autotest_scheduler.ProjectClass(kingston)
      AutotestMatrixFactory.getInstance(TestAutotestMatrix)
      autotest_scheduler.pipeline(groovy.lang.Closure)
         autotest_scheduler.allowBrokenBuildClaiming()
         autotest_scheduler.timestamps()
         autotest_scheduler.echo(Executing on agent [label:( scheduler && master ) || executor_agent])
         autotest_scheduler.stage(Validate test category, groovy.lang.Closure)
            autotest_scheduler.script(groovy.lang.Closure)
               autotest_scheduler.echo(Checking if the job has been orphaned...)
               autotest_scheduler.echo(Configuration for test category has been found. The job is not orphaned.)
         autotest_scheduler.stage(Determine changelists, groovy.lang.Closure)
            autotest_scheduler.script(groovy.lang.Closure)
               autotest_scheduler.echo(Determine changelists)
               BuildSelector.trigger(null, kin-dev, https://url.com)
         autotest_scheduler.stage(Trigger autotest jobs, groovy.lang.Closure)
            autotest_scheduler.script(groovy.lang.Closure)
               autotest_scheduler.retryOnFailureCause(3, [], groovy.lang.Closure)
                  autotest_scheduler.echo(Trigger Autotest jobs)
                  autotest_scheduler.AutotestRunCategory(null, kin-dev, TestAutotestMatrix, {ps4={dataChangelist=123, codeChangelist=234, clientBuildId=\\client\build\id, serverBuildId=\\server\build\id}}, [])
         autotest_scheduler.echo(Skipping stage Trigger LKG counters)
         autotest_scheduler.echo(Skipping stage Trigger register verified_for_preflight)
         autotest_scheduler.echo(Skipping stage Trigger LKG job for integrations)
         autotest_scheduler.echo(Skipping stage Trigger downstream autotests)
         autotest_scheduler.script(groovy.lang.Closure)
            autotest_scheduler.retryOnFailureCause(3, [], groovy.lang.Closure)
               autotest_scheduler.AutotestRegisterBilboResult(true, false, done, kin-dev, kindata, {ps4={dataChangelist=123, codeChangelist=234, clientBuildId=\\client\build\id, serverBuildId=\\server\build\id}}, awesomeTest, SUCCESS, [])
            autotest_scheduler.AutotestSetPipelineResult({absoluteUrl=http://example.com/dummy, buildVariables={}, changeSets=[], currentResult=SUCCESS, description=dummy, displayName=#1, duration=1, durationString=1 ms, fullDisplayName=dummy #1, fullProjectName=dummy, id=1, keepLog=false, nextBuild=null, number=1, previousBuild=null, projectName=dummy, result=SUCCESS, startTimeInMillis=1, timeInMillis=1, upstreamBuilds=[]}, SUCCESS)
            autotest_scheduler.SlackMessageNew({absoluteUrl=http://example.com/dummy, buildVariables={}, changeSets=[], currentResult=SUCCESS, description=dummy, displayName=#1, duration=1, durationString=1 ms, fullDisplayName=dummy #1, fullProjectName=dummy, id=1, keepLog=false, nextBuild=null, number=1, previousBuild=null, projectName=dummy, result=SUCCESS, startTimeInMillis=1, timeInMillis=1, upstreamBuilds=[]}, #cobra-test, kin)
