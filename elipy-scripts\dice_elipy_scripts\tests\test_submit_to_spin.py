"""
test_submit_to_spin.py

Unit testing for submit_to_spin
"""

import os
import tempfile
from unittest import TestCase, mock

from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from elipy2.exceptions import ELIPYException

from dice_elipy_scripts.submit_to_spin import (
    cli,
    collect_files_to_submit,
    prepare_aws_for_spin,
    upload_to_spin,
)


class TestSubmitToSpin(TestCase):
    MOCKED_PLATFORM_CONFIG = {
        "linuxserver": {
            "s3_bucket": "testbucket",
            "role_arn": "arn:aws:iam::1234567890:role/test-role",
            "access_key_id_key": "SPIN_USER_AWS_ACCESS_KEY_ID",
            "secret_access_key_key": "SPIN_USER_AWS_SECRET_ACCESS_KEY",
            "file_patterns": ["*_Binaries.zip", "build.json"],
        },
        "linux64": {
            "s3_bucket": "testbucket",
            "role_arn": "arn:aws:iam::1234567890:role/test-role",
            "access_key_id_key": "SPIN_USER_AWS_ACCESS_KEY_ID",
            "secret_access_key_key": "SPIN_USER_AWS_SECRET_ACCESS_KEY",
            "file_patterns": ["*", "build.json"],
        },
    }

    TEST_SECRETS_WHERE = {"project_secrets": True}
    VALUE_PROJECT = "codebranch"
    VALUE_CODE_BRANCH = "codebranch"
    VALUE_CODE_CHANGELIST = "123"
    VALUE_CONFIG = "final"
    VALUE_DATA_BRANCH = "databranch"
    VALUE_DATA_CHANGELIST = "456"
    VALUE_REGION = "ww"
    VALUE_LINUXSERVER = "linuxserver"
    VALUE_LINUX64 = "linux64"
    VALUE_FORMAT = "digital"

    @classmethod
    def setUpClass(cls):
        cls.runner = CliRunner()

    @mock.patch("elipy2.telemetry.upload_metrics", mock.MagicMock())
    @mock.patch("elipy2.SETTINGS.get", mock.MagicMock(side_effect=["project", "testbucket"]))
    @mock.patch("os.listdir", return_value=["file"])
    @mock.patch("elipy2.filer_paths.get_frosty_build_path", return_value="path/to")
    @mock.patch("dice_elipy_scripts.submit_to_spin.collect_files_to_submit", return_value=["file"])
    @mock.patch(
        "dice_elipy_scripts.submit_to_spin.prepare_aws_for_spin", return_value=mock.MagicMock()
    )
    @mock.patch("dice_elipy_scripts.submit_to_spin.upload_to_spin", return_value=True)
    @mock.patch(
        "dice_elipy_scripts.submit_to_spin.get_platform_configuration",
        return_value=MOCKED_PLATFORM_CONFIG[VALUE_LINUXSERVER],
    )
    def test_cli(
        self,
        mock_upload_to_spin,
        mock_prepare_aws_for_spin,
        mock_collect_files_to_submit,
        mock_get_frosty_build_path,
        _mock_listdir,
        mock_get_platform_configuration,
    ):
        args = [
            "--code-branch",
            self.VALUE_CODE_BRANCH,
            "--code-changelist",
            self.VALUE_CODE_CHANGELIST,
            "--config",
            self.VALUE_CONFIG,
            "--data-branch",
            self.VALUE_DATA_BRANCH,
            "--data-changelist",
            self.VALUE_DATA_CHANGELIST,
            "--region",
            self.VALUE_REGION,
            "--platform",
            self.VALUE_LINUXSERVER,
            "--format",
            self.VALUE_FORMAT,
        ]
        result = self.runner.invoke(cli, args)
        self.assertEqual(result.exit_code, 0)
        mock_get_frosty_build_path.assert_called_once()
        mock_collect_files_to_submit.assert_called_once()
        mock_prepare_aws_for_spin.assert_called_once()
        mock_prepare_aws_for_spin.assert_called_once()
        mock_upload_to_spin.assert_called_once()
        mock_get_platform_configuration.assert_called_once()

    @mock.patch(
        "dice_elipy_scripts.submit_to_spin.get_platform_configuration",
        return_value=MOCKED_PLATFORM_CONFIG[VALUE_LINUXSERVER],
    )
    def test_collect_files_to_submit_success(self, mock_platform_config):
        files = ["123_Binaries.zip", "build.json"]
        with tempfile.TemporaryDirectory() as test_folder:
            for file in files:
                open(os.path.join(test_folder, file), "w").close()

            collected_files = collect_files_to_submit(
                test_folder, mock_platform_config.return_value
            )
            self.assertEqual(set(files), set(collected_files))

    @mock.patch(
        "dice_elipy_scripts.submit_to_spin.get_platform_configuration",
        return_value=MOCKED_PLATFORM_CONFIG[VALUE_LINUX64],
    )
    @mock.patch("elipy2.core.create_zip")
    def test_collect_files_to_submit_success_zipped(self, mock_create_zip, mock_platform_config):
        files = ["Main_Linux64_final", "build.json"]
        with tempfile.TemporaryDirectory() as test_folder:
            for file in files:
                open(os.path.join(test_folder, file), "w").close()

            pattern = mock_platform_config.return_value.get("file_patterns", [])

            expected_files = set(pattern) - {"*"}
            expected_files.add("archive.zip")

            collected_files = collect_files_to_submit(
                test_folder, mock_platform_config.return_value
            )

            mock_create_zip.assert_called_once()
            self.assertEqual(set(expected_files), set(collected_files))

    def test_collect_files_to_submit_failure_raises_exception(self):
        with tempfile.TemporaryDirectory() as test_folder:
            with self.assertRaises(Exception):
                collect_files_to_submit(
                    test_folder, self.MOCKED_PLATFORM_CONFIG[self.VALUE_LINUXSERVER]
                )

    @mock.patch("elipy2.aws.AWSManager.__init__", return_value=None)
    @mock.patch("elipy2.secrets.get_secrets", return_value=None)
    def test_prepare_aws_for_spin_credentials_none(self, mock_secrets, mock_aws):
        with self.assertRaises(ELIPYException):
            prepare_aws_for_spin(self.MOCKED_PLATFORM_CONFIG[self.VALUE_LINUXSERVER])
        mock_secrets.assert_called_once_with(self.TEST_SECRETS_WHERE)
        assert mock_aws.call_count == 0

    @mock.patch("elipy2.secrets.get_secrets", return_value={"first": {"empty": "empty"}})
    def test_prepare_aws_for_spin_credentials_missing(self, mock_secrets):
        with self.assertRaises(ELIPYException) as context:
            prepare_aws_for_spin(self.MOCKED_PLATFORM_CONFIG[self.VALUE_LINUXSERVER])
        mock_secrets.assert_called_once_with(self.TEST_SECRETS_WHERE)

    @mock.patch("elipy2.aws.AWSManager")
    @mock.patch("elipy2.secrets.get_secrets")
    def test_prepare_aws_for_spin(self, mock_secrets, mock_aws_manager):
        access_key_id = "a1b2c3"
        secret_access_key = "d4e5f6"
        aws_region = None
        mock_secrets.return_value = {
            "first": {
                "SPIN_USER_AWS_ACCESS_KEY_ID": access_key_id,
                "SPIN_USER_AWS_SECRET_ACCESS_KEY": secret_access_key,
            }
        }

        mock_aws_instance = mock_aws_manager.return_value
        mock_aws_instance.assume_role.return_value = None

        prepare_aws_for_spin(self.MOCKED_PLATFORM_CONFIG[self.VALUE_LINUXSERVER])

        mock_secrets.assert_called_once()
        mock_aws_manager.assert_called_once_with(
            access_key_id=access_key_id,
            secret_access_key=secret_access_key,
            aws_region=aws_region,
        )
        mock_aws_instance.assume_role.assert_called_once()

    @mock.patch("elipy2.filer.core.use_bilbo", return_value=True)
    @mock.patch("elipy2.aws.AWSManager")
    @mock.patch("elipy2.build_metadata_utils.setup_metadata_manager")
    def test_upload_to_spin(self, mock_setup_metadata_manager, mock_aws_manager, mock_use_bilbo):
        mock_aws_instance = mock_aws_manager.return_value
        mock_bilbo_instance = mock_setup_metadata_manager.return_value
        mock_bilbo_instance.register_spin_build = mock.MagicMock()
        with tempfile.TemporaryDirectory() as test_folder:
            mock_aws_instance.upload_to_s3 = mock.MagicMock()
            files_list = ["build.json", "123_Binaries.zip"]

            upload_to_spin(
                mock_aws_instance,
                files_list,
                test_folder,
                dst_prefix="/".join(
                    [
                        self.VALUE_PROJECT,
                        self.VALUE_DATA_BRANCH,
                        self.VALUE_DATA_CHANGELIST,
                        self.VALUE_CODE_BRANCH,
                        self.VALUE_CODE_CHANGELIST,
                        self.VALUE_LINUXSERVER,
                        self.VALUE_FORMAT,
                        self.VALUE_REGION,
                        self.VALUE_CONFIG,
                    ]
                ),
                platform_config=self.MOCKED_PLATFORM_CONFIG[self.VALUE_LINUXSERVER],
            )
            assert mock_aws_instance.upload_to_s3.call_count == len(files_list)
            mock_bilbo_instance.register_spin_build.assert_called_once_with(test_folder, mock.ANY)
            mock_use_bilbo.assert_called_once()
