{#
    Command:
        patch_databuild
            short_help: Build data aginst baseline state, and deploy delta and new state.

    Arguments:
        data_dir
        platform
        assets
            nargs: -1
            required: True

    Required variables:
        data_branch
            help: Data branch.
            required: True
        data_changelist
            help: Data changelist number.
            required: True
        code_branch
            help: Code branch.
            required: True
        code_changelist
            help: Code changelist number
            required: True
        patch_type
            type: click.Choice(['from_disk', 'incremental', 'disc_build'])
            default: incremental
            help: What kind of patch are we producing
            required: True

    Optional variables:
        pipeline_args
            multiple: True
            help: Pipeline arguments for data build.
        first_patch/__not_first_patch
            default: False
            help: Flag for building the first patch.
        baseline_set/__no_baseline_set
            default: True
            help: flag for baseline being set.
        dry_run
            is_flag: True
            help: Build patch without deploying
        enable_compression
            default: False
            help: Add compression flag when exporting bundles
        use_recompression_cache
            default: False
            help: Alternative Avalanche server to use for the recompression cache
        staging_stream
            is_flag: True
            help: Skip clean on staging streams.
        import_avalanche_state
            is_flag: True
            help: Skip clean on staging streams.
        disc_data_branch
            help: Which branch the disc data was built on
            required: False
        disc_data_changelist
            required: False
            help: Which changelist the disc data was deployed from.
        disc_code_branch
            help: Which branch the disc code was built on
            required: False
        disc_code_changelist
            required: False
            help: Which changelist the disc code was deployed from
        patch_data_branch
            required: False
            help: Which branch the latest live patch data was built on.
        patch_data_changelist
            required: False
            help: Which changelist the latest live patch data was deployed from.
        patch_code_branch
            required: False
            help: Which branch the latest live patch code was built on.
        patch_code_changelist
            required: False
            help: Which changelist the latest live patch code was deployed from.
        clean_master_version_check
            default: False
            help: Run clean on master version update.
        data_clean
            default: false
            help: Clean Avalanche if --data-clean true is passed.
        skip_importing_baseline_state
            is_flag: True
            help: Skip importing Avalanche state for the baseline.
        expression_debug_data
            default: False
            help: Export expression debug data after data cook.
        clear_cache
            is_flag: True
            help: Clear the cache after importing state, but before cooking.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    patch_databuild
    data_dir {{ data_dir }}
    platform {{ platform }}
    assets {{ assets }}
    --data-branch {{ data_branch }}
    --data-changelist {{ data_changelist }}
    --code-branch {{ code_branch }}
    --code-changelist {{ code_changelist }}
    --patch-type {{ patch_type }}
    {%- if pipeline_args %}
    --pipeline-args {{ pipeline_args }}
    {%- endif %}
    {%- if first_patch/__not_first_patch %}
    --first-patch/--not-first-patch {{ first_patch/__not_first_patch }}
    {%- endif %}
    {%- if baseline_set/__no_baseline_set %}
    --baseline-set/--no-baseline-set {{ baseline_set/__no_baseline_set }}
    {%- endif %}
    {%- if dry_run %}
    --dry-run {{ dry_run }}
    {%- endif %}
    {%- if enable_compression %}
    --enable-compression {{ enable_compression }}
    {%- endif %}
    {%- if use_recompression_cache %}
    --use-recompression-cache {{ use_recompression_cache }}
    {%- endif %}
    {%- if staging_stream %}
    --staging-stream {{ staging_stream }}
    {%- endif %}
    {%- if import_avalanche_state %}
    --import-avalanche-state {{ import_avalanche_state }}
    {%- endif %}
    {%- if disc_data_branch %}
    --disc-data-branch {{ disc_data_branch }}
    {%- endif %}
    {%- if disc_data_changelist %}
    --disc-data-changelist {{ disc_data_changelist }}
    {%- endif %}
    {%- if disc_code_branch %}
    --disc-code-branch {{ disc_code_branch }}
    {%- endif %}
    {%- if disc_code_changelist %}
    --disc-code-changelist {{ disc_code_changelist }}
    {%- endif %}
    {%- if patch_data_branch %}
    --patch-data-branch {{ patch_data_branch }}
    {%- endif %}
    {%- if patch_data_changelist %}
    --patch-data-changelist {{ patch_data_changelist }}
    {%- endif %}
    {%- if patch_code_branch %}
    --patch-code-branch {{ patch_code_branch }}
    {%- endif %}
    {%- if patch_code_changelist %}
    --patch-code-changelist {{ patch_code_changelist }}
    {%- endif %}
    {%- if clean_master_version_check %}
    --clean-master-version-check {{ clean_master_version_check }}
    {%- endif %}
    {%- if data_clean %}
    --data-clean {{ data_clean }}
    {%- endif %}
    {%- if skip_importing_baseline_state %}
    --skip-importing-baseline-state {{ skip_importing_baseline_state }}
    {%- endif %}
    {%- if expression_debug_data %}
    --expression-debug-data {{ expression_debug_data }}
    {%- endif %}
    {%- if clear_cache %}
    --clear-cache {{ clear_cache }}
    {%- endif %}
  displayName: elipy patch_databuild
