package schedulers

import spock.lang.Unroll
import support.DeclarativePipelineSpockTest

@Unroll
class DataPreflightSchedulerSpec extends DeclarativePipelineSpockTest {

    private final String unshelveChangelist = '3456'
    private final String dataChangelist = '1234'
    private final String codeChangelist = '2345'
    private final boolean validateDirectReferences = false
    private final boolean cleanIndex = false

    void setup() {
        binding.setVariable('env', [
            branch_name                : 'kin-dev',
            datapreflight_reference_job: 'kin-dev.data.lastknowngood',
            dataset                    : 'kindata',
            JOB_NAME                   : 'kin-dev.datapreflight.start',
            project_name               : 'kingston',
        ])
        binding.setVariable('params', [
            unshelve_changelist: unshelveChangelist,
            preflighter        : 'Christofer',
        ])

        helper.with {
            registerAllowedMethod('get_branchfile', [String, String]) { projectName, branchName ->
                [
                    data_preflight_matrix: [
                        [name: 'xb1', platform: 'xb1', assets: ['PreflightLevels'], extra_label: ''],
                        [name: 'ps5', platform: 'ps5', assets: ['PreflightLevels'], extra_label: ''],
                    ],
                    preflight_settings   : [],
                ]
            }
            registerAllowedMethod('getLastStableDataChangelist', [String]) { jobName -> dataChangelist }
            registerAllowedMethod('getLastStableCodeChangelist', [String]) { jobName -> codeChangelist }
        }
    }

    void 'test data_preflight_scheduler'() {
        when:
        runScript('data_preflight_scheduler.groovy')
        printCallStack()

        then:
        assertCalledTimes('build', 2)
        assertCalledWith('build', [
            job       : 'kin-dev.kindata.preflight.ps5',
            propagate : false,
            parameters: [
                [name: 'code_changelist', value: codeChangelist],
                [name: 'data_changelist', value: dataChangelist],
                [name: 'unshelve_changelist', value: unshelveChangelist],
                [name: 'validate_direct_references', value: validateDirectReferences],
                [name: 'clean_index', value: cleanIndex],
            ],
        ])

        assertCalledWith('build', [
            job       : 'kin-dev.kindata.preflight.xb1',
            propagate : false,
            parameters: [
                [name: 'code_changelist', value: codeChangelist],
                [name: 'data_changelist', value: dataChangelist],
                [name: 'unshelve_changelist', value: unshelveChangelist],
                [name: 'validate_direct_references', value: validateDirectReferences],
                [name: 'clean_index', value: cleanIndex],
            ],
        ])

        assertJobStatusSuccess()
        testNonRegression('data_preflight_scheduler')
    }

    void 'test post email sent on #expected_result job'() {
        given:
        helper.with {
            registerAllowedMethod('build', [Map]) { [result: job_result] }
        }

        when:
        runScript('data_preflight_scheduler.groovy')
        printCallStack()

        then:
        assertCalledTimes('emailext', 1)
        binding.getVariable('currentBuild').result == expected_result

        where:
        job_result || expected_result
        'SUCCESS'  || 'SUCCESS'
    }
}
