"""
test_process_shift_subscription_downloads.py

Unit testing for process_shift_subscription_downloads
"""
import unittest
import pytest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from mock import <PERSON><PERSON><PERSON>, mock_open, patch
from dice_elipy_scripts.process_shift_subscription_downloads import (
    cli,
    find_file_with_prefix,
    register_build_in_bilbo,
)
from elipy2.exceptions import EL<PERSON>YException


@patch("os.path.join", MagicMock(side_effect=lambda *args: "\\".join(args)))
@patch("dice_elipy_scripts.process_shift_subscription_downloads.add_sentry_tags", MagicMock())
@patch("dice_elipy_scripts.process_shift_subscription_downloads.throw_if_files_found", MagicMock())
@patch("elipy2.running_processes.kill", MagicMock())
@patch("elipy2.telemetry.collect_metrics", MagicMock())
class TestProcessShiftSubscriptionDownloadsCli(unittest.TestCase):
    OPTION_BUILD_TYPE = "--build-type"
    OPTION_CODE_BRANCH = "--code-branch"

    VALUE_BUILD_TYPE = "frosty"
    VALUE_CODE_BRANCH = "code_branch"

    BASIC_ARGS = [
        OPTION_CODE_BRANCH,
        VALUE_CODE_BRANCH,
    ]

    def setUp(self):
        self.patcher_delete_folder = patch("elipy2.core.delete_folder")
        self.mock_delete_folder = self.patcher_delete_folder.start()

        self.patcher_extract_zip = patch("elipy2.core.extract_zip")
        self.mock_extract_zip = self.patcher_extract_zip.start()

        self.patcher_robocopy = patch("elipy2.core.robocopy")
        self.mock_robocopy = self.patcher_robocopy.start()

        self.patcher_get_shift_subscription_download_path = patch(
            "elipy2.filer_paths.get_shift_subscription_download_path"
        )
        self.mock_get_shift_subscription_download_path = (
            self.patcher_get_shift_subscription_download_path.start()
        )
        self.mock_get_shift_subscription_download_path.return_value = "\\shift\\delivery"

        self.patcher_get_code_build_root_path = patch("elipy2.filer_paths.get_code_build_root_path")
        self.mock_get_code_build_root_path = self.patcher_get_code_build_root_path.start()
        self.mock_get_code_build_root_path.side_effect = [
            "\\code\\build\\root\\1212",
            "\\code\\build\\root\\2323",
        ]

        self.patcher_listdir = patch("os.listdir")
        self.mock_listdir = self.patcher_listdir.start()
        self.mock_listdir.return_value = [
            "1122",
            "2233",
            "1122 info 1212.txt",
            "2233 info 2323.txt",
        ]

        self.patcher_isdir = patch("os.path.isdir")
        self.mock_isdir = self.patcher_isdir.start()
        self.mock_isdir.side_effect = [True, True, False, False]

        self.patcher_isfile = patch("os.path.isfile")
        self.mock_isfile = self.patcher_isfile.start()
        self.mock_isfile.side_effect = [False, False, True, True]

        self.patcher_find_file_with_prefix = patch(
            "dice_elipy_scripts.process_shift_subscription_downloads.find_file_with_prefix"
        )
        self.mock_find_file_with_prefix = self.patcher_find_file_with_prefix.start()
        self.mock_find_file_with_prefix.side_effect = ["1122 info 1212.txt", "2233 info 2323.txt"]

        self.patcher_register_build_in_bilbo = patch(
            "dice_elipy_scripts.process_shift_subscription_downloads.register_build_in_bilbo"
        )
        self.mock_register_build_in_bilbo = self.patcher_register_build_in_bilbo.start()

        self.patcher_os_exists = patch(
            "dice_elipy_scripts.process_shift_subscription_downloads.os.path.exists"
        )
        self.mock_os_exists = self.patcher_os_exists.start()
        self.mock_os_exists.return_value = False

    def tearDown(self):
        patch.stopall()

    def test_basic_args(self):
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 0
        assert self.mock_delete_folder.call_count == 4

    # def test_copy_calls(self):
    #     is_ci = os.environ.get("CI", False)
    #     if is_ci:  # Gitlab checks 8 localization files in the beginning of a pipeline
    #         self.mock_os_exists.side_effect = [
    #             True,
    #             True,
    #             True,
    #             True,
    #             True,
    #             True,
    #             True,
    #             True,
    #             False,
    #             False,
    #             False,
    #             False,
    #             False,
    #         ]
    #     else:
    #         self.mock_os_exists.side_effect = [False, False, False, False, False]
    #     runner = CliRunner()
    #     result = runner.invoke(cli, self.BASIC_ARGS)
    #     assert "Usage:" not in result.output
    #     assert result.exit_code == 0
    #     self.mock_robocopy.assert_has_calls(
    #         [
    #             call("\\shift\\delivery\\1122", "\\code\\build\\root\\1212"),
    #             call("\\shift\\delivery\\2233", "\\code\\build\\root\\2323"),
    #         ]
    #     )
    #     assert self.mock_extract_zip.call_count == 0

    # def test_zip_calls(self):
    #     is_ci = os.environ.get("CI", False)
    #     if is_ci:  # Gitlab checks 8 localization files in the beginning of a pipeline
    #         self.mock_os_exists.side_effect = [
    #             True,
    #             True,
    #             True,
    #             True,
    #             True,
    #             True,
    #             True,
    #             True,
    #             False,
    #             False,
    #             False,
    #             True,
    #             True,
    #         ]
    #     else:
    #         self.mock_os_exists.side_effect = [False, False, False, True, True]
    #     runner = CliRunner()
    #     result = runner.invoke(cli, self.BASIC_ARGS)
    #     assert "Usage:" not in result.output
    #     assert result.exit_code == 0
    #     self.mock_extract_zip.assert_has_calls(
    #         [
    #             call("\\shift\\delivery\\1122\\1212.zip", "\\code\\build\\root\\1212"),
    #             call("\\shift\\delivery\\2233\\2323.zip", "\\code\\build\\root\\2323"),
    #         ]
    #     )
    #     assert self.mock_robocopy.call_count == 0

    def test_failure_non_implemented_build_type(self):
        runner = CliRunner()
        result = runner.invoke(
            cli, self.BASIC_ARGS + [self.OPTION_BUILD_TYPE, self.VALUE_BUILD_TYPE]
        )
        assert "Usage:" not in result.output
        assert result.exit_code == 1

    def test_failure_capturing(self):
        self.mock_find_file_with_prefix.side_effect = ELIPYException()
        runner = CliRunner()
        result = runner.invoke(cli, self.BASIC_ARGS)
        assert "Usage:" not in result.output
        assert result.exit_code == 1


class TestFindFileWithPrefix(unittest.TestCase):
    def test_find_file_with_prefix_first(self):
        file_list = ["1122 info 1212.txt", "2233 info 2323.txt"]
        prefix = "1122"
        assert find_file_with_prefix(file_list, prefix) == "1122 info 1212.txt"

    def test_find_file_with_prefix_second(self):
        file_list = ["1122 info 1212.txt", "2233 info 2323.txt"]
        prefix = "2233"
        assert find_file_with_prefix(file_list, prefix) == "2233 info 2323.txt"

    def test_find_file_with_prefix_not_found(self):
        file_list = ["1122 info 1212.txt", "2233 info 2323.txt"]
        prefix = "4455"
        with pytest.raises(ELIPYException):
            find_file_with_prefix(file_list, prefix)


class TestRegisterBuildInBilbo:
    @patch("os.path.exists", MagicMock(return_value=True))
    @patch("json.loads")
    @patch("dice_elipy_scripts.process_shift_subscription_downloads.open", new_callable=mock_open())
    @patch("elipy2.bilbo._BilboElasticSearch.post_call")
    def test_register_build_in_bilbo(
        self, mock_post_call, mock_open_file, mock_json_loads, fixture_metadata_manager
    ):
        mock_json_loads.return_value = {
            "changelist": "19393703",
            "branch": "dev-na-dice-next-build",
            "verified_data": [
                {
                    "branch": "dev-na-dice-next-build-data",
                    "changelist": "19380483",
                    "dataset": "DiceNextData",
                    "timestamp": "2023-12-13T18:58:00.751198",
                },
                {
                    "branch": "dev-na-dice-next-build-data",
                    "changelist": "19380483",
                    "dataset": "DiceNextData",
                    "timestamp": "2023-12-13T18:58:14.027006",
                },
            ],
            "type": "drone",
            "created": "2023-12-13T18:58:00.751198",
            "location": "RippleEffect",
            "source": "\\\\dice-la.la.ad.ea.com\\builds\\frostbite\\code\\dev-na-dice-next-build\\19393703",
            "uuid": "edcb56c2-7673-59ac-ae5e-d9c4cd254c40",
            "updated": "2023-12-13T18:58:14.027006",
        }
        with patch("dice_elipy_scripts.process_shift_subscription_downloads.e2e_time") as mock_date:
            mock_date.return_value = "1h:0m:1s"
            attribute = {
                "changelist": "1234",
                "branch": "dev-na-dice-next-build",
                "verified_data": [
                    {
                        "branch": "dev-na-dice-next-build-data",
                        "changelist": "19380483",
                        "dataset": "DiceNextData",
                        "timestamp": "2023-12-13T18:58:00.751198",
                    },
                    {
                        "branch": "dev-na-dice-next-build-data",
                        "changelist": "19380483",
                        "dataset": "DiceNextData",
                        "timestamp": "2023-12-13T18:58:14.027006",
                    },
                ],
                "type": "drone",
                "created": "2023-12-13T18:58:00.751198",
                "location": "RippleEffect",
                "source": "\\target\\path",
                "uuid": "edcb56c2-7673-59ac-ae5e-d9c4cd254c40",
                "updated": "2023-12-13T18:58:14.027006",
                "original_source": "\\\\dice-la.la.ad.ea.com\\builds\\frostbite\\code\\dev-na-dice-next-build\\19393703",
                "shift_e2e_time": "1h:0m:1s",
            }
            code_changelist = "1234"
            register_build_in_bilbo("\\target\\path", code_changelist, fixture_metadata_manager)
            fixture_metadata_manager.register_build_data.assert_called_once_with(
                path="\\target\\path",
                attributes=attribute,
                write_attributes_file=True,
                shift_copy=True,
            )

    @patch("os.path.exists", MagicMock(return_value=False))
    @patch("json.loads")
    @patch("dice_elipy_scripts.process_shift_subscription_downloads.open", new_callable=mock_open())
    def test_register_build_in_bilbo_raises_exception_when_no_build_json_file_found(
        self, mock_open_file, mock_json_loads, fixture_metadata_manager
    ):
        mock_json_loads.return_value = {"source": "\\source\\path"}
        code_changelist = "1234"
        with pytest.raises(ELIPYException):
            register_build_in_bilbo("\\target\\path", code_changelist, fixture_metadata_manager)
