package com.ea.project.kin.mastersettings

class DiceKinPreflight {
    static Class project = com.ea.project.kin.Kingston
    static Map branches = [:]
    static Map preflight_branches = [
        'future-dev-content' : [code_folder: 'dev', code_branch: 'kin-dev', data_folder: 'future', data_branch: 'future-dev-content'],
        'future-dev-runmode': [code_folder: 'dev', code_branch: 'kin-dev', data_folder: 'dev', data_branch: 'future-dev-runmode'],
        'future-dev-runmode-02': [code_folder: 'dev', code_branch: 'kin-dev', data_folder: 'dev', data_branch: 'future-dev-runmode-02'],
        'kin-dev'            : [code_folder: 'dev', code_branch: 'kin-dev', data_folder: 'dev', data_branch: 'kin-dev'],
        'kin-dev-unverified' : [code_folder: 'dev', code_branch: 'kin-dev-unverified', data_folder: 'dev', data_branch: 'kin-dev-unverified-aws-code-preflight'],
        'kin-stage'          : [code_folder: 'stage', code_branch: 'kin-stage', data_folder: 'stage', data_branch: 'kin-stage'],
        'kin-release'        : [code_folder: 'release', code_branch: 'kin-release', data_folder: 'release', data_branch: 'kin-release'],
    ]
    static Map autotest_branches = [:]
    static Map integrate_branches = [:]
    static Map copy_branches = [:]
    static Map feature_integrations = [:]
    static Map maintenance_branch = [
        'kin-dev': [
            code_folder              : 'dev',
            code_branch              : 'kin-dev',
            data_folder              : 'dev',
            data_branch              : 'kin-dev',
            scheduled_nuke           : 'H 1 * * 7',  // Sunday morning 1 A.M
            nuke_label               : 'kindata',
            avalanche_maint          : true,
            p4_delete_workspace_label: 'kindata',
        ],
    ]
    static List dashboard_list = []
    static Map dvcs_configs = [:]
    static Map MAINTENANCE_SETTINGS = [:]
}
