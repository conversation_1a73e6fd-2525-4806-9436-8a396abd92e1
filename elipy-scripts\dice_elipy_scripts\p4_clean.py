"""
p4_clean.py
"""
import os
import click
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.delete_utils import delete_empty_folders
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import core, LOGGER, p4, code, local_paths, frostbite_core
from elipy2.exceptions import ELIPYException
from elipy2.cli import pass_context
from elipy2.telemetry import collect_metrics


@click.command("p4_clean", short_help="Performs a p4 clean on the given workspace.")
@click.argument("workspace_type", required=True)
@click.argument("port", required=True)
@click.argument("client", required=True)
@click.argument("user", required=True)
@click.argument("datadir", required=False, default=None)
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(_, workspace_type, port, client, user, datadir):  # pylint: disable=unused-argument
    """
    Performs a p4 clean on the given workspace.
    """
    # adding sentry tags
    add_sentry_tags(__file__)

    folder = None
    if workspace_type.lower() == "code":
        code.CodeUtils.clean_local(close_handles=True)
        core.delete_folder(local_paths.get_tnt_localpackages_path())
        folder = frostbite_core.get_tnt_root()
    elif workspace_type.lower() == "data":
        if datadir is not None:
            # Construting path manually since we're currently working with machines that we can't
            # construct the GAME_DATA_DIR path on.
            folder = os.path.join(frostbite_core.get_game_root(), datadir)
            core.delete_folder(local_paths.get_dataset_state_path(datadir))
        else:
            raise ELIPYException("Unable to clean data without a datadir.")
    else:
        raise ELIPYException("Unexpected workspace type {0}".format(workspace_type))

    if folder is None:
        raise ELIPYException("p4 clean called without a folder, this can be catastrophic.")

    p4_folder = os.path.join(folder, "...")
    perforce = p4.P4Utils(port=port, client=client, user=user)
    perforce.clean(folder=p4_folder)

    exceptionlist = delete_empty_folders(path=folder, stop_crawling_when_files_found=False)

    if exceptionlist:
        for i in exceptionlist:
            LOGGER.error(i)
        raise ELIPYException("Failed to delete at least one folder.")
