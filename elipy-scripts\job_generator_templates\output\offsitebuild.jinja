{#
    Command:
        offsitebuild
            short_help: Stores a build in the Offsite folder.

    Arguments:

    Required variables:
        code_changelist
            required: True
            help: Code changelist to store.
        code_branch
            required: True
            help: Which code branch to store.

    Optional variables:
        destination
            default: None
            help: Where to move the build.
        basic_drone_build
            is_flag: True
            help: Should we exclude console builds
        basic_drone_zip
            is_flag: True
            help: Basic (excluding console) drone zip builds
        qa_verified_builds_only
            is_flag: True
            help: Excluding non-verified QA build
        force_update_zip
            is_flag: True
            help: Overwrite the existing zip file if it exists
        outsourcer
            multiple: True
            help: Outsourcer with their own network share folder.
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    offsitebuild
    --code-changelist {{ code_changelist }}
    --code-branch {{ code_branch }}
    {%- if destination %}
    --destination {{ destination }}
    {%- endif %}
    {%- if basic_drone_build %}
    --basic-drone-build {{ basic_drone_build }}
    {%- endif %}
    {%- if basic_drone_zip %}
    --basic-drone-zip {{ basic_drone_zip }}
    {%- endif %}
    {%- if qa_verified_builds_only %}
    --qa-verified-builds-only {{ qa_verified_builds_only }}
    {%- endif %}
    {%- if force_update_zip %}
    --force-update-zip {{ force_update_zip }}
    {%- endif %}
    {%- if outsourcer %}
    --outsourcer {{ outsourcer }}
    {%- endif %}
  displayName: elipy offsitebuild
