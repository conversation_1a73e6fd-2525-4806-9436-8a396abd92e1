/**
 * P4PreviewCode.groovy
 * Preview code path(s) in Perforce, to see if there's anything new to build and to get the latest changelist within these paths.
 */

import com.ea.lib.LibCommonNonCps

void call(Class project, String previewType, String codeFolder, String codeBranch, String nonVirtualCodeFolder = '', String nonVirtualCodeBranch = '', List ignorePaths = [], List filterPaths = [], Map settingsMap = [:]) {
    echo '[P4PreviewCode] Start'
    def filter = filterPaths
    def ignore = ignorePaths
    // Set values depending on preview type
    String workspaceExtra = previewType
    String p4_code_root = LibCommonNonCps.get_setting_value(settingsMap, [], 'p4_code_root', '', project)

    switch (previewType) {
        case 'check': // Check only the code (TnT) part of the Perforce stream.
            if (filter.isEmpty()) {
                filter = [
                    'TnT/...',
                ]
            }
            break
        case 'cherrypick': // Check if there's been a change on what to cherrypick.
            if (filter.isEmpty()) {
                filter = [
                    'AutoIntegrate.json',
                ]
            }
            break
        case 'stream': // Check the whole stream.
            if (ignore.isEmpty()) {
                ignore = [
                    'TnT/Prebuild',
                    'AutoIntegrate.json',
                ]
            }
            workspaceExtra = ''
            break
        case 'upgrade': // Check if there's been any update for the FrostbiteDatabaseUpgrader files.
            if (filter.isEmpty()) {
                filter = [
                    'TnT/Code/Utils/FrostbiteDatabaseUpgrader/...',
                    'TnT/Code/DICE/UpgradeScripts/UpgradeScripts.txt',
                ]
            }
            break
        default: throw new IllegalArgumentException("Unknown preview type: ${previewType}, aborting.")
    }

    // Set filter folder
    String filterFolder = codeFolder
    if (nonVirtualCodeFolder != '') {
        filterFolder = nonVirtualCodeFolder
    }
    // Set filter branch
    String filterBranch = codeBranch
    if (nonVirtualCodeBranch != '') {
        filterBranch = nonVirtualCodeBranch
    }

    // Create the workspace name
    String hostname = 'master'
    if (env.PRODUCTION == 'False') {
        hostname += '-test'
    }
    String workspaceName = "jenkins-${project.short_name}-${hostname}-${env.JOB_NAME}-previewcode${workspaceExtra}"

    if (previewType == 'stream') {
        // Create the filter, including paths to ignore
        String filterPathStream = ''
        if (project.single_perforce_server.toBoolean() == true && project.frostbite_syncer_setup.toBoolean() == false) {
            filterPathStream = '/TnT'
        }
        String filterName = "${p4_code_root}/${filterFolder}/${filterBranch}${filterPathStream}"
        for (ignorePath in ignore) {
            filterName += "\n-${p4_code_root}/${filterFolder}/${filterBranch}/${ignorePath}"
        }
        String streamName = "${p4_code_root}/${codeFolder}/${codeBranch}"
        p4CheckoutStream(project, filterName, streamName, workspaceName, settingsMap)

        // For a special type of stream where we need to check also in the stream used as base stream.
        String extraCodePath = settingsMap.extra_code_path
        if (extraCodePath) {
            // Set variables to use.
            String codeChangelist = env.P4_CHANGELIST // Keep this value for later.
            String workspaceNameExtra = workspaceName + 'extra'
            // Create filter to use.
            String filterNameExtra = "${project.p4_code_root}/${extraCodePath}${filterPathStream}"
            for (ignorePath in ignorePaths) {
                filterNameExtra += "\n-${project.p4_code_root}/${extraCodePath}/${ignorePath}"
            }
            p4CheckoutStream(project, filterNameExtra, streamName, workspaceNameExtra, settingsMap)
            String codeChangelistExtra = env.P4_CHANGELIST
            if (Integer.parseInt(codeChangelistExtra) > Integer.parseInt(codeChangelist)) { // Use the latest of the two changelists.
                codeChangelist = codeChangelistExtra
            }
            EnvInject(currentBuild, ['P4_CHANGELIST': codeChangelist])
        }
    } else {
        // Create the view
        String viewName = ''
        for (filterPath in filter) {
            if (viewName != '') {
                viewName += ' \n'
            }
            viewName += "${p4_code_root}/${filterFolder}/${filterBranch}/${filterPath} //${workspaceName}/${filterPath}"
        }

        p4CheckoutManual(project, viewName, workspaceName, settingsMap)
    }
    echo '[P4PreviewCode] End'
}

void p4CheckoutStream(Class project, String filterName, String streamName, String workspaceName, Map branchInfo) {
    def p4CodeCreds = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_code_creds', '', project)
    checkout perforce(
        credential: p4CodeCreds,
        workspace: streamSpec(
            charset: 'none',
            pinHost: true,
            streamName: streamName,
            format: workspaceName,
        ),
        filter: [viewFilter(filterName)],
        populate: previewOnly(
            quiet: true
        )
    )
}

void p4CheckoutManual(Class project, String viewName, String workspaceName, Map branchInfo) {
    def p4CodeCreds = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_code_creds', '', project)
    checkout perforce(
        credential: p4CodeCreds,
        workspace: manualSpec(
            charset: 'none',
            pinHost: true,
            name: workspaceName,
            spec: clientSpec(
                allwrite: false,
                clobber: true,
                compress: false,
                locked: false,
                modtime: false,
                rmdir: false,
                streamName: '',
                line: 'LOCAL',
                view: viewName,
                changeView: '',
                type: 'WRITABLE',
                serverID: '',
                backup: true
            ),
            cleanup: false,
            syncID: ''
        ),
        populate: previewOnly(
            quiet: true
        )
    )
}
