package scripts.schedulers

import groovy.json.JsonSlurper
import org.apache.http.HttpEntity
import org.apache.http.HttpException
import org.apache.http.client.methods.CloseableHttpResponse
import org.apache.http.client.methods.HttpUriRequest
import org.apache.http.client.methods.RequestBuilder
import org.apache.http.impl.client.CloseableHttpClient
import org.apache.http.impl.client.HttpClients
import org.apache.http.util.EntityUtils

/**
 * trigger-gitlab-pipeline.groovy
 */
pipeline {
    options {
        allowBrokenBuildClaiming()
    }
    agent any
    stages {
        stage('Run') {
            steps {
                script {
                    String codeChangelist = params.code_changelist
                    String dataChangelist = params.data_changelist
                    currentBuild.displayName = "${currentBuild.projectName}.${dataChangelist}.${codeChangelist}"
                    CloseableHttpClient httpclient = HttpClients.createDefault()

                    String platform = env.platform
                    Map settings_map = [
                        'server': [repository_number: '4976', token: '10d2eafc5598e2fa014ff0a5be0b46'],
                        'client': [repository_number: '9011', token: 'bc22decfcfe806e71aac1cced9195a'],
                    ]

                    try {
                        HttpUriRequest httpPost = RequestBuilder.post()
                            .setUri(new URI("https://gitlab.ea.com/api/v4/projects/${settings_map[platform].repository_number}/trigger/pipeline"))
                            .addParameter('token', settings_map[platform].token)
                            .addParameter('ref', 'master')
                            .addParameter('variables[CODE_BRANCH]', params.code_branch)
                            .addParameter('variables[CODE_CL]', codeChangelist)
                            .addParameter('variables[DATA_BRANCH]', params.data_branch)
                            .addParameter('variables[DATA_CL]', dataChangelist)
                            .build()
                        CloseableHttpResponse response = httpclient.execute(httpPost)

                        try {
                            HttpEntity entity = response.entity
                            String content = EntityUtils.toString(entity)
                            if (response.statusLine.statusCode != 201) {
                                throw new HttpException(response.statusLine.toString() + ' ' + content)
                            }
                            JsonSlurper parser = new JsonSlurper()
                            AbstractMap json = parser.parseText(content)
                            echo('Pipeline URL: ' + json.get('web_url'))
                        }
                        finally {
                            response.close()
                        }
                    }
                    finally {
                        httpclient.close()
                    }
                }
            }
        }
    }
}
