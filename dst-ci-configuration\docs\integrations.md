# Integrations

## What are integrations?

Similar to a 'git merge' or a pull request/merge request, an integration is a way to merge or pass content between two
or
more Perforce streams.

## What are integrations in dst-ci-configuration?

There are two primary types of integrations in dst-ci-configuration:

> **Copy branches**: This is a simple copy the contents of one perforce stream (defined in dst-ci as a branch) to
> another. Primarily by building an elipy-script command which calls p4_copy.py

> **Standard integrations**: This is a more complex, customisable integration, which can include code and/or data.
> Depending on the elipy command build, primarily the following elipy-scripts are used:

## elipy-scripts used for integrations

`p4_copy_cherrypick.py`
Runs a Perforce copy and cherrypick operation using a provided JSON file of changelists. It sets up the Perforce
environment, reverts any pending changes, and then calls an external script (`integrate_stream.py`) to perform the
cherrypick copy, reverting changes again at the end.

`data_upgrade_integration.py`
Performs a "data upgrade integration" by syncing source data to a specific changelist, compiling code, and running a
Frostbite data upgrade script to upgrade data from one changelist to another. It then submits the result to Perforce,
handling both code and data directories and supporting various authentication and configuration options.

`data_upgrader_validator.py`
**(Deprecated)** Runs a validation for the FrostbiteDatabaseUpgrader tool, building the upgrader, setting up source and
destination data directories, and executing the upgrader to validate data upgrades between two branches. It is marked as
deprecated and was never actively used.

`p4_copy.py`
Performs a Perforce branch copy operation using a specified mapping and changelist. It can optionally submit the result,
revert excluded paths, and supports copying in reverse, streaming, and other advanced Perforce options. Used for copying
code or data between branches/streams.

`smoke_integrate.py`
Performs a "smoke integration" between unverified and verified data streams. Integrates changes from an unverified
stream into a verified one, submits the result, and updates metadata services to reflect the new integration. Used to
automate and track the process of promoting data from unverified to verified status.

`integrate_upgrade_one_stream.py`
Integrates or copies code and upgrades game data—either locally or via integration—using configurable mappings and
scripts. Compiles code, cooks data assets, and optionally leverages Branch Guardian for rule-based integration and
cleanup. Handles Perforce operations, error recovery, and submits the result, supporting extensive customisation for
build and integration pipelines.

## How do I create an integration using dst-ci-configuration?

Since integrations involve by nature more than one branch/stream, their configuration is primarily done in the
mastersettings files like `com/ea/project/bctch1/mastersettings/BctCh1Dev.groovy`.

- This mastersettings file includes a `copy_branches` list of maps. Each entry holds the metadata needed to perform the
  branch copy (note how few configurations are needed to perform a branch copy):

  ![img](./images/integrations-copy-branches-example.png)

- The mastersettings file also includes a `static Map integrate_branches` list of maps. Each entry here includes
  metadata needed to configure a standard integration:

  ![img](./images/integrations-integrate-example.png)

- There is also a `feature_integrations` list of Maps but as of right now it is unused in the dst-ci-configuration
  configuration.

In a standard integration like the one linked at the end of this document, the configuration options are as follows:

- **asset**
    - Some integration scripts also cook data. This setting configures which asset will be cooked in any cook operation
      during the integration script.
      It assumes the root so `//bf/CH1/CH1-content-dev/bfdata/Source/DevLevels.dbx` becomes `DevLevels`.
- **branch_guardian**
    - See the branch guardian section below for more context regarding its use case.
    - Before pushing to production, ensure that the elipy command generated is using an elipy script which supports
      branch-guardian. At the time of writing, only `integrate_upgrade_one_stream.py` supports branch guardian.
- **disable_build**
    - Whether the Jenkins job should be disabled and not run. Usually false but could be useful to submit prework to
      master to avoid complex merges later.
- **elipy_call**
    - E.g. `project.elipy_call`.
- **elipy_install_call**
    - E.g. `project.elipy_install_call`.
- **extra_args**
    - Usually an empty string, it can be used to add new args quickly or to overwrite the default on a specific
      integration ` --licensee BattlefieldGame`.
- **freestyle_job_trigger_matrix**
    - A freestyle trigger matrix as part of the integration, so one freestyle job can trigger other freestyle jobs.
- **frostbite_licensee**
    - E.g. `project.frostbite_licensee`.
- **integrate_mapping**
    - This string is passed directly to the p4 cli as a an argument and is the actual name of the branch mapping or
      stream spec persisted in Perforce. All future executions of the integration job will use this mapping.
    - The `[]` enclosed parts delimit the source and target of a p4 integration operation like
      `[<source>]_to_[<target>]`
      but it's ultimately a reference string so additional information can be added to it to better describe the
      integration e.g. `BF_[trunk-code-dev]_to_[trunk-content-dev]_IgnoreData`.
- **copy_mapping**
    - This mapping string is passed the p4 cli in the same way and has the function i.e. to persist details of the
      operation to perforce for future executions.
- **integrate_upgrade_one_stream**
    - When true, after having integrated the code and data (but before submitting to perforce), we run the data upgrade
      script to ensure that the structure of the data files is at the latest state before submitting to perforce.
- **job_label**
    - The job will only run on VMs with this label e.g. `CH1-content-dev-to-CH1-to-trunk-branch-guardian`.
- **manual_trigger**
    - When true, the integration will only be triggered manually from within Jenkins, not automatically triggered by P4
      submissions.
- **no_submit**
    - When true, the elipy script which performs the integration will not submit the changes to perforce and will
      instead shelve the changes for manual review.
- **preview_project**
    - When performing integrations, you can preview a predefined perforce location for changes without syncing those
      changes.
    - This is the imported project name of the preview location e.g if the project file is at
      `com/ea/project/bctch1/BctCh1.groovy` then the preview_project would be `BctCh1`.
- **preview_folder**
    - When performing integrations, you can preview a predefined perforce location for changes without syncing those
      changes.
    - This is the perforce location e.g. `CH1`.

      ![img](./images/preview_folder.png)
- **preview_branch**
    - When performing integrations, you can preview a predefined perforce location for changes without syncing those
      changes.
    - This is the perforce stream name e.g. `CH1-content-dev`.

      ![img](./images/preview_stream.png)

- **slack_channel**
    - Which slack channel to notify when the integration is complete e.g. `#bct-build-notify`.
- **source_project**
    - What is the dst-ci project it should be imported. e.g if the project file is at
      `com/ea/project/bctch1/BctCh1.groovy` then the preview_project would be `BctCh1`,
- **source_folder**
    - Perforce folder containing the source which will be integrated into the target. Like preview_folder above, e.g.
      `CH1`.
- **source_branch**
    - Perforce stream containing the source which will be integrated into the target. Like preview_branch e.g.
      `CH1-content-dev`.
- **target_project**
    - Same as source_project but for the target.
- **target_folder**
    - Same as source_folder but for the target.
- **target_branch**
    - Same as source_branch but for the target.
- **timeout_hours**
    - How many hours a job can run before failing with a timeout error. Number of hours e.g. 8.
- **use_preview_dotnet_version**
    - Currently set to false throughout; when true, the elipy script will use an as yet unreleased version of dotnet.
      Usually a temporary change while preparing for a dotnet version upgrade.
- **verified_integration**
    - This means that we are using the changelist from the latest successful build of some job on the source stream,
      instead of integrating the latest changelist we find in Perforce.
- **workspace_root**
    - This is defined in the project file and so is always `project.workspace_root`.

> See [README.md](../README.md) for more details of integration settings which can be used.

## Order of operations

1. When the seed job is ran on a Jenkins controller, the seed file `seeds/all/integrations_seed.groovy` is ran as part
   of the scheduled controller seed job, it reads the mastersettings files and creates the jobs in Jenkins.

1. `com.ea.lib.jobs.LibIntegration` is the heavy lifter in terms of the logic for creating integrations during the
   `seeds/all/integrations_seed.groovy` execution. `com.ea.lib.jobs.LibIntegration` builds the elipy calls based on the
   configuration in the mastersettings files, project settings files, and (sometimes) branchsettings files.

## What is Branch Guardian?

Branch Guardian is a data check-in management tool which aims to prevent developers from submitting changes that could
lead to complex merges, highlight assets being worked on across streams, and assist in maintaining a single source of
truth for
assets. It enables Battlefield to bring content between chapters and have developers working with data across multiple
different streams without increasing complexity.

Every file or folder has exactly one owner which is a branch/stream, e.g. `//bf/mainline/trunk-content-dev`. YouOnly
assets which are owned by the current workspace stream can be submitted.

We only turn branch guardian on and off in dst-ci, and if it is turned on we sync it in
`com.ea.lib.jobs.LibIntegration`.

## Example integration config MR

- https://gitlab.ea.com/dre-cobra/dst-ci-configuration/-/merge_requests/10473/diffs
