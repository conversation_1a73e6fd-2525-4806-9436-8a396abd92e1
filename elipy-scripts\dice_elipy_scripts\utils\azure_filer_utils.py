"""
Helper methods to authenticate towards Azure filer
"""
from elipy2 import LOGGER, filer_paths, secrets


def authenticate_filer(
    _filer,
    secret_context,
    target_fileshare_path=None,
    location=None,
    target_build_share=None,
):
    """
    Authenticate the filer if user/password specified
    """
    _filer.delete_network_connection()

    LOGGER.info("Polling ess for azure fileshare credentials...")
    azure_fileshare_username, azure_fileshare_password = _get_azure_fileshare_credentials(
        secret_context, location=location
    )

    LOGGER.info("Authenticating to fileshare with user {}...".format(azure_fileshare_username))
    if not target_fileshare_path:
        target_fileshare_path = filer_paths.get_build_share_path(
            location=location, target_build_share=target_build_share
        )

    LOGGER.info("Authenticating to fileshare at network path {}...".format(target_fileshare_path))

    _filer.auth_network_connection(
        network_path=target_fileshare_path,
        username=azure_fileshare_username,
        password=azure_fileshare_password,
    )

    return _filer


def _get_azure_fileshare_credentials(secret_context, location=None):
    """
    Get the credentials from ESS for authenticating
    with Azure fileshare based on elipy config yml.
    @param secret_context: which secret in the elipy config to use
    @param location: which location to use in the elipy config (default, dice etc.)
    """
    LOGGER.info("Using elipy_config_location {}".format(location))
    credentials = secrets.get_secrets({secret_context: True}, location=location)
    try:
        k = next(iter(credentials))  # get first and only key
    except StopIteration as _:
        LOGGER.error(
            "No secret in elipy config matching secret_context {} in location {}".format(
                secret_context, location
            )
        )
        raise

    secrets_dict = credentials[k]
    azure_fileshare_user = secrets_dict["AZURE_FILESHARE_USERNAME"]
    azure_fileshare_password = secrets_dict["AZURE_FILESHARE_PASSWORD"]
    LOGGER.info("Retrieved credentials for Azure fileshare user {}".format(azure_fileshare_user))
    return azure_fileshare_user, azure_fileshare_password
