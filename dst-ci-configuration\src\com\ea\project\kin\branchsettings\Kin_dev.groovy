package com.ea.project.kin.branchsettings

import com.ea.lib.jobsettings.ShiftSettings

class Kin_dev {
    // Settings for jobs
    static Class project = com.ea.project.kin.Kingston
    static Map general_settings = [
        dataset           : project.dataset,
        frostbite_licensee: project.frostbite_licensee,
        elipy_install_call: project.elipy_install_call,
        elipy_call        : project.elipy_call,
        workspace_root    : project.workspace_root,
    ]
    static Map standard_jobs_settings = [
        asset                                   : 'ShippingLevels',
        baseline_set                            : false,
        bilbo_store_offsite                     : true,
        clean_data_validation_pipeline_args     : ' --disable-caches true',
        data_reference_job                      : 'kin-dev.kindata.upgrade.data',
        enable_clean_build_validation           : false,
        drone_outsourcers                       : ['Jukebox'],
        enable_daily_data_clean                 : true,
        enable_lkg_cleaning                     : true,
        enable_lkg_p4_counters                  : true,
        enable_maptool                          : true,
        extra_data_args                         : ['--pipeline-args -ContentDatabase.EnableHailstormLocalCache --pipeline-args true '],
        extra_datapreflight_args                : ' --pipeline-args -Pipeline.AbortOnPipelineError --pipeline-args true ',
        extra_navmesh_args                      : ' --p4-edit-list Source\\Game\\Levels\\MP\\MP_Orbital\\MP_Orbital_pathfinding.fb --p4-edit-list Source\\Game\\Levels\\MP\\MP_Orbital\\MP_Orbital_pathfinding.dbx',
        fake_ooa_wrapped_symbol                 : true,
        frosty_asset                            : 'Game/Setup/Build/ReleaseLooseFileLevels',
        frosty_digital_asset                    : 'ShippingLevels',
        frosty_reference_job                    : 'kin-dev.data.start',
        import_avalanche_autotest               : false,
        linux_docker_images                     : false,
        marvin_trigger_upload                   : false,
        navmesh_asset                           : 'Game\\Levels\\MP\\MP_Orbital\\MP_Orbital_pathfinding',
        navmesh                                 : false,
        offsite_basic_drone_zip_builds          : true,
        offsite_code_token                      : '<EMAIL>:1180acfb166f9612424e10e603d75acc0e',
        offsite_drone_basic_builds              : true,
        offsite_drone_builds                    : true,
        offsite_job_remote                      : 'http://dice-la-jenkins-tools.la.ad.ea.com:8080/job/GetNewDrone_kin-dev/buildWithParameters?token=2Zm67RaPGVd6^&code_changelist=%code_changelist%^&cause=%BUILD_URL%^&share_root=\\\\filer.dice.ad.ea.com\\Builds\\Kingston\\code\\kin-dev',
        offsitedrone_reference_job              : 'kin-dev.bilbo.register-kindata-dronebuild',
        reshift_offsitedrone                    : true,
        pipeline_warning_job                    : false,
        poolbuild_frosty                        : true,
        poolbuild_label_frosty_xb1_files        : 'poolbuild_frosty',
        poolbuild_label_frosty_xbsx_files       : 'poolbuild_frosty',
        poolbuild_label_frosty_ps4_files        : 'poolbuild_frosty',
        poolbuild_label_frosty_ps5_files        : 'poolbuild_frosty',
        poolbuild_label_frosty_win64_files      : 'poolbuild_frosty',
        poolbuild_label_frosty_server_files     : 'poolbuild_frosty',
        poolbuild_label_frosty_linuxserver_files: 'poolbuild_frosty',
        quickscope_db                           : 'kinpipeline',
        quickscope_import                       : true,
        remote_masters_to_receive_code          : [[name: 'kin-preflight-jenkins.cobra.dre.ea.com', allow_failure: false]],
        remote_masters_to_receive_data          : [[name: 'kin-preflight-jenkins.cobra.dre.ea.com', allow_failure: false]],
        remote_masters_trigger_pipeline         : true,
        retry_limit_patchdata                   : 1,
        server_asset                            : 'Game/Setup/Build/ReleaseLooseFileLevels',
        shift_branch                            : true,
        shift_reference_job                     : 'kin-dev.patchfrosty.start',
        single_stream_smoke                     : true,
        skip_frosty_scheduler                   : true,
        skip_frosty_trigger                     : false,
        skip_icepick_settings_file              : true,
        slack_channel_clean_data_validation     : [
            channels                  : ['#kin-build-notify'],
            skip_for_multiple_failures: true,
        ],
        slack_channel_code                      : [
            channels                  : ['#kin-build-notify'],
            skip_for_multiple_failures: true,
        ],
        slack_channel_data                      : [
            channels                  : ['#kin-build-notify'],
            skip_for_multiple_failures: true,
        ],
        slack_channel_frosty                    : [
            channels                  : ['#kin-build-notify'],
            skip_for_multiple_failures: true,
        ],
        slack_channel_patchfrosty               : [
            channels                  : ['#earo-frosty-notify'],
            skip_for_multiple_failures: true,
        ],
        // autotest_remote_settings               : [
        //     p4_code_server            : 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
        //     p4_code_creds             : 'dice-p4buildedge03-fb',
        // ],
        smoke_downstream_job                    : 'kin-dev.kindata.upgrade.data',
        statebuild_code_list                    : ['xb1', 'tool'],
        statebuild_data                         : false,
        timeout_hours_clean_data_validation     : 10,
        timeout_hours_data                      : 5,
        trigger_string_navmesh                  : 'H 0 * * 1-6\nH 6 * * 7',
        trigger_string_shift                    : 'TZ=Europe/Stockholm \n H 5,13 * * 1-6\nH 6,13 * * 7',
        trigger_string_shift_offsite_drone      : 'TZ=Europe/London \n H 16 * * 1-6\nH 16 * * 7',
        upgrade_data_job                        : true,
        use_deprecated_blox_packages            : true,
        use_linuxclient                         : true,
        use_snowcache                           : true,
        webexport_branch                        : true,
        webexport_import_avalanche              : false,
        webexport_label                         : 'data && webexport && kin-dev',
        move_location_parallel                  : true,
        new_locations                           : [
            earo: [
                elipy_call_new_location: project.elipy_call_earo + ' --use-fbenv-core',
            ],
        ],
    ]
    static Map preflight_settings = [
        concurrent_data                  : 7,
        use_icepick_test                 : true,
        pre_preflight                    : true,
        statebuild_datapreflight         : false,
        statebuild_codepreflight         : false,
        slack_channel_preflight          : [channels: ['#cobra-build-preflight']],
        datapreflight_reference_job      : 'kin-dev.data.lastknowngood',
        max_builds_tokeep                : 200,
        trigger_type                     : 'cron',
        prepreflight_idle_length         : '90', //minutes idling before to run pre-preflight
        trigger_string_pre_preflight_data: 'H */2 * * 1-5\nH 6-23 * * 6-7',
        timeout_hours_datapreflight      : 5,
        timeout_hours_dataprepreflight   : 3,
        timeout_hours_postpreflight      : 3,
        p4_code_server                   : 'dice-p4buildedge03-fb.dice.ad.ea.com:2001',
        p4_code_creds                    : 'dice-p4buildedge03-fb',
    ]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'win64game', configs: ['final', 'release', 'retail', 'performance']],
        [name: 'ps4', configs: ['final', 'release', 'retail', 'performance']],
        [name: 'xb1', configs: ['final', 'release', 'retail', 'performance']],
        [name: 'win64server', configs: ['final', 'release']],
        [name: 'linux64server', configs: ['final', 'release']],
        [name: 'tool', configs: [[name: 'release', compile_unit_tests: true, run_unit_tests: true],]],
        [name: 'ps5', configs: ['final', 'release', 'retail', 'performance']],
        [name: 'xbsx', configs: ['final', 'release', 'retail', 'performance']],
    ]
    static List bilbo_move_matrix = [
        [name: 'win64game', configs: ['final', 'release', 'performance']],
        [name: 'tool', configs: [[name: 'release']]],
        [name: 'win64server', configs: ['final', 'release']],
        [name: 'xbsx', configs: ['final', 'release', 'performance']],
        [name: 'ps5', configs: ['final', 'release', 'performance']],
        [name: 'linux64server', configs: ['final', 'release']],
        [name: 'ps4', configs: ['final', 'release', 'performance']],
        [name: 'xb1', configs: ['final', 'release', 'performance']],
    ]
    static List code_nomaster_matrix = []
    static List code_downstream_matrix = [
        [name: '.code.p4counterupdater', args: ['code_changelist', 'code_countername']],
        [name: '.code.lastknowngood', args: ['code_changelist']],
        [name: '.data.start', args: []],
    ]
    static List data_matrix = [
        [name: 'win64'],
        [name: 'server'],
        [name: 'xb1'],
        [name: 'ps4', nightly_clean_build: true],
        [name: 'xbsx'],
        [name: 'ps5'],
    ]
    static List data_downstream_matrix = [
        [name: '.data.lastknowngood', args: ['code_changelist', 'data_changelist']],
        [name: '.data.p4counterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.data.p4cleancounterupdater', args: ['code_changelist', 'data_changelist', 'code_countername', 'data_countername']],
        [name: '.patchfrosty.start', args: []],
        [name: 'future-dev-content.kindata.upgrade.data', args: ['code_changelist']],
        [name: 'future-dev-runmode-02.data.merge-down.future-dev-content', args: ['code_changelist', 'data_changelist']],
        [name: 'future-dev-runmode-02.kindata.upgrade.data', args: ['code_changelist']],
    ]
    static List patchdata_matrix = []
    static List frosty_matrix = []
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = [
        [name: 'win64', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release --additional-configs performance'],
                                   [format: 'digital', config: 'final', region: 'ww', args: ''],
                                   [format: 'digital', config: 'retail', region: 'ww', args: '']]],
        [name: 'ps4', variants: [[format: 'digital', config: 'final', region: 'eu', args: ''],
                                 [format: 'digital', config: 'retail', region: 'eu', args: ''],
                                 [format: 'files', config: 'final', region: 'eu', args: ' --additional-configs release --additional-configs performance'],
                                 [format: 'digital', config: 'final', region: 'na', args: ''],
                                 [format: 'digital', config: 'retail', region: 'na', args: ''],
                                 [format: 'files', config: 'final', region: 'na', args: ' --additional-configs release --additional-configs performance']]],
        [name: 'xb1', variants: [[format: 'digital', config: 'final', region: 'ww', args: ''],
                                 [format: 'digital', config: 'retail', region: 'ww', args: ''],
                                 [format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release --additional-configs performance']]],
        [name: 'ps5', variants: [[format: 'digital', config: 'final', region: 'dev', args: '', statebuild: false],
                                 [format: 'digital', config: 'retail', region: 'dev', args: '', statebuild: false],
                                 [format: 'files', config: 'final', region: 'dev', args: ' --additional-configs performance']]],
        [name: 'xbsx', variants: [[format: 'digital', config: 'final', region: 'ww', args: ''],
                                  [format: 'digital', config: 'retail', region: 'ww', args: ''],
                                  [format: 'files', config: 'final', region: 'ww', args: ' --additional-configs performance']]],
        [name: 'server', variants: [[format: 'digital', config: 'final', region: 'ww', args: ''],
                                    [format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release']]],
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww', args: ''],
                                         [format: 'files', config: 'final', region: 'ww', args: '']]],
    ]
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = [
        [name: '.spin.linuxserver.digital.final.ww', args: ['code_changelist', 'data_changelist']],
    ]
    static List code_preflight_matrix = []
    static List data_preflight_matrix = [
        [name: 'win64', platform: 'win64', assets: ['PreflightLevels'], extra_label: ''],
        [name: 'server', platform: 'server', assets: ['PreflightLevels'], extra_label: ''],
        [name: 'xb1', platform: 'xb1', assets: ['PreflightLevels'], extra_label: ''],
        [name: 'ps5', platform: 'ps5', assets: ['PreflightLevels'], extra_label: ''],
    ]
    static List spin_upload_matrix = [
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww']]],
    ]
    static List shift_upload_matrix = [
        [shifter_type: ShiftSettings.SHIFTER_TYPE_OFFSITE_BASIC_DRONE, args: ['code_changelist']],
    ]
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
