import com.ea.lib.jobsettings.CustomTestsSettings
import spock.lang.Specification

class CustomTestsSettingsSpec extends Specification {

    class BranchFile {
        static Map standard_jobs_settings = [
            branch_name         : 'branch',
            custom_tests        : [
                custom_configs: [
                    'config1.json',
                    'config2.json',
                ],
            ],
            disable_build       : true,
            elipy_call          : 'elipy-call',
            elipy_install_call  : 'elipy-install-call',
            job_label           : 'label',
            job_label_statebuild: 'name && util',
            p4_fb_settings      : [p4_creds: 'p4-creds', p4_port: '1111'],
            workspace_root      : 'workspace-root',
        ]
        static Map general_settings = [:]
    }

    class BranchFileNonDefault {
        static Map standard_jobs_settings = [
            branch_name         : 'branch',
            custom_tests        : [
                attempts         : 3,
                pass_through_args: [
                    'gtestArg1',
                    'gtestArg2',
                ],
                test_filter      : 'a_filter',
                custom_configs   : [
                    'config1.json',
                    'config2.json',
                ],
                timeout          : 600,
                workers          : 1,
            ],
            disable_build       : true,
            elipy_call          : 'elipy-call',
            elipy_install_call  : 'elipy-install-call',
            frostbite_licensee  : 'a_licensee',
            job_label           : 'label',
            job_label_statebuild: 'name && util',
            p4_fb_settings      : [p4_creds: 'p4-creds', p4_port: '1111'],
            workspace_root      : 'workspace-root',
        ]
        static Map general_settings = [:]
    }

    class MasterFile {
        static Map branches = [branch: [data_branch: 'data-branch', code_branch: 'code-branch']]
    }

    class ProjectFile {
        static String name = 'Kingston'
        static String dataset = 'project-dataset'
    }

    void "test that we get expected job settings in initializeCustomTestsStart"() {
        when:
        CustomTestsSettings settings = new CustomTestsSettings()
        settings.initializeCustomTestsStart(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            isDisabled == BranchFile.standard_jobs_settings.disable_build
            description == 'Scheduler to run tests for branch'
            customTestsReferenceJob == 'branch.code.start'
            triggerType == 'cron'
            cronTrigger == 'H H/2 * * 1-6\nH 6-23/2 * * 7'
        }
    }

    void "test that we get expected default job settings in initializeCustomTestsJob"() {
        when:
        CustomTestsSettings settings = new CustomTestsSettings()
        settings.initializeCustomTestsJob(BranchFile, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            description == 'Job for running certain types of tests.'
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${ENV, var="code_changelist"}'
            elipyCmd == 'elipy-call test_runner --data-directory project-dataset --custom-configs config1.json --custom-configs config2.json'
            elipyInstallCall == 'elipy-install-call'
        }
    }

    void "test that we get expected non-default job settings in initializeCustomTestsJob"() {
        when:
        CustomTestsSettings settings = new CustomTestsSettings()
        settings.initializeCustomTestsJob(BranchFileNonDefault, MasterFile, ProjectFile, 'branch')
        then:
        with(settings) {
            description == 'Job for running certain types of tests.'
            workspaceRoot == BranchFile.standard_jobs_settings.workspace_root
            buildName == '${JOB_NAME}.${ENV, var="code_changelist"}'
            elipyCmd == 'elipy-call test_runner --data-directory project-dataset --custom-configs config1.json --custom-configs config2.json --attempts 3 --test-filter a_filter --timeout 600 --workers 1 --licensee a_licensee --pass-trough gtestArg1 --pass-trough gtestArg2'
            elipyInstallCall == 'elipy-install-call'
        }
    }
}
