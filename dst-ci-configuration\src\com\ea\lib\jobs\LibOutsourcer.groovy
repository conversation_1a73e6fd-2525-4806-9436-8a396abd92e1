package com.ea.lib.jobs

import com.ea.lib.LibCommonNonCps
import com.ea.lib.LibJobDsl

class LibOutsourcer {
    /**
     * Adds generic job parameters for prebuild start jobs.
     */
    static void prebuild_start(def job, def project, def branch_info) {
        // Set values for variables.
        def prebuild_info = branch_info.prebuild_info

        def trigger_string = prebuild_info.trigger_string ?: 'H 22 * * *' // Default: run once a day, in the evening.
        def modifiers = ['prebuild']
        def disable_build = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'disable_build', false)

        // Add sections to the Jenkins job.
        job.with {
            description('Scheduler to start a prebuild job for ' + branch_info.branch_name + '.')
            disabled(disable_build)
            logRotator(7, 100)
            quietPeriod(0)
            properties {
                disableConcurrentBuilds()
                disableResume()
                pipelineTriggers {
                    triggers {
                        cron {
                            spec(trigger_string)
                        }
                    }
                }
            }
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                choiceParam('clean_local', ['false', 'true'], 'If true, TnT/Local will be deleted at the beginning of the run.')
            }
            environmentVariables {
                env('branch_name', branch_info.branch_name)
                env('code_branch', branch_info.code_branch)
                env('code_folder', branch_info.code_folder)
                env('project_name', project.name)
                env('outsource_validation', branch_info.prebuild_info.outsource_validation)
            }
        }
    }

    /**
     * Adds generic job parameters for prebuild jobs.
     */
    static void prebuild_job(def job, def project, def branch_info) {
        // Set values for variables.
        def modifiers = ['prebuild']
        def prebuild_info = branch_info.prebuild_info

        def job_label = prebuild_info.job_label ?: branch_info.job_label_statebuild ?: 'statebuild'
        def timeout_hours = branch_info.timeout_hours_prebuild ?: 24
        def timeout_minutes = timeout_hours * 60
        def fb_login_details = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'p4_fb_settings', [:], project)
        def user_credentials = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'user_credentials', '', project)
        String p4_code_server = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'p4_code_server', '', project)

        def extra_args_list = prebuild_info.extra_args ?: []
        def extra_args = extra_args_list.join(' ')
        for (platform in prebuild_info.platforms_sln) {
            extra_args += ' --platform-sln ' + platform
        }
        for (platform in prebuild_info.skip_platforms) {
            extra_args += ' --skip-platform ' + platform
        }
        if (branch_info.frostbite_licensee) {
            extra_args += ' --licensee ' + branch_info.frostbite_licensee
        }
        if (prebuild_info.dry_run) {
            extra_args += ' --dry-run'
        }
        if (user_credentials) {
            extra_args += ' --email %monkey_email% --password "%monkey_passwd%"'
        }

        for (platform in prebuild_info.platform_prebuild) {
            extra_args += " --platform-prebuild \"${platform}\""
        }

        def p4_client_prebuild = project.p4_code_client_env + '-prebuild'
        if (prebuild_info.workspace_type == 'streams') {
            p4_client_prebuild = project.p4_code_client_env
        }

        // Add sections to the Jenkins job.
        job.with {
            description('Builds a prebuild for ' + branch_info.branch_name + '.')
            label(job_label)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(branch_info.workspace_root)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                choiceParam('clean_local', ['false', 'true'], 'If true, TnT/Local will be deleted at the beginning of the run.')
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName('${JOB_NAME}.${ENV, var="code_changelist"}')
                timeout {
                    absolute(timeout_minutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
                credentialsBinding {
                    if (user_credentials) {
                        usernamePassword('monkey_email', 'monkey_passwd', user_credentials)
                    }
                    if (fb_login_details.p4_creds) {
                        usernamePassword('fb_p4_user', 'fb_p4_passwd', fb_login_details.p4_creds)
                    }
                }
            }
            steps {
                if (fb_login_details) {
                    batchFile("echo %fb_p4_passwd%|p4 -p ${fb_login_details.p4_port} -u %fb_p4_user% login & exit 0")
                }
                LibJobDsl.installElipy(delegate, branch_info.elipy_install_call, project)
                batchFile(branch_info.elipy_call + ' prebuild --clean %clean_local% ' + ' --code-changelist %code_changelist%' +
                    ' --config ' + prebuild_info.config + ' --data-directory ' + branch_info.dataset +
                    ' --framework-args -G:package.eaconfig.usedebugfastlink=false' +
                    ' --framework-args -G:frostbite.is-outsource-build=true' +
                    ' --input-param-path ' + prebuild_info.input_param_path +
                    ' --p4-client-code ' + project.p4_code_client_env + ' --p4-client-prebuild ' + p4_client_prebuild +
                    ' --p4-port ' + p4_code_server + ' --p4-user %P4_USER%' +
                    ' ' + extra_args)
            }
        }
    }

    /**
     * Adds generic job parameters for outsourcevalidation jobs.
     */
    static void outsource_validation(def job, def project, def branch_info) {
        // Set values for variables.
        def modifiers = []
        def user_credentials = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'user_credentials', '', project)
        def prebuild_info = branch_info.prebuild_info
        def job_label = prebuild_info.job_label ?: branch_info.job_label_statebuild ?: 'statebuild'
        def timeout_hours = branch_info.timeout_hours_prebuild ?: 24
        def timeout_minutes = timeout_hours * 60
        def fb_login_details = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'p4_fb_settings', [:], project)
        def extra_args_list = prebuild_info.extra_args_validation ?: []
        def extra_args = extra_args_list.join(' ')
        String p4_code_server = LibCommonNonCps.get_setting_value(branch_info, modifiers, 'p4_code_server', '', project)
        // set platform_validation to overwrite platforms_sln if we need to validate different platform(s)
        def platform_validation_list = prebuild_info.platform_validation ?: prebuild_info.platforms_sln
        for (platform_valid in platform_validation_list) {
            extra_args += ' --platform-sln ' + platform_valid
        }

        if (branch_info.frostbite_licensee) {
            extra_args += ' --licensee ' + branch_info.frostbite_licensee
        }
        if (user_credentials) {
            extra_args += ' --email %monkey_email% --password "%monkey_passwd%"'
        }
        def p4_client_prebuild = project.p4_code_client_env + '-prebuild'
        if (prebuild_info.workspace_type == 'streams') {
            p4_client_prebuild = project.p4_code_client_env
        }

        // Add sections to the Jenkins job.
        job.with {
            description('Validate outsource code from prebuild job on ' + branch_info.branch_name + '.')
            label(job_label)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(branch_info.workspace_root)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to validate from prebuild.')
                    trim(true)
                }
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName('${JOB_NAME}.${ENV, var="code_changelist"}')
                timeout {
                    absolute(timeout_minutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
                credentialsBinding {
                    if (user_credentials) {
                        usernamePassword('monkey_email', 'monkey_passwd', user_credentials)
                    }
                    if (fb_login_details.p4_creds) {
                        usernamePassword('fb_p4_user', 'fb_p4_passwd', fb_login_details.p4_creds)
                    }
                }
                steps {
                    LibJobDsl.installElipy(delegate, branch_info.elipy_install_call, project)
                    batchFile(branch_info.elipy_call + ' prebuild --validation --code-changelist %code_changelist%' +
                        ' --config ' + prebuild_info.config + ' --data-directory ' + branch_info.dataset +
                        ' --framework-args -G:frostbite.is-outsource-build=true' +
                        ' --p4-client-code ' + project.p4_code_client_env + ' --p4-client-prebuild ' + p4_client_prebuild +
                        ' --p4-port ' + p4_code_server + ' --p4-user %P4_USER%' +
                        ' ' + extra_args)
                }
            }
        }
    }

    /**
     * Adds generic job parameters for outsource package start jobs.
     */
    static void outsourcePackageStart(def job, def project, def branchInfo) {
        // Set values for variables.
        def outsourcePackageInfo = branchInfo.outsource_package_info

        def disableBuild = outsourcePackageInfo.disable_build ?: false
        def triggerString = outsourcePackageInfo.trigger_string ?: 'H 22 * * *' // Default: run once a day, in the evening.
        def triggerType = outsourcePackageInfo.trigger_type ?: 'cron'

        if (branchInfo.prebuild_triggers_package) {
            triggerType = 'none'
        }

        // Add sections to the Jenkins job.
        job.with {
            description('Scheduler to start an outsource package job for ' + branchInfo.branch_name + '.')
            disabled(disableBuild)
            logRotator(7, 100)
            quietPeriod(0)
            properties {
                disableConcurrentBuilds()
                disableResume()
                pipelineTriggers {
                    triggers {
                        if (triggerType == 'cron') {
                            cron {
                                spec(triggerString)
                            }
                        }
                    }
                }
            }
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                choiceParam('clean_local', ['false', 'true'], 'If true, TnT/Local will be deleted at the beginning of the run.')
            }
            environmentVariables {
                env('branchName', branchInfo.branch_name)
                env('projectName', project.name)
            }
        }
    }

    /**
     * Adds generic job parameters for outsource package build jobs.
     */
    static void outsourcePackageBuild(def job, def project, def branchInfo) {
        // Set values for variables.
        def outsourcePackageInfo = branchInfo.outsource_package_info
        def modifiers = ['outsourcepackage']

        def jobLabel = outsourcePackageInfo.job_label ?: branchInfo.job_label_statebuild ?: 'statebuild'
        def timeoutHours = outsourcePackageInfo.timeout_hours ?: 24
        def timeoutMinutes = timeoutHours * 60
        def fbLoginDetails = LibCommonNonCps.get_setting_value(branchInfo, modifiers, 'p4_fb_settings', [:], project)
        def userCredentials = LibCommonNonCps.get_setting_value(branchInfo, modifiers, 'user_credentials', '', project)
        def p4_code_server = LibCommonNonCps.get_setting_value(branchInfo, [], 'p4_code_server', '', project)
        def workspaceType = outsourcePackageInfo.workspace_type ?: 'streams'

        def p4ClientPackages = project.p4_code_client_env + '-outsource-package'
        if (workspaceType == 'streams') {
            p4ClientPackages = project.p4_code_client_env
        }

        def extraArgsList = outsourcePackageInfo.extra_args ?: []
        def extraArgs = extraArgsList.join(' ')
        if (branchInfo.frostbite_licensee) {
            extraArgs += ' --licensee ' + branchInfo.frostbite_licensee
        }
        if (outsourcePackageInfo.dry_run) {
            extraArgs += ' --dry-run'
        }
        if (userCredentials) {
            extraArgs += ' --email %monkeyEmail% --password "%monkeyPasswd%"'
        }

        // Add sections to the Jenkins job.
        job.with {
            description('Builds an outsource package job for ' + branchInfo.branch_name + '.')
            label(jobLabel)
            logRotator(7, 100)
            quietPeriod(0)
            customWorkspace(branchInfo.workspace_root)
            parameters {
                stringParam {
                    name('code_changelist')
                    defaultValue('')
                    description('Specifies code changelist to sync.')
                    trim(true)
                }
                choiceParam('clean_local', ['false', 'true'], 'If true, TnT/Local will be deleted at the beginning of the run.')
            }
            wrappers {
                colorizeOutput()
                timestamps()
                buildName('${JOB_NAME}.${ENV, var="code_changelist"}')
                timeout {
                    absolute(timeoutMinutes)
                    failBuild()
                    writeDescription('Build failed due to timeout after {0} minutes')
                }
                credentialsBinding {
                    if (userCredentials) {
                        usernamePassword('monkeyEmail', 'monkeyPasswd', userCredentials)
                    }
                    if (fbLoginDetails.p4_creds) {
                        usernamePassword('fbP4User', 'fbP4Passwd', fbLoginDetails.p4_creds)
                    }
                }
            }
            steps {
                if (fbLoginDetails) {
                    batchFile("echo %fbP4Passwd%|p4 -p ${fbLoginDetails.p4_port} -u %fbP4User% login & exit 0")
                }
                LibJobDsl.installElipy(delegate, branchInfo.elipy_install_call, project)
                batchFile(branchInfo.elipy_call + ' outsource_package --clean %clean_local% ' +
                    ' --code-changelist %code_changelist%' + ' --data-directory ' + branchInfo.dataset +
                    ' --p4-client-code ' + project.p4_code_client_env + ' --p4-client-packages ' + p4ClientPackages +
                    ' --p4-port ' + p4_code_server + ' --p4-user %P4_USER%' +
                    ' --script-path ' + outsourcePackageInfo.script_path + ' ' + extraArgs)
            }
        }
    }
}
