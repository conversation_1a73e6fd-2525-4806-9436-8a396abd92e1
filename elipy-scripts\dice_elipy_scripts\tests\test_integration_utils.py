"""
test_integration_utils.py

Unit testing for integration_utils
"""
import unittest
import pytest
import os
from mock import call, MagicMock, patch
from dice_elipy_scripts.utils.integration_utils import compile_code, cook_data, submit_integration
from elipy2.exceptions import ELIPYException


@patch.dict(os.environ, {"BUILD_URL": "jenkins-url.fake"}, clear=True)
@patch("os.path.join", MagicMock(side_effect=lambda *args: "//".join(args)))
class TestIntegrationUtils(unittest.TestCase):
    VALUE_ASSETS = ["asset_1", "asset_2"]
    VALUE_BUILDSLN_FRAMEWORK_ARGS = ["buildsln_fw_arg"]
    VALUE_CLIENT = "perforce_client"
    VALUE_CODE_BRANCH = "code_branch"
    VALUE_DATA_BRANCH = "data_branch"
    VALUE_DATA_DIRECTORY = "data_directory"
    VALUE_DOMAIN_USER = "domain_user"
    VALUE_EMAIL = "email_pkg"
    VALUE_INITIAL_MESSAGE = "initial_message"
    VALUE_LICENSEE = "licensee"
    VALUE_PASSWORD = "password_pkg"
    VALUE_PIPELINE_ARGS = ["arg1"]
    VALUE_PLATFORM = "win64"
    VALUE_PORT = "perforce_port"
    VALUE_URL_MESSAGE = f"\nJenkins URL: jenkins-url.fake"
    VALUE_USER = "perforce_user"

    def setUp(self):
        def settings_get_side_effect(key):
            if key == "snowcache_host":
                return {"tool": "host_name"}
            return key  # Hack to not care about keys that are irrelevant for the test.

        self.patcher_settings_get = patch("elipy2.SETTINGS.get")
        self.mock_settings_get = self.patcher_settings_get.start()
        self.mock_settings_get.side_effect = settings_get_side_effect

        self.patcher_codeutils = patch("elipy2.code.CodeUtils")
        self.mock_codeutils = self.patcher_codeutils.start()
        self.mock_codeutils.return_value = MagicMock()

        self.patcher_datautils = patch("elipy2.data.DataUtils")
        self.mock_datautils = self.patcher_datautils.start()
        self.mock_datautils.return_value = MagicMock()

        self.patcher_filerutils = patch("elipy2.filer.FilerUtils")
        self.mock_filerutils = self.patcher_filerutils.start()
        self.mock_filerutils.return_value = MagicMock()

        self.patcher_p4utils = patch("elipy2.p4.P4Utils")
        self.mock_p4utils = self.patcher_p4utils.start()
        self.mock_p4utils.return_value = MagicMock()
        self.mock_p4utils.return_value.latest_pending_changelist.return_value = "1234"

        self.patcher_cleanup_temp_dbs = patch("elipy2.avalanche.cleanup_temp_dbs")
        self.mock_cleanup_temp_dbs = self.patcher_cleanup_temp_dbs.start()

        self.patcher_get_game_data_dir = patch("elipy2.frostbite_core.get_game_data_dir")
        self.mock_get_game_data_dir = self.patcher_get_game_data_dir.start()
        self.mock_get_game_data_dir.return_value = "game_data_dir"

        self.patcher_get_latest_drone_changelist = patch(
            "dice_elipy_scripts.utils.integration_utils.get_latest_drone_changelist"
        )
        self.mock_get_latest_drone_changelist = self.patcher_get_latest_drone_changelist.start()

        self.patcher_get_snowcache_host_arg = patch(
            "dice_elipy_scripts.utils.integration_utils.get_snowcache_host_arg"
        )
        self.mock_get_snowcache_host_arg = self.patcher_get_snowcache_host_arg.start()
        self.mock_get_snowcache_host_arg.return_value = "snowcache_host_arg"

        self.patcher_get_snowcache_mode_args = patch(
            "dice_elipy_scripts.utils.integration_utils.get_snowcache_mode_args"
        )
        self.mock_get_snowcache_mode_args = self.patcher_get_snowcache_mode_args.start()
        self.mock_get_snowcache_mode_args.return_value = ["snowcache_mode_args"]

        self.patcher_import_avalanche_data_state = patch(
            "dice_elipy_scripts.utils.integration_utils.import_avalanche_data_state"
        )
        self.mock_import_avalanche_data_state = self.patcher_import_avalanche_data_state.start()
        self.mock_import_avalanche_data_state.return_value = ["avalanche_args"]

        self.patcher_run_gensln = patch("dice_elipy_scripts.utils.integration_utils.run_gensln")
        self.mock_run_gensln = self.patcher_run_gensln.start()

        self.patcher_set_licensee = patch("dice_elipy_scripts.utils.integration_utils.set_licensee")
        self.mock_set_licensee = self.patcher_set_licensee.start()
        self.mock_set_licensee.return_value = ["set_licensee_arg"]

        self.patcher_validate_snowcache = patch(
            "dice_elipy_scripts.utils.integration_utils.validate_use_snowcache_param"
        )
        self.mock_validate_snowcache = self.patcher_validate_snowcache.start()
        self.mock_validate_snowcache.return_value = False

    def tearDown(self):
        patch.stopall()

    def test_submit_integration(self):
        submit_integration(
            p4_object=self.mock_p4utils.return_value,
            submit_message=self.VALUE_INITIAL_MESSAGE,
            submit=True,
        )
        self.mock_p4utils.return_value.submit.assert_called_once_with(
            message=self.VALUE_INITIAL_MESSAGE + self.VALUE_URL_MESSAGE
        )

    def test_submit_integration_no_submit(self):
        submit_integration(
            p4_object=self.mock_p4utils.return_value,
            submit_message=self.VALUE_INITIAL_MESSAGE,
            submit=False,
        )
        assert self.mock_p4utils.return_value.submit.call_count == 0

    def test_submit_integration_data_upgrade(self):
        submit_integration(
            p4_object=self.mock_p4utils.return_value,
            submit_message=self.VALUE_INITIAL_MESSAGE,
            submit=True,
            data_upgrade=True,
        )
        self.mock_p4utils.return_value.submit.assert_called_once_with(
            message=self.VALUE_INITIAL_MESSAGE + self.VALUE_URL_MESSAGE
        )
        self.mock_p4utils.return_value.reopen.assert_called_once_with(file_name="//...")
        self.mock_p4utils.return_value.revert.assert_called_once_with(
            wipe=False, only_unchanged=True
        )

    def test_submit_integration_data_upgrade_submit_folder(self):
        submit_integration(
            p4_object=self.mock_p4utils.return_value,
            submit_message=self.VALUE_INITIAL_MESSAGE,
            submit=True,
            data_upgrade=True,
            submit_folder="submit_folder",
        )
        self.mock_p4utils.return_value.submit.assert_called_once_with(
            message=self.VALUE_INITIAL_MESSAGE + self.VALUE_URL_MESSAGE
        )
        self.mock_p4utils.return_value.reopen.assert_called_once_with(
            file_name="game_data_dir//submit_folder//..."
        )
        self.mock_p4utils.return_value.revert.assert_called_once_with(
            wipe=False, only_unchanged=True
        )

    def test_submit_integration_data_upgrade_revert_branchid_file(self):
        submit_integration(
            p4_object=self.mock_p4utils.return_value,
            submit_message=self.VALUE_INITIAL_MESSAGE,
            submit=True,
            data_upgrade=True,
            revert_branchid_file=True,
        )
        self.mock_p4utils.return_value.submit.assert_called_once_with(
            message=self.VALUE_INITIAL_MESSAGE + self.VALUE_URL_MESSAGE
        )
        self.mock_p4utils.return_value.reopen.assert_called_once_with(file_name="//...")
        assert self.mock_p4utils.return_value.revert.call_count == 2
        self.mock_p4utils.return_value.revert.assert_has_calls(
            [
                call(wipe=False, only_unchanged=True),
                call(path="game_data_dir//Scripts//BranchId.py"),
            ]
        )

    def test_no_submit_shelve_cl(self):
        submit_integration(
            p4_object=self.mock_p4utils.return_value,
            submit_message=self.VALUE_INITIAL_MESSAGE,
            submit=False,
            data_upgrade=False,
            revert_branchid_file=False,
            shelve_cl=True,
        )
        self.mock_p4utils.return_value.latest_pending_changelist.assert_called_once_with()
        self.mock_p4utils.return_value.set_description.assert_called_once_with(
            "1234",
            "Shelved changelist from integration running with --no-submit."
            + self.VALUE_URL_MESSAGE,
        )
        self.mock_p4utils.return_value.shelve.assert_called_once_with("1234", discard=False)

    def test_submit_shelve_cl(self):
        submit_integration(
            p4_object=self.mock_p4utils.return_value,
            submit_message=self.VALUE_INITIAL_MESSAGE,
            submit=True,
            data_upgrade=False,
            revert_branchid_file=False,
            shelve_cl=True,
        )
        self.mock_p4utils.return_value.submit.assert_called_once_with(
            message=self.VALUE_INITIAL_MESSAGE + self.VALUE_URL_MESSAGE
        )
        assert self.mock_p4utils.return_value.latest_pending_changelist.call_count == 0
        assert self.mock_p4utils.return_value.set_description.call_count == 0
        assert self.mock_p4utils.return_value.shelve.call_count == 0

    def test_no_submit_skip_shelve_cl(self):
        submit_integration(
            p4_object=self.mock_p4utils.return_value,
            submit_message=self.VALUE_INITIAL_MESSAGE,
            submit=False,
            data_upgrade=False,
            revert_branchid_file=False,
            shelve_cl=False,
        )
        assert self.mock_p4utils.return_value.submit.call_count == 0
        assert self.mock_p4utils.return_value.latest_pending_changelist.call_count == 0
        assert self.mock_p4utils.return_value.set_description.call_count == 0
        assert self.mock_p4utils.return_value.shelve.call_count == 0

    def test_no_submit_shelve_cl_no_pending_changelist(self):
        self.mock_p4utils.return_value.latest_pending_changelist.return_value = None
        submit_integration(
            p4_object=self.mock_p4utils.return_value,
            submit_message=self.VALUE_INITIAL_MESSAGE,
            submit=False,
            data_upgrade=False,
            revert_branchid_file=False,
            shelve_cl=True,
        )
        self.mock_p4utils.return_value.latest_pending_changelist.assert_called_once_with()
        assert self.mock_p4utils.return_value.set_description.call_count == 0
        assert self.mock_p4utils.return_value.shelve.call_count == 0

    def test_compile_code_default(self):
        compile_code(
            licensee=[self.VALUE_LICENSEE],
            password=self.VALUE_PASSWORD,
            email=self.VALUE_EMAIL,
            domain_user=self.VALUE_DOMAIN_USER,
        )
        self.mock_set_licensee.assert_called_once_with(["licensee"], [])
        self.mock_codeutils.assert_called_once_with(
            "tool",
            "release",
            p4_port=None,
            p4_user=None,
            p4_client=None,
            overwrite_p4config=False,
        )
        self.mock_run_gensln.assert_called_once_with(
            builder=self.mock_codeutils.return_value,
            password=self.VALUE_PASSWORD,
            user=self.VALUE_EMAIL,
            domain_user=self.VALUE_DOMAIN_USER,
            framework_args=["set_licensee_arg"],
        )

    def test_compile_code_buildsln(self):
        compile_code(
            licensee=[self.VALUE_LICENSEE],
            password=self.VALUE_PASSWORD,
            email=self.VALUE_EMAIL,
            domain_user=self.VALUE_DOMAIN_USER,
        )
        self.mock_codeutils.return_value.buildsln.assert_called_once_with(framework_args=None)

    def test_compile_code_buildsln_framework_args(self):
        compile_code(
            licensee=[self.VALUE_LICENSEE],
            password=self.VALUE_PASSWORD,
            email=self.VALUE_EMAIL,
            domain_user=self.VALUE_DOMAIN_USER,
            buildsln_framework_args=self.VALUE_BUILDSLN_FRAMEWORK_ARGS,
        )
        self.mock_codeutils.return_value.buildsln.assert_called_once_with(
            framework_args=self.VALUE_BUILDSLN_FRAMEWORK_ARGS
        )

    def test_compile_code_p4_settings(self):
        compile_code(
            licensee=[self.VALUE_LICENSEE],
            password=self.VALUE_PASSWORD,
            email=self.VALUE_EMAIL,
            domain_user=self.VALUE_DOMAIN_USER,
            port=self.VALUE_PORT,
            user=self.VALUE_USER,
            client=self.VALUE_CLIENT,
        )
        self.mock_codeutils.assert_called_once_with(
            "tool",
            "release",
            p4_port=self.VALUE_PORT,
            p4_user=self.VALUE_USER,
            p4_client=self.VALUE_CLIENT,
            overwrite_p4config=False,
        )

    def test_compile_code_snowcache_default(self):
        self.mock_validate_snowcache.return_value = True
        compile_code(
            licensee=[self.VALUE_LICENSEE],
            password=self.VALUE_PASSWORD,
            email=self.VALUE_EMAIL,
            domain_user=self.VALUE_DOMAIN_USER,
            use_snowcache=True,
        )
        self.mock_get_snowcache_mode_args.assert_called_once_with(False, "", True)
        self.mock_run_gensln.assert_called_once_with(
            builder=self.mock_codeutils.return_value,
            password=self.VALUE_PASSWORD,
            user=self.VALUE_EMAIL,
            domain_user=self.VALUE_DOMAIN_USER,
            framework_args=["set_licensee_arg", "snowcache_host_arg", "snowcache_mode_args"],
        )

    def test_cook_data_default(self):
        self.mock_get_latest_drone_changelist.return_value = "1234"
        cook_data(
            assets=self.VALUE_ASSETS,
            data_directory=self.VALUE_DATA_DIRECTORY,
            platform=self.VALUE_PLATFORM,
            code_branch=self.VALUE_CODE_BRANCH,
        )
        self.mock_filerutils.return_value.fetch_code.assert_called_once_with(
            self.VALUE_CODE_BRANCH,
            "1234",
            "pipeline",
            "release",
            use_bilbo=False,
        )
        self.mock_datautils.return_value.cook.assert_called_once_with(
            pipeline_args=[],
            trim=False,
            collect_mdmps=True,
            clean_master_version_check=True,
        )

    def test_cook_data_copy_code_no_code_branch(self):
        with pytest.raises(ELIPYException):
            cook_data(
                assets=self.VALUE_ASSETS,
                data_directory=self.VALUE_DATA_DIRECTORY,
                platform=self.VALUE_PLATFORM,
            )

    def test_cook_data_use_local_code(self):
        cook_data(
            assets=self.VALUE_ASSETS,
            data_directory=self.VALUE_DATA_DIRECTORY,
            platform=self.VALUE_PLATFORM,
            use_local_code=True,
        )
        assert self.mock_filerutils.return_value.fetch_code.call_count == 0

    def test_cook_data_pipeline_args(self):
        self.mock_get_latest_drone_changelist.return_value = "1234"
        cook_data(
            assets=self.VALUE_ASSETS,
            data_directory=self.VALUE_DATA_DIRECTORY,
            platform=self.VALUE_PLATFORM,
            code_branch=self.VALUE_CODE_BRANCH,
            pipeline_args=self.VALUE_PIPELINE_ARGS,
        )
        self.mock_datautils.return_value.cook.assert_called_once_with(
            pipeline_args=self.VALUE_PIPELINE_ARGS,
            trim=False,
            collect_mdmps=True,
            clean_master_version_check=True,
        )

    def test_cook_data_import_avalanche_missing_args(self):
        with pytest.raises(ELIPYException):
            cook_data(
                assets=self.VALUE_ASSETS,
                data_directory=self.VALUE_DATA_DIRECTORY,
                platform=self.VALUE_PLATFORM,
                import_avalanche=True,
                use_local_code=True,
            )

    def test_cook_data_import_avalanche(self):
        cook_data(
            assets=self.VALUE_ASSETS,
            data_directory=self.VALUE_DATA_DIRECTORY,
            platform=self.VALUE_PLATFORM,
            code_branch=self.VALUE_CODE_BRANCH,
            data_branch=self.VALUE_DATA_BRANCH,
            import_avalanche=True,
            p4_object=self.mock_p4utils.return_value,
            pipeline_args=self.VALUE_PIPELINE_ARGS,
            use_local_code=True,
        )
        self.mock_import_avalanche_data_state.assert_called_once_with(
            self.VALUE_DATA_BRANCH,
            self.VALUE_CODE_BRANCH,
            self.VALUE_PLATFORM,
            self.mock_filerutils.return_value,
            "1234",
        )
        self.mock_datautils.return_value.cook.assert_called_once_with(
            pipeline_args=["arg1", "avalanche_args"],
            trim=False,
            collect_mdmps=True,
            clean_master_version_check=True,
        )

    def test_cook_data_clean_avalanche_cook(self):
        cook_data(
            assets=self.VALUE_ASSETS,
            data_directory=self.VALUE_DATA_DIRECTORY,
            platform=self.VALUE_PLATFORM,
            clean_avalanche_cook=True,
            use_local_code=True,
        )
        self.mock_datautils.return_value.clean.assert_called_once_with()
