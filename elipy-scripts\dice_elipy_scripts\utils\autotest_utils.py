"""
utility module
"""
import time
import json

from elipy2 import LOGGER, filer_paths, build_metadata_utils
from elipy2.exceptions import ELIPYException


def handle_failure(test_results, test_suite_name, autotest_results_args):
    """
    Reports a failure to the configured metadata services
    """
    test_results += "- {} failed\n".format(test_suite_name)
    register_autotest_results(test_status="failed", **autotest_results_args)
    return test_results


def register_autotest_results(
    should_register_in_bilbo,
    code_branch,
    code_changelist,
    data_branch,
    data_changelist,
    test_definition,
    test_status,
    test_suite=None,
    platform=None,
    max_retries=5,
    wait_time_seconds=180,
):
    """
    Registers autotest result in the configured metadata services.
    If the build doesn't exist in Bilbo, it will wait and retry up to max_retries times.
    """
    metadata_manager = build_metadata_utils.setup_metadata_manager()
    if should_register_in_bilbo:
        LOGGER.info(
            "Registering Autotest build on code {}.CL{} with data {}.CL{}".format(
                code_branch, code_changelist, data_branch, data_changelist
            )
        )
        LOGGER.info(
            "Registering test: {}, category: {} with status: {}.".format(
                test_suite, test_definition, test_status
            )
        )

        build_path = filer_paths.get_code_build_root_path(code_branch, code_changelist)

        # Check if the build exists in Bilbo before registering autotests
        retry_count = 0
        build_exists = False

        while retry_count < max_retries and not build_exists:
            try:
                # Try to get the build from Bilbo
                builds = metadata_manager.get_builds_matching(build_path)
                if builds and len(builds) > 0:
                    build_exists = True
                    LOGGER.info("Build found in Bilbo: %s", build_path)
                else:
                    retry_count += 1
                    if retry_count < max_retries:
                        LOGGER.warning(
                            "Build not found in Bilbo: %s. "
                            "Waiting %s seconds before retry %s/%s",
                            build_path,
                            wait_time_seconds,
                            retry_count,
                            max_retries,
                        )
                        time.sleep(wait_time_seconds)
                    else:
                        LOGGER.warning(
                            "Build not found in Bilbo after %s retries: %s.",
                            max_retries,
                            build_path,
                        )
            except IndexError as index_error:
                LOGGER.warning(
                    "Error checking if build exists: %s. Continuing with waiting for build.",
                    str(index_error),
                )
                retry_count += 1
        if not build_exists:
            raise ELIPYException(
                "Build not found in Bilbo after {} retries: {}".format(max_retries, build_path)
            )
        metadata_manager.register_autotest_build(
            build_path,
            data_changelist=data_changelist,
            data_branch=data_branch,
            test_category=test_definition,
            test_name=test_suite,
            status=test_status,
            platform=platform,
            write_attributes_file=False,
        )


def get_test_suites_names(test_suites):
    """
    Parse and output test suite names
    """
    test_suite_names = []
    LOGGER.info("Going to run %s test suites:", len(test_suites))
    for test_suite in test_suites:
        LOGGER.info("DECODED TEST SUITE: %s", test_suite)
        if "name" not in test_suite:
            raise ELIPYException("Unexpected args in test suites json. Expected name")
        test_suite_names.append(test_suite["name"])

    return test_suite_names


def get_builds_with_config(builds, config):
    """
    Return a list of builds that contains config as config value or in additional configs
    """
    if not config:
        LOGGER.info("Config value is not provided.")
        return builds

    _builds = []
    builds_with_config = list(filter(lambda build: build.source["config"] == config, builds))
    if builds_with_config:
        _builds = builds_with_config
    else:
        _builds = list(
            filter(
                lambda build: "additional_configs" in build.source
                and config in build.source["additional_configs"],
                builds,
            )
        )
    return _builds


def get_test_suites(test_suites_json, test_suites):
    """
    Load and return test suites from either a JSON file or a list of JSON strings.
    @param test_suites_json: Path to a JSON file containing a list of test suites.
    @param test_suites: A list of test suite definitions as JSON strings.
    @return: A list of test suite dictionaries.
    @raise: ELIPYException: If neither test_suites_json nor test_suites is provided.
    """
    if test_suites_json:
        with open(test_suites_json) as json_file:
            LOGGER.info("Test suites provided by --test-suite-json %s", test_suites_json)
            try:
                return list(json.load(json_file))
            except json.decoder.JSONDecodeError as exc:
                LOGGER.error(str(exc))
                raise ELIPYException(
                    "Test suite on file {} contains an invalid JSON format".format(test_suites_json)
                )
    elif test_suites and not test_suites_json:
        test_suite_list = []
        for test_suite in test_suites:
            decoded_test_suite = json.loads(test_suite)
            test_suite_list.append(decoded_test_suite)
        LOGGER.info("Test suites provided by --test-suite args")
        return test_suite_list
    else:
        raise ELIPYException(
            "Test suite information needs to be provided by either a test suite json using "
            "--test-suite-json (--tsj) or optionally multiple (at least one) --test-suite (--ts) "
            "arguments."
        )
