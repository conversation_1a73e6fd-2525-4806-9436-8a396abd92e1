"""
offsitebuild.py

Stores a build in the offsite folder compressed as a zip file.
"""
import os
import tempfile
import click
import pickle
from dice_elipy_scripts.utils.decorators import throw_if_files_found
from dice_elipy_scripts.utils.sentry_utils import add_sentry_tags
from elipy2 import build_metadata_utils, code, core, filer_paths, local_paths, LOGGER, secrets
from elipy2.cli import pass_context
from elipy2.exceptions import CoreException
from elipy2.telemetry import collect_metrics
from pathlib import Path
from typing import List, Optional


@click.command("offsitebuild", short_help="Stores a build in the Offsite folder.")
@click.option("--code-changelist", required=True, help="Code changelist to store.")
@click.option("--code-branch", required=True, help="Which code branch to store.")
@click.option("--destination", default=None, help="Where to move the build.")
@click.option("--basic-drone-build", is_flag=True, help="Should we exclude console builds")
@click.option("--basic-drone-zip", is_flag=True, help="Basic (excluding console) drone zip builds")
@click.option("--qa-verified-builds-only", is_flag=True, help="Excluding non-verified QA build")
@click.option(
    "--force-update-zip",
    is_flag=True,
    help="Overwrite the existing zip file if it exists",
)
@click.option("--outsourcer", multiple=True, help="Outsourcer with their own network share folder.")
@pass_context
@throw_if_files_found()
@collect_metrics(os.path.basename(__file__))
def cli(
    _,
    code_changelist,
    code_branch,
    destination,
    basic_drone_build,
    basic_drone_zip,
    qa_verified_builds_only,
    force_update_zip,
    outsourcer,
):
    """
    Stores a build in the Offsite folder, compressed as a zip file.
    """
    outsourcers = list(outsourcer)
    offsitebuild(
        code_changelist,
        code_branch,
        destination,
        outsourcers,
        basic_drone_build,
        basic_drone_zip,
        qa_verified_builds_only,
        force_update_zip,
    )


def offsitebuild(
    code_changelist: str,
    code_branch: str,
    destination: str,
    outsourcers: List[str],
    basic_drone_build: Optional[bool] = False,
    basic_drone_zip: Optional[bool] = False,
    qa_verified_builds_only: Optional[bool] = False,
    force_update_zip: Optional[bool] = False,
):
    """
    Internal function, added when adapting the script for later Python versions.
    """
    # Adding sentry tags
    add_sentry_tags(__file__)

    code_root_path = filer_paths.get_code_build_root_path(code_branch, code_changelist)
    is_qa_passed = is_qa_qualify(code_branch, code_changelist) if qa_verified_builds_only else True
    destination_list = []
    if destination is None:
        if basic_drone_build:
            destination = filer_paths.get_offsite_basic_build(code_branch, code_changelist)
        elif basic_drone_zip:
            # Once DICE have adopted this new structure, we can merge basic and basic_drone logic
            destination = filer_paths.get_offsite_basic_drone_build(code_branch, code_changelist)
        elif outsourcers:
            for outsourcer in outsourcers:
                destination_list.append(
                    filer_paths.get_outsourcer_build(outsourcer, code_branch, code_changelist)
                )
        else:
            destination = filer_paths.get_offsite_build(code_branch, code_changelist)
    if not outsourcers:
        dst = (
            str(Path(destination).parents[0])
            if basic_drone_build
            else str(Path(destination).parents[1])
        )
        LOGGER.info("Copy build.json from {} to {}".format(code_root_path, dst))
        core.robocopy(
            code_root_path, dst, extra_args=["build.json"], include_empty_dirs=False, quiet=True
        )

    dest_to_check = destination if not outsourcers else destination_list[0]
    destination_exists = os.path.exists(dest_to_check)
    if is_qa_passed and (not destination_exists or force_update_zip):
        with tempfile.TemporaryDirectory() as temp_dir:
            fingerprint_path = os.path.join("Win64-Dll", "release", "fingerprint.bin")
            if basic_drone_build or basic_drone_zip or outsourcers:
                source = get_basic_drone_source(code_root_path, temp_dir, fingerprint_path)
                update_eacopy_file_list(source)
            else:
                source = get_full_drone_source(code_root_path, temp_dir, fingerprint_path)

            if outsourcers:
                for dest in destination_list:
                    core.robocopy(source, dest, quiet=True)
            else:
                try:
                    core.create_zip(source, destination, skip_compression=[fingerprint_path])
                except CoreException as exc:
                    core.delete_folder(destination + ".tmp")
                    core.delete_folder(destination + ".tmp1")
                    raise exc
    else:
        LOGGER.info("File(s) already existing in destination, skipping creation.")


def get_basic_drone_source(code_root_path: str, temp_dir: str, fingerprint_path: str):
    """
    Helper function to create the directory structure required for drone build
    when we only want a subset of folders.
    """
    # copy build.json
    core.robocopy(
        code_root_path, temp_dir, extra_args=["build.json"], include_empty_dirs=False, quiet=True
    )
    # win64-dll includes tool so only win64-dll needed
    platform_path_part = local_paths.get_platform_path("Win64-dll")
    temp_target_path = os.path.join(temp_dir, platform_path_part)
    platform_source_path = os.path.join(code_root_path, platform_path_part)
    os.makedirs(temp_target_path)
    core.robocopy(
        platform_source_path, temp_target_path, extra_args=["/XF", "*.pdb", "*.map"], quiet=True
    )
    add_fingerprint(os.path.join(temp_dir, fingerprint_path))
    return temp_dir


def get_full_drone_source(code_root_path: str, temp_dir: str, fingerprint_path: str):
    """
    Helper function to create the directory structure required for full drone builds,
    where we need to add a fingerprint file.
    """
    # Copy the build to a temp dir.
    core.robocopy(code_root_path, temp_dir, quiet=True)
    # Add a fingerprint file to the temp dir.
    add_fingerprint(os.path.join(temp_dir, fingerprint_path))
    return temp_dir


def update_eacopy_file_list(source: str):
    """
    Helper function to remove files from the drone offsite builds.
    """
    platform_path_part = local_paths.get_platform_path("Win64-dll")
    file_path = os.path.join(source, platform_path_part, "release")

    code.CodeUtils.remove_files_for_eacopy(
        platform="tool", config="release", exclude_pattern=".pdb", file_path=file_path
    )


def is_qa_qualify(code_branch: str, code_changelist: str):
    """
    Added special check for Customer Request.
    qa_verified tag is required to build zip files for dice-next.
    """
    ret = True
    query = {
        "type.keyword": "drone",
        "branch.keyword": code_branch,
        "changelist.keyword": code_changelist,
    }
    metadata_manager = build_metadata_utils.setup_metadata_manager()
    build = list(metadata_manager.get_all_builds(limit=1, query=query))
    if build[0].source.get("build_promotion_level") != "qa_verified":
        ret = False
    return ret


def add_fingerprint(fingerprint_path: str):
    """
    Adds a fingerprint file to the build.
    """
    ess_creds = secrets.get_secrets({"build_type": "drone"})
    credentials = next(v for v in ess_creds.values())
    fingerprint = credentials["fingerprint"]

    with open(fingerprint_path, "wb") as fp_file:
        fp_file.write(pickle.dumps(fingerprint))
