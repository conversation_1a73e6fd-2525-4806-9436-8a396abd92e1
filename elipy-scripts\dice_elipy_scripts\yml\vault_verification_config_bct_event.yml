root:
  default:
    base: [ '$DESTINATION_BASEPATH$', '$VERSION$' ]
    paths:
    - path_location: []
      min_items: 1

server:
  default:
    base: [ '$DESTINATION_BASEPATH$', '$VERSION$', 'server', '$DATACHANGELIST$_$CODECHANGELIST$' ]
    paths:
    - path_location: [ 'symbol' ]
    - path_location: [ 'files', 'event', 'final', 'build.json' ]
    - path_location: [ 'files', 'event', 'final' ]
      min_items: 25

linuxserver:
  default:
    base: ['$DESTINATION_BASEPATH$', '$VERSION$', 'linuxserver', '$DATACHANGELIST$_$CODECHANGELIST$' ]
    paths:
    - path_location: [ 'symbol']
    - path_location: [ 'digital', 'event', 'final', 'FrostyLogFile.txt' ]
    - path_location: [ 'digital', 'event', 'final', 'build.json' ]
    - path_location: [ 'digital', 'event', 'final', 'builtLevels.json' ]
    - path_location: [ 'digital', 'event', 'final', 'Frostbite_win32_Server_$DATACHANGELIST$_Binaries.zip' ]

ps5:
  default:
    base: ['$DESTINATION_BASEPATH$', '$VERSION$', 'ps5', '$DATACHANGELIST$_$CODECHANGELIST$' ]
    paths:
    - path_location: [ 'symbol' ]
    - path_location: [ 'bundles', 'head', 'data', 'ops_chain.zip' ]
    - path_location: [ 'bundles', 'state', 'cas.cat' ]
    - path_location: [ 'bundles', 'state', 'meta', 'CURRENT' ]
    - path_location: [ 'patch', 'event', 'final', 'publish.log' ]
    - path_location: [ 'patch', 'event', 'final', 'streaminstall_chunk.log' ]
    - path_location: [ 'patch', 'event', 'retail', 'publish.log' ]
    - path_location: [ 'patch', 'event', 'retail', 'streaminstall_chunk.log' ]
    - path_location: [ 'bundles', 'head', 'data' ]
      min_items: 10
    - path_location: [ 'bundles', 'state' ]
      min_items: 25
    - path_location: [ 'bundles', 'state', 'meta' ]
      min_items: 450
    - path_location: [ 'files', 'event', 'final' ]
      min_items: 20
    - path_location: [ 'patch', 'event', 'final' ]
      min_items: 10
    - path_location: [ 'patch', 'event', 'retail' ]
      min_items: 10

xbsx:
  default:
    base: ['$DESTINATION_BASEPATH$', '$VERSION$', 'xbsx', '$DATACHANGELIST$_$CODECHANGELIST$']
    paths:
    - path_location: [ 'symbol' ]
    - path_location: [ 'bundles', 'head', 'data', 'ops_chain.zip' ]
    - path_location: [ 'bundles', 'state', 'cas.cat' ]
    - path_location: [ 'bundles', 'state', 'meta', 'CURRENT' ]
    - path_location: [ 'patch', 'event', 'final', 'streaminstall_chunk.log' ]
    - path_location: [ 'patch', 'event', 'final', 'BattlefieldGame.Main_Xbsx_final.exe' ]
    - path_location: [ 'patch', 'event', 'final', 'BattlefieldGame.Main_Xbsx_final.pdb' ]
    - path_location: [ 'patch', 'event', 'retail', 'streaminstall_chunk.log' ]
    - path_location: [ 'patch', 'event', 'retail', 'BattlefieldGame.Main_Xbsx_retail.exe' ]
    - path_location: [ 'patch', 'event', 'retail', 'BattlefieldGame.Main_Xbsx_retail.pdb' ]
    - path_location: [ 'bundles', 'head', 'data' ]
      min_items: 10
    - path_location: [ 'bundles', 'state' ]
      min_items: 25
    - path_location: [ 'bundles', 'state', 'meta' ]
      min_items: 450
    - path_location: [ 'files', 'event', 'final' ]
      min_items: 40

win64:
  default:
    base: [ '$DESTINATION_BASEPATH$', '$VERSION$', 'win64', '$DATACHANGELIST$_$CODECHANGELIST$' ]
    paths:
    - path_location: [ 'symbol' ]
    - path_location: [ 'bundles', 'head', 'data', 'ops_chain.zip' ]
    - path_location: [ 'bundles', 'state', 'cas.cat' ]
    - path_location: [ 'bundles', 'state', 'meta', 'CURRENT' ]
    - path_location: [ 'patch', 'event', 'final', 'bf6event.exe' ]
    - path_location: [ 'patch', 'event', 'final', 'streaminstall_chunk.log' ]
    - path_location: [ 'patch', 'event', 'final', 'BattlefieldGame.Main_Win64_final.pdb' ]
    - path_location: [ 'patch', 'event', 'final', 'BattlefieldGameData.zip' ]
    - path_location: [ 'patch', 'event', 'retail', 'bf6event.exe' ]
    - path_location: [ 'patch', 'event', 'retail', 'streaminstall_chunk.log' ]
    - path_location: [ 'patch', 'event', 'retail', 'BattlefieldGame.Main_Win64_retail.pdb' ]
    - path_location: [ 'patch', 'event', 'retail', 'BattlefieldGameData.zip' ]
    - path_location: [ 'bundles' ]
      min_items: 3
    - path_location: [ 'bundles', 'head', 'data' ]
      min_items: 10
    - path_location: [ 'bundles', 'state' ]
      min_items: 25
    - path_location: ['bundles', 'state', 'meta']
      min_items: 450
    - path_location: [ 'files', 'event', 'final' ]
      min_items: 50
    - path_location: [ 'steam', 'event', 'final' ]
      min_items: 10
    - path_location: [ 'steam', 'event', 'retail' ]
      min_items: 10
