{#
    Command:
        backup_baseline
            short_help: Backup a frosty build that will be used as a baseline later.

    Arguments:

    Required variables:
        platform
            help: Which platform to back up.
            required: True
            type: click.Choice(['ps4', 'ps5', 'win64', 'xb1', 'xbsx', 'all-but-gen5', 'all-but-gen4', 'all-but-win64', 'all'])
        baseline_data_branch
            required: True
            help: Which branch the baseline data came from.
        baseline_data_changelist
            required: True
            help: Which changelist the baseline data came from.
        baseline_code_branch
            required: True
            help: Which branch the baseline code came from.
        baseline_code_changelist
            required: True
            help: Which changelist the baseline code came from.

    Optional variables:
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    backup_baseline
    --platform {{ platform }}
    --baseline-data-branch {{ baseline_data_branch }}
    --baseline-data-changelist {{ baseline_data_changelist }}
    --baseline-code-branch {{ baseline_code_branch }}
    --baseline-code-changelist {{ baseline_code_changelist }}
  displayName: elipy backup_baseline
