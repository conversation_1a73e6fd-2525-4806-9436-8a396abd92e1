{#
    Command:
        bilbo_select_autotest
            short_help: Uses the primary metadata service to find build for testing.

    Arguments:

    Required variables:
        code_branch
            help: Perforce code branch/stream name.
            required: True
        data_branch
            help: Perforce data branch/stream name.
            required: True

    Optional variables:
        is_test_with_loose_files/__is_not_test_with_loose_files
            default: False
            help: Whether or not it's a test with loose files.
        use_shift_build
            default: False
            type: bool
            help: Whether or not it's a test with Shift builds.
        use_spin_build
            default: False
            type: bool
            help: Whether or not it's a test with Spin builds.
        platform
            help: Which platform the tests run on
            required: False
            default: None
        required_platforms
            help: What platforms are required
            required: False
            multiple: True
        prerequisite_test
            help: What test tier should already be successful on the build.
            default: None
        use_latest_drone
            default: False
            type: bool
            help: Whether or not to use the latest Drone build.
        build_timeout_hours
            default: 1
            type: int
            help: Mark frosty builds as in use for build-timeout-hours => won't be deleted
        job_url
            default: ''
            help: The URL to the start job
        region
            help: Which frosty region to find builds for
            default: ww
        config
            help: Which frosty config to find builds for
            default: final
#}

- script: >
    $(WorkspaceRoot)/{{ site_variables.stream_name }}/tnt/bin/fbcli/cli.bat x64 &&
    $(Build.SourcesDirectory)/elipy-setup/setup-elipy-env.bat {{ site_variables.elipy_config }} &&
    elipy
    {%- if debug %} --debug {%- endif %}
    {%- if location %} --location {{ location }} {%- endif %}
    {%- if use_fbenv_core %} --use-fbenv-core {%- endif %}
    {%- if use_fbcli %} --use-fbcli {%- endif %}
    bilbo_select_autotest
    --code-branch {{ code_branch }}
    --data-branch {{ data_branch }}
    {%- if is_test_with_loose_files/__is_not_test_with_loose_files %}
    --is-test-with-loose-files/--is-not-test-with-loose-files {{ is_test_with_loose_files/__is_not_test_with_loose_files }}
    {%- endif %}
    {%- if use_shift_build %}
    --use-shift-build {{ use_shift_build }}
    {%- endif %}
    {%- if use_spin_build %}
    --use-spin-build {{ use_spin_build }}
    {%- endif %}
    {%- if platform %}
    --platform {{ platform }}
    {%- endif %}
    {%- if required_platforms %}
    --required-platforms {{ required_platforms }}
    {%- endif %}
    {%- if prerequisite_test %}
    --prerequisite-test {{ prerequisite_test }}
    {%- endif %}
    {%- if use_latest_drone %}
    --use-latest-drone {{ use_latest_drone }}
    {%- endif %}
    {%- if build_timeout_hours %}
    --build-timeout-hours {{ build_timeout_hours }}
    {%- endif %}
    {%- if job_url %}
    --job-url {{ job_url }}
    {%- endif %}
    {%- if region %}
    --region {{ region }}
    {%- endif %}
    {%- if config %}
    --config {{ config }}
    {%- endif %}
  displayName: elipy bilbo_select_autotest
