package com.ea.project.nfs.branchsettings

import com.ea.project.nfs.NFSUpgrade

class Upgrade {
    // Settings for jobs
    static Class project = NFSUpgrade
    static Map general_settings = [
        dataset           : project.dataset,
        elipy_install_call: project.elipy_install_call,
        elipy_call        : project.elipy_call,
        workspace_root    : project.workspace_root,
    ]
    static Map standard_jobs_settings = [
        asset                        : 'DevelopmentLevels',
        avalanche_compress_bundles   : true,
        clean_master_version_check   : true,
        dataset_in_path              : true,
        frosty_asset                 : 'StagingLevels',
        frosty_reference_job         : 'upgrade.code.start',
        frosty_server_asset          : 'StagingLevels',
        job_label_statebuild         : 'upgrade && util',
        server_asset                 : 'DevelopmentLevels',
        skip_code_build_if_no_changes: false,
        shift_branch                 : true,
        trigger_string_shift         : 'TZ=Europe/London \n H 01,12,18 * * 1-5',
        statebuild_code              : false,
        statebuild_code_nomaster     : false,
        statebuild_data              : false,
        statebuild_frosty            : false,
    ]
    static Map preflight_settings = [:]

    // Matrix definitions for jobs
    static List code_matrix = [
        [name: 'win64game', configs: ['final', 'release', 'performance', 'retail']],
        [name: 'ps5', configs: ['final', 'release', 'performance', 'retail']],
        [name: 'xbsx', configs: ['final', 'release', 'performance', 'retail']],
        [name: 'win64server', configs: ['final', 'release']],
        [name: 'linux64server', configs: ['final']],
        [name: 'tool', configs: ['release']],
    ]
    static List code_nomaster_matrix = [
        [name: 'win64game', configs: ['release']],
        [name: 'ps5', configs: ['final']],
        [name: 'xbsx', configs: ['retail']],
        [name: 'linux64server', configs: ['final']],
    ]
    static List code_downstream_matrix = [
        [name: '.data.start', args: []],
        [name: '.frosty.start', args: []],
    ]
    static List data_matrix = [
        [name: 'win64'],
        [name: 'ps5'],
        [name: 'xbsx'],
        [name: 'server'],
    ]
    static List data_downstream_matrix = []
    static List patchdata_matrix = []
    static List frosty_matrix = [
        [name: 'win64', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release --additional-configs retail --additional-configs performance']]],
        [name: 'ps5', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release --additional-configs retail --additional-configs performance']]],
        [name: 'xbsx', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release --additional-configs retail --additional-configs performance']]],
        [name: 'server', variants: [[format: 'files', config: 'final', region: 'ww', args: ' --additional-configs release']]],
        [name: 'linuxserver', variants: [[format: 'digital', config: 'final', region: 'ww', args: '']]],
    ]
    static List frosty_downstream_matrix = []
    static List frosty_for_patch_matrix = []
    static List patchfrosty_matrix = []
    static List patchfrosty_downstream_matrix = []
    static List code_preflight_matrix = []
    static List data_preflight_matrix = []
    static List spin_upload_matrix = []
    static List shift_upload_matrix = []
    static List shift_downstream_matrix = []
    static List freestyle_job_trigger_matrix = []
    static List azure_uploads_matrix = []
}
