"""
test_sentry_utils.py

Unit testing for sentry_utils
"""
from mock import MagicMock, patch
from dice_elipy_scripts.utils import sentry_utils


@patch("dice_elipy_scripts.utils.sentry_utils.add_frostbite_sentry_tags", MagicMock())
class TestSentryUtilsSetTags:
    @patch("sentry_sdk.scope.Scope.set_tag")
    def test_set_tag(self, mock_set_tag):
        sentry_utils.add_sentry_tags("c:/testing/again/file.py", "bar")
        assert mock_set_tag.call_count == 2


class TestSentryUtilsAddFrostbiteSentryTags:
    @patch("sentry_sdk.scope.Scope.set_tag")
    def test_add_frostbite_sentry_tags(self, mock_set_tag: MagicMock):
        sentry_utils.add_frostbite_sentry_tags()
        assert mock_set_tag.call_count == 1
        mock_set_tag.assert_called_once_with("elipy_scripts.fb_version", "9999-PR99")

    @patch("sentry_sdk.scope.Scope.set_tag")
    def test_add_frostbite_sentry_tags_exception(self, mock_set_tag: MagicMock):
        mock_set_tag.side_effect = Exception("Test exception")
        sentry_utils.add_frostbite_sentry_tags()
